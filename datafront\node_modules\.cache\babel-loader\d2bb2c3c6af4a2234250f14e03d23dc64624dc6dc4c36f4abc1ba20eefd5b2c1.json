{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { each, bind } from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nvar ParallelSeriesModel = /** @class */function (_super) {\n  __extends(ParallelSeriesModel, _super);\n  function ParallelSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelSeriesModel.type;\n    _this.visualStyleAccessPath = 'lineStyle';\n    _this.visualDrawType = 'stroke';\n    return _this;\n  }\n  ParallelSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: bind(makeDefaultEncode, null, this)\n    });\n  };\n  /**\r\n   * User can get data raw indices on 'axisAreaSelected' event received.\r\n   *\r\n   * @return Raw indices\r\n   */\n  ParallelSeriesModel.prototype.getRawIndicesByActiveState = function (activeState) {\n    var coordSys = this.coordinateSystem;\n    var data = this.getData();\n    var indices = [];\n    coordSys.eachActiveState(data, function (theActiveState, dataIndex) {\n      if (activeState === theActiveState) {\n        indices.push(data.getRawIndex(dataIndex));\n      }\n    });\n    return indices;\n  };\n  ParallelSeriesModel.type = 'series.parallel';\n  ParallelSeriesModel.dependencies = ['parallel'];\n  ParallelSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'parallel',\n    parallelIndex: 0,\n    label: {\n      show: false\n    },\n    inactiveOpacity: 0.05,\n    activeOpacity: 1,\n    lineStyle: {\n      width: 1,\n      opacity: 0.45,\n      type: 'solid'\n    },\n    emphasis: {\n      label: {\n        show: false\n      }\n    },\n    progressive: 500,\n    smooth: false,\n    animationEasing: 'linear'\n  };\n  return ParallelSeriesModel;\n}(SeriesModel);\nfunction makeDefaultEncode(seriesModel) {\n  // The mapping of parallelAxis dimension to data dimension can\n  // be specified in parallelAxis.option.dim. For example, if\n  // parallelAxis.option.dim is 'dim3', it mapping to the third\n  // dimension of data. But `data.encode` has higher priority.\n  // Moreover, parallelModel.dimension should not be regarded as data\n  // dimensions. Consider dimensions = ['dim4', 'dim2', 'dim6'];\n  var parallelModel = seriesModel.ecModel.getComponent('parallel', seriesModel.get('parallelIndex'));\n  if (!parallelModel) {\n    return;\n  }\n  var encodeDefine = {};\n  each(parallelModel.dimensions, function (axisDim) {\n    var dataDimIndex = convertDimNameToNumber(axisDim);\n    encodeDefine[axisDim] = dataDimIndex;\n  });\n  return encodeDefine;\n}\nfunction convertDimNameToNumber(dimName) {\n  return +dimName.replace('dim', '');\n}\nexport default ParallelSeriesModel;", "map": {"version": 3, "names": ["__extends", "each", "bind", "SeriesModel", "createSeriesData", "ParallelSeriesModel", "_super", "_this", "apply", "arguments", "type", "visualStyleAccessPath", "visualDrawType", "prototype", "getInitialData", "option", "ecModel", "useEncodeDefaulter", "makeDefaultEncode", "getRawIndicesByActiveState", "activeState", "coordSys", "coordinateSystem", "data", "getData", "indices", "eachActiveState", "theActiveState", "dataIndex", "push", "getRawIndex", "dependencies", "defaultOption", "z", "parallelIndex", "label", "show", "inactiveOpacity", "activeOpacity", "lineStyle", "width", "opacity", "emphasis", "progressive", "smooth", "animationEasing", "seriesModel", "parallelModel", "getComponent", "get", "encodeDefine", "dimensions", "axisDim", "dataDimIndex", "convertDimNameToNumber", "dimName", "replace"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/parallel/ParallelSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { each, bind } from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nvar ParallelSeriesModel = /** @class */function (_super) {\n  __extends(ParallelSeriesModel, _super);\n  function ParallelSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelSeriesModel.type;\n    _this.visualStyleAccessPath = 'lineStyle';\n    _this.visualDrawType = 'stroke';\n    return _this;\n  }\n  ParallelSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: bind(makeDefaultEncode, null, this)\n    });\n  };\n  /**\r\n   * User can get data raw indices on 'axisAreaSelected' event received.\r\n   *\r\n   * @return Raw indices\r\n   */\n  ParallelSeriesModel.prototype.getRawIndicesByActiveState = function (activeState) {\n    var coordSys = this.coordinateSystem;\n    var data = this.getData();\n    var indices = [];\n    coordSys.eachActiveState(data, function (theActiveState, dataIndex) {\n      if (activeState === theActiveState) {\n        indices.push(data.getRawIndex(dataIndex));\n      }\n    });\n    return indices;\n  };\n  ParallelSeriesModel.type = 'series.parallel';\n  ParallelSeriesModel.dependencies = ['parallel'];\n  ParallelSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'parallel',\n    parallelIndex: 0,\n    label: {\n      show: false\n    },\n    inactiveOpacity: 0.05,\n    activeOpacity: 1,\n    lineStyle: {\n      width: 1,\n      opacity: 0.45,\n      type: 'solid'\n    },\n    emphasis: {\n      label: {\n        show: false\n      }\n    },\n    progressive: 500,\n    smooth: false,\n    animationEasing: 'linear'\n  };\n  return ParallelSeriesModel;\n}(SeriesModel);\nfunction makeDefaultEncode(seriesModel) {\n  // The mapping of parallelAxis dimension to data dimension can\n  // be specified in parallelAxis.option.dim. For example, if\n  // parallelAxis.option.dim is 'dim3', it mapping to the third\n  // dimension of data. But `data.encode` has higher priority.\n  // Moreover, parallelModel.dimension should not be regarded as data\n  // dimensions. Consider dimensions = ['dim4', 'dim2', 'dim6'];\n  var parallelModel = seriesModel.ecModel.getComponent('parallel', seriesModel.get('parallelIndex'));\n  if (!parallelModel) {\n    return;\n  }\n  var encodeDefine = {};\n  each(parallelModel.dimensions, function (axisDim) {\n    var dataDimIndex = convertDimNameToNumber(axisDim);\n    encodeDefine[axisDim] = dataDimIndex;\n  });\n  return encodeDefine;\n}\nfunction convertDimNameToNumber(dimName) {\n  return +dimName.replace('dim', '');\n}\nexport default ParallelSeriesModel;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,IAAI,EAAEC,IAAI,QAAQ,0BAA0B;AACrD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,IAAIC,mBAAmB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACvDN,SAAS,CAACK,mBAAmB,EAAEC,MAAM,CAAC;EACtC,SAASD,mBAAmBA,CAAA,EAAG;IAC7B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,mBAAmB,CAACK,IAAI;IACrCH,KAAK,CAACI,qBAAqB,GAAG,WAAW;IACzCJ,KAAK,CAACK,cAAc,GAAG,QAAQ;IAC/B,OAAOL,KAAK;EACd;EACAF,mBAAmB,CAACQ,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACxE,OAAOZ,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE;MAClCa,kBAAkB,EAAEf,IAAI,CAACgB,iBAAiB,EAAE,IAAI,EAAE,IAAI;IACxD,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;EACEb,mBAAmB,CAACQ,SAAS,CAACM,0BAA0B,GAAG,UAAUC,WAAW,EAAE;IAChF,IAAIC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;IACpC,IAAIC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;IACzB,IAAIC,OAAO,GAAG,EAAE;IAChBJ,QAAQ,CAACK,eAAe,CAACH,IAAI,EAAE,UAAUI,cAAc,EAAEC,SAAS,EAAE;MAClE,IAAIR,WAAW,KAAKO,cAAc,EAAE;QAClCF,OAAO,CAACI,IAAI,CAACN,IAAI,CAACO,WAAW,CAACF,SAAS,CAAC,CAAC;MAC3C;IACF,CAAC,CAAC;IACF,OAAOH,OAAO;EAChB,CAAC;EACDpB,mBAAmB,CAACK,IAAI,GAAG,iBAAiB;EAC5CL,mBAAmB,CAAC0B,YAAY,GAAG,CAAC,UAAU,CAAC;EAC/C1B,mBAAmB,CAAC2B,aAAa,GAAG;IAClC;IACAC,CAAC,EAAE,CAAC;IACJX,gBAAgB,EAAE,UAAU;IAC5BY,aAAa,EAAE,CAAC;IAChBC,KAAK,EAAE;MACLC,IAAI,EAAE;IACR,CAAC;IACDC,eAAe,EAAE,IAAI;IACrBC,aAAa,EAAE,CAAC;IAChBC,SAAS,EAAE;MACTC,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE,IAAI;MACb/B,IAAI,EAAE;IACR,CAAC;IACDgC,QAAQ,EAAE;MACRP,KAAK,EAAE;QACLC,IAAI,EAAE;MACR;IACF,CAAC;IACDO,WAAW,EAAE,GAAG;IAChBC,MAAM,EAAE,KAAK;IACbC,eAAe,EAAE;EACnB,CAAC;EACD,OAAOxC,mBAAmB;AAC5B,CAAC,CAACF,WAAW,CAAC;AACd,SAASe,iBAAiBA,CAAC4B,WAAW,EAAE;EACtC;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,aAAa,GAAGD,WAAW,CAAC9B,OAAO,CAACgC,YAAY,CAAC,UAAU,EAAEF,WAAW,CAACG,GAAG,CAAC,eAAe,CAAC,CAAC;EAClG,IAAI,CAACF,aAAa,EAAE;IAClB;EACF;EACA,IAAIG,YAAY,GAAG,CAAC,CAAC;EACrBjD,IAAI,CAAC8C,aAAa,CAACI,UAAU,EAAE,UAAUC,OAAO,EAAE;IAChD,IAAIC,YAAY,GAAGC,sBAAsB,CAACF,OAAO,CAAC;IAClDF,YAAY,CAACE,OAAO,CAAC,GAAGC,YAAY;EACtC,CAAC,CAAC;EACF,OAAOH,YAAY;AACrB;AACA,SAASI,sBAAsBA,CAACC,OAAO,EAAE;EACvC,OAAO,CAACA,OAAO,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AACpC;AACA,eAAenD,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}