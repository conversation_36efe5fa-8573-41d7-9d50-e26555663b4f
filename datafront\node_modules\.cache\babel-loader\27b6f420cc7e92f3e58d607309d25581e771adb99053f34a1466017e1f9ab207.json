{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { retrieve, defaults, extend, each, isObject, map, isString, isNumber, isFunction, retrieve2 } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport { isRadianAroundZero, remRadian } from '../../util/number.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport * as matrixUtil from 'zrender/lib/core/matrix.js';\nimport { applyTransform as v2ApplyTransform } from 'zrender/lib/core/vector.js';\nimport { shouldShowAllLabels } from '../../coord/axisHelper.js';\nimport { prepareLayoutList, hideOverlap } from '../../label/labelLayoutHelper.js';\nvar PI = Math.PI;\n/**\r\n * A final axis is translated and rotated from a \"standard axis\".\r\n * So opt.position and opt.rotation is required.\r\n *\r\n * A standard axis is and axis from [0, 0] to [0, axisExtent[1]],\r\n * for example: (0, 0) ------------> (0, 50)\r\n *\r\n * nameDirection or tickDirection or labelDirection is 1 means tick\r\n * or label is below the standard axis, whereas is -1 means above\r\n * the standard axis. labelOffset means offset between label and axis,\r\n * which is useful when 'onZero', where axisLabel is in the grid and\r\n * label in outside grid.\r\n *\r\n * Tips: like always,\r\n * positive rotation represents anticlockwise, and negative rotation\r\n * represents clockwise.\r\n * The direction of position coordinate is the same as the direction\r\n * of screen coordinate.\r\n *\r\n * Do not need to consider axis 'inverse', which is auto processed by\r\n * axis extent.\r\n */\nvar AxisBuilder = /** @class */function () {\n  function AxisBuilder(axisModel, opt) {\n    this.group = new graphic.Group();\n    this.opt = opt;\n    this.axisModel = axisModel;\n    // Default value\n    defaults(opt, {\n      labelOffset: 0,\n      nameDirection: 1,\n      tickDirection: 1,\n      labelDirection: 1,\n      silent: true,\n      handleAutoShown: function () {\n        return true;\n      }\n    });\n    // FIXME Not use a separate text group?\n    var transformGroup = new graphic.Group({\n      x: opt.position[0],\n      y: opt.position[1],\n      rotation: opt.rotation\n    });\n    // this.group.add(transformGroup);\n    // this._transformGroup = transformGroup;\n    transformGroup.updateTransform();\n    this._transformGroup = transformGroup;\n  }\n  AxisBuilder.prototype.hasBuilder = function (name) {\n    return !!builders[name];\n  };\n  AxisBuilder.prototype.add = function (name) {\n    builders[name](this.opt, this.axisModel, this.group, this._transformGroup);\n  };\n  AxisBuilder.prototype.getGroup = function () {\n    return this.group;\n  };\n  AxisBuilder.innerTextLayout = function (axisRotation, textRotation, direction) {\n    var rotationDiff = remRadian(textRotation - axisRotation);\n    var textAlign;\n    var textVerticalAlign;\n    if (isRadianAroundZero(rotationDiff)) {\n      // Label is parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'top' : 'bottom';\n      textAlign = 'center';\n    } else if (isRadianAroundZero(rotationDiff - PI)) {\n      // Label is inverse parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'bottom' : 'top';\n      textAlign = 'center';\n    } else {\n      textVerticalAlign = 'middle';\n      if (rotationDiff > 0 && rotationDiff < PI) {\n        textAlign = direction > 0 ? 'right' : 'left';\n      } else {\n        textAlign = direction > 0 ? 'left' : 'right';\n      }\n    }\n    return {\n      rotation: rotationDiff,\n      textAlign: textAlign,\n      textVerticalAlign: textVerticalAlign\n    };\n  };\n  AxisBuilder.makeAxisEventDataBase = function (axisModel) {\n    var eventData = {\n      componentType: axisModel.mainType,\n      componentIndex: axisModel.componentIndex\n    };\n    eventData[axisModel.mainType + 'Index'] = axisModel.componentIndex;\n    return eventData;\n  };\n  AxisBuilder.isLabelSilent = function (axisModel) {\n    var tooltipOpt = axisModel.get('tooltip');\n    return axisModel.get('silent')\n    // Consider mouse cursor, add these restrictions.\n    || !(axisModel.get('triggerEvent') || tooltipOpt && tooltipOpt.show);\n  };\n  return AxisBuilder;\n}();\n;\nvar builders = {\n  axisLine: function (opt, axisModel, group, transformGroup) {\n    var shown = axisModel.get(['axisLine', 'show']);\n    if (shown === 'auto' && opt.handleAutoShown) {\n      shown = opt.handleAutoShown('axisLine');\n    }\n    if (!shown) {\n      return;\n    }\n    var extent = axisModel.axis.getExtent();\n    var matrix = transformGroup.transform;\n    var pt1 = [extent[0], 0];\n    var pt2 = [extent[1], 0];\n    var inverse = pt1[0] > pt2[0];\n    if (matrix) {\n      v2ApplyTransform(pt1, pt1, matrix);\n      v2ApplyTransform(pt2, pt2, matrix);\n    }\n    var lineStyle = extend({\n      lineCap: 'round'\n    }, axisModel.getModel(['axisLine', 'lineStyle']).getLineStyle());\n    var line = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: lineStyle,\n      strokeContainThreshold: opt.strokeContainThreshold || 5,\n      silent: true,\n      z2: 1\n    });\n    graphic.subPixelOptimizeLine(line.shape, line.style.lineWidth);\n    line.anid = 'line';\n    group.add(line);\n    var arrows = axisModel.get(['axisLine', 'symbol']);\n    if (arrows != null) {\n      var arrowSize = axisModel.get(['axisLine', 'symbolSize']);\n      if (isString(arrows)) {\n        // Use the same arrow for start and end point\n        arrows = [arrows, arrows];\n      }\n      if (isString(arrowSize) || isNumber(arrowSize)) {\n        // Use the same size for width and height\n        arrowSize = [arrowSize, arrowSize];\n      }\n      var arrowOffset = normalizeSymbolOffset(axisModel.get(['axisLine', 'symbolOffset']) || 0, arrowSize);\n      var symbolWidth_1 = arrowSize[0];\n      var symbolHeight_1 = arrowSize[1];\n      each([{\n        rotate: opt.rotation + Math.PI / 2,\n        offset: arrowOffset[0],\n        r: 0\n      }, {\n        rotate: opt.rotation - Math.PI / 2,\n        offset: arrowOffset[1],\n        r: Math.sqrt((pt1[0] - pt2[0]) * (pt1[0] - pt2[0]) + (pt1[1] - pt2[1]) * (pt1[1] - pt2[1]))\n      }], function (point, index) {\n        if (arrows[index] !== 'none' && arrows[index] != null) {\n          var symbol = createSymbol(arrows[index], -symbolWidth_1 / 2, -symbolHeight_1 / 2, symbolWidth_1, symbolHeight_1, lineStyle.stroke, true);\n          // Calculate arrow position with offset\n          var r = point.r + point.offset;\n          var pt = inverse ? pt2 : pt1;\n          symbol.attr({\n            rotation: point.rotate,\n            x: pt[0] + r * Math.cos(opt.rotation),\n            y: pt[1] - r * Math.sin(opt.rotation),\n            silent: true,\n            z2: 11\n          });\n          group.add(symbol);\n        }\n      });\n    }\n  },\n  axisTickLabel: function (opt, axisModel, group, transformGroup) {\n    var ticksEls = buildAxisMajorTicks(group, transformGroup, axisModel, opt);\n    var labelEls = buildAxisLabel(group, transformGroup, axisModel, opt);\n    fixMinMaxLabelShow(axisModel, labelEls, ticksEls);\n    buildAxisMinorTicks(group, transformGroup, axisModel, opt.tickDirection);\n    // This bit fixes the label overlap issue for the time chart.\n    // See https://github.com/apache/echarts/issues/14266 for more.\n    if (axisModel.get(['axisLabel', 'hideOverlap'])) {\n      var labelList = prepareLayoutList(map(labelEls, function (label) {\n        return {\n          label: label,\n          priority: label.z2,\n          defaultAttr: {\n            ignore: label.ignore\n          }\n        };\n      }));\n      hideOverlap(labelList);\n    }\n  },\n  axisName: function (opt, axisModel, group, transformGroup) {\n    var name = retrieve(opt.axisName, axisModel.get('name'));\n    if (!name) {\n      return;\n    }\n    var nameLocation = axisModel.get('nameLocation');\n    var nameDirection = opt.nameDirection;\n    var textStyleModel = axisModel.getModel('nameTextStyle');\n    var gap = axisModel.get('nameGap') || 0;\n    var extent = axisModel.axis.getExtent();\n    var gapSignal = extent[0] > extent[1] ? -1 : 1;\n    var pos = [nameLocation === 'start' ? extent[0] - gapSignal * gap : nameLocation === 'end' ? extent[1] + gapSignal * gap : (extent[0] + extent[1]) / 2,\n    // Reuse labelOffset.\n    isNameLocationCenter(nameLocation) ? opt.labelOffset + nameDirection * gap : 0];\n    var labelLayout;\n    var nameRotation = axisModel.get('nameRotate');\n    if (nameRotation != null) {\n      nameRotation = nameRotation * PI / 180; // To radian.\n    }\n    var axisNameAvailableWidth;\n    if (isNameLocationCenter(nameLocation)) {\n      labelLayout = AxisBuilder.innerTextLayout(opt.rotation, nameRotation != null ? nameRotation : opt.rotation,\n      // Adapt to axis.\n      nameDirection);\n    } else {\n      labelLayout = endTextLayout(opt.rotation, nameLocation, nameRotation || 0, extent);\n      axisNameAvailableWidth = opt.axisNameAvailableWidth;\n      if (axisNameAvailableWidth != null) {\n        axisNameAvailableWidth = Math.abs(axisNameAvailableWidth / Math.sin(labelLayout.rotation));\n        !isFinite(axisNameAvailableWidth) && (axisNameAvailableWidth = null);\n      }\n    }\n    var textFont = textStyleModel.getFont();\n    var truncateOpt = axisModel.get('nameTruncate', true) || {};\n    var ellipsis = truncateOpt.ellipsis;\n    var maxWidth = retrieve(opt.nameTruncateMaxWidth, truncateOpt.maxWidth, axisNameAvailableWidth);\n    var textEl = new graphic.Text({\n      x: pos[0],\n      y: pos[1],\n      rotation: labelLayout.rotation,\n      silent: AxisBuilder.isLabelSilent(axisModel),\n      style: createTextStyle(textStyleModel, {\n        text: name,\n        font: textFont,\n        overflow: 'truncate',\n        width: maxWidth,\n        ellipsis: ellipsis,\n        fill: textStyleModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']),\n        align: textStyleModel.get('align') || labelLayout.textAlign,\n        verticalAlign: textStyleModel.get('verticalAlign') || labelLayout.textVerticalAlign\n      }),\n      z2: 1\n    });\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: name\n    });\n    textEl.__fullText = name;\n    // Id for animation\n    textEl.anid = 'name';\n    if (axisModel.get('triggerEvent')) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisName';\n      eventData.name = name;\n      getECData(textEl).eventData = eventData;\n    }\n    // FIXME\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    group.add(textEl);\n    textEl.decomposeTransform();\n  }\n};\nfunction endTextLayout(rotation, textPosition, textRotate, extent) {\n  var rotationDiff = remRadian(textRotate - rotation);\n  var textAlign;\n  var textVerticalAlign;\n  var inverse = extent[0] > extent[1];\n  var onLeft = textPosition === 'start' && !inverse || textPosition !== 'start' && inverse;\n  if (isRadianAroundZero(rotationDiff - PI / 2)) {\n    textVerticalAlign = onLeft ? 'bottom' : 'top';\n    textAlign = 'center';\n  } else if (isRadianAroundZero(rotationDiff - PI * 1.5)) {\n    textVerticalAlign = onLeft ? 'top' : 'bottom';\n    textAlign = 'center';\n  } else {\n    textVerticalAlign = 'middle';\n    if (rotationDiff < PI * 1.5 && rotationDiff > PI / 2) {\n      textAlign = onLeft ? 'left' : 'right';\n    } else {\n      textAlign = onLeft ? 'right' : 'left';\n    }\n  }\n  return {\n    rotation: rotationDiff,\n    textAlign: textAlign,\n    textVerticalAlign: textVerticalAlign\n  };\n}\nfunction fixMinMaxLabelShow(axisModel, labelEls, tickEls) {\n  if (shouldShowAllLabels(axisModel.axis)) {\n    return;\n  }\n  // If min or max are user set, we need to check\n  // If the tick on min(max) are overlap on their neighbour tick\n  // If they are overlapped, we need to hide the min(max) tick label\n  var showMinLabel = axisModel.get(['axisLabel', 'showMinLabel']);\n  var showMaxLabel = axisModel.get(['axisLabel', 'showMaxLabel']);\n  // FIXME\n  // Have not consider onBand yet, where tick els is more than label els.\n  labelEls = labelEls || [];\n  tickEls = tickEls || [];\n  var firstLabel = labelEls[0];\n  var nextLabel = labelEls[1];\n  var lastLabel = labelEls[labelEls.length - 1];\n  var prevLabel = labelEls[labelEls.length - 2];\n  var firstTick = tickEls[0];\n  var nextTick = tickEls[1];\n  var lastTick = tickEls[tickEls.length - 1];\n  var prevTick = tickEls[tickEls.length - 2];\n  if (showMinLabel === false) {\n    ignoreEl(firstLabel);\n    ignoreEl(firstTick);\n  } else if (isTwoLabelOverlapped(firstLabel, nextLabel)) {\n    if (showMinLabel) {\n      ignoreEl(nextLabel);\n      ignoreEl(nextTick);\n    } else {\n      ignoreEl(firstLabel);\n      ignoreEl(firstTick);\n    }\n  }\n  if (showMaxLabel === false) {\n    ignoreEl(lastLabel);\n    ignoreEl(lastTick);\n  } else if (isTwoLabelOverlapped(prevLabel, lastLabel)) {\n    if (showMaxLabel) {\n      ignoreEl(prevLabel);\n      ignoreEl(prevTick);\n    } else {\n      ignoreEl(lastLabel);\n      ignoreEl(lastTick);\n    }\n  }\n}\nfunction ignoreEl(el) {\n  el && (el.ignore = true);\n}\nfunction isTwoLabelOverlapped(current, next) {\n  // current and next has the same rotation.\n  var firstRect = current && current.getBoundingRect().clone();\n  var nextRect = next && next.getBoundingRect().clone();\n  if (!firstRect || !nextRect) {\n    return;\n  }\n  // When checking intersect of two rotated labels, we use mRotationBack\n  // to avoid that boundingRect is enlarge when using `boundingRect.applyTransform`.\n  var mRotationBack = matrixUtil.identity([]);\n  matrixUtil.rotate(mRotationBack, mRotationBack, -current.rotation);\n  firstRect.applyTransform(matrixUtil.mul([], mRotationBack, current.getLocalTransform()));\n  nextRect.applyTransform(matrixUtil.mul([], mRotationBack, next.getLocalTransform()));\n  return firstRect.intersect(nextRect);\n}\nfunction isNameLocationCenter(nameLocation) {\n  return nameLocation === 'middle' || nameLocation === 'center';\n}\nfunction createTicks(ticksCoords, tickTransform, tickEndCoord, tickLineStyle, anidPrefix) {\n  var tickEls = [];\n  var pt1 = [];\n  var pt2 = [];\n  for (var i = 0; i < ticksCoords.length; i++) {\n    var tickCoord = ticksCoords[i].coord;\n    pt1[0] = tickCoord;\n    pt1[1] = 0;\n    pt2[0] = tickCoord;\n    pt2[1] = tickEndCoord;\n    if (tickTransform) {\n      v2ApplyTransform(pt1, pt1, tickTransform);\n      v2ApplyTransform(pt2, pt2, tickTransform);\n    }\n    // Tick line, Not use group transform to have better line draw\n    var tickEl = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: tickLineStyle,\n      z2: 2,\n      autoBatch: true,\n      silent: true\n    });\n    graphic.subPixelOptimizeLine(tickEl.shape, tickEl.style.lineWidth);\n    tickEl.anid = anidPrefix + '_' + ticksCoords[i].tickValue;\n    tickEls.push(tickEl);\n  }\n  return tickEls;\n}\nfunction buildAxisMajorTicks(group, transformGroup, axisModel, opt) {\n  var axis = axisModel.axis;\n  var tickModel = axisModel.getModel('axisTick');\n  var shown = tickModel.get('show');\n  if (shown === 'auto' && opt.handleAutoShown) {\n    shown = opt.handleAutoShown('axisTick');\n  }\n  if (!shown || axis.scale.isBlank()) {\n    return;\n  }\n  var lineStyleModel = tickModel.getModel('lineStyle');\n  var tickEndCoord = opt.tickDirection * tickModel.get('length');\n  var ticksCoords = axis.getTicksCoords();\n  var ticksEls = createTicks(ticksCoords, transformGroup.transform, tickEndCoord, defaults(lineStyleModel.getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }), 'ticks');\n  for (var i = 0; i < ticksEls.length; i++) {\n    group.add(ticksEls[i]);\n  }\n  return ticksEls;\n}\nfunction buildAxisMinorTicks(group, transformGroup, axisModel, tickDirection) {\n  var axis = axisModel.axis;\n  var minorTickModel = axisModel.getModel('minorTick');\n  if (!minorTickModel.get('show') || axis.scale.isBlank()) {\n    return;\n  }\n  var minorTicksCoords = axis.getMinorTicksCoords();\n  if (!minorTicksCoords.length) {\n    return;\n  }\n  var lineStyleModel = minorTickModel.getModel('lineStyle');\n  var tickEndCoord = tickDirection * minorTickModel.get('length');\n  var minorTickLineStyle = defaults(lineStyleModel.getLineStyle(), defaults(axisModel.getModel('axisTick').getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }));\n  for (var i = 0; i < minorTicksCoords.length; i++) {\n    var minorTicksEls = createTicks(minorTicksCoords[i], transformGroup.transform, tickEndCoord, minorTickLineStyle, 'minorticks_' + i);\n    for (var k = 0; k < minorTicksEls.length; k++) {\n      group.add(minorTicksEls[k]);\n    }\n  }\n}\nfunction buildAxisLabel(group, transformGroup, axisModel, opt) {\n  var axis = axisModel.axis;\n  var show = retrieve(opt.axisLabelShow, axisModel.get(['axisLabel', 'show']));\n  if (!show || axis.scale.isBlank()) {\n    return;\n  }\n  var labelModel = axisModel.getModel('axisLabel');\n  var labelMargin = labelModel.get('margin');\n  var labels = axis.getViewLabels();\n  // Special label rotate.\n  var labelRotation = (retrieve(opt.labelRotate, labelModel.get('rotate')) || 0) * PI / 180;\n  var labelLayout = AxisBuilder.innerTextLayout(opt.rotation, labelRotation, opt.labelDirection);\n  var rawCategoryData = axisModel.getCategories && axisModel.getCategories(true);\n  var labelEls = [];\n  var silent = AxisBuilder.isLabelSilent(axisModel);\n  var triggerEvent = axisModel.get('triggerEvent');\n  each(labels, function (labelItem, index) {\n    var tickValue = axis.scale.type === 'ordinal' ? axis.scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n    var formattedLabel = labelItem.formattedLabel;\n    var rawLabel = labelItem.rawLabel;\n    var itemLabelModel = labelModel;\n    if (rawCategoryData && rawCategoryData[tickValue]) {\n      var rawCategoryItem = rawCategoryData[tickValue];\n      if (isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n        itemLabelModel = new Model(rawCategoryItem.textStyle, labelModel, axisModel.ecModel);\n      }\n    }\n    var textColor = itemLabelModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']);\n    var tickCoord = axis.dataToCoord(tickValue);\n    var align = itemLabelModel.getShallow('align', true) || labelLayout.textAlign;\n    var alignMin = retrieve2(itemLabelModel.getShallow('alignMinLabel', true), align);\n    var alignMax = retrieve2(itemLabelModel.getShallow('alignMaxLabel', true), align);\n    var verticalAlign = itemLabelModel.getShallow('verticalAlign', true) || itemLabelModel.getShallow('baseline', true) || labelLayout.textVerticalAlign;\n    var verticalAlignMin = retrieve2(itemLabelModel.getShallow('verticalAlignMinLabel', true), verticalAlign);\n    var verticalAlignMax = retrieve2(itemLabelModel.getShallow('verticalAlignMaxLabel', true), verticalAlign);\n    var textEl = new graphic.Text({\n      x: tickCoord,\n      y: opt.labelOffset + opt.labelDirection * labelMargin,\n      rotation: labelLayout.rotation,\n      silent: silent,\n      z2: 10 + (labelItem.level || 0),\n      style: createTextStyle(itemLabelModel, {\n        text: formattedLabel,\n        align: index === 0 ? alignMin : index === labels.length - 1 ? alignMax : align,\n        verticalAlign: index === 0 ? verticalAlignMin : index === labels.length - 1 ? verticalAlignMax : verticalAlign,\n        fill: isFunction(textColor) ? textColor(\n        // (1) In category axis with data zoom, tick is not the original\n        // index of axis.data. So tick should not be exposed to user\n        // in category axis.\n        // (2) Compatible with previous version, which always use formatted label as\n        // input. But in interval scale the formatted label is like '223,445', which\n        // maked user replace ','. So we modify it to return original val but remain\n        // it as 'string' to avoid error in replacing.\n        axis.type === 'category' ? rawLabel : axis.type === 'value' ? tickValue + '' : tickValue, index) : textColor\n      })\n    });\n    textEl.anid = 'label_' + tickValue;\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: formattedLabel,\n      formatterParamsExtra: {\n        isTruncated: function () {\n          return textEl.isTruncated;\n        },\n        value: rawLabel,\n        tickIndex: index\n      }\n    });\n    // Pack data for mouse event\n    if (triggerEvent) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisLabel';\n      eventData.value = rawLabel;\n      eventData.tickIndex = index;\n      if (axis.type === 'category') {\n        eventData.dataIndex = tickValue;\n      }\n      getECData(textEl).eventData = eventData;\n    }\n    // FIXME\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    labelEls.push(textEl);\n    group.add(textEl);\n    textEl.decomposeTransform();\n  });\n  return labelEls;\n}\nexport default AxisBuilder;", "map": {"version": 3, "names": ["retrieve", "defaults", "extend", "each", "isObject", "map", "isString", "isNumber", "isFunction", "retrieve2", "graphic", "getECData", "createTextStyle", "Model", "isRadianAroundZero", "remRadian", "createSymbol", "normalizeSymbolOffset", "matrixUtil", "applyTransform", "v2ApplyTransform", "shouldShowAllLabels", "prepareLayoutList", "hideOverlap", "PI", "Math", "AxisBuilder", "axisModel", "opt", "group", "Group", "labelOffset", "nameDirection", "tickDirection", "labelDirection", "silent", "handleAutoShown", "transformGroup", "x", "position", "y", "rotation", "updateTransform", "_transformGroup", "prototype", "hasBuilder", "name", "builders", "add", "getGroup", "innerTextLayout", "axisRotation", "textRotation", "direction", "rotationDiff", "textAlign", "textVerticalAlign", "makeAxisEventDataBase", "eventData", "componentType", "mainType", "componentIndex", "isLabelSilent", "tooltipOpt", "get", "show", "axisLine", "shown", "extent", "axis", "getExtent", "matrix", "transform", "pt1", "pt2", "inverse", "lineStyle", "lineCap", "getModel", "getLineStyle", "line", "Line", "shape", "x1", "y1", "x2", "y2", "style", "strokeContainThreshold", "z2", "subPixelOptimizeLine", "lineWidth", "anid", "arrows", "arrowSize", "arrowOffset", "symbolWidth_1", "symbolHeight_1", "rotate", "offset", "r", "sqrt", "point", "index", "symbol", "stroke", "pt", "attr", "cos", "sin", "axisTickLabel", "ticksEls", "buildAxisMajorTicks", "labelEls", "buildAxisLabel", "fixMinMaxLabelShow", "buildAxisMinorTicks", "labelList", "label", "priority", "defaultAttr", "ignore", "axisName", "nameLocation", "textStyleModel", "gap", "gapSignal", "pos", "isNameLocationCenter", "labelLayout", "nameRotation", "axisNameAvailableWidth", "endTextLayout", "abs", "isFinite", "textFont", "getFont", "truncateOpt", "ellipsis", "max<PERSON><PERSON><PERSON>", "nameTruncateMaxWidth", "textEl", "Text", "text", "font", "overflow", "width", "fill", "getTextColor", "align", "verticalAlign", "setTooltipConfig", "el", "componentModel", "itemName", "__fullText", "targetType", "decomposeTransform", "textPosition", "textRotate", "onLeft", "tickEls", "showMinLabel", "showMaxLabel", "firstLabel", "next<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "prevLabel", "firstTick", "nextTick", "lastTick", "prevTick", "ignoreEl", "isTwoLabelOverlapped", "current", "next", "firstRect", "getBoundingRect", "clone", "nextRect", "mRotationBack", "identity", "mul", "getLocalTransform", "intersect", "createTicks", "ticksCoords", "tickTransform", "tickEndCoord", "tickLineStyle", "anidPrefix", "i", "tickCoord", "coord", "tickEl", "autoBatch", "tickValue", "push", "tickModel", "scale", "isBlank", "lineStyleModel", "getTicksCoords", "minorTickModel", "minorTicksCoords", "getMinorTicksCoords", "minorTickLineStyle", "minorTicksEls", "k", "axisLabelShow", "labelModel", "labelMargin", "labels", "getViewLabels", "labelRotation", "labelRotate", "rawCategoryData", "getCategories", "triggerEvent", "labelItem", "type", "getRawOrdinalNumber", "formattedLabel", "rawLabel", "itemLabelModel", "rawCategoryItem", "textStyle", "ecModel", "textColor", "dataToCoord", "getShallow", "alignMin", "alignMax", "verticalAlignMin", "verticalAlignMax", "level", "formatterParamsExtra", "isTruncated", "value", "tickIndex", "dataIndex"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/axis/AxisBuilder.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { retrieve, defaults, extend, each, isObject, map, isString, isNumber, isFunction, retrieve2 } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport { isRadianAroundZero, remRadian } from '../../util/number.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport * as matrixUtil from 'zrender/lib/core/matrix.js';\nimport { applyTransform as v2ApplyTransform } from 'zrender/lib/core/vector.js';\nimport { shouldShowAllLabels } from '../../coord/axisHelper.js';\nimport { prepareLayoutList, hideOverlap } from '../../label/labelLayoutHelper.js';\nvar PI = Math.PI;\n/**\r\n * A final axis is translated and rotated from a \"standard axis\".\r\n * So opt.position and opt.rotation is required.\r\n *\r\n * A standard axis is and axis from [0, 0] to [0, axisExtent[1]],\r\n * for example: (0, 0) ------------> (0, 50)\r\n *\r\n * nameDirection or tickDirection or labelDirection is 1 means tick\r\n * or label is below the standard axis, whereas is -1 means above\r\n * the standard axis. labelOffset means offset between label and axis,\r\n * which is useful when 'onZero', where axisLabel is in the grid and\r\n * label in outside grid.\r\n *\r\n * Tips: like always,\r\n * positive rotation represents anticlockwise, and negative rotation\r\n * represents clockwise.\r\n * The direction of position coordinate is the same as the direction\r\n * of screen coordinate.\r\n *\r\n * Do not need to consider axis 'inverse', which is auto processed by\r\n * axis extent.\r\n */\nvar AxisBuilder = /** @class */function () {\n  function AxisBuilder(axisModel, opt) {\n    this.group = new graphic.Group();\n    this.opt = opt;\n    this.axisModel = axisModel;\n    // Default value\n    defaults(opt, {\n      labelOffset: 0,\n      nameDirection: 1,\n      tickDirection: 1,\n      labelDirection: 1,\n      silent: true,\n      handleAutoShown: function () {\n        return true;\n      }\n    });\n    // FIXME Not use a separate text group?\n    var transformGroup = new graphic.Group({\n      x: opt.position[0],\n      y: opt.position[1],\n      rotation: opt.rotation\n    });\n    // this.group.add(transformGroup);\n    // this._transformGroup = transformGroup;\n    transformGroup.updateTransform();\n    this._transformGroup = transformGroup;\n  }\n  AxisBuilder.prototype.hasBuilder = function (name) {\n    return !!builders[name];\n  };\n  AxisBuilder.prototype.add = function (name) {\n    builders[name](this.opt, this.axisModel, this.group, this._transformGroup);\n  };\n  AxisBuilder.prototype.getGroup = function () {\n    return this.group;\n  };\n  AxisBuilder.innerTextLayout = function (axisRotation, textRotation, direction) {\n    var rotationDiff = remRadian(textRotation - axisRotation);\n    var textAlign;\n    var textVerticalAlign;\n    if (isRadianAroundZero(rotationDiff)) {\n      // Label is parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'top' : 'bottom';\n      textAlign = 'center';\n    } else if (isRadianAroundZero(rotationDiff - PI)) {\n      // Label is inverse parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'bottom' : 'top';\n      textAlign = 'center';\n    } else {\n      textVerticalAlign = 'middle';\n      if (rotationDiff > 0 && rotationDiff < PI) {\n        textAlign = direction > 0 ? 'right' : 'left';\n      } else {\n        textAlign = direction > 0 ? 'left' : 'right';\n      }\n    }\n    return {\n      rotation: rotationDiff,\n      textAlign: textAlign,\n      textVerticalAlign: textVerticalAlign\n    };\n  };\n  AxisBuilder.makeAxisEventDataBase = function (axisModel) {\n    var eventData = {\n      componentType: axisModel.mainType,\n      componentIndex: axisModel.componentIndex\n    };\n    eventData[axisModel.mainType + 'Index'] = axisModel.componentIndex;\n    return eventData;\n  };\n  AxisBuilder.isLabelSilent = function (axisModel) {\n    var tooltipOpt = axisModel.get('tooltip');\n    return axisModel.get('silent')\n    // Consider mouse cursor, add these restrictions.\n    || !(axisModel.get('triggerEvent') || tooltipOpt && tooltipOpt.show);\n  };\n  return AxisBuilder;\n}();\n;\nvar builders = {\n  axisLine: function (opt, axisModel, group, transformGroup) {\n    var shown = axisModel.get(['axisLine', 'show']);\n    if (shown === 'auto' && opt.handleAutoShown) {\n      shown = opt.handleAutoShown('axisLine');\n    }\n    if (!shown) {\n      return;\n    }\n    var extent = axisModel.axis.getExtent();\n    var matrix = transformGroup.transform;\n    var pt1 = [extent[0], 0];\n    var pt2 = [extent[1], 0];\n    var inverse = pt1[0] > pt2[0];\n    if (matrix) {\n      v2ApplyTransform(pt1, pt1, matrix);\n      v2ApplyTransform(pt2, pt2, matrix);\n    }\n    var lineStyle = extend({\n      lineCap: 'round'\n    }, axisModel.getModel(['axisLine', 'lineStyle']).getLineStyle());\n    var line = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: lineStyle,\n      strokeContainThreshold: opt.strokeContainThreshold || 5,\n      silent: true,\n      z2: 1\n    });\n    graphic.subPixelOptimizeLine(line.shape, line.style.lineWidth);\n    line.anid = 'line';\n    group.add(line);\n    var arrows = axisModel.get(['axisLine', 'symbol']);\n    if (arrows != null) {\n      var arrowSize = axisModel.get(['axisLine', 'symbolSize']);\n      if (isString(arrows)) {\n        // Use the same arrow for start and end point\n        arrows = [arrows, arrows];\n      }\n      if (isString(arrowSize) || isNumber(arrowSize)) {\n        // Use the same size for width and height\n        arrowSize = [arrowSize, arrowSize];\n      }\n      var arrowOffset = normalizeSymbolOffset(axisModel.get(['axisLine', 'symbolOffset']) || 0, arrowSize);\n      var symbolWidth_1 = arrowSize[0];\n      var symbolHeight_1 = arrowSize[1];\n      each([{\n        rotate: opt.rotation + Math.PI / 2,\n        offset: arrowOffset[0],\n        r: 0\n      }, {\n        rotate: opt.rotation - Math.PI / 2,\n        offset: arrowOffset[1],\n        r: Math.sqrt((pt1[0] - pt2[0]) * (pt1[0] - pt2[0]) + (pt1[1] - pt2[1]) * (pt1[1] - pt2[1]))\n      }], function (point, index) {\n        if (arrows[index] !== 'none' && arrows[index] != null) {\n          var symbol = createSymbol(arrows[index], -symbolWidth_1 / 2, -symbolHeight_1 / 2, symbolWidth_1, symbolHeight_1, lineStyle.stroke, true);\n          // Calculate arrow position with offset\n          var r = point.r + point.offset;\n          var pt = inverse ? pt2 : pt1;\n          symbol.attr({\n            rotation: point.rotate,\n            x: pt[0] + r * Math.cos(opt.rotation),\n            y: pt[1] - r * Math.sin(opt.rotation),\n            silent: true,\n            z2: 11\n          });\n          group.add(symbol);\n        }\n      });\n    }\n  },\n  axisTickLabel: function (opt, axisModel, group, transformGroup) {\n    var ticksEls = buildAxisMajorTicks(group, transformGroup, axisModel, opt);\n    var labelEls = buildAxisLabel(group, transformGroup, axisModel, opt);\n    fixMinMaxLabelShow(axisModel, labelEls, ticksEls);\n    buildAxisMinorTicks(group, transformGroup, axisModel, opt.tickDirection);\n    // This bit fixes the label overlap issue for the time chart.\n    // See https://github.com/apache/echarts/issues/14266 for more.\n    if (axisModel.get(['axisLabel', 'hideOverlap'])) {\n      var labelList = prepareLayoutList(map(labelEls, function (label) {\n        return {\n          label: label,\n          priority: label.z2,\n          defaultAttr: {\n            ignore: label.ignore\n          }\n        };\n      }));\n      hideOverlap(labelList);\n    }\n  },\n  axisName: function (opt, axisModel, group, transformGroup) {\n    var name = retrieve(opt.axisName, axisModel.get('name'));\n    if (!name) {\n      return;\n    }\n    var nameLocation = axisModel.get('nameLocation');\n    var nameDirection = opt.nameDirection;\n    var textStyleModel = axisModel.getModel('nameTextStyle');\n    var gap = axisModel.get('nameGap') || 0;\n    var extent = axisModel.axis.getExtent();\n    var gapSignal = extent[0] > extent[1] ? -1 : 1;\n    var pos = [nameLocation === 'start' ? extent[0] - gapSignal * gap : nameLocation === 'end' ? extent[1] + gapSignal * gap : (extent[0] + extent[1]) / 2,\n    // Reuse labelOffset.\n    isNameLocationCenter(nameLocation) ? opt.labelOffset + nameDirection * gap : 0];\n    var labelLayout;\n    var nameRotation = axisModel.get('nameRotate');\n    if (nameRotation != null) {\n      nameRotation = nameRotation * PI / 180; // To radian.\n    }\n    var axisNameAvailableWidth;\n    if (isNameLocationCenter(nameLocation)) {\n      labelLayout = AxisBuilder.innerTextLayout(opt.rotation, nameRotation != null ? nameRotation : opt.rotation,\n      // Adapt to axis.\n      nameDirection);\n    } else {\n      labelLayout = endTextLayout(opt.rotation, nameLocation, nameRotation || 0, extent);\n      axisNameAvailableWidth = opt.axisNameAvailableWidth;\n      if (axisNameAvailableWidth != null) {\n        axisNameAvailableWidth = Math.abs(axisNameAvailableWidth / Math.sin(labelLayout.rotation));\n        !isFinite(axisNameAvailableWidth) && (axisNameAvailableWidth = null);\n      }\n    }\n    var textFont = textStyleModel.getFont();\n    var truncateOpt = axisModel.get('nameTruncate', true) || {};\n    var ellipsis = truncateOpt.ellipsis;\n    var maxWidth = retrieve(opt.nameTruncateMaxWidth, truncateOpt.maxWidth, axisNameAvailableWidth);\n    var textEl = new graphic.Text({\n      x: pos[0],\n      y: pos[1],\n      rotation: labelLayout.rotation,\n      silent: AxisBuilder.isLabelSilent(axisModel),\n      style: createTextStyle(textStyleModel, {\n        text: name,\n        font: textFont,\n        overflow: 'truncate',\n        width: maxWidth,\n        ellipsis: ellipsis,\n        fill: textStyleModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']),\n        align: textStyleModel.get('align') || labelLayout.textAlign,\n        verticalAlign: textStyleModel.get('verticalAlign') || labelLayout.textVerticalAlign\n      }),\n      z2: 1\n    });\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: name\n    });\n    textEl.__fullText = name;\n    // Id for animation\n    textEl.anid = 'name';\n    if (axisModel.get('triggerEvent')) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisName';\n      eventData.name = name;\n      getECData(textEl).eventData = eventData;\n    }\n    // FIXME\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    group.add(textEl);\n    textEl.decomposeTransform();\n  }\n};\nfunction endTextLayout(rotation, textPosition, textRotate, extent) {\n  var rotationDiff = remRadian(textRotate - rotation);\n  var textAlign;\n  var textVerticalAlign;\n  var inverse = extent[0] > extent[1];\n  var onLeft = textPosition === 'start' && !inverse || textPosition !== 'start' && inverse;\n  if (isRadianAroundZero(rotationDiff - PI / 2)) {\n    textVerticalAlign = onLeft ? 'bottom' : 'top';\n    textAlign = 'center';\n  } else if (isRadianAroundZero(rotationDiff - PI * 1.5)) {\n    textVerticalAlign = onLeft ? 'top' : 'bottom';\n    textAlign = 'center';\n  } else {\n    textVerticalAlign = 'middle';\n    if (rotationDiff < PI * 1.5 && rotationDiff > PI / 2) {\n      textAlign = onLeft ? 'left' : 'right';\n    } else {\n      textAlign = onLeft ? 'right' : 'left';\n    }\n  }\n  return {\n    rotation: rotationDiff,\n    textAlign: textAlign,\n    textVerticalAlign: textVerticalAlign\n  };\n}\nfunction fixMinMaxLabelShow(axisModel, labelEls, tickEls) {\n  if (shouldShowAllLabels(axisModel.axis)) {\n    return;\n  }\n  // If min or max are user set, we need to check\n  // If the tick on min(max) are overlap on their neighbour tick\n  // If they are overlapped, we need to hide the min(max) tick label\n  var showMinLabel = axisModel.get(['axisLabel', 'showMinLabel']);\n  var showMaxLabel = axisModel.get(['axisLabel', 'showMaxLabel']);\n  // FIXME\n  // Have not consider onBand yet, where tick els is more than label els.\n  labelEls = labelEls || [];\n  tickEls = tickEls || [];\n  var firstLabel = labelEls[0];\n  var nextLabel = labelEls[1];\n  var lastLabel = labelEls[labelEls.length - 1];\n  var prevLabel = labelEls[labelEls.length - 2];\n  var firstTick = tickEls[0];\n  var nextTick = tickEls[1];\n  var lastTick = tickEls[tickEls.length - 1];\n  var prevTick = tickEls[tickEls.length - 2];\n  if (showMinLabel === false) {\n    ignoreEl(firstLabel);\n    ignoreEl(firstTick);\n  } else if (isTwoLabelOverlapped(firstLabel, nextLabel)) {\n    if (showMinLabel) {\n      ignoreEl(nextLabel);\n      ignoreEl(nextTick);\n    } else {\n      ignoreEl(firstLabel);\n      ignoreEl(firstTick);\n    }\n  }\n  if (showMaxLabel === false) {\n    ignoreEl(lastLabel);\n    ignoreEl(lastTick);\n  } else if (isTwoLabelOverlapped(prevLabel, lastLabel)) {\n    if (showMaxLabel) {\n      ignoreEl(prevLabel);\n      ignoreEl(prevTick);\n    } else {\n      ignoreEl(lastLabel);\n      ignoreEl(lastTick);\n    }\n  }\n}\nfunction ignoreEl(el) {\n  el && (el.ignore = true);\n}\nfunction isTwoLabelOverlapped(current, next) {\n  // current and next has the same rotation.\n  var firstRect = current && current.getBoundingRect().clone();\n  var nextRect = next && next.getBoundingRect().clone();\n  if (!firstRect || !nextRect) {\n    return;\n  }\n  // When checking intersect of two rotated labels, we use mRotationBack\n  // to avoid that boundingRect is enlarge when using `boundingRect.applyTransform`.\n  var mRotationBack = matrixUtil.identity([]);\n  matrixUtil.rotate(mRotationBack, mRotationBack, -current.rotation);\n  firstRect.applyTransform(matrixUtil.mul([], mRotationBack, current.getLocalTransform()));\n  nextRect.applyTransform(matrixUtil.mul([], mRotationBack, next.getLocalTransform()));\n  return firstRect.intersect(nextRect);\n}\nfunction isNameLocationCenter(nameLocation) {\n  return nameLocation === 'middle' || nameLocation === 'center';\n}\nfunction createTicks(ticksCoords, tickTransform, tickEndCoord, tickLineStyle, anidPrefix) {\n  var tickEls = [];\n  var pt1 = [];\n  var pt2 = [];\n  for (var i = 0; i < ticksCoords.length; i++) {\n    var tickCoord = ticksCoords[i].coord;\n    pt1[0] = tickCoord;\n    pt1[1] = 0;\n    pt2[0] = tickCoord;\n    pt2[1] = tickEndCoord;\n    if (tickTransform) {\n      v2ApplyTransform(pt1, pt1, tickTransform);\n      v2ApplyTransform(pt2, pt2, tickTransform);\n    }\n    // Tick line, Not use group transform to have better line draw\n    var tickEl = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: tickLineStyle,\n      z2: 2,\n      autoBatch: true,\n      silent: true\n    });\n    graphic.subPixelOptimizeLine(tickEl.shape, tickEl.style.lineWidth);\n    tickEl.anid = anidPrefix + '_' + ticksCoords[i].tickValue;\n    tickEls.push(tickEl);\n  }\n  return tickEls;\n}\nfunction buildAxisMajorTicks(group, transformGroup, axisModel, opt) {\n  var axis = axisModel.axis;\n  var tickModel = axisModel.getModel('axisTick');\n  var shown = tickModel.get('show');\n  if (shown === 'auto' && opt.handleAutoShown) {\n    shown = opt.handleAutoShown('axisTick');\n  }\n  if (!shown || axis.scale.isBlank()) {\n    return;\n  }\n  var lineStyleModel = tickModel.getModel('lineStyle');\n  var tickEndCoord = opt.tickDirection * tickModel.get('length');\n  var ticksCoords = axis.getTicksCoords();\n  var ticksEls = createTicks(ticksCoords, transformGroup.transform, tickEndCoord, defaults(lineStyleModel.getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }), 'ticks');\n  for (var i = 0; i < ticksEls.length; i++) {\n    group.add(ticksEls[i]);\n  }\n  return ticksEls;\n}\nfunction buildAxisMinorTicks(group, transformGroup, axisModel, tickDirection) {\n  var axis = axisModel.axis;\n  var minorTickModel = axisModel.getModel('minorTick');\n  if (!minorTickModel.get('show') || axis.scale.isBlank()) {\n    return;\n  }\n  var minorTicksCoords = axis.getMinorTicksCoords();\n  if (!minorTicksCoords.length) {\n    return;\n  }\n  var lineStyleModel = minorTickModel.getModel('lineStyle');\n  var tickEndCoord = tickDirection * minorTickModel.get('length');\n  var minorTickLineStyle = defaults(lineStyleModel.getLineStyle(), defaults(axisModel.getModel('axisTick').getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }));\n  for (var i = 0; i < minorTicksCoords.length; i++) {\n    var minorTicksEls = createTicks(minorTicksCoords[i], transformGroup.transform, tickEndCoord, minorTickLineStyle, 'minorticks_' + i);\n    for (var k = 0; k < minorTicksEls.length; k++) {\n      group.add(minorTicksEls[k]);\n    }\n  }\n}\nfunction buildAxisLabel(group, transformGroup, axisModel, opt) {\n  var axis = axisModel.axis;\n  var show = retrieve(opt.axisLabelShow, axisModel.get(['axisLabel', 'show']));\n  if (!show || axis.scale.isBlank()) {\n    return;\n  }\n  var labelModel = axisModel.getModel('axisLabel');\n  var labelMargin = labelModel.get('margin');\n  var labels = axis.getViewLabels();\n  // Special label rotate.\n  var labelRotation = (retrieve(opt.labelRotate, labelModel.get('rotate')) || 0) * PI / 180;\n  var labelLayout = AxisBuilder.innerTextLayout(opt.rotation, labelRotation, opt.labelDirection);\n  var rawCategoryData = axisModel.getCategories && axisModel.getCategories(true);\n  var labelEls = [];\n  var silent = AxisBuilder.isLabelSilent(axisModel);\n  var triggerEvent = axisModel.get('triggerEvent');\n  each(labels, function (labelItem, index) {\n    var tickValue = axis.scale.type === 'ordinal' ? axis.scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n    var formattedLabel = labelItem.formattedLabel;\n    var rawLabel = labelItem.rawLabel;\n    var itemLabelModel = labelModel;\n    if (rawCategoryData && rawCategoryData[tickValue]) {\n      var rawCategoryItem = rawCategoryData[tickValue];\n      if (isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n        itemLabelModel = new Model(rawCategoryItem.textStyle, labelModel, axisModel.ecModel);\n      }\n    }\n    var textColor = itemLabelModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']);\n    var tickCoord = axis.dataToCoord(tickValue);\n    var align = itemLabelModel.getShallow('align', true) || labelLayout.textAlign;\n    var alignMin = retrieve2(itemLabelModel.getShallow('alignMinLabel', true), align);\n    var alignMax = retrieve2(itemLabelModel.getShallow('alignMaxLabel', true), align);\n    var verticalAlign = itemLabelModel.getShallow('verticalAlign', true) || itemLabelModel.getShallow('baseline', true) || labelLayout.textVerticalAlign;\n    var verticalAlignMin = retrieve2(itemLabelModel.getShallow('verticalAlignMinLabel', true), verticalAlign);\n    var verticalAlignMax = retrieve2(itemLabelModel.getShallow('verticalAlignMaxLabel', true), verticalAlign);\n    var textEl = new graphic.Text({\n      x: tickCoord,\n      y: opt.labelOffset + opt.labelDirection * labelMargin,\n      rotation: labelLayout.rotation,\n      silent: silent,\n      z2: 10 + (labelItem.level || 0),\n      style: createTextStyle(itemLabelModel, {\n        text: formattedLabel,\n        align: index === 0 ? alignMin : index === labels.length - 1 ? alignMax : align,\n        verticalAlign: index === 0 ? verticalAlignMin : index === labels.length - 1 ? verticalAlignMax : verticalAlign,\n        fill: isFunction(textColor) ? textColor(\n        // (1) In category axis with data zoom, tick is not the original\n        // index of axis.data. So tick should not be exposed to user\n        // in category axis.\n        // (2) Compatible with previous version, which always use formatted label as\n        // input. But in interval scale the formatted label is like '223,445', which\n        // maked user replace ','. So we modify it to return original val but remain\n        // it as 'string' to avoid error in replacing.\n        axis.type === 'category' ? rawLabel : axis.type === 'value' ? tickValue + '' : tickValue, index) : textColor\n      })\n    });\n    textEl.anid = 'label_' + tickValue;\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: formattedLabel,\n      formatterParamsExtra: {\n        isTruncated: function () {\n          return textEl.isTruncated;\n        },\n        value: rawLabel,\n        tickIndex: index\n      }\n    });\n    // Pack data for mouse event\n    if (triggerEvent) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisLabel';\n      eventData.value = rawLabel;\n      eventData.tickIndex = index;\n      if (axis.type === 'category') {\n        eventData.dataIndex = tickValue;\n      }\n      getECData(textEl).eventData = eventData;\n    }\n    // FIXME\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    labelEls.push(textEl);\n    group.add(textEl);\n    textEl.decomposeTransform();\n  });\n  return labelEls;\n}\nexport default AxisBuilder;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,QAAQ,0BAA0B;AACrI,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,kBAAkB,EAAEC,SAAS,QAAQ,sBAAsB;AACpE,SAASC,YAAY,EAAEC,qBAAqB,QAAQ,sBAAsB;AAC1E,OAAO,KAAKC,UAAU,MAAM,4BAA4B;AACxD,SAASC,cAAc,IAAIC,gBAAgB,QAAQ,4BAA4B;AAC/E,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,kCAAkC;AACjF,IAAIC,EAAE,GAAGC,IAAI,CAACD,EAAE;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAG,aAAa,YAAY;EACzC,SAASA,WAAWA,CAACC,SAAS,EAAEC,GAAG,EAAE;IACnC,IAAI,CAACC,KAAK,GAAG,IAAInB,OAAO,CAACoB,KAAK,CAAC,CAAC;IAChC,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B;IACA1B,QAAQ,CAAC2B,GAAG,EAAE;MACZG,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,MAAM,EAAE,IAAI;MACZC,eAAe,EAAE,SAAAA,CAAA,EAAY;QAC3B,OAAO,IAAI;MACb;IACF,CAAC,CAAC;IACF;IACA,IAAIC,cAAc,GAAG,IAAI3B,OAAO,CAACoB,KAAK,CAAC;MACrCQ,CAAC,EAAEV,GAAG,CAACW,QAAQ,CAAC,CAAC,CAAC;MAClBC,CAAC,EAAEZ,GAAG,CAACW,QAAQ,CAAC,CAAC,CAAC;MAClBE,QAAQ,EAAEb,GAAG,CAACa;IAChB,CAAC,CAAC;IACF;IACA;IACAJ,cAAc,CAACK,eAAe,CAAC,CAAC;IAChC,IAAI,CAACC,eAAe,GAAGN,cAAc;EACvC;EACAX,WAAW,CAACkB,SAAS,CAACC,UAAU,GAAG,UAAUC,IAAI,EAAE;IACjD,OAAO,CAAC,CAACC,QAAQ,CAACD,IAAI,CAAC;EACzB,CAAC;EACDpB,WAAW,CAACkB,SAAS,CAACI,GAAG,GAAG,UAAUF,IAAI,EAAE;IAC1CC,QAAQ,CAACD,IAAI,CAAC,CAAC,IAAI,CAAClB,GAAG,EAAE,IAAI,CAACD,SAAS,EAAE,IAAI,CAACE,KAAK,EAAE,IAAI,CAACc,eAAe,CAAC;EAC5E,CAAC;EACDjB,WAAW,CAACkB,SAAS,CAACK,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACpB,KAAK;EACnB,CAAC;EACDH,WAAW,CAACwB,eAAe,GAAG,UAAUC,YAAY,EAAEC,YAAY,EAAEC,SAAS,EAAE;IAC7E,IAAIC,YAAY,GAAGvC,SAAS,CAACqC,YAAY,GAAGD,YAAY,CAAC;IACzD,IAAII,SAAS;IACb,IAAIC,iBAAiB;IACrB,IAAI1C,kBAAkB,CAACwC,YAAY,CAAC,EAAE;MACpC;MACAE,iBAAiB,GAAGH,SAAS,GAAG,CAAC,GAAG,KAAK,GAAG,QAAQ;MACpDE,SAAS,GAAG,QAAQ;IACtB,CAAC,MAAM,IAAIzC,kBAAkB,CAACwC,YAAY,GAAG9B,EAAE,CAAC,EAAE;MAChD;MACAgC,iBAAiB,GAAGH,SAAS,GAAG,CAAC,GAAG,QAAQ,GAAG,KAAK;MACpDE,SAAS,GAAG,QAAQ;IACtB,CAAC,MAAM;MACLC,iBAAiB,GAAG,QAAQ;MAC5B,IAAIF,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAG9B,EAAE,EAAE;QACzC+B,SAAS,GAAGF,SAAS,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM;MAC9C,CAAC,MAAM;QACLE,SAAS,GAAGF,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO;MAC9C;IACF;IACA,OAAO;MACLZ,QAAQ,EAAEa,YAAY;MACtBC,SAAS,EAAEA,SAAS;MACpBC,iBAAiB,EAAEA;IACrB,CAAC;EACH,CAAC;EACD9B,WAAW,CAAC+B,qBAAqB,GAAG,UAAU9B,SAAS,EAAE;IACvD,IAAI+B,SAAS,GAAG;MACdC,aAAa,EAAEhC,SAAS,CAACiC,QAAQ;MACjCC,cAAc,EAAElC,SAAS,CAACkC;IAC5B,CAAC;IACDH,SAAS,CAAC/B,SAAS,CAACiC,QAAQ,GAAG,OAAO,CAAC,GAAGjC,SAAS,CAACkC,cAAc;IAClE,OAAOH,SAAS;EAClB,CAAC;EACDhC,WAAW,CAACoC,aAAa,GAAG,UAAUnC,SAAS,EAAE;IAC/C,IAAIoC,UAAU,GAAGpC,SAAS,CAACqC,GAAG,CAAC,SAAS,CAAC;IACzC,OAAOrC,SAAS,CAACqC,GAAG,CAAC,QAAQ;IAC7B;IAAA,GACG,EAAErC,SAAS,CAACqC,GAAG,CAAC,cAAc,CAAC,IAAID,UAAU,IAAIA,UAAU,CAACE,IAAI,CAAC;EACtE,CAAC;EACD,OAAOvC,WAAW;AACpB,CAAC,CAAC,CAAC;AACH;AACA,IAAIqB,QAAQ,GAAG;EACbmB,QAAQ,EAAE,SAAAA,CAAUtC,GAAG,EAAED,SAAS,EAAEE,KAAK,EAAEQ,cAAc,EAAE;IACzD,IAAI8B,KAAK,GAAGxC,SAAS,CAACqC,GAAG,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IAC/C,IAAIG,KAAK,KAAK,MAAM,IAAIvC,GAAG,CAACQ,eAAe,EAAE;MAC3C+B,KAAK,GAAGvC,GAAG,CAACQ,eAAe,CAAC,UAAU,CAAC;IACzC;IACA,IAAI,CAAC+B,KAAK,EAAE;MACV;IACF;IACA,IAAIC,MAAM,GAAGzC,SAAS,CAAC0C,IAAI,CAACC,SAAS,CAAC,CAAC;IACvC,IAAIC,MAAM,GAAGlC,cAAc,CAACmC,SAAS;IACrC,IAAIC,GAAG,GAAG,CAACL,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxB,IAAIM,GAAG,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACxB,IAAIO,OAAO,GAAGF,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC;IAC7B,IAAIH,MAAM,EAAE;MACVnD,gBAAgB,CAACqD,GAAG,EAAEA,GAAG,EAAEF,MAAM,CAAC;MAClCnD,gBAAgB,CAACsD,GAAG,EAAEA,GAAG,EAAEH,MAAM,CAAC;IACpC;IACA,IAAIK,SAAS,GAAG1E,MAAM,CAAC;MACrB2E,OAAO,EAAE;IACX,CAAC,EAAElD,SAAS,CAACmD,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CAAC;IAChE,IAAIC,IAAI,GAAG,IAAItE,OAAO,CAACuE,IAAI,CAAC;MAC1BC,KAAK,EAAE;QACLC,EAAE,EAAEV,GAAG,CAAC,CAAC,CAAC;QACVW,EAAE,EAAEX,GAAG,CAAC,CAAC,CAAC;QACVY,EAAE,EAAEX,GAAG,CAAC,CAAC,CAAC;QACVY,EAAE,EAAEZ,GAAG,CAAC,CAAC;MACX,CAAC;MACDa,KAAK,EAAEX,SAAS;MAChBY,sBAAsB,EAAE5D,GAAG,CAAC4D,sBAAsB,IAAI,CAAC;MACvDrD,MAAM,EAAE,IAAI;MACZsD,EAAE,EAAE;IACN,CAAC,CAAC;IACF/E,OAAO,CAACgF,oBAAoB,CAACV,IAAI,CAACE,KAAK,EAAEF,IAAI,CAACO,KAAK,CAACI,SAAS,CAAC;IAC9DX,IAAI,CAACY,IAAI,GAAG,MAAM;IAClB/D,KAAK,CAACmB,GAAG,CAACgC,IAAI,CAAC;IACf,IAAIa,MAAM,GAAGlE,SAAS,CAACqC,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAClD,IAAI6B,MAAM,IAAI,IAAI,EAAE;MAClB,IAAIC,SAAS,GAAGnE,SAAS,CAACqC,GAAG,CAAC,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;MACzD,IAAI1D,QAAQ,CAACuF,MAAM,CAAC,EAAE;QACpB;QACAA,MAAM,GAAG,CAACA,MAAM,EAAEA,MAAM,CAAC;MAC3B;MACA,IAAIvF,QAAQ,CAACwF,SAAS,CAAC,IAAIvF,QAAQ,CAACuF,SAAS,CAAC,EAAE;QAC9C;QACAA,SAAS,GAAG,CAACA,SAAS,EAAEA,SAAS,CAAC;MACpC;MACA,IAAIC,WAAW,GAAG9E,qBAAqB,CAACU,SAAS,CAACqC,GAAG,CAAC,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE8B,SAAS,CAAC;MACpG,IAAIE,aAAa,GAAGF,SAAS,CAAC,CAAC,CAAC;MAChC,IAAIG,cAAc,GAAGH,SAAS,CAAC,CAAC,CAAC;MACjC3F,IAAI,CAAC,CAAC;QACJ+F,MAAM,EAAEtE,GAAG,CAACa,QAAQ,GAAGhB,IAAI,CAACD,EAAE,GAAG,CAAC;QAClC2E,MAAM,EAAEJ,WAAW,CAAC,CAAC,CAAC;QACtBK,CAAC,EAAE;MACL,CAAC,EAAE;QACDF,MAAM,EAAEtE,GAAG,CAACa,QAAQ,GAAGhB,IAAI,CAACD,EAAE,GAAG,CAAC;QAClC2E,MAAM,EAAEJ,WAAW,CAAC,CAAC,CAAC;QACtBK,CAAC,EAAE3E,IAAI,CAAC4E,IAAI,CAAC,CAAC5B,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,KAAKD,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAACD,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,KAAKD,GAAG,CAAC,CAAC,CAAC,GAAGC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5F,CAAC,CAAC,EAAE,UAAU4B,KAAK,EAAEC,KAAK,EAAE;QAC1B,IAAIV,MAAM,CAACU,KAAK,CAAC,KAAK,MAAM,IAAIV,MAAM,CAACU,KAAK,CAAC,IAAI,IAAI,EAAE;UACrD,IAAIC,MAAM,GAAGxF,YAAY,CAAC6E,MAAM,CAACU,KAAK,CAAC,EAAE,CAACP,aAAa,GAAG,CAAC,EAAE,CAACC,cAAc,GAAG,CAAC,EAAED,aAAa,EAAEC,cAAc,EAAErB,SAAS,CAAC6B,MAAM,EAAE,IAAI,CAAC;UACxI;UACA,IAAIL,CAAC,GAAGE,KAAK,CAACF,CAAC,GAAGE,KAAK,CAACH,MAAM;UAC9B,IAAIO,EAAE,GAAG/B,OAAO,GAAGD,GAAG,GAAGD,GAAG;UAC5B+B,MAAM,CAACG,IAAI,CAAC;YACVlE,QAAQ,EAAE6D,KAAK,CAACJ,MAAM;YACtB5D,CAAC,EAAEoE,EAAE,CAAC,CAAC,CAAC,GAAGN,CAAC,GAAG3E,IAAI,CAACmF,GAAG,CAAChF,GAAG,CAACa,QAAQ,CAAC;YACrCD,CAAC,EAAEkE,EAAE,CAAC,CAAC,CAAC,GAAGN,CAAC,GAAG3E,IAAI,CAACoF,GAAG,CAACjF,GAAG,CAACa,QAAQ,CAAC;YACrCN,MAAM,EAAE,IAAI;YACZsD,EAAE,EAAE;UACN,CAAC,CAAC;UACF5D,KAAK,CAACmB,GAAG,CAACwD,MAAM,CAAC;QACnB;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACDM,aAAa,EAAE,SAAAA,CAAUlF,GAAG,EAAED,SAAS,EAAEE,KAAK,EAAEQ,cAAc,EAAE;IAC9D,IAAI0E,QAAQ,GAAGC,mBAAmB,CAACnF,KAAK,EAAEQ,cAAc,EAAEV,SAAS,EAAEC,GAAG,CAAC;IACzE,IAAIqF,QAAQ,GAAGC,cAAc,CAACrF,KAAK,EAAEQ,cAAc,EAAEV,SAAS,EAAEC,GAAG,CAAC;IACpEuF,kBAAkB,CAACxF,SAAS,EAAEsF,QAAQ,EAAEF,QAAQ,CAAC;IACjDK,mBAAmB,CAACvF,KAAK,EAAEQ,cAAc,EAAEV,SAAS,EAAEC,GAAG,CAACK,aAAa,CAAC;IACxE;IACA;IACA,IAAIN,SAAS,CAACqC,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,EAAE;MAC/C,IAAIqD,SAAS,GAAG/F,iBAAiB,CAACjB,GAAG,CAAC4G,QAAQ,EAAE,UAAUK,KAAK,EAAE;QAC/D,OAAO;UACLA,KAAK,EAAEA,KAAK;UACZC,QAAQ,EAAED,KAAK,CAAC7B,EAAE;UAClB+B,WAAW,EAAE;YACXC,MAAM,EAAEH,KAAK,CAACG;UAChB;QACF,CAAC;MACH,CAAC,CAAC,CAAC;MACHlG,WAAW,CAAC8F,SAAS,CAAC;IACxB;EACF,CAAC;EACDK,QAAQ,EAAE,SAAAA,CAAU9F,GAAG,EAAED,SAAS,EAAEE,KAAK,EAAEQ,cAAc,EAAE;IACzD,IAAIS,IAAI,GAAG9C,QAAQ,CAAC4B,GAAG,CAAC8F,QAAQ,EAAE/F,SAAS,CAACqC,GAAG,CAAC,MAAM,CAAC,CAAC;IACxD,IAAI,CAAClB,IAAI,EAAE;MACT;IACF;IACA,IAAI6E,YAAY,GAAGhG,SAAS,CAACqC,GAAG,CAAC,cAAc,CAAC;IAChD,IAAIhC,aAAa,GAAGJ,GAAG,CAACI,aAAa;IACrC,IAAI4F,cAAc,GAAGjG,SAAS,CAACmD,QAAQ,CAAC,eAAe,CAAC;IACxD,IAAI+C,GAAG,GAAGlG,SAAS,CAACqC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;IACvC,IAAII,MAAM,GAAGzC,SAAS,CAAC0C,IAAI,CAACC,SAAS,CAAC,CAAC;IACvC,IAAIwD,SAAS,GAAG1D,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9C,IAAI2D,GAAG,GAAG,CAACJ,YAAY,KAAK,OAAO,GAAGvD,MAAM,CAAC,CAAC,CAAC,GAAG0D,SAAS,GAAGD,GAAG,GAAGF,YAAY,KAAK,KAAK,GAAGvD,MAAM,CAAC,CAAC,CAAC,GAAG0D,SAAS,GAAGD,GAAG,GAAG,CAACzD,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;IACtJ;IACA4D,oBAAoB,CAACL,YAAY,CAAC,GAAG/F,GAAG,CAACG,WAAW,GAAGC,aAAa,GAAG6F,GAAG,GAAG,CAAC,CAAC;IAC/E,IAAII,WAAW;IACf,IAAIC,YAAY,GAAGvG,SAAS,CAACqC,GAAG,CAAC,YAAY,CAAC;IAC9C,IAAIkE,YAAY,IAAI,IAAI,EAAE;MACxBA,YAAY,GAAGA,YAAY,GAAG1G,EAAE,GAAG,GAAG,CAAC,CAAC;IAC1C;IACA,IAAI2G,sBAAsB;IAC1B,IAAIH,oBAAoB,CAACL,YAAY,CAAC,EAAE;MACtCM,WAAW,GAAGvG,WAAW,CAACwB,eAAe,CAACtB,GAAG,CAACa,QAAQ,EAAEyF,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGtG,GAAG,CAACa,QAAQ;MAC1G;MACAT,aAAa,CAAC;IAChB,CAAC,MAAM;MACLiG,WAAW,GAAGG,aAAa,CAACxG,GAAG,CAACa,QAAQ,EAAEkF,YAAY,EAAEO,YAAY,IAAI,CAAC,EAAE9D,MAAM,CAAC;MAClF+D,sBAAsB,GAAGvG,GAAG,CAACuG,sBAAsB;MACnD,IAAIA,sBAAsB,IAAI,IAAI,EAAE;QAClCA,sBAAsB,GAAG1G,IAAI,CAAC4G,GAAG,CAACF,sBAAsB,GAAG1G,IAAI,CAACoF,GAAG,CAACoB,WAAW,CAACxF,QAAQ,CAAC,CAAC;QAC1F,CAAC6F,QAAQ,CAACH,sBAAsB,CAAC,KAAKA,sBAAsB,GAAG,IAAI,CAAC;MACtE;IACF;IACA,IAAII,QAAQ,GAAGX,cAAc,CAACY,OAAO,CAAC,CAAC;IACvC,IAAIC,WAAW,GAAG9G,SAAS,CAACqC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3D,IAAI0E,QAAQ,GAAGD,WAAW,CAACC,QAAQ;IACnC,IAAIC,QAAQ,GAAG3I,QAAQ,CAAC4B,GAAG,CAACgH,oBAAoB,EAAEH,WAAW,CAACE,QAAQ,EAAER,sBAAsB,CAAC;IAC/F,IAAIU,MAAM,GAAG,IAAInI,OAAO,CAACoI,IAAI,CAAC;MAC5BxG,CAAC,EAAEyF,GAAG,CAAC,CAAC,CAAC;MACTvF,CAAC,EAAEuF,GAAG,CAAC,CAAC,CAAC;MACTtF,QAAQ,EAAEwF,WAAW,CAACxF,QAAQ;MAC9BN,MAAM,EAAET,WAAW,CAACoC,aAAa,CAACnC,SAAS,CAAC;MAC5C4D,KAAK,EAAE3E,eAAe,CAACgH,cAAc,EAAE;QACrCmB,IAAI,EAAEjG,IAAI;QACVkG,IAAI,EAAET,QAAQ;QACdU,QAAQ,EAAE,UAAU;QACpBC,KAAK,EAAEP,QAAQ;QACfD,QAAQ,EAAEA,QAAQ;QAClBS,IAAI,EAAEvB,cAAc,CAACwB,YAAY,CAAC,CAAC,IAAIzH,SAAS,CAACqC,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QACxFqF,KAAK,EAAEzB,cAAc,CAAC5D,GAAG,CAAC,OAAO,CAAC,IAAIiE,WAAW,CAAC1E,SAAS;QAC3D+F,aAAa,EAAE1B,cAAc,CAAC5D,GAAG,CAAC,eAAe,CAAC,IAAIiE,WAAW,CAACzE;MACpE,CAAC,CAAC;MACFiC,EAAE,EAAE;IACN,CAAC,CAAC;IACF/E,OAAO,CAAC6I,gBAAgB,CAAC;MACvBC,EAAE,EAAEX,MAAM;MACVY,cAAc,EAAE9H,SAAS;MACzB+H,QAAQ,EAAE5G;IACZ,CAAC,CAAC;IACF+F,MAAM,CAACc,UAAU,GAAG7G,IAAI;IACxB;IACA+F,MAAM,CAACjD,IAAI,GAAG,MAAM;IACpB,IAAIjE,SAAS,CAACqC,GAAG,CAAC,cAAc,CAAC,EAAE;MACjC,IAAIN,SAAS,GAAGhC,WAAW,CAAC+B,qBAAqB,CAAC9B,SAAS,CAAC;MAC5D+B,SAAS,CAACkG,UAAU,GAAG,UAAU;MACjClG,SAAS,CAACZ,IAAI,GAAGA,IAAI;MACrBnC,SAAS,CAACkI,MAAM,CAAC,CAACnF,SAAS,GAAGA,SAAS;IACzC;IACA;IACArB,cAAc,CAACW,GAAG,CAAC6F,MAAM,CAAC;IAC1BA,MAAM,CAACnG,eAAe,CAAC,CAAC;IACxBb,KAAK,CAACmB,GAAG,CAAC6F,MAAM,CAAC;IACjBA,MAAM,CAACgB,kBAAkB,CAAC,CAAC;EAC7B;AACF,CAAC;AACD,SAASzB,aAAaA,CAAC3F,QAAQ,EAAEqH,YAAY,EAAEC,UAAU,EAAE3F,MAAM,EAAE;EACjE,IAAId,YAAY,GAAGvC,SAAS,CAACgJ,UAAU,GAAGtH,QAAQ,CAAC;EACnD,IAAIc,SAAS;EACb,IAAIC,iBAAiB;EACrB,IAAImB,OAAO,GAAGP,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;EACnC,IAAI4F,MAAM,GAAGF,YAAY,KAAK,OAAO,IAAI,CAACnF,OAAO,IAAImF,YAAY,KAAK,OAAO,IAAInF,OAAO;EACxF,IAAI7D,kBAAkB,CAACwC,YAAY,GAAG9B,EAAE,GAAG,CAAC,CAAC,EAAE;IAC7CgC,iBAAiB,GAAGwG,MAAM,GAAG,QAAQ,GAAG,KAAK;IAC7CzG,SAAS,GAAG,QAAQ;EACtB,CAAC,MAAM,IAAIzC,kBAAkB,CAACwC,YAAY,GAAG9B,EAAE,GAAG,GAAG,CAAC,EAAE;IACtDgC,iBAAiB,GAAGwG,MAAM,GAAG,KAAK,GAAG,QAAQ;IAC7CzG,SAAS,GAAG,QAAQ;EACtB,CAAC,MAAM;IACLC,iBAAiB,GAAG,QAAQ;IAC5B,IAAIF,YAAY,GAAG9B,EAAE,GAAG,GAAG,IAAI8B,YAAY,GAAG9B,EAAE,GAAG,CAAC,EAAE;MACpD+B,SAAS,GAAGyG,MAAM,GAAG,MAAM,GAAG,OAAO;IACvC,CAAC,MAAM;MACLzG,SAAS,GAAGyG,MAAM,GAAG,OAAO,GAAG,MAAM;IACvC;EACF;EACA,OAAO;IACLvH,QAAQ,EAAEa,YAAY;IACtBC,SAAS,EAAEA,SAAS;IACpBC,iBAAiB,EAAEA;EACrB,CAAC;AACH;AACA,SAAS2D,kBAAkBA,CAACxF,SAAS,EAAEsF,QAAQ,EAAEgD,OAAO,EAAE;EACxD,IAAI5I,mBAAmB,CAACM,SAAS,CAAC0C,IAAI,CAAC,EAAE;IACvC;EACF;EACA;EACA;EACA;EACA,IAAI6F,YAAY,GAAGvI,SAAS,CAACqC,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;EAC/D,IAAImG,YAAY,GAAGxI,SAAS,CAACqC,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;EAC/D;EACA;EACAiD,QAAQ,GAAGA,QAAQ,IAAI,EAAE;EACzBgD,OAAO,GAAGA,OAAO,IAAI,EAAE;EACvB,IAAIG,UAAU,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EAC5B,IAAIoD,SAAS,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EAC3B,IAAIqD,SAAS,GAAGrD,QAAQ,CAACA,QAAQ,CAACsD,MAAM,GAAG,CAAC,CAAC;EAC7C,IAAIC,SAAS,GAAGvD,QAAQ,CAACA,QAAQ,CAACsD,MAAM,GAAG,CAAC,CAAC;EAC7C,IAAIE,SAAS,GAAGR,OAAO,CAAC,CAAC,CAAC;EAC1B,IAAIS,QAAQ,GAAGT,OAAO,CAAC,CAAC,CAAC;EACzB,IAAIU,QAAQ,GAAGV,OAAO,CAACA,OAAO,CAACM,MAAM,GAAG,CAAC,CAAC;EAC1C,IAAIK,QAAQ,GAAGX,OAAO,CAACA,OAAO,CAACM,MAAM,GAAG,CAAC,CAAC;EAC1C,IAAIL,YAAY,KAAK,KAAK,EAAE;IAC1BW,QAAQ,CAACT,UAAU,CAAC;IACpBS,QAAQ,CAACJ,SAAS,CAAC;EACrB,CAAC,MAAM,IAAIK,oBAAoB,CAACV,UAAU,EAAEC,SAAS,CAAC,EAAE;IACtD,IAAIH,YAAY,EAAE;MAChBW,QAAQ,CAACR,SAAS,CAAC;MACnBQ,QAAQ,CAACH,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLG,QAAQ,CAACT,UAAU,CAAC;MACpBS,QAAQ,CAACJ,SAAS,CAAC;IACrB;EACF;EACA,IAAIN,YAAY,KAAK,KAAK,EAAE;IAC1BU,QAAQ,CAACP,SAAS,CAAC;IACnBO,QAAQ,CAACF,QAAQ,CAAC;EACpB,CAAC,MAAM,IAAIG,oBAAoB,CAACN,SAAS,EAAEF,SAAS,CAAC,EAAE;IACrD,IAAIH,YAAY,EAAE;MAChBU,QAAQ,CAACL,SAAS,CAAC;MACnBK,QAAQ,CAACD,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLC,QAAQ,CAACP,SAAS,CAAC;MACnBO,QAAQ,CAACF,QAAQ,CAAC;IACpB;EACF;AACF;AACA,SAASE,QAAQA,CAACrB,EAAE,EAAE;EACpBA,EAAE,KAAKA,EAAE,CAAC/B,MAAM,GAAG,IAAI,CAAC;AAC1B;AACA,SAASqD,oBAAoBA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC3C;EACA,IAAIC,SAAS,GAAGF,OAAO,IAAIA,OAAO,CAACG,eAAe,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EAC5D,IAAIC,QAAQ,GAAGJ,IAAI,IAAIA,IAAI,CAACE,eAAe,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACrD,IAAI,CAACF,SAAS,IAAI,CAACG,QAAQ,EAAE;IAC3B;EACF;EACA;EACA;EACA,IAAIC,aAAa,GAAGnK,UAAU,CAACoK,QAAQ,CAAC,EAAE,CAAC;EAC3CpK,UAAU,CAACgF,MAAM,CAACmF,aAAa,EAAEA,aAAa,EAAE,CAACN,OAAO,CAACtI,QAAQ,CAAC;EAClEwI,SAAS,CAAC9J,cAAc,CAACD,UAAU,CAACqK,GAAG,CAAC,EAAE,EAAEF,aAAa,EAAEN,OAAO,CAACS,iBAAiB,CAAC,CAAC,CAAC,CAAC;EACxFJ,QAAQ,CAACjK,cAAc,CAACD,UAAU,CAACqK,GAAG,CAAC,EAAE,EAAEF,aAAa,EAAEL,IAAI,CAACQ,iBAAiB,CAAC,CAAC,CAAC,CAAC;EACpF,OAAOP,SAAS,CAACQ,SAAS,CAACL,QAAQ,CAAC;AACtC;AACA,SAASpD,oBAAoBA,CAACL,YAAY,EAAE;EAC1C,OAAOA,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,QAAQ;AAC/D;AACA,SAAS+D,WAAWA,CAACC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,UAAU,EAAE;EACxF,IAAI9B,OAAO,GAAG,EAAE;EAChB,IAAIxF,GAAG,GAAG,EAAE;EACZ,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIsH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,CAACpB,MAAM,EAAEyB,CAAC,EAAE,EAAE;IAC3C,IAAIC,SAAS,GAAGN,WAAW,CAACK,CAAC,CAAC,CAACE,KAAK;IACpCzH,GAAG,CAAC,CAAC,CAAC,GAAGwH,SAAS;IAClBxH,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IACVC,GAAG,CAAC,CAAC,CAAC,GAAGuH,SAAS;IAClBvH,GAAG,CAAC,CAAC,CAAC,GAAGmH,YAAY;IACrB,IAAID,aAAa,EAAE;MACjBxK,gBAAgB,CAACqD,GAAG,EAAEA,GAAG,EAAEmH,aAAa,CAAC;MACzCxK,gBAAgB,CAACsD,GAAG,EAAEA,GAAG,EAAEkH,aAAa,CAAC;IAC3C;IACA;IACA,IAAIO,MAAM,GAAG,IAAIzL,OAAO,CAACuE,IAAI,CAAC;MAC5BC,KAAK,EAAE;QACLC,EAAE,EAAEV,GAAG,CAAC,CAAC,CAAC;QACVW,EAAE,EAAEX,GAAG,CAAC,CAAC,CAAC;QACVY,EAAE,EAAEX,GAAG,CAAC,CAAC,CAAC;QACVY,EAAE,EAAEZ,GAAG,CAAC,CAAC;MACX,CAAC;MACDa,KAAK,EAAEuG,aAAa;MACpBrG,EAAE,EAAE,CAAC;MACL2G,SAAS,EAAE,IAAI;MACfjK,MAAM,EAAE;IACV,CAAC,CAAC;IACFzB,OAAO,CAACgF,oBAAoB,CAACyG,MAAM,CAACjH,KAAK,EAAEiH,MAAM,CAAC5G,KAAK,CAACI,SAAS,CAAC;IAClEwG,MAAM,CAACvG,IAAI,GAAGmG,UAAU,GAAG,GAAG,GAAGJ,WAAW,CAACK,CAAC,CAAC,CAACK,SAAS;IACzDpC,OAAO,CAACqC,IAAI,CAACH,MAAM,CAAC;EACtB;EACA,OAAOlC,OAAO;AAChB;AACA,SAASjD,mBAAmBA,CAACnF,KAAK,EAAEQ,cAAc,EAAEV,SAAS,EAAEC,GAAG,EAAE;EAClE,IAAIyC,IAAI,GAAG1C,SAAS,CAAC0C,IAAI;EACzB,IAAIkI,SAAS,GAAG5K,SAAS,CAACmD,QAAQ,CAAC,UAAU,CAAC;EAC9C,IAAIX,KAAK,GAAGoI,SAAS,CAACvI,GAAG,CAAC,MAAM,CAAC;EACjC,IAAIG,KAAK,KAAK,MAAM,IAAIvC,GAAG,CAACQ,eAAe,EAAE;IAC3C+B,KAAK,GAAGvC,GAAG,CAACQ,eAAe,CAAC,UAAU,CAAC;EACzC;EACA,IAAI,CAAC+B,KAAK,IAAIE,IAAI,CAACmI,KAAK,CAACC,OAAO,CAAC,CAAC,EAAE;IAClC;EACF;EACA,IAAIC,cAAc,GAAGH,SAAS,CAACzH,QAAQ,CAAC,WAAW,CAAC;EACpD,IAAI+G,YAAY,GAAGjK,GAAG,CAACK,aAAa,GAAGsK,SAAS,CAACvI,GAAG,CAAC,QAAQ,CAAC;EAC9D,IAAI2H,WAAW,GAAGtH,IAAI,CAACsI,cAAc,CAAC,CAAC;EACvC,IAAI5F,QAAQ,GAAG2E,WAAW,CAACC,WAAW,EAAEtJ,cAAc,CAACmC,SAAS,EAAEqH,YAAY,EAAE5L,QAAQ,CAACyM,cAAc,CAAC3H,YAAY,CAAC,CAAC,EAAE;IACtH0B,MAAM,EAAE9E,SAAS,CAACqC,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;EAC1D,CAAC,CAAC,EAAE,OAAO,CAAC;EACZ,KAAK,IAAIgI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjF,QAAQ,CAACwD,MAAM,EAAEyB,CAAC,EAAE,EAAE;IACxCnK,KAAK,CAACmB,GAAG,CAAC+D,QAAQ,CAACiF,CAAC,CAAC,CAAC;EACxB;EACA,OAAOjF,QAAQ;AACjB;AACA,SAASK,mBAAmBA,CAACvF,KAAK,EAAEQ,cAAc,EAAEV,SAAS,EAAEM,aAAa,EAAE;EAC5E,IAAIoC,IAAI,GAAG1C,SAAS,CAAC0C,IAAI;EACzB,IAAIuI,cAAc,GAAGjL,SAAS,CAACmD,QAAQ,CAAC,WAAW,CAAC;EACpD,IAAI,CAAC8H,cAAc,CAAC5I,GAAG,CAAC,MAAM,CAAC,IAAIK,IAAI,CAACmI,KAAK,CAACC,OAAO,CAAC,CAAC,EAAE;IACvD;EACF;EACA,IAAII,gBAAgB,GAAGxI,IAAI,CAACyI,mBAAmB,CAAC,CAAC;EACjD,IAAI,CAACD,gBAAgB,CAACtC,MAAM,EAAE;IAC5B;EACF;EACA,IAAImC,cAAc,GAAGE,cAAc,CAAC9H,QAAQ,CAAC,WAAW,CAAC;EACzD,IAAI+G,YAAY,GAAG5J,aAAa,GAAG2K,cAAc,CAAC5I,GAAG,CAAC,QAAQ,CAAC;EAC/D,IAAI+I,kBAAkB,GAAG9M,QAAQ,CAACyM,cAAc,CAAC3H,YAAY,CAAC,CAAC,EAAE9E,QAAQ,CAAC0B,SAAS,CAACmD,QAAQ,CAAC,UAAU,CAAC,CAACC,YAAY,CAAC,CAAC,EAAE;IACvH0B,MAAM,EAAE9E,SAAS,CAACqC,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;EAC1D,CAAC,CAAC,CAAC;EACH,KAAK,IAAIgI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,gBAAgB,CAACtC,MAAM,EAAEyB,CAAC,EAAE,EAAE;IAChD,IAAIgB,aAAa,GAAGtB,WAAW,CAACmB,gBAAgB,CAACb,CAAC,CAAC,EAAE3J,cAAc,CAACmC,SAAS,EAAEqH,YAAY,EAAEkB,kBAAkB,EAAE,aAAa,GAAGf,CAAC,CAAC;IACnI,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,aAAa,CAACzC,MAAM,EAAE0C,CAAC,EAAE,EAAE;MAC7CpL,KAAK,CAACmB,GAAG,CAACgK,aAAa,CAACC,CAAC,CAAC,CAAC;IAC7B;EACF;AACF;AACA,SAAS/F,cAAcA,CAACrF,KAAK,EAAEQ,cAAc,EAAEV,SAAS,EAAEC,GAAG,EAAE;EAC7D,IAAIyC,IAAI,GAAG1C,SAAS,CAAC0C,IAAI;EACzB,IAAIJ,IAAI,GAAGjE,QAAQ,CAAC4B,GAAG,CAACsL,aAAa,EAAEvL,SAAS,CAACqC,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;EAC5E,IAAI,CAACC,IAAI,IAAII,IAAI,CAACmI,KAAK,CAACC,OAAO,CAAC,CAAC,EAAE;IACjC;EACF;EACA,IAAIU,UAAU,GAAGxL,SAAS,CAACmD,QAAQ,CAAC,WAAW,CAAC;EAChD,IAAIsI,WAAW,GAAGD,UAAU,CAACnJ,GAAG,CAAC,QAAQ,CAAC;EAC1C,IAAIqJ,MAAM,GAAGhJ,IAAI,CAACiJ,aAAa,CAAC,CAAC;EACjC;EACA,IAAIC,aAAa,GAAG,CAACvN,QAAQ,CAAC4B,GAAG,CAAC4L,WAAW,EAAEL,UAAU,CAACnJ,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAIxC,EAAE,GAAG,GAAG;EACzF,IAAIyG,WAAW,GAAGvG,WAAW,CAACwB,eAAe,CAACtB,GAAG,CAACa,QAAQ,EAAE8K,aAAa,EAAE3L,GAAG,CAACM,cAAc,CAAC;EAC9F,IAAIuL,eAAe,GAAG9L,SAAS,CAAC+L,aAAa,IAAI/L,SAAS,CAAC+L,aAAa,CAAC,IAAI,CAAC;EAC9E,IAAIzG,QAAQ,GAAG,EAAE;EACjB,IAAI9E,MAAM,GAAGT,WAAW,CAACoC,aAAa,CAACnC,SAAS,CAAC;EACjD,IAAIgM,YAAY,GAAGhM,SAAS,CAACqC,GAAG,CAAC,cAAc,CAAC;EAChD7D,IAAI,CAACkN,MAAM,EAAE,UAAUO,SAAS,EAAErH,KAAK,EAAE;IACvC,IAAI8F,SAAS,GAAGhI,IAAI,CAACmI,KAAK,CAACqB,IAAI,KAAK,SAAS,GAAGxJ,IAAI,CAACmI,KAAK,CAACsB,mBAAmB,CAACF,SAAS,CAACvB,SAAS,CAAC,GAAGuB,SAAS,CAACvB,SAAS;IACzH,IAAI0B,cAAc,GAAGH,SAAS,CAACG,cAAc;IAC7C,IAAIC,QAAQ,GAAGJ,SAAS,CAACI,QAAQ;IACjC,IAAIC,cAAc,GAAGd,UAAU;IAC/B,IAAIM,eAAe,IAAIA,eAAe,CAACpB,SAAS,CAAC,EAAE;MACjD,IAAI6B,eAAe,GAAGT,eAAe,CAACpB,SAAS,CAAC;MAChD,IAAIjM,QAAQ,CAAC8N,eAAe,CAAC,IAAIA,eAAe,CAACC,SAAS,EAAE;QAC1DF,cAAc,GAAG,IAAIpN,KAAK,CAACqN,eAAe,CAACC,SAAS,EAAEhB,UAAU,EAAExL,SAAS,CAACyM,OAAO,CAAC;MACtF;IACF;IACA,IAAIC,SAAS,GAAGJ,cAAc,CAAC7E,YAAY,CAAC,CAAC,IAAIzH,SAAS,CAACqC,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;IAClG,IAAIiI,SAAS,GAAG5H,IAAI,CAACiK,WAAW,CAACjC,SAAS,CAAC;IAC3C,IAAIhD,KAAK,GAAG4E,cAAc,CAACM,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,IAAItG,WAAW,CAAC1E,SAAS;IAC7E,IAAIiL,QAAQ,GAAG/N,SAAS,CAACwN,cAAc,CAACM,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,EAAElF,KAAK,CAAC;IACjF,IAAIoF,QAAQ,GAAGhO,SAAS,CAACwN,cAAc,CAACM,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,EAAElF,KAAK,CAAC;IACjF,IAAIC,aAAa,GAAG2E,cAAc,CAACM,UAAU,CAAC,eAAe,EAAE,IAAI,CAAC,IAAIN,cAAc,CAACM,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,IAAItG,WAAW,CAACzE,iBAAiB;IACpJ,IAAIkL,gBAAgB,GAAGjO,SAAS,CAACwN,cAAc,CAACM,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAAEjF,aAAa,CAAC;IACzG,IAAIqF,gBAAgB,GAAGlO,SAAS,CAACwN,cAAc,CAACM,UAAU,CAAC,uBAAuB,EAAE,IAAI,CAAC,EAAEjF,aAAa,CAAC;IACzG,IAAIT,MAAM,GAAG,IAAInI,OAAO,CAACoI,IAAI,CAAC;MAC5BxG,CAAC,EAAE2J,SAAS;MACZzJ,CAAC,EAAEZ,GAAG,CAACG,WAAW,GAAGH,GAAG,CAACM,cAAc,GAAGkL,WAAW;MACrD3K,QAAQ,EAAEwF,WAAW,CAACxF,QAAQ;MAC9BN,MAAM,EAAEA,MAAM;MACdsD,EAAE,EAAE,EAAE,IAAImI,SAAS,CAACgB,KAAK,IAAI,CAAC,CAAC;MAC/BrJ,KAAK,EAAE3E,eAAe,CAACqN,cAAc,EAAE;QACrClF,IAAI,EAAEgF,cAAc;QACpB1E,KAAK,EAAE9C,KAAK,KAAK,CAAC,GAAGiI,QAAQ,GAAGjI,KAAK,KAAK8G,MAAM,CAAC9C,MAAM,GAAG,CAAC,GAAGkE,QAAQ,GAAGpF,KAAK;QAC9EC,aAAa,EAAE/C,KAAK,KAAK,CAAC,GAAGmI,gBAAgB,GAAGnI,KAAK,KAAK8G,MAAM,CAAC9C,MAAM,GAAG,CAAC,GAAGoE,gBAAgB,GAAGrF,aAAa;QAC9GH,IAAI,EAAE3I,UAAU,CAAC6N,SAAS,CAAC,GAAGA,SAAS;QACvC;QACA;QACA;QACA;QACA;QACA;QACA;QACAhK,IAAI,CAACwJ,IAAI,KAAK,UAAU,GAAGG,QAAQ,GAAG3J,IAAI,CAACwJ,IAAI,KAAK,OAAO,GAAGxB,SAAS,GAAG,EAAE,GAAGA,SAAS,EAAE9F,KAAK,CAAC,GAAG8H;MACrG,CAAC;IACH,CAAC,CAAC;IACFxF,MAAM,CAACjD,IAAI,GAAG,QAAQ,GAAGyG,SAAS;IAClC3L,OAAO,CAAC6I,gBAAgB,CAAC;MACvBC,EAAE,EAAEX,MAAM;MACVY,cAAc,EAAE9H,SAAS;MACzB+H,QAAQ,EAAEqE,cAAc;MACxBc,oBAAoB,EAAE;QACpBC,WAAW,EAAE,SAAAA,CAAA,EAAY;UACvB,OAAOjG,MAAM,CAACiG,WAAW;QAC3B,CAAC;QACDC,KAAK,EAAEf,QAAQ;QACfgB,SAAS,EAAEzI;MACb;IACF,CAAC,CAAC;IACF;IACA,IAAIoH,YAAY,EAAE;MAChB,IAAIjK,SAAS,GAAGhC,WAAW,CAAC+B,qBAAqB,CAAC9B,SAAS,CAAC;MAC5D+B,SAAS,CAACkG,UAAU,GAAG,WAAW;MAClClG,SAAS,CAACqL,KAAK,GAAGf,QAAQ;MAC1BtK,SAAS,CAACsL,SAAS,GAAGzI,KAAK;MAC3B,IAAIlC,IAAI,CAACwJ,IAAI,KAAK,UAAU,EAAE;QAC5BnK,SAAS,CAACuL,SAAS,GAAG5C,SAAS;MACjC;MACA1L,SAAS,CAACkI,MAAM,CAAC,CAACnF,SAAS,GAAGA,SAAS;IACzC;IACA;IACArB,cAAc,CAACW,GAAG,CAAC6F,MAAM,CAAC;IAC1BA,MAAM,CAACnG,eAAe,CAAC,CAAC;IACxBuE,QAAQ,CAACqF,IAAI,CAACzD,MAAM,CAAC;IACrBhH,KAAK,CAACmB,GAAG,CAAC6F,MAAM,CAAC;IACjBA,MAAM,CAACgB,kBAAkB,CAAC,CAAC;EAC7B,CAAC,CAAC;EACF,OAAO5C,QAAQ;AACjB;AACA,eAAevF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}