{"name": "globals", "version": "11.12.0", "description": "Global identifiers from different JavaScript environments", "license": "MIT", "repository": "sindresorhus/globals", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "globals.json"], "keywords": ["globals", "global", "identifiers", "variables", "vars", "j<PERSON>t", "eslint", "environments"], "devDependencies": {"ava": "0.21.0", "xo": "0.18.0"}, "xo": {"ignores": ["get-browser-globals.js"]}}