{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport AxisView from '../axis/AxisView.js';\nimport CartesianAxisPointer from './CartesianAxisPointer.js';\nimport AxisPointerModel from './AxisPointerModel.js';\nimport AxisPointerView from './AxisPointerView.js';\nimport { isArray } from 'zrender/lib/core/util.js';\nimport { collect } from './modelHelper.js';\nimport axisTrigger from './axisTrigger.js';\nexport function install(registers) {\n  // CartesianAxisPointer is not supposed to be required here. But consider\n  // echarts.simple.js and online build tooltip, which only require gridSimple,\n  // CartesianAxisPointer should be able to required somewhere.\n  AxisView.registerAxisPointerClass('CartesianAxisPointer', CartesianAxisPointer);\n  registers.registerComponentModel(AxisPointerModel);\n  registers.registerComponentView(AxisPointerView);\n  registers.registerPreprocessor(function (option) {\n    // Always has a global axisPointerModel for default setting.\n    if (option) {\n      (!option.axisPointer || option.axisPointer.length === 0) && (option.axisPointer = {});\n      var link = option.axisPointer.link;\n      // Normalize to array to avoid object mergin. But if link\n      // is not set, remain null/undefined, otherwise it will\n      // override existent link setting.\n      if (link && !isArray(link)) {\n        option.axisPointer.link = [link];\n      }\n    }\n  });\n  // This process should proformed after coordinate systems created\n  // and series data processed. So put it on statistic processing stage.\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, function (ecModel, api) {\n    // Build axisPointerModel, mergin tooltip.axisPointer model for each axis.\n    // allAxesInfo should be updated when setOption performed.\n    ecModel.getComponent('axisPointer').coordSysAxesInfo = collect(ecModel, api);\n  });\n  // Broadcast to all views.\n  registers.registerAction({\n    type: 'updateAxisPointer',\n    event: 'updateAxisPointer',\n    update: ':updateAxisPointer'\n  }, axisTrigger);\n}", "map": {"version": 3, "names": ["AxisView", "<PERSON><PERSON>ian<PERSON><PERSON>s<PERSON>oint<PERSON>", "AxisPointerModel", "AxisPointerView", "isArray", "collect", "axisTrigger", "install", "registers", "registerAxisPointerClass", "registerComponentModel", "registerComponentView", "registerPreprocessor", "option", "axisPointer", "length", "link", "registerProcessor", "PRIORITY", "PROCESSOR", "STATISTIC", "ecModel", "api", "getComponent", "coordSysAxesInfo", "registerAction", "type", "event", "update"], "sources": ["E:/AllProject/datafront/node_modules/echarts/lib/component/axisPointer/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport AxisView from '../axis/AxisView.js';\nimport CartesianAxisPointer from './CartesianAxisPointer.js';\nimport AxisPointerModel from './AxisPointerModel.js';\nimport AxisPointerView from './AxisPointerView.js';\nimport { isArray } from 'zrender/lib/core/util.js';\nimport { collect } from './modelHelper.js';\nimport axisTrigger from './axisTrigger.js';\nexport function install(registers) {\n  // CartesianAxisPointer is not supposed to be required here. But consider\n  // echarts.simple.js and online build tooltip, which only require gridSimple,\n  // CartesianAxisPointer should be able to required somewhere.\n  AxisView.registerAxisPointerClass('CartesianAxisPointer', CartesianAxisPointer);\n  registers.registerComponentModel(AxisPointerModel);\n  registers.registerComponentView(AxisPointerView);\n  registers.registerPreprocessor(function (option) {\n    // Always has a global axisPointerModel for default setting.\n    if (option) {\n      (!option.axisPointer || option.axisPointer.length === 0) && (option.axisPointer = {});\n      var link = option.axisPointer.link;\n      // Normalize to array to avoid object mergin. But if link\n      // is not set, remain null/undefined, otherwise it will\n      // override existent link setting.\n      if (link && !isArray(link)) {\n        option.axisPointer.link = [link];\n      }\n    }\n  });\n  // This process should proformed after coordinate systems created\n  // and series data processed. So put it on statistic processing stage.\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, function (ecModel, api) {\n    // Build axisPointerModel, mergin tooltip.axisPointer model for each axis.\n    // allAxesInfo should be updated when setOption performed.\n    ecModel.getComponent('axisPointer').coordSysAxesInfo = collect(ecModel, api);\n  });\n  // Broadcast to all views.\n  registers.registerAction({\n    type: 'updateAxisPointer',\n    event: 'updateAxisPointer',\n    update: ':updateAxisPointer'\n  }, axisTrigger);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,qBAAqB;AAC1C,OAAOC,oBAAoB,MAAM,2BAA2B;AAC5D,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,eAAe,MAAM,sBAAsB;AAClD,SAASC,OAAO,QAAQ,0BAA0B;AAClD,SAASC,OAAO,QAAQ,kBAAkB;AAC1C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjC;EACA;EACA;EACAR,QAAQ,CAACS,wBAAwB,CAAC,sBAAsB,EAAER,oBAAoB,CAAC;EAC/EO,SAAS,CAACE,sBAAsB,CAACR,gBAAgB,CAAC;EAClDM,SAAS,CAACG,qBAAqB,CAACR,eAAe,CAAC;EAChDK,SAAS,CAACI,oBAAoB,CAAC,UAAUC,MAAM,EAAE;IAC/C;IACA,IAAIA,MAAM,EAAE;MACV,CAAC,CAACA,MAAM,CAACC,WAAW,IAAID,MAAM,CAACC,WAAW,CAACC,MAAM,KAAK,CAAC,MAAMF,MAAM,CAACC,WAAW,GAAG,CAAC,CAAC,CAAC;MACrF,IAAIE,IAAI,GAAGH,MAAM,CAACC,WAAW,CAACE,IAAI;MAClC;MACA;MACA;MACA,IAAIA,IAAI,IAAI,CAACZ,OAAO,CAACY,IAAI,CAAC,EAAE;QAC1BH,MAAM,CAACC,WAAW,CAACE,IAAI,GAAG,CAACA,IAAI,CAAC;MAClC;IACF;EACF,CAAC,CAAC;EACF;EACA;EACAR,SAAS,CAACS,iBAAiB,CAACT,SAAS,CAACU,QAAQ,CAACC,SAAS,CAACC,SAAS,EAAE,UAAUC,OAAO,EAAEC,GAAG,EAAE;IAC1F;IACA;IACAD,OAAO,CAACE,YAAY,CAAC,aAAa,CAAC,CAACC,gBAAgB,GAAGnB,OAAO,CAACgB,OAAO,EAAEC,GAAG,CAAC;EAC9E,CAAC,CAAC;EACF;EACAd,SAAS,CAACiB,cAAc,CAAC;IACvBC,IAAI,EAAE,mBAAmB;IACzBC,KAAK,EAAE,mBAAmB;IAC1BC,MAAM,EAAE;EACV,CAAC,EAAEtB,WAAW,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}