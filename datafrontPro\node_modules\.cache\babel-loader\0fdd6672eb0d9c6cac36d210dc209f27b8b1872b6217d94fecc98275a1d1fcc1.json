{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport { WhiskerBoxCommonMixin } from '../helper/whiskerBoxCommon.js';\nimport { mixin } from 'zrender/lib/core/util.js';\nvar BoxplotSeriesModel = /** @class */function (_super) {\n  __extends(BoxplotSeriesModel, _super);\n  function BoxplotSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BoxplotSeriesModel.type;\n    // TODO\n    // box width represents group size, so dimension should have 'size'.\n    /**\r\n     * @see <https://en.wikipedia.org/wiki/Box_plot>\r\n     * The meanings of 'min' and 'max' depend on user,\r\n     * and echarts do not need to know it.\r\n     * @readOnly\r\n     */\n    _this.defaultValueDimensions = [{\n      name: 'min',\n      defaultTooltip: true\n    }, {\n      name: 'Q1',\n      defaultTooltip: true\n    }, {\n      name: 'median',\n      defaultTooltip: true\n    }, {\n      name: 'Q3',\n      defaultTooltip: true\n    }, {\n      name: 'max',\n      defaultTooltip: true\n    }];\n    _this.visualDrawType = 'stroke';\n    return _this;\n  }\n  BoxplotSeriesModel.type = 'series.boxplot';\n  BoxplotSeriesModel.dependencies = ['xAxis', 'yAxis', 'grid'];\n  BoxplotSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    layout: null,\n    boxWidth: [7, 50],\n    itemStyle: {\n      color: '#fff',\n      borderWidth: 1\n    },\n    emphasis: {\n      scale: true,\n      itemStyle: {\n        borderWidth: 2,\n        shadowBlur: 5,\n        shadowOffsetX: 1,\n        shadowOffsetY: 1,\n        shadowColor: 'rgba(0,0,0,0.2)'\n      }\n    },\n    animationDuration: 800\n  };\n  return BoxplotSeriesModel;\n}(SeriesModel);\nmixin(BoxplotSeriesModel, WhiskerBoxCommonMixin, true);\nexport default BoxplotSeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesModel", "WhiskerBoxCommonMixin", "mixin", "BoxplotSeriesModel", "_super", "_this", "apply", "arguments", "type", "defaultValueDimensions", "name", "defaultTooltip", "visualDrawType", "dependencies", "defaultOption", "z", "coordinateSystem", "legendHoverLink", "layout", "boxWidth", "itemStyle", "color", "borderWidth", "emphasis", "scale", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "shadowColor", "animationDuration"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/chart/boxplot/BoxplotSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport { WhiskerBoxCommonMixin } from '../helper/whiskerBoxCommon.js';\nimport { mixin } from 'zrender/lib/core/util.js';\nvar BoxplotSeriesModel = /** @class */function (_super) {\n  __extends(BoxplotSeriesModel, _super);\n  function BoxplotSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BoxplotSeriesModel.type;\n    // TODO\n    // box width represents group size, so dimension should have 'size'.\n    /**\r\n     * @see <https://en.wikipedia.org/wiki/Box_plot>\r\n     * The meanings of 'min' and 'max' depend on user,\r\n     * and echarts do not need to know it.\r\n     * @readOnly\r\n     */\n    _this.defaultValueDimensions = [{\n      name: 'min',\n      defaultTooltip: true\n    }, {\n      name: 'Q1',\n      defaultTooltip: true\n    }, {\n      name: 'median',\n      defaultTooltip: true\n    }, {\n      name: 'Q3',\n      defaultTooltip: true\n    }, {\n      name: 'max',\n      defaultTooltip: true\n    }];\n    _this.visualDrawType = 'stroke';\n    return _this;\n  }\n  BoxplotSeriesModel.type = 'series.boxplot';\n  BoxplotSeriesModel.dependencies = ['xAxis', 'yAxis', 'grid'];\n  BoxplotSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    layout: null,\n    boxWidth: [7, 50],\n    itemStyle: {\n      color: '#fff',\n      borderWidth: 1\n    },\n    emphasis: {\n      scale: true,\n      itemStyle: {\n        borderWidth: 2,\n        shadowBlur: 5,\n        shadowOffsetX: 1,\n        shadowOffsetY: 1,\n        shadowColor: 'rgba(0,0,0,0.2)'\n      }\n    },\n    animationDuration: 800\n  };\n  return BoxplotSeriesModel;\n}(SeriesModel);\nmixin(BoxplotSeriesModel, WhiskerBoxCommonMixin, true);\nexport default BoxplotSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,qBAAqB,QAAQ,+BAA+B;AACrE,SAASC,KAAK,QAAQ,0BAA0B;AAChD,IAAIC,kBAAkB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACtDL,SAAS,CAACI,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,kBAAkB,CAACK,IAAI;IACpC;IACA;IACA;AACJ;AACA;AACA;AACA;AACA;IACIH,KAAK,CAACI,sBAAsB,GAAG,CAAC;MAC9BC,IAAI,EAAE,KAAK;MACXC,cAAc,EAAE;IAClB,CAAC,EAAE;MACDD,IAAI,EAAE,IAAI;MACVC,cAAc,EAAE;IAClB,CAAC,EAAE;MACDD,IAAI,EAAE,QAAQ;MACdC,cAAc,EAAE;IAClB,CAAC,EAAE;MACDD,IAAI,EAAE,IAAI;MACVC,cAAc,EAAE;IAClB,CAAC,EAAE;MACDD,IAAI,EAAE,KAAK;MACXC,cAAc,EAAE;IAClB,CAAC,CAAC;IACFN,KAAK,CAACO,cAAc,GAAG,QAAQ;IAC/B,OAAOP,KAAK;EACd;EACAF,kBAAkB,CAACK,IAAI,GAAG,gBAAgB;EAC1CL,kBAAkB,CAACU,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;EAC5DV,kBAAkB,CAACW,aAAa,GAAG;IACjC;IACAC,CAAC,EAAE,CAAC;IACJC,gBAAgB,EAAE,aAAa;IAC/BC,eAAe,EAAE,IAAI;IACrBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACjBC,SAAS,EAAE;MACTC,KAAK,EAAE,MAAM;MACbC,WAAW,EAAE;IACf,CAAC;IACDC,QAAQ,EAAE;MACRC,KAAK,EAAE,IAAI;MACXJ,SAAS,EAAE;QACTE,WAAW,EAAE,CAAC;QACdG,UAAU,EAAE,CAAC;QACbC,aAAa,EAAE,CAAC;QAChBC,aAAa,EAAE,CAAC;QAChBC,WAAW,EAAE;MACf;IACF,CAAC;IACDC,iBAAiB,EAAE;EACrB,CAAC;EACD,OAAO1B,kBAAkB;AAC3B,CAAC,CAACH,WAAW,CAAC;AACdE,KAAK,CAACC,kBAAkB,EAAEF,qBAAqB,EAAE,IAAI,CAAC;AACtD,eAAeE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}