{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport Model from '../../model/Model.js';\nimport ComponentModel from '../../model/Component.js';\nvar DEFAULT_OUT_OF_BRUSH_COLOR = '#ddd';\nvar BrushModel = /** @class */function (_super) {\n  __extends(BrushModel, _super);\n  function BrushModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BrushModel.type;\n    /**\r\n     * @readOnly\r\n     */\n    _this.areas = [];\n    /**\r\n     * Current brush painting area settings.\r\n     * @readOnly\r\n     */\n    _this.brushOption = {};\n    return _this;\n  }\n  BrushModel.prototype.optionUpdated = function (newOption, isInit) {\n    var thisOption = this.option;\n    !isInit && visualSolution.replaceVisualOption(thisOption, newOption, ['inBrush', 'outOfBrush']);\n    var inBrush = thisOption.inBrush = thisOption.inBrush || {};\n    // Always give default visual, consider setOption at the second time.\n    thisOption.outOfBrush = thisOption.outOfBrush || {\n      color: DEFAULT_OUT_OF_BRUSH_COLOR\n    };\n    if (!inBrush.hasOwnProperty('liftZ')) {\n      // Bigger than the highlight z lift, otherwise it will\n      // be effected by the highlight z when brush.\n      inBrush.liftZ = 5;\n    }\n  };\n  /**\r\n   * If `areas` is null/undefined, range state remain.\r\n   */\n  BrushModel.prototype.setAreas = function (areas) {\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.assert(zrUtil.isArray(areas));\n      zrUtil.each(areas, function (area) {\n        zrUtil.assert(area.brushType, 'Illegal areas');\n      });\n    }\n    // If areas is null/undefined, range state remain.\n    // This helps user to dispatchAction({type: 'brush'}) with no areas\n    // set but just want to get the current brush select info from a `brush` event.\n    if (!areas) {\n      return;\n    }\n    this.areas = zrUtil.map(areas, function (area) {\n      return generateBrushOption(this.option, area);\n    }, this);\n  };\n  /**\r\n   * Set the current painting brush option.\r\n   */\n  BrushModel.prototype.setBrushOption = function (brushOption) {\n    this.brushOption = generateBrushOption(this.option, brushOption);\n    this.brushType = this.brushOption.brushType;\n  };\n  BrushModel.type = 'brush';\n  BrushModel.dependencies = ['geo', 'grid', 'xAxis', 'yAxis', 'parallel', 'series'];\n  BrushModel.defaultOption = {\n    seriesIndex: 'all',\n    brushType: 'rect',\n    brushMode: 'single',\n    transformable: true,\n    brushStyle: {\n      borderWidth: 1,\n      color: 'rgba(210,219,238,0.3)',\n      borderColor: '#D2DBEE'\n    },\n    throttleType: 'fixRate',\n    throttleDelay: 0,\n    removeOnClick: true,\n    z: 10000\n  };\n  return BrushModel;\n}(ComponentModel);\nfunction generateBrushOption(option, brushOption) {\n  return zrUtil.merge({\n    brushType: option.brushType,\n    brushMode: option.brushMode,\n    transformable: option.transformable,\n    brushStyle: new Model(option.brushStyle).getItemStyle(),\n    removeOnClick: option.removeOnClick,\n    z: option.z\n  }, brushOption, true);\n}\nexport default BrushModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "visualSolution", "Model", "ComponentModel", "DEFAULT_OUT_OF_BRUSH_COLOR", "BrushModel", "_super", "_this", "apply", "arguments", "type", "areas", "brushOption", "prototype", "optionUpdated", "newOption", "isInit", "thisOption", "option", "replaceVisualOption", "inBrush", "outOfBrush", "color", "hasOwnProperty", "liftZ", "<PERSON><PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "assert", "isArray", "each", "area", "brushType", "map", "generateBrushOption", "setBrushOption", "dependencies", "defaultOption", "seriesIndex", "brushMode", "transformable", "brushStyle", "borderWidth", "borderColor", "throttleType", "throttle<PERSON><PERSON><PERSON>", "removeOnClick", "z", "merge", "getItemStyle"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/brush/BrushModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport Model from '../../model/Model.js';\nimport ComponentModel from '../../model/Component.js';\nvar DEFAULT_OUT_OF_BRUSH_COLOR = '#ddd';\nvar BrushModel = /** @class */function (_super) {\n  __extends(BrushModel, _super);\n  function BrushModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BrushModel.type;\n    /**\r\n     * @readOnly\r\n     */\n    _this.areas = [];\n    /**\r\n     * Current brush painting area settings.\r\n     * @readOnly\r\n     */\n    _this.brushOption = {};\n    return _this;\n  }\n  BrushModel.prototype.optionUpdated = function (newOption, isInit) {\n    var thisOption = this.option;\n    !isInit && visualSolution.replaceVisualOption(thisOption, newOption, ['inBrush', 'outOfBrush']);\n    var inBrush = thisOption.inBrush = thisOption.inBrush || {};\n    // Always give default visual, consider setOption at the second time.\n    thisOption.outOfBrush = thisOption.outOfBrush || {\n      color: DEFAULT_OUT_OF_BRUSH_COLOR\n    };\n    if (!inBrush.hasOwnProperty('liftZ')) {\n      // Bigger than the highlight z lift, otherwise it will\n      // be effected by the highlight z when brush.\n      inBrush.liftZ = 5;\n    }\n  };\n  /**\r\n   * If `areas` is null/undefined, range state remain.\r\n   */\n  BrushModel.prototype.setAreas = function (areas) {\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.assert(zrUtil.isArray(areas));\n      zrUtil.each(areas, function (area) {\n        zrUtil.assert(area.brushType, 'Illegal areas');\n      });\n    }\n    // If areas is null/undefined, range state remain.\n    // This helps user to dispatchAction({type: 'brush'}) with no areas\n    // set but just want to get the current brush select info from a `brush` event.\n    if (!areas) {\n      return;\n    }\n    this.areas = zrUtil.map(areas, function (area) {\n      return generateBrushOption(this.option, area);\n    }, this);\n  };\n  /**\r\n   * Set the current painting brush option.\r\n   */\n  BrushModel.prototype.setBrushOption = function (brushOption) {\n    this.brushOption = generateBrushOption(this.option, brushOption);\n    this.brushType = this.brushOption.brushType;\n  };\n  BrushModel.type = 'brush';\n  BrushModel.dependencies = ['geo', 'grid', 'xAxis', 'yAxis', 'parallel', 'series'];\n  BrushModel.defaultOption = {\n    seriesIndex: 'all',\n    brushType: 'rect',\n    brushMode: 'single',\n    transformable: true,\n    brushStyle: {\n      borderWidth: 1,\n      color: 'rgba(210,219,238,0.3)',\n      borderColor: '#D2DBEE'\n    },\n    throttleType: 'fixRate',\n    throttleDelay: 0,\n    removeOnClick: true,\n    z: 10000\n  };\n  return BrushModel;\n}(ComponentModel);\nfunction generateBrushOption(option, brushOption) {\n  return zrUtil.merge({\n    brushType: option.brushType,\n    brushMode: option.brushMode,\n    transformable: option.transformable,\n    brushStyle: new Model(option.brushStyle).getItemStyle(),\n    removeOnClick: option.removeOnClick,\n    z: option.z\n  }, brushOption, true);\n}\nexport default BrushModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,cAAc,MAAM,gCAAgC;AAChE,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,cAAc,MAAM,0BAA0B;AACrD,IAAIC,0BAA0B,GAAG,MAAM;AACvC,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9CP,SAAS,CAACM,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,UAAU,CAACK,IAAI;IAC5B;AACJ;AACA;IACIH,KAAK,CAACI,KAAK,GAAG,EAAE;IAChB;AACJ;AACA;AACA;IACIJ,KAAK,CAACK,WAAW,GAAG,CAAC,CAAC;IACtB,OAAOL,KAAK;EACd;EACAF,UAAU,CAACQ,SAAS,CAACC,aAAa,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;IAChE,IAAIC,UAAU,GAAG,IAAI,CAACC,MAAM;IAC5B,CAACF,MAAM,IAAIf,cAAc,CAACkB,mBAAmB,CAACF,UAAU,EAAEF,SAAS,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IAC/F,IAAIK,OAAO,GAAGH,UAAU,CAACG,OAAO,GAAGH,UAAU,CAACG,OAAO,IAAI,CAAC,CAAC;IAC3D;IACAH,UAAU,CAACI,UAAU,GAAGJ,UAAU,CAACI,UAAU,IAAI;MAC/CC,KAAK,EAAElB;IACT,CAAC;IACD,IAAI,CAACgB,OAAO,CAACG,cAAc,CAAC,OAAO,CAAC,EAAE;MACpC;MACA;MACAH,OAAO,CAACI,KAAK,GAAG,CAAC;IACnB;EACF,CAAC;EACD;AACF;AACA;EACEnB,UAAU,CAACQ,SAAS,CAACY,QAAQ,GAAG,UAAUd,KAAK,EAAE;IAC/C,IAAIe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC5B,MAAM,CAAC6B,MAAM,CAAC7B,MAAM,CAAC8B,OAAO,CAACnB,KAAK,CAAC,CAAC;MACpCX,MAAM,CAAC+B,IAAI,CAACpB,KAAK,EAAE,UAAUqB,IAAI,EAAE;QACjChC,MAAM,CAAC6B,MAAM,CAACG,IAAI,CAACC,SAAS,EAAE,eAAe,CAAC;MAChD,CAAC,CAAC;IACJ;IACA;IACA;IACA;IACA,IAAI,CAACtB,KAAK,EAAE;MACV;IACF;IACA,IAAI,CAACA,KAAK,GAAGX,MAAM,CAACkC,GAAG,CAACvB,KAAK,EAAE,UAAUqB,IAAI,EAAE;MAC7C,OAAOG,mBAAmB,CAAC,IAAI,CAACjB,MAAM,EAAEc,IAAI,CAAC;IAC/C,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;EACE3B,UAAU,CAACQ,SAAS,CAACuB,cAAc,GAAG,UAAUxB,WAAW,EAAE;IAC3D,IAAI,CAACA,WAAW,GAAGuB,mBAAmB,CAAC,IAAI,CAACjB,MAAM,EAAEN,WAAW,CAAC;IAChE,IAAI,CAACqB,SAAS,GAAG,IAAI,CAACrB,WAAW,CAACqB,SAAS;EAC7C,CAAC;EACD5B,UAAU,CAACK,IAAI,GAAG,OAAO;EACzBL,UAAU,CAACgC,YAAY,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC;EACjFhC,UAAU,CAACiC,aAAa,GAAG;IACzBC,WAAW,EAAE,KAAK;IAClBN,SAAS,EAAE,MAAM;IACjBO,SAAS,EAAE,QAAQ;IACnBC,aAAa,EAAE,IAAI;IACnBC,UAAU,EAAE;MACVC,WAAW,EAAE,CAAC;MACdrB,KAAK,EAAE,uBAAuB;MAC9BsB,WAAW,EAAE;IACf,CAAC;IACDC,YAAY,EAAE,SAAS;IACvBC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,IAAI;IACnBC,CAAC,EAAE;EACL,CAAC;EACD,OAAO3C,UAAU;AACnB,CAAC,CAACF,cAAc,CAAC;AACjB,SAASgC,mBAAmBA,CAACjB,MAAM,EAAEN,WAAW,EAAE;EAChD,OAAOZ,MAAM,CAACiD,KAAK,CAAC;IAClBhB,SAAS,EAAEf,MAAM,CAACe,SAAS;IAC3BO,SAAS,EAAEtB,MAAM,CAACsB,SAAS;IAC3BC,aAAa,EAAEvB,MAAM,CAACuB,aAAa;IACnCC,UAAU,EAAE,IAAIxC,KAAK,CAACgB,MAAM,CAACwB,UAAU,CAAC,CAACQ,YAAY,CAAC,CAAC;IACvDH,aAAa,EAAE7B,MAAM,CAAC6B,aAAa;IACnCC,CAAC,EAAE9B,MAAM,CAAC8B;EACZ,CAAC,EAAEpC,WAAW,EAAE,IAAI,CAAC;AACvB;AACA,eAAeP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}