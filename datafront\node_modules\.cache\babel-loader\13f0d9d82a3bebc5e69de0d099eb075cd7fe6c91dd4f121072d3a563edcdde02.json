{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport Group from 'zrender/lib/graphic/Group.js';\nimport * as componentUtil from '../util/component.js';\nimport * as clazzUtil from '../util/clazz.js';\nvar ComponentView = /** @class */function () {\n  function ComponentView() {\n    this.group = new Group();\n    this.uid = componentUtil.getUID('viewComponent');\n  }\n  ComponentView.prototype.init = function (ecModel, api) {};\n  ComponentView.prototype.render = function (model, ecModel, api, payload) {};\n  ComponentView.prototype.dispose = function (ecModel, api) {};\n  ComponentView.prototype.updateView = function (model, ecModel, api, payload) {\n    // Do nothing;\n  };\n  ComponentView.prototype.updateLayout = function (model, ecModel, api, payload) {\n    // Do nothing;\n  };\n  ComponentView.prototype.updateVisual = function (model, ecModel, api, payload) {\n    // Do nothing;\n  };\n  /**\r\n   * Hook for toggle blur target series.\r\n   * Can be used in marker for blur or leave blur the markers\r\n   */\n  ComponentView.prototype.toggleBlurSeries = function (seriesModels, isBlur, ecModel) {\n    // Do nothing;\n  };\n  /**\r\n   * Traverse the new rendered elements.\r\n   *\r\n   * It will traverse the new added element in progressive rendering.\r\n   * And traverse all in normal rendering.\r\n   */\n  ComponentView.prototype.eachRendered = function (cb) {\n    var group = this.group;\n    if (group) {\n      group.traverse(cb);\n    }\n  };\n  return ComponentView;\n}();\n;\nclazzUtil.enableClassExtend(ComponentView);\nclazzUtil.enableClassManagement(ComponentView);\nexport default ComponentView;", "map": {"version": 3, "names": ["Group", "componentUtil", "clazzUtil", "ComponentView", "group", "uid", "getUID", "prototype", "init", "ecModel", "api", "render", "model", "payload", "dispose", "updateView", "updateLayout", "updateVisual", "toggleBlurSeries", "seriesModels", "isBlur", "eachRendered", "cb", "traverse", "enableClassExtend", "enableClassManagement"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/view/Component.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport Group from 'zrender/lib/graphic/Group.js';\nimport * as componentUtil from '../util/component.js';\nimport * as clazzUtil from '../util/clazz.js';\nvar ComponentView = /** @class */function () {\n  function ComponentView() {\n    this.group = new Group();\n    this.uid = componentUtil.getUID('viewComponent');\n  }\n  ComponentView.prototype.init = function (ecModel, api) {};\n  ComponentView.prototype.render = function (model, ecModel, api, payload) {};\n  ComponentView.prototype.dispose = function (ecModel, api) {};\n  ComponentView.prototype.updateView = function (model, ecModel, api, payload) {\n    // Do nothing;\n  };\n  ComponentView.prototype.updateLayout = function (model, ecModel, api, payload) {\n    // Do nothing;\n  };\n  ComponentView.prototype.updateVisual = function (model, ecModel, api, payload) {\n    // Do nothing;\n  };\n  /**\r\n   * Hook for toggle blur target series.\r\n   * Can be used in marker for blur or leave blur the markers\r\n   */\n  ComponentView.prototype.toggleBlurSeries = function (seriesModels, isBlur, ecModel) {\n    // Do nothing;\n  };\n  /**\r\n   * Traverse the new rendered elements.\r\n   *\r\n   * It will traverse the new added element in progressive rendering.\r\n   * And traverse all in normal rendering.\r\n   */\n  ComponentView.prototype.eachRendered = function (cb) {\n    var group = this.group;\n    if (group) {\n      group.traverse(cb);\n    }\n  };\n  return ComponentView;\n}();\n;\nclazzUtil.enableClassExtend(ComponentView);\nclazzUtil.enableClassManagement(ComponentView);\nexport default ComponentView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,8BAA8B;AAChD,OAAO,KAAKC,aAAa,MAAM,sBAAsB;AACrD,OAAO,KAAKC,SAAS,MAAM,kBAAkB;AAC7C,IAAIC,aAAa,GAAG,aAAa,YAAY;EAC3C,SAASA,aAAaA,CAAA,EAAG;IACvB,IAAI,CAACC,KAAK,GAAG,IAAIJ,KAAK,CAAC,CAAC;IACxB,IAAI,CAACK,GAAG,GAAGJ,aAAa,CAACK,MAAM,CAAC,eAAe,CAAC;EAClD;EACAH,aAAa,CAACI,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE,CAAC,CAAC;EACzDP,aAAa,CAACI,SAAS,CAACI,MAAM,GAAG,UAAUC,KAAK,EAAEH,OAAO,EAAEC,GAAG,EAAEG,OAAO,EAAE,CAAC,CAAC;EAC3EV,aAAa,CAACI,SAAS,CAACO,OAAO,GAAG,UAAUL,OAAO,EAAEC,GAAG,EAAE,CAAC,CAAC;EAC5DP,aAAa,CAACI,SAAS,CAACQ,UAAU,GAAG,UAAUH,KAAK,EAAEH,OAAO,EAAEC,GAAG,EAAEG,OAAO,EAAE;IAC3E;EAAA,CACD;EACDV,aAAa,CAACI,SAAS,CAACS,YAAY,GAAG,UAAUJ,KAAK,EAAEH,OAAO,EAAEC,GAAG,EAAEG,OAAO,EAAE;IAC7E;EAAA,CACD;EACDV,aAAa,CAACI,SAAS,CAACU,YAAY,GAAG,UAAUL,KAAK,EAAEH,OAAO,EAAEC,GAAG,EAAEG,OAAO,EAAE;IAC7E;EAAA,CACD;EACD;AACF;AACA;AACA;EACEV,aAAa,CAACI,SAAS,CAACW,gBAAgB,GAAG,UAAUC,YAAY,EAAEC,MAAM,EAAEX,OAAO,EAAE;IAClF;EAAA,CACD;EACD;AACF;AACA;AACA;AACA;AACA;EACEN,aAAa,CAACI,SAAS,CAACc,YAAY,GAAG,UAAUC,EAAE,EAAE;IACnD,IAAIlB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACmB,QAAQ,CAACD,EAAE,CAAC;IACpB;EACF,CAAC;EACD,OAAOnB,aAAa;AACtB,CAAC,CAAC,CAAC;AACH;AACAD,SAAS,CAACsB,iBAAiB,CAACrB,aAAa,CAAC;AAC1CD,SAAS,CAACuB,qBAAqB,CAACtB,aAAa,CAAC;AAC9C,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}