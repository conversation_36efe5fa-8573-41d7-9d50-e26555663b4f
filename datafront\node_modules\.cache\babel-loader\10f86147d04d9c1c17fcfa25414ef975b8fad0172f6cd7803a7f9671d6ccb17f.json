{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport brushPreprocessor from './preprocessor.js';\nimport BrushView from './BrushView.js';\nimport BrushModel from './BrushModel.js';\nimport brushVisual from './visualEncoding.js';\n// TODO\nimport BrushFeature from '../toolbox/feature/Brush.js';\nimport { registerFeature } from '../toolbox/featureManager.js';\nimport { noop } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  registers.registerComponentView(BrushView);\n  registers.registerComponentModel(BrushModel);\n  registers.registerPreprocessor(brushPreprocessor);\n  registers.registerVisual(registers.PRIORITY.VISUAL.BRUSH, brushVisual);\n  registers.registerAction({\n    type: 'brush',\n    event: 'brush',\n    update: 'updateVisual'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'brush',\n      query: payload\n    }, function (brushModel) {\n      brushModel.setAreas(payload.areas);\n    });\n  });\n  /**\r\n   * payload: {\r\n   *      brushComponents: [\r\n   *          {\r\n   *              brushId,\r\n   *              brushIndex,\r\n   *              brushName,\r\n   *              series: [\r\n   *                  {\r\n   *                      seriesId,\r\n   *                      seriesIndex,\r\n   *                      seriesName,\r\n   *                      rawIndices: [21, 34, ...]\r\n   *                  },\r\n   *                  ...\r\n   *              ]\r\n   *          },\r\n   *          ...\r\n   *      ]\r\n   * }\r\n   */\n  registers.registerAction({\n    type: 'brushSelect',\n    event: 'brushSelected',\n    update: 'none'\n  }, noop);\n  registers.registerAction({\n    type: 'brushEnd',\n    event: 'brushEnd',\n    update: 'none'\n  }, noop);\n  registerFeature('brush', BrushFeature);\n}", "map": {"version": 3, "names": ["brushPreprocessor", "BrushView", "BrushModel", "brushVisual", "BrushFeature", "registerFeature", "noop", "install", "registers", "registerComponentView", "registerComponentModel", "registerPreprocessor", "registerVisual", "PRIORITY", "VISUAL", "BRUSH", "registerAction", "type", "event", "update", "payload", "ecModel", "eachComponent", "mainType", "query", "brushModel", "<PERSON><PERSON><PERSON><PERSON>", "areas"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/brush/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport brushPreprocessor from './preprocessor.js';\nimport BrushView from './BrushView.js';\nimport BrushModel from './BrushModel.js';\nimport brushVisual from './visualEncoding.js';\n// TODO\nimport BrushFeature from '../toolbox/feature/Brush.js';\nimport { registerFeature } from '../toolbox/featureManager.js';\nimport { noop } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  registers.registerComponentView(BrushView);\n  registers.registerComponentModel(BrushModel);\n  registers.registerPreprocessor(brushPreprocessor);\n  registers.registerVisual(registers.PRIORITY.VISUAL.BRUSH, brushVisual);\n  registers.registerAction({\n    type: 'brush',\n    event: 'brush',\n    update: 'updateVisual'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'brush',\n      query: payload\n    }, function (brushModel) {\n      brushModel.setAreas(payload.areas);\n    });\n  });\n  /**\r\n   * payload: {\r\n   *      brushComponents: [\r\n   *          {\r\n   *              brushId,\r\n   *              brushIndex,\r\n   *              brushName,\r\n   *              series: [\r\n   *                  {\r\n   *                      seriesId,\r\n   *                      seriesIndex,\r\n   *                      seriesName,\r\n   *                      rawIndices: [21, 34, ...]\r\n   *                  },\r\n   *                  ...\r\n   *              ]\r\n   *          },\r\n   *          ...\r\n   *      ]\r\n   * }\r\n   */\n  registers.registerAction({\n    type: 'brushSelect',\n    event: 'brushSelected',\n    update: 'none'\n  }, noop);\n  registers.registerAction({\n    type: 'brushEnd',\n    event: 'brushEnd',\n    update: 'none'\n  }, noop);\n  registerFeature('brush', BrushFeature);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,iBAAiB,MAAM,mBAAmB;AACjD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C;AACA,OAAOC,YAAY,MAAM,6BAA6B;AACtD,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,IAAI,QAAQ,0BAA0B;AAC/C,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,qBAAqB,CAACR,SAAS,CAAC;EAC1CO,SAAS,CAACE,sBAAsB,CAACR,UAAU,CAAC;EAC5CM,SAAS,CAACG,oBAAoB,CAACX,iBAAiB,CAAC;EACjDQ,SAAS,CAACI,cAAc,CAACJ,SAAS,CAACK,QAAQ,CAACC,MAAM,CAACC,KAAK,EAAEZ,WAAW,CAAC;EACtEK,SAAS,CAACQ,cAAc,CAAC;IACvBC,IAAI,EAAE,OAAO;IACbC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE;EACV,CAAC,EAAE,UAAUC,OAAO,EAAEC,OAAO,EAAE;IAC7BA,OAAO,CAACC,aAAa,CAAC;MACpBC,QAAQ,EAAE,OAAO;MACjBC,KAAK,EAAEJ;IACT,CAAC,EAAE,UAAUK,UAAU,EAAE;MACvBA,UAAU,CAACC,QAAQ,CAACN,OAAO,CAACO,KAAK,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEnB,SAAS,CAACQ,cAAc,CAAC;IACvBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE;EACV,CAAC,EAAEb,IAAI,CAAC;EACRE,SAAS,CAACQ,cAAc,CAAC;IACvBC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,UAAU;IACjBC,MAAM,EAAE;EACV,CAAC,EAAEb,IAAI,CAAC;EACRD,eAAe,CAAC,OAAO,EAAED,YAAY,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}