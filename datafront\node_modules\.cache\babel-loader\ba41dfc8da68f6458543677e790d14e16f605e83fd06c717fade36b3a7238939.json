{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Symbol factory\nimport { each, isArray, retrieve2 } from 'zrender/lib/core/util.js';\nimport * as graphic from './graphic.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { calculateTextPosition } from 'zrender/lib/contain/text.js';\nimport { parsePercent } from './number.js';\n/**\r\n * Triangle shape\r\n * @inner\r\n */\nvar Triangle = graphic.Path.extend({\n  type: 'triangle',\n  shape: {\n    cx: 0,\n    cy: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var width = shape.width / 2;\n    var height = shape.height / 2;\n    path.moveTo(cx, cy - height);\n    path.lineTo(cx + width, cy + height);\n    path.lineTo(cx - width, cy + height);\n    path.closePath();\n  }\n});\n/**\r\n * Diamond shape\r\n * @inner\r\n */\nvar Diamond = graphic.Path.extend({\n  type: 'diamond',\n  shape: {\n    cx: 0,\n    cy: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var width = shape.width / 2;\n    var height = shape.height / 2;\n    path.moveTo(cx, cy - height);\n    path.lineTo(cx + width, cy);\n    path.lineTo(cx, cy + height);\n    path.lineTo(cx - width, cy);\n    path.closePath();\n  }\n});\n/**\r\n * Pin shape\r\n * @inner\r\n */\nvar Pin = graphic.Path.extend({\n  type: 'pin',\n  shape: {\n    // x, y on the cusp\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var x = shape.x;\n    var y = shape.y;\n    var w = shape.width / 5 * 3;\n    // Height must be larger than width\n    var h = Math.max(w, shape.height);\n    var r = w / 2;\n    // Dist on y with tangent point and circle center\n    var dy = r * r / (h - r);\n    var cy = y - h + r + dy;\n    var angle = Math.asin(dy / r);\n    // Dist on x with tangent point and circle center\n    var dx = Math.cos(angle) * r;\n    var tanX = Math.sin(angle);\n    var tanY = Math.cos(angle);\n    var cpLen = r * 0.6;\n    var cpLen2 = r * 0.7;\n    path.moveTo(x - dx, cy + dy);\n    path.arc(x, cy, r, Math.PI - angle, Math.PI * 2 + angle);\n    path.bezierCurveTo(x + dx - tanX * cpLen, cy + dy + tanY * cpLen, x, y - cpLen2, x, y);\n    path.bezierCurveTo(x, y - cpLen2, x - dx + tanX * cpLen, cy + dy + tanY * cpLen, x - dx, cy + dy);\n    path.closePath();\n  }\n});\n/**\r\n * Arrow shape\r\n * @inner\r\n */\nvar Arrow = graphic.Path.extend({\n  type: 'arrow',\n  shape: {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (ctx, shape) {\n    var height = shape.height;\n    var width = shape.width;\n    var x = shape.x;\n    var y = shape.y;\n    var dx = width / 3 * 2;\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + dx, y + height);\n    ctx.lineTo(x, y + height / 4 * 3);\n    ctx.lineTo(x - dx, y + height);\n    ctx.lineTo(x, y);\n    ctx.closePath();\n  }\n});\n/**\r\n * Map of path constructors\r\n */\n// TODO Use function to build symbol path.\nvar symbolCtors = {\n  line: graphic.Line,\n  rect: graphic.Rect,\n  roundRect: graphic.Rect,\n  square: graphic.Rect,\n  circle: graphic.Circle,\n  diamond: Diamond,\n  pin: Pin,\n  arrow: Arrow,\n  triangle: Triangle\n};\nvar symbolShapeMakers = {\n  line: function (x, y, w, h, shape) {\n    shape.x1 = x;\n    shape.y1 = y + h / 2;\n    shape.x2 = x + w;\n    shape.y2 = y + h / 2;\n  },\n  rect: function (x, y, w, h, shape) {\n    shape.x = x;\n    shape.y = y;\n    shape.width = w;\n    shape.height = h;\n  },\n  roundRect: function (x, y, w, h, shape) {\n    shape.x = x;\n    shape.y = y;\n    shape.width = w;\n    shape.height = h;\n    shape.r = Math.min(w, h) / 4;\n  },\n  square: function (x, y, w, h, shape) {\n    var size = Math.min(w, h);\n    shape.x = x;\n    shape.y = y;\n    shape.width = size;\n    shape.height = size;\n  },\n  circle: function (x, y, w, h, shape) {\n    // Put circle in the center of square\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.r = Math.min(w, h) / 2;\n  },\n  diamond: function (x, y, w, h, shape) {\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  pin: function (x, y, w, h, shape) {\n    shape.x = x + w / 2;\n    shape.y = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  arrow: function (x, y, w, h, shape) {\n    shape.x = x + w / 2;\n    shape.y = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  triangle: function (x, y, w, h, shape) {\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  }\n};\nexport var symbolBuildProxies = {};\neach(symbolCtors, function (Ctor, name) {\n  symbolBuildProxies[name] = new Ctor();\n});\nvar SymbolClz = graphic.Path.extend({\n  type: 'symbol',\n  shape: {\n    symbolType: '',\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  calculateTextPosition: function (out, config, rect) {\n    var res = calculateTextPosition(out, config, rect);\n    var shape = this.shape;\n    if (shape && shape.symbolType === 'pin' && config.position === 'inside') {\n      res.y = rect.y + rect.height * 0.4;\n    }\n    return res;\n  },\n  buildPath: function (ctx, shape, inBundle) {\n    var symbolType = shape.symbolType;\n    if (symbolType !== 'none') {\n      var proxySymbol = symbolBuildProxies[symbolType];\n      if (!proxySymbol) {\n        // Default rect\n        symbolType = 'rect';\n        proxySymbol = symbolBuildProxies[symbolType];\n      }\n      symbolShapeMakers[symbolType](shape.x, shape.y, shape.width, shape.height, proxySymbol.shape);\n      proxySymbol.buildPath(ctx, proxySymbol.shape, inBundle);\n    }\n  }\n});\n// Provide setColor helper method to avoid determine if set the fill or stroke outside\nfunction symbolPathSetColor(color, innerColor) {\n  if (this.type !== 'image') {\n    var symbolStyle = this.style;\n    if (this.__isEmptyBrush) {\n      symbolStyle.stroke = color;\n      symbolStyle.fill = innerColor || '#fff';\n      // TODO Same width with lineStyle in LineView\n      symbolStyle.lineWidth = 2;\n    } else if (this.shape.symbolType === 'line') {\n      symbolStyle.stroke = color;\n    } else {\n      symbolStyle.fill = color;\n    }\n    this.markRedraw();\n  }\n}\n/**\r\n * Create a symbol element with given symbol configuration: shape, x, y, width, height, color\r\n */\nexport function createSymbol(symbolType, x, y, w, h, color,\n// whether to keep the ratio of w/h,\nkeepAspect) {\n  // TODO Support image object, DynamicImage.\n  var isEmpty = symbolType.indexOf('empty') === 0;\n  if (isEmpty) {\n    symbolType = symbolType.substr(5, 1).toLowerCase() + symbolType.substr(6);\n  }\n  var symbolPath;\n  if (symbolType.indexOf('image://') === 0) {\n    symbolPath = graphic.makeImage(symbolType.slice(8), new BoundingRect(x, y, w, h), keepAspect ? 'center' : 'cover');\n  } else if (symbolType.indexOf('path://') === 0) {\n    symbolPath = graphic.makePath(symbolType.slice(7), {}, new BoundingRect(x, y, w, h), keepAspect ? 'center' : 'cover');\n  } else {\n    symbolPath = new SymbolClz({\n      shape: {\n        symbolType: symbolType,\n        x: x,\n        y: y,\n        width: w,\n        height: h\n      }\n    });\n  }\n  symbolPath.__isEmptyBrush = isEmpty;\n  // TODO Should deprecate setColor\n  symbolPath.setColor = symbolPathSetColor;\n  if (color) {\n    symbolPath.setColor(color);\n  }\n  return symbolPath;\n}\nexport function normalizeSymbolSize(symbolSize) {\n  if (!isArray(symbolSize)) {\n    symbolSize = [+symbolSize, +symbolSize];\n  }\n  return [symbolSize[0] || 0, symbolSize[1] || 0];\n}\nexport function normalizeSymbolOffset(symbolOffset, symbolSize) {\n  if (symbolOffset == null) {\n    return;\n  }\n  if (!isArray(symbolOffset)) {\n    symbolOffset = [symbolOffset, symbolOffset];\n  }\n  return [parsePercent(symbolOffset[0], symbolSize[0]) || 0, parsePercent(retrieve2(symbolOffset[1], symbolOffset[0]), symbolSize[1]) || 0];\n}", "map": {"version": 3, "names": ["each", "isArray", "retrieve2", "graphic", "BoundingRect", "calculateTextPosition", "parsePercent", "Triangle", "Path", "extend", "type", "shape", "cx", "cy", "width", "height", "buildPath", "path", "moveTo", "lineTo", "closePath", "Diamond", "<PERSON>n", "x", "y", "w", "h", "Math", "max", "r", "dy", "angle", "asin", "dx", "cos", "tanX", "sin", "tanY", "cpLen", "cpLen2", "arc", "PI", "bezierCurveTo", "Arrow", "ctx", "symbolCtors", "line", "Line", "rect", "Rect", "roundRect", "square", "circle", "Circle", "diamond", "pin", "arrow", "triangle", "symbolShapeMakers", "x1", "y1", "x2", "y2", "min", "size", "symbolBuildProxies", "Ctor", "name", "SymbolClz", "symbolType", "out", "config", "res", "position", "inBundle", "proxySymbol", "symbolPathSetColor", "color", "innerColor", "symbolStyle", "style", "__isEmptyBrush", "stroke", "fill", "lineWidth", "mark<PERSON><PERSON><PERSON>", "createSymbol", "keepAspect", "isEmpty", "indexOf", "substr", "toLowerCase", "symbolPath", "makeImage", "slice", "<PERSON><PERSON><PERSON>", "setColor", "normalizeSymbolSize", "symbolSize", "normalizeSymbolOffset", "symbolOffset"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/util/symbol.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Symbol factory\nimport { each, isArray, retrieve2 } from 'zrender/lib/core/util.js';\nimport * as graphic from './graphic.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { calculateTextPosition } from 'zrender/lib/contain/text.js';\nimport { parsePercent } from './number.js';\n/**\r\n * Triangle shape\r\n * @inner\r\n */\nvar Triangle = graphic.Path.extend({\n  type: 'triangle',\n  shape: {\n    cx: 0,\n    cy: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var width = shape.width / 2;\n    var height = shape.height / 2;\n    path.moveTo(cx, cy - height);\n    path.lineTo(cx + width, cy + height);\n    path.lineTo(cx - width, cy + height);\n    path.closePath();\n  }\n});\n/**\r\n * Diamond shape\r\n * @inner\r\n */\nvar Diamond = graphic.Path.extend({\n  type: 'diamond',\n  shape: {\n    cx: 0,\n    cy: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var cx = shape.cx;\n    var cy = shape.cy;\n    var width = shape.width / 2;\n    var height = shape.height / 2;\n    path.moveTo(cx, cy - height);\n    path.lineTo(cx + width, cy);\n    path.lineTo(cx, cy + height);\n    path.lineTo(cx - width, cy);\n    path.closePath();\n  }\n});\n/**\r\n * Pin shape\r\n * @inner\r\n */\nvar Pin = graphic.Path.extend({\n  type: 'pin',\n  shape: {\n    // x, y on the cusp\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (path, shape) {\n    var x = shape.x;\n    var y = shape.y;\n    var w = shape.width / 5 * 3;\n    // Height must be larger than width\n    var h = Math.max(w, shape.height);\n    var r = w / 2;\n    // Dist on y with tangent point and circle center\n    var dy = r * r / (h - r);\n    var cy = y - h + r + dy;\n    var angle = Math.asin(dy / r);\n    // Dist on x with tangent point and circle center\n    var dx = Math.cos(angle) * r;\n    var tanX = Math.sin(angle);\n    var tanY = Math.cos(angle);\n    var cpLen = r * 0.6;\n    var cpLen2 = r * 0.7;\n    path.moveTo(x - dx, cy + dy);\n    path.arc(x, cy, r, Math.PI - angle, Math.PI * 2 + angle);\n    path.bezierCurveTo(x + dx - tanX * cpLen, cy + dy + tanY * cpLen, x, y - cpLen2, x, y);\n    path.bezierCurveTo(x, y - cpLen2, x - dx + tanX * cpLen, cy + dy + tanY * cpLen, x - dx, cy + dy);\n    path.closePath();\n  }\n});\n/**\r\n * Arrow shape\r\n * @inner\r\n */\nvar Arrow = graphic.Path.extend({\n  type: 'arrow',\n  shape: {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  buildPath: function (ctx, shape) {\n    var height = shape.height;\n    var width = shape.width;\n    var x = shape.x;\n    var y = shape.y;\n    var dx = width / 3 * 2;\n    ctx.moveTo(x, y);\n    ctx.lineTo(x + dx, y + height);\n    ctx.lineTo(x, y + height / 4 * 3);\n    ctx.lineTo(x - dx, y + height);\n    ctx.lineTo(x, y);\n    ctx.closePath();\n  }\n});\n/**\r\n * Map of path constructors\r\n */\n// TODO Use function to build symbol path.\nvar symbolCtors = {\n  line: graphic.Line,\n  rect: graphic.Rect,\n  roundRect: graphic.Rect,\n  square: graphic.Rect,\n  circle: graphic.Circle,\n  diamond: Diamond,\n  pin: Pin,\n  arrow: Arrow,\n  triangle: Triangle\n};\nvar symbolShapeMakers = {\n  line: function (x, y, w, h, shape) {\n    shape.x1 = x;\n    shape.y1 = y + h / 2;\n    shape.x2 = x + w;\n    shape.y2 = y + h / 2;\n  },\n  rect: function (x, y, w, h, shape) {\n    shape.x = x;\n    shape.y = y;\n    shape.width = w;\n    shape.height = h;\n  },\n  roundRect: function (x, y, w, h, shape) {\n    shape.x = x;\n    shape.y = y;\n    shape.width = w;\n    shape.height = h;\n    shape.r = Math.min(w, h) / 4;\n  },\n  square: function (x, y, w, h, shape) {\n    var size = Math.min(w, h);\n    shape.x = x;\n    shape.y = y;\n    shape.width = size;\n    shape.height = size;\n  },\n  circle: function (x, y, w, h, shape) {\n    // Put circle in the center of square\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.r = Math.min(w, h) / 2;\n  },\n  diamond: function (x, y, w, h, shape) {\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  pin: function (x, y, w, h, shape) {\n    shape.x = x + w / 2;\n    shape.y = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  arrow: function (x, y, w, h, shape) {\n    shape.x = x + w / 2;\n    shape.y = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  },\n  triangle: function (x, y, w, h, shape) {\n    shape.cx = x + w / 2;\n    shape.cy = y + h / 2;\n    shape.width = w;\n    shape.height = h;\n  }\n};\nexport var symbolBuildProxies = {};\neach(symbolCtors, function (Ctor, name) {\n  symbolBuildProxies[name] = new Ctor();\n});\nvar SymbolClz = graphic.Path.extend({\n  type: 'symbol',\n  shape: {\n    symbolType: '',\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0\n  },\n  calculateTextPosition: function (out, config, rect) {\n    var res = calculateTextPosition(out, config, rect);\n    var shape = this.shape;\n    if (shape && shape.symbolType === 'pin' && config.position === 'inside') {\n      res.y = rect.y + rect.height * 0.4;\n    }\n    return res;\n  },\n  buildPath: function (ctx, shape, inBundle) {\n    var symbolType = shape.symbolType;\n    if (symbolType !== 'none') {\n      var proxySymbol = symbolBuildProxies[symbolType];\n      if (!proxySymbol) {\n        // Default rect\n        symbolType = 'rect';\n        proxySymbol = symbolBuildProxies[symbolType];\n      }\n      symbolShapeMakers[symbolType](shape.x, shape.y, shape.width, shape.height, proxySymbol.shape);\n      proxySymbol.buildPath(ctx, proxySymbol.shape, inBundle);\n    }\n  }\n});\n// Provide setColor helper method to avoid determine if set the fill or stroke outside\nfunction symbolPathSetColor(color, innerColor) {\n  if (this.type !== 'image') {\n    var symbolStyle = this.style;\n    if (this.__isEmptyBrush) {\n      symbolStyle.stroke = color;\n      symbolStyle.fill = innerColor || '#fff';\n      // TODO Same width with lineStyle in LineView\n      symbolStyle.lineWidth = 2;\n    } else if (this.shape.symbolType === 'line') {\n      symbolStyle.stroke = color;\n    } else {\n      symbolStyle.fill = color;\n    }\n    this.markRedraw();\n  }\n}\n/**\r\n * Create a symbol element with given symbol configuration: shape, x, y, width, height, color\r\n */\nexport function createSymbol(symbolType, x, y, w, h, color,\n// whether to keep the ratio of w/h,\nkeepAspect) {\n  // TODO Support image object, DynamicImage.\n  var isEmpty = symbolType.indexOf('empty') === 0;\n  if (isEmpty) {\n    symbolType = symbolType.substr(5, 1).toLowerCase() + symbolType.substr(6);\n  }\n  var symbolPath;\n  if (symbolType.indexOf('image://') === 0) {\n    symbolPath = graphic.makeImage(symbolType.slice(8), new BoundingRect(x, y, w, h), keepAspect ? 'center' : 'cover');\n  } else if (symbolType.indexOf('path://') === 0) {\n    symbolPath = graphic.makePath(symbolType.slice(7), {}, new BoundingRect(x, y, w, h), keepAspect ? 'center' : 'cover');\n  } else {\n    symbolPath = new SymbolClz({\n      shape: {\n        symbolType: symbolType,\n        x: x,\n        y: y,\n        width: w,\n        height: h\n      }\n    });\n  }\n  symbolPath.__isEmptyBrush = isEmpty;\n  // TODO Should deprecate setColor\n  symbolPath.setColor = symbolPathSetColor;\n  if (color) {\n    symbolPath.setColor(color);\n  }\n  return symbolPath;\n}\nexport function normalizeSymbolSize(symbolSize) {\n  if (!isArray(symbolSize)) {\n    symbolSize = [+symbolSize, +symbolSize];\n  }\n  return [symbolSize[0] || 0, symbolSize[1] || 0];\n}\nexport function normalizeSymbolOffset(symbolOffset, symbolSize) {\n  if (symbolOffset == null) {\n    return;\n  }\n  if (!isArray(symbolOffset)) {\n    symbolOffset = [symbolOffset, symbolOffset];\n  }\n  return [parsePercent(symbolOffset[0], symbolSize[0]) || 0, parsePercent(retrieve2(symbolOffset[1], symbolOffset[0]), symbolSize[1]) || 0];\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,OAAO,EAAEC,SAAS,QAAQ,0BAA0B;AACnE,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,SAASC,qBAAqB,QAAQ,6BAA6B;AACnE,SAASC,YAAY,QAAQ,aAAa;AAC1C;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAGJ,OAAO,CAACK,IAAI,CAACC,MAAM,CAAC;EACjCC,IAAI,EAAE,UAAU;EAChBC,KAAK,EAAE;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAUC,IAAI,EAAEN,KAAK,EAAE;IAChC,IAAIC,EAAE,GAAGD,KAAK,CAACC,EAAE;IACjB,IAAIC,EAAE,GAAGF,KAAK,CAACE,EAAE;IACjB,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK,GAAG,CAAC;IAC3B,IAAIC,MAAM,GAAGJ,KAAK,CAACI,MAAM,GAAG,CAAC;IAC7BE,IAAI,CAACC,MAAM,CAACN,EAAE,EAAEC,EAAE,GAAGE,MAAM,CAAC;IAC5BE,IAAI,CAACE,MAAM,CAACP,EAAE,GAAGE,KAAK,EAAED,EAAE,GAAGE,MAAM,CAAC;IACpCE,IAAI,CAACE,MAAM,CAACP,EAAE,GAAGE,KAAK,EAAED,EAAE,GAAGE,MAAM,CAAC;IACpCE,IAAI,CAACG,SAAS,CAAC,CAAC;EAClB;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGlB,OAAO,CAACK,IAAI,CAACC,MAAM,CAAC;EAChCC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE;IACLC,EAAE,EAAE,CAAC;IACLC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAUC,IAAI,EAAEN,KAAK,EAAE;IAChC,IAAIC,EAAE,GAAGD,KAAK,CAACC,EAAE;IACjB,IAAIC,EAAE,GAAGF,KAAK,CAACE,EAAE;IACjB,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK,GAAG,CAAC;IAC3B,IAAIC,MAAM,GAAGJ,KAAK,CAACI,MAAM,GAAG,CAAC;IAC7BE,IAAI,CAACC,MAAM,CAACN,EAAE,EAAEC,EAAE,GAAGE,MAAM,CAAC;IAC5BE,IAAI,CAACE,MAAM,CAACP,EAAE,GAAGE,KAAK,EAAED,EAAE,CAAC;IAC3BI,IAAI,CAACE,MAAM,CAACP,EAAE,EAAEC,EAAE,GAAGE,MAAM,CAAC;IAC5BE,IAAI,CAACE,MAAM,CAACP,EAAE,GAAGE,KAAK,EAAED,EAAE,CAAC;IAC3BI,IAAI,CAACG,SAAS,CAAC,CAAC;EAClB;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,IAAIE,GAAG,GAAGnB,OAAO,CAACK,IAAI,CAACC,MAAM,CAAC;EAC5BC,IAAI,EAAE,KAAK;EACXC,KAAK,EAAE;IACL;IACAY,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJV,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAUC,IAAI,EAAEN,KAAK,EAAE;IAChC,IAAIY,CAAC,GAAGZ,KAAK,CAACY,CAAC;IACf,IAAIC,CAAC,GAAGb,KAAK,CAACa,CAAC;IACf,IAAIC,CAAC,GAAGd,KAAK,CAACG,KAAK,GAAG,CAAC,GAAG,CAAC;IAC3B;IACA,IAAIY,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACH,CAAC,EAAEd,KAAK,CAACI,MAAM,CAAC;IACjC,IAAIc,CAAC,GAAGJ,CAAC,GAAG,CAAC;IACb;IACA,IAAIK,EAAE,GAAGD,CAAC,GAAGA,CAAC,IAAIH,CAAC,GAAGG,CAAC,CAAC;IACxB,IAAIhB,EAAE,GAAGW,CAAC,GAAGE,CAAC,GAAGG,CAAC,GAAGC,EAAE;IACvB,IAAIC,KAAK,GAAGJ,IAAI,CAACK,IAAI,CAACF,EAAE,GAAGD,CAAC,CAAC;IAC7B;IACA,IAAII,EAAE,GAAGN,IAAI,CAACO,GAAG,CAACH,KAAK,CAAC,GAAGF,CAAC;IAC5B,IAAIM,IAAI,GAAGR,IAAI,CAACS,GAAG,CAACL,KAAK,CAAC;IAC1B,IAAIM,IAAI,GAAGV,IAAI,CAACO,GAAG,CAACH,KAAK,CAAC;IAC1B,IAAIO,KAAK,GAAGT,CAAC,GAAG,GAAG;IACnB,IAAIU,MAAM,GAAGV,CAAC,GAAG,GAAG;IACpBZ,IAAI,CAACC,MAAM,CAACK,CAAC,GAAGU,EAAE,EAAEpB,EAAE,GAAGiB,EAAE,CAAC;IAC5Bb,IAAI,CAACuB,GAAG,CAACjB,CAAC,EAAEV,EAAE,EAAEgB,CAAC,EAAEF,IAAI,CAACc,EAAE,GAAGV,KAAK,EAAEJ,IAAI,CAACc,EAAE,GAAG,CAAC,GAAGV,KAAK,CAAC;IACxDd,IAAI,CAACyB,aAAa,CAACnB,CAAC,GAAGU,EAAE,GAAGE,IAAI,GAAGG,KAAK,EAAEzB,EAAE,GAAGiB,EAAE,GAAGO,IAAI,GAAGC,KAAK,EAAEf,CAAC,EAAEC,CAAC,GAAGe,MAAM,EAAEhB,CAAC,EAAEC,CAAC,CAAC;IACtFP,IAAI,CAACyB,aAAa,CAACnB,CAAC,EAAEC,CAAC,GAAGe,MAAM,EAAEhB,CAAC,GAAGU,EAAE,GAAGE,IAAI,GAAGG,KAAK,EAAEzB,EAAE,GAAGiB,EAAE,GAAGO,IAAI,GAAGC,KAAK,EAAEf,CAAC,GAAGU,EAAE,EAAEpB,EAAE,GAAGiB,EAAE,CAAC;IACjGb,IAAI,CAACG,SAAS,CAAC,CAAC;EAClB;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,IAAIuB,KAAK,GAAGxC,OAAO,CAACK,IAAI,CAACC,MAAM,CAAC;EAC9BC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE;IACLY,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJV,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAU4B,GAAG,EAAEjC,KAAK,EAAE;IAC/B,IAAII,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACzB,IAAID,KAAK,GAAGH,KAAK,CAACG,KAAK;IACvB,IAAIS,CAAC,GAAGZ,KAAK,CAACY,CAAC;IACf,IAAIC,CAAC,GAAGb,KAAK,CAACa,CAAC;IACf,IAAIS,EAAE,GAAGnB,KAAK,GAAG,CAAC,GAAG,CAAC;IACtB8B,GAAG,CAAC1B,MAAM,CAACK,CAAC,EAAEC,CAAC,CAAC;IAChBoB,GAAG,CAACzB,MAAM,CAACI,CAAC,GAAGU,EAAE,EAAET,CAAC,GAAGT,MAAM,CAAC;IAC9B6B,GAAG,CAACzB,MAAM,CAACI,CAAC,EAAEC,CAAC,GAAGT,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IACjC6B,GAAG,CAACzB,MAAM,CAACI,CAAC,GAAGU,EAAE,EAAET,CAAC,GAAGT,MAAM,CAAC;IAC9B6B,GAAG,CAACzB,MAAM,CAACI,CAAC,EAAEC,CAAC,CAAC;IAChBoB,GAAG,CAACxB,SAAS,CAAC,CAAC;EACjB;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,IAAIyB,WAAW,GAAG;EAChBC,IAAI,EAAE3C,OAAO,CAAC4C,IAAI;EAClBC,IAAI,EAAE7C,OAAO,CAAC8C,IAAI;EAClBC,SAAS,EAAE/C,OAAO,CAAC8C,IAAI;EACvBE,MAAM,EAAEhD,OAAO,CAAC8C,IAAI;EACpBG,MAAM,EAAEjD,OAAO,CAACkD,MAAM;EACtBC,OAAO,EAAEjC,OAAO;EAChBkC,GAAG,EAAEjC,GAAG;EACRkC,KAAK,EAAEb,KAAK;EACZc,QAAQ,EAAElD;AACZ,CAAC;AACD,IAAImD,iBAAiB,GAAG;EACtBZ,IAAI,EAAE,SAAAA,CAAUvB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEf,KAAK,EAAE;IACjCA,KAAK,CAACgD,EAAE,GAAGpC,CAAC;IACZZ,KAAK,CAACiD,EAAE,GAAGpC,CAAC,GAAGE,CAAC,GAAG,CAAC;IACpBf,KAAK,CAACkD,EAAE,GAAGtC,CAAC,GAAGE,CAAC;IAChBd,KAAK,CAACmD,EAAE,GAAGtC,CAAC,GAAGE,CAAC,GAAG,CAAC;EACtB,CAAC;EACDsB,IAAI,EAAE,SAAAA,CAAUzB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEf,KAAK,EAAE;IACjCA,KAAK,CAACY,CAAC,GAAGA,CAAC;IACXZ,KAAK,CAACa,CAAC,GAAGA,CAAC;IACXb,KAAK,CAACG,KAAK,GAAGW,CAAC;IACfd,KAAK,CAACI,MAAM,GAAGW,CAAC;EAClB,CAAC;EACDwB,SAAS,EAAE,SAAAA,CAAU3B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEf,KAAK,EAAE;IACtCA,KAAK,CAACY,CAAC,GAAGA,CAAC;IACXZ,KAAK,CAACa,CAAC,GAAGA,CAAC;IACXb,KAAK,CAACG,KAAK,GAAGW,CAAC;IACfd,KAAK,CAACI,MAAM,GAAGW,CAAC;IAChBf,KAAK,CAACkB,CAAC,GAAGF,IAAI,CAACoC,GAAG,CAACtC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC;EAC9B,CAAC;EACDyB,MAAM,EAAE,SAAAA,CAAU5B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEf,KAAK,EAAE;IACnC,IAAIqD,IAAI,GAAGrC,IAAI,CAACoC,GAAG,CAACtC,CAAC,EAAEC,CAAC,CAAC;IACzBf,KAAK,CAACY,CAAC,GAAGA,CAAC;IACXZ,KAAK,CAACa,CAAC,GAAGA,CAAC;IACXb,KAAK,CAACG,KAAK,GAAGkD,IAAI;IAClBrD,KAAK,CAACI,MAAM,GAAGiD,IAAI;EACrB,CAAC;EACDZ,MAAM,EAAE,SAAAA,CAAU7B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEf,KAAK,EAAE;IACnC;IACAA,KAAK,CAACC,EAAE,GAAGW,CAAC,GAAGE,CAAC,GAAG,CAAC;IACpBd,KAAK,CAACE,EAAE,GAAGW,CAAC,GAAGE,CAAC,GAAG,CAAC;IACpBf,KAAK,CAACkB,CAAC,GAAGF,IAAI,CAACoC,GAAG,CAACtC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC;EAC9B,CAAC;EACD4B,OAAO,EAAE,SAAAA,CAAU/B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEf,KAAK,EAAE;IACpCA,KAAK,CAACC,EAAE,GAAGW,CAAC,GAAGE,CAAC,GAAG,CAAC;IACpBd,KAAK,CAACE,EAAE,GAAGW,CAAC,GAAGE,CAAC,GAAG,CAAC;IACpBf,KAAK,CAACG,KAAK,GAAGW,CAAC;IACfd,KAAK,CAACI,MAAM,GAAGW,CAAC;EAClB,CAAC;EACD6B,GAAG,EAAE,SAAAA,CAAUhC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEf,KAAK,EAAE;IAChCA,KAAK,CAACY,CAAC,GAAGA,CAAC,GAAGE,CAAC,GAAG,CAAC;IACnBd,KAAK,CAACa,CAAC,GAAGA,CAAC,GAAGE,CAAC,GAAG,CAAC;IACnBf,KAAK,CAACG,KAAK,GAAGW,CAAC;IACfd,KAAK,CAACI,MAAM,GAAGW,CAAC;EAClB,CAAC;EACD8B,KAAK,EAAE,SAAAA,CAAUjC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEf,KAAK,EAAE;IAClCA,KAAK,CAACY,CAAC,GAAGA,CAAC,GAAGE,CAAC,GAAG,CAAC;IACnBd,KAAK,CAACa,CAAC,GAAGA,CAAC,GAAGE,CAAC,GAAG,CAAC;IACnBf,KAAK,CAACG,KAAK,GAAGW,CAAC;IACfd,KAAK,CAACI,MAAM,GAAGW,CAAC;EAClB,CAAC;EACD+B,QAAQ,EAAE,SAAAA,CAAUlC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEf,KAAK,EAAE;IACrCA,KAAK,CAACC,EAAE,GAAGW,CAAC,GAAGE,CAAC,GAAG,CAAC;IACpBd,KAAK,CAACE,EAAE,GAAGW,CAAC,GAAGE,CAAC,GAAG,CAAC;IACpBf,KAAK,CAACG,KAAK,GAAGW,CAAC;IACfd,KAAK,CAACI,MAAM,GAAGW,CAAC;EAClB;AACF,CAAC;AACD,OAAO,IAAIuC,kBAAkB,GAAG,CAAC,CAAC;AAClCjE,IAAI,CAAC6C,WAAW,EAAE,UAAUqB,IAAI,EAAEC,IAAI,EAAE;EACtCF,kBAAkB,CAACE,IAAI,CAAC,GAAG,IAAID,IAAI,CAAC,CAAC;AACvC,CAAC,CAAC;AACF,IAAIE,SAAS,GAAGjE,OAAO,CAACK,IAAI,CAACC,MAAM,CAAC;EAClCC,IAAI,EAAE,QAAQ;EACdC,KAAK,EAAE;IACL0D,UAAU,EAAE,EAAE;IACd9C,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJV,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC;EACDV,qBAAqB,EAAE,SAAAA,CAAUiE,GAAG,EAAEC,MAAM,EAAEvB,IAAI,EAAE;IAClD,IAAIwB,GAAG,GAAGnE,qBAAqB,CAACiE,GAAG,EAAEC,MAAM,EAAEvB,IAAI,CAAC;IAClD,IAAIrC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIA,KAAK,IAAIA,KAAK,CAAC0D,UAAU,KAAK,KAAK,IAAIE,MAAM,CAACE,QAAQ,KAAK,QAAQ,EAAE;MACvED,GAAG,CAAChD,CAAC,GAAGwB,IAAI,CAACxB,CAAC,GAAGwB,IAAI,CAACjC,MAAM,GAAG,GAAG;IACpC;IACA,OAAOyD,GAAG;EACZ,CAAC;EACDxD,SAAS,EAAE,SAAAA,CAAU4B,GAAG,EAAEjC,KAAK,EAAE+D,QAAQ,EAAE;IACzC,IAAIL,UAAU,GAAG1D,KAAK,CAAC0D,UAAU;IACjC,IAAIA,UAAU,KAAK,MAAM,EAAE;MACzB,IAAIM,WAAW,GAAGV,kBAAkB,CAACI,UAAU,CAAC;MAChD,IAAI,CAACM,WAAW,EAAE;QAChB;QACAN,UAAU,GAAG,MAAM;QACnBM,WAAW,GAAGV,kBAAkB,CAACI,UAAU,CAAC;MAC9C;MACAX,iBAAiB,CAACW,UAAU,CAAC,CAAC1D,KAAK,CAACY,CAAC,EAAEZ,KAAK,CAACa,CAAC,EAAEb,KAAK,CAACG,KAAK,EAAEH,KAAK,CAACI,MAAM,EAAE4D,WAAW,CAAChE,KAAK,CAAC;MAC7FgE,WAAW,CAAC3D,SAAS,CAAC4B,GAAG,EAAE+B,WAAW,CAAChE,KAAK,EAAE+D,QAAQ,CAAC;IACzD;EACF;AACF,CAAC,CAAC;AACF;AACA,SAASE,kBAAkBA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC7C,IAAI,IAAI,CAACpE,IAAI,KAAK,OAAO,EAAE;IACzB,IAAIqE,WAAW,GAAG,IAAI,CAACC,KAAK;IAC5B,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBF,WAAW,CAACG,MAAM,GAAGL,KAAK;MAC1BE,WAAW,CAACI,IAAI,GAAGL,UAAU,IAAI,MAAM;MACvC;MACAC,WAAW,CAACK,SAAS,GAAG,CAAC;IAC3B,CAAC,MAAM,IAAI,IAAI,CAACzE,KAAK,CAAC0D,UAAU,KAAK,MAAM,EAAE;MAC3CU,WAAW,CAACG,MAAM,GAAGL,KAAK;IAC5B,CAAC,MAAM;MACLE,WAAW,CAACI,IAAI,GAAGN,KAAK;IAC1B;IACA,IAAI,CAACQ,UAAU,CAAC,CAAC;EACnB;AACF;AACA;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAACjB,UAAU,EAAE9C,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEmD,KAAK;AAC1D;AACAU,UAAU,EAAE;EACV;EACA,IAAIC,OAAO,GAAGnB,UAAU,CAACoB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;EAC/C,IAAID,OAAO,EAAE;IACXnB,UAAU,GAAGA,UAAU,CAACqB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGtB,UAAU,CAACqB,MAAM,CAAC,CAAC,CAAC;EAC3E;EACA,IAAIE,UAAU;EACd,IAAIvB,UAAU,CAACoB,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;IACxCG,UAAU,GAAGzF,OAAO,CAAC0F,SAAS,CAACxB,UAAU,CAACyB,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI1F,YAAY,CAACmB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE6D,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;EACpH,CAAC,MAAM,IAAIlB,UAAU,CAACoB,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;IAC9CG,UAAU,GAAGzF,OAAO,CAAC4F,QAAQ,CAAC1B,UAAU,CAACyB,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI1F,YAAY,CAACmB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,EAAE6D,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;EACvH,CAAC,MAAM;IACLK,UAAU,GAAG,IAAIxB,SAAS,CAAC;MACzBzD,KAAK,EAAE;QACL0D,UAAU,EAAEA,UAAU;QACtB9C,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAEA,CAAC;QACJV,KAAK,EAAEW,CAAC;QACRV,MAAM,EAAEW;MACV;IACF,CAAC,CAAC;EACJ;EACAkE,UAAU,CAACX,cAAc,GAAGO,OAAO;EACnC;EACAI,UAAU,CAACI,QAAQ,GAAGpB,kBAAkB;EACxC,IAAIC,KAAK,EAAE;IACTe,UAAU,CAACI,QAAQ,CAACnB,KAAK,CAAC;EAC5B;EACA,OAAOe,UAAU;AACnB;AACA,OAAO,SAASK,mBAAmBA,CAACC,UAAU,EAAE;EAC9C,IAAI,CAACjG,OAAO,CAACiG,UAAU,CAAC,EAAE;IACxBA,UAAU,GAAG,CAAC,CAACA,UAAU,EAAE,CAACA,UAAU,CAAC;EACzC;EACA,OAAO,CAACA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACjD;AACA,OAAO,SAASC,qBAAqBA,CAACC,YAAY,EAAEF,UAAU,EAAE;EAC9D,IAAIE,YAAY,IAAI,IAAI,EAAE;IACxB;EACF;EACA,IAAI,CAACnG,OAAO,CAACmG,YAAY,CAAC,EAAE;IAC1BA,YAAY,GAAG,CAACA,YAAY,EAAEA,YAAY,CAAC;EAC7C;EACA,OAAO,CAAC9F,YAAY,CAAC8F,YAAY,CAAC,CAAC,CAAC,EAAEF,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE5F,YAAY,CAACJ,SAAS,CAACkG,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEF,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AAC3I", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}