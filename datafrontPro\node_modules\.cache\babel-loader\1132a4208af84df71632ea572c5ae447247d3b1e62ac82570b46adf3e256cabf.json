{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport AxisView from './AxisView.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport { getECData } from '../../util/innerStore.js';\nvar elementList = ['axisLine', 'axisLabel', 'axisTick', 'minorTick', 'splitLine', 'minorSplitLine', 'splitArea'];\nfunction getAxisLineShape(polar, rExtent, angle) {\n  rExtent[1] > rExtent[0] && (rExtent = rExtent.slice().reverse());\n  var start = polar.coordToPoint([rExtent[0], angle]);\n  var end = polar.coordToPoint([rExtent[1], angle]);\n  return {\n    x1: start[0],\n    y1: start[1],\n    x2: end[0],\n    y2: end[1]\n  };\n}\nfunction getRadiusIdx(polar) {\n  var radiusAxis = polar.getRadiusAxis();\n  return radiusAxis.inverse ? 0 : 1;\n}\n// Remove the last tick which will overlap the first tick\nfunction fixAngleOverlap(list) {\n  var firstItem = list[0];\n  var lastItem = list[list.length - 1];\n  if (firstItem && lastItem && Math.abs(Math.abs(firstItem.coord - lastItem.coord) - 360) < 1e-4) {\n    list.pop();\n  }\n}\nvar AngleAxisView = /** @class */function (_super) {\n  __extends(AngleAxisView, _super);\n  function AngleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AngleAxisView.type;\n    _this.axisPointerClass = 'PolarAxisPointer';\n    return _this;\n  }\n  AngleAxisView.prototype.render = function (angleAxisModel, ecModel) {\n    this.group.removeAll();\n    if (!angleAxisModel.get('show')) {\n      return;\n    }\n    var angleAxis = angleAxisModel.axis;\n    var polar = angleAxis.polar;\n    var radiusExtent = polar.getRadiusAxis().getExtent();\n    var ticksAngles = angleAxis.getTicksCoords();\n    var minorTickAngles = angleAxis.getMinorTicksCoords();\n    var labels = zrUtil.map(angleAxis.getViewLabels(), function (labelItem) {\n      labelItem = zrUtil.clone(labelItem);\n      var scale = angleAxis.scale;\n      var tickValue = scale.type === 'ordinal' ? scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n      labelItem.coord = angleAxis.dataToCoord(tickValue);\n      return labelItem;\n    });\n    fixAngleOverlap(labels);\n    fixAngleOverlap(ticksAngles);\n    zrUtil.each(elementList, function (name) {\n      if (angleAxisModel.get([name, 'show']) && (!angleAxis.scale.isBlank() || name === 'axisLine')) {\n        angelAxisElementsBuilders[name](this.group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels);\n      }\n    }, this);\n  };\n  AngleAxisView.type = 'angleAxis';\n  return AngleAxisView;\n}(AxisView);\nvar angelAxisElementsBuilders = {\n  axisLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var lineStyleModel = angleAxisModel.getModel(['axisLine', 'lineStyle']);\n    var angleAxis = polar.getAngleAxis();\n    var RADIAN = Math.PI / 180;\n    var angleExtent = angleAxis.getExtent();\n    // extent id of the axis radius (r0 and r)\n    var rId = getRadiusIdx(polar);\n    var r0Id = rId ? 0 : 1;\n    var shape;\n    var shapeType = Math.abs(angleExtent[1] - angleExtent[0]) === 360 ? 'Circle' : 'Arc';\n    if (radiusExtent[r0Id] === 0) {\n      shape = new graphic[shapeType]({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          startAngle: -angleExtent[0] * RADIAN,\n          endAngle: -angleExtent[1] * RADIAN,\n          clockwise: angleAxis.inverse\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    } else {\n      shape = new graphic.Ring({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          r0: radiusExtent[r0Id]\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    }\n    shape.style.fill = null;\n    group.add(shape);\n  },\n  axisTick: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * tickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = zrUtil.map(ticksAngles, function (tickAngleItem) {\n      return new graphic.Line({\n        shape: getAxisLineShape(polar, [radius, radius + tickLen], tickAngleItem.coord)\n      });\n    });\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(tickModel.getModel('lineStyle').getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      })\n    }));\n  },\n  minorTick: function (group, angleAxisModel, polar, tickAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var minorTickModel = angleAxisModel.getModel('minorTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * minorTickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, [radius, radius + tickLen], minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(minorTickModel.getModel('lineStyle').getLineStyle(), zrUtil.defaults(tickModel.getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      }))\n    }));\n  },\n  axisLabel: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels) {\n    var rawCategoryData = angleAxisModel.getCategories(true);\n    var commonLabelModel = angleAxisModel.getModel('axisLabel');\n    var labelMargin = commonLabelModel.get('margin');\n    var triggerEvent = angleAxisModel.get('triggerEvent');\n    // Use length of ticksAngles because it may remove the last tick to avoid overlapping\n    zrUtil.each(labels, function (labelItem, idx) {\n      var labelModel = commonLabelModel;\n      var tickValue = labelItem.tickValue;\n      var r = radiusExtent[getRadiusIdx(polar)];\n      var p = polar.coordToPoint([r + labelMargin, labelItem.coord]);\n      var cx = polar.cx;\n      var cy = polar.cy;\n      var labelTextAlign = Math.abs(p[0] - cx) / r < 0.3 ? 'center' : p[0] > cx ? 'left' : 'right';\n      var labelTextVerticalAlign = Math.abs(p[1] - cy) / r < 0.3 ? 'middle' : p[1] > cy ? 'top' : 'bottom';\n      if (rawCategoryData && rawCategoryData[tickValue]) {\n        var rawCategoryItem = rawCategoryData[tickValue];\n        if (zrUtil.isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n          labelModel = new Model(rawCategoryItem.textStyle, commonLabelModel, commonLabelModel.ecModel);\n        }\n      }\n      var textEl = new graphic.Text({\n        silent: AxisBuilder.isLabelSilent(angleAxisModel),\n        style: createTextStyle(labelModel, {\n          x: p[0],\n          y: p[1],\n          fill: labelModel.getTextColor() || angleAxisModel.get(['axisLine', 'lineStyle', 'color']),\n          text: labelItem.formattedLabel,\n          align: labelTextAlign,\n          verticalAlign: labelTextVerticalAlign\n        })\n      });\n      group.add(textEl);\n      // Pack data for mouse event\n      if (triggerEvent) {\n        var eventData = AxisBuilder.makeAxisEventDataBase(angleAxisModel);\n        eventData.targetType = 'axisLabel';\n        eventData.value = labelItem.rawLabel;\n        getECData(textEl).eventData = eventData;\n      }\n    }, this);\n  },\n  splitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var splitLineModel = angleAxisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var lineCount = 0;\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var splitLines = [];\n    for (var i = 0; i < ticksAngles.length; i++) {\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(new graphic.Line({\n        shape: getAxisLineShape(polar, radiusExtent, ticksAngles[i].coord)\n      }));\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitLines.length; i++) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyleModel.getLineStyle()),\n        silent: true,\n        z: angleAxisModel.get('z')\n      }));\n    }\n  },\n  minorSplitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var minorSplitLineModel = angleAxisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, radiusExtent, minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: lineStyleModel.getLineStyle(),\n      silent: true,\n      z: angleAxisModel.get('z')\n    }));\n  },\n  splitArea: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!ticksAngles.length) {\n      return;\n    }\n    var splitAreaModel = angleAxisModel.getModel('splitArea');\n    var areaStyleModel = splitAreaModel.getModel('areaStyle');\n    var areaColors = areaStyleModel.get('color');\n    var lineCount = 0;\n    areaColors = areaColors instanceof Array ? areaColors : [areaColors];\n    var splitAreas = [];\n    var RADIAN = Math.PI / 180;\n    var prevAngle = -ticksAngles[0].coord * RADIAN;\n    var r0 = Math.min(radiusExtent[0], radiusExtent[1]);\n    var r1 = Math.max(radiusExtent[0], radiusExtent[1]);\n    var clockwise = angleAxisModel.get('clockwise');\n    for (var i = 1, len = ticksAngles.length; i <= len; i++) {\n      var coord = i === len ? ticksAngles[0].coord : ticksAngles[i].coord;\n      var colorIndex = lineCount++ % areaColors.length;\n      splitAreas[colorIndex] = splitAreas[colorIndex] || [];\n      splitAreas[colorIndex].push(new graphic.Sector({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r0: r0,\n          r: r1,\n          startAngle: prevAngle,\n          endAngle: -coord * RADIAN,\n          clockwise: clockwise\n        },\n        silent: true\n      }));\n      prevAngle = -coord * RADIAN;\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitAreas.length; i++) {\n      group.add(graphic.mergePath(splitAreas[i], {\n        style: zrUtil.defaults({\n          fill: areaColors[i % areaColors.length]\n        }, areaStyleModel.getAreaStyle()),\n        silent: true\n      }));\n    }\n  }\n};\nexport default AngleAxisView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "graphic", "createTextStyle", "Model", "AxisView", "AxisBuilder", "getECData", "elementList", "getAxisLineShape", "polar", "rExtent", "angle", "slice", "reverse", "start", "coordToPoint", "end", "x1", "y1", "x2", "y2", "getRadiusIdx", "radiusAxis", "getRadiusAxis", "inverse", "fixAngleOverlap", "list", "firstItem", "lastItem", "length", "Math", "abs", "coord", "pop", "AngleAxisView", "_super", "_this", "apply", "arguments", "type", "axisPointerClass", "prototype", "render", "angleAxisModel", "ecModel", "group", "removeAll", "get", "angleAxis", "axis", "radiusExtent", "getExtent", "ticksAngles", "getTicksCoords", "minorTickAngles", "getMinorTicksCoords", "labels", "map", "getViewLabels", "labelItem", "clone", "scale", "tickValue", "getRawOrdinalNumber", "dataToCoord", "each", "name", "isBlank", "angelAxisElementsBuilders", "axisLine", "lineStyleModel", "getModel", "getAngleAxis", "RADIAN", "PI", "angleExtent", "rId", "r0Id", "shape", "shapeType", "cx", "cy", "r", "startAngle", "endAngle", "clockwise", "style", "getLineStyle", "z2", "silent", "Ring", "r0", "fill", "add", "axisTick", "tickModel", "tickLen", "radius", "lines", "tickAngleItem", "Line", "mergePath", "defaults", "stroke", "minor<PERSON><PERSON>", "tickAngles", "minorTickModel", "i", "k", "push", "axisLabel", "rawCategoryData", "getCategories", "commonLabelModel", "labelMargin", "triggerEvent", "idx", "labelModel", "p", "labelTextAlign", "labelTextVerticalAlign", "rawCategoryItem", "isObject", "textStyle", "textEl", "Text", "isLabelSilent", "x", "y", "getTextColor", "text", "formattedLabel", "align", "verticalAlign", "eventData", "makeAxisEventDataBase", "targetType", "value", "rawLabel", "splitLine", "splitLineModel", "lineColors", "lineCount", "Array", "splitLines", "colorIndex", "z", "minorSplitLine", "minorSplitLineModel", "splitArea", "splitAreaModel", "areaStyleModel", "areaColors", "splitAreas", "prevAngle", "min", "r1", "max", "len", "Sector", "getAreaStyle"], "sources": ["E:/AllProject/datafront/node_modules/echarts/lib/component/axis/AngleAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport AxisView from './AxisView.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport { getECData } from '../../util/innerStore.js';\nvar elementList = ['axisLine', 'axisLabel', 'axisTick', 'minorTick', 'splitLine', 'minorSplitLine', 'splitArea'];\nfunction getAxisLineShape(polar, rExtent, angle) {\n  rExtent[1] > rExtent[0] && (rExtent = rExtent.slice().reverse());\n  var start = polar.coordToPoint([rExtent[0], angle]);\n  var end = polar.coordToPoint([rExtent[1], angle]);\n  return {\n    x1: start[0],\n    y1: start[1],\n    x2: end[0],\n    y2: end[1]\n  };\n}\nfunction getRadiusIdx(polar) {\n  var radiusAxis = polar.getRadiusAxis();\n  return radiusAxis.inverse ? 0 : 1;\n}\n// Remove the last tick which will overlap the first tick\nfunction fixAngleOverlap(list) {\n  var firstItem = list[0];\n  var lastItem = list[list.length - 1];\n  if (firstItem && lastItem && Math.abs(Math.abs(firstItem.coord - lastItem.coord) - 360) < 1e-4) {\n    list.pop();\n  }\n}\nvar AngleAxisView = /** @class */function (_super) {\n  __extends(AngleAxisView, _super);\n  function AngleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AngleAxisView.type;\n    _this.axisPointerClass = 'PolarAxisPointer';\n    return _this;\n  }\n  AngleAxisView.prototype.render = function (angleAxisModel, ecModel) {\n    this.group.removeAll();\n    if (!angleAxisModel.get('show')) {\n      return;\n    }\n    var angleAxis = angleAxisModel.axis;\n    var polar = angleAxis.polar;\n    var radiusExtent = polar.getRadiusAxis().getExtent();\n    var ticksAngles = angleAxis.getTicksCoords();\n    var minorTickAngles = angleAxis.getMinorTicksCoords();\n    var labels = zrUtil.map(angleAxis.getViewLabels(), function (labelItem) {\n      labelItem = zrUtil.clone(labelItem);\n      var scale = angleAxis.scale;\n      var tickValue = scale.type === 'ordinal' ? scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n      labelItem.coord = angleAxis.dataToCoord(tickValue);\n      return labelItem;\n    });\n    fixAngleOverlap(labels);\n    fixAngleOverlap(ticksAngles);\n    zrUtil.each(elementList, function (name) {\n      if (angleAxisModel.get([name, 'show']) && (!angleAxis.scale.isBlank() || name === 'axisLine')) {\n        angelAxisElementsBuilders[name](this.group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels);\n      }\n    }, this);\n  };\n  AngleAxisView.type = 'angleAxis';\n  return AngleAxisView;\n}(AxisView);\nvar angelAxisElementsBuilders = {\n  axisLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var lineStyleModel = angleAxisModel.getModel(['axisLine', 'lineStyle']);\n    var angleAxis = polar.getAngleAxis();\n    var RADIAN = Math.PI / 180;\n    var angleExtent = angleAxis.getExtent();\n    // extent id of the axis radius (r0 and r)\n    var rId = getRadiusIdx(polar);\n    var r0Id = rId ? 0 : 1;\n    var shape;\n    var shapeType = Math.abs(angleExtent[1] - angleExtent[0]) === 360 ? 'Circle' : 'Arc';\n    if (radiusExtent[r0Id] === 0) {\n      shape = new graphic[shapeType]({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          startAngle: -angleExtent[0] * RADIAN,\n          endAngle: -angleExtent[1] * RADIAN,\n          clockwise: angleAxis.inverse\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    } else {\n      shape = new graphic.Ring({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          r0: radiusExtent[r0Id]\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    }\n    shape.style.fill = null;\n    group.add(shape);\n  },\n  axisTick: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * tickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = zrUtil.map(ticksAngles, function (tickAngleItem) {\n      return new graphic.Line({\n        shape: getAxisLineShape(polar, [radius, radius + tickLen], tickAngleItem.coord)\n      });\n    });\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(tickModel.getModel('lineStyle').getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      })\n    }));\n  },\n  minorTick: function (group, angleAxisModel, polar, tickAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var minorTickModel = angleAxisModel.getModel('minorTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * minorTickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, [radius, radius + tickLen], minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(minorTickModel.getModel('lineStyle').getLineStyle(), zrUtil.defaults(tickModel.getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      }))\n    }));\n  },\n  axisLabel: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels) {\n    var rawCategoryData = angleAxisModel.getCategories(true);\n    var commonLabelModel = angleAxisModel.getModel('axisLabel');\n    var labelMargin = commonLabelModel.get('margin');\n    var triggerEvent = angleAxisModel.get('triggerEvent');\n    // Use length of ticksAngles because it may remove the last tick to avoid overlapping\n    zrUtil.each(labels, function (labelItem, idx) {\n      var labelModel = commonLabelModel;\n      var tickValue = labelItem.tickValue;\n      var r = radiusExtent[getRadiusIdx(polar)];\n      var p = polar.coordToPoint([r + labelMargin, labelItem.coord]);\n      var cx = polar.cx;\n      var cy = polar.cy;\n      var labelTextAlign = Math.abs(p[0] - cx) / r < 0.3 ? 'center' : p[0] > cx ? 'left' : 'right';\n      var labelTextVerticalAlign = Math.abs(p[1] - cy) / r < 0.3 ? 'middle' : p[1] > cy ? 'top' : 'bottom';\n      if (rawCategoryData && rawCategoryData[tickValue]) {\n        var rawCategoryItem = rawCategoryData[tickValue];\n        if (zrUtil.isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n          labelModel = new Model(rawCategoryItem.textStyle, commonLabelModel, commonLabelModel.ecModel);\n        }\n      }\n      var textEl = new graphic.Text({\n        silent: AxisBuilder.isLabelSilent(angleAxisModel),\n        style: createTextStyle(labelModel, {\n          x: p[0],\n          y: p[1],\n          fill: labelModel.getTextColor() || angleAxisModel.get(['axisLine', 'lineStyle', 'color']),\n          text: labelItem.formattedLabel,\n          align: labelTextAlign,\n          verticalAlign: labelTextVerticalAlign\n        })\n      });\n      group.add(textEl);\n      // Pack data for mouse event\n      if (triggerEvent) {\n        var eventData = AxisBuilder.makeAxisEventDataBase(angleAxisModel);\n        eventData.targetType = 'axisLabel';\n        eventData.value = labelItem.rawLabel;\n        getECData(textEl).eventData = eventData;\n      }\n    }, this);\n  },\n  splitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var splitLineModel = angleAxisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var lineCount = 0;\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var splitLines = [];\n    for (var i = 0; i < ticksAngles.length; i++) {\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(new graphic.Line({\n        shape: getAxisLineShape(polar, radiusExtent, ticksAngles[i].coord)\n      }));\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitLines.length; i++) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyleModel.getLineStyle()),\n        silent: true,\n        z: angleAxisModel.get('z')\n      }));\n    }\n  },\n  minorSplitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var minorSplitLineModel = angleAxisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, radiusExtent, minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: lineStyleModel.getLineStyle(),\n      silent: true,\n      z: angleAxisModel.get('z')\n    }));\n  },\n  splitArea: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!ticksAngles.length) {\n      return;\n    }\n    var splitAreaModel = angleAxisModel.getModel('splitArea');\n    var areaStyleModel = splitAreaModel.getModel('areaStyle');\n    var areaColors = areaStyleModel.get('color');\n    var lineCount = 0;\n    areaColors = areaColors instanceof Array ? areaColors : [areaColors];\n    var splitAreas = [];\n    var RADIAN = Math.PI / 180;\n    var prevAngle = -ticksAngles[0].coord * RADIAN;\n    var r0 = Math.min(radiusExtent[0], radiusExtent[1]);\n    var r1 = Math.max(radiusExtent[0], radiusExtent[1]);\n    var clockwise = angleAxisModel.get('clockwise');\n    for (var i = 1, len = ticksAngles.length; i <= len; i++) {\n      var coord = i === len ? ticksAngles[0].coord : ticksAngles[i].coord;\n      var colorIndex = lineCount++ % areaColors.length;\n      splitAreas[colorIndex] = splitAreas[colorIndex] || [];\n      splitAreas[colorIndex].push(new graphic.Sector({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r0: r0,\n          r: r1,\n          startAngle: prevAngle,\n          endAngle: -coord * RADIAN,\n          clockwise: clockwise\n        },\n        silent: true\n      }));\n      prevAngle = -coord * RADIAN;\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitAreas.length; i++) {\n      group.add(graphic.mergePath(splitAreas[i], {\n        style: zrUtil.defaults({\n          fill: areaColors[i % areaColors.length]\n        }, areaStyleModel.getAreaStyle()),\n        silent: true\n      }));\n    }\n  }\n};\nexport default AngleAxisView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,SAASC,SAAS,QAAQ,0BAA0B;AACpD,IAAIC,WAAW,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,WAAW,CAAC;AAChH,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAE;EAC/CD,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,KAAKA,OAAO,GAAGA,OAAO,CAACE,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAChE,IAAIC,KAAK,GAAGL,KAAK,CAACM,YAAY,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC;EACnD,IAAIK,GAAG,GAAGP,KAAK,CAACM,YAAY,CAAC,CAACL,OAAO,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC;EACjD,OAAO;IACLM,EAAE,EAAEH,KAAK,CAAC,CAAC,CAAC;IACZI,EAAE,EAAEJ,KAAK,CAAC,CAAC,CAAC;IACZK,EAAE,EAAEH,GAAG,CAAC,CAAC,CAAC;IACVI,EAAE,EAAEJ,GAAG,CAAC,CAAC;EACX,CAAC;AACH;AACA,SAASK,YAAYA,CAACZ,KAAK,EAAE;EAC3B,IAAIa,UAAU,GAAGb,KAAK,CAACc,aAAa,CAAC,CAAC;EACtC,OAAOD,UAAU,CAACE,OAAO,GAAG,CAAC,GAAG,CAAC;AACnC;AACA;AACA,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC7B,IAAIC,SAAS,GAAGD,IAAI,CAAC,CAAC,CAAC;EACvB,IAAIE,QAAQ,GAAGF,IAAI,CAACA,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;EACpC,IAAIF,SAAS,IAAIC,QAAQ,IAAIE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACC,GAAG,CAACJ,SAAS,CAACK,KAAK,GAAGJ,QAAQ,CAACI,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE;IAC9FN,IAAI,CAACO,GAAG,CAAC,CAAC;EACZ;AACF;AACA,IAAIC,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDpC,SAAS,CAACmC,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC/BH,KAAK,CAACI,gBAAgB,GAAG,kBAAkB;IAC3C,OAAOJ,KAAK;EACd;EACAF,aAAa,CAACO,SAAS,CAACC,MAAM,GAAG,UAAUC,cAAc,EAAEC,OAAO,EAAE;IAClE,IAAI,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAI,CAACH,cAAc,CAACI,GAAG,CAAC,MAAM,CAAC,EAAE;MAC/B;IACF;IACA,IAAIC,SAAS,GAAGL,cAAc,CAACM,IAAI;IACnC,IAAIxC,KAAK,GAAGuC,SAAS,CAACvC,KAAK;IAC3B,IAAIyC,YAAY,GAAGzC,KAAK,CAACc,aAAa,CAAC,CAAC,CAAC4B,SAAS,CAAC,CAAC;IACpD,IAAIC,WAAW,GAAGJ,SAAS,CAACK,cAAc,CAAC,CAAC;IAC5C,IAAIC,eAAe,GAAGN,SAAS,CAACO,mBAAmB,CAAC,CAAC;IACrD,IAAIC,MAAM,GAAGxD,MAAM,CAACyD,GAAG,CAACT,SAAS,CAACU,aAAa,CAAC,CAAC,EAAE,UAAUC,SAAS,EAAE;MACtEA,SAAS,GAAG3D,MAAM,CAAC4D,KAAK,CAACD,SAAS,CAAC;MACnC,IAAIE,KAAK,GAAGb,SAAS,CAACa,KAAK;MAC3B,IAAIC,SAAS,GAAGD,KAAK,CAACtB,IAAI,KAAK,SAAS,GAAGsB,KAAK,CAACE,mBAAmB,CAACJ,SAAS,CAACG,SAAS,CAAC,GAAGH,SAAS,CAACG,SAAS;MAC/GH,SAAS,CAAC3B,KAAK,GAAGgB,SAAS,CAACgB,WAAW,CAACF,SAAS,CAAC;MAClD,OAAOH,SAAS;IAClB,CAAC,CAAC;IACFlC,eAAe,CAAC+B,MAAM,CAAC;IACvB/B,eAAe,CAAC2B,WAAW,CAAC;IAC5BpD,MAAM,CAACiE,IAAI,CAAC1D,WAAW,EAAE,UAAU2D,IAAI,EAAE;MACvC,IAAIvB,cAAc,CAACI,GAAG,CAAC,CAACmB,IAAI,EAAE,MAAM,CAAC,CAAC,KAAK,CAAClB,SAAS,CAACa,KAAK,CAACM,OAAO,CAAC,CAAC,IAAID,IAAI,KAAK,UAAU,CAAC,EAAE;QAC7FE,yBAAyB,CAACF,IAAI,CAAC,CAAC,IAAI,CAACrB,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAEM,MAAM,CAAC;MACxH;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDtB,aAAa,CAACK,IAAI,GAAG,WAAW;EAChC,OAAOL,aAAa;AACtB,CAAC,CAAC9B,QAAQ,CAAC;AACX,IAAIgE,yBAAyB,GAAG;EAC9BC,QAAQ,EAAE,SAAAA,CAAUxB,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAC5F,IAAIoB,cAAc,GAAG3B,cAAc,CAAC4B,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IACvE,IAAIvB,SAAS,GAAGvC,KAAK,CAAC+D,YAAY,CAAC,CAAC;IACpC,IAAIC,MAAM,GAAG3C,IAAI,CAAC4C,EAAE,GAAG,GAAG;IAC1B,IAAIC,WAAW,GAAG3B,SAAS,CAACG,SAAS,CAAC,CAAC;IACvC;IACA,IAAIyB,GAAG,GAAGvD,YAAY,CAACZ,KAAK,CAAC;IAC7B,IAAIoE,IAAI,GAAGD,GAAG,GAAG,CAAC,GAAG,CAAC;IACtB,IAAIE,KAAK;IACT,IAAIC,SAAS,GAAGjD,IAAI,CAACC,GAAG,CAAC4C,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK;IACpF,IAAIzB,YAAY,CAAC2B,IAAI,CAAC,KAAK,CAAC,EAAE;MAC5BC,KAAK,GAAG,IAAI7E,OAAO,CAAC8E,SAAS,CAAC,CAAC;QAC7BD,KAAK,EAAE;UACLE,EAAE,EAAEvE,KAAK,CAACuE,EAAE;UACZC,EAAE,EAAExE,KAAK,CAACwE,EAAE;UACZC,CAAC,EAAEhC,YAAY,CAAC0B,GAAG,CAAC;UACpBO,UAAU,EAAE,CAACR,WAAW,CAAC,CAAC,CAAC,GAAGF,MAAM;UACpCW,QAAQ,EAAE,CAACT,WAAW,CAAC,CAAC,CAAC,GAAGF,MAAM;UAClCY,SAAS,EAAErC,SAAS,CAACxB;QACvB,CAAC;QACD8D,KAAK,EAAEhB,cAAc,CAACiB,YAAY,CAAC,CAAC;QACpCC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACLX,KAAK,GAAG,IAAI7E,OAAO,CAACyF,IAAI,CAAC;QACvBZ,KAAK,EAAE;UACLE,EAAE,EAAEvE,KAAK,CAACuE,EAAE;UACZC,EAAE,EAAExE,KAAK,CAACwE,EAAE;UACZC,CAAC,EAAEhC,YAAY,CAAC0B,GAAG,CAAC;UACpBe,EAAE,EAAEzC,YAAY,CAAC2B,IAAI;QACvB,CAAC;QACDS,KAAK,EAAEhB,cAAc,CAACiB,YAAY,CAAC,CAAC;QACpCC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ;IACAX,KAAK,CAACQ,KAAK,CAACM,IAAI,GAAG,IAAI;IACvB/C,KAAK,CAACgD,GAAG,CAACf,KAAK,CAAC;EAClB,CAAC;EACDgB,QAAQ,EAAE,SAAAA,CAAUjD,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAC5F,IAAI6C,SAAS,GAAGpD,cAAc,CAAC4B,QAAQ,CAAC,UAAU,CAAC;IACnD,IAAIyB,OAAO,GAAG,CAACD,SAAS,CAAChD,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIgD,SAAS,CAAChD,GAAG,CAAC,QAAQ,CAAC;IAC1E,IAAIkD,MAAM,GAAG/C,YAAY,CAAC7B,YAAY,CAACZ,KAAK,CAAC,CAAC;IAC9C,IAAIyF,KAAK,GAAGlG,MAAM,CAACyD,GAAG,CAACL,WAAW,EAAE,UAAU+C,aAAa,EAAE;MAC3D,OAAO,IAAIlG,OAAO,CAACmG,IAAI,CAAC;QACtBtB,KAAK,EAAEtE,gBAAgB,CAACC,KAAK,EAAE,CAACwF,MAAM,EAAEA,MAAM,GAAGD,OAAO,CAAC,EAAEG,aAAa,CAACnE,KAAK;MAChF,CAAC,CAAC;IACJ,CAAC,CAAC;IACFa,KAAK,CAACgD,GAAG,CAAC5F,OAAO,CAACoG,SAAS,CAACH,KAAK,EAAE;MACjCZ,KAAK,EAAEtF,MAAM,CAACsG,QAAQ,CAACP,SAAS,CAACxB,QAAQ,CAAC,WAAW,CAAC,CAACgB,YAAY,CAAC,CAAC,EAAE;QACrEgB,MAAM,EAAE5D,cAAc,CAACI,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;MAC/D,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC;EACDyD,SAAS,EAAE,SAAAA,CAAU3D,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAEgG,UAAU,EAAEnD,eAAe,EAAEJ,YAAY,EAAE;IAC5F,IAAI,CAACI,eAAe,CAACzB,MAAM,EAAE;MAC3B;IACF;IACA,IAAIkE,SAAS,GAAGpD,cAAc,CAAC4B,QAAQ,CAAC,UAAU,CAAC;IACnD,IAAImC,cAAc,GAAG/D,cAAc,CAAC4B,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAIyB,OAAO,GAAG,CAACD,SAAS,CAAChD,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI2D,cAAc,CAAC3D,GAAG,CAAC,QAAQ,CAAC;IAC/E,IAAIkD,MAAM,GAAG/C,YAAY,CAAC7B,YAAY,CAACZ,KAAK,CAAC,CAAC;IAC9C,IAAIyF,KAAK,GAAG,EAAE;IACd,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,eAAe,CAACzB,MAAM,EAAE8E,CAAC,EAAE,EAAE;MAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,eAAe,CAACqD,CAAC,CAAC,CAAC9E,MAAM,EAAE+E,CAAC,EAAE,EAAE;QAClDV,KAAK,CAACW,IAAI,CAAC,IAAI5G,OAAO,CAACmG,IAAI,CAAC;UAC1BtB,KAAK,EAAEtE,gBAAgB,CAACC,KAAK,EAAE,CAACwF,MAAM,EAAEA,MAAM,GAAGD,OAAO,CAAC,EAAE1C,eAAe,CAACqD,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC5E,KAAK;QACxF,CAAC,CAAC,CAAC;MACL;IACF;IACAa,KAAK,CAACgD,GAAG,CAAC5F,OAAO,CAACoG,SAAS,CAACH,KAAK,EAAE;MACjCZ,KAAK,EAAEtF,MAAM,CAACsG,QAAQ,CAACI,cAAc,CAACnC,QAAQ,CAAC,WAAW,CAAC,CAACgB,YAAY,CAAC,CAAC,EAAEvF,MAAM,CAACsG,QAAQ,CAACP,SAAS,CAACR,YAAY,CAAC,CAAC,EAAE;QACpHgB,MAAM,EAAE5D,cAAc,CAACI,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC;MAC/D,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL,CAAC;EACD+D,SAAS,EAAE,SAAAA,CAAUjE,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAEM,MAAM,EAAE;IACrG,IAAIuD,eAAe,GAAGpE,cAAc,CAACqE,aAAa,CAAC,IAAI,CAAC;IACxD,IAAIC,gBAAgB,GAAGtE,cAAc,CAAC4B,QAAQ,CAAC,WAAW,CAAC;IAC3D,IAAI2C,WAAW,GAAGD,gBAAgB,CAAClE,GAAG,CAAC,QAAQ,CAAC;IAChD,IAAIoE,YAAY,GAAGxE,cAAc,CAACI,GAAG,CAAC,cAAc,CAAC;IACrD;IACA/C,MAAM,CAACiE,IAAI,CAACT,MAAM,EAAE,UAAUG,SAAS,EAAEyD,GAAG,EAAE;MAC5C,IAAIC,UAAU,GAAGJ,gBAAgB;MACjC,IAAInD,SAAS,GAAGH,SAAS,CAACG,SAAS;MACnC,IAAIoB,CAAC,GAAGhC,YAAY,CAAC7B,YAAY,CAACZ,KAAK,CAAC,CAAC;MACzC,IAAI6G,CAAC,GAAG7G,KAAK,CAACM,YAAY,CAAC,CAACmE,CAAC,GAAGgC,WAAW,EAAEvD,SAAS,CAAC3B,KAAK,CAAC,CAAC;MAC9D,IAAIgD,EAAE,GAAGvE,KAAK,CAACuE,EAAE;MACjB,IAAIC,EAAE,GAAGxE,KAAK,CAACwE,EAAE;MACjB,IAAIsC,cAAc,GAAGzF,IAAI,CAACC,GAAG,CAACuF,CAAC,CAAC,CAAC,CAAC,GAAGtC,EAAE,CAAC,GAAGE,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAGoC,CAAC,CAAC,CAAC,CAAC,GAAGtC,EAAE,GAAG,MAAM,GAAG,OAAO;MAC5F,IAAIwC,sBAAsB,GAAG1F,IAAI,CAACC,GAAG,CAACuF,CAAC,CAAC,CAAC,CAAC,GAAGrC,EAAE,CAAC,GAAGC,CAAC,GAAG,GAAG,GAAG,QAAQ,GAAGoC,CAAC,CAAC,CAAC,CAAC,GAAGrC,EAAE,GAAG,KAAK,GAAG,QAAQ;MACpG,IAAI8B,eAAe,IAAIA,eAAe,CAACjD,SAAS,CAAC,EAAE;QACjD,IAAI2D,eAAe,GAAGV,eAAe,CAACjD,SAAS,CAAC;QAChD,IAAI9D,MAAM,CAAC0H,QAAQ,CAACD,eAAe,CAAC,IAAIA,eAAe,CAACE,SAAS,EAAE;UACjEN,UAAU,GAAG,IAAIlH,KAAK,CAACsH,eAAe,CAACE,SAAS,EAAEV,gBAAgB,EAAEA,gBAAgB,CAACrE,OAAO,CAAC;QAC/F;MACF;MACA,IAAIgF,MAAM,GAAG,IAAI3H,OAAO,CAAC4H,IAAI,CAAC;QAC5BpC,MAAM,EAAEpF,WAAW,CAACyH,aAAa,CAACnF,cAAc,CAAC;QACjD2C,KAAK,EAAEpF,eAAe,CAACmH,UAAU,EAAE;UACjCU,CAAC,EAAET,CAAC,CAAC,CAAC,CAAC;UACPU,CAAC,EAAEV,CAAC,CAAC,CAAC,CAAC;UACP1B,IAAI,EAAEyB,UAAU,CAACY,YAAY,CAAC,CAAC,IAAItF,cAAc,CAACI,GAAG,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;UACzFmF,IAAI,EAAEvE,SAAS,CAACwE,cAAc;UAC9BC,KAAK,EAAEb,cAAc;UACrBc,aAAa,EAAEb;QACjB,CAAC;MACH,CAAC,CAAC;MACF3E,KAAK,CAACgD,GAAG,CAAC+B,MAAM,CAAC;MACjB;MACA,IAAIT,YAAY,EAAE;QAChB,IAAImB,SAAS,GAAGjI,WAAW,CAACkI,qBAAqB,CAAC5F,cAAc,CAAC;QACjE2F,SAAS,CAACE,UAAU,GAAG,WAAW;QAClCF,SAAS,CAACG,KAAK,GAAG9E,SAAS,CAAC+E,QAAQ;QACpCpI,SAAS,CAACsH,MAAM,CAAC,CAACU,SAAS,GAAGA,SAAS;MACzC;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACDK,SAAS,EAAE,SAAAA,CAAU9F,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAC7F,IAAI0F,cAAc,GAAGjG,cAAc,CAAC4B,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAID,cAAc,GAAGsE,cAAc,CAACrE,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAIsE,UAAU,GAAGvE,cAAc,CAACvB,GAAG,CAAC,OAAO,CAAC;IAC5C,IAAI+F,SAAS,GAAG,CAAC;IACjBD,UAAU,GAAGA,UAAU,YAAYE,KAAK,GAAGF,UAAU,GAAG,CAACA,UAAU,CAAC;IACpE,IAAIG,UAAU,GAAG,EAAE;IACnB,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvD,WAAW,CAACvB,MAAM,EAAE8E,CAAC,EAAE,EAAE;MAC3C,IAAIsC,UAAU,GAAGH,SAAS,EAAE,GAAGD,UAAU,CAAChH,MAAM;MAChDmH,UAAU,CAACC,UAAU,CAAC,GAAGD,UAAU,CAACC,UAAU,CAAC,IAAI,EAAE;MACrDD,UAAU,CAACC,UAAU,CAAC,CAACpC,IAAI,CAAC,IAAI5G,OAAO,CAACmG,IAAI,CAAC;QAC3CtB,KAAK,EAAEtE,gBAAgB,CAACC,KAAK,EAAEyC,YAAY,EAAEE,WAAW,CAACuD,CAAC,CAAC,CAAC3E,KAAK;MACnE,CAAC,CAAC,CAAC;IACL;IACA;IACA;IACA,KAAK,IAAI2E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,UAAU,CAACnH,MAAM,EAAE8E,CAAC,EAAE,EAAE;MAC1C9D,KAAK,CAACgD,GAAG,CAAC5F,OAAO,CAACoG,SAAS,CAAC2C,UAAU,CAACrC,CAAC,CAAC,EAAE;QACzCrB,KAAK,EAAEtF,MAAM,CAACsG,QAAQ,CAAC;UACrBC,MAAM,EAAEsC,UAAU,CAAClC,CAAC,GAAGkC,UAAU,CAAChH,MAAM;QAC1C,CAAC,EAAEyC,cAAc,CAACiB,YAAY,CAAC,CAAC,CAAC;QACjCE,MAAM,EAAE,IAAI;QACZyD,CAAC,EAAEvG,cAAc,CAACI,GAAG,CAAC,GAAG;MAC3B,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACDoG,cAAc,EAAE,SAAAA,CAAUtG,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAClG,IAAI,CAACI,eAAe,CAACzB,MAAM,EAAE;MAC3B;IACF;IACA,IAAIuH,mBAAmB,GAAGzG,cAAc,CAAC4B,QAAQ,CAAC,gBAAgB,CAAC;IACnE,IAAID,cAAc,GAAG8E,mBAAmB,CAAC7E,QAAQ,CAAC,WAAW,CAAC;IAC9D,IAAI2B,KAAK,GAAG,EAAE;IACd,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrD,eAAe,CAACzB,MAAM,EAAE8E,CAAC,EAAE,EAAE;MAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtD,eAAe,CAACqD,CAAC,CAAC,CAAC9E,MAAM,EAAE+E,CAAC,EAAE,EAAE;QAClDV,KAAK,CAACW,IAAI,CAAC,IAAI5G,OAAO,CAACmG,IAAI,CAAC;UAC1BtB,KAAK,EAAEtE,gBAAgB,CAACC,KAAK,EAAEyC,YAAY,EAAEI,eAAe,CAACqD,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC5E,KAAK;QAC1E,CAAC,CAAC,CAAC;MACL;IACF;IACAa,KAAK,CAACgD,GAAG,CAAC5F,OAAO,CAACoG,SAAS,CAACH,KAAK,EAAE;MACjCZ,KAAK,EAAEhB,cAAc,CAACiB,YAAY,CAAC,CAAC;MACpCE,MAAM,EAAE,IAAI;MACZyD,CAAC,EAAEvG,cAAc,CAACI,GAAG,CAAC,GAAG;IAC3B,CAAC,CAAC,CAAC;EACL,CAAC;EACDsG,SAAS,EAAE,SAAAA,CAAUxG,KAAK,EAAEF,cAAc,EAAElC,KAAK,EAAE2C,WAAW,EAAEE,eAAe,EAAEJ,YAAY,EAAE;IAC7F,IAAI,CAACE,WAAW,CAACvB,MAAM,EAAE;MACvB;IACF;IACA,IAAIyH,cAAc,GAAG3G,cAAc,CAAC4B,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAIgF,cAAc,GAAGD,cAAc,CAAC/E,QAAQ,CAAC,WAAW,CAAC;IACzD,IAAIiF,UAAU,GAAGD,cAAc,CAACxG,GAAG,CAAC,OAAO,CAAC;IAC5C,IAAI+F,SAAS,GAAG,CAAC;IACjBU,UAAU,GAAGA,UAAU,YAAYT,KAAK,GAAGS,UAAU,GAAG,CAACA,UAAU,CAAC;IACpE,IAAIC,UAAU,GAAG,EAAE;IACnB,IAAIhF,MAAM,GAAG3C,IAAI,CAAC4C,EAAE,GAAG,GAAG;IAC1B,IAAIgF,SAAS,GAAG,CAACtG,WAAW,CAAC,CAAC,CAAC,CAACpB,KAAK,GAAGyC,MAAM;IAC9C,IAAIkB,EAAE,GAAG7D,IAAI,CAAC6H,GAAG,CAACzG,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;IACnD,IAAI0G,EAAE,GAAG9H,IAAI,CAAC+H,GAAG,CAAC3G,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;IACnD,IAAImC,SAAS,GAAG1C,cAAc,CAACI,GAAG,CAAC,WAAW,CAAC;IAC/C,KAAK,IAAI4D,CAAC,GAAG,CAAC,EAAEmD,GAAG,GAAG1G,WAAW,CAACvB,MAAM,EAAE8E,CAAC,IAAImD,GAAG,EAAEnD,CAAC,EAAE,EAAE;MACvD,IAAI3E,KAAK,GAAG2E,CAAC,KAAKmD,GAAG,GAAG1G,WAAW,CAAC,CAAC,CAAC,CAACpB,KAAK,GAAGoB,WAAW,CAACuD,CAAC,CAAC,CAAC3E,KAAK;MACnE,IAAIiH,UAAU,GAAGH,SAAS,EAAE,GAAGU,UAAU,CAAC3H,MAAM;MAChD4H,UAAU,CAACR,UAAU,CAAC,GAAGQ,UAAU,CAACR,UAAU,CAAC,IAAI,EAAE;MACrDQ,UAAU,CAACR,UAAU,CAAC,CAACpC,IAAI,CAAC,IAAI5G,OAAO,CAAC8J,MAAM,CAAC;QAC7CjF,KAAK,EAAE;UACLE,EAAE,EAAEvE,KAAK,CAACuE,EAAE;UACZC,EAAE,EAAExE,KAAK,CAACwE,EAAE;UACZU,EAAE,EAAEA,EAAE;UACNT,CAAC,EAAE0E,EAAE;UACLzE,UAAU,EAAEuE,SAAS;UACrBtE,QAAQ,EAAE,CAACpD,KAAK,GAAGyC,MAAM;UACzBY,SAAS,EAAEA;QACb,CAAC;QACDI,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;MACHiE,SAAS,GAAG,CAAC1H,KAAK,GAAGyC,MAAM;IAC7B;IACA;IACA;IACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8C,UAAU,CAAC5H,MAAM,EAAE8E,CAAC,EAAE,EAAE;MAC1C9D,KAAK,CAACgD,GAAG,CAAC5F,OAAO,CAACoG,SAAS,CAACoD,UAAU,CAAC9C,CAAC,CAAC,EAAE;QACzCrB,KAAK,EAAEtF,MAAM,CAACsG,QAAQ,CAAC;UACrBV,IAAI,EAAE4D,UAAU,CAAC7C,CAAC,GAAG6C,UAAU,CAAC3H,MAAM;QACxC,CAAC,EAAE0H,cAAc,CAACS,YAAY,CAAC,CAAC,CAAC;QACjCvE,MAAM,EAAE;MACV,CAAC,CAAC,CAAC;IACL;EACF;AACF,CAAC;AACD,eAAevD,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}