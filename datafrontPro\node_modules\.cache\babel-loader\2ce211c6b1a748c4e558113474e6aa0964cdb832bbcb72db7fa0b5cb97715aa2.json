{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nvar RingShape = function () {\n  function RingShape() {\n    this.cx = 0;\n    this.cy = 0;\n    this.r = 0;\n    this.r0 = 0;\n  }\n  return RingShape;\n}();\nexport { RingShape };\nvar Ring = function (_super) {\n  __extends(Ring, _super);\n  function Ring(opts) {\n    return _super.call(this, opts) || this;\n  }\n  Ring.prototype.getDefaultShape = function () {\n    return new RingShape();\n  };\n  Ring.prototype.buildPath = function (ctx, shape) {\n    var x = shape.cx;\n    var y = shape.cy;\n    var PI2 = Math.PI * 2;\n    ctx.moveTo(x + shape.r, y);\n    ctx.arc(x, y, shape.r, 0, PI2, false);\n    ctx.moveTo(x + shape.r0, y);\n    ctx.arc(x, y, shape.r0, 0, PI2, true);\n  };\n  return Ring;\n}(Path);\nRing.prototype.type = 'ring';\nexport default Ring;", "map": {"version": 3, "names": ["__extends", "Path", "RingShape", "cx", "cy", "r", "r0", "Ring", "_super", "opts", "call", "prototype", "getDefaultShape", "buildPath", "ctx", "shape", "x", "y", "PI2", "Math", "PI", "moveTo", "arc", "type"], "sources": ["E:/AllProject/datafront/node_modules/zrender/lib/graphic/shape/Ring.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Path from '../Path.js';\nvar RingShape = (function () {\n    function RingShape() {\n        this.cx = 0;\n        this.cy = 0;\n        this.r = 0;\n        this.r0 = 0;\n    }\n    return RingShape;\n}());\nexport { RingShape };\nvar Ring = (function (_super) {\n    __extends(Ring, _super);\n    function Ring(opts) {\n        return _super.call(this, opts) || this;\n    }\n    Ring.prototype.getDefaultShape = function () {\n        return new RingShape();\n    };\n    Ring.prototype.buildPath = function (ctx, shape) {\n        var x = shape.cx;\n        var y = shape.cy;\n        var PI2 = Math.PI * 2;\n        ctx.moveTo(x + shape.r, y);\n        ctx.arc(x, y, shape.r, 0, PI2, false);\n        ctx.moveTo(x + shape.r0, y);\n        ctx.arc(x, y, shape.r0, 0, PI2, true);\n    };\n    return Ring;\n}(Path));\nRing.prototype.type = 'ring';\nexport default Ring;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,IAAIC,SAAS,GAAI,YAAY;EACzB,SAASA,SAASA,CAAA,EAAG;IACjB,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,EAAE,GAAG,CAAC;IACX,IAAI,CAACC,CAAC,GAAG,CAAC;IACV,IAAI,CAACC,EAAE,GAAG,CAAC;EACf;EACA,OAAOJ,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,SAASA,SAAS;AAClB,IAAIK,IAAI,GAAI,UAAUC,MAAM,EAAE;EAC1BR,SAAS,CAACO,IAAI,EAAEC,MAAM,CAAC;EACvB,SAASD,IAAIA,CAACE,IAAI,EAAE;IAChB,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;EAC1C;EACAF,IAAI,CAACI,SAAS,CAACC,eAAe,GAAG,YAAY;IACzC,OAAO,IAAIV,SAAS,CAAC,CAAC;EAC1B,CAAC;EACDK,IAAI,CAACI,SAAS,CAACE,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IAC7C,IAAIC,CAAC,GAAGD,KAAK,CAACZ,EAAE;IAChB,IAAIc,CAAC,GAAGF,KAAK,CAACX,EAAE;IAChB,IAAIc,GAAG,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;IACrBN,GAAG,CAACO,MAAM,CAACL,CAAC,GAAGD,KAAK,CAACV,CAAC,EAAEY,CAAC,CAAC;IAC1BH,GAAG,CAACQ,GAAG,CAACN,CAAC,EAAEC,CAAC,EAAEF,KAAK,CAACV,CAAC,EAAE,CAAC,EAAEa,GAAG,EAAE,KAAK,CAAC;IACrCJ,GAAG,CAACO,MAAM,CAACL,CAAC,GAAGD,KAAK,CAACT,EAAE,EAAEW,CAAC,CAAC;IAC3BH,GAAG,CAACQ,GAAG,CAACN,CAAC,EAAEC,CAAC,EAAEF,KAAK,CAACT,EAAE,EAAE,CAAC,EAAEY,GAAG,EAAE,IAAI,CAAC;EACzC,CAAC;EACD,OAAOX,IAAI;AACf,CAAC,CAACN,IAAI,CAAE;AACRM,IAAI,CAACI,SAAS,CAACY,IAAI,GAAG,MAAM;AAC5B,eAAehB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}