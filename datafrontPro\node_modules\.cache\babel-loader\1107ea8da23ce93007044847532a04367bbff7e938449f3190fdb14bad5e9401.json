{"ast": null, "code": "import { dataApi } from './api';\nexport default {\n  name: 'App',\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: []\n    };\n  },\n  mounted() {\n    this.loadTables();\n  },\n  methods: {\n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables();\n        this.tables = response.data;\n      } catch (error) {\n        this.$message.error('加载数据表失败');\n        console.error(error);\n      }\n    },\n    selectTable(table) {\n      this.selectedTable = table;\n      this.queryResult = null;\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    async submitQuestion() {\n      if (!this.question.trim()) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n      try {\n        // 如果用户没选择表，默认使用第一个\n        const tableCode = this.selectedTable ? this.selectedTable.tableCode : this.tables.length > 0 ? this.tables[0].tableCode : null;\n        if (!tableCode) {\n          this.$message.warning('请先选择数据表');\n          return;\n        }\n        const response = await dataApi.queryByQuestion(tableCode, this.question);\n        this.queryResult = response.data;\n\n        // 清空输入框\n        this.question = '';\n      } catch (error) {\n        this.$message.error('查询出错，请稍后重试');\n        console.error(error);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "name", "data", "description", "tablename", "tables", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "mounted", "loadTables", "methods", "response", "getAllTables", "error", "$message", "console", "selectTable", "table", "showSuggestions", "useQuestion", "q", "getTableFields", "tableCode", "submitQuestion", "trim", "warning", "length", "queryByQuestion"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <!-- 顶部导航栏 -->\n    <div class=\"header-nav\">\n      <div class=\"logo\">\n        <span>FAST BI</span>\n      </div>\n      <div class=\"nav-menu\">\n        <div class=\"menu-item active\">智能小Q</div>\n        <div class=\"menu-item\">我的看板</div>\n        <div class=\"menu-item\">企业门户</div>\n        <div class=\"menu-item\">工作台</div>\n        <div class=\"menu-item\">开放平台</div>\n        <div class=\"menu-item\">模板市场</div>\n      </div>\n      <div class=\"nav-tools\">\n        <i class=\"el-icon-search\"></i>\n        <i class=\"el-icon-menu\"></i>\n        <i class=\"el-icon-bell\"></i>\n        <i class=\"el-icon-setting\"></i>\n        <div class=\"avatar\">\n          <img src=\"https://via.placeholder.com/30\" alt=\"avatar\" />\n        </div>\n      </div>\n    </div>\n\n    <!-- 主体内容区 -->\n    <div class=\"main-container\">\n      <!-- 左侧菜单 -->\n      <div class=\"side-menu\">\n        <div class=\"side-header\">\n          <span>智能小Q</span>\n          <i class=\"el-icon-menu\"></i>\n        </div>\n\n        <div class=\"side-menu-items\">\n          <div class=\"side-menu-item active\">\n            <i class=\"el-icon-chat-dot-round\"></i>\n            <span>智能问数</span>\n          </div>\n          <div class=\"side-menu-item\">\n            <i class=\"el-icon-chat-line-round\"></i>\n            <span>智汇问答</span>\n          </div>\n        </div>\n\n        <div class=\"recent-chats\">\n          <div class=\"recent-chats-header\">\n            <span>近30天对话</span>\n            <i class=\"el-icon-arrow-down\"></i>\n          </div>\n\n          <div class=\"chat-items\">\n            <div class=\"chat-item\">\n              <div class=\"chat-title\">上个月女装销售的增速是多少?</div>\n              <div class=\"chat-time\">25/06/25 14:26:18</div>\n            </div>\n            \n            <div class=\"chat-item\">\n              <div class=\"chat-title\">去年各省份的销售额分布是什么样的?</div>\n              <div class=\"chat-time\">25/06/24 09:55:41</div>\n            </div>\n            \n            <div class=\"chat-item\">\n              <div class=\"chat-title\">给我生成省份的图标</div>\n              <div class=\"chat-time\">25/06/23 17:37:56</div>\n            </div>\n            \n            <div class=\"chat-item\">\n              <div class=\"chat-title\">给我说下2020-2025年出生人口</div>\n              <div class=\"chat-time\">25/06/23 15:51:01</div>\n            </div>\n            \n            <div class=\"chat-item\">\n              <div class=\"chat-title\">列出2025年GDP数据表格</div>\n              <div class=\"chat-time\">25/06/23 15:10:37</div>\n            </div>\n            \n            <div class=\"chat-item\">\n              <div class=\"chat-title\">去年销量第一的商品名称是什么</div>\n              <div class=\"chat-time\">25/06/23 11:41:13</div>\n            </div>\n          </div>\n        </div>\n        \n        <div class=\"side-footer\">\n          <i class=\"el-icon-document\"></i>\n          <span>帮助文档</span>\n        </div>\n      </div>\n\n      <!-- 右侧内容区 -->\n      <div class=\"content-area\">\n        <div class=\"welcome-section\">\n          <h1>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h1>\n          <p class=\"description\">通过对话的方式进行数据二次计算与可视化分析，立即从以下数据集中选择开始问吧！</p>\n        </div>\n\n        <div class=\"data-section\">\n          <div class=\"section-title\">可用数据</div>\n          \n          <div class=\"data-cards\">\n            <div class=\"data-card\">\n              <div class=\"data-card-header\">\n                <span class=\"card-tag\">销售明细数据</span>\n                <span class=\"card-usage\">常用</span>\n              </div>\n              <div class=\"data-card-content\">\n                <div class=\"data-field\">数量</div>\n                <div class=\"data-field\">销售金额</div>\n                <div class=\"data-field\">利润</div>\n                <div class=\"data-field\">订单日期</div>\n                <div class=\"data-field\">...</div>\n              </div>\n            </div>\n\n            <div class=\"data-card\">\n              <div class=\"data-card-header\">\n                <span class=\"card-tag\">各省份GDP年度数据（2000-2022）</span>\n              </div>\n              <div class=\"data-card-content\">\n                <div class=\"data-field\">地区生产总值</div>\n                <div class=\"data-field\">人均地区生产总值</div>\n                <div class=\"data-field\">...</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 输入区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-hint\">\n            <span>👋 直接问我问题，或在上方选择一个主题/数据开始！</span>\n          </div>\n          \n          <div class=\"input-box\">\n            <div class=\"select-data\">\n              <span>选择数据</span>\n              <i class=\"el-icon-arrow-down\"></i>\n            </div>\n            \n            <input type=\"text\" placeholder=\"请直接告诉我您的问题，或输入/唤起快捷提问吧\" />\n            \n            <div class=\"input-actions\">\n              <div class=\"action-button recommend\">\n                <i class=\"el-icon-magic-stick\"></i>\n                <span>官方推荐</span>\n                <i class=\"el-icon-arrow-down\"></i>\n              </div>\n              \n              <div class=\"action-button help\">\n                <i class=\"el-icon-question\"></i>\n                <span>数据解读</span>\n              </div>\n              \n              <div class=\"action-button send\">\n                <i class=\"el-icon-position\"></i>\n              </div>\n            </div>\n          </div>\n          \n          <div class=\"tokens-info\">\n            <span>剩余问答额度 490</span>\n            <span class=\"buy-more\">购买</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api'\n\nexport default {\n  name: 'App',\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: []\n    }\n  },\n  mounted() {\n    this.loadTables()\n  },\n  methods: {\n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables()\n        this.tables = response.data\n      } catch (error) {\n        this.$message.error('加载数据表失败')\n        console.error(error)\n      }\n    },\n    selectTable(table) {\n      this.selectedTable = table\n      this.queryResult = null\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    async submitQuestion() {\n      if (!this.question.trim()) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      try {\n        // 如果用户没选择表，默认使用第一个\n        const tableCode = this.selectedTable ? this.selectedTable.tableCode : \n                         (this.tables.length > 0 ? this.tables[0].tableCode : null)\n        \n        if (!tableCode) {\n          this.$message.warning('请先选择数据表')\n          return\n        }\n        \n        const response = await dataApi.queryByQuestion(tableCode, this.question)\n        this.queryResult = response.data\n        \n        // 清空输入框\n        this.question = ''\n      } catch (error) {\n        this.$message.error('查询出错，请稍后重试')\n        console.error(error)\n      }\n    }\n  }\n}\n</script>\n\n<style>\n/* 全局样式 */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: \"PingFang SC\", \"Helvetica Neue\", Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #333;\n  background-color: #f5f7fa;\n}\n\n#app {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n}\n\n/* 顶部导航栏 */\n.header-nav {\n  display: flex;\n  align-items: center;\n  height: 56px;\n  padding: 0 20px;\n  background-color: #fff;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  position: relative;\n  z-index: 100;\n}\n\n.logo {\n  font-size: 22px;\n  font-weight: bold;\n  margin-right: 40px;\n}\n\n.nav-menu {\n  display: flex;\n  flex-grow: 1;\n}\n\n.menu-item {\n  padding: 0 15px;\n  font-size: 14px;\n  cursor: pointer;\n  height: 56px;\n  line-height: 56px;\n  position: relative;\n}\n\n.menu-item.active {\n  color: #1890ff;\n  font-weight: 500;\n}\n\n.menu-item.active::after {\n  content: \"\";\n  position: absolute;\n  bottom: 0;\n  left: 15px;\n  right: 15px;\n  height: 2px;\n  background-color: #1890ff;\n}\n\n.nav-tools {\n  display: flex;\n  align-items: center;\n}\n\n.nav-tools i {\n  font-size: 20px;\n  margin-left: 20px;\n  color: #666;\n  cursor: pointer;\n}\n\n.avatar {\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  margin-left: 20px;\n  overflow: hidden;\n}\n\n.avatar img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 主体内容区 */\n.main-container {\n  display: flex;\n  flex-grow: 1;\n  overflow: hidden;\n}\n\n/* 左侧菜单 */\n.side-menu {\n  width: 220px;\n  background-color: #f7f9fc;\n  border-right: 1px solid #e8eaec;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.side-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e8eaec;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.side-menu-items {\n  padding: 10px 0;\n}\n\n.side-menu-item {\n  padding: 10px 20px;\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  font-size: 14px;\n}\n\n.side-menu-item i {\n  font-size: 16px;\n  margin-right: 8px;\n}\n\n.side-menu-item.active {\n  background-color: #e6f7ff;\n  color: #1890ff;\n  border-right: 3px solid #1890ff;\n}\n\n.recent-chats {\n  flex-grow: 1;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.recent-chats-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 20px;\n  color: #999;\n  font-size: 14px;\n}\n\n.chat-items {\n  overflow-y: auto;\n}\n\n.chat-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.chat-title {\n  font-size: 13px;\n  color: #333;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  line-height: 1.5;\n}\n\n.chat-time {\n  font-size: 12px;\n  color: #999;\n  margin-top: 4px;\n}\n\n.side-footer {\n  padding: 15px 20px;\n  display: flex;\n  align-items: center;\n  border-top: 1px solid #e8eaec;\n  color: #666;\n  font-size: 14px;\n}\n\n.side-footer i {\n  margin-right: 6px;\n}\n\n/* 右侧内容区 */\n.content-area {\n  flex-grow: 1;\n  padding: 20px 40px;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.welcome-section {\n  margin-bottom: 30px;\n}\n\n.welcome-section h1 {\n  font-size: 24px;\n  font-weight: 500;\n  color: #333;\n}\n\n.highlight {\n  color: #1890ff;\n}\n\n.description {\n  font-size: 14px;\n  color: #666;\n  margin-top: 10px;\n}\n\n.data-section {\n  margin-bottom: 30px;\n}\n\n.section-title {\n  font-size: 16px;\n  font-weight: 500;\n  margin-bottom: 15px;\n}\n\n.data-cards {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 20px;\n}\n\n.data-card {\n  background-color: #fff;\n  border-radius: 4px;\n  padding: 15px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n  cursor: pointer;\n  transition: transform 0.2s, box-shadow 0.2s;\n}\n\n.data-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n}\n\n.data-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n  margin-bottom: 10px;\n}\n\n.card-tag {\n  font-weight: 500;\n}\n\n.card-usage {\n  background-color: #e6f7ff;\n  color: #1890ff;\n  padding: 2px 8px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.data-card-content {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.data-field {\n  background-color: #f5f7fa;\n  padding: 3px 10px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n/* 输入区域 */\n.input-section {\n  margin-top: auto;\n  padding-top: 20px;\n}\n\n.input-hint {\n  text-align: center;\n  margin-bottom: 10px;\n  color: #666;\n  font-size: 14px;\n}\n\n.input-box {\n  display: flex;\n  align-items: center;\n  background-color: #fff;\n  border-radius: 4px;\n  padding: 10px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n}\n\n.select-data {\n  display: flex;\n  align-items: center;\n  background-color: #f7f9fc;\n  padding: 5px 10px;\n  border-radius: 4px;\n  margin-right: 10px;\n  font-size: 14px;\n  color: #666;\n}\n\n.input-box input {\n  flex-grow: 1;\n  border: none;\n  outline: none;\n  padding: 8px;\n  font-size: 14px;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n}\n\n.action-button {\n  display: flex;\n  align-items: center;\n  padding: 5px 10px;\n  border-radius: 4px;\n  margin-left: 10px;\n  cursor: pointer;\n  font-size: 13px;\n}\n\n.action-button i {\n  margin-right: 4px;\n}\n\n.action-button.recommend {\n  background-color: #f7f9fc;\n  color: #666;\n}\n\n.action-button.help {\n  background-color: #f7f9fc;\n  color: #666;\n}\n\n.action-button.send {\n  background-color: #1890ff;\n  color: #fff;\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.action-button.send i {\n  margin: 0;\n}\n\n.tokens-info {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  margin-top: 10px;\n  color: #999;\n  font-size: 12px;\n}\n\n.buy-more {\n  color: #1890ff;\n  margin-left: 5px;\n  cursor: pointer;\n}\n</style>"], "mappings": "AA4KA,SAAAA,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACA,MAAAD,WAAA;MACA;QACA,MAAAE,QAAA,SAAAf,OAAA,CAAAgB,YAAA;QACA,KAAAX,MAAA,GAAAU,QAAA,CAAAb,IAAA;MACA,SAAAe,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,CAAAA,KAAA;MACA;IACA;IACAG,YAAAC,KAAA;MACA,KAAAf,aAAA,GAAAe,KAAA;MACA,KAAAb,WAAA;IACA;IACAc,gBAAA;MACA,KAAAb,oBAAA,SAAAA,oBAAA;IACA;IACAc,YAAAC,CAAA;MACA,KAAAjB,QAAA,GAAAiB,CAAA;MACA,KAAAf,oBAAA;IACA;IACAgB,eAAAJ,KAAA;MACA,IAAAA,KAAA,CAAAK,SAAA;QACA;MACA;MACA;IACA;IACA,MAAAC,eAAA;MACA,UAAApB,QAAA,CAAAqB,IAAA;QACA,KAAAV,QAAA,CAAAW,OAAA;QACA;MACA;MAEA;QACA;QACA,MAAAH,SAAA,QAAApB,aAAA,QAAAA,aAAA,CAAAoB,SAAA,GACA,KAAArB,MAAA,CAAAyB,MAAA,YAAAzB,MAAA,IAAAqB,SAAA;QAEA,KAAAA,SAAA;UACA,KAAAR,QAAA,CAAAW,OAAA;UACA;QACA;QAEA,MAAAd,QAAA,SAAAf,OAAA,CAAA+B,eAAA,CAAAL,SAAA,OAAAnB,QAAA;QACA,KAAAC,WAAA,GAAAO,QAAA,CAAAb,IAAA;;QAEA;QACA,KAAAK,QAAA;MACA,SAAAU,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,CAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}