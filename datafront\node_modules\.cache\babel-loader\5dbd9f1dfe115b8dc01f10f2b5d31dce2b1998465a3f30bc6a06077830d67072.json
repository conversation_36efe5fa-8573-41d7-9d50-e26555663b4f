{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { indexOf, createHashMap, assert } from 'zrender/lib/core/util.js';\nexport var DATA_ZOOM_AXIS_DIMENSIONS = ['x', 'y', 'radius', 'angle', 'single'];\n// Supported coords.\n// FIXME: polar has been broken (but rarely used).\nvar SERIES_COORDS = ['cartesian2d', 'polar', 'singleAxis'];\nexport function isCoordSupported(seriesModel) {\n  var coordType = seriesModel.get('coordinateSystem');\n  return indexOf(SERIES_COORDS, coordType) >= 0;\n}\nexport function getAxisMainType(axisDim) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(axisDim);\n  }\n  return axisDim + 'Axis';\n}\nexport function getAxisIndexPropName(axisDim) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(axisDim);\n  }\n  return axisDim + 'AxisIndex';\n}\nexport function getAxisIdPropName(axisDim) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(axisDim);\n  }\n  return axisDim + 'AxisId';\n}\n/**\r\n * If two dataZoomModels has the same axis controlled, we say that they are 'linked'.\r\n * This function finds all linked dataZoomModels start from the given payload.\r\n */\nexport function findEffectedDataZooms(ecModel, payload) {\n  // Key: `DataZoomAxisDimension`\n  var axisRecords = createHashMap();\n  var effectedModels = [];\n  // Key: uid of dataZoomModel\n  var effectedModelMap = createHashMap();\n  // Find the dataZooms specified by payload.\n  ecModel.eachComponent({\n    mainType: 'dataZoom',\n    query: payload\n  }, function (dataZoomModel) {\n    if (!effectedModelMap.get(dataZoomModel.uid)) {\n      addToEffected(dataZoomModel);\n    }\n  });\n  // Start from the given dataZoomModels, travel the graph to find\n  // all of the linked dataZoom models.\n  var foundNewLink;\n  do {\n    foundNewLink = false;\n    ecModel.eachComponent('dataZoom', processSingle);\n  } while (foundNewLink);\n  function processSingle(dataZoomModel) {\n    if (!effectedModelMap.get(dataZoomModel.uid) && isLinked(dataZoomModel)) {\n      addToEffected(dataZoomModel);\n      foundNewLink = true;\n    }\n  }\n  function addToEffected(dataZoom) {\n    effectedModelMap.set(dataZoom.uid, true);\n    effectedModels.push(dataZoom);\n    markAxisControlled(dataZoom);\n  }\n  function isLinked(dataZoomModel) {\n    var isLink = false;\n    dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n      var axisIdxArr = axisRecords.get(axisDim);\n      if (axisIdxArr && axisIdxArr[axisIndex]) {\n        isLink = true;\n      }\n    });\n    return isLink;\n  }\n  function markAxisControlled(dataZoomModel) {\n    dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n      (axisRecords.get(axisDim) || axisRecords.set(axisDim, []))[axisIndex] = true;\n    });\n  }\n  return effectedModels;\n}\n/**\r\n * Find the first target coordinate system.\r\n * Available after model built.\r\n *\r\n * @return Like {\r\n *                  grid: [\r\n *                      {model: coord0, axisModels: [axis1, axis3], coordIndex: 1},\r\n *                      {model: coord1, axisModels: [axis0, axis2], coordIndex: 0},\r\n *                      ...\r\n *                  ],  // cartesians must not be null/undefined.\r\n *                  polar: [\r\n *                      {model: coord0, axisModels: [axis4], coordIndex: 0},\r\n *                      ...\r\n *                  ],  // polars must not be null/undefined.\r\n *                  singleAxis: [\r\n *                      {model: coord0, axisModels: [], coordIndex: 0}\r\n *                  ]\r\n *              }\r\n */\nexport function collectReferCoordSysModelInfo(dataZoomModel) {\n  var ecModel = dataZoomModel.ecModel;\n  var coordSysInfoWrap = {\n    infoList: [],\n    infoMap: createHashMap()\n  };\n  dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n    var axisModel = ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n    if (!axisModel) {\n      return;\n    }\n    var coordSysModel = axisModel.getCoordSysModel();\n    if (!coordSysModel) {\n      return;\n    }\n    var coordSysUid = coordSysModel.uid;\n    var coordSysInfo = coordSysInfoWrap.infoMap.get(coordSysUid);\n    if (!coordSysInfo) {\n      coordSysInfo = {\n        model: coordSysModel,\n        axisModels: []\n      };\n      coordSysInfoWrap.infoList.push(coordSysInfo);\n      coordSysInfoWrap.infoMap.set(coordSysUid, coordSysInfo);\n    }\n    coordSysInfo.axisModels.push(axisModel);\n  });\n  return coordSysInfoWrap;\n}", "map": {"version": 3, "names": ["indexOf", "createHashMap", "assert", "DATA_ZOOM_AXIS_DIMENSIONS", "SERIES_COORDS", "isCoordSupported", "seriesModel", "coordType", "get", "getAxisMainType", "axisDim", "process", "env", "NODE_ENV", "getAxisIndexPropName", "getAxisIdPropName", "findEffectedDataZooms", "ecModel", "payload", "axisRecords", "effectedModels", "effectedModelMap", "eachComponent", "mainType", "query", "dataZoomModel", "uid", "addToEffected", "foundNewLink", "processSingle", "isLinked", "dataZoom", "set", "push", "markAxisControlled", "isLink", "eachTargetAxis", "axisIndex", "axisIdxArr", "collectReferCoordSysModelInfo", "coordSysInfoWrap", "infoList", "infoMap", "axisModel", "getComponent", "coordSysModel", "getCoordSysModel", "coordSysUid", "coordSysInfo", "model", "axisModels"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/dataZoom/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { indexOf, createHashMap, assert } from 'zrender/lib/core/util.js';\nexport var DATA_ZOOM_AXIS_DIMENSIONS = ['x', 'y', 'radius', 'angle', 'single'];\n// Supported coords.\n// FIXME: polar has been broken (but rarely used).\nvar SERIES_COORDS = ['cartesian2d', 'polar', 'singleAxis'];\nexport function isCoordSupported(seriesModel) {\n  var coordType = seriesModel.get('coordinateSystem');\n  return indexOf(SERIES_COORDS, coordType) >= 0;\n}\nexport function getAxisMainType(axisDim) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(axisDim);\n  }\n  return axisDim + 'Axis';\n}\nexport function getAxisIndexPropName(axisDim) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(axisDim);\n  }\n  return axisDim + 'AxisIndex';\n}\nexport function getAxisIdPropName(axisDim) {\n  if (process.env.NODE_ENV !== 'production') {\n    assert(axisDim);\n  }\n  return axisDim + 'AxisId';\n}\n/**\r\n * If two dataZoomModels has the same axis controlled, we say that they are 'linked'.\r\n * This function finds all linked dataZoomModels start from the given payload.\r\n */\nexport function findEffectedDataZooms(ecModel, payload) {\n  // Key: `DataZoomAxisDimension`\n  var axisRecords = createHashMap();\n  var effectedModels = [];\n  // Key: uid of dataZoomModel\n  var effectedModelMap = createHashMap();\n  // Find the dataZooms specified by payload.\n  ecModel.eachComponent({\n    mainType: 'dataZoom',\n    query: payload\n  }, function (dataZoomModel) {\n    if (!effectedModelMap.get(dataZoomModel.uid)) {\n      addToEffected(dataZoomModel);\n    }\n  });\n  // Start from the given dataZoomModels, travel the graph to find\n  // all of the linked dataZoom models.\n  var foundNewLink;\n  do {\n    foundNewLink = false;\n    ecModel.eachComponent('dataZoom', processSingle);\n  } while (foundNewLink);\n  function processSingle(dataZoomModel) {\n    if (!effectedModelMap.get(dataZoomModel.uid) && isLinked(dataZoomModel)) {\n      addToEffected(dataZoomModel);\n      foundNewLink = true;\n    }\n  }\n  function addToEffected(dataZoom) {\n    effectedModelMap.set(dataZoom.uid, true);\n    effectedModels.push(dataZoom);\n    markAxisControlled(dataZoom);\n  }\n  function isLinked(dataZoomModel) {\n    var isLink = false;\n    dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n      var axisIdxArr = axisRecords.get(axisDim);\n      if (axisIdxArr && axisIdxArr[axisIndex]) {\n        isLink = true;\n      }\n    });\n    return isLink;\n  }\n  function markAxisControlled(dataZoomModel) {\n    dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n      (axisRecords.get(axisDim) || axisRecords.set(axisDim, []))[axisIndex] = true;\n    });\n  }\n  return effectedModels;\n}\n/**\r\n * Find the first target coordinate system.\r\n * Available after model built.\r\n *\r\n * @return Like {\r\n *                  grid: [\r\n *                      {model: coord0, axisModels: [axis1, axis3], coordIndex: 1},\r\n *                      {model: coord1, axisModels: [axis0, axis2], coordIndex: 0},\r\n *                      ...\r\n *                  ],  // cartesians must not be null/undefined.\r\n *                  polar: [\r\n *                      {model: coord0, axisModels: [axis4], coordIndex: 0},\r\n *                      ...\r\n *                  ],  // polars must not be null/undefined.\r\n *                  singleAxis: [\r\n *                      {model: coord0, axisModels: [], coordIndex: 0}\r\n *                  ]\r\n *              }\r\n */\nexport function collectReferCoordSysModelInfo(dataZoomModel) {\n  var ecModel = dataZoomModel.ecModel;\n  var coordSysInfoWrap = {\n    infoList: [],\n    infoMap: createHashMap()\n  };\n  dataZoomModel.eachTargetAxis(function (axisDim, axisIndex) {\n    var axisModel = ecModel.getComponent(getAxisMainType(axisDim), axisIndex);\n    if (!axisModel) {\n      return;\n    }\n    var coordSysModel = axisModel.getCoordSysModel();\n    if (!coordSysModel) {\n      return;\n    }\n    var coordSysUid = coordSysModel.uid;\n    var coordSysInfo = coordSysInfoWrap.infoMap.get(coordSysUid);\n    if (!coordSysInfo) {\n      coordSysInfo = {\n        model: coordSysModel,\n        axisModels: []\n      };\n      coordSysInfoWrap.infoList.push(coordSysInfo);\n      coordSysInfoWrap.infoMap.set(coordSysUid, coordSysInfo);\n    }\n    coordSysInfo.axisModels.push(axisModel);\n  });\n  return coordSysInfoWrap;\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAO,EAAEC,aAAa,EAAEC,MAAM,QAAQ,0BAA0B;AACzE,OAAO,IAAIC,yBAAyB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;AAC9E;AACA;AACA,IAAIC,aAAa,GAAG,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,CAAC;AAC1D,OAAO,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EAC5C,IAAIC,SAAS,GAAGD,WAAW,CAACE,GAAG,CAAC,kBAAkB,CAAC;EACnD,OAAOR,OAAO,CAACI,aAAa,EAAEG,SAAS,CAAC,IAAI,CAAC;AAC/C;AACA,OAAO,SAASE,eAAeA,CAACC,OAAO,EAAE;EACvC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCX,MAAM,CAACQ,OAAO,CAAC;EACjB;EACA,OAAOA,OAAO,GAAG,MAAM;AACzB;AACA,OAAO,SAASI,oBAAoBA,CAACJ,OAAO,EAAE;EAC5C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCX,MAAM,CAACQ,OAAO,CAAC;EACjB;EACA,OAAOA,OAAO,GAAG,WAAW;AAC9B;AACA,OAAO,SAASK,iBAAiBA,CAACL,OAAO,EAAE;EACzC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCX,MAAM,CAACQ,OAAO,CAAC;EACjB;EACA,OAAOA,OAAO,GAAG,QAAQ;AAC3B;AACA;AACA;AACA;AACA;AACA,OAAO,SAASM,qBAAqBA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACtD;EACA,IAAIC,WAAW,GAAGlB,aAAa,CAAC,CAAC;EACjC,IAAImB,cAAc,GAAG,EAAE;EACvB;EACA,IAAIC,gBAAgB,GAAGpB,aAAa,CAAC,CAAC;EACtC;EACAgB,OAAO,CAACK,aAAa,CAAC;IACpBC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAEN;EACT,CAAC,EAAE,UAAUO,aAAa,EAAE;IAC1B,IAAI,CAACJ,gBAAgB,CAACb,GAAG,CAACiB,aAAa,CAACC,GAAG,CAAC,EAAE;MAC5CC,aAAa,CAACF,aAAa,CAAC;IAC9B;EACF,CAAC,CAAC;EACF;EACA;EACA,IAAIG,YAAY;EAChB,GAAG;IACDA,YAAY,GAAG,KAAK;IACpBX,OAAO,CAACK,aAAa,CAAC,UAAU,EAAEO,aAAa,CAAC;EAClD,CAAC,QAAQD,YAAY;EACrB,SAASC,aAAaA,CAACJ,aAAa,EAAE;IACpC,IAAI,CAACJ,gBAAgB,CAACb,GAAG,CAACiB,aAAa,CAACC,GAAG,CAAC,IAAII,QAAQ,CAACL,aAAa,CAAC,EAAE;MACvEE,aAAa,CAACF,aAAa,CAAC;MAC5BG,YAAY,GAAG,IAAI;IACrB;EACF;EACA,SAASD,aAAaA,CAACI,QAAQ,EAAE;IAC/BV,gBAAgB,CAACW,GAAG,CAACD,QAAQ,CAACL,GAAG,EAAE,IAAI,CAAC;IACxCN,cAAc,CAACa,IAAI,CAACF,QAAQ,CAAC;IAC7BG,kBAAkB,CAACH,QAAQ,CAAC;EAC9B;EACA,SAASD,QAAQA,CAACL,aAAa,EAAE;IAC/B,IAAIU,MAAM,GAAG,KAAK;IAClBV,aAAa,CAACW,cAAc,CAAC,UAAU1B,OAAO,EAAE2B,SAAS,EAAE;MACzD,IAAIC,UAAU,GAAGnB,WAAW,CAACX,GAAG,CAACE,OAAO,CAAC;MACzC,IAAI4B,UAAU,IAAIA,UAAU,CAACD,SAAS,CAAC,EAAE;QACvCF,MAAM,GAAG,IAAI;MACf;IACF,CAAC,CAAC;IACF,OAAOA,MAAM;EACf;EACA,SAASD,kBAAkBA,CAACT,aAAa,EAAE;IACzCA,aAAa,CAACW,cAAc,CAAC,UAAU1B,OAAO,EAAE2B,SAAS,EAAE;MACzD,CAAClB,WAAW,CAACX,GAAG,CAACE,OAAO,CAAC,IAAIS,WAAW,CAACa,GAAG,CAACtB,OAAO,EAAE,EAAE,CAAC,EAAE2B,SAAS,CAAC,GAAG,IAAI;IAC9E,CAAC,CAAC;EACJ;EACA,OAAOjB,cAAc;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASmB,6BAA6BA,CAACd,aAAa,EAAE;EAC3D,IAAIR,OAAO,GAAGQ,aAAa,CAACR,OAAO;EACnC,IAAIuB,gBAAgB,GAAG;IACrBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAEzC,aAAa,CAAC;EACzB,CAAC;EACDwB,aAAa,CAACW,cAAc,CAAC,UAAU1B,OAAO,EAAE2B,SAAS,EAAE;IACzD,IAAIM,SAAS,GAAG1B,OAAO,CAAC2B,YAAY,CAACnC,eAAe,CAACC,OAAO,CAAC,EAAE2B,SAAS,CAAC;IACzE,IAAI,CAACM,SAAS,EAAE;MACd;IACF;IACA,IAAIE,aAAa,GAAGF,SAAS,CAACG,gBAAgB,CAAC,CAAC;IAChD,IAAI,CAACD,aAAa,EAAE;MAClB;IACF;IACA,IAAIE,WAAW,GAAGF,aAAa,CAACnB,GAAG;IACnC,IAAIsB,YAAY,GAAGR,gBAAgB,CAACE,OAAO,CAAClC,GAAG,CAACuC,WAAW,CAAC;IAC5D,IAAI,CAACC,YAAY,EAAE;MACjBA,YAAY,GAAG;QACbC,KAAK,EAAEJ,aAAa;QACpBK,UAAU,EAAE;MACd,CAAC;MACDV,gBAAgB,CAACC,QAAQ,CAACR,IAAI,CAACe,YAAY,CAAC;MAC5CR,gBAAgB,CAACE,OAAO,CAACV,GAAG,CAACe,WAAW,EAAEC,YAAY,CAAC;IACzD;IACAA,YAAY,CAACE,UAAU,CAACjB,IAAI,CAACU,SAAS,CAAC;EACzC,CAAC,CAAC;EACF,OAAOH,gBAAgB;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}