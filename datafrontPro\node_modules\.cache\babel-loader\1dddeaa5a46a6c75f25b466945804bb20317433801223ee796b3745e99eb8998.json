{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar ParallelAxis = /** @class */function (_super) {\n  __extends(ParallelAxis, _super);\n  function ParallelAxis(dim, scale, coordExtent, axisType, axisIndex) {\n    var _this = _super.call(this, dim, scale, coordExtent) || this;\n    _this.type = axisType || 'value';\n    _this.axisIndex = axisIndex;\n    return _this;\n  }\n  ParallelAxis.prototype.isHorizontal = function () {\n    return this.coordinateSystem.getModel().get('layout') !== 'horizontal';\n  };\n  return ParallelAxis;\n}(Axis);\nexport default ParallelAxis;", "map": {"version": 3, "names": ["__extends", "Axis", "ParallelAxis", "_super", "dim", "scale", "coordExtent", "axisType", "axisIndex", "_this", "call", "type", "prototype", "isHorizontal", "coordinateSystem", "getModel", "get"], "sources": ["E:/AllProject/datafront/node_modules/echarts/lib/coord/parallel/ParallelAxis.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Axis from '../Axis.js';\nvar ParallelAxis = /** @class */function (_super) {\n  __extends(ParallelAxis, _super);\n  function ParallelAxis(dim, scale, coordExtent, axisType, axisIndex) {\n    var _this = _super.call(this, dim, scale, coordExtent) || this;\n    _this.type = axisType || 'value';\n    _this.axisIndex = axisIndex;\n    return _this;\n  }\n  ParallelAxis.prototype.isHorizontal = function () {\n    return this.coordinateSystem.getModel().get('layout') !== 'horizontal';\n  };\n  return ParallelAxis;\n}(Axis);\nexport default ParallelAxis;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDH,SAAS,CAACE,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAACE,GAAG,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAE;IAClE,IAAIC,KAAK,GAAGN,MAAM,CAACO,IAAI,CAAC,IAAI,EAAEN,GAAG,EAAEC,KAAK,EAAEC,WAAW,CAAC,IAAI,IAAI;IAC9DG,KAAK,CAACE,IAAI,GAAGJ,QAAQ,IAAI,OAAO;IAChCE,KAAK,CAACD,SAAS,GAAGA,SAAS;IAC3B,OAAOC,KAAK;EACd;EACAP,YAAY,CAACU,SAAS,CAACC,YAAY,GAAG,YAAY;IAChD,OAAO,IAAI,CAACC,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,GAAG,CAAC,QAAQ,CAAC,KAAK,YAAY;EACxE,CAAC;EACD,OAAOd,YAAY;AACrB,CAAC,CAACD,IAAI,CAAC;AACP,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}