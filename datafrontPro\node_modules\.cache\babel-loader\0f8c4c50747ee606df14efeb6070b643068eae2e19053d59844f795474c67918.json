{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport SymbolClz from '../helper/Symbol.js';\nimport { radialCoordinate } from './layoutHelper.js';\nimport * as bbox from 'zrender/lib/core/bbox.js';\nimport View from '../../coord/View.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport { parsePercent } from '../../util/number.js';\nimport ChartView from '../../view/Chart.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { setStatesStylesFromModel, setStatesFlag, setDefaultStateProxy, HOVER_STATE_BLUR } from '../../util/states.js';\nvar TreeEdgeShape = /** @class */function () {\n  function TreeEdgeShape() {\n    this.parentPoint = [];\n    this.childPoints = [];\n  }\n  return TreeEdgeShape;\n}();\nvar TreePath = /** @class */function (_super) {\n  __extends(TreePath, _super);\n  function TreePath(opts) {\n    return _super.call(this, opts) || this;\n  }\n  TreePath.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  TreePath.prototype.getDefaultShape = function () {\n    return new TreeEdgeShape();\n  };\n  TreePath.prototype.buildPath = function (ctx, shape) {\n    var childPoints = shape.childPoints;\n    var childLen = childPoints.length;\n    var parentPoint = shape.parentPoint;\n    var firstChildPos = childPoints[0];\n    var lastChildPos = childPoints[childLen - 1];\n    if (childLen === 1) {\n      ctx.moveTo(parentPoint[0], parentPoint[1]);\n      ctx.lineTo(firstChildPos[0], firstChildPos[1]);\n      return;\n    }\n    var orient = shape.orient;\n    var forkDim = orient === 'TB' || orient === 'BT' ? 0 : 1;\n    var otherDim = 1 - forkDim;\n    var forkPosition = parsePercent(shape.forkPosition, 1);\n    var tmpPoint = [];\n    tmpPoint[forkDim] = parentPoint[forkDim];\n    tmpPoint[otherDim] = parentPoint[otherDim] + (lastChildPos[otherDim] - parentPoint[otherDim]) * forkPosition;\n    ctx.moveTo(parentPoint[0], parentPoint[1]);\n    ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    ctx.moveTo(firstChildPos[0], firstChildPos[1]);\n    tmpPoint[forkDim] = firstChildPos[forkDim];\n    ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    tmpPoint[forkDim] = lastChildPos[forkDim];\n    ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    ctx.lineTo(lastChildPos[0], lastChildPos[1]);\n    for (var i = 1; i < childLen - 1; i++) {\n      var point = childPoints[i];\n      ctx.moveTo(point[0], point[1]);\n      tmpPoint[forkDim] = point[forkDim];\n      ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    }\n  };\n  return TreePath;\n}(Path);\nvar TreeView = /** @class */function (_super) {\n  __extends(TreeView, _super);\n  function TreeView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreeView.type;\n    _this._mainGroup = new graphic.Group();\n    return _this;\n  }\n  TreeView.prototype.init = function (ecModel, api) {\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: this.group\n    };\n    this.group.add(this._mainGroup);\n  };\n  TreeView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var layoutInfo = seriesModel.layoutInfo;\n    var group = this._mainGroup;\n    var layout = seriesModel.get('layout');\n    if (layout === 'radial') {\n      group.x = layoutInfo.x + layoutInfo.width / 2;\n      group.y = layoutInfo.y + layoutInfo.height / 2;\n    } else {\n      group.x = layoutInfo.x;\n      group.y = layoutInfo.y;\n    }\n    this._updateViewCoordSys(seriesModel, api);\n    this._updateController(seriesModel, ecModel, api);\n    var oldData = this._data;\n    data.diff(oldData).add(function (newIdx) {\n      if (symbolNeedsDraw(data, newIdx)) {\n        // Create node and edge\n        updateNode(data, newIdx, null, group, seriesModel);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      if (!symbolNeedsDraw(data, newIdx)) {\n        symbolEl && removeNode(oldData, oldIdx, symbolEl, group, seriesModel);\n        return;\n      }\n      // Update node and edge\n      updateNode(data, newIdx, symbolEl, group, seriesModel);\n    }).remove(function (oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      // When remove a collapsed node of subtree, since the collapsed\n      // node haven't been initialized with a symbol element,\n      // you can't found it's symbol element through index.\n      // so if we want to remove the symbol element we should insure\n      // that the symbol element is not null.\n      if (symbolEl) {\n        removeNode(oldData, oldIdx, symbolEl, group, seriesModel);\n      }\n    }).execute();\n    this._nodeScaleRatio = seriesModel.get('nodeScaleRatio');\n    this._updateNodeAndLinkScale(seriesModel);\n    if (seriesModel.get('expandAndCollapse') === true) {\n      data.eachItemGraphicEl(function (el, dataIndex) {\n        el.off('click').on('click', function () {\n          api.dispatchAction({\n            type: 'treeExpandAndCollapse',\n            seriesId: seriesModel.id,\n            dataIndex: dataIndex\n          });\n        });\n      });\n    }\n    this._data = data;\n  };\n  TreeView.prototype._updateViewCoordSys = function (seriesModel, api) {\n    var data = seriesModel.getData();\n    var points = [];\n    data.each(function (idx) {\n      var layout = data.getItemLayout(idx);\n      if (layout && !isNaN(layout.x) && !isNaN(layout.y)) {\n        points.push([+layout.x, +layout.y]);\n      }\n    });\n    var min = [];\n    var max = [];\n    bbox.fromPoints(points, min, max);\n    // If don't Store min max when collapse the root node after roam,\n    // the root node will disappear.\n    var oldMin = this._min;\n    var oldMax = this._max;\n    // If width or height is 0\n    if (max[0] - min[0] === 0) {\n      min[0] = oldMin ? oldMin[0] : min[0] - 1;\n      max[0] = oldMax ? oldMax[0] : max[0] + 1;\n    }\n    if (max[1] - min[1] === 0) {\n      min[1] = oldMin ? oldMin[1] : min[1] - 1;\n      max[1] = oldMax ? oldMax[1] : max[1] + 1;\n    }\n    var viewCoordSys = seriesModel.coordinateSystem = new View();\n    viewCoordSys.zoomLimit = seriesModel.get('scaleLimit');\n    viewCoordSys.setBoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    viewCoordSys.setCenter(seriesModel.get('center'), api);\n    viewCoordSys.setZoom(seriesModel.get('zoom'));\n    // Here we use viewCoordSys just for computing the 'position' and 'scale' of the group\n    this.group.attr({\n      x: viewCoordSys.x,\n      y: viewCoordSys.y,\n      scaleX: viewCoordSys.scaleX,\n      scaleY: viewCoordSys.scaleY\n    });\n    this._min = min;\n    this._max = max;\n  };\n  TreeView.prototype._updateController = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    var group = this.group;\n    controller.setPointerChecker(function (e, x, y) {\n      var rect = group.getBoundingRect();\n      rect.applyTransform(group.transform);\n      return rect.contain(x, y) && !onIrrelevantElement(e, api, seriesModel);\n    });\n    controller.enable(seriesModel.get('roam'));\n    controllerHost.zoomLimit = seriesModel.get('scaleLimit');\n    controllerHost.zoom = seriesModel.coordinateSystem.getZoom();\n    controller.off('pan').off('zoom').on('pan', function (e) {\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'treeRoam',\n        dx: e.dx,\n        dy: e.dy\n      });\n    }).on('zoom', function (e) {\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'treeRoam',\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY\n      });\n      _this._updateNodeAndLinkScale(seriesModel);\n      // Only update label layout on zoom\n      api.updateLabelLayout();\n    });\n  };\n  TreeView.prototype._updateNodeAndLinkScale = function (seriesModel) {\n    var data = seriesModel.getData();\n    var nodeScale = this._getNodeGlobalScale(seriesModel);\n    data.eachItemGraphicEl(function (el, idx) {\n      el.setSymbolScale(nodeScale);\n    });\n  };\n  TreeView.prototype._getNodeGlobalScale = function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys.type !== 'view') {\n      return 1;\n    }\n    var nodeScaleRatio = this._nodeScaleRatio;\n    var groupZoom = coordSys.scaleX || 1;\n    // Scale node when zoom changes\n    var roamZoom = coordSys.getZoom();\n    var nodeScale = (roamZoom - 1) * nodeScaleRatio + 1;\n    return nodeScale / groupZoom;\n  };\n  TreeView.prototype.dispose = function () {\n    this._controller && this._controller.dispose();\n    this._controllerHost = null;\n  };\n  TreeView.prototype.remove = function () {\n    this._mainGroup.removeAll();\n    this._data = null;\n  };\n  TreeView.type = 'tree';\n  return TreeView;\n}(ChartView);\nfunction symbolNeedsDraw(data, dataIndex) {\n  var layout = data.getItemLayout(dataIndex);\n  return layout && !isNaN(layout.x) && !isNaN(layout.y);\n}\nfunction updateNode(data, dataIndex, symbolEl, group, seriesModel) {\n  var isInit = !symbolEl;\n  var node = data.tree.getNodeByDataIndex(dataIndex);\n  var itemModel = node.getModel();\n  var visualColor = node.getVisual('style').fill;\n  var symbolInnerColor = node.isExpand === false && node.children.length !== 0 ? visualColor : '#fff';\n  var virtualRoot = data.tree.root;\n  var source = node.parentNode === virtualRoot ? node : node.parentNode || node;\n  var sourceSymbolEl = data.getItemGraphicEl(source.dataIndex);\n  var sourceLayout = source.getLayout();\n  var sourceOldLayout = sourceSymbolEl ? {\n    x: sourceSymbolEl.__oldX,\n    y: sourceSymbolEl.__oldY,\n    rawX: sourceSymbolEl.__radialOldRawX,\n    rawY: sourceSymbolEl.__radialOldRawY\n  } : sourceLayout;\n  var targetLayout = node.getLayout();\n  if (isInit) {\n    symbolEl = new SymbolClz(data, dataIndex, null, {\n      symbolInnerColor: symbolInnerColor,\n      useNameLabel: true\n    });\n    symbolEl.x = sourceOldLayout.x;\n    symbolEl.y = sourceOldLayout.y;\n  } else {\n    symbolEl.updateData(data, dataIndex, null, {\n      symbolInnerColor: symbolInnerColor,\n      useNameLabel: true\n    });\n  }\n  symbolEl.__radialOldRawX = symbolEl.__radialRawX;\n  symbolEl.__radialOldRawY = symbolEl.__radialRawY;\n  symbolEl.__radialRawX = targetLayout.rawX;\n  symbolEl.__radialRawY = targetLayout.rawY;\n  group.add(symbolEl);\n  data.setItemGraphicEl(dataIndex, symbolEl);\n  symbolEl.__oldX = symbolEl.x;\n  symbolEl.__oldY = symbolEl.y;\n  graphic.updateProps(symbolEl, {\n    x: targetLayout.x,\n    y: targetLayout.y\n  }, seriesModel);\n  var symbolPath = symbolEl.getSymbolPath();\n  if (seriesModel.get('layout') === 'radial') {\n    var realRoot = virtualRoot.children[0];\n    var rootLayout = realRoot.getLayout();\n    var length_1 = realRoot.children.length;\n    var rad = void 0;\n    var isLeft = void 0;\n    if (targetLayout.x === rootLayout.x && node.isExpand === true && realRoot.children.length) {\n      var center = {\n        x: (realRoot.children[0].getLayout().x + realRoot.children[length_1 - 1].getLayout().x) / 2,\n        y: (realRoot.children[0].getLayout().y + realRoot.children[length_1 - 1].getLayout().y) / 2\n      };\n      rad = Math.atan2(center.y - rootLayout.y, center.x - rootLayout.x);\n      if (rad < 0) {\n        rad = Math.PI * 2 + rad;\n      }\n      isLeft = center.x < rootLayout.x;\n      if (isLeft) {\n        rad = rad - Math.PI;\n      }\n    } else {\n      rad = Math.atan2(targetLayout.y - rootLayout.y, targetLayout.x - rootLayout.x);\n      if (rad < 0) {\n        rad = Math.PI * 2 + rad;\n      }\n      if (node.children.length === 0 || node.children.length !== 0 && node.isExpand === false) {\n        isLeft = targetLayout.x < rootLayout.x;\n        if (isLeft) {\n          rad = rad - Math.PI;\n        }\n      } else {\n        isLeft = targetLayout.x > rootLayout.x;\n        if (!isLeft) {\n          rad = rad - Math.PI;\n        }\n      }\n    }\n    var textPosition = isLeft ? 'left' : 'right';\n    var normalLabelModel = itemModel.getModel('label');\n    var rotate = normalLabelModel.get('rotate');\n    var labelRotateRadian = rotate * (Math.PI / 180);\n    var textContent = symbolPath.getTextContent();\n    if (textContent) {\n      symbolPath.setTextConfig({\n        position: normalLabelModel.get('position') || textPosition,\n        rotation: rotate == null ? -rad : labelRotateRadian,\n        origin: 'center'\n      });\n      textContent.setStyle('verticalAlign', 'middle');\n    }\n  }\n  // Handle status\n  var focus = itemModel.get(['emphasis', 'focus']);\n  var focusDataIndices = focus === 'relative' ? zrUtil.concatArray(node.getAncestorsIndices(), node.getDescendantIndices()) : focus === 'ancestor' ? node.getAncestorsIndices() : focus === 'descendant' ? node.getDescendantIndices() : null;\n  if (focusDataIndices) {\n    // Modify the focus to data indices.\n    getECData(symbolEl).focus = focusDataIndices;\n  }\n  drawEdge(seriesModel, node, virtualRoot, symbolEl, sourceOldLayout, sourceLayout, targetLayout, group);\n  if (symbolEl.__edge) {\n    symbolEl.onHoverStateChange = function (toState) {\n      if (toState !== 'blur') {\n        // NOTE: Ensure the parent elements will been blurred firstly.\n        // According to the return of getAncestorsIndices and getDescendantIndices\n        // TODO: A bit tricky.\n        var parentEl = node.parentNode && data.getItemGraphicEl(node.parentNode.dataIndex);\n        if (!(parentEl && parentEl.hoverState === HOVER_STATE_BLUR)) {\n          setStatesFlag(symbolEl.__edge, toState);\n        }\n      }\n    };\n  }\n}\nfunction drawEdge(seriesModel, node, virtualRoot, symbolEl, sourceOldLayout, sourceLayout, targetLayout, group) {\n  var itemModel = node.getModel();\n  var edgeShape = seriesModel.get('edgeShape');\n  var layout = seriesModel.get('layout');\n  var orient = seriesModel.getOrient();\n  var curvature = seriesModel.get(['lineStyle', 'curveness']);\n  var edgeForkPosition = seriesModel.get('edgeForkPosition');\n  var lineStyle = itemModel.getModel('lineStyle').getLineStyle();\n  var edge = symbolEl.__edge;\n  // curve edge from node -> parent\n  // polyline edge from node -> children\n  if (edgeShape === 'curve') {\n    if (node.parentNode && node.parentNode !== virtualRoot) {\n      if (!edge) {\n        edge = symbolEl.__edge = new graphic.BezierCurve({\n          shape: getEdgeShape(layout, orient, curvature, sourceOldLayout, sourceOldLayout)\n        });\n      }\n      graphic.updateProps(edge, {\n        shape: getEdgeShape(layout, orient, curvature, sourceLayout, targetLayout)\n      }, seriesModel);\n    }\n  } else if (edgeShape === 'polyline') {\n    if (layout === 'orthogonal') {\n      if (node !== virtualRoot && node.children && node.children.length !== 0 && node.isExpand === true) {\n        var children = node.children;\n        var childPoints = [];\n        for (var i = 0; i < children.length; i++) {\n          var childLayout = children[i].getLayout();\n          childPoints.push([childLayout.x, childLayout.y]);\n        }\n        if (!edge) {\n          edge = symbolEl.__edge = new TreePath({\n            shape: {\n              parentPoint: [targetLayout.x, targetLayout.y],\n              childPoints: [[targetLayout.x, targetLayout.y]],\n              orient: orient,\n              forkPosition: edgeForkPosition\n            }\n          });\n        }\n        graphic.updateProps(edge, {\n          shape: {\n            parentPoint: [targetLayout.x, targetLayout.y],\n            childPoints: childPoints\n          }\n        }, seriesModel);\n      }\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error('The polyline edgeShape can only be used in orthogonal layout');\n      }\n    }\n  }\n  // show all edge when edgeShape is 'curve', filter node `isExpand` is false when edgeShape is 'polyline'\n  if (edge && !(edgeShape === 'polyline' && !node.isExpand)) {\n    edge.useStyle(zrUtil.defaults({\n      strokeNoScale: true,\n      fill: null\n    }, lineStyle));\n    setStatesStylesFromModel(edge, itemModel, 'lineStyle');\n    setDefaultStateProxy(edge);\n    group.add(edge);\n  }\n}\nfunction removeNodeEdge(node, data, group, seriesModel, removeAnimationOpt) {\n  var virtualRoot = data.tree.root;\n  var _a = getSourceNode(virtualRoot, node),\n    source = _a.source,\n    sourceLayout = _a.sourceLayout;\n  var symbolEl = data.getItemGraphicEl(node.dataIndex);\n  if (!symbolEl) {\n    return;\n  }\n  var sourceSymbolEl = data.getItemGraphicEl(source.dataIndex);\n  var sourceEdge = sourceSymbolEl.__edge;\n  // 1. when expand the sub tree, delete the children node should delete the edge of\n  // the source at the same time. because the polyline edge shape is only owned by the source.\n  // 2.when the node is the only children of the source, delete the node should delete the edge of\n  // the source at the same time. the same reason as above.\n  var edge = symbolEl.__edge || (source.isExpand === false || source.children.length === 1 ? sourceEdge : undefined);\n  var edgeShape = seriesModel.get('edgeShape');\n  var layoutOpt = seriesModel.get('layout');\n  var orient = seriesModel.get('orient');\n  var curvature = seriesModel.get(['lineStyle', 'curveness']);\n  if (edge) {\n    if (edgeShape === 'curve') {\n      graphic.removeElement(edge, {\n        shape: getEdgeShape(layoutOpt, orient, curvature, sourceLayout, sourceLayout),\n        style: {\n          opacity: 0\n        }\n      }, seriesModel, {\n        cb: function () {\n          group.remove(edge);\n        },\n        removeOpt: removeAnimationOpt\n      });\n    } else if (edgeShape === 'polyline' && seriesModel.get('layout') === 'orthogonal') {\n      graphic.removeElement(edge, {\n        shape: {\n          parentPoint: [sourceLayout.x, sourceLayout.y],\n          childPoints: [[sourceLayout.x, sourceLayout.y]]\n        },\n        style: {\n          opacity: 0\n        }\n      }, seriesModel, {\n        cb: function () {\n          group.remove(edge);\n        },\n        removeOpt: removeAnimationOpt\n      });\n    }\n  }\n}\nfunction getSourceNode(virtualRoot, node) {\n  var source = node.parentNode === virtualRoot ? node : node.parentNode || node;\n  var sourceLayout;\n  while (sourceLayout = source.getLayout(), sourceLayout == null) {\n    source = source.parentNode === virtualRoot ? source : source.parentNode || source;\n  }\n  return {\n    source: source,\n    sourceLayout: sourceLayout\n  };\n}\nfunction removeNode(data, dataIndex, symbolEl, group, seriesModel) {\n  var node = data.tree.getNodeByDataIndex(dataIndex);\n  var virtualRoot = data.tree.root;\n  var sourceLayout = getSourceNode(virtualRoot, node).sourceLayout;\n  // Use same duration and easing with update to have more consistent animation.\n  var removeAnimationOpt = {\n    duration: seriesModel.get('animationDurationUpdate'),\n    easing: seriesModel.get('animationEasingUpdate')\n  };\n  graphic.removeElement(symbolEl, {\n    x: sourceLayout.x + 1,\n    y: sourceLayout.y + 1\n  }, seriesModel, {\n    cb: function () {\n      group.remove(symbolEl);\n      data.setItemGraphicEl(dataIndex, null);\n    },\n    removeOpt: removeAnimationOpt\n  });\n  symbolEl.fadeOut(null, data.hostModel, {\n    fadeLabel: true,\n    animation: removeAnimationOpt\n  });\n  // remove edge as parent node\n  node.children.forEach(function (childNode) {\n    removeNodeEdge(childNode, data, group, seriesModel, removeAnimationOpt);\n  });\n  // remove edge as child node\n  removeNodeEdge(node, data, group, seriesModel, removeAnimationOpt);\n}\nfunction getEdgeShape(layoutOpt, orient, curvature, sourceLayout, targetLayout) {\n  var cpx1;\n  var cpy1;\n  var cpx2;\n  var cpy2;\n  var x1;\n  var x2;\n  var y1;\n  var y2;\n  if (layoutOpt === 'radial') {\n    x1 = sourceLayout.rawX;\n    y1 = sourceLayout.rawY;\n    x2 = targetLayout.rawX;\n    y2 = targetLayout.rawY;\n    var radialCoor1 = radialCoordinate(x1, y1);\n    var radialCoor2 = radialCoordinate(x1, y1 + (y2 - y1) * curvature);\n    var radialCoor3 = radialCoordinate(x2, y2 + (y1 - y2) * curvature);\n    var radialCoor4 = radialCoordinate(x2, y2);\n    return {\n      x1: radialCoor1.x || 0,\n      y1: radialCoor1.y || 0,\n      x2: radialCoor4.x || 0,\n      y2: radialCoor4.y || 0,\n      cpx1: radialCoor2.x || 0,\n      cpy1: radialCoor2.y || 0,\n      cpx2: radialCoor3.x || 0,\n      cpy2: radialCoor3.y || 0\n    };\n  } else {\n    x1 = sourceLayout.x;\n    y1 = sourceLayout.y;\n    x2 = targetLayout.x;\n    y2 = targetLayout.y;\n    if (orient === 'LR' || orient === 'RL') {\n      cpx1 = x1 + (x2 - x1) * curvature;\n      cpy1 = y1;\n      cpx2 = x2 + (x1 - x2) * curvature;\n      cpy2 = y2;\n    }\n    if (orient === 'TB' || orient === 'BT') {\n      cpx1 = x1;\n      cpy1 = y1 + (y2 - y1) * curvature;\n      cpx2 = x2;\n      cpy2 = y2 + (y1 - y2) * curvature;\n    }\n  }\n  return {\n    x1: x1,\n    y1: y1,\n    x2: x2,\n    y2: y2,\n    cpx1: cpx1,\n    cpy1: cpy1,\n    cpx2: cpx2,\n    cpy2: cpy2\n  };\n}\nexport default TreeView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "graphic", "getECData", "SymbolClz", "radialCoordinate", "bbox", "View", "roamHelper", "RoamController", "onIrrelevantElement", "parsePercent", "ChartView", "Path", "setStatesStylesFromModel", "setStatesFlag", "setDefaultStateProxy", "HOVER_STATE_BLUR", "TreeEdgeShape", "parentPoint", "childPoints", "TreePath", "_super", "opts", "call", "prototype", "getDefaultStyle", "stroke", "fill", "getDefaultShape", "buildPath", "ctx", "shape", "child<PERSON>en", "length", "firstChildPos", "last<PERSON><PERSON>dPos", "moveTo", "lineTo", "orient", "forkDim", "otherDim", "forkPosition", "tmpPoint", "i", "point", "TreeView", "_this", "apply", "arguments", "type", "_mainGroup", "Group", "init", "ecModel", "api", "_controller", "getZr", "_controllerHost", "target", "group", "add", "render", "seriesModel", "data", "getData", "layoutInfo", "layout", "get", "x", "width", "y", "height", "_updateViewCoordSys", "_updateController", "oldData", "_data", "diff", "newIdx", "symbolNeedsDraw", "updateNode", "update", "oldIdx", "symbolEl", "getItemGraphicEl", "removeNode", "remove", "execute", "_nodeScaleRatio", "_updateNodeAndLinkScale", "eachItemGraphicEl", "el", "dataIndex", "off", "on", "dispatchAction", "seriesId", "id", "points", "each", "idx", "getItemLayout", "isNaN", "push", "min", "max", "fromPoints", "old<PERSON>in", "_min", "oldMax", "_max", "viewCoordSys", "coordinateSystem", "zoomLimit", "setBoundingRect", "setCenter", "setZoom", "attr", "scaleX", "scaleY", "controller", "controllerHost", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "rect", "getBoundingRect", "applyTransform", "transform", "contain", "enable", "zoom", "getZoom", "updateViewOnPan", "dx", "dy", "updateViewOnZoom", "scale", "originX", "originY", "updateLabelLayout", "nodeScale", "_getNodeGlobalScale", "setSymbolScale", "coordSys", "nodeScaleRatio", "groupZoom", "roamZoom", "dispose", "removeAll", "isInit", "node", "tree", "getNodeByDataIndex", "itemModel", "getModel", "visualColor", "getVisual", "symbolInnerColor", "isExpand", "children", "virtualRoot", "root", "source", "parentNode", "sourceSymbolEl", "sourceLayout", "getLayout", "sourceOldLayout", "__oldX", "__oldY", "rawX", "__radialOldRawX", "rawY", "__radialOldRawY", "targetLayout", "useNameLabel", "updateData", "__radialRawX", "__radialRawY", "setItemGraphicEl", "updateProps", "symbolPath", "getSymbolPath", "realRoot", "rootLayout", "length_1", "rad", "isLeft", "center", "Math", "atan2", "PI", "textPosition", "normalLabelModel", "rotate", "labelRotateRadian", "textContent", "getTextContent", "setTextConfig", "position", "rotation", "origin", "setStyle", "focus", "focusDataIndices", "concatArray", "getAncestorsIndices", "getDescendantIndices", "drawEdge", "__edge", "onHoverStateChange", "toState", "parentEl", "hoverState", "edgeShape", "getOrient", "curvature", "edgeForkPosition", "lineStyle", "getLineStyle", "edge", "BezierCurve", "getEdgeShape", "childLayout", "process", "env", "NODE_ENV", "Error", "useStyle", "defaults", "strokeNoScale", "removeNodeEdge", "removeAnimationOpt", "_a", "getSourceNode", "sourceEdge", "undefined", "layoutOpt", "removeElement", "style", "opacity", "cb", "removeOpt", "duration", "easing", "fadeOut", "hostModel", "<PERSON><PERSON><PERSON><PERSON>", "animation", "for<PERSON>ach", "childNode", "cpx1", "cpy1", "cpx2", "cpy2", "x1", "x2", "y1", "y2", "radialCoor1", "radialCoor2", "radialCoor3", "radialCoor4"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/chart/tree/TreeView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport SymbolClz from '../helper/Symbol.js';\nimport { radialCoordinate } from './layoutHelper.js';\nimport * as bbox from 'zrender/lib/core/bbox.js';\nimport View from '../../coord/View.js';\nimport * as roamHelper from '../../component/helper/roamHelper.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport { onIrrelevantElement } from '../../component/helper/cursorHelper.js';\nimport { parsePercent } from '../../util/number.js';\nimport ChartView from '../../view/Chart.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { setStatesStylesFromModel, setStatesFlag, setDefaultStateProxy, HOVER_STATE_BLUR } from '../../util/states.js';\nvar TreeEdgeShape = /** @class */function () {\n  function TreeEdgeShape() {\n    this.parentPoint = [];\n    this.childPoints = [];\n  }\n  return TreeEdgeShape;\n}();\nvar TreePath = /** @class */function (_super) {\n  __extends(TreePath, _super);\n  function TreePath(opts) {\n    return _super.call(this, opts) || this;\n  }\n  TreePath.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  TreePath.prototype.getDefaultShape = function () {\n    return new TreeEdgeShape();\n  };\n  TreePath.prototype.buildPath = function (ctx, shape) {\n    var childPoints = shape.childPoints;\n    var childLen = childPoints.length;\n    var parentPoint = shape.parentPoint;\n    var firstChildPos = childPoints[0];\n    var lastChildPos = childPoints[childLen - 1];\n    if (childLen === 1) {\n      ctx.moveTo(parentPoint[0], parentPoint[1]);\n      ctx.lineTo(firstChildPos[0], firstChildPos[1]);\n      return;\n    }\n    var orient = shape.orient;\n    var forkDim = orient === 'TB' || orient === 'BT' ? 0 : 1;\n    var otherDim = 1 - forkDim;\n    var forkPosition = parsePercent(shape.forkPosition, 1);\n    var tmpPoint = [];\n    tmpPoint[forkDim] = parentPoint[forkDim];\n    tmpPoint[otherDim] = parentPoint[otherDim] + (lastChildPos[otherDim] - parentPoint[otherDim]) * forkPosition;\n    ctx.moveTo(parentPoint[0], parentPoint[1]);\n    ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    ctx.moveTo(firstChildPos[0], firstChildPos[1]);\n    tmpPoint[forkDim] = firstChildPos[forkDim];\n    ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    tmpPoint[forkDim] = lastChildPos[forkDim];\n    ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    ctx.lineTo(lastChildPos[0], lastChildPos[1]);\n    for (var i = 1; i < childLen - 1; i++) {\n      var point = childPoints[i];\n      ctx.moveTo(point[0], point[1]);\n      tmpPoint[forkDim] = point[forkDim];\n      ctx.lineTo(tmpPoint[0], tmpPoint[1]);\n    }\n  };\n  return TreePath;\n}(Path);\nvar TreeView = /** @class */function (_super) {\n  __extends(TreeView, _super);\n  function TreeView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreeView.type;\n    _this._mainGroup = new graphic.Group();\n    return _this;\n  }\n  TreeView.prototype.init = function (ecModel, api) {\n    this._controller = new RoamController(api.getZr());\n    this._controllerHost = {\n      target: this.group\n    };\n    this.group.add(this._mainGroup);\n  };\n  TreeView.prototype.render = function (seriesModel, ecModel, api) {\n    var data = seriesModel.getData();\n    var layoutInfo = seriesModel.layoutInfo;\n    var group = this._mainGroup;\n    var layout = seriesModel.get('layout');\n    if (layout === 'radial') {\n      group.x = layoutInfo.x + layoutInfo.width / 2;\n      group.y = layoutInfo.y + layoutInfo.height / 2;\n    } else {\n      group.x = layoutInfo.x;\n      group.y = layoutInfo.y;\n    }\n    this._updateViewCoordSys(seriesModel, api);\n    this._updateController(seriesModel, ecModel, api);\n    var oldData = this._data;\n    data.diff(oldData).add(function (newIdx) {\n      if (symbolNeedsDraw(data, newIdx)) {\n        // Create node and edge\n        updateNode(data, newIdx, null, group, seriesModel);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      if (!symbolNeedsDraw(data, newIdx)) {\n        symbolEl && removeNode(oldData, oldIdx, symbolEl, group, seriesModel);\n        return;\n      }\n      // Update node and edge\n      updateNode(data, newIdx, symbolEl, group, seriesModel);\n    }).remove(function (oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      // When remove a collapsed node of subtree, since the collapsed\n      // node haven't been initialized with a symbol element,\n      // you can't found it's symbol element through index.\n      // so if we want to remove the symbol element we should insure\n      // that the symbol element is not null.\n      if (symbolEl) {\n        removeNode(oldData, oldIdx, symbolEl, group, seriesModel);\n      }\n    }).execute();\n    this._nodeScaleRatio = seriesModel.get('nodeScaleRatio');\n    this._updateNodeAndLinkScale(seriesModel);\n    if (seriesModel.get('expandAndCollapse') === true) {\n      data.eachItemGraphicEl(function (el, dataIndex) {\n        el.off('click').on('click', function () {\n          api.dispatchAction({\n            type: 'treeExpandAndCollapse',\n            seriesId: seriesModel.id,\n            dataIndex: dataIndex\n          });\n        });\n      });\n    }\n    this._data = data;\n  };\n  TreeView.prototype._updateViewCoordSys = function (seriesModel, api) {\n    var data = seriesModel.getData();\n    var points = [];\n    data.each(function (idx) {\n      var layout = data.getItemLayout(idx);\n      if (layout && !isNaN(layout.x) && !isNaN(layout.y)) {\n        points.push([+layout.x, +layout.y]);\n      }\n    });\n    var min = [];\n    var max = [];\n    bbox.fromPoints(points, min, max);\n    // If don't Store min max when collapse the root node after roam,\n    // the root node will disappear.\n    var oldMin = this._min;\n    var oldMax = this._max;\n    // If width or height is 0\n    if (max[0] - min[0] === 0) {\n      min[0] = oldMin ? oldMin[0] : min[0] - 1;\n      max[0] = oldMax ? oldMax[0] : max[0] + 1;\n    }\n    if (max[1] - min[1] === 0) {\n      min[1] = oldMin ? oldMin[1] : min[1] - 1;\n      max[1] = oldMax ? oldMax[1] : max[1] + 1;\n    }\n    var viewCoordSys = seriesModel.coordinateSystem = new View();\n    viewCoordSys.zoomLimit = seriesModel.get('scaleLimit');\n    viewCoordSys.setBoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n    viewCoordSys.setCenter(seriesModel.get('center'), api);\n    viewCoordSys.setZoom(seriesModel.get('zoom'));\n    // Here we use viewCoordSys just for computing the 'position' and 'scale' of the group\n    this.group.attr({\n      x: viewCoordSys.x,\n      y: viewCoordSys.y,\n      scaleX: viewCoordSys.scaleX,\n      scaleY: viewCoordSys.scaleY\n    });\n    this._min = min;\n    this._max = max;\n  };\n  TreeView.prototype._updateController = function (seriesModel, ecModel, api) {\n    var _this = this;\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    var group = this.group;\n    controller.setPointerChecker(function (e, x, y) {\n      var rect = group.getBoundingRect();\n      rect.applyTransform(group.transform);\n      return rect.contain(x, y) && !onIrrelevantElement(e, api, seriesModel);\n    });\n    controller.enable(seriesModel.get('roam'));\n    controllerHost.zoomLimit = seriesModel.get('scaleLimit');\n    controllerHost.zoom = seriesModel.coordinateSystem.getZoom();\n    controller.off('pan').off('zoom').on('pan', function (e) {\n      roamHelper.updateViewOnPan(controllerHost, e.dx, e.dy);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'treeRoam',\n        dx: e.dx,\n        dy: e.dy\n      });\n    }).on('zoom', function (e) {\n      roamHelper.updateViewOnZoom(controllerHost, e.scale, e.originX, e.originY);\n      api.dispatchAction({\n        seriesId: seriesModel.id,\n        type: 'treeRoam',\n        zoom: e.scale,\n        originX: e.originX,\n        originY: e.originY\n      });\n      _this._updateNodeAndLinkScale(seriesModel);\n      // Only update label layout on zoom\n      api.updateLabelLayout();\n    });\n  };\n  TreeView.prototype._updateNodeAndLinkScale = function (seriesModel) {\n    var data = seriesModel.getData();\n    var nodeScale = this._getNodeGlobalScale(seriesModel);\n    data.eachItemGraphicEl(function (el, idx) {\n      el.setSymbolScale(nodeScale);\n    });\n  };\n  TreeView.prototype._getNodeGlobalScale = function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys.type !== 'view') {\n      return 1;\n    }\n    var nodeScaleRatio = this._nodeScaleRatio;\n    var groupZoom = coordSys.scaleX || 1;\n    // Scale node when zoom changes\n    var roamZoom = coordSys.getZoom();\n    var nodeScale = (roamZoom - 1) * nodeScaleRatio + 1;\n    return nodeScale / groupZoom;\n  };\n  TreeView.prototype.dispose = function () {\n    this._controller && this._controller.dispose();\n    this._controllerHost = null;\n  };\n  TreeView.prototype.remove = function () {\n    this._mainGroup.removeAll();\n    this._data = null;\n  };\n  TreeView.type = 'tree';\n  return TreeView;\n}(ChartView);\nfunction symbolNeedsDraw(data, dataIndex) {\n  var layout = data.getItemLayout(dataIndex);\n  return layout && !isNaN(layout.x) && !isNaN(layout.y);\n}\nfunction updateNode(data, dataIndex, symbolEl, group, seriesModel) {\n  var isInit = !symbolEl;\n  var node = data.tree.getNodeByDataIndex(dataIndex);\n  var itemModel = node.getModel();\n  var visualColor = node.getVisual('style').fill;\n  var symbolInnerColor = node.isExpand === false && node.children.length !== 0 ? visualColor : '#fff';\n  var virtualRoot = data.tree.root;\n  var source = node.parentNode === virtualRoot ? node : node.parentNode || node;\n  var sourceSymbolEl = data.getItemGraphicEl(source.dataIndex);\n  var sourceLayout = source.getLayout();\n  var sourceOldLayout = sourceSymbolEl ? {\n    x: sourceSymbolEl.__oldX,\n    y: sourceSymbolEl.__oldY,\n    rawX: sourceSymbolEl.__radialOldRawX,\n    rawY: sourceSymbolEl.__radialOldRawY\n  } : sourceLayout;\n  var targetLayout = node.getLayout();\n  if (isInit) {\n    symbolEl = new SymbolClz(data, dataIndex, null, {\n      symbolInnerColor: symbolInnerColor,\n      useNameLabel: true\n    });\n    symbolEl.x = sourceOldLayout.x;\n    symbolEl.y = sourceOldLayout.y;\n  } else {\n    symbolEl.updateData(data, dataIndex, null, {\n      symbolInnerColor: symbolInnerColor,\n      useNameLabel: true\n    });\n  }\n  symbolEl.__radialOldRawX = symbolEl.__radialRawX;\n  symbolEl.__radialOldRawY = symbolEl.__radialRawY;\n  symbolEl.__radialRawX = targetLayout.rawX;\n  symbolEl.__radialRawY = targetLayout.rawY;\n  group.add(symbolEl);\n  data.setItemGraphicEl(dataIndex, symbolEl);\n  symbolEl.__oldX = symbolEl.x;\n  symbolEl.__oldY = symbolEl.y;\n  graphic.updateProps(symbolEl, {\n    x: targetLayout.x,\n    y: targetLayout.y\n  }, seriesModel);\n  var symbolPath = symbolEl.getSymbolPath();\n  if (seriesModel.get('layout') === 'radial') {\n    var realRoot = virtualRoot.children[0];\n    var rootLayout = realRoot.getLayout();\n    var length_1 = realRoot.children.length;\n    var rad = void 0;\n    var isLeft = void 0;\n    if (targetLayout.x === rootLayout.x && node.isExpand === true && realRoot.children.length) {\n      var center = {\n        x: (realRoot.children[0].getLayout().x + realRoot.children[length_1 - 1].getLayout().x) / 2,\n        y: (realRoot.children[0].getLayout().y + realRoot.children[length_1 - 1].getLayout().y) / 2\n      };\n      rad = Math.atan2(center.y - rootLayout.y, center.x - rootLayout.x);\n      if (rad < 0) {\n        rad = Math.PI * 2 + rad;\n      }\n      isLeft = center.x < rootLayout.x;\n      if (isLeft) {\n        rad = rad - Math.PI;\n      }\n    } else {\n      rad = Math.atan2(targetLayout.y - rootLayout.y, targetLayout.x - rootLayout.x);\n      if (rad < 0) {\n        rad = Math.PI * 2 + rad;\n      }\n      if (node.children.length === 0 || node.children.length !== 0 && node.isExpand === false) {\n        isLeft = targetLayout.x < rootLayout.x;\n        if (isLeft) {\n          rad = rad - Math.PI;\n        }\n      } else {\n        isLeft = targetLayout.x > rootLayout.x;\n        if (!isLeft) {\n          rad = rad - Math.PI;\n        }\n      }\n    }\n    var textPosition = isLeft ? 'left' : 'right';\n    var normalLabelModel = itemModel.getModel('label');\n    var rotate = normalLabelModel.get('rotate');\n    var labelRotateRadian = rotate * (Math.PI / 180);\n    var textContent = symbolPath.getTextContent();\n    if (textContent) {\n      symbolPath.setTextConfig({\n        position: normalLabelModel.get('position') || textPosition,\n        rotation: rotate == null ? -rad : labelRotateRadian,\n        origin: 'center'\n      });\n      textContent.setStyle('verticalAlign', 'middle');\n    }\n  }\n  // Handle status\n  var focus = itemModel.get(['emphasis', 'focus']);\n  var focusDataIndices = focus === 'relative' ? zrUtil.concatArray(node.getAncestorsIndices(), node.getDescendantIndices()) : focus === 'ancestor' ? node.getAncestorsIndices() : focus === 'descendant' ? node.getDescendantIndices() : null;\n  if (focusDataIndices) {\n    // Modify the focus to data indices.\n    getECData(symbolEl).focus = focusDataIndices;\n  }\n  drawEdge(seriesModel, node, virtualRoot, symbolEl, sourceOldLayout, sourceLayout, targetLayout, group);\n  if (symbolEl.__edge) {\n    symbolEl.onHoverStateChange = function (toState) {\n      if (toState !== 'blur') {\n        // NOTE: Ensure the parent elements will been blurred firstly.\n        // According to the return of getAncestorsIndices and getDescendantIndices\n        // TODO: A bit tricky.\n        var parentEl = node.parentNode && data.getItemGraphicEl(node.parentNode.dataIndex);\n        if (!(parentEl && parentEl.hoverState === HOVER_STATE_BLUR)) {\n          setStatesFlag(symbolEl.__edge, toState);\n        }\n      }\n    };\n  }\n}\nfunction drawEdge(seriesModel, node, virtualRoot, symbolEl, sourceOldLayout, sourceLayout, targetLayout, group) {\n  var itemModel = node.getModel();\n  var edgeShape = seriesModel.get('edgeShape');\n  var layout = seriesModel.get('layout');\n  var orient = seriesModel.getOrient();\n  var curvature = seriesModel.get(['lineStyle', 'curveness']);\n  var edgeForkPosition = seriesModel.get('edgeForkPosition');\n  var lineStyle = itemModel.getModel('lineStyle').getLineStyle();\n  var edge = symbolEl.__edge;\n  // curve edge from node -> parent\n  // polyline edge from node -> children\n  if (edgeShape === 'curve') {\n    if (node.parentNode && node.parentNode !== virtualRoot) {\n      if (!edge) {\n        edge = symbolEl.__edge = new graphic.BezierCurve({\n          shape: getEdgeShape(layout, orient, curvature, sourceOldLayout, sourceOldLayout)\n        });\n      }\n      graphic.updateProps(edge, {\n        shape: getEdgeShape(layout, orient, curvature, sourceLayout, targetLayout)\n      }, seriesModel);\n    }\n  } else if (edgeShape === 'polyline') {\n    if (layout === 'orthogonal') {\n      if (node !== virtualRoot && node.children && node.children.length !== 0 && node.isExpand === true) {\n        var children = node.children;\n        var childPoints = [];\n        for (var i = 0; i < children.length; i++) {\n          var childLayout = children[i].getLayout();\n          childPoints.push([childLayout.x, childLayout.y]);\n        }\n        if (!edge) {\n          edge = symbolEl.__edge = new TreePath({\n            shape: {\n              parentPoint: [targetLayout.x, targetLayout.y],\n              childPoints: [[targetLayout.x, targetLayout.y]],\n              orient: orient,\n              forkPosition: edgeForkPosition\n            }\n          });\n        }\n        graphic.updateProps(edge, {\n          shape: {\n            parentPoint: [targetLayout.x, targetLayout.y],\n            childPoints: childPoints\n          }\n        }, seriesModel);\n      }\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error('The polyline edgeShape can only be used in orthogonal layout');\n      }\n    }\n  }\n  // show all edge when edgeShape is 'curve', filter node `isExpand` is false when edgeShape is 'polyline'\n  if (edge && !(edgeShape === 'polyline' && !node.isExpand)) {\n    edge.useStyle(zrUtil.defaults({\n      strokeNoScale: true,\n      fill: null\n    }, lineStyle));\n    setStatesStylesFromModel(edge, itemModel, 'lineStyle');\n    setDefaultStateProxy(edge);\n    group.add(edge);\n  }\n}\nfunction removeNodeEdge(node, data, group, seriesModel, removeAnimationOpt) {\n  var virtualRoot = data.tree.root;\n  var _a = getSourceNode(virtualRoot, node),\n    source = _a.source,\n    sourceLayout = _a.sourceLayout;\n  var symbolEl = data.getItemGraphicEl(node.dataIndex);\n  if (!symbolEl) {\n    return;\n  }\n  var sourceSymbolEl = data.getItemGraphicEl(source.dataIndex);\n  var sourceEdge = sourceSymbolEl.__edge;\n  // 1. when expand the sub tree, delete the children node should delete the edge of\n  // the source at the same time. because the polyline edge shape is only owned by the source.\n  // 2.when the node is the only children of the source, delete the node should delete the edge of\n  // the source at the same time. the same reason as above.\n  var edge = symbolEl.__edge || (source.isExpand === false || source.children.length === 1 ? sourceEdge : undefined);\n  var edgeShape = seriesModel.get('edgeShape');\n  var layoutOpt = seriesModel.get('layout');\n  var orient = seriesModel.get('orient');\n  var curvature = seriesModel.get(['lineStyle', 'curveness']);\n  if (edge) {\n    if (edgeShape === 'curve') {\n      graphic.removeElement(edge, {\n        shape: getEdgeShape(layoutOpt, orient, curvature, sourceLayout, sourceLayout),\n        style: {\n          opacity: 0\n        }\n      }, seriesModel, {\n        cb: function () {\n          group.remove(edge);\n        },\n        removeOpt: removeAnimationOpt\n      });\n    } else if (edgeShape === 'polyline' && seriesModel.get('layout') === 'orthogonal') {\n      graphic.removeElement(edge, {\n        shape: {\n          parentPoint: [sourceLayout.x, sourceLayout.y],\n          childPoints: [[sourceLayout.x, sourceLayout.y]]\n        },\n        style: {\n          opacity: 0\n        }\n      }, seriesModel, {\n        cb: function () {\n          group.remove(edge);\n        },\n        removeOpt: removeAnimationOpt\n      });\n    }\n  }\n}\nfunction getSourceNode(virtualRoot, node) {\n  var source = node.parentNode === virtualRoot ? node : node.parentNode || node;\n  var sourceLayout;\n  while (sourceLayout = source.getLayout(), sourceLayout == null) {\n    source = source.parentNode === virtualRoot ? source : source.parentNode || source;\n  }\n  return {\n    source: source,\n    sourceLayout: sourceLayout\n  };\n}\nfunction removeNode(data, dataIndex, symbolEl, group, seriesModel) {\n  var node = data.tree.getNodeByDataIndex(dataIndex);\n  var virtualRoot = data.tree.root;\n  var sourceLayout = getSourceNode(virtualRoot, node).sourceLayout;\n  // Use same duration and easing with update to have more consistent animation.\n  var removeAnimationOpt = {\n    duration: seriesModel.get('animationDurationUpdate'),\n    easing: seriesModel.get('animationEasingUpdate')\n  };\n  graphic.removeElement(symbolEl, {\n    x: sourceLayout.x + 1,\n    y: sourceLayout.y + 1\n  }, seriesModel, {\n    cb: function () {\n      group.remove(symbolEl);\n      data.setItemGraphicEl(dataIndex, null);\n    },\n    removeOpt: removeAnimationOpt\n  });\n  symbolEl.fadeOut(null, data.hostModel, {\n    fadeLabel: true,\n    animation: removeAnimationOpt\n  });\n  // remove edge as parent node\n  node.children.forEach(function (childNode) {\n    removeNodeEdge(childNode, data, group, seriesModel, removeAnimationOpt);\n  });\n  // remove edge as child node\n  removeNodeEdge(node, data, group, seriesModel, removeAnimationOpt);\n}\nfunction getEdgeShape(layoutOpt, orient, curvature, sourceLayout, targetLayout) {\n  var cpx1;\n  var cpy1;\n  var cpx2;\n  var cpy2;\n  var x1;\n  var x2;\n  var y1;\n  var y2;\n  if (layoutOpt === 'radial') {\n    x1 = sourceLayout.rawX;\n    y1 = sourceLayout.rawY;\n    x2 = targetLayout.rawX;\n    y2 = targetLayout.rawY;\n    var radialCoor1 = radialCoordinate(x1, y1);\n    var radialCoor2 = radialCoordinate(x1, y1 + (y2 - y1) * curvature);\n    var radialCoor3 = radialCoordinate(x2, y2 + (y1 - y2) * curvature);\n    var radialCoor4 = radialCoordinate(x2, y2);\n    return {\n      x1: radialCoor1.x || 0,\n      y1: radialCoor1.y || 0,\n      x2: radialCoor4.x || 0,\n      y2: radialCoor4.y || 0,\n      cpx1: radialCoor2.x || 0,\n      cpy1: radialCoor2.y || 0,\n      cpx2: radialCoor3.x || 0,\n      cpy2: radialCoor3.y || 0\n    };\n  } else {\n    x1 = sourceLayout.x;\n    y1 = sourceLayout.y;\n    x2 = targetLayout.x;\n    y2 = targetLayout.y;\n    if (orient === 'LR' || orient === 'RL') {\n      cpx1 = x1 + (x2 - x1) * curvature;\n      cpy1 = y1;\n      cpx2 = x2 + (x1 - x2) * curvature;\n      cpy2 = y2;\n    }\n    if (orient === 'TB' || orient === 'BT') {\n      cpx1 = x1;\n      cpy1 = y1 + (y2 - y1) * curvature;\n      cpx2 = x2;\n      cpy2 = y2 + (y1 - y2) * curvature;\n    }\n  }\n  return {\n    x1: x1,\n    y1: y1,\n    x2: x2,\n    y2: y2,\n    cpx1: cpx1,\n    cpy1: cpy1,\n    cpx2: cpx2,\n    cpy2: cpy2\n  };\n}\nexport default TreeView;"], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,gBAAgB,QAAQ,mBAAmB;AACpD,OAAO,KAAKC,IAAI,MAAM,0BAA0B;AAChD,OAAOC,IAAI,MAAM,qBAAqB;AACtC,OAAO,KAAKC,UAAU,MAAM,sCAAsC;AAClE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,mBAAmB,QAAQ,wCAAwC;AAC5E,SAASC,YAAY,QAAQ,sBAAsB;AACnD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,SAASC,wBAAwB,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,gBAAgB,QAAQ,sBAAsB;AACtH,IAAIC,aAAa,GAAG,aAAa,YAAY;EAC3C,SAASA,aAAaA,CAAA,EAAG;IACvB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,WAAW,GAAG,EAAE;EACvB;EACA,OAAOF,aAAa;AACtB,CAAC,CAAC,CAAC;AACH,IAAIG,QAAQ,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC5CtB,SAAS,CAACqB,QAAQ,EAAEC,MAAM,CAAC;EAC3B,SAASD,QAAQA,CAACE,IAAI,EAAE;IACtB,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;EACxC;EACAF,QAAQ,CAACI,SAAS,CAACC,eAAe,GAAG,YAAY;IAC/C,OAAO;MACLC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDP,QAAQ,CAACI,SAAS,CAACI,eAAe,GAAG,YAAY;IAC/C,OAAO,IAAIX,aAAa,CAAC,CAAC;EAC5B,CAAC;EACDG,QAAQ,CAACI,SAAS,CAACK,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IACnD,IAAIZ,WAAW,GAAGY,KAAK,CAACZ,WAAW;IACnC,IAAIa,QAAQ,GAAGb,WAAW,CAACc,MAAM;IACjC,IAAIf,WAAW,GAAGa,KAAK,CAACb,WAAW;IACnC,IAAIgB,aAAa,GAAGf,WAAW,CAAC,CAAC,CAAC;IAClC,IAAIgB,YAAY,GAAGhB,WAAW,CAACa,QAAQ,GAAG,CAAC,CAAC;IAC5C,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClBF,GAAG,CAACM,MAAM,CAAClB,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC;MAC1CY,GAAG,CAACO,MAAM,CAACH,aAAa,CAAC,CAAC,CAAC,EAAEA,aAAa,CAAC,CAAC,CAAC,CAAC;MAC9C;IACF;IACA,IAAII,MAAM,GAAGP,KAAK,CAACO,MAAM;IACzB,IAAIC,OAAO,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;IACxD,IAAIE,QAAQ,GAAG,CAAC,GAAGD,OAAO;IAC1B,IAAIE,YAAY,GAAG/B,YAAY,CAACqB,KAAK,CAACU,YAAY,EAAE,CAAC,CAAC;IACtD,IAAIC,QAAQ,GAAG,EAAE;IACjBA,QAAQ,CAACH,OAAO,CAAC,GAAGrB,WAAW,CAACqB,OAAO,CAAC;IACxCG,QAAQ,CAACF,QAAQ,CAAC,GAAGtB,WAAW,CAACsB,QAAQ,CAAC,GAAG,CAACL,YAAY,CAACK,QAAQ,CAAC,GAAGtB,WAAW,CAACsB,QAAQ,CAAC,IAAIC,YAAY;IAC5GX,GAAG,CAACM,MAAM,CAAClB,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC;IAC1CY,GAAG,CAACO,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpCZ,GAAG,CAACM,MAAM,CAACF,aAAa,CAAC,CAAC,CAAC,EAAEA,aAAa,CAAC,CAAC,CAAC,CAAC;IAC9CQ,QAAQ,CAACH,OAAO,CAAC,GAAGL,aAAa,CAACK,OAAO,CAAC;IAC1CT,GAAG,CAACO,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpCA,QAAQ,CAACH,OAAO,CAAC,GAAGJ,YAAY,CAACI,OAAO,CAAC;IACzCT,GAAG,CAACO,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACpCZ,GAAG,CAACO,MAAM,CAACF,YAAY,CAAC,CAAC,CAAC,EAAEA,YAAY,CAAC,CAAC,CAAC,CAAC;IAC5C,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,QAAQ,GAAG,CAAC,EAAEW,CAAC,EAAE,EAAE;MACrC,IAAIC,KAAK,GAAGzB,WAAW,CAACwB,CAAC,CAAC;MAC1Bb,GAAG,CAACM,MAAM,CAACQ,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;MAC9BF,QAAQ,CAACH,OAAO,CAAC,GAAGK,KAAK,CAACL,OAAO,CAAC;MAClCT,GAAG,CAACO,MAAM,CAACK,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtC;EACF,CAAC;EACD,OAAOtB,QAAQ;AACjB,CAAC,CAACR,IAAI,CAAC;AACP,IAAIiC,QAAQ,GAAG,aAAa,UAAUxB,MAAM,EAAE;EAC5CtB,SAAS,CAAC8C,QAAQ,EAAExB,MAAM,CAAC;EAC3B,SAASwB,QAAQA,CAAA,EAAG;IAClB,IAAIC,KAAK,GAAGzB,MAAM,KAAK,IAAI,IAAIA,MAAM,CAAC0B,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGJ,QAAQ,CAACI,IAAI;IAC1BH,KAAK,CAACI,UAAU,GAAG,IAAIjD,OAAO,CAACkD,KAAK,CAAC,CAAC;IACtC,OAAOL,KAAK;EACd;EACAD,QAAQ,CAACrB,SAAS,CAAC4B,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IAChD,IAAI,CAACC,WAAW,GAAG,IAAI/C,cAAc,CAAC8C,GAAG,CAACE,KAAK,CAAC,CAAC,CAAC;IAClD,IAAI,CAACC,eAAe,GAAG;MACrBC,MAAM,EAAE,IAAI,CAACC;IACf,CAAC;IACD,IAAI,CAACA,KAAK,CAACC,GAAG,CAAC,IAAI,CAACV,UAAU,CAAC;EACjC,CAAC;EACDL,QAAQ,CAACrB,SAAS,CAACqC,MAAM,GAAG,UAAUC,WAAW,EAAET,OAAO,EAAEC,GAAG,EAAE;IAC/D,IAAIS,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;IAChC,IAAIC,UAAU,GAAGH,WAAW,CAACG,UAAU;IACvC,IAAIN,KAAK,GAAG,IAAI,CAACT,UAAU;IAC3B,IAAIgB,MAAM,GAAGJ,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAID,MAAM,KAAK,QAAQ,EAAE;MACvBP,KAAK,CAACS,CAAC,GAAGH,UAAU,CAACG,CAAC,GAAGH,UAAU,CAACI,KAAK,GAAG,CAAC;MAC7CV,KAAK,CAACW,CAAC,GAAGL,UAAU,CAACK,CAAC,GAAGL,UAAU,CAACM,MAAM,GAAG,CAAC;IAChD,CAAC,MAAM;MACLZ,KAAK,CAACS,CAAC,GAAGH,UAAU,CAACG,CAAC;MACtBT,KAAK,CAACW,CAAC,GAAGL,UAAU,CAACK,CAAC;IACxB;IACA,IAAI,CAACE,mBAAmB,CAACV,WAAW,EAAER,GAAG,CAAC;IAC1C,IAAI,CAACmB,iBAAiB,CAACX,WAAW,EAAET,OAAO,EAAEC,GAAG,CAAC;IACjD,IAAIoB,OAAO,GAAG,IAAI,CAACC,KAAK;IACxBZ,IAAI,CAACa,IAAI,CAACF,OAAO,CAAC,CAACd,GAAG,CAAC,UAAUiB,MAAM,EAAE;MACvC,IAAIC,eAAe,CAACf,IAAI,EAAEc,MAAM,CAAC,EAAE;QACjC;QACAE,UAAU,CAAChB,IAAI,EAAEc,MAAM,EAAE,IAAI,EAAElB,KAAK,EAAEG,WAAW,CAAC;MACpD;IACF,CAAC,CAAC,CAACkB,MAAM,CAAC,UAAUH,MAAM,EAAEI,MAAM,EAAE;MAClC,IAAIC,QAAQ,GAAGR,OAAO,CAACS,gBAAgB,CAACF,MAAM,CAAC;MAC/C,IAAI,CAACH,eAAe,CAACf,IAAI,EAAEc,MAAM,CAAC,EAAE;QAClCK,QAAQ,IAAIE,UAAU,CAACV,OAAO,EAAEO,MAAM,EAAEC,QAAQ,EAAEvB,KAAK,EAAEG,WAAW,CAAC;QACrE;MACF;MACA;MACAiB,UAAU,CAAChB,IAAI,EAAEc,MAAM,EAAEK,QAAQ,EAAEvB,KAAK,EAAEG,WAAW,CAAC;IACxD,CAAC,CAAC,CAACuB,MAAM,CAAC,UAAUJ,MAAM,EAAE;MAC1B,IAAIC,QAAQ,GAAGR,OAAO,CAACS,gBAAgB,CAACF,MAAM,CAAC;MAC/C;MACA;MACA;MACA;MACA;MACA,IAAIC,QAAQ,EAAE;QACZE,UAAU,CAACV,OAAO,EAAEO,MAAM,EAAEC,QAAQ,EAAEvB,KAAK,EAAEG,WAAW,CAAC;MAC3D;IACF,CAAC,CAAC,CAACwB,OAAO,CAAC,CAAC;IACZ,IAAI,CAACC,eAAe,GAAGzB,WAAW,CAACK,GAAG,CAAC,gBAAgB,CAAC;IACxD,IAAI,CAACqB,uBAAuB,CAAC1B,WAAW,CAAC;IACzC,IAAIA,WAAW,CAACK,GAAG,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;MACjDJ,IAAI,CAAC0B,iBAAiB,CAAC,UAAUC,EAAE,EAAEC,SAAS,EAAE;QAC9CD,EAAE,CAACE,GAAG,CAAC,OAAO,CAAC,CAACC,EAAE,CAAC,OAAO,EAAE,YAAY;UACtCvC,GAAG,CAACwC,cAAc,CAAC;YACjB7C,IAAI,EAAE,uBAAuB;YAC7B8C,QAAQ,EAAEjC,WAAW,CAACkC,EAAE;YACxBL,SAAS,EAAEA;UACb,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,IAAI,CAAChB,KAAK,GAAGZ,IAAI;EACnB,CAAC;EACDlB,QAAQ,CAACrB,SAAS,CAACgD,mBAAmB,GAAG,UAAUV,WAAW,EAAER,GAAG,EAAE;IACnE,IAAIS,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;IAChC,IAAIiC,MAAM,GAAG,EAAE;IACflC,IAAI,CAACmC,IAAI,CAAC,UAAUC,GAAG,EAAE;MACvB,IAAIjC,MAAM,GAAGH,IAAI,CAACqC,aAAa,CAACD,GAAG,CAAC;MACpC,IAAIjC,MAAM,IAAI,CAACmC,KAAK,CAACnC,MAAM,CAACE,CAAC,CAAC,IAAI,CAACiC,KAAK,CAACnC,MAAM,CAACI,CAAC,CAAC,EAAE;QAClD2B,MAAM,CAACK,IAAI,CAAC,CAAC,CAACpC,MAAM,CAACE,CAAC,EAAE,CAACF,MAAM,CAACI,CAAC,CAAC,CAAC;MACrC;IACF,CAAC,CAAC;IACF,IAAIiC,GAAG,GAAG,EAAE;IACZ,IAAIC,GAAG,GAAG,EAAE;IACZnG,IAAI,CAACoG,UAAU,CAACR,MAAM,EAAEM,GAAG,EAAEC,GAAG,CAAC;IACjC;IACA;IACA,IAAIE,MAAM,GAAG,IAAI,CAACC,IAAI;IACtB,IAAIC,MAAM,GAAG,IAAI,CAACC,IAAI;IACtB;IACA,IAAIL,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MACzBA,GAAG,CAAC,CAAC,CAAC,GAAGG,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MACxCC,GAAG,CAAC,CAAC,CAAC,GAAGI,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGJ,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAC1C;IACA,IAAIA,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MACzBA,GAAG,CAAC,CAAC,CAAC,GAAGG,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGH,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MACxCC,GAAG,CAAC,CAAC,CAAC,GAAGI,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAGJ,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAC1C;IACA,IAAIM,YAAY,GAAGhD,WAAW,CAACiD,gBAAgB,GAAG,IAAIzG,IAAI,CAAC,CAAC;IAC5DwG,YAAY,CAACE,SAAS,GAAGlD,WAAW,CAACK,GAAG,CAAC,YAAY,CAAC;IACtD2C,YAAY,CAACG,eAAe,CAACV,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAAC,GAAGD,GAAG,CAAC,CAAC,CAAC,CAAC;IAC9EO,YAAY,CAACI,SAAS,CAACpD,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC,EAAEb,GAAG,CAAC;IACtDwD,YAAY,CAACK,OAAO,CAACrD,WAAW,CAACK,GAAG,CAAC,MAAM,CAAC,CAAC;IAC7C;IACA,IAAI,CAACR,KAAK,CAACyD,IAAI,CAAC;MACdhD,CAAC,EAAE0C,YAAY,CAAC1C,CAAC;MACjBE,CAAC,EAAEwC,YAAY,CAACxC,CAAC;MACjB+C,MAAM,EAAEP,YAAY,CAACO,MAAM;MAC3BC,MAAM,EAAER,YAAY,CAACQ;IACvB,CAAC,CAAC;IACF,IAAI,CAACX,IAAI,GAAGJ,GAAG;IACf,IAAI,CAACM,IAAI,GAAGL,GAAG;EACjB,CAAC;EACD3D,QAAQ,CAACrB,SAAS,CAACiD,iBAAiB,GAAG,UAAUX,WAAW,EAAET,OAAO,EAAEC,GAAG,EAAE;IAC1E,IAAIR,KAAK,GAAG,IAAI;IAChB,IAAIyE,UAAU,GAAG,IAAI,CAAChE,WAAW;IACjC,IAAIiE,cAAc,GAAG,IAAI,CAAC/D,eAAe;IACzC,IAAIE,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB4D,UAAU,CAACE,iBAAiB,CAAC,UAAUC,CAAC,EAAEtD,CAAC,EAAEE,CAAC,EAAE;MAC9C,IAAIqD,IAAI,GAAGhE,KAAK,CAACiE,eAAe,CAAC,CAAC;MAClCD,IAAI,CAACE,cAAc,CAAClE,KAAK,CAACmE,SAAS,CAAC;MACpC,OAAOH,IAAI,CAACI,OAAO,CAAC3D,CAAC,EAAEE,CAAC,CAAC,IAAI,CAAC7D,mBAAmB,CAACiH,CAAC,EAAEpE,GAAG,EAAEQ,WAAW,CAAC;IACxE,CAAC,CAAC;IACFyD,UAAU,CAACS,MAAM,CAAClE,WAAW,CAACK,GAAG,CAAC,MAAM,CAAC,CAAC;IAC1CqD,cAAc,CAACR,SAAS,GAAGlD,WAAW,CAACK,GAAG,CAAC,YAAY,CAAC;IACxDqD,cAAc,CAACS,IAAI,GAAGnE,WAAW,CAACiD,gBAAgB,CAACmB,OAAO,CAAC,CAAC;IAC5DX,UAAU,CAAC3B,GAAG,CAAC,KAAK,CAAC,CAACA,GAAG,CAAC,MAAM,CAAC,CAACC,EAAE,CAAC,KAAK,EAAE,UAAU6B,CAAC,EAAE;MACvDnH,UAAU,CAAC4H,eAAe,CAACX,cAAc,EAAEE,CAAC,CAACU,EAAE,EAAEV,CAAC,CAACW,EAAE,CAAC;MACtD/E,GAAG,CAACwC,cAAc,CAAC;QACjBC,QAAQ,EAAEjC,WAAW,CAACkC,EAAE;QACxB/C,IAAI,EAAE,UAAU;QAChBmF,EAAE,EAAEV,CAAC,CAACU,EAAE;QACRC,EAAE,EAAEX,CAAC,CAACW;MACR,CAAC,CAAC;IACJ,CAAC,CAAC,CAACxC,EAAE,CAAC,MAAM,EAAE,UAAU6B,CAAC,EAAE;MACzBnH,UAAU,CAAC+H,gBAAgB,CAACd,cAAc,EAAEE,CAAC,CAACa,KAAK,EAAEb,CAAC,CAACc,OAAO,EAAEd,CAAC,CAACe,OAAO,CAAC;MAC1EnF,GAAG,CAACwC,cAAc,CAAC;QACjBC,QAAQ,EAAEjC,WAAW,CAACkC,EAAE;QACxB/C,IAAI,EAAE,UAAU;QAChBgF,IAAI,EAAEP,CAAC,CAACa,KAAK;QACbC,OAAO,EAAEd,CAAC,CAACc,OAAO;QAClBC,OAAO,EAAEf,CAAC,CAACe;MACb,CAAC,CAAC;MACF3F,KAAK,CAAC0C,uBAAuB,CAAC1B,WAAW,CAAC;MAC1C;MACAR,GAAG,CAACoF,iBAAiB,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC;EACD7F,QAAQ,CAACrB,SAAS,CAACgE,uBAAuB,GAAG,UAAU1B,WAAW,EAAE;IAClE,IAAIC,IAAI,GAAGD,WAAW,CAACE,OAAO,CAAC,CAAC;IAChC,IAAI2E,SAAS,GAAG,IAAI,CAACC,mBAAmB,CAAC9E,WAAW,CAAC;IACrDC,IAAI,CAAC0B,iBAAiB,CAAC,UAAUC,EAAE,EAAES,GAAG,EAAE;MACxCT,EAAE,CAACmD,cAAc,CAACF,SAAS,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC;EACD9F,QAAQ,CAACrB,SAAS,CAACoH,mBAAmB,GAAG,UAAU9E,WAAW,EAAE;IAC9D,IAAIgF,QAAQ,GAAGhF,WAAW,CAACiD,gBAAgB;IAC3C,IAAI+B,QAAQ,CAAC7F,IAAI,KAAK,MAAM,EAAE;MAC5B,OAAO,CAAC;IACV;IACA,IAAI8F,cAAc,GAAG,IAAI,CAACxD,eAAe;IACzC,IAAIyD,SAAS,GAAGF,QAAQ,CAACzB,MAAM,IAAI,CAAC;IACpC;IACA,IAAI4B,QAAQ,GAAGH,QAAQ,CAACZ,OAAO,CAAC,CAAC;IACjC,IAAIS,SAAS,GAAG,CAACM,QAAQ,GAAG,CAAC,IAAIF,cAAc,GAAG,CAAC;IACnD,OAAOJ,SAAS,GAAGK,SAAS;EAC9B,CAAC;EACDnG,QAAQ,CAACrB,SAAS,CAAC0H,OAAO,GAAG,YAAY;IACvC,IAAI,CAAC3F,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC2F,OAAO,CAAC,CAAC;IAC9C,IAAI,CAACzF,eAAe,GAAG,IAAI;EAC7B,CAAC;EACDZ,QAAQ,CAACrB,SAAS,CAAC6D,MAAM,GAAG,YAAY;IACtC,IAAI,CAACnC,UAAU,CAACiG,SAAS,CAAC,CAAC;IAC3B,IAAI,CAACxE,KAAK,GAAG,IAAI;EACnB,CAAC;EACD9B,QAAQ,CAACI,IAAI,GAAG,MAAM;EACtB,OAAOJ,QAAQ;AACjB,CAAC,CAAClC,SAAS,CAAC;AACZ,SAASmE,eAAeA,CAACf,IAAI,EAAE4B,SAAS,EAAE;EACxC,IAAIzB,MAAM,GAAGH,IAAI,CAACqC,aAAa,CAACT,SAAS,CAAC;EAC1C,OAAOzB,MAAM,IAAI,CAACmC,KAAK,CAACnC,MAAM,CAACE,CAAC,CAAC,IAAI,CAACiC,KAAK,CAACnC,MAAM,CAACI,CAAC,CAAC;AACvD;AACA,SAASS,UAAUA,CAAChB,IAAI,EAAE4B,SAAS,EAAET,QAAQ,EAAEvB,KAAK,EAAEG,WAAW,EAAE;EACjE,IAAIsF,MAAM,GAAG,CAAClE,QAAQ;EACtB,IAAImE,IAAI,GAAGtF,IAAI,CAACuF,IAAI,CAACC,kBAAkB,CAAC5D,SAAS,CAAC;EAClD,IAAI6D,SAAS,GAAGH,IAAI,CAACI,QAAQ,CAAC,CAAC;EAC/B,IAAIC,WAAW,GAAGL,IAAI,CAACM,SAAS,CAAC,OAAO,CAAC,CAAChI,IAAI;EAC9C,IAAIiI,gBAAgB,GAAGP,IAAI,CAACQ,QAAQ,KAAK,KAAK,IAAIR,IAAI,CAACS,QAAQ,CAAC7H,MAAM,KAAK,CAAC,GAAGyH,WAAW,GAAG,MAAM;EACnG,IAAIK,WAAW,GAAGhG,IAAI,CAACuF,IAAI,CAACU,IAAI;EAChC,IAAIC,MAAM,GAAGZ,IAAI,CAACa,UAAU,KAAKH,WAAW,GAAGV,IAAI,GAAGA,IAAI,CAACa,UAAU,IAAIb,IAAI;EAC7E,IAAIc,cAAc,GAAGpG,IAAI,CAACoB,gBAAgB,CAAC8E,MAAM,CAACtE,SAAS,CAAC;EAC5D,IAAIyE,YAAY,GAAGH,MAAM,CAACI,SAAS,CAAC,CAAC;EACrC,IAAIC,eAAe,GAAGH,cAAc,GAAG;IACrC/F,CAAC,EAAE+F,cAAc,CAACI,MAAM;IACxBjG,CAAC,EAAE6F,cAAc,CAACK,MAAM;IACxBC,IAAI,EAAEN,cAAc,CAACO,eAAe;IACpCC,IAAI,EAAER,cAAc,CAACS;EACvB,CAAC,GAAGR,YAAY;EAChB,IAAIS,YAAY,GAAGxB,IAAI,CAACgB,SAAS,CAAC,CAAC;EACnC,IAAIjB,MAAM,EAAE;IACVlE,QAAQ,GAAG,IAAI/E,SAAS,CAAC4D,IAAI,EAAE4B,SAAS,EAAE,IAAI,EAAE;MAC9CiE,gBAAgB,EAAEA,gBAAgB;MAClCkB,YAAY,EAAE;IAChB,CAAC,CAAC;IACF5F,QAAQ,CAACd,CAAC,GAAGkG,eAAe,CAAClG,CAAC;IAC9Bc,QAAQ,CAACZ,CAAC,GAAGgG,eAAe,CAAChG,CAAC;EAChC,CAAC,MAAM;IACLY,QAAQ,CAAC6F,UAAU,CAAChH,IAAI,EAAE4B,SAAS,EAAE,IAAI,EAAE;MACzCiE,gBAAgB,EAAEA,gBAAgB;MAClCkB,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ;EACA5F,QAAQ,CAACwF,eAAe,GAAGxF,QAAQ,CAAC8F,YAAY;EAChD9F,QAAQ,CAAC0F,eAAe,GAAG1F,QAAQ,CAAC+F,YAAY;EAChD/F,QAAQ,CAAC8F,YAAY,GAAGH,YAAY,CAACJ,IAAI;EACzCvF,QAAQ,CAAC+F,YAAY,GAAGJ,YAAY,CAACF,IAAI;EACzChH,KAAK,CAACC,GAAG,CAACsB,QAAQ,CAAC;EACnBnB,IAAI,CAACmH,gBAAgB,CAACvF,SAAS,EAAET,QAAQ,CAAC;EAC1CA,QAAQ,CAACqF,MAAM,GAAGrF,QAAQ,CAACd,CAAC;EAC5Bc,QAAQ,CAACsF,MAAM,GAAGtF,QAAQ,CAACZ,CAAC;EAC5BrE,OAAO,CAACkL,WAAW,CAACjG,QAAQ,EAAE;IAC5Bd,CAAC,EAAEyG,YAAY,CAACzG,CAAC;IACjBE,CAAC,EAAEuG,YAAY,CAACvG;EAClB,CAAC,EAAER,WAAW,CAAC;EACf,IAAIsH,UAAU,GAAGlG,QAAQ,CAACmG,aAAa,CAAC,CAAC;EACzC,IAAIvH,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;IAC1C,IAAImH,QAAQ,GAAGvB,WAAW,CAACD,QAAQ,CAAC,CAAC,CAAC;IACtC,IAAIyB,UAAU,GAAGD,QAAQ,CAACjB,SAAS,CAAC,CAAC;IACrC,IAAImB,QAAQ,GAAGF,QAAQ,CAACxB,QAAQ,CAAC7H,MAAM;IACvC,IAAIwJ,GAAG,GAAG,KAAK,CAAC;IAChB,IAAIC,MAAM,GAAG,KAAK,CAAC;IACnB,IAAIb,YAAY,CAACzG,CAAC,KAAKmH,UAAU,CAACnH,CAAC,IAAIiF,IAAI,CAACQ,QAAQ,KAAK,IAAI,IAAIyB,QAAQ,CAACxB,QAAQ,CAAC7H,MAAM,EAAE;MACzF,IAAI0J,MAAM,GAAG;QACXvH,CAAC,EAAE,CAACkH,QAAQ,CAACxB,QAAQ,CAAC,CAAC,CAAC,CAACO,SAAS,CAAC,CAAC,CAACjG,CAAC,GAAGkH,QAAQ,CAACxB,QAAQ,CAAC0B,QAAQ,GAAG,CAAC,CAAC,CAACnB,SAAS,CAAC,CAAC,CAACjG,CAAC,IAAI,CAAC;QAC3FE,CAAC,EAAE,CAACgH,QAAQ,CAACxB,QAAQ,CAAC,CAAC,CAAC,CAACO,SAAS,CAAC,CAAC,CAAC/F,CAAC,GAAGgH,QAAQ,CAACxB,QAAQ,CAAC0B,QAAQ,GAAG,CAAC,CAAC,CAACnB,SAAS,CAAC,CAAC,CAAC/F,CAAC,IAAI;MAC5F,CAAC;MACDmH,GAAG,GAAGG,IAAI,CAACC,KAAK,CAACF,MAAM,CAACrH,CAAC,GAAGiH,UAAU,CAACjH,CAAC,EAAEqH,MAAM,CAACvH,CAAC,GAAGmH,UAAU,CAACnH,CAAC,CAAC;MAClE,IAAIqH,GAAG,GAAG,CAAC,EAAE;QACXA,GAAG,GAAGG,IAAI,CAACE,EAAE,GAAG,CAAC,GAAGL,GAAG;MACzB;MACAC,MAAM,GAAGC,MAAM,CAACvH,CAAC,GAAGmH,UAAU,CAACnH,CAAC;MAChC,IAAIsH,MAAM,EAAE;QACVD,GAAG,GAAGA,GAAG,GAAGG,IAAI,CAACE,EAAE;MACrB;IACF,CAAC,MAAM;MACLL,GAAG,GAAGG,IAAI,CAACC,KAAK,CAAChB,YAAY,CAACvG,CAAC,GAAGiH,UAAU,CAACjH,CAAC,EAAEuG,YAAY,CAACzG,CAAC,GAAGmH,UAAU,CAACnH,CAAC,CAAC;MAC9E,IAAIqH,GAAG,GAAG,CAAC,EAAE;QACXA,GAAG,GAAGG,IAAI,CAACE,EAAE,GAAG,CAAC,GAAGL,GAAG;MACzB;MACA,IAAIpC,IAAI,CAACS,QAAQ,CAAC7H,MAAM,KAAK,CAAC,IAAIoH,IAAI,CAACS,QAAQ,CAAC7H,MAAM,KAAK,CAAC,IAAIoH,IAAI,CAACQ,QAAQ,KAAK,KAAK,EAAE;QACvF6B,MAAM,GAAGb,YAAY,CAACzG,CAAC,GAAGmH,UAAU,CAACnH,CAAC;QACtC,IAAIsH,MAAM,EAAE;UACVD,GAAG,GAAGA,GAAG,GAAGG,IAAI,CAACE,EAAE;QACrB;MACF,CAAC,MAAM;QACLJ,MAAM,GAAGb,YAAY,CAACzG,CAAC,GAAGmH,UAAU,CAACnH,CAAC;QACtC,IAAI,CAACsH,MAAM,EAAE;UACXD,GAAG,GAAGA,GAAG,GAAGG,IAAI,CAACE,EAAE;QACrB;MACF;IACF;IACA,IAAIC,YAAY,GAAGL,MAAM,GAAG,MAAM,GAAG,OAAO;IAC5C,IAAIM,gBAAgB,GAAGxC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC;IAClD,IAAIwC,MAAM,GAAGD,gBAAgB,CAAC7H,GAAG,CAAC,QAAQ,CAAC;IAC3C,IAAI+H,iBAAiB,GAAGD,MAAM,IAAIL,IAAI,CAACE,EAAE,GAAG,GAAG,CAAC;IAChD,IAAIK,WAAW,GAAGf,UAAU,CAACgB,cAAc,CAAC,CAAC;IAC7C,IAAID,WAAW,EAAE;MACff,UAAU,CAACiB,aAAa,CAAC;QACvBC,QAAQ,EAAEN,gBAAgB,CAAC7H,GAAG,CAAC,UAAU,CAAC,IAAI4H,YAAY;QAC1DQ,QAAQ,EAAEN,MAAM,IAAI,IAAI,GAAG,CAACR,GAAG,GAAGS,iBAAiB;QACnDM,MAAM,EAAE;MACV,CAAC,CAAC;MACFL,WAAW,CAACM,QAAQ,CAAC,eAAe,EAAE,QAAQ,CAAC;IACjD;EACF;EACA;EACA,IAAIC,KAAK,GAAGlD,SAAS,CAACrF,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;EAChD,IAAIwI,gBAAgB,GAAGD,KAAK,KAAK,UAAU,GAAG1M,MAAM,CAAC4M,WAAW,CAACvD,IAAI,CAACwD,mBAAmB,CAAC,CAAC,EAAExD,IAAI,CAACyD,oBAAoB,CAAC,CAAC,CAAC,GAAGJ,KAAK,KAAK,UAAU,GAAGrD,IAAI,CAACwD,mBAAmB,CAAC,CAAC,GAAGH,KAAK,KAAK,YAAY,GAAGrD,IAAI,CAACyD,oBAAoB,CAAC,CAAC,GAAG,IAAI;EAC3O,IAAIH,gBAAgB,EAAE;IACpB;IACAzM,SAAS,CAACgF,QAAQ,CAAC,CAACwH,KAAK,GAAGC,gBAAgB;EAC9C;EACAI,QAAQ,CAACjJ,WAAW,EAAEuF,IAAI,EAAEU,WAAW,EAAE7E,QAAQ,EAAEoF,eAAe,EAAEF,YAAY,EAAES,YAAY,EAAElH,KAAK,CAAC;EACtG,IAAIuB,QAAQ,CAAC8H,MAAM,EAAE;IACnB9H,QAAQ,CAAC+H,kBAAkB,GAAG,UAAUC,OAAO,EAAE;MAC/C,IAAIA,OAAO,KAAK,MAAM,EAAE;QACtB;QACA;QACA;QACA,IAAIC,QAAQ,GAAG9D,IAAI,CAACa,UAAU,IAAInG,IAAI,CAACoB,gBAAgB,CAACkE,IAAI,CAACa,UAAU,CAACvE,SAAS,CAAC;QAClF,IAAI,EAAEwH,QAAQ,IAAIA,QAAQ,CAACC,UAAU,KAAKpM,gBAAgB,CAAC,EAAE;UAC3DF,aAAa,CAACoE,QAAQ,CAAC8H,MAAM,EAAEE,OAAO,CAAC;QACzC;MACF;IACF,CAAC;EACH;AACF;AACA,SAASH,QAAQA,CAACjJ,WAAW,EAAEuF,IAAI,EAAEU,WAAW,EAAE7E,QAAQ,EAAEoF,eAAe,EAAEF,YAAY,EAAES,YAAY,EAAElH,KAAK,EAAE;EAC9G,IAAI6F,SAAS,GAAGH,IAAI,CAACI,QAAQ,CAAC,CAAC;EAC/B,IAAI4D,SAAS,GAAGvJ,WAAW,CAACK,GAAG,CAAC,WAAW,CAAC;EAC5C,IAAID,MAAM,GAAGJ,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC;EACtC,IAAI7B,MAAM,GAAGwB,WAAW,CAACwJ,SAAS,CAAC,CAAC;EACpC,IAAIC,SAAS,GAAGzJ,WAAW,CAACK,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;EAC3D,IAAIqJ,gBAAgB,GAAG1J,WAAW,CAACK,GAAG,CAAC,kBAAkB,CAAC;EAC1D,IAAIsJ,SAAS,GAAGjE,SAAS,CAACC,QAAQ,CAAC,WAAW,CAAC,CAACiE,YAAY,CAAC,CAAC;EAC9D,IAAIC,IAAI,GAAGzI,QAAQ,CAAC8H,MAAM;EAC1B;EACA;EACA,IAAIK,SAAS,KAAK,OAAO,EAAE;IACzB,IAAIhE,IAAI,CAACa,UAAU,IAAIb,IAAI,CAACa,UAAU,KAAKH,WAAW,EAAE;MACtD,IAAI,CAAC4D,IAAI,EAAE;QACTA,IAAI,GAAGzI,QAAQ,CAAC8H,MAAM,GAAG,IAAI/M,OAAO,CAAC2N,WAAW,CAAC;UAC/C7L,KAAK,EAAE8L,YAAY,CAAC3J,MAAM,EAAE5B,MAAM,EAAEiL,SAAS,EAAEjD,eAAe,EAAEA,eAAe;QACjF,CAAC,CAAC;MACJ;MACArK,OAAO,CAACkL,WAAW,CAACwC,IAAI,EAAE;QACxB5L,KAAK,EAAE8L,YAAY,CAAC3J,MAAM,EAAE5B,MAAM,EAAEiL,SAAS,EAAEnD,YAAY,EAAES,YAAY;MAC3E,CAAC,EAAE/G,WAAW,CAAC;IACjB;EACF,CAAC,MAAM,IAAIuJ,SAAS,KAAK,UAAU,EAAE;IACnC,IAAInJ,MAAM,KAAK,YAAY,EAAE;MAC3B,IAAImF,IAAI,KAAKU,WAAW,IAAIV,IAAI,CAACS,QAAQ,IAAIT,IAAI,CAACS,QAAQ,CAAC7H,MAAM,KAAK,CAAC,IAAIoH,IAAI,CAACQ,QAAQ,KAAK,IAAI,EAAE;QACjG,IAAIC,QAAQ,GAAGT,IAAI,CAACS,QAAQ;QAC5B,IAAI3I,WAAW,GAAG,EAAE;QACpB,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmH,QAAQ,CAAC7H,MAAM,EAAEU,CAAC,EAAE,EAAE;UACxC,IAAImL,WAAW,GAAGhE,QAAQ,CAACnH,CAAC,CAAC,CAAC0H,SAAS,CAAC,CAAC;UACzClJ,WAAW,CAACmF,IAAI,CAAC,CAACwH,WAAW,CAAC1J,CAAC,EAAE0J,WAAW,CAACxJ,CAAC,CAAC,CAAC;QAClD;QACA,IAAI,CAACqJ,IAAI,EAAE;UACTA,IAAI,GAAGzI,QAAQ,CAAC8H,MAAM,GAAG,IAAI5L,QAAQ,CAAC;YACpCW,KAAK,EAAE;cACLb,WAAW,EAAE,CAAC2J,YAAY,CAACzG,CAAC,EAAEyG,YAAY,CAACvG,CAAC,CAAC;cAC7CnD,WAAW,EAAE,CAAC,CAAC0J,YAAY,CAACzG,CAAC,EAAEyG,YAAY,CAACvG,CAAC,CAAC,CAAC;cAC/ChC,MAAM,EAAEA,MAAM;cACdG,YAAY,EAAE+K;YAChB;UACF,CAAC,CAAC;QACJ;QACAvN,OAAO,CAACkL,WAAW,CAACwC,IAAI,EAAE;UACxB5L,KAAK,EAAE;YACLb,WAAW,EAAE,CAAC2J,YAAY,CAACzG,CAAC,EAAEyG,YAAY,CAACvG,CAAC,CAAC;YAC7CnD,WAAW,EAAEA;UACf;QACF,CAAC,EAAE2C,WAAW,CAAC;MACjB;IACF,CAAC,MAAM;MACL,IAAIiK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAM,IAAIC,KAAK,CAAC,8DAA8D,CAAC;MACjF;IACF;EACF;EACA;EACA,IAAIP,IAAI,IAAI,EAAEN,SAAS,KAAK,UAAU,IAAI,CAAChE,IAAI,CAACQ,QAAQ,CAAC,EAAE;IACzD8D,IAAI,CAACQ,QAAQ,CAACnO,MAAM,CAACoO,QAAQ,CAAC;MAC5BC,aAAa,EAAE,IAAI;MACnB1M,IAAI,EAAE;IACR,CAAC,EAAE8L,SAAS,CAAC,CAAC;IACd5M,wBAAwB,CAAC8M,IAAI,EAAEnE,SAAS,EAAE,WAAW,CAAC;IACtDzI,oBAAoB,CAAC4M,IAAI,CAAC;IAC1BhK,KAAK,CAACC,GAAG,CAAC+J,IAAI,CAAC;EACjB;AACF;AACA,SAASW,cAAcA,CAACjF,IAAI,EAAEtF,IAAI,EAAEJ,KAAK,EAAEG,WAAW,EAAEyK,kBAAkB,EAAE;EAC1E,IAAIxE,WAAW,GAAGhG,IAAI,CAACuF,IAAI,CAACU,IAAI;EAChC,IAAIwE,EAAE,GAAGC,aAAa,CAAC1E,WAAW,EAAEV,IAAI,CAAC;IACvCY,MAAM,GAAGuE,EAAE,CAACvE,MAAM;IAClBG,YAAY,GAAGoE,EAAE,CAACpE,YAAY;EAChC,IAAIlF,QAAQ,GAAGnB,IAAI,CAACoB,gBAAgB,CAACkE,IAAI,CAAC1D,SAAS,CAAC;EACpD,IAAI,CAACT,QAAQ,EAAE;IACb;EACF;EACA,IAAIiF,cAAc,GAAGpG,IAAI,CAACoB,gBAAgB,CAAC8E,MAAM,CAACtE,SAAS,CAAC;EAC5D,IAAI+I,UAAU,GAAGvE,cAAc,CAAC6C,MAAM;EACtC;EACA;EACA;EACA;EACA,IAAIW,IAAI,GAAGzI,QAAQ,CAAC8H,MAAM,KAAK/C,MAAM,CAACJ,QAAQ,KAAK,KAAK,IAAII,MAAM,CAACH,QAAQ,CAAC7H,MAAM,KAAK,CAAC,GAAGyM,UAAU,GAAGC,SAAS,CAAC;EAClH,IAAItB,SAAS,GAAGvJ,WAAW,CAACK,GAAG,CAAC,WAAW,CAAC;EAC5C,IAAIyK,SAAS,GAAG9K,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC;EACzC,IAAI7B,MAAM,GAAGwB,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC;EACtC,IAAIoJ,SAAS,GAAGzJ,WAAW,CAACK,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;EAC3D,IAAIwJ,IAAI,EAAE;IACR,IAAIN,SAAS,KAAK,OAAO,EAAE;MACzBpN,OAAO,CAAC4O,aAAa,CAAClB,IAAI,EAAE;QAC1B5L,KAAK,EAAE8L,YAAY,CAACe,SAAS,EAAEtM,MAAM,EAAEiL,SAAS,EAAEnD,YAAY,EAAEA,YAAY,CAAC;QAC7E0E,KAAK,EAAE;UACLC,OAAO,EAAE;QACX;MACF,CAAC,EAAEjL,WAAW,EAAE;QACdkL,EAAE,EAAE,SAAAA,CAAA,EAAY;UACdrL,KAAK,CAAC0B,MAAM,CAACsI,IAAI,CAAC;QACpB,CAAC;QACDsB,SAAS,EAAEV;MACb,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIlB,SAAS,KAAK,UAAU,IAAIvJ,WAAW,CAACK,GAAG,CAAC,QAAQ,CAAC,KAAK,YAAY,EAAE;MACjFlE,OAAO,CAAC4O,aAAa,CAAClB,IAAI,EAAE;QAC1B5L,KAAK,EAAE;UACLb,WAAW,EAAE,CAACkJ,YAAY,CAAChG,CAAC,EAAEgG,YAAY,CAAC9F,CAAC,CAAC;UAC7CnD,WAAW,EAAE,CAAC,CAACiJ,YAAY,CAAChG,CAAC,EAAEgG,YAAY,CAAC9F,CAAC,CAAC;QAChD,CAAC;QACDwK,KAAK,EAAE;UACLC,OAAO,EAAE;QACX;MACF,CAAC,EAAEjL,WAAW,EAAE;QACdkL,EAAE,EAAE,SAAAA,CAAA,EAAY;UACdrL,KAAK,CAAC0B,MAAM,CAACsI,IAAI,CAAC;QACpB,CAAC;QACDsB,SAAS,EAAEV;MACb,CAAC,CAAC;IACJ;EACF;AACF;AACA,SAASE,aAAaA,CAAC1E,WAAW,EAAEV,IAAI,EAAE;EACxC,IAAIY,MAAM,GAAGZ,IAAI,CAACa,UAAU,KAAKH,WAAW,GAAGV,IAAI,GAAGA,IAAI,CAACa,UAAU,IAAIb,IAAI;EAC7E,IAAIe,YAAY;EAChB,OAAOA,YAAY,GAAGH,MAAM,CAACI,SAAS,CAAC,CAAC,EAAED,YAAY,IAAI,IAAI,EAAE;IAC9DH,MAAM,GAAGA,MAAM,CAACC,UAAU,KAAKH,WAAW,GAAGE,MAAM,GAAGA,MAAM,CAACC,UAAU,IAAID,MAAM;EACnF;EACA,OAAO;IACLA,MAAM,EAAEA,MAAM;IACdG,YAAY,EAAEA;EAChB,CAAC;AACH;AACA,SAAShF,UAAUA,CAACrB,IAAI,EAAE4B,SAAS,EAAET,QAAQ,EAAEvB,KAAK,EAAEG,WAAW,EAAE;EACjE,IAAIuF,IAAI,GAAGtF,IAAI,CAACuF,IAAI,CAACC,kBAAkB,CAAC5D,SAAS,CAAC;EAClD,IAAIoE,WAAW,GAAGhG,IAAI,CAACuF,IAAI,CAACU,IAAI;EAChC,IAAII,YAAY,GAAGqE,aAAa,CAAC1E,WAAW,EAAEV,IAAI,CAAC,CAACe,YAAY;EAChE;EACA,IAAImE,kBAAkB,GAAG;IACvBW,QAAQ,EAAEpL,WAAW,CAACK,GAAG,CAAC,yBAAyB,CAAC;IACpDgL,MAAM,EAAErL,WAAW,CAACK,GAAG,CAAC,uBAAuB;EACjD,CAAC;EACDlE,OAAO,CAAC4O,aAAa,CAAC3J,QAAQ,EAAE;IAC9Bd,CAAC,EAAEgG,YAAY,CAAChG,CAAC,GAAG,CAAC;IACrBE,CAAC,EAAE8F,YAAY,CAAC9F,CAAC,GAAG;EACtB,CAAC,EAAER,WAAW,EAAE;IACdkL,EAAE,EAAE,SAAAA,CAAA,EAAY;MACdrL,KAAK,CAAC0B,MAAM,CAACH,QAAQ,CAAC;MACtBnB,IAAI,CAACmH,gBAAgB,CAACvF,SAAS,EAAE,IAAI,CAAC;IACxC,CAAC;IACDsJ,SAAS,EAAEV;EACb,CAAC,CAAC;EACFrJ,QAAQ,CAACkK,OAAO,CAAC,IAAI,EAAErL,IAAI,CAACsL,SAAS,EAAE;IACrCC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAEhB;EACb,CAAC,CAAC;EACF;EACAlF,IAAI,CAACS,QAAQ,CAAC0F,OAAO,CAAC,UAAUC,SAAS,EAAE;IACzCnB,cAAc,CAACmB,SAAS,EAAE1L,IAAI,EAAEJ,KAAK,EAAEG,WAAW,EAAEyK,kBAAkB,CAAC;EACzE,CAAC,CAAC;EACF;EACAD,cAAc,CAACjF,IAAI,EAAEtF,IAAI,EAAEJ,KAAK,EAAEG,WAAW,EAAEyK,kBAAkB,CAAC;AACpE;AACA,SAASV,YAAYA,CAACe,SAAS,EAAEtM,MAAM,EAAEiL,SAAS,EAAEnD,YAAY,EAAES,YAAY,EAAE;EAC9E,IAAI6E,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,EAAE;EACN,IAAIC,EAAE;EACN,IAAIC,EAAE;EACN,IAAIC,EAAE;EACN,IAAIrB,SAAS,KAAK,QAAQ,EAAE;IAC1BkB,EAAE,GAAG1F,YAAY,CAACK,IAAI;IACtBuF,EAAE,GAAG5F,YAAY,CAACO,IAAI;IACtBoF,EAAE,GAAGlF,YAAY,CAACJ,IAAI;IACtBwF,EAAE,GAAGpF,YAAY,CAACF,IAAI;IACtB,IAAIuF,WAAW,GAAG9P,gBAAgB,CAAC0P,EAAE,EAAEE,EAAE,CAAC;IAC1C,IAAIG,WAAW,GAAG/P,gBAAgB,CAAC0P,EAAE,EAAEE,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAIzC,SAAS,CAAC;IAClE,IAAI6C,WAAW,GAAGhQ,gBAAgB,CAAC2P,EAAE,EAAEE,EAAE,GAAG,CAACD,EAAE,GAAGC,EAAE,IAAI1C,SAAS,CAAC;IAClE,IAAI8C,WAAW,GAAGjQ,gBAAgB,CAAC2P,EAAE,EAAEE,EAAE,CAAC;IAC1C,OAAO;MACLH,EAAE,EAAEI,WAAW,CAAC9L,CAAC,IAAI,CAAC;MACtB4L,EAAE,EAAEE,WAAW,CAAC5L,CAAC,IAAI,CAAC;MACtByL,EAAE,EAAEM,WAAW,CAACjM,CAAC,IAAI,CAAC;MACtB6L,EAAE,EAAEI,WAAW,CAAC/L,CAAC,IAAI,CAAC;MACtBoL,IAAI,EAAES,WAAW,CAAC/L,CAAC,IAAI,CAAC;MACxBuL,IAAI,EAAEQ,WAAW,CAAC7L,CAAC,IAAI,CAAC;MACxBsL,IAAI,EAAEQ,WAAW,CAAChM,CAAC,IAAI,CAAC;MACxByL,IAAI,EAAEO,WAAW,CAAC9L,CAAC,IAAI;IACzB,CAAC;EACH,CAAC,MAAM;IACLwL,EAAE,GAAG1F,YAAY,CAAChG,CAAC;IACnB4L,EAAE,GAAG5F,YAAY,CAAC9F,CAAC;IACnByL,EAAE,GAAGlF,YAAY,CAACzG,CAAC;IACnB6L,EAAE,GAAGpF,YAAY,CAACvG,CAAC;IACnB,IAAIhC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,IAAI,EAAE;MACtCoN,IAAI,GAAGI,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAIvC,SAAS;MACjCoC,IAAI,GAAGK,EAAE;MACTJ,IAAI,GAAGG,EAAE,GAAG,CAACD,EAAE,GAAGC,EAAE,IAAIxC,SAAS;MACjCsC,IAAI,GAAGI,EAAE;IACX;IACA,IAAI3N,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,IAAI,EAAE;MACtCoN,IAAI,GAAGI,EAAE;MACTH,IAAI,GAAGK,EAAE,GAAG,CAACC,EAAE,GAAGD,EAAE,IAAIzC,SAAS;MACjCqC,IAAI,GAAGG,EAAE;MACTF,IAAI,GAAGI,EAAE,GAAG,CAACD,EAAE,GAAGC,EAAE,IAAI1C,SAAS;IACnC;EACF;EACA,OAAO;IACLuC,EAAE,EAAEA,EAAE;IACNE,EAAE,EAAEA,EAAE;IACND,EAAE,EAAEA,EAAE;IACNE,EAAE,EAAEA,EAAE;IACNP,IAAI,EAAEA,IAAI;IACVC,IAAI,EAAEA,IAAI;IACVC,IAAI,EAAEA,IAAI;IACVC,IAAI,EAAEA;EACR,CAAC;AACH;AACA,eAAehN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}