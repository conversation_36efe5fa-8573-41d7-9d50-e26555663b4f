{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nvar _util = require('element-ui/lib/utils/util');\n\n/**\n * Show migrating guide in browser console.\n *\n * Usage:\n * import Migrating from 'element-ui/src/mixins/migrating';\n *\n * mixins: [Migrating]\n *\n * add getMigratingConfig method for your component.\n *  getMigratingConfig() {\n *    return {\n *      props: {\n *        'allow-no-selection': 'allow-no-selection is removed.',\n *        'selection-mode': 'selection-mode is removed.'\n *      },\n *      events: {\n *        selectionchange: 'selectionchange is renamed to selection-change.'\n *      }\n *    };\n *  },\n */\nexports.default = {\n  mounted: function mounted() {\n    if (process.env.NODE_ENV === 'production') return;\n    if (!this.$vnode) return;\n    var _getMigratingConfig = this.getMigratingConfig(),\n      _getMigratingConfig$p = _getMigratingConfig.props,\n      props = _getMigratingConfig$p === undefined ? {} : _getMigratingConfig$p,\n      _getMigratingConfig$e = _getMigratingConfig.events,\n      events = _getMigratingConfig$e === undefined ? {} : _getMigratingConfig$e;\n    var _$vnode = this.$vnode,\n      data = _$vnode.data,\n      componentOptions = _$vnode.componentOptions;\n    var definedProps = data.attrs || {};\n    var definedEvents = componentOptions.listeners || {};\n    for (var propName in definedProps) {\n      propName = (0, _util.kebabCase)(propName); // compatible with camel case\n      if (props[propName]) {\n        console.warn('[Element Migrating][' + this.$options.name + '][Attribute]: ' + props[propName]);\n      }\n    }\n    for (var eventName in definedEvents) {\n      eventName = (0, _util.kebabCase)(eventName); // compatible with camel case\n      if (events[eventName]) {\n        console.warn('[Element Migrating][' + this.$options.name + '][Event]: ' + events[eventName]);\n      }\n    }\n  },\n  methods: {\n    getMigratingConfig: function getMigratingConfig() {\n      return {\n        props: {},\n        events: {}\n      };\n    }\n  }\n};", "map": {"version": 3, "names": ["exports", "__esModule", "_util", "require", "default", "mounted", "process", "env", "NODE_ENV", "$vnode", "_getMigratingConfig", "getMigratingConfig", "_getMigratingConfig$p", "props", "undefined", "_getMigratingConfig$e", "events", "_$vnode", "data", "componentOptions", "definedProps", "attrs", "definedEvents", "listeners", "propName", "kebabCase", "console", "warn", "$options", "name", "eventName", "methods"], "sources": ["D:/FastBI/datafront/node_modules/element-ui/lib/mixins/migrating.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _util = require('element-ui/lib/utils/util');\n\n/**\n * Show migrating guide in browser console.\n *\n * Usage:\n * import Migrating from 'element-ui/src/mixins/migrating';\n *\n * mixins: [Migrating]\n *\n * add getMigratingConfig method for your component.\n *  getMigratingConfig() {\n *    return {\n *      props: {\n *        'allow-no-selection': 'allow-no-selection is removed.',\n *        'selection-mode': 'selection-mode is removed.'\n *      },\n *      events: {\n *        selectionchange: 'selectionchange is renamed to selection-change.'\n *      }\n *    };\n *  },\n */\nexports.default = {\n  mounted: function mounted() {\n    if (process.env.NODE_ENV === 'production') return;\n    if (!this.$vnode) return;\n\n    var _getMigratingConfig = this.getMigratingConfig(),\n        _getMigratingConfig$p = _getMigratingConfig.props,\n        props = _getMigratingConfig$p === undefined ? {} : _getMigratingConfig$p,\n        _getMigratingConfig$e = _getMigratingConfig.events,\n        events = _getMigratingConfig$e === undefined ? {} : _getMigratingConfig$e;\n\n    var _$vnode = this.$vnode,\n        data = _$vnode.data,\n        componentOptions = _$vnode.componentOptions;\n\n    var definedProps = data.attrs || {};\n    var definedEvents = componentOptions.listeners || {};\n\n    for (var propName in definedProps) {\n      propName = (0, _util.kebabCase)(propName); // compatible with camel case\n      if (props[propName]) {\n        console.warn('[Element Migrating][' + this.$options.name + '][Attribute]: ' + props[propName]);\n      }\n    }\n\n    for (var eventName in definedEvents) {\n      eventName = (0, _util.kebabCase)(eventName); // compatible with camel case\n      if (events[eventName]) {\n        console.warn('[Element Migrating][' + this.$options.name + '][Event]: ' + events[eventName]);\n      }\n    }\n  },\n\n  methods: {\n    getMigratingConfig: function getMigratingConfig() {\n      return {\n        props: {},\n        events: {}\n      };\n    }\n  }\n};"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,KAAK,GAAGC,OAAO,CAAC,2BAA2B,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAH,OAAO,CAACI,OAAO,GAAG;EAChBC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IAC3C,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;IAElB,IAAIC,mBAAmB,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC/CC,qBAAqB,GAAGF,mBAAmB,CAACG,KAAK;MACjDA,KAAK,GAAGD,qBAAqB,KAAKE,SAAS,GAAG,CAAC,CAAC,GAAGF,qBAAqB;MACxEG,qBAAqB,GAAGL,mBAAmB,CAACM,MAAM;MAClDA,MAAM,GAAGD,qBAAqB,KAAKD,SAAS,GAAG,CAAC,CAAC,GAAGC,qBAAqB;IAE7E,IAAIE,OAAO,GAAG,IAAI,CAACR,MAAM;MACrBS,IAAI,GAAGD,OAAO,CAACC,IAAI;MACnBC,gBAAgB,GAAGF,OAAO,CAACE,gBAAgB;IAE/C,IAAIC,YAAY,GAAGF,IAAI,CAACG,KAAK,IAAI,CAAC,CAAC;IACnC,IAAIC,aAAa,GAAGH,gBAAgB,CAACI,SAAS,IAAI,CAAC,CAAC;IAEpD,KAAK,IAAIC,QAAQ,IAAIJ,YAAY,EAAE;MACjCI,QAAQ,GAAG,CAAC,CAAC,EAAEtB,KAAK,CAACuB,SAAS,EAAED,QAAQ,CAAC,CAAC,CAAC;MAC3C,IAAIX,KAAK,CAACW,QAAQ,CAAC,EAAE;QACnBE,OAAO,CAACC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAACC,QAAQ,CAACC,IAAI,GAAG,gBAAgB,GAAGhB,KAAK,CAACW,QAAQ,CAAC,CAAC;MAChG;IACF;IAEA,KAAK,IAAIM,SAAS,IAAIR,aAAa,EAAE;MACnCQ,SAAS,GAAG,CAAC,CAAC,EAAE5B,KAAK,CAACuB,SAAS,EAAEK,SAAS,CAAC,CAAC,CAAC;MAC7C,IAAId,MAAM,CAACc,SAAS,CAAC,EAAE;QACrBJ,OAAO,CAACC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAACC,QAAQ,CAACC,IAAI,GAAG,YAAY,GAAGb,MAAM,CAACc,SAAS,CAAC,CAAC;MAC9F;IACF;EACF,CAAC;EAEDC,OAAO,EAAE;IACPpB,kBAAkB,EAAE,SAASA,kBAAkBA,CAAA,EAAG;MAChD,OAAO;QACLE,KAAK,EAAE,CAAC,CAAC;QACTG,MAAM,EAAE,CAAC;MACX,CAAC;IACH;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}