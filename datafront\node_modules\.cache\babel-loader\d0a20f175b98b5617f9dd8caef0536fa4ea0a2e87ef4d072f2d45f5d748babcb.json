{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      localChartConfig: null,\n      // 本地图表配置副本\n      dropdownVisible: false,\n      // 下拉菜单显示状态\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'chart-icon-bar',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'chart-icon-line',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'chart-icon-pie',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'chart-icon-bar-horizontal',\n          compatible: true\n        }\n      }\n    };\n  },\n  computed: {\n    // 是否显示图表切换器\n    showChartSwitcher() {\n      return this.availableChartTypes.length > 1 && !this.loading && !this.error;\n    },\n    // 可用的图表类型列表\n    availableChartTypes() {\n      const types = [];\n\n      // 获取所有支持的图表类型\n      const allTypes = Object.keys(this.chartTypeConfig);\n\n      // 添加当前图表类型（排在第一位）\n      if (this.currentChartType && this.chartTypeConfig[this.currentChartType]) {\n        types.push({\n          type: this.currentChartType,\n          ...this.chartTypeConfig[this.currentChartType],\n          compatible: true // 当前类型肯定兼容\n        });\n      }\n\n      // 添加其他所有图表类型\n      allTypes.forEach(type => {\n        if (type !== this.currentChartType) {\n          types.push({\n            type: type,\n            ...this.chartTypeConfig[type],\n            compatible: true // 暂时都设为兼容\n          });\n        }\n      });\n      return types;\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler(newConfig) {\n        // 更新当前图表类型\n        if (newConfig && newConfig.type) {\n          this.currentChartType = newConfig.type;\n        }\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    // 初始化当前图表类型\n    if (this.chartConfig && this.chartConfig.type) {\n      this.currentChartType = this.chartConfig.type;\n    }\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n    // 添加点击外部关闭下拉菜单的监听\n    document.addEventListener('click', this.handleClickOutside);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 移除点击外部关闭下拉菜单的监听\n    document.removeEventListener('click', this.handleClickOutside);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    // 切换图表类型\n    async switchChartType(newType) {\n      if (newType === this.currentChartType || this.switchingChart) {\n        return;\n      }\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n      try {\n        this.switchingChart = true;\n\n        // 更新图表类型\n        this.currentChartType = newType;\n\n        // 创建新的图表配置，复用相同数据\n        const newChartConfig = {\n          ...this.chartConfig,\n          type: newType\n        };\n\n        // 触发重新渲染\n        this.$emit('chart-type-changed', newChartConfig);\n\n        // 使用本地副本进行渲染，避免直接修改 prop\n        this.localChartConfig = {\n          ...newChartConfig\n        };\n        this.renderChartWithConfig(this.localChartConfig);\n        this.$message.success(`已切换到${this.chartTypeConfig[newType]?.name}`);\n      } catch (error) {\n        console.error('图表切换失败:', error);\n        this.$message.error('图表切换失败: ' + error.message);\n      } finally {\n        this.switchingChart = false;\n      }\n    },\n    // 切换下拉菜单显示状态\n    toggleDropdown() {\n      this.dropdownVisible = !this.dropdownVisible;\n    },\n    // 选择图表类型\n    selectChartType(chartType) {\n      this.dropdownVisible = false;\n      this.switchChartType(chartType);\n    },\n    // 获取当前图表类型的图标\n    getCurrentChartIcon() {\n      return this.chartTypeConfig[this.currentChartType]?.icon || 'chart-icon-bar';\n    },\n    // 获取当前图表类型的名称\n    getCurrentChartName() {\n      return this.chartTypeConfig[this.currentChartType]?.name || '柱图';\n    },\n    // 点击外部关闭下拉菜单\n    handleClickOutside(event) {\n      if (!this.$el.contains(event.target)) {\n        this.dropdownVisible = false;\n      }\n    },\n    renderChart() {\n      this.renderChartWithConfig(this.chartConfig);\n    },\n    renderChartWithConfig(config) {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!config || !config.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', config);\n        return;\n      }\n      console.log('开始渲染图表，配置:', config);\n\n      // 检查chartConfig是否已包含完整数据\n      if (config.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(config);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n\n      // 如果没有数据，才调用API获取\n      getData(config).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n\n      // 使用当前的图表类型（可能是切换后的类型）\n      const chartType = chartData.type || this.currentChartType || this.chartConfig.type;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch (chartType) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n\n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n\n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n\n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        console.log('提取的数据:', extractedData);\n\n        // 根据当前图表类型调用不同的处理函数\n        const currentType = chartType || extractedData.type;\n        switch (currentType) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.localChartConfig && this.localChartConfig.title || this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const {\n        data = [],\n        metrics = []\n      } = chartData;\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const {\n        data = []\n      } = chartData;\n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getData", "echarts", "name", "props", "chartConfig", "type", "Object", "required", "default", "recommendedChartTypes", "Array", "data", "loading", "error", "errorMsg", "chartInstance", "<PERSON><PERSON><PERSON>", "currentChartType", "localChartConfig", "dropdownVisible", "chartTypeConfig", "icon", "compatible", "computed", "showChartSwitcher", "availableChartTypes", "length", "types", "allTypes", "keys", "push", "for<PERSON>ach", "watch", "deep", "handler", "newConfig", "<PERSON><PERSON><PERSON>", "mounted", "window", "addEventListener", "resizeChart", "document", "handleClickOutside", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "dispose", "methods", "switchChartType", "newType", "console", "log", "newChartConfig", "$emit", "renderChartWithConfig", "$message", "success", "message", "toggleDropdown", "selectChartType", "chartType", "getCurrentChartIcon", "getCurrentChartName", "event", "$el", "contains", "target", "config", "options", "convertToChartOptions", "<PERSON><PERSON><PERSON>", "then", "response", "code", "msg", "chartData", "catch", "isArray", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "extractedData", "metrics", "fields", "filter", "f", "groupType", "currentType", "xAxisData", "map", "item", "field", "series", "categoriesSet", "Set", "s", "add", "category", "categories", "from", "seriesData", "find", "value", "title", "text", "tooltip", "trigger", "axisPointer", "legend", "xAxis", "yAxis", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "position", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "$refs", "chartRef", "init", "setOption", "resize"], "sources": ["src/components/ChartDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <div class=\"chart-content-wrapper\">\n      <!-- 图表显示区域 -->\n      <div class=\"chart-main\">\n        <div v-if=\"loading\" class=\"chart-loading\">\n          <i class=\"el-icon-loading\"></i>\n          <span>加载图表中...</span>\n        </div>\n        <div v-else-if=\"error\" class=\"chart-error\">\n          <i class=\"el-icon-warning\"></i>\n          <span>{{ errorMsg }}</span>\n        </div>\n        <div v-else ref=\"chartRef\" class=\"chart-canvas\"></div>\n      </div>\n\n      <!-- 图表切换下拉选择器 -->\n      <div v-if=\"showChartSwitcher\" class=\"chart-switcher-dropdown\">\n        <div class=\"chart-switcher-title\">图表切换</div>\n        <div class=\"chart-selector-wrapper\">\n          <div\n            class=\"chart-selector-trigger\"\n            @click=\"toggleDropdown\"\n            :class=\"{ 'active': dropdownVisible }\"\n          >\n            <div class=\"current-chart-icon\">\n              <i :class=\"getCurrentChartIcon()\"></i>\n            </div>\n            <span class=\"current-chart-name\">{{ getCurrentChartName() }}</span>\n            <i class=\"el-icon-arrow-down dropdown-arrow\" :class=\"{ 'rotated': dropdownVisible }\"></i>\n            <div v-if=\"switchingChart\" class=\"switching-loader\">\n              <i class=\"el-icon-loading\"></i>\n            </div>\n          </div>\n\n          <div v-show=\"dropdownVisible\" class=\"chart-dropdown-menu\">\n            <div\n              v-for=\"chartType in availableChartTypes\"\n              :key=\"chartType.type\"\n              :class=\"['chart-dropdown-item', {\n                'active': currentChartType === chartType.type\n              }]\"\n              @click=\"selectChartType(chartType.type)\"\n            >\n              <div class=\"chart-item-icon\">\n                <i :class=\"chartType.icon\"></i>\n              </div>\n              <span class=\"chart-item-name\">{{ chartType.name }}</span>\n              <i v-if=\"currentChartType === chartType.type\" class=\"el-icon-check chart-check-icon\"></i>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\n\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      localChartConfig: null, // 本地图表配置副本\n      dropdownVisible: false, // 下拉菜单显示状态\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'chart-icon-bar',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'chart-icon-line',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'chart-icon-pie',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'chart-icon-bar-horizontal',\n          compatible: true\n        }\n      }\n    }\n  },\n  computed: {\n    // 是否显示图表切换器\n    showChartSwitcher() {\n      return this.availableChartTypes.length > 1 && !this.loading && !this.error;\n    },\n\n    // 可用的图表类型列表\n    availableChartTypes() {\n      const types = [];\n\n      // 获取所有支持的图表类型\n      const allTypes = Object.keys(this.chartTypeConfig);\n\n      // 添加当前图表类型（排在第一位）\n      if (this.currentChartType && this.chartTypeConfig[this.currentChartType]) {\n        types.push({\n          type: this.currentChartType,\n          ...this.chartTypeConfig[this.currentChartType],\n          compatible: true // 当前类型肯定兼容\n        });\n      }\n\n      // 添加其他所有图表类型\n      allTypes.forEach(type => {\n        if (type !== this.currentChartType) {\n          types.push({\n            type: type,\n            ...this.chartTypeConfig[type],\n            compatible: true // 暂时都设为兼容\n          });\n        }\n      });\n\n      return types;\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler(newConfig) {\n        // 更新当前图表类型\n        if (newConfig && newConfig.type) {\n          this.currentChartType = newConfig.type;\n        }\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    // 初始化当前图表类型\n    if (this.chartConfig && this.chartConfig.type) {\n      this.currentChartType = this.chartConfig.type;\n    }\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n    // 添加点击外部关闭下拉菜单的监听\n    document.addEventListener('click', this.handleClickOutside);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 移除点击外部关闭下拉菜单的监听\n    document.removeEventListener('click', this.handleClickOutside);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    // 切换图表类型\n    async switchChartType(newType) {\n      if (newType === this.currentChartType || this.switchingChart) {\n        return;\n      }\n\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n\n      try {\n        this.switchingChart = true;\n\n        // 更新图表类型\n        this.currentChartType = newType;\n\n        // 创建新的图表配置，复用相同数据\n        const newChartConfig = {\n          ...this.chartConfig,\n          type: newType\n        };\n\n        // 触发重新渲染\n        this.$emit('chart-type-changed', newChartConfig);\n\n        // 使用本地副本进行渲染，避免直接修改 prop\n        this.localChartConfig = { ...newChartConfig };\n        this.renderChartWithConfig(this.localChartConfig);\n\n        this.$message.success(`已切换到${this.chartTypeConfig[newType]?.name}`);\n\n      } catch (error) {\n        console.error('图表切换失败:', error);\n        this.$message.error('图表切换失败: ' + error.message);\n      } finally {\n        this.switchingChart = false;\n      }\n    },\n\n    // 切换下拉菜单显示状态\n    toggleDropdown() {\n      this.dropdownVisible = !this.dropdownVisible;\n    },\n\n    // 选择图表类型\n    selectChartType(chartType) {\n      this.dropdownVisible = false;\n      this.switchChartType(chartType);\n    },\n\n    // 获取当前图表类型的图标\n    getCurrentChartIcon() {\n      return this.chartTypeConfig[this.currentChartType]?.icon || 'chart-icon-bar';\n    },\n\n    // 获取当前图表类型的名称\n    getCurrentChartName() {\n      return this.chartTypeConfig[this.currentChartType]?.name || '柱图';\n    },\n\n    // 点击外部关闭下拉菜单\n    handleClickOutside(event) {\n      if (!this.$el.contains(event.target)) {\n        this.dropdownVisible = false;\n      }\n    },\n\n    renderChart() {\n      this.renderChartWithConfig(this.chartConfig);\n    },\n\n    renderChartWithConfig(config) {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!config || !config.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', config);\n        return;\n      }\n\n      console.log('开始渲染图表，配置:', config);\n\n      // 检查chartConfig是否已包含完整数据\n      if (config.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(config);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n\n      // 如果没有数据，才调用API获取\n      getData(config).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        \n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        \n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        \n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        \n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n          \n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    \n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n\n      // 使用当前的图表类型（可能是切换后的类型）\n      const chartType = chartData.type || this.currentChartType || this.chartConfig.type;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch(chartType) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n        \n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n        \n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n        \n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        \n        console.log('提取的数据:', extractedData);\n        \n        // 根据当前图表类型调用不同的处理函数\n        const currentType = chartType || extractedData.type;\n        switch(currentType) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    \n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: (this.localChartConfig && this.localChartConfig.title) || this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const { data = [], metrics = [] } = chartData;\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const { data = [] } = chartData;\n      \n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n        \n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n        \n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    \n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.chart-container {\n  width: 100%;\n  position: relative;\n  max-width: 800px;\n  margin: 0 auto;\n  z-index: 1;\n  box-sizing: border-box;\n  isolation: isolate;\n}\n\n.chart-content-wrapper {\n  display: flex;\n  gap: 16px;\n  align-items: flex-start;\n}\n\n.chart-main {\n  flex: 1;\n  min-width: 0;\n}\n\n/* 图表切换侧边栏样式 */\n.chart-switcher-sidebar {\n  width: 180px;\n  background: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  padding: 16px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.chart-switcher-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #495057;\n  margin-bottom: 16px;\n  text-align: center;\n}\n\n.chart-type-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 8px;\n}\n\n.chart-type-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 60px;\n  height: 60px;\n  border: 2px solid #dee2e6;\n  border-radius: 8px;\n  background: #fff;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.chart-type-btn:hover {\n  border-color: #409eff;\n  background: #ecf5ff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);\n}\n\n.chart-type-btn.active {\n  border-color: #409eff;\n  background: #409eff;\n  color: #fff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n}\n\n.chart-type-btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none !important;\n}\n\n.chart-icon-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n\n.switching-loader {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.switching-loader i {\n  font-size: 12px;\n  animation: rotating 1s linear infinite;\n}\n\n@keyframes rotating {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n/* 自定义图表图标样式 */\n.chart-icon-bar::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M7 17h2v-7H7v7zm4 0h2V7h-2v10zm4 0h2v-4h-2v4z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-icon-line::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-icon-pie::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M11 2v20c-5.07-.5-9-4.79-9-10s3.93-9.5 9-10zm2.03 0v8.99H22c-.47-4.74-4.24-8.52-8.97-8.99zm0 11.01V22c4.74-.47 8.5-4.25 8.97-8.99h-8.97z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-icon-bar-horizontal::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z' transform='rotate(90 12 12)'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-canvas {\n  width: 100%;\n  height: 400px;\n  position: relative;\n  z-index: 1;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  background: #fff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.chart-loading, .chart-error {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  color: #909399;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 2;\n  background: rgba(255,255,255,0.9);\n}\n\n.chart-loading i, .chart-error i {\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.chart-error {\n  color: #F56C6C;\n}\n</style> "], "mappings": ";;;;;;;;;;;;;AA0DA,SAAAA,OAAA;AACA,YAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACA;IACAC,qBAAA;MACAJ,IAAA,EAAAK,KAAA;MACAF,OAAA,EAAAA,CAAA;IACA;EACA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;MACAC,aAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,gBAAA;MAAA;MACAC,eAAA;MAAA;MACA;MACAC,eAAA;QACA;UACAlB,IAAA;UACAmB,IAAA;UACAC,UAAA;QACA;QACA;UACApB,IAAA;UACAmB,IAAA;UACAC,UAAA;QACA;QACA;UACApB,IAAA;UACAmB,IAAA;UACAC,UAAA;QACA;QACA;UACApB,IAAA;UACAmB,IAAA;UACAC,UAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,kBAAA;MACA,YAAAC,mBAAA,CAAAC,MAAA,cAAAd,OAAA,UAAAC,KAAA;IACA;IAEA;IACAY,oBAAA;MACA,MAAAE,KAAA;;MAEA;MACA,MAAAC,QAAA,GAAAtB,MAAA,CAAAuB,IAAA,MAAAT,eAAA;;MAEA;MACA,SAAAH,gBAAA,SAAAG,eAAA,MAAAH,gBAAA;QACAU,KAAA,CAAAG,IAAA;UACAzB,IAAA,OAAAY,gBAAA;UACA,QAAAG,eAAA,MAAAH,gBAAA;UACAK,UAAA;QACA;MACA;;MAEA;MACAM,QAAA,CAAAG,OAAA,CAAA1B,IAAA;QACA,IAAAA,IAAA,UAAAY,gBAAA;UACAU,KAAA,CAAAG,IAAA;YACAzB,IAAA,EAAAA,IAAA;YACA,QAAAe,eAAA,CAAAf,IAAA;YACAiB,UAAA;UACA;QACA;MACA;MAEA,OAAAK,KAAA;IACA;EACA;EACAK,KAAA;IACA5B,WAAA;MACA6B,IAAA;MACAC,QAAAC,SAAA;QACA;QACA,IAAAA,SAAA,IAAAA,SAAA,CAAA9B,IAAA;UACA,KAAAY,gBAAA,GAAAkB,SAAA,CAAA9B,IAAA;QACA;QACA,KAAA+B,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACA,SAAAjC,WAAA,SAAAA,WAAA,CAAAC,IAAA;MACA,KAAAY,gBAAA,QAAAb,WAAA,CAAAC,IAAA;IACA;IACA,KAAA+B,WAAA;IACA;IACAE,MAAA,CAAAC,gBAAA,gBAAAC,WAAA;IACA;IACAC,QAAA,CAAAF,gBAAA,eAAAG,kBAAA;EACA;EACAC,cAAA;IACA;IACAL,MAAA,CAAAM,mBAAA,gBAAAJ,WAAA;IACA;IACAC,QAAA,CAAAG,mBAAA,eAAAF,kBAAA;IACA;IACA,SAAA3B,aAAA;MACA,KAAAA,aAAA,CAAA8B,OAAA;IACA;EACA;EACAC,OAAA;IACA;IACA,MAAAC,gBAAAC,OAAA;MACA,IAAAA,OAAA,UAAA/B,gBAAA,SAAAD,cAAA;QACA;MACA;MAEAiC,OAAA,CAAAC,GAAA,iBAAAjC,gBAAA,QAAA+B,OAAA;MAEA;QACA,KAAAhC,cAAA;;QAEA;QACA,KAAAC,gBAAA,GAAA+B,OAAA;;QAEA;QACA,MAAAG,cAAA;UACA,QAAA/C,WAAA;UACAC,IAAA,EAAA2C;QACA;;QAEA;QACA,KAAAI,KAAA,uBAAAD,cAAA;;QAEA;QACA,KAAAjC,gBAAA;UAAA,GAAAiC;QAAA;QACA,KAAAE,qBAAA,MAAAnC,gBAAA;QAEA,KAAAoC,QAAA,CAAAC,OAAA,aAAAnC,eAAA,CAAA4B,OAAA,GAAA9C,IAAA;MAEA,SAAAW,KAAA;QACAoC,OAAA,CAAApC,KAAA,YAAAA,KAAA;QACA,KAAAyC,QAAA,CAAAzC,KAAA,cAAAA,KAAA,CAAA2C,OAAA;MACA;QACA,KAAAxC,cAAA;MACA;IACA;IAEA;IACAyC,eAAA;MACA,KAAAtC,eAAA,SAAAA,eAAA;IACA;IAEA;IACAuC,gBAAAC,SAAA;MACA,KAAAxC,eAAA;MACA,KAAA4B,eAAA,CAAAY,SAAA;IACA;IAEA;IACAC,oBAAA;MACA,YAAAxC,eAAA,MAAAH,gBAAA,GAAAI,IAAA;IACA;IAEA;IACAwC,oBAAA;MACA,YAAAzC,eAAA,MAAAH,gBAAA,GAAAf,IAAA;IACA;IAEA;IACAwC,mBAAAoB,KAAA;MACA,UAAAC,GAAA,CAAAC,QAAA,CAAAF,KAAA,CAAAG,MAAA;QACA,KAAA9C,eAAA;MACA;IACA;IAEAiB,YAAA;MACA,KAAAiB,qBAAA,MAAAjD,WAAA;IACA;IAEAiD,sBAAAa,MAAA;MACA,KAAAtD,OAAA;MACA,KAAAC,KAAA;;MAEA;MACA,KAAAqD,MAAA,KAAAA,MAAA,CAAA7D,IAAA;QACA,KAAAO,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA;QACAmC,OAAA,CAAApC,KAAA,YAAAqD,MAAA;QACA;MACA;MAEAjB,OAAA,CAAAC,GAAA,eAAAgB,MAAA;;MAEA;MACA,IAAAA,MAAA,CAAAvD,IAAA;QACAsC,OAAA,CAAAC,GAAA;QACA,KAAAtC,OAAA;QAEA;UACA;UACA,MAAAuD,OAAA,QAAAC,qBAAA,CAAAF,MAAA;UACAjB,OAAA,CAAAC,GAAA,kBAAAiB,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAtD,KAAA;UACAoC,OAAA,CAAApC,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAA2C,OAAA;QACA;QACA;MACA;;MAEA;MACAxD,OAAA,CAAAkE,MAAA,EAAAI,IAAA,CAAAC,QAAA;QACAtB,OAAA,CAAAC,GAAA,eAAAqB,QAAA;QACA,KAAA3D,OAAA;QAEA,KAAA2D,QAAA;UACA,KAAA1D,KAAA;UACA,KAAAC,QAAA;UACAmC,OAAA,CAAApC,KAAA;UACA;QACA;QAEA,IAAA0D,QAAA,CAAAC,IAAA;UACA,KAAA3D,KAAA;UACA,KAAAC,QAAA,GAAAyD,QAAA,CAAAE,GAAA,iBAAAF,QAAA,CAAAC,IAAA;UACAvB,OAAA,CAAApC,KAAA,cAAA0D,QAAA;UACA;QACA;QAEA,MAAAG,SAAA,GAAAH,QAAA,CAAA5D,IAAA;QACAsC,OAAA,CAAAC,GAAA,cAAAwB,SAAA;QAEA;UACA;UACA,MAAAP,OAAA,QAAAC,qBAAA,CAAAM,SAAA;UACAzB,OAAA,CAAAC,GAAA,kBAAAiB,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAtD,KAAA;UACAoC,OAAA,CAAApC,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAA2C,OAAA;QACA;MACA,GAAAmB,KAAA,CAAA9D,KAAA;QACAoC,OAAA,CAAApC,KAAA,cAAAA,KAAA;QACA,KAAAD,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA,kBAAAD,KAAA,CAAA2C,OAAA;MACA;IACA;IAEA;IACAY,sBAAAM,SAAA;MACAzB,OAAA,CAAAC,GAAA,yBAAAwB,SAAA;;MAEA;MACA,MAAAf,SAAA,GAAAe,SAAA,CAAArE,IAAA,SAAAY,gBAAA,SAAAb,WAAA,CAAAC,IAAA;;MAEA;MACA,IAAAqE,SAAA,CAAA/D,IAAA,IAAAD,KAAA,CAAAkE,OAAA,CAAAF,SAAA,CAAA/D,IAAA;QACAsC,OAAA,CAAAC,GAAA;QACA;QACA,QAAAS,SAAA;UACA;YACA,YAAAkB,kBAAA,CAAAH,SAAA;UACA;YACA,YAAAI,mBAAA,CAAAJ,SAAA;UACA;YACA,YAAAK,kBAAA,CAAAL,SAAA;UACA;YACA,YAAAM,uBAAA,CAAAN,SAAA;UACA;YACA,YAAAO,iBAAA;QACA;MACA;QACAhC,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAAgC,aAAA;;QAEA;QACA,IAAAR,SAAA,CAAA/D,IAAA,IAAA+D,SAAA,CAAA/D,IAAA,CAAAA,IAAA;UACAsC,OAAA,CAAAC,GAAA;UACAgC,aAAA;YACA7E,IAAA,EAAAqE,SAAA,CAAA/D,IAAA,CAAAN,IAAA,SAAAD,WAAA,CAAAC,IAAA;YACAM,IAAA,EAAA+D,SAAA,CAAA/D,IAAA,CAAAA,IAAA;YACAwE,OAAA,EAAAT,SAAA,CAAA/D,IAAA,CAAAyE,MAAA,GAAAV,SAAA,CAAA/D,IAAA,CAAAyE,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;UACA;QACA;;QAEA;QACA,KAAAL,aAAA;UACAjC,OAAA,CAAAC,GAAA;UACA,YAAA+B,iBAAA;QACA;QAEAhC,OAAA,CAAAC,GAAA,WAAAgC,aAAA;;QAEA;QACA,MAAAM,WAAA,GAAA7B,SAAA,IAAAuB,aAAA,CAAA7E,IAAA;QACA,QAAAmF,WAAA;UACA;YACA,YAAAX,kBAAA,CAAAK,aAAA;UACA;YACA,YAAAJ,mBAAA,CAAAI,aAAA;UACA;YACA,YAAAH,kBAAA,CAAAG,aAAA;UACA;YACA,YAAAF,uBAAA,CAAAE,aAAA;UACA;YACA,YAAAD,iBAAA;QACA;MACA;IACA;IAEA;IACAJ,mBAAAH,SAAA;MACAzB,OAAA,CAAAC,GAAA,gBAAAwB,SAAA;;MAEA;MACA,IAAA/D,IAAA;MACA,IAAAwE,OAAA;;MAEA;MACA,IAAAT,SAAA,CAAA/D,IAAA,IAAAD,KAAA,CAAAkE,OAAA,CAAAF,SAAA,CAAA/D,IAAA;QACAA,IAAA,GAAA+D,SAAA,CAAA/D,IAAA;QACAwE,OAAA,GAAAT,SAAA,CAAAS,OAAA;MACA;MACA;MAAA,KACA,IAAAT,SAAA,CAAA/D,IAAA,IAAA+D,SAAA,CAAA/D,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAAkE,OAAA,CAAAF,SAAA,CAAA/D,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA+D,SAAA,CAAA/D,IAAA,CAAAA,IAAA;QACAwE,OAAA,GAAAT,SAAA,CAAA/D,IAAA,CAAAyE,MAAA,GACAV,SAAA,CAAA/D,IAAA,CAAAyE,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAtC,OAAA,CAAAC,GAAA,YAAAvC,IAAA;MACAsC,OAAA,CAAAC,GAAA,QAAAiC,OAAA;;MAEA;MACA,MAAAM,SAAA,GAAA9E,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAzF,IAAA;;MAEA;MACA,MAAA2F,MAAA;MACA,IAAAlF,IAAA,CAAAe,MAAA,QAAAf,IAAA,IAAAkF,MAAA;QACA;QACA,MAAAC,aAAA,OAAAC,GAAA;QACApF,IAAA,CAAAoB,OAAA,CAAA4D,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAA9D,OAAA,CAAAiE,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAzF,KAAA,CAAA0F,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAApE,OAAA,CAAAmE,QAAA;UACA,MAAAG,UAAA,GAAA1F,IAAA,CAAA+E,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAS,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAL,MAAA,GAAAA,MAAA,CAAAU,KAAA;UACA;UAEAV,MAAA,CAAA/D,IAAA;YACA5B,IAAA,EAAAgG,QAAA;YACA7F,IAAA;YACAM,IAAA,EAAA0F;UACA;QACA;MACA;QACA;QACAR,MAAA,CAAA/D,IAAA;UACA5B,IAAA,EAAAiF,OAAA,KAAAjF,IAAA;UACAG,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAY,KAAA;QACA;MACA;MAEA;QACAC,KAAA;UACAC,IAAA,OAAAvF,gBAAA,SAAAA,gBAAA,CAAAsF,KAAA,SAAApG,WAAA,CAAAoG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACAvG,IAAA;UACA;QACA;QACAwG,MAAA;UACAlG,IAAA,EAAAkF,MAAA,CAAAH,GAAA,CAAAM,CAAA,IAAAA,CAAA,CAAA9F,IAAA;QACA;QACA4G,KAAA;UACAzG,IAAA;UACAM,IAAA,EAAA8E;QACA;QACAsB,KAAA;UACA1G,IAAA;QACA;QACAwF,MAAA,EAAAA;MACA;IACA;IAEA;IACAf,oBAAAJ,SAAA;MACA;QAAA/D,IAAA;QAAAwE,OAAA;MAAA,IAAAT,SAAA;;MAEA;MACA,MAAAe,SAAA,GAAA9E,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA;;MAEA;MACA,MAAAC,MAAA;MACA,IAAAlF,IAAA,CAAAe,MAAA,QAAAf,IAAA,IAAAkF,MAAA;QACA;QACA,MAAAC,aAAA,OAAAC,GAAA;QACApF,IAAA,CAAAoB,OAAA,CAAA4D,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAA9D,OAAA,CAAAiE,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAzF,KAAA,CAAA0F,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAApE,OAAA,CAAAmE,QAAA;UACA,MAAAG,UAAA,GAAA1F,IAAA,CAAA+E,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAS,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAL,MAAA,GAAAA,MAAA,CAAAU,KAAA;UACA;UAEAV,MAAA,CAAA/D,IAAA;YACA5B,IAAA,EAAAgG,QAAA;YACA7F,IAAA;YACAM,IAAA,EAAA0F;UACA;QACA;MACA;QACA;QACAR,MAAA,CAAA/D,IAAA;UACA5B,IAAA,EAAAiF,OAAA,KAAAjF,IAAA;UACAG,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAY,KAAA;QACA;MACA;MAEA;QACAC,KAAA;UACAC,IAAA,OAAArG,WAAA,CAAAoG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAE,MAAA;UACAlG,IAAA,EAAAkF,MAAA,CAAAH,GAAA,CAAAM,CAAA,IAAAA,CAAA,CAAA9F,IAAA;QACA;QACA4G,KAAA;UACAzG,IAAA;UACAM,IAAA,EAAA8E;QACA;QACAsB,KAAA;UACA1G,IAAA;QACA;QACAwF,MAAA,EAAAA;MACA;IACA;IAEA;IACAd,mBAAAL,SAAA;MACA;QAAA/D,IAAA;MAAA,IAAA+D,SAAA;MAEA,MAAA2B,UAAA,GAAA1F,IAAA,CAAA+E,GAAA,CAAAC,IAAA;QACAzF,IAAA,EAAAyF,IAAA,CAAAC,KAAA;QACAW,KAAA,EAAAZ,IAAA,CAAAY;MACA;MAEA;QACAC,KAAA;UACAC,IAAA,OAAArG,WAAA,CAAAoG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAK,SAAA;QACA;QACAH,MAAA;UACAI,MAAA;UACAC,KAAA;UACAC,GAAA;UACAxG,IAAA,EAAA0F,UAAA,CAAAX,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAzF,IAAA;QACA;QACA2F,MAAA;UACA3F,IAAA;UACAG,IAAA;UACA+G,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACAH,KAAA;cACAC,IAAA;cACAG,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAL,IAAA;UACA;UACA5G,IAAA,EAAA0F;QACA;MACA;IACA;IAEA;IACArB,wBAAAN,SAAA;MACAzB,OAAA,CAAAC,GAAA,gBAAAwB,SAAA;;MAEA;MACA,IAAA/D,IAAA;MACA,IAAAwE,OAAA;;MAEA;MACA,IAAAT,SAAA,CAAA/D,IAAA,IAAAD,KAAA,CAAAkE,OAAA,CAAAF,SAAA,CAAA/D,IAAA;QACAA,IAAA,GAAA+D,SAAA,CAAA/D,IAAA;QACAwE,OAAA,GAAAT,SAAA,CAAAS,OAAA;MACA;MACA;MAAA,KACA,IAAAT,SAAA,CAAA/D,IAAA,IAAA+D,SAAA,CAAA/D,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAAkE,OAAA,CAAAF,SAAA,CAAA/D,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA+D,SAAA,CAAA/D,IAAA,CAAAA,IAAA;QACAwE,OAAA,GAAAT,SAAA,CAAA/D,IAAA,CAAAyE,MAAA,GACAV,SAAA,CAAA/D,IAAA,CAAAyE,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAtC,OAAA,CAAAC,GAAA,YAAAvC,IAAA;MACAsC,OAAA,CAAAC,GAAA,QAAAiC,OAAA;;MAEA;MACA,MAAA0C,SAAA,GAAAlH,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAzF,IAAA;;MAEA;MACA,MAAA2F,MAAA;MACA,IAAAlF,IAAA,CAAAe,MAAA,QAAAf,IAAA,IAAAkF,MAAA;QACA;QACA,MAAAC,aAAA,OAAAC,GAAA;QACApF,IAAA,CAAAoB,OAAA,CAAA4D,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAA9D,OAAA,CAAAiE,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAzF,KAAA,CAAA0F,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAApE,OAAA,CAAAmE,QAAA;UACA,MAAAG,UAAA,GAAA1F,IAAA,CAAA+E,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAS,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAL,MAAA,GAAAA,MAAA,CAAAU,KAAA;UACA;UAEAV,MAAA,CAAA/D,IAAA;YACA5B,IAAA,EAAAgG,QAAA;YACA7F,IAAA;YAAA;YACAM,IAAA,EAAA0F;UACA;QACA;MACA;QACA;QACAR,MAAA,CAAA/D,IAAA;UACA5B,IAAA,EAAAiF,OAAA,KAAAjF,IAAA;UACAG,IAAA;UAAA;UACAM,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAY,KAAA;QACA;MACA;MAEA;QACAC,KAAA;UACAC,IAAA,OAAArG,WAAA,CAAAoG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACAvG,IAAA;UACA;QACA;QACAwG,MAAA;UACAlG,IAAA,EAAAkF,MAAA,CAAAH,GAAA,CAAAM,CAAA,IAAAA,CAAA,CAAA9F,IAAA;QACA;QACA;QACA4G,KAAA;UACAzG,IAAA;QACA;QACA0G,KAAA;UACA1G,IAAA;UAAA;UACAM,IAAA,EAAAkH;QACA;QACAhC,MAAA,EAAAA;MACA;IACA;IAEA;IACAZ,kBAAA;MACAhC,OAAA,CAAAC,GAAA;MACA;QACAsD,KAAA;UACAC,IAAA,OAAArG,WAAA,CAAAoG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAG,KAAA;UACAzG,IAAA;UACAM,IAAA;QACA;QACAoG,KAAA;UACA1G,IAAA;QACA;QACAwF,MAAA;UACAxF,IAAA,OAAAD,WAAA,CAAAC,IAAA;UACAM,IAAA;QACA;MACA;IACA;IAEA;IACA0D,UAAAF,OAAA;MACA;QACAlB,OAAA,CAAAC,GAAA;QACA,UAAA4E,KAAA,CAAAC,QAAA;UACA9E,OAAA,CAAApC,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA;UACA;QACA;;QAEA;QACA,SAAAC,aAAA;UACA,KAAAA,aAAA,CAAA8B,OAAA;QACA;;QAEA;QACA,KAAA9B,aAAA,GAAAd,OAAA,CAAA+H,IAAA,MAAAF,KAAA,CAAAC,QAAA;QACA,KAAAhH,aAAA,CAAAkH,SAAA,CAAA9D,OAAA;QACAlB,OAAA,CAAAC,GAAA;MACA,SAAArC,KAAA;QACAoC,OAAA,CAAApC,KAAA,YAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,gBAAAD,KAAA,CAAA2C,OAAA;MACA;IACA;IAEA;IACAhB,YAAA;MACA,SAAAzB,aAAA;QACA,KAAAA,aAAA,CAAAmH,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}