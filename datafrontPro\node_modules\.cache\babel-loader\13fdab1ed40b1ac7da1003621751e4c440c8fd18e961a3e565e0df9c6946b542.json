{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { bind } from 'zrender/lib/core/util.js';\nfunction dataToCoordSize(dataSize, dataItem) {\n  // dataItem is necessary in log axis.\n  var axis = this.getAxis();\n  var val = dataItem instanceof Array ? dataItem[0] : dataItem;\n  var halfSize = (dataSize instanceof Array ? dataSize[0] : dataSize) / 2;\n  return axis.type === 'category' ? axis.getBandWidth() : Math.abs(axis.dataToCoord(val - halfSize) - axis.dataToCoord(val + halfSize));\n}\nexport default function singlePrepareCustom(coordSys) {\n  var rect = coordSys.getRect();\n  return {\n    coordSys: {\n      type: 'singleAxis',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    },\n    api: {\n      coord: function (val) {\n        // do not provide \"out\" param\n        return coordSys.dataToPoint(val);\n      },\n      size: bind(dataToCoordSize, coordSys)\n    }\n  };\n}", "map": {"version": 3, "names": ["bind", "dataToCoordSize", "dataSize", "dataItem", "axis", "getAxis", "val", "Array", "halfSize", "type", "getBandWidth", "Math", "abs", "dataToCoord", "singlePrepareCustom", "coordSys", "rect", "getRect", "x", "y", "width", "height", "api", "coord", "dataToPoint", "size"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/coord/single/prepareCustom.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { bind } from 'zrender/lib/core/util.js';\nfunction dataToCoordSize(dataSize, dataItem) {\n  // dataItem is necessary in log axis.\n  var axis = this.getAxis();\n  var val = dataItem instanceof Array ? dataItem[0] : dataItem;\n  var halfSize = (dataSize instanceof Array ? dataSize[0] : dataSize) / 2;\n  return axis.type === 'category' ? axis.getBandWidth() : Math.abs(axis.dataToCoord(val - halfSize) - axis.dataToCoord(val + halfSize));\n}\nexport default function singlePrepareCustom(coordSys) {\n  var rect = coordSys.getRect();\n  return {\n    coordSys: {\n      type: 'singleAxis',\n      x: rect.x,\n      y: rect.y,\n      width: rect.width,\n      height: rect.height\n    },\n    api: {\n      coord: function (val) {\n        // do not provide \"out\" param\n        return coordSys.dataToPoint(val);\n      },\n      size: bind(dataToCoordSize, coordSys)\n    }\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,QAAQ,0BAA0B;AAC/C,SAASC,eAAeA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EAC3C;EACA,IAAIC,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;EACzB,IAAIC,GAAG,GAAGH,QAAQ,YAAYI,KAAK,GAAGJ,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ;EAC5D,IAAIK,QAAQ,GAAG,CAACN,QAAQ,YAAYK,KAAK,GAAGL,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,IAAI,CAAC;EACvE,OAAOE,IAAI,CAACK,IAAI,KAAK,UAAU,GAAGL,IAAI,CAACM,YAAY,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACR,IAAI,CAACS,WAAW,CAACP,GAAG,GAAGE,QAAQ,CAAC,GAAGJ,IAAI,CAACS,WAAW,CAACP,GAAG,GAAGE,QAAQ,CAAC,CAAC;AACvI;AACA,eAAe,SAASM,mBAAmBA,CAACC,QAAQ,EAAE;EACpD,IAAIC,IAAI,GAAGD,QAAQ,CAACE,OAAO,CAAC,CAAC;EAC7B,OAAO;IACLF,QAAQ,EAAE;MACRN,IAAI,EAAE,YAAY;MAClBS,CAAC,EAAEF,IAAI,CAACE,CAAC;MACTC,CAAC,EAAEH,IAAI,CAACG,CAAC;MACTC,KAAK,EAAEJ,IAAI,CAACI,KAAK;MACjBC,MAAM,EAAEL,IAAI,CAACK;IACf,CAAC;IACDC,GAAG,EAAE;MACHC,KAAK,EAAE,SAAAA,CAAUjB,GAAG,EAAE;QACpB;QACA,OAAOS,QAAQ,CAACS,WAAW,CAAClB,GAAG,CAAC;MAClC,CAAC;MACDmB,IAAI,EAAEzB,IAAI,CAACC,eAAe,EAAEc,QAAQ;IACtC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}