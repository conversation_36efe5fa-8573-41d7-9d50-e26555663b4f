{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null\n    };\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler() {\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    renderChart() {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n      console.log('开始渲染图表，配置:', this.chartConfig);\n\n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartConfig);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n\n      // 如果没有数据，才调用API获取\n      getData(this.chartConfig).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch (this.chartConfig.type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n\n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n\n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n\n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        console.log('提取的数据:', extractedData);\n\n        // 根据提取的数据类型调用不同的处理函数\n        switch (extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const {\n        data = [],\n        metrics = []\n      } = chartData;\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const {\n        data = []\n      } = chartData;\n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getData", "echarts", "name", "props", "chartConfig", "type", "Object", "required", "default", "data", "loading", "error", "errorMsg", "chartInstance", "watch", "deep", "handler", "<PERSON><PERSON><PERSON>", "mounted", "window", "addEventListener", "resizeChart", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "dispose", "methods", "console", "log", "options", "convertToChartOptions", "<PERSON><PERSON><PERSON>", "message", "then", "response", "code", "msg", "chartData", "catch", "Array", "isArray", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getDefaultOptions", "extractedData", "metrics", "fields", "filter", "f", "groupType", "xAxisData", "map", "item", "field", "series", "length", "categoriesSet", "Set", "for<PERSON>ach", "s", "add", "category", "categories", "from", "seriesData", "find", "value", "push", "title", "text", "tooltip", "trigger", "axisPointer", "legend", "xAxis", "yAxis", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "position", "emphasis", "fontSize", "fontWeight", "labelLine", "$refs", "chartRef", "init", "setOption", "resize"], "sources": ["src/components/ChartDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <div v-if=\"loading\" class=\"chart-loading\">\n      <i class=\"el-icon-loading\"></i>\n      <span>加载图表中...</span>\n    </div>\n    <div v-else-if=\"error\" class=\"chart-error\">\n      <i class=\"el-icon-warning\"></i>\n      <span>{{ errorMsg }}</span>\n    </div>\n    <div v-else ref=\"chartRef\" class=\"chart-canvas\"></div>\n  </div>\n</template>\n\n<script>\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\n\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler() {\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    renderChart() {\n      this.loading = true;\n      this.error = false;\n      \n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n      \n      console.log('开始渲染图表，配置:', this.chartConfig);\n      \n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n        \n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartConfig);\n          console.log('生成的echarts选项:', options);\n          \n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n      \n      // 如果没有数据，才调用API获取\n      getData(this.chartConfig).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        \n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        \n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        \n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        \n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n          \n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    \n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n      \n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch(this.chartConfig.type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n        \n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n        \n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n        \n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        \n        console.log('提取的数据:', extractedData);\n        \n        // 根据提取的数据类型调用不同的处理函数\n        switch(extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    \n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const { data = [], metrics = [] } = chartData;\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const { data = [] } = chartData;\n      \n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n        \n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n        \n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    \n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.chart-container {\n  width: 100%;\n  height: 400px;\n  position: relative;\n}\n\n.chart-canvas {\n  width: 100%;\n  height: 100%;\n\n}\n\n.chart-loading, .chart-error {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  color: #909399;\n}\n\n.chart-loading i, .chart-error i {\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.chart-error {\n  color: #F56C6C;\n}\n</style> "], "mappings": ";;;;;;;;;;;;;AAeA,SAAAA,OAAA;AACA,YAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,EAAAA,CAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACAV,WAAA;MACAW,IAAA;MACAC,QAAA;QACA,KAAAC,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAD,WAAA;IACA;IACAE,MAAA,CAAAC,gBAAA,gBAAAC,WAAA;EACA;EACAC,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,WAAA;IACA;IACA,SAAAR,aAAA;MACA,KAAAA,aAAA,CAAAW,OAAA;IACA;EACA;EACAC,OAAA;IACAR,YAAA;MACA,KAAAP,OAAA;MACA,KAAAC,KAAA;;MAEA;MACA,UAAAP,WAAA,UAAAA,WAAA,CAAAC,IAAA;QACA,KAAAK,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA;QACAc,OAAA,CAAAf,KAAA,iBAAAP,WAAA;QACA;MACA;MAEAsB,OAAA,CAAAC,GAAA,oBAAAvB,WAAA;;MAEA;MACA,SAAAA,WAAA,CAAAK,IAAA;QACAiB,OAAA,CAAAC,GAAA;QACA,KAAAjB,OAAA;QAEA;UACA;UACA,MAAAkB,OAAA,QAAAC,qBAAA,MAAAzB,WAAA;UACAsB,OAAA,CAAAC,GAAA,kBAAAC,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAjB,KAAA;UACAe,OAAA,CAAAf,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAoB,OAAA;QACA;QACA;MACA;;MAEA;MACA/B,OAAA,MAAAI,WAAA,EAAA4B,IAAA,CAAAC,QAAA;QACAP,OAAA,CAAAC,GAAA,eAAAM,QAAA;QACA,KAAAvB,OAAA;QAEA,KAAAuB,QAAA;UACA,KAAAtB,KAAA;UACA,KAAAC,QAAA;UACAc,OAAA,CAAAf,KAAA;UACA;QACA;QAEA,IAAAsB,QAAA,CAAAC,IAAA;UACA,KAAAvB,KAAA;UACA,KAAAC,QAAA,GAAAqB,QAAA,CAAAE,GAAA,iBAAAF,QAAA,CAAAC,IAAA;UACAR,OAAA,CAAAf,KAAA,cAAAsB,QAAA;UACA;QACA;QAEA,MAAAG,SAAA,GAAAH,QAAA,CAAAxB,IAAA;QACAiB,OAAA,CAAAC,GAAA,cAAAS,SAAA;QAEA;UACA;UACA,MAAAR,OAAA,QAAAC,qBAAA,CAAAO,SAAA;UACAV,OAAA,CAAAC,GAAA,kBAAAC,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAjB,KAAA;UACAe,OAAA,CAAAf,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAoB,OAAA;QACA;MACA,GAAAM,KAAA,CAAA1B,KAAA;QACAe,OAAA,CAAAf,KAAA,cAAAA,KAAA;QACA,KAAAD,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAoB,OAAA;MACA;IACA;IAEA;IACAF,sBAAAO,SAAA;MACAV,OAAA,CAAAC,GAAA,yBAAAS,SAAA;;MAEA;MACA,IAAAA,SAAA,CAAA3B,IAAA,IAAA6B,KAAA,CAAAC,OAAA,CAAAH,SAAA,CAAA3B,IAAA;QACAiB,OAAA,CAAAC,GAAA;QACA;QACA,aAAAvB,WAAA,CAAAC,IAAA;UACA;YACA,YAAAmC,kBAAA,CAAAJ,SAAA;UACA;YACA,YAAAK,mBAAA,CAAAL,SAAA;UACA;YACA,YAAAM,kBAAA,CAAAN,SAAA;UACA;YACA,YAAAO,iBAAA;QACA;MACA;QACAjB,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAAiB,aAAA;;QAEA;QACA,IAAAR,SAAA,CAAA3B,IAAA,IAAA2B,SAAA,CAAA3B,IAAA,CAAAA,IAAA;UACAiB,OAAA,CAAAC,GAAA;UACAiB,aAAA;YACAvC,IAAA,EAAA+B,SAAA,CAAA3B,IAAA,CAAAJ,IAAA,SAAAD,WAAA,CAAAC,IAAA;YACAI,IAAA,EAAA2B,SAAA,CAAA3B,IAAA,CAAAA,IAAA;YACAoC,OAAA,EAAAT,SAAA,CAAA3B,IAAA,CAAAqC,MAAA,GAAAV,SAAA,CAAA3B,IAAA,CAAAqC,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;UACA;QACA;;QAEA;QACA,KAAAL,aAAA;UACAlB,OAAA,CAAAC,GAAA;UACA,YAAAgB,iBAAA;QACA;QAEAjB,OAAA,CAAAC,GAAA,WAAAiB,aAAA;;QAEA;QACA,QAAAA,aAAA,CAAAvC,IAAA;UACA;YACA,YAAAmC,kBAAA,CAAAI,aAAA;UACA;YACA,YAAAH,mBAAA,CAAAG,aAAA;UACA;YACA,YAAAF,kBAAA,CAAAE,aAAA;UACA;YACA,YAAAD,iBAAA;QACA;MACA;IACA;IAEA;IACAH,mBAAAJ,SAAA;MACAV,OAAA,CAAAC,GAAA,gBAAAS,SAAA;;MAEA;MACA,IAAA3B,IAAA;MACA,IAAAoC,OAAA;;MAEA;MACA,IAAAT,SAAA,CAAA3B,IAAA,IAAA6B,KAAA,CAAAC,OAAA,CAAAH,SAAA,CAAA3B,IAAA;QACAA,IAAA,GAAA2B,SAAA,CAAA3B,IAAA;QACAoC,OAAA,GAAAT,SAAA,CAAAS,OAAA;MACA;MACA;MAAA,KACA,IAAAT,SAAA,CAAA3B,IAAA,IAAA2B,SAAA,CAAA3B,IAAA,CAAAA,IAAA,IAAA6B,KAAA,CAAAC,OAAA,CAAAH,SAAA,CAAA3B,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA2B,SAAA,CAAA3B,IAAA,CAAAA,IAAA;QACAoC,OAAA,GAAAT,SAAA,CAAA3B,IAAA,CAAAqC,MAAA,GACAV,SAAA,CAAA3B,IAAA,CAAAqC,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAvB,OAAA,CAAAC,GAAA,YAAAlB,IAAA;MACAiB,OAAA,CAAAC,GAAA,QAAAkB,OAAA;;MAEA;MACA,MAAAK,SAAA,GAAAzC,IAAA,CAAA0C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAlD,IAAA;;MAEA;MACA,MAAAoD,MAAA;MACA,IAAA7C,IAAA,CAAA8C,MAAA,QAAA9C,IAAA,IAAA6C,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACAhD,IAAA,CAAAiD,OAAA,CAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAI,OAAA,CAAAC,CAAA,IAAAH,aAAA,CAAAI,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAxB,KAAA,CAAAyB,IAAA,CAAAP,aAAA;QACAM,UAAA,CAAAJ,OAAA,CAAAG,QAAA;UACA,MAAAG,UAAA,GAAAvD,IAAA,CAAA0C,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAW,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAP,MAAA,GAAAA,MAAA,CAAAY,KAAA;UACA;UAEAZ,MAAA,CAAAa,IAAA;YACAjE,IAAA,EAAA2D,QAAA;YACAxD,IAAA;YACAI,IAAA,EAAAuD;UACA;QACA;MACA;QACA;QACAV,MAAA,CAAAa,IAAA;UACAjE,IAAA,EAAA2C,OAAA,KAAA3C,IAAA;UACAG,IAAA;UACAI,IAAA,EAAAA,IAAA,CAAA0C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAc,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAjE,WAAA,CAAAgE,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACAnE,IAAA;UACA;QACA;QACAoE,MAAA;UACAhE,IAAA,EAAA6C,MAAA,CAAAH,GAAA,CAAAQ,CAAA,IAAAA,CAAA,CAAAzD,IAAA;QACA;QACAwE,KAAA;UACArE,IAAA;UACAI,IAAA,EAAAyC;QACA;QACAyB,KAAA;UACAtE,IAAA;QACA;QACAiD,MAAA,EAAAA;MACA;IACA;IAEA;IACAb,oBAAAL,SAAA;MACA;QAAA3B,IAAA;QAAAoC,OAAA;MAAA,IAAAT,SAAA;;MAEA;MACA,MAAAc,SAAA,GAAAzC,IAAA,CAAA0C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA;;MAEA;MACA,MAAAC,MAAA;MACA,IAAA7C,IAAA,CAAA8C,MAAA,QAAA9C,IAAA,IAAA6C,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACAhD,IAAA,CAAAiD,OAAA,CAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAI,OAAA,CAAAC,CAAA,IAAAH,aAAA,CAAAI,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAxB,KAAA,CAAAyB,IAAA,CAAAP,aAAA;QACAM,UAAA,CAAAJ,OAAA,CAAAG,QAAA;UACA,MAAAG,UAAA,GAAAvD,IAAA,CAAA0C,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAW,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAP,MAAA,GAAAA,MAAA,CAAAY,KAAA;UACA;UAEAZ,MAAA,CAAAa,IAAA;YACAjE,IAAA,EAAA2D,QAAA;YACAxD,IAAA;YACAI,IAAA,EAAAuD;UACA;QACA;MACA;QACA;QACAV,MAAA,CAAAa,IAAA;UACAjE,IAAA,EAAA2C,OAAA,KAAA3C,IAAA;UACAG,IAAA;UACAI,IAAA,EAAAA,IAAA,CAAA0C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAc,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAjE,WAAA,CAAAgE,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAE,MAAA;UACAhE,IAAA,EAAA6C,MAAA,CAAAH,GAAA,CAAAQ,CAAA,IAAAA,CAAA,CAAAzD,IAAA;QACA;QACAwE,KAAA;UACArE,IAAA;UACAI,IAAA,EAAAyC;QACA;QACAyB,KAAA;UACAtE,IAAA;QACA;QACAiD,MAAA,EAAAA;MACA;IACA;IAEA;IACAZ,mBAAAN,SAAA;MACA;QAAA3B,IAAA;MAAA,IAAA2B,SAAA;MAEA,MAAA4B,UAAA,GAAAvD,IAAA,CAAA0C,GAAA,CAAAC,IAAA;QACAlD,IAAA,EAAAkD,IAAA,CAAAC,KAAA;QACAa,KAAA,EAAAd,IAAA,CAAAc;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAjE,WAAA,CAAAgE,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAK,SAAA;QACA;QACAH,MAAA;UACAI,MAAA;UACAC,KAAA;UACAC,GAAA;UACAtE,IAAA,EAAAuD,UAAA,CAAAb,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAlD,IAAA;QACA;QACAoD,MAAA;UACApD,IAAA;UACAG,IAAA;UACA2E,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACAH,KAAA;cACAC,IAAA;cACAG,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAL,IAAA;UACA;UACA1E,IAAA,EAAAuD;QACA;MACA;IACA;IAEA;IACArB,kBAAA;MACAjB,OAAA,CAAAC,GAAA;MACA;QACAyC,KAAA;UACAC,IAAA,OAAAjE,WAAA,CAAAgE,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAG,KAAA;UACArE,IAAA;UACAI,IAAA;QACA;QACAkE,KAAA;UACAtE,IAAA;QACA;QACAiD,MAAA;UACAjD,IAAA,OAAAD,WAAA,CAAAC,IAAA;UACAI,IAAA;QACA;MACA;IACA;IAEA;IACAqB,UAAAF,OAAA;MACA;QACAF,OAAA,CAAAC,GAAA;QACA,UAAA8D,KAAA,CAAAC,QAAA;UACAhE,OAAA,CAAAf,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA;UACA;QACA;;QAEA;QACA,SAAAC,aAAA;UACA,KAAAA,aAAA,CAAAW,OAAA;QACA;;QAEA;QACA,KAAAX,aAAA,GAAAZ,OAAA,CAAA0F,IAAA,MAAAF,KAAA,CAAAC,QAAA;QACA,KAAA7E,aAAA,CAAA+E,SAAA,CAAAhE,OAAA;QACAF,OAAA,CAAAC,GAAA;MACA,SAAAhB,KAAA;QACAe,OAAA,CAAAf,KAAA,YAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,gBAAAD,KAAA,CAAAoB,OAAA;MACA;IACA;IAEA;IACAV,YAAA;MACA,SAAAR,aAAA;QACA,KAAAA,aAAA,CAAAgF,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}