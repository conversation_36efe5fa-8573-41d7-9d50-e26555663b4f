<!-- Generated by SpecStory -->

# Untitled (2025-07-16 07:58Z)

_**User**_

这个实际返回的数据集列表的响应结果，然后你把前端展示的，目前可用数据展示出来一排，这一排都是数据集。和对话宽度一致就好。然后在点击选择数据后，右边展示的抽屉能弹出来也能展示数据集，这个时候还是不是具体的数据集详情。也是数据集。这个弹出来的要显示所有的数据集。最后在弹出来的数据集中，选择一个点击后，能展示出来具体的数据和字段详情。也就是截图的样子。大概样子你看看我给你的截图。我不要树形展示，而是直接展示具体的数据集。然后详情是点击具体的数据集预览后才可以看到字段和数据详情。然后你把代码给我写一下。只是在现有的逻辑上接收参数来去做展示而已。不要改我的给你说的之外的代码。先梳理需求在梳理代码内容。需求梳理


---

_**Assistant**_



---

