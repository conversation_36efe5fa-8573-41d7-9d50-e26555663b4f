{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nvar _assign = require(\"../core-js/object/assign\");\nvar _assign2 = _interopRequireDefault(_assign);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nexports.default = _assign2.default || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};", "map": {"version": 3, "names": ["exports", "__esModule", "_assign", "require", "_assign2", "_interopRequireDefault", "obj", "default", "target", "i", "arguments", "length", "source", "key", "Object", "prototype", "hasOwnProperty", "call"], "sources": ["E:/AllProject/datafront/node_modules/babel-runtime/helpers/extends.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\n\nvar _assign = require(\"../core-js/object/assign\");\n\nvar _assign2 = _interopRequireDefault(_assign);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = _assign2.default || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,OAAO,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AAEjD,IAAIC,QAAQ,GAAGC,sBAAsB,CAACH,OAAO,CAAC;AAE9C,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACL,UAAU,GAAGK,GAAG,GAAG;IAAEC,OAAO,EAAED;EAAI,CAAC;AAAE;AAE9FN,OAAO,CAACO,OAAO,GAAGH,QAAQ,CAACG,OAAO,IAAI,UAAUC,MAAM,EAAE;EACtD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAEzB,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MACtB,IAAIE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,MAAM,EAAEC,GAAG,CAAC,EAAE;QACrDL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAC3B;IACF;EACF;EAEA,OAAOL,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}