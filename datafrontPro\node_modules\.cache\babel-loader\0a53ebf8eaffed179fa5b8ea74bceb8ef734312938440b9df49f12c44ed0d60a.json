{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nimport ChartTypeSelector from './ChartTypeSelector.vue';\nimport { getCompatibleChartTypes, getRecommendedChartType, CHART_TYPES } from '@/utils/chartCompatibility';\nexport default {\n  name: 'ChartDisplay',\n  components: {\n    ChartTypeSelector\n  },\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 是否启用图表类型切换功能\n    enableTypeSwitch: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      // 多图表类型相关状态\n      chartData: null,\n      currentChartType: CHART_TYPES.BAR,\n      chartCompatibility: {},\n      chartInstances: {},\n      // 缓存不同类型的图表实例\n      chartLoadingStates: {},\n      // 各图表类型的加载状态\n      switchLoading: false // 切换时的加载状态\n    };\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler() {\n        // 重新渲染图表\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    this.renderChart(); // 恢复使用原有的渲染逻辑\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁所有图表实例\n    this.destroyAllCharts();\n  },\n  methods: {\n    // 分析数据兼容性\n    analyzeCompatibility() {\n      if (!this.chartData) return;\n      this.chartCompatibility = getCompatibleChartTypes(this.chartData);\n      console.log('图表兼容性分析结果:', this.chartCompatibility);\n    },\n    // 预渲染其他兼容的图表类型\n    async preRenderOtherCharts() {\n      const compatibleTypes = Object.keys(this.chartCompatibility).filter(type => type !== this.currentChartType && this.chartCompatibility[type].compatible);\n      console.log('开始预渲染其他图表类型:', compatibleTypes);\n\n      // 异步渲染其他类型，不阻塞当前显示\n      setTimeout(async () => {\n        for (const type of compatibleTypes) {\n          try {\n            this.$set(this.chartLoadingStates, type, true);\n            await this.renderChartType(type, false); // false表示不显示\n            this.$set(this.chartLoadingStates, type, false);\n          } catch (error) {\n            console.error(`预渲染图表类型 ${type} 失败:`, error);\n            this.$set(this.chartLoadingStates, type, false);\n          }\n        }\n      }, 100);\n    },\n    // 渲染指定类型的图表\n    async renderChartType(chartType, display = true) {\n      if (!this.chartData) return;\n      try {\n        const options = this.convertToChartOptions(this.chartData, chartType);\n        if (display) {\n          // 显示图表\n          this.drawChart(options, chartType);\n        } else {\n          // 仅创建实例缓存，不显示\n          this.createChartInstance(options, chartType);\n        }\n      } catch (error) {\n        console.error(`渲染图表类型 ${chartType} 失败:`, error);\n        throw error;\n      }\n    },\n    // 处理图表类型切换\n    async handleChartTypeChange(newType) {\n      if (newType === this.currentChartType) return;\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n      this.switchLoading = true;\n      try {\n        // 更新当前图表类型\n        this.currentChartType = newType;\n\n        // 重新生成图表选项并渲染\n        const options = this.convertToChartOptions(this.chartData, newType);\n        console.log('切换图表选项:', options);\n\n        // 等待DOM更新\n        await this.$nextTick();\n\n        // 重新渲染图表\n        this.drawChart(options);\n        console.log('图表类型切换完成:', newType);\n      } catch (error) {\n        console.error('切换图表类型失败:', error);\n        this.$message.error('切换图表类型失败: ' + error.message);\n      } finally {\n        this.switchLoading = false;\n      }\n    },\n    // 创建图表实例（用于缓存）\n    createChartInstance(options, chartType) {\n      // 创建临时容器\n      const tempContainer = document.createElement('div');\n      tempContainer.style.width = '100%';\n      tempContainer.style.height = '100%';\n      tempContainer.style.position = 'absolute';\n      tempContainer.style.top = '-9999px';\n      document.body.appendChild(tempContainer);\n      try {\n        const instance = echarts.init(tempContainer);\n        instance.setOption(options);\n\n        // 缓存实例\n        if (this.chartInstances[chartType]) {\n          this.chartInstances[chartType].dispose();\n        }\n        this.chartInstances[chartType] = instance;\n        console.log(`图表类型 ${chartType} 实例已缓存`);\n      } catch (error) {\n        console.error(`创建图表实例 ${chartType} 失败:`, error);\n      } finally {\n        // 移除临时容器\n        document.body.removeChild(tempContainer);\n      }\n    },\n    // 销毁所有图表实例\n    destroyAllCharts() {\n      // 销毁当前实例\n      if (this.chartInstance) {\n        this.chartInstance.dispose();\n        this.chartInstance = null;\n      }\n\n      // 销毁缓存的实例\n      Object.values(this.chartInstances).forEach(instance => {\n        if (instance) {\n          instance.dispose();\n        }\n      });\n      this.chartInstances = {};\n    },\n    // 主渲染方法 - 保持原有逻辑，然后增强多图表功能\n    async renderChart() {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n      console.log('开始渲染图表，配置:', this.chartConfig);\n      try {\n        // 1. 先使用原有逻辑获取数据并渲染\n        await this.renderOriginalChart();\n\n        // 2. 原有图表渲染成功后，启用多图表功能\n        if (this.enableTypeSwitch && this.chartData) {\n          this.setupMultiChartSupport();\n        }\n      } catch (error) {\n        console.error('渲染图表失败:', error);\n        this.error = true;\n        this.errorMsg = '渲染图表失败: ' + error.message;\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 使用原有逻辑渲染图表\n    async renderOriginalChart() {\n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.chartData = this.chartConfig.data;\n\n        // 将原始数据转换为echarts选项\n        const options = this.convertToChartOptions(this.chartData, this.chartConfig.type);\n        console.log('生成的echarts选项:', options);\n\n        // 渲染图表\n        this.drawChart(options);\n        return;\n      }\n\n      // 如果有chartDataId，从后端获取数据\n      if (this.chartConfig.chartDataId) {\n        console.log('从后端获取图表数据，ID:', this.chartConfig.chartDataId);\n        const response = await getData(this.chartConfig);\n        console.log('获取到的数据:', response);\n        if (response && response.code === 0 && response.data) {\n          this.chartData = response.data.data || response.data;\n\n          // 将数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartData, this.chartConfig.type);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } else {\n          throw new Error(response?.msg || '获取图表数据失败');\n        }\n      } else {\n        throw new Error('缺少数据源配置');\n      }\n    },\n    // 设置多图表支持功能\n    setupMultiChartSupport() {\n      console.log('启用多图表切换功能');\n\n      // 分析数据兼容性\n      this.analyzeCompatibility();\n\n      // 设置当前图表类型\n      this.currentChartType = this.chartConfig.type || getRecommendedChartType(this.chartData);\n      console.log('当前图表类型:', this.currentChartType);\n\n      // 异步预渲染其他兼容的图表类型\n      setTimeout(() => {\n        this.preRenderOtherCharts();\n      }, 500); // 延迟500ms，确保当前图表渲染完成\n    },\n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData, chartType) {\n      console.log('转换图表数据为echarts选项，数据:', chartData, '图表类型:', chartType);\n\n      // 使用传入的chartType，如果没有则使用配置中的类型\n      const type = chartType || this.chartConfig.type || this.currentChartType;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch (type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n\n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n\n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n\n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        console.log('提取的数据:', extractedData);\n\n        // 根据提取的数据类型调用不同的处理函数\n        switch (extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const {\n        data = [],\n        metrics = []\n      } = chartData;\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const {\n        data = []\n      } = chartData;\n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getData", "echarts", "ChartTypeSelector", "getCompatibleChartTypes", "getRecommendedChartType", "CHART_TYPES", "name", "components", "props", "chartConfig", "type", "Object", "required", "default", "enableTypeSwitch", "Boolean", "data", "loading", "error", "errorMsg", "chartInstance", "chartData", "currentChartType", "BAR", "chartCompatibility", "chartInstances", "chartLoadingStates", "switchLoading", "watch", "deep", "handler", "<PERSON><PERSON><PERSON>", "mounted", "window", "addEventListener", "resizeChart", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "destroy<PERSON>ll<PERSON><PERSON><PERSON>", "methods", "analyzeCompatibility", "console", "log", "<PERSON>R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "compatibleTypes", "keys", "filter", "compatible", "setTimeout", "$set", "renderChartType", "chartType", "display", "options", "convertToChartOptions", "<PERSON><PERSON><PERSON>", "createChartInstance", "handleChartTypeChange", "newType", "$nextTick", "$message", "message", "tempContainer", "document", "createElement", "style", "width", "height", "position", "top", "body", "append<PERSON><PERSON><PERSON>", "instance", "init", "setOption", "dispose", "<PERSON><PERSON><PERSON><PERSON>", "values", "for<PERSON>ach", "<PERSON><PERSON><PERSON>inal<PERSON>hart", "setupMultiChartSupport", "chartDataId", "response", "code", "Error", "msg", "Array", "isArray", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "extractedData", "metrics", "fields", "f", "groupType", "xAxisData", "map", "item", "field", "series", "length", "categoriesSet", "Set", "s", "add", "category", "categories", "from", "seriesData", "find", "value", "push", "title", "text", "tooltip", "trigger", "axisPointer", "legend", "xAxis", "yAxis", "formatter", "orient", "right", "radius", "avoidLabelOverlap", "label", "show", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "$refs", "chartRef", "resize"], "sources": ["src/components/ChartDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <!-- 图表类型选择器 -->\n    <chart-type-selector\n      v-if=\"enableTypeSwitch && chartData\"\n      :current-type=\"currentChartType\"\n      :compatibility=\"chartCompatibility\"\n      :loading-states=\"chartLoadingStates\"\n      @type-change=\"handleChartTypeChange\"\n    />\n\n    <!-- 图表容器 - 始终存在，避免DOM不存在问题 -->\n    <div class=\"chart-wrapper\">\n      <!-- 图表画布 - 始终存在 -->\n      <div ref=\"chartRef\" class=\"chart-canvas\" v-show=\"!loading && !error && !switchLoading\"></div>\n\n      <!-- 加载状态 -->\n      <div v-if=\"loading\" class=\"chart-loading\">\n        <i class=\"el-icon-loading\"></i>\n        <span>加载图表中...</span>\n      </div>\n\n      <!-- 错误状态 -->\n      <div v-if=\"error\" class=\"chart-error\">\n        <i class=\"el-icon-warning\"></i>\n        <span>{{ errorMsg }}</span>\n      </div>\n\n      <!-- 切换加载状态 -->\n      <div v-if=\"switchLoading\" class=\"chart-switch-loading\">\n        <i class=\"el-icon-loading\"></i>\n        <span>切换图表中...</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nimport ChartTypeSelector from './ChartTypeSelector.vue';\nimport {\n  getCompatibleChartTypes,\n  getRecommendedChartType,\n  CHART_TYPES\n} from '@/utils/chartCompatibility';\n\nexport default {\n  name: 'ChartDisplay',\n  components: {\n    ChartTypeSelector\n  },\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 是否启用图表类型切换功能\n    enableTypeSwitch: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      // 多图表类型相关状态\n      chartData: null,\n      currentChartType: CHART_TYPES.BAR,\n      chartCompatibility: {},\n      chartInstances: {}, // 缓存不同类型的图表实例\n      chartLoadingStates: {}, // 各图表类型的加载状态\n      switchLoading: false // 切换时的加载状态\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler() {\n        // 重新渲染图表\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    this.renderChart(); // 恢复使用原有的渲染逻辑\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁所有图表实例\n    this.destroyAllCharts();\n  },\n  methods: {\n\n    // 分析数据兼容性\n    analyzeCompatibility() {\n      if (!this.chartData) return;\n\n      this.chartCompatibility = getCompatibleChartTypes(this.chartData);\n      console.log('图表兼容性分析结果:', this.chartCompatibility);\n    },\n\n\n    // 预渲染其他兼容的图表类型\n    async preRenderOtherCharts() {\n      const compatibleTypes = Object.keys(this.chartCompatibility)\n        .filter(type => type !== this.currentChartType && this.chartCompatibility[type].compatible);\n\n      console.log('开始预渲染其他图表类型:', compatibleTypes);\n\n      // 异步渲染其他类型，不阻塞当前显示\n      setTimeout(async () => {\n        for (const type of compatibleTypes) {\n          try {\n            this.$set(this.chartLoadingStates, type, true);\n            await this.renderChartType(type, false); // false表示不显示\n            this.$set(this.chartLoadingStates, type, false);\n          } catch (error) {\n            console.error(`预渲染图表类型 ${type} 失败:`, error);\n            this.$set(this.chartLoadingStates, type, false);\n          }\n        }\n      }, 100);\n    },\n\n    // 渲染指定类型的图表\n    async renderChartType(chartType, display = true) {\n      if (!this.chartData) return;\n\n      try {\n        const options = this.convertToChartOptions(this.chartData, chartType);\n\n        if (display) {\n          // 显示图表\n          this.drawChart(options, chartType);\n        } else {\n          // 仅创建实例缓存，不显示\n          this.createChartInstance(options, chartType);\n        }\n      } catch (error) {\n        console.error(`渲染图表类型 ${chartType} 失败:`, error);\n        throw error;\n      }\n    },\n\n    // 处理图表类型切换\n    async handleChartTypeChange(newType) {\n      if (newType === this.currentChartType) return;\n\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n\n      this.switchLoading = true;\n\n      try {\n        // 更新当前图表类型\n        this.currentChartType = newType;\n\n        // 重新生成图表选项并渲染\n        const options = this.convertToChartOptions(this.chartData, newType);\n        console.log('切换图表选项:', options);\n\n        // 等待DOM更新\n        await this.$nextTick();\n\n        // 重新渲染图表\n        this.drawChart(options);\n\n        console.log('图表类型切换完成:', newType);\n      } catch (error) {\n        console.error('切换图表类型失败:', error);\n        this.$message.error('切换图表类型失败: ' + error.message);\n      } finally {\n        this.switchLoading = false;\n      }\n    },\n\n    // 创建图表实例（用于缓存）\n    createChartInstance(options, chartType) {\n      // 创建临时容器\n      const tempContainer = document.createElement('div');\n      tempContainer.style.width = '100%';\n      tempContainer.style.height = '100%';\n      tempContainer.style.position = 'absolute';\n      tempContainer.style.top = '-9999px';\n      document.body.appendChild(tempContainer);\n\n      try {\n        const instance = echarts.init(tempContainer);\n        instance.setOption(options);\n\n        // 缓存实例\n        if (this.chartInstances[chartType]) {\n          this.chartInstances[chartType].dispose();\n        }\n        this.chartInstances[chartType] = instance;\n\n        console.log(`图表类型 ${chartType} 实例已缓存`);\n      } catch (error) {\n        console.error(`创建图表实例 ${chartType} 失败:`, error);\n      } finally {\n        // 移除临时容器\n        document.body.removeChild(tempContainer);\n      }\n    },\n\n    // 销毁所有图表实例\n    destroyAllCharts() {\n      // 销毁当前实例\n      if (this.chartInstance) {\n        this.chartInstance.dispose();\n        this.chartInstance = null;\n      }\n\n      // 销毁缓存的实例\n      Object.values(this.chartInstances).forEach(instance => {\n        if (instance) {\n          instance.dispose();\n        }\n      });\n      this.chartInstances = {};\n    },\n\n    // 主渲染方法 - 保持原有逻辑，然后增强多图表功能\n    async renderChart() {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n\n      console.log('开始渲染图表，配置:', this.chartConfig);\n\n      try {\n        // 1. 先使用原有逻辑获取数据并渲染\n        await this.renderOriginalChart();\n\n        // 2. 原有图表渲染成功后，启用多图表功能\n        if (this.enableTypeSwitch && this.chartData) {\n          this.setupMultiChartSupport();\n        }\n      } catch (error) {\n        console.error('渲染图表失败:', error);\n        this.error = true;\n        this.errorMsg = '渲染图表失败: ' + error.message;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 使用原有逻辑渲染图表\n    async renderOriginalChart() {\n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.chartData = this.chartConfig.data;\n\n        // 将原始数据转换为echarts选项\n        const options = this.convertToChartOptions(this.chartData, this.chartConfig.type);\n        console.log('生成的echarts选项:', options);\n\n        // 渲染图表\n        this.drawChart(options);\n        return;\n      }\n\n      // 如果有chartDataId，从后端获取数据\n      if (this.chartConfig.chartDataId) {\n        console.log('从后端获取图表数据，ID:', this.chartConfig.chartDataId);\n\n        const response = await getData(this.chartConfig);\n        console.log('获取到的数据:', response);\n\n        if (response && response.code === 0 && response.data) {\n          this.chartData = response.data.data || response.data;\n\n          // 将数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartData, this.chartConfig.type);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } else {\n          throw new Error(response?.msg || '获取图表数据失败');\n        }\n      } else {\n        throw new Error('缺少数据源配置');\n      }\n    },\n\n    // 设置多图表支持功能\n    setupMultiChartSupport() {\n      console.log('启用多图表切换功能');\n\n      // 分析数据兼容性\n      this.analyzeCompatibility();\n\n      // 设置当前图表类型\n      this.currentChartType = this.chartConfig.type || getRecommendedChartType(this.chartData);\n      console.log('当前图表类型:', this.currentChartType);\n\n      // 异步预渲染其他兼容的图表类型\n      setTimeout(() => {\n        this.preRenderOtherCharts();\n      }, 500); // 延迟500ms，确保当前图表渲染完成\n    },\n\n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData, chartType) {\n      console.log('转换图表数据为echarts选项，数据:', chartData, '图表类型:', chartType);\n\n      // 使用传入的chartType，如果没有则使用配置中的类型\n      const type = chartType || this.chartConfig.type || this.currentChartType;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch(type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n        \n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n        \n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n        \n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        \n        console.log('提取的数据:', extractedData);\n        \n        // 根据提取的数据类型调用不同的处理函数\n        switch(extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    \n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const { data = [], metrics = [] } = chartData;\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const { data = [] } = chartData;\n      \n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    \n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.chart-container {\n  width: 100%;\n  height: auto;\n  min-height: 350px;\n  position: relative;\n  max-width: 650px;\n  margin: 0 auto;\n  z-index: 1;\n  box-sizing: border-box;\n  isolation: isolate; /* 创建新的层叠上下文 */\n}\n\n.chart-wrapper {\n  position: relative;\n  width: 100%;\n  height: 350px;\n}\n\n.chart-canvas {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  z-index: 1;\n}\n\n.chart-loading, .chart-error, .chart-switch-loading {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  color: #909399;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 2;\n  background: rgba(255,255,255,0.9);\n}\n\n.chart-loading i, .chart-error i, .chart-switch-loading i {\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.chart-error {\n  color: #F56C6C;\n}\n\n.chart-switch-loading {\n  background: rgba(255,255,255,0.8);\n  backdrop-filter: blur(2px);\n}\n\n/* 图表切换过渡动画 */\n.chart-fade-enter-active, .chart-fade-leave-active {\n  transition: opacity 0.3s ease;\n}\n\n.chart-fade-enter, .chart-fade-leave-to {\n  opacity: 0;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chart-container {\n    max-width: 100%;\n    margin: 0;\n    padding: 0 10px;\n  }\n\n  .chart-wrapper {\n    height: 300px;\n  }\n}\n</style>"], "mappings": ";;;;;;;;;;;;;AAsCA,SAAAA,OAAA;AACA,YAAAC,OAAA;AACA,OAAAC,iBAAA;AACA,SACAC,uBAAA,EACAC,uBAAA,EACAC,WAAA,QACA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACA;IACAC,gBAAA;MACAJ,IAAA,EAAAK,OAAA;MACAF,OAAA;IACA;EACA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;MACAC,aAAA;MACA;MACAC,SAAA;MACAC,gBAAA,EAAAjB,WAAA,CAAAkB,GAAA;MACAC,kBAAA;MACAC,cAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACAnB,WAAA;MACAoB,IAAA;MACAC,QAAA;QACA;QACA,KAAAC,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAD,WAAA;IACA;IACAE,MAAA,CAAAC,gBAAA,gBAAAC,WAAA;EACA;EACAC,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,WAAA;IACA;IACA,KAAAG,gBAAA;EACA;EACAC,OAAA;IAEA;IACAC,qBAAA;MACA,UAAAnB,SAAA;MAEA,KAAAG,kBAAA,GAAArB,uBAAA,MAAAkB,SAAA;MACAoB,OAAA,CAAAC,GAAA,oBAAAlB,kBAAA;IACA;IAGA;IACA,MAAAmB,qBAAA;MACA,MAAAC,eAAA,GAAAjC,MAAA,CAAAkC,IAAA,MAAArB,kBAAA,EACAsB,MAAA,CAAApC,IAAA,IAAAA,IAAA,UAAAY,gBAAA,SAAAE,kBAAA,CAAAd,IAAA,EAAAqC,UAAA;MAEAN,OAAA,CAAAC,GAAA,iBAAAE,eAAA;;MAEA;MACAI,UAAA;QACA,WAAAtC,IAAA,IAAAkC,eAAA;UACA;YACA,KAAAK,IAAA,MAAAvB,kBAAA,EAAAhB,IAAA;YACA,WAAAwC,eAAA,CAAAxC,IAAA;YACA,KAAAuC,IAAA,MAAAvB,kBAAA,EAAAhB,IAAA;UACA,SAAAQ,KAAA;YACAuB,OAAA,CAAAvB,KAAA,YAAAR,IAAA,QAAAQ,KAAA;YACA,KAAA+B,IAAA,MAAAvB,kBAAA,EAAAhB,IAAA;UACA;QACA;MACA;IACA;IAEA;IACA,MAAAwC,gBAAAC,SAAA,EAAAC,OAAA;MACA,UAAA/B,SAAA;MAEA;QACA,MAAAgC,OAAA,QAAAC,qBAAA,MAAAjC,SAAA,EAAA8B,SAAA;QAEA,IAAAC,OAAA;UACA;UACA,KAAAG,SAAA,CAAAF,OAAA,EAAAF,SAAA;QACA;UACA;UACA,KAAAK,mBAAA,CAAAH,OAAA,EAAAF,SAAA;QACA;MACA,SAAAjC,KAAA;QACAuB,OAAA,CAAAvB,KAAA,WAAAiC,SAAA,QAAAjC,KAAA;QACA,MAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAuC,sBAAAC,OAAA;MACA,IAAAA,OAAA,UAAApC,gBAAA;MAEAmB,OAAA,CAAAC,GAAA,iBAAApB,gBAAA,QAAAoC,OAAA;MAEA,KAAA/B,aAAA;MAEA;QACA;QACA,KAAAL,gBAAA,GAAAoC,OAAA;;QAEA;QACA,MAAAL,OAAA,QAAAC,qBAAA,MAAAjC,SAAA,EAAAqC,OAAA;QACAjB,OAAA,CAAAC,GAAA,YAAAW,OAAA;;QAEA;QACA,WAAAM,SAAA;;QAEA;QACA,KAAAJ,SAAA,CAAAF,OAAA;QAEAZ,OAAA,CAAAC,GAAA,cAAAgB,OAAA;MACA,SAAAxC,KAAA;QACAuB,OAAA,CAAAvB,KAAA,cAAAA,KAAA;QACA,KAAA0C,QAAA,CAAA1C,KAAA,gBAAAA,KAAA,CAAA2C,OAAA;MACA;QACA,KAAAlC,aAAA;MACA;IACA;IAEA;IACA6B,oBAAAH,OAAA,EAAAF,SAAA;MACA;MACA,MAAAW,aAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,aAAA,CAAAG,KAAA,CAAAC,KAAA;MACAJ,aAAA,CAAAG,KAAA,CAAAE,MAAA;MACAL,aAAA,CAAAG,KAAA,CAAAG,QAAA;MACAN,aAAA,CAAAG,KAAA,CAAAI,GAAA;MACAN,QAAA,CAAAO,IAAA,CAAAC,WAAA,CAAAT,aAAA;MAEA;QACA,MAAAU,QAAA,GAAAvE,OAAA,CAAAwE,IAAA,CAAAX,aAAA;QACAU,QAAA,CAAAE,SAAA,CAAArB,OAAA;;QAEA;QACA,SAAA5B,cAAA,CAAA0B,SAAA;UACA,KAAA1B,cAAA,CAAA0B,SAAA,EAAAwB,OAAA;QACA;QACA,KAAAlD,cAAA,CAAA0B,SAAA,IAAAqB,QAAA;QAEA/B,OAAA,CAAAC,GAAA,SAAAS,SAAA;MACA,SAAAjC,KAAA;QACAuB,OAAA,CAAAvB,KAAA,WAAAiC,SAAA,QAAAjC,KAAA;MACA;QACA;QACA6C,QAAA,CAAAO,IAAA,CAAAM,WAAA,CAAAd,aAAA;MACA;IACA;IAEA;IACAxB,iBAAA;MACA;MACA,SAAAlB,aAAA;QACA,KAAAA,aAAA,CAAAuD,OAAA;QACA,KAAAvD,aAAA;MACA;;MAEA;MACAT,MAAA,CAAAkE,MAAA,MAAApD,cAAA,EAAAqD,OAAA,CAAAN,QAAA;QACA,IAAAA,QAAA;UACAA,QAAA,CAAAG,OAAA;QACA;MACA;MACA,KAAAlD,cAAA;IACA;IAEA;IACA,MAAAM,YAAA;MACA,KAAAd,OAAA;MACA,KAAAC,KAAA;;MAEA;MACA,UAAAT,WAAA,UAAAA,WAAA,CAAAC,IAAA;QACA,KAAAO,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA;QACAsB,OAAA,CAAAvB,KAAA,iBAAAT,WAAA;QACA;MACA;MAEAgC,OAAA,CAAAC,GAAA,oBAAAjC,WAAA;MAEA;QACA;QACA,WAAAsE,mBAAA;;QAEA;QACA,SAAAjE,gBAAA,SAAAO,SAAA;UACA,KAAA2D,sBAAA;QACA;MACA,SAAA9D,KAAA;QACAuB,OAAA,CAAAvB,KAAA,YAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,gBAAAD,KAAA,CAAA2C,OAAA;MACA;QACA,KAAA5C,OAAA;MACA;IACA;IAEA;IACA,MAAA8D,oBAAA;MACA;MACA,SAAAtE,WAAA,CAAAO,IAAA;QACAyB,OAAA,CAAAC,GAAA;QACA,KAAArB,SAAA,QAAAZ,WAAA,CAAAO,IAAA;;QAEA;QACA,MAAAqC,OAAA,QAAAC,qBAAA,MAAAjC,SAAA,OAAAZ,WAAA,CAAAC,IAAA;QACA+B,OAAA,CAAAC,GAAA,kBAAAW,OAAA;;QAEA;QACA,KAAAE,SAAA,CAAAF,OAAA;QACA;MACA;;MAEA;MACA,SAAA5C,WAAA,CAAAwE,WAAA;QACAxC,OAAA,CAAAC,GAAA,uBAAAjC,WAAA,CAAAwE,WAAA;QAEA,MAAAC,QAAA,SAAAlF,OAAA,MAAAS,WAAA;QACAgC,OAAA,CAAAC,GAAA,YAAAwC,QAAA;QAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAC,IAAA,UAAAD,QAAA,CAAAlE,IAAA;UACA,KAAAK,SAAA,GAAA6D,QAAA,CAAAlE,IAAA,CAAAA,IAAA,IAAAkE,QAAA,CAAAlE,IAAA;;UAEA;UACA,MAAAqC,OAAA,QAAAC,qBAAA,MAAAjC,SAAA,OAAAZ,WAAA,CAAAC,IAAA;UACA+B,OAAA,CAAAC,GAAA,kBAAAW,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA;UACA,UAAA+B,KAAA,CAAAF,QAAA,EAAAG,GAAA;QACA;MACA;QACA,UAAAD,KAAA;MACA;IACA;IAEA;IACAJ,uBAAA;MACAvC,OAAA,CAAAC,GAAA;;MAEA;MACA,KAAAF,oBAAA;;MAEA;MACA,KAAAlB,gBAAA,QAAAb,WAAA,CAAAC,IAAA,IAAAN,uBAAA,MAAAiB,SAAA;MACAoB,OAAA,CAAAC,GAAA,iBAAApB,gBAAA;;MAEA;MACA0B,UAAA;QACA,KAAAL,oBAAA;MACA;IACA;IAEA;IACAW,sBAAAjC,SAAA,EAAA8B,SAAA;MACAV,OAAA,CAAAC,GAAA,yBAAArB,SAAA,WAAA8B,SAAA;;MAEA;MACA,MAAAzC,IAAA,GAAAyC,SAAA,SAAA1C,WAAA,CAAAC,IAAA,SAAAY,gBAAA;;MAEA;MACA,IAAAD,SAAA,CAAAL,IAAA,IAAAsE,KAAA,CAAAC,OAAA,CAAAlE,SAAA,CAAAL,IAAA;QACAyB,OAAA,CAAAC,GAAA;QACA;QACA,QAAAhC,IAAA;UACA;YACA,YAAA8E,kBAAA,CAAAnE,SAAA;UACA;YACA,YAAAoE,mBAAA,CAAApE,SAAA;UACA;YACA,YAAAqE,kBAAA,CAAArE,SAAA;UACA;YACA,YAAAsE,uBAAA,CAAAtE,SAAA;UACA;YACA,YAAAuE,iBAAA;QACA;MACA;QACAnD,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAAmD,aAAA;;QAEA;QACA,IAAAxE,SAAA,CAAAL,IAAA,IAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA;UACAyB,OAAA,CAAAC,GAAA;UACAmD,aAAA;YACAnF,IAAA,EAAAW,SAAA,CAAAL,IAAA,CAAAN,IAAA,SAAAD,WAAA,CAAAC,IAAA;YACAM,IAAA,EAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA;YACA8E,OAAA,EAAAzE,SAAA,CAAAL,IAAA,CAAA+E,MAAA,GAAA1E,SAAA,CAAAL,IAAA,CAAA+E,MAAA,CAAAjD,MAAA,CAAAkD,CAAA,IAAAA,CAAA,CAAAC,SAAA;UACA;QACA;;QAEA;QACA,KAAAJ,aAAA;UACApD,OAAA,CAAAC,GAAA;UACA,YAAAkD,iBAAA;QACA;QAEAnD,OAAA,CAAAC,GAAA,WAAAmD,aAAA;;QAEA;QACA,QAAAA,aAAA,CAAAnF,IAAA;UACA;YACA,YAAA8E,kBAAA,CAAAK,aAAA;UACA;YACA,YAAAJ,mBAAA,CAAAI,aAAA;UACA;YACA,YAAAH,kBAAA,CAAAG,aAAA;UACA;YACA,YAAAF,uBAAA,CAAAE,aAAA;UACA;YACA,YAAAD,iBAAA;QACA;MACA;IACA;IAEA;IACAJ,mBAAAnE,SAAA;MACAoB,OAAA,CAAAC,GAAA,gBAAArB,SAAA;;MAEA;MACA,IAAAL,IAAA;MACA,IAAA8E,OAAA;;MAEA;MACA,IAAAzE,SAAA,CAAAL,IAAA,IAAAsE,KAAA,CAAAC,OAAA,CAAAlE,SAAA,CAAAL,IAAA;QACAA,IAAA,GAAAK,SAAA,CAAAL,IAAA;QACA8E,OAAA,GAAAzE,SAAA,CAAAyE,OAAA;MACA;MACA;MAAA,KACA,IAAAzE,SAAA,CAAAL,IAAA,IAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA,IAAAsE,KAAA,CAAAC,OAAA,CAAAlE,SAAA,CAAAL,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA;QACA8E,OAAA,GAAAzE,SAAA,CAAAL,IAAA,CAAA+E,MAAA,GACA1E,SAAA,CAAAL,IAAA,CAAA+E,MAAA,CAAAjD,MAAA,CAAAkD,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAxD,OAAA,CAAAC,GAAA,YAAA1B,IAAA;MACAyB,OAAA,CAAAC,GAAA,QAAAoD,OAAA;;MAEA;MACA,MAAAI,SAAA,GAAAlF,IAAA,CAAAmF,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAA9F,IAAA;;MAEA;MACA,MAAAgG,MAAA;MACA,IAAAtF,IAAA,CAAAuF,MAAA,QAAAvF,IAAA,IAAAsF,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACAzF,IAAA,CAAA8D,OAAA,CAAAsB,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAxB,OAAA,CAAA4B,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAvB,KAAA,CAAAwB,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAA/B,OAAA,CAAA8B,QAAA;UACA,MAAAG,UAAA,GAAA/F,IAAA,CAAAmF,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAU,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAN,MAAA,GAAAA,MAAA,CAAAW,KAAA;UACA;UAEAX,MAAA,CAAAY,IAAA;YACA5G,IAAA,EAAAsG,QAAA;YACAlG,IAAA;YACAM,IAAA,EAAA+F;UACA;QACA;MACA;QACA;QACAT,MAAA,CAAAY,IAAA;UACA5G,IAAA,EAAAwF,OAAA,KAAAxF,IAAA;UACAI,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAAmF,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAa,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAA3G,WAAA,CAAA0G,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA7G,IAAA;UACA;QACA;QACA8G,MAAA;UACAxG,IAAA,EAAAsF,MAAA,CAAAH,GAAA,CAAAO,CAAA,IAAAA,CAAA,CAAApG,IAAA;QACA;QACAmH,KAAA;UACA/G,IAAA;UACAM,IAAA,EAAAkF;QACA;QACAwB,KAAA;UACAhH,IAAA;QACA;QACA4F,MAAA,EAAAA;MACA;IACA;IAEA;IACAb,oBAAApE,SAAA;MACA;QAAAL,IAAA;QAAA8E,OAAA;MAAA,IAAAzE,SAAA;;MAEA;MACA,MAAA6E,SAAA,GAAAlF,IAAA,CAAAmF,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA;;MAEA;MACA,MAAAC,MAAA;MACA,IAAAtF,IAAA,CAAAuF,MAAA,QAAAvF,IAAA,IAAAsF,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACAzF,IAAA,CAAA8D,OAAA,CAAAsB,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAxB,OAAA,CAAA4B,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAvB,KAAA,CAAAwB,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAA/B,OAAA,CAAA8B,QAAA;UACA,MAAAG,UAAA,GAAA/F,IAAA,CAAAmF,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAU,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAN,MAAA,GAAAA,MAAA,CAAAW,KAAA;UACA;UAEAX,MAAA,CAAAY,IAAA;YACA5G,IAAA,EAAAsG,QAAA;YACAlG,IAAA;YACAM,IAAA,EAAA+F;UACA;QACA;MACA;QACA;QACAT,MAAA,CAAAY,IAAA;UACA5G,IAAA,EAAAwF,OAAA,KAAAxF,IAAA;UACAI,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAAmF,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAa,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAA3G,WAAA,CAAA0G,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAE,MAAA;UACAxG,IAAA,EAAAsF,MAAA,CAAAH,GAAA,CAAAO,CAAA,IAAAA,CAAA,CAAApG,IAAA;QACA;QACAmH,KAAA;UACA/G,IAAA;UACAM,IAAA,EAAAkF;QACA;QACAwB,KAAA;UACAhH,IAAA;QACA;QACA4F,MAAA,EAAAA;MACA;IACA;IAEA;IACAZ,mBAAArE,SAAA;MACA;QAAAL,IAAA;MAAA,IAAAK,SAAA;MAEA,MAAA0F,UAAA,GAAA/F,IAAA,CAAAmF,GAAA,CAAAC,IAAA;QACA9F,IAAA,EAAA8F,IAAA,CAAAC,KAAA;QACAY,KAAA,EAAAb,IAAA,CAAAa;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAA3G,WAAA,CAAA0G,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAK,SAAA;QACA;QACAH,MAAA;UACAI,MAAA;UACAC,KAAA;UACAxD,GAAA;UACArD,IAAA,EAAA+F,UAAA,CAAAZ,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA9F,IAAA;QACA;QACAgG,MAAA;UACAhG,IAAA;UACAI,IAAA;UACAoH,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACA7D,QAAA;UACA;UACA8D,QAAA;YACAF,KAAA;cACAC,IAAA;cACAE,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAJ,IAAA;UACA;UACAjH,IAAA,EAAA+F;QACA;MACA;IACA;IAEA;IACApB,wBAAAtE,SAAA;MACAoB,OAAA,CAAAC,GAAA,gBAAArB,SAAA;;MAEA;MACA,IAAAL,IAAA;MACA,IAAA8E,OAAA;;MAEA;MACA,IAAAzE,SAAA,CAAAL,IAAA,IAAAsE,KAAA,CAAAC,OAAA,CAAAlE,SAAA,CAAAL,IAAA;QACAA,IAAA,GAAAK,SAAA,CAAAL,IAAA;QACA8E,OAAA,GAAAzE,SAAA,CAAAyE,OAAA;MACA;MACA;MAAA,KACA,IAAAzE,SAAA,CAAAL,IAAA,IAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA,IAAAsE,KAAA,CAAAC,OAAA,CAAAlE,SAAA,CAAAL,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA;QACA8E,OAAA,GAAAzE,SAAA,CAAAL,IAAA,CAAA+E,MAAA,GACA1E,SAAA,CAAAL,IAAA,CAAA+E,MAAA,CAAAjD,MAAA,CAAAkD,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAxD,OAAA,CAAAC,GAAA,YAAA1B,IAAA;MACAyB,OAAA,CAAAC,GAAA,QAAAoD,OAAA;;MAEA;MACA,MAAAwC,SAAA,GAAAtH,IAAA,CAAAmF,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAA9F,IAAA;;MAEA;MACA,MAAAgG,MAAA;MACA,IAAAtF,IAAA,CAAAuF,MAAA,QAAAvF,IAAA,IAAAsF,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACAzF,IAAA,CAAA8D,OAAA,CAAAsB,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAxB,OAAA,CAAA4B,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAvB,KAAA,CAAAwB,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAA/B,OAAA,CAAA8B,QAAA;UACA,MAAAG,UAAA,GAAA/F,IAAA,CAAAmF,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAU,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAN,MAAA,GAAAA,MAAA,CAAAW,KAAA;UACA;UAEAX,MAAA,CAAAY,IAAA;YACA5G,IAAA,EAAAsG,QAAA;YACAlG,IAAA;YAAA;YACAM,IAAA,EAAA+F;UACA;QACA;MACA;QACA;QACAT,MAAA,CAAAY,IAAA;UACA5G,IAAA,EAAAwF,OAAA,KAAAxF,IAAA;UACAI,IAAA;UAAA;UACAM,IAAA,EAAAA,IAAA,CAAAmF,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAa,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAA3G,WAAA,CAAA0G,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA7G,IAAA;UACA;QACA;QACA8G,MAAA;UACAxG,IAAA,EAAAsF,MAAA,CAAAH,GAAA,CAAAO,CAAA,IAAAA,CAAA,CAAApG,IAAA;QACA;QACA;QACAmH,KAAA;UACA/G,IAAA;QACA;QACAgH,KAAA;UACAhH,IAAA;UAAA;UACAM,IAAA,EAAAsH;QACA;QACAhC,MAAA,EAAAA;MACA;IACA;IAEA;IACAV,kBAAA;MACAnD,OAAA,CAAAC,GAAA;MACA;QACAyE,KAAA;UACAC,IAAA,OAAA3G,WAAA,CAAA0G,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAG,KAAA;UACA/G,IAAA;UACAM,IAAA;QACA;QACA0G,KAAA;UACAhH,IAAA;QACA;QACA4F,MAAA;UACA5F,IAAA,OAAAD,WAAA,CAAAC,IAAA;UACAM,IAAA;QACA;MACA;IACA;IAEA;IACAuC,UAAAF,OAAA;MACA;QACAZ,OAAA,CAAAC,GAAA;QACA,UAAA6F,KAAA,CAAAC,QAAA;UACA/F,OAAA,CAAAvB,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA;UACA;QACA;;QAEA;QACA,SAAAC,aAAA;UACA,KAAAA,aAAA,CAAAuD,OAAA;QACA;;QAEA;QACA,KAAAvD,aAAA,GAAAnB,OAAA,CAAAwE,IAAA,MAAA8D,KAAA,CAAAC,QAAA;QACA,KAAApH,aAAA,CAAAsD,SAAA,CAAArB,OAAA;QACAZ,OAAA,CAAAC,GAAA;MACA,SAAAxB,KAAA;QACAuB,OAAA,CAAAvB,KAAA,YAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,gBAAAD,KAAA,CAAA2C,OAAA;MACA;IACA;IAEA;IACA1B,YAAA;MACA,SAAAf,aAAA;QACA,KAAAA,aAAA,CAAAqH,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}