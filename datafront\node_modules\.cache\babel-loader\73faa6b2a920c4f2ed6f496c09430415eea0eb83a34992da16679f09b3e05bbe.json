{"ast": null, "code": "import \"core-js/modules/es.typed-array.with.js\";\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nconst _state = {};\nfunction v1(options, buf, offset) {\n  let bytes;\n  const isV6 = options?._v6 ?? false;\n  if (options) {\n    const optionsKeys = Object.keys(options);\n    if (optionsKeys.length === 1 && optionsKeys[0] === '_v6') {\n      options = undefined;\n    }\n  }\n  if (options) {\n    bytes = v1Bytes(options.random ?? options.rng?.() ?? rng(), options.msecs, options.nsecs, options.clockseq, options.node, buf, offset);\n  } else {\n    const now = Date.now();\n    const rnds = rng();\n    updateV1State(_state, now, rnds);\n    bytes = v1Bytes(rnds, _state.msecs, _state.nsecs, isV6 ? undefined : _state.clockseq, isV6 ? undefined : _state.node, buf, offset);\n  }\n  return buf ?? unsafeStringify(bytes);\n}\nexport function updateV1State(state, now, rnds) {\n  state.msecs ??= -Infinity;\n  state.nsecs ??= 0;\n  if (now === state.msecs) {\n    state.nsecs++;\n    if (state.nsecs >= 10000) {\n      state.node = undefined;\n      state.nsecs = 0;\n    }\n  } else if (now > state.msecs) {\n    state.nsecs = 0;\n  } else if (now < state.msecs) {\n    state.node = undefined;\n  }\n  if (!state.node) {\n    state.node = rnds.slice(10, 16);\n    state.node[0] |= 0x01;\n    state.clockseq = (rnds[8] << 8 | rnds[9]) & 0x3fff;\n  }\n  state.msecs = now;\n  return state;\n}\nfunction v1Bytes(rnds, msecs, nsecs, clockseq, node, buf, offset = 0) {\n  if (rnds.length < 16) {\n    throw new Error('Random bytes length must be >= 16');\n  }\n  if (!buf) {\n    buf = new Uint8Array(16);\n    offset = 0;\n  } else {\n    if (offset < 0 || offset + 16 > buf.length) {\n      throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n    }\n  }\n  msecs ??= Date.now();\n  nsecs ??= 0;\n  clockseq ??= (rnds[8] << 8 | rnds[9]) & 0x3fff;\n  node ??= rnds.slice(10, 16);\n  msecs += 12219292800000;\n  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  buf[offset++] = tl >>> 24 & 0xff;\n  buf[offset++] = tl >>> 16 & 0xff;\n  buf[offset++] = tl >>> 8 & 0xff;\n  buf[offset++] = tl & 0xff;\n  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  buf[offset++] = tmh >>> 8 & 0xff;\n  buf[offset++] = tmh & 0xff;\n  buf[offset++] = tmh >>> 24 & 0xf | 0x10;\n  buf[offset++] = tmh >>> 16 & 0xff;\n  buf[offset++] = clockseq >>> 8 | 0x80;\n  buf[offset++] = clockseq & 0xff;\n  for (let n = 0; n < 6; ++n) {\n    buf[offset++] = node[n];\n  }\n  return buf;\n}\nexport default v1;", "map": {"version": 3, "names": ["rng", "unsafeStringify", "_state", "v1", "options", "buf", "offset", "bytes", "isV6", "_v6", "optionsKeys", "Object", "keys", "length", "undefined", "v1Bytes", "random", "msecs", "nsecs", "clockseq", "node", "now", "Date", "rnds", "updateV1State", "state", "Infinity", "slice", "Error", "Uint8Array", "RangeError", "tl", "tmh", "n"], "sources": ["D:/FastBI/datafront/node_modules/uuid/dist/esm-browser/v1.js"], "sourcesContent": ["import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nconst _state = {};\nfunction v1(options, buf, offset) {\n    let bytes;\n    const isV6 = options?._v6 ?? false;\n    if (options) {\n        const optionsKeys = Object.keys(options);\n        if (optionsKeys.length === 1 && optionsKeys[0] === '_v6') {\n            options = undefined;\n        }\n    }\n    if (options) {\n        bytes = v1Bytes(options.random ?? options.rng?.() ?? rng(), options.msecs, options.nsecs, options.clockseq, options.node, buf, offset);\n    }\n    else {\n        const now = Date.now();\n        const rnds = rng();\n        updateV1State(_state, now, rnds);\n        bytes = v1Bytes(rnds, _state.msecs, _state.nsecs, isV6 ? undefined : _state.clockseq, isV6 ? undefined : _state.node, buf, offset);\n    }\n    return buf ?? unsafeStringify(bytes);\n}\nexport function updateV1State(state, now, rnds) {\n    state.msecs ??= -Infinity;\n    state.nsecs ??= 0;\n    if (now === state.msecs) {\n        state.nsecs++;\n        if (state.nsecs >= 10000) {\n            state.node = undefined;\n            state.nsecs = 0;\n        }\n    }\n    else if (now > state.msecs) {\n        state.nsecs = 0;\n    }\n    else if (now < state.msecs) {\n        state.node = undefined;\n    }\n    if (!state.node) {\n        state.node = rnds.slice(10, 16);\n        state.node[0] |= 0x01;\n        state.clockseq = ((rnds[8] << 8) | rnds[9]) & 0x3fff;\n    }\n    state.msecs = now;\n    return state;\n}\nfunction v1Bytes(rnds, msecs, nsecs, clockseq, node, buf, offset = 0) {\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    if (!buf) {\n        buf = new Uint8Array(16);\n        offset = 0;\n    }\n    else {\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n    }\n    msecs ??= Date.now();\n    nsecs ??= 0;\n    clockseq ??= ((rnds[8] << 8) | rnds[9]) & 0x3fff;\n    node ??= rnds.slice(10, 16);\n    msecs += 12219292800000;\n    const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n    buf[offset++] = (tl >>> 24) & 0xff;\n    buf[offset++] = (tl >>> 16) & 0xff;\n    buf[offset++] = (tl >>> 8) & 0xff;\n    buf[offset++] = tl & 0xff;\n    const tmh = ((msecs / 0x100000000) * 10000) & 0xfffffff;\n    buf[offset++] = (tmh >>> 8) & 0xff;\n    buf[offset++] = tmh & 0xff;\n    buf[offset++] = ((tmh >>> 24) & 0xf) | 0x10;\n    buf[offset++] = (tmh >>> 16) & 0xff;\n    buf[offset++] = (clockseq >>> 8) | 0x80;\n    buf[offset++] = clockseq & 0xff;\n    for (let n = 0; n < 6; ++n) {\n        buf[offset++] = node[n];\n    }\n    return buf;\n}\nexport default v1;\n"], "mappings": ";AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,MAAMC,MAAM,GAAG,CAAC,CAAC;AACjB,SAASC,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAC9B,IAAIC,KAAK;EACT,MAAMC,IAAI,GAAGJ,OAAO,EAAEK,GAAG,IAAI,KAAK;EAClC,IAAIL,OAAO,EAAE;IACT,MAAMM,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACR,OAAO,CAAC;IACxC,IAAIM,WAAW,CAACG,MAAM,KAAK,CAAC,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;MACtDN,OAAO,GAAGU,SAAS;IACvB;EACJ;EACA,IAAIV,OAAO,EAAE;IACTG,KAAK,GAAGQ,OAAO,CAACX,OAAO,CAACY,MAAM,IAAIZ,OAAO,CAACJ,GAAG,GAAG,CAAC,IAAIA,GAAG,CAAC,CAAC,EAAEI,OAAO,CAACa,KAAK,EAAEb,OAAO,CAACc,KAAK,EAAEd,OAAO,CAACe,QAAQ,EAAEf,OAAO,CAACgB,IAAI,EAAEf,GAAG,EAAEC,MAAM,CAAC;EAC1I,CAAC,MACI;IACD,MAAMe,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,IAAI,GAAGvB,GAAG,CAAC,CAAC;IAClBwB,aAAa,CAACtB,MAAM,EAAEmB,GAAG,EAAEE,IAAI,CAAC;IAChChB,KAAK,GAAGQ,OAAO,CAACQ,IAAI,EAAErB,MAAM,CAACe,KAAK,EAAEf,MAAM,CAACgB,KAAK,EAAEV,IAAI,GAAGM,SAAS,GAAGZ,MAAM,CAACiB,QAAQ,EAAEX,IAAI,GAAGM,SAAS,GAAGZ,MAAM,CAACkB,IAAI,EAAEf,GAAG,EAAEC,MAAM,CAAC;EACtI;EACA,OAAOD,GAAG,IAAIJ,eAAe,CAACM,KAAK,CAAC;AACxC;AACA,OAAO,SAASiB,aAAaA,CAACC,KAAK,EAAEJ,GAAG,EAAEE,IAAI,EAAE;EAC5CE,KAAK,CAACR,KAAK,KAAK,CAACS,QAAQ;EACzBD,KAAK,CAACP,KAAK,KAAK,CAAC;EACjB,IAAIG,GAAG,KAAKI,KAAK,CAACR,KAAK,EAAE;IACrBQ,KAAK,CAACP,KAAK,EAAE;IACb,IAAIO,KAAK,CAACP,KAAK,IAAI,KAAK,EAAE;MACtBO,KAAK,CAACL,IAAI,GAAGN,SAAS;MACtBW,KAAK,CAACP,KAAK,GAAG,CAAC;IACnB;EACJ,CAAC,MACI,IAAIG,GAAG,GAAGI,KAAK,CAACR,KAAK,EAAE;IACxBQ,KAAK,CAACP,KAAK,GAAG,CAAC;EACnB,CAAC,MACI,IAAIG,GAAG,GAAGI,KAAK,CAACR,KAAK,EAAE;IACxBQ,KAAK,CAACL,IAAI,GAAGN,SAAS;EAC1B;EACA,IAAI,CAACW,KAAK,CAACL,IAAI,EAAE;IACbK,KAAK,CAACL,IAAI,GAAGG,IAAI,CAACI,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;IAC/BF,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;IACrBK,KAAK,CAACN,QAAQ,GAAG,CAAEI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM;EACxD;EACAE,KAAK,CAACR,KAAK,GAAGI,GAAG;EACjB,OAAOI,KAAK;AAChB;AACA,SAASV,OAAOA,CAACQ,IAAI,EAAEN,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEf,GAAG,EAAEC,MAAM,GAAG,CAAC,EAAE;EAClE,IAAIiB,IAAI,CAACV,MAAM,GAAG,EAAE,EAAE;IAClB,MAAM,IAAIe,KAAK,CAAC,mCAAmC,CAAC;EACxD;EACA,IAAI,CAACvB,GAAG,EAAE;IACNA,GAAG,GAAG,IAAIwB,UAAU,CAAC,EAAE,CAAC;IACxBvB,MAAM,GAAG,CAAC;EACd,CAAC,MACI;IACD,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,EAAE,GAAGD,GAAG,CAACQ,MAAM,EAAE;MACxC,MAAM,IAAIiB,UAAU,CAAC,mBAAmBxB,MAAM,IAAIA,MAAM,GAAG,EAAE,0BAA0B,CAAC;IAC5F;EACJ;EACAW,KAAK,KAAKK,IAAI,CAACD,GAAG,CAAC,CAAC;EACpBH,KAAK,KAAK,CAAC;EACXC,QAAQ,KAAK,CAAEI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM;EAChDH,IAAI,KAAKG,IAAI,CAACI,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC;EAC3BV,KAAK,IAAI,cAAc;EACvB,MAAMc,EAAE,GAAG,CAAC,CAACd,KAAK,GAAG,SAAS,IAAI,KAAK,GAAGC,KAAK,IAAI,WAAW;EAC9Db,GAAG,CAACC,MAAM,EAAE,CAAC,GAAIyB,EAAE,KAAK,EAAE,GAAI,IAAI;EAClC1B,GAAG,CAACC,MAAM,EAAE,CAAC,GAAIyB,EAAE,KAAK,EAAE,GAAI,IAAI;EAClC1B,GAAG,CAACC,MAAM,EAAE,CAAC,GAAIyB,EAAE,KAAK,CAAC,GAAI,IAAI;EACjC1B,GAAG,CAACC,MAAM,EAAE,CAAC,GAAGyB,EAAE,GAAG,IAAI;EACzB,MAAMC,GAAG,GAAKf,KAAK,GAAG,WAAW,GAAI,KAAK,GAAI,SAAS;EACvDZ,GAAG,CAACC,MAAM,EAAE,CAAC,GAAI0B,GAAG,KAAK,CAAC,GAAI,IAAI;EAClC3B,GAAG,CAACC,MAAM,EAAE,CAAC,GAAG0B,GAAG,GAAG,IAAI;EAC1B3B,GAAG,CAACC,MAAM,EAAE,CAAC,GAAK0B,GAAG,KAAK,EAAE,GAAI,GAAG,GAAI,IAAI;EAC3C3B,GAAG,CAACC,MAAM,EAAE,CAAC,GAAI0B,GAAG,KAAK,EAAE,GAAI,IAAI;EACnC3B,GAAG,CAACC,MAAM,EAAE,CAAC,GAAIa,QAAQ,KAAK,CAAC,GAAI,IAAI;EACvCd,GAAG,CAACC,MAAM,EAAE,CAAC,GAAGa,QAAQ,GAAG,IAAI;EAC/B,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;IACxB5B,GAAG,CAACC,MAAM,EAAE,CAAC,GAAGc,IAAI,CAACa,CAAC,CAAC;EAC3B;EACA,OAAO5B,GAAG;AACd;AACA,eAAeF,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}