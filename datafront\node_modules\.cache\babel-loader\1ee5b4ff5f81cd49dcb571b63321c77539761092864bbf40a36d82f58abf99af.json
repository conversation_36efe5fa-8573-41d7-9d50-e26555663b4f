{"ast": null, "code": "import rules from '../rule/';\nimport { isEmptyValue } from '../util';\nfunction date(rule, value, callback, source, options) {\n  // console.log('integer rule called %j', rule);\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  // console.log('validate on %s value', value);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value)) {\n      var dateObject = void 0;\n      if (typeof value === 'number') {\n        dateObject = new Date(value);\n      } else {\n        dateObject = value;\n      }\n      rules.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        rules.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n}\nexport default date;", "map": {"version": 3, "names": ["rules", "isEmptyValue", "date", "rule", "value", "callback", "source", "options", "errors", "validate", "required", "hasOwnProperty", "field", "dateObject", "Date", "type", "range", "getTime"], "sources": ["D:/FastBI/datafront/node_modules/async-validator/es/validator/date.js"], "sourcesContent": ["import rules from '../rule/';\nimport { isEmptyValue } from '../util';\n\nfunction date(rule, value, callback, source, options) {\n  // console.log('integer rule called %j', rule);\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  // console.log('validate on %s value', value);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value)) {\n      var dateObject = void 0;\n\n      if (typeof value === 'number') {\n        dateObject = new Date(value);\n      } else {\n        dateObject = value;\n      }\n\n      rules.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        rules.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n}\n\nexport default date;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,UAAU;AAC5B,SAASC,YAAY,QAAQ,SAAS;AAEtC,SAASC,IAAIA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACpD;EACA,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAACP,IAAI,CAACO,QAAQ,IAAIJ,MAAM,CAACK,cAAc,CAACR,IAAI,CAACS,KAAK,CAAC;EACnF;EACA,IAAIH,QAAQ,EAAE;IACZ,IAAIR,YAAY,CAACG,KAAK,CAAC,IAAI,CAACD,IAAI,CAACO,QAAQ,EAAE;MACzC,OAAOL,QAAQ,CAAC,CAAC;IACnB;IACAL,KAAK,CAACU,QAAQ,CAACP,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IACpD,IAAI,CAACN,YAAY,CAACG,KAAK,CAAC,EAAE;MACxB,IAAIS,UAAU,GAAG,KAAK,CAAC;MAEvB,IAAI,OAAOT,KAAK,KAAK,QAAQ,EAAE;QAC7BS,UAAU,GAAG,IAAIC,IAAI,CAACV,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLS,UAAU,GAAGT,KAAK;MACpB;MAEAJ,KAAK,CAACe,IAAI,CAACZ,IAAI,EAAEU,UAAU,EAAEP,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MACrD,IAAIM,UAAU,EAAE;QACdb,KAAK,CAACgB,KAAK,CAACb,IAAI,EAAEU,UAAU,CAACI,OAAO,CAAC,CAAC,EAAEX,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MAClE;IACF;EACF;EACAF,QAAQ,CAACG,MAAM,CAAC;AAClB;AAEA,eAAeN,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}