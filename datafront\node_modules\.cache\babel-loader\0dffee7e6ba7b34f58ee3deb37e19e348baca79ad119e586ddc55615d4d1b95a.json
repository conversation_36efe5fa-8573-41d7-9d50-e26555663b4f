{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis } from '../../util/states.js';\nimport HeatmapLayer from './HeatmapLayer.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ChartView from '../../view/Chart.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nfunction getIsInPiecewiseRange(dataExtent, pieceList, selected) {\n  var dataSpan = dataExtent[1] - dataExtent[0];\n  pieceList = zrUtil.map(pieceList, function (piece) {\n    return {\n      interval: [(piece.interval[0] - dataExtent[0]) / dataSpan, (piece.interval[1] - dataExtent[0]) / dataSpan]\n    };\n  });\n  var len = pieceList.length;\n  var lastIndex = 0;\n  return function (val) {\n    var i;\n    // Try to find in the location of the last found\n    for (i = lastIndex; i < len; i++) {\n      var interval = pieceList[i].interval;\n      if (interval[0] <= val && val <= interval[1]) {\n        lastIndex = i;\n        break;\n      }\n    }\n    if (i === len) {\n      // Not found, back interation\n      for (i = lastIndex - 1; i >= 0; i--) {\n        var interval = pieceList[i].interval;\n        if (interval[0] <= val && val <= interval[1]) {\n          lastIndex = i;\n          break;\n        }\n      }\n    }\n    return i >= 0 && i < len && selected[i];\n  };\n}\nfunction getIsInContinuousRange(dataExtent, range) {\n  var dataSpan = dataExtent[1] - dataExtent[0];\n  range = [(range[0] - dataExtent[0]) / dataSpan, (range[1] - dataExtent[0]) / dataSpan];\n  return function (val) {\n    return val >= range[0] && val <= range[1];\n  };\n}\nfunction isGeoCoordSys(coordSys) {\n  var dimensions = coordSys.dimensions;\n  // Not use coordSys.type === 'geo' because coordSys maybe extended\n  return dimensions[0] === 'lng' && dimensions[1] === 'lat';\n}\nvar HeatmapView = /** @class */function (_super) {\n  __extends(HeatmapView, _super);\n  function HeatmapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = HeatmapView.type;\n    return _this;\n  }\n  HeatmapView.prototype.render = function (seriesModel, ecModel, api) {\n    var visualMapOfThisSeries;\n    ecModel.eachComponent('visualMap', function (visualMap) {\n      visualMap.eachTargetSeries(function (targetSeries) {\n        if (targetSeries === seriesModel) {\n          visualMapOfThisSeries = visualMap;\n        }\n      });\n    });\n    if (process.env.NODE_ENV !== 'production') {\n      if (!visualMapOfThisSeries) {\n        throw new Error('Heatmap must use with visualMap');\n      }\n    }\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    this.group.removeAll();\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys.type === 'cartesian2d' || coordSys.type === 'calendar') {\n      this._renderOnCartesianAndCalendar(seriesModel, api, 0, seriesModel.getData().count());\n    } else if (isGeoCoordSys(coordSys)) {\n      this._renderOnGeo(coordSys, seriesModel, visualMapOfThisSeries, api);\n    }\n  };\n  HeatmapView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    this.group.removeAll();\n  };\n  HeatmapView.prototype.incrementalRender = function (params, seriesModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys) {\n      // geo does not support incremental rendering?\n      if (isGeoCoordSys(coordSys)) {\n        this.render(seriesModel, ecModel, api);\n      } else {\n        this._progressiveEls = [];\n        this._renderOnCartesianAndCalendar(seriesModel, api, params.start, params.end, true);\n      }\n    }\n  };\n  HeatmapView.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  HeatmapView.prototype._renderOnCartesianAndCalendar = function (seriesModel, api, start, end, incremental) {\n    var coordSys = seriesModel.coordinateSystem;\n    var isCartesian2d = isCoordinateSystemType(coordSys, 'cartesian2d');\n    var width;\n    var height;\n    var xAxisExtent;\n    var yAxisExtent;\n    if (isCartesian2d) {\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      if (process.env.NODE_ENV !== 'production') {\n        if (!(xAxis.type === 'category' && yAxis.type === 'category')) {\n          throw new Error('Heatmap on cartesian must have two category axes');\n        }\n        if (!(xAxis.onBand && yAxis.onBand)) {\n          throw new Error('Heatmap on cartesian must have two axes with boundaryGap true');\n        }\n      }\n      // add 0.5px to avoid the gaps\n      width = xAxis.getBandWidth() + .5;\n      height = yAxis.getBandWidth() + .5;\n      xAxisExtent = xAxis.scale.getExtent();\n      yAxisExtent = yAxis.scale.getExtent();\n    }\n    var group = this.group;\n    var data = seriesModel.getData();\n    var emphasisStyle = seriesModel.getModel(['emphasis', 'itemStyle']).getItemStyle();\n    var blurStyle = seriesModel.getModel(['blur', 'itemStyle']).getItemStyle();\n    var selectStyle = seriesModel.getModel(['select', 'itemStyle']).getItemStyle();\n    var borderRadius = seriesModel.get(['itemStyle', 'borderRadius']);\n    var labelStatesModels = getLabelStatesModels(seriesModel);\n    var emphasisModel = seriesModel.getModel('emphasis');\n    var focus = emphasisModel.get('focus');\n    var blurScope = emphasisModel.get('blurScope');\n    var emphasisDisabled = emphasisModel.get('disabled');\n    var dataDims = isCartesian2d ? [data.mapDimension('x'), data.mapDimension('y'), data.mapDimension('value')] : [data.mapDimension('time'), data.mapDimension('value')];\n    for (var idx = start; idx < end; idx++) {\n      var rect = void 0;\n      var style = data.getItemVisual(idx, 'style');\n      if (isCartesian2d) {\n        var dataDimX = data.get(dataDims[0], idx);\n        var dataDimY = data.get(dataDims[1], idx);\n        // Ignore empty data and out of extent data\n        if (isNaN(data.get(dataDims[2], idx)) || isNaN(dataDimX) || isNaN(dataDimY) || dataDimX < xAxisExtent[0] || dataDimX > xAxisExtent[1] || dataDimY < yAxisExtent[0] || dataDimY > yAxisExtent[1]) {\n          continue;\n        }\n        var point = coordSys.dataToPoint([dataDimX, dataDimY]);\n        rect = new graphic.Rect({\n          shape: {\n            x: point[0] - width / 2,\n            y: point[1] - height / 2,\n            width: width,\n            height: height\n          },\n          style: style\n        });\n      } else {\n        // Ignore empty data\n        if (isNaN(data.get(dataDims[1], idx))) {\n          continue;\n        }\n        rect = new graphic.Rect({\n          z2: 1,\n          shape: coordSys.dataToRect([data.get(dataDims[0], idx)]).contentShape,\n          style: style\n        });\n      }\n      // Optimization for large dataset\n      if (data.hasItemOption) {\n        var itemModel = data.getItemModel(idx);\n        var emphasisModel_1 = itemModel.getModel('emphasis');\n        emphasisStyle = emphasisModel_1.getModel('itemStyle').getItemStyle();\n        blurStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n        selectStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n        // Each item value struct in the data would be firstly\n        // {\n        //     itemStyle: { borderRadius: [30, 30] },\n        //     value: [2022, 02, 22]\n        // }\n        borderRadius = itemModel.get(['itemStyle', 'borderRadius']);\n        focus = emphasisModel_1.get('focus');\n        blurScope = emphasisModel_1.get('blurScope');\n        emphasisDisabled = emphasisModel_1.get('disabled');\n        labelStatesModels = getLabelStatesModels(itemModel);\n      }\n      rect.shape.r = borderRadius;\n      var rawValue = seriesModel.getRawValue(idx);\n      var defaultText = '-';\n      if (rawValue && rawValue[2] != null) {\n        defaultText = rawValue[2] + '';\n      }\n      setLabelStyle(rect, labelStatesModels, {\n        labelFetcher: seriesModel,\n        labelDataIndex: idx,\n        defaultOpacity: style.opacity,\n        defaultText: defaultText\n      });\n      rect.ensureState('emphasis').style = emphasisStyle;\n      rect.ensureState('blur').style = blurStyle;\n      rect.ensureState('select').style = selectStyle;\n      toggleHoverEmphasis(rect, focus, blurScope, emphasisDisabled);\n      rect.incremental = incremental;\n      // PENDING\n      if (incremental) {\n        // Rect must use hover layer if it's incremental.\n        rect.states.emphasis.hoverLayer = true;\n      }\n      group.add(rect);\n      data.setItemGraphicEl(idx, rect);\n      if (this._progressiveEls) {\n        this._progressiveEls.push(rect);\n      }\n    }\n  };\n  HeatmapView.prototype._renderOnGeo = function (geo, seriesModel, visualMapModel, api) {\n    var inRangeVisuals = visualMapModel.targetVisuals.inRange;\n    var outOfRangeVisuals = visualMapModel.targetVisuals.outOfRange;\n    // if (!visualMapping) {\n    //     throw new Error('Data range must have color visuals');\n    // }\n    var data = seriesModel.getData();\n    var hmLayer = this._hmLayer || this._hmLayer || new HeatmapLayer();\n    hmLayer.blurSize = seriesModel.get('blurSize');\n    hmLayer.pointSize = seriesModel.get('pointSize');\n    hmLayer.minOpacity = seriesModel.get('minOpacity');\n    hmLayer.maxOpacity = seriesModel.get('maxOpacity');\n    var rect = geo.getViewRect().clone();\n    var roamTransform = geo.getRoamTransform();\n    rect.applyTransform(roamTransform);\n    // Clamp on viewport\n    var x = Math.max(rect.x, 0);\n    var y = Math.max(rect.y, 0);\n    var x2 = Math.min(rect.width + rect.x, api.getWidth());\n    var y2 = Math.min(rect.height + rect.y, api.getHeight());\n    var width = x2 - x;\n    var height = y2 - y;\n    var dims = [data.mapDimension('lng'), data.mapDimension('lat'), data.mapDimension('value')];\n    var points = data.mapArray(dims, function (lng, lat, value) {\n      var pt = geo.dataToPoint([lng, lat]);\n      pt[0] -= x;\n      pt[1] -= y;\n      pt.push(value);\n      return pt;\n    });\n    var dataExtent = visualMapModel.getExtent();\n    var isInRange = visualMapModel.type === 'visualMap.continuous' ? getIsInContinuousRange(dataExtent, visualMapModel.option.range) : getIsInPiecewiseRange(dataExtent, visualMapModel.getPieceList(), visualMapModel.option.selected);\n    hmLayer.update(points, width, height, inRangeVisuals.color.getNormalizer(), {\n      inRange: inRangeVisuals.color.getColorMapper(),\n      outOfRange: outOfRangeVisuals.color.getColorMapper()\n    }, isInRange);\n    var img = new graphic.Image({\n      style: {\n        width: width,\n        height: height,\n        x: x,\n        y: y,\n        image: hmLayer.canvas\n      },\n      silent: true\n    });\n    this.group.add(img);\n  };\n  HeatmapView.type = 'heatmap';\n  return HeatmapView;\n}(ChartView);\nexport default HeatmapView;", "map": {"version": 3, "names": ["__extends", "graphic", "toggleHoverEmphasis", "Heatmap<PERSON>ayer", "zrUtil", "ChartView", "isCoordinateSystemType", "setLabelStyle", "getLabelStatesModels", "getIsInPiecewiseRange", "dataExtent", "pieceList", "selected", "dataSpan", "map", "piece", "interval", "len", "length", "lastIndex", "val", "i", "getIsInContinuousRange", "range", "isGeoCoordSys", "coordSys", "dimensions", "HeatmapView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "seriesModel", "ecModel", "api", "visualMapOfThisSeries", "eachComponent", "visualMap", "eachTargetSeries", "targetSeries", "process", "env", "NODE_ENV", "Error", "_progressiveEls", "group", "removeAll", "coordinateSystem", "_renderOnCartesianAndCalendar", "getData", "count", "_renderOnGeo", "incrementalPrepareRender", "incrementalRender", "params", "start", "end", "eachRendered", "cb", "traverseElements", "incremental", "isCartesian2d", "width", "height", "xAxisExtent", "yAxisExtent", "xAxis", "getAxis", "yAxis", "onBand", "getBandWidth", "scale", "getExtent", "data", "emphasisStyle", "getModel", "getItemStyle", "blurStyle", "selectStyle", "borderRadius", "get", "labelStatesModels", "emphasisModel", "focus", "blurScope", "emphasisDisabled", "dataDims", "mapDimension", "idx", "rect", "style", "getItemVisual", "dataDimX", "dataDimY", "isNaN", "point", "dataToPoint", "Rect", "shape", "x", "y", "z2", "dataToRect", "contentShape", "hasItemOption", "itemModel", "getItemModel", "emphasisModel_1", "r", "rawValue", "getRawValue", "defaultText", "labelFetcher", "labelDataIndex", "defaultOpacity", "opacity", "ensureState", "states", "emphasis", "hoverLayer", "add", "setItemGraphicEl", "push", "geo", "visualMapModel", "inRangeVisuals", "targetVisuals", "inRange", "outOfRangeVisuals", "outOfRange", "hm<PERSON><PERSON><PERSON>", "_hmLayer", "blurSize", "pointSize", "minOpacity", "maxOpacity", "getViewRect", "clone", "roamTransform", "getRoamTransform", "applyTransform", "Math", "max", "x2", "min", "getWidth", "y2", "getHeight", "dims", "points", "mapArray", "lng", "lat", "value", "pt", "isInRange", "option", "getPieceList", "update", "color", "getNormalizer", "getColorMapper", "img", "Image", "image", "canvas", "silent"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/heatmap/HeatmapView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis } from '../../util/states.js';\nimport HeatmapLayer from './HeatmapLayer.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ChartView from '../../view/Chart.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nfunction getIsInPiecewiseRange(dataExtent, pieceList, selected) {\n  var dataSpan = dataExtent[1] - dataExtent[0];\n  pieceList = zrUtil.map(pieceList, function (piece) {\n    return {\n      interval: [(piece.interval[0] - dataExtent[0]) / dataSpan, (piece.interval[1] - dataExtent[0]) / dataSpan]\n    };\n  });\n  var len = pieceList.length;\n  var lastIndex = 0;\n  return function (val) {\n    var i;\n    // Try to find in the location of the last found\n    for (i = lastIndex; i < len; i++) {\n      var interval = pieceList[i].interval;\n      if (interval[0] <= val && val <= interval[1]) {\n        lastIndex = i;\n        break;\n      }\n    }\n    if (i === len) {\n      // Not found, back interation\n      for (i = lastIndex - 1; i >= 0; i--) {\n        var interval = pieceList[i].interval;\n        if (interval[0] <= val && val <= interval[1]) {\n          lastIndex = i;\n          break;\n        }\n      }\n    }\n    return i >= 0 && i < len && selected[i];\n  };\n}\nfunction getIsInContinuousRange(dataExtent, range) {\n  var dataSpan = dataExtent[1] - dataExtent[0];\n  range = [(range[0] - dataExtent[0]) / dataSpan, (range[1] - dataExtent[0]) / dataSpan];\n  return function (val) {\n    return val >= range[0] && val <= range[1];\n  };\n}\nfunction isGeoCoordSys(coordSys) {\n  var dimensions = coordSys.dimensions;\n  // Not use coordSys.type === 'geo' because coordSys maybe extended\n  return dimensions[0] === 'lng' && dimensions[1] === 'lat';\n}\nvar HeatmapView = /** @class */function (_super) {\n  __extends(HeatmapView, _super);\n  function HeatmapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = HeatmapView.type;\n    return _this;\n  }\n  HeatmapView.prototype.render = function (seriesModel, ecModel, api) {\n    var visualMapOfThisSeries;\n    ecModel.eachComponent('visualMap', function (visualMap) {\n      visualMap.eachTargetSeries(function (targetSeries) {\n        if (targetSeries === seriesModel) {\n          visualMapOfThisSeries = visualMap;\n        }\n      });\n    });\n    if (process.env.NODE_ENV !== 'production') {\n      if (!visualMapOfThisSeries) {\n        throw new Error('Heatmap must use with visualMap');\n      }\n    }\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    this.group.removeAll();\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys.type === 'cartesian2d' || coordSys.type === 'calendar') {\n      this._renderOnCartesianAndCalendar(seriesModel, api, 0, seriesModel.getData().count());\n    } else if (isGeoCoordSys(coordSys)) {\n      this._renderOnGeo(coordSys, seriesModel, visualMapOfThisSeries, api);\n    }\n  };\n  HeatmapView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    this.group.removeAll();\n  };\n  HeatmapView.prototype.incrementalRender = function (params, seriesModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (coordSys) {\n      // geo does not support incremental rendering?\n      if (isGeoCoordSys(coordSys)) {\n        this.render(seriesModel, ecModel, api);\n      } else {\n        this._progressiveEls = [];\n        this._renderOnCartesianAndCalendar(seriesModel, api, params.start, params.end, true);\n      }\n    }\n  };\n  HeatmapView.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  HeatmapView.prototype._renderOnCartesianAndCalendar = function (seriesModel, api, start, end, incremental) {\n    var coordSys = seriesModel.coordinateSystem;\n    var isCartesian2d = isCoordinateSystemType(coordSys, 'cartesian2d');\n    var width;\n    var height;\n    var xAxisExtent;\n    var yAxisExtent;\n    if (isCartesian2d) {\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      if (process.env.NODE_ENV !== 'production') {\n        if (!(xAxis.type === 'category' && yAxis.type === 'category')) {\n          throw new Error('Heatmap on cartesian must have two category axes');\n        }\n        if (!(xAxis.onBand && yAxis.onBand)) {\n          throw new Error('Heatmap on cartesian must have two axes with boundaryGap true');\n        }\n      }\n      // add 0.5px to avoid the gaps\n      width = xAxis.getBandWidth() + .5;\n      height = yAxis.getBandWidth() + .5;\n      xAxisExtent = xAxis.scale.getExtent();\n      yAxisExtent = yAxis.scale.getExtent();\n    }\n    var group = this.group;\n    var data = seriesModel.getData();\n    var emphasisStyle = seriesModel.getModel(['emphasis', 'itemStyle']).getItemStyle();\n    var blurStyle = seriesModel.getModel(['blur', 'itemStyle']).getItemStyle();\n    var selectStyle = seriesModel.getModel(['select', 'itemStyle']).getItemStyle();\n    var borderRadius = seriesModel.get(['itemStyle', 'borderRadius']);\n    var labelStatesModels = getLabelStatesModels(seriesModel);\n    var emphasisModel = seriesModel.getModel('emphasis');\n    var focus = emphasisModel.get('focus');\n    var blurScope = emphasisModel.get('blurScope');\n    var emphasisDisabled = emphasisModel.get('disabled');\n    var dataDims = isCartesian2d ? [data.mapDimension('x'), data.mapDimension('y'), data.mapDimension('value')] : [data.mapDimension('time'), data.mapDimension('value')];\n    for (var idx = start; idx < end; idx++) {\n      var rect = void 0;\n      var style = data.getItemVisual(idx, 'style');\n      if (isCartesian2d) {\n        var dataDimX = data.get(dataDims[0], idx);\n        var dataDimY = data.get(dataDims[1], idx);\n        // Ignore empty data and out of extent data\n        if (isNaN(data.get(dataDims[2], idx)) || isNaN(dataDimX) || isNaN(dataDimY) || dataDimX < xAxisExtent[0] || dataDimX > xAxisExtent[1] || dataDimY < yAxisExtent[0] || dataDimY > yAxisExtent[1]) {\n          continue;\n        }\n        var point = coordSys.dataToPoint([dataDimX, dataDimY]);\n        rect = new graphic.Rect({\n          shape: {\n            x: point[0] - width / 2,\n            y: point[1] - height / 2,\n            width: width,\n            height: height\n          },\n          style: style\n        });\n      } else {\n        // Ignore empty data\n        if (isNaN(data.get(dataDims[1], idx))) {\n          continue;\n        }\n        rect = new graphic.Rect({\n          z2: 1,\n          shape: coordSys.dataToRect([data.get(dataDims[0], idx)]).contentShape,\n          style: style\n        });\n      }\n      // Optimization for large dataset\n      if (data.hasItemOption) {\n        var itemModel = data.getItemModel(idx);\n        var emphasisModel_1 = itemModel.getModel('emphasis');\n        emphasisStyle = emphasisModel_1.getModel('itemStyle').getItemStyle();\n        blurStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n        selectStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n        // Each item value struct in the data would be firstly\n        // {\n        //     itemStyle: { borderRadius: [30, 30] },\n        //     value: [2022, 02, 22]\n        // }\n        borderRadius = itemModel.get(['itemStyle', 'borderRadius']);\n        focus = emphasisModel_1.get('focus');\n        blurScope = emphasisModel_1.get('blurScope');\n        emphasisDisabled = emphasisModel_1.get('disabled');\n        labelStatesModels = getLabelStatesModels(itemModel);\n      }\n      rect.shape.r = borderRadius;\n      var rawValue = seriesModel.getRawValue(idx);\n      var defaultText = '-';\n      if (rawValue && rawValue[2] != null) {\n        defaultText = rawValue[2] + '';\n      }\n      setLabelStyle(rect, labelStatesModels, {\n        labelFetcher: seriesModel,\n        labelDataIndex: idx,\n        defaultOpacity: style.opacity,\n        defaultText: defaultText\n      });\n      rect.ensureState('emphasis').style = emphasisStyle;\n      rect.ensureState('blur').style = blurStyle;\n      rect.ensureState('select').style = selectStyle;\n      toggleHoverEmphasis(rect, focus, blurScope, emphasisDisabled);\n      rect.incremental = incremental;\n      // PENDING\n      if (incremental) {\n        // Rect must use hover layer if it's incremental.\n        rect.states.emphasis.hoverLayer = true;\n      }\n      group.add(rect);\n      data.setItemGraphicEl(idx, rect);\n      if (this._progressiveEls) {\n        this._progressiveEls.push(rect);\n      }\n    }\n  };\n  HeatmapView.prototype._renderOnGeo = function (geo, seriesModel, visualMapModel, api) {\n    var inRangeVisuals = visualMapModel.targetVisuals.inRange;\n    var outOfRangeVisuals = visualMapModel.targetVisuals.outOfRange;\n    // if (!visualMapping) {\n    //     throw new Error('Data range must have color visuals');\n    // }\n    var data = seriesModel.getData();\n    var hmLayer = this._hmLayer || this._hmLayer || new HeatmapLayer();\n    hmLayer.blurSize = seriesModel.get('blurSize');\n    hmLayer.pointSize = seriesModel.get('pointSize');\n    hmLayer.minOpacity = seriesModel.get('minOpacity');\n    hmLayer.maxOpacity = seriesModel.get('maxOpacity');\n    var rect = geo.getViewRect().clone();\n    var roamTransform = geo.getRoamTransform();\n    rect.applyTransform(roamTransform);\n    // Clamp on viewport\n    var x = Math.max(rect.x, 0);\n    var y = Math.max(rect.y, 0);\n    var x2 = Math.min(rect.width + rect.x, api.getWidth());\n    var y2 = Math.min(rect.height + rect.y, api.getHeight());\n    var width = x2 - x;\n    var height = y2 - y;\n    var dims = [data.mapDimension('lng'), data.mapDimension('lat'), data.mapDimension('value')];\n    var points = data.mapArray(dims, function (lng, lat, value) {\n      var pt = geo.dataToPoint([lng, lat]);\n      pt[0] -= x;\n      pt[1] -= y;\n      pt.push(value);\n      return pt;\n    });\n    var dataExtent = visualMapModel.getExtent();\n    var isInRange = visualMapModel.type === 'visualMap.continuous' ? getIsInContinuousRange(dataExtent, visualMapModel.option.range) : getIsInPiecewiseRange(dataExtent, visualMapModel.getPieceList(), visualMapModel.option.selected);\n    hmLayer.update(points, width, height, inRangeVisuals.color.getNormalizer(), {\n      inRange: inRangeVisuals.color.getColorMapper(),\n      outOfRange: outOfRangeVisuals.color.getColorMapper()\n    }, isInRange);\n    var img = new graphic.Image({\n      style: {\n        width: width,\n        height: height,\n        x: x,\n        y: y,\n        image: hmLayer.canvas\n      },\n      silent: true\n    });\n    this.group.add(img);\n  };\n  HeatmapView.type = 'heatmap';\n  return HeatmapView;\n}(ChartView);\nexport default HeatmapView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,aAAa,EAAEC,oBAAoB,QAAQ,2BAA2B;AAC/E,SAASC,qBAAqBA,CAACC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC9D,IAAIC,QAAQ,GAAGH,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;EAC5CC,SAAS,GAAGP,MAAM,CAACU,GAAG,CAACH,SAAS,EAAE,UAAUI,KAAK,EAAE;IACjD,OAAO;MACLC,QAAQ,EAAE,CAAC,CAACD,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAGN,UAAU,CAAC,CAAC,CAAC,IAAIG,QAAQ,EAAE,CAACE,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAGN,UAAU,CAAC,CAAC,CAAC,IAAIG,QAAQ;IAC3G,CAAC;EACH,CAAC,CAAC;EACF,IAAII,GAAG,GAAGN,SAAS,CAACO,MAAM;EAC1B,IAAIC,SAAS,GAAG,CAAC;EACjB,OAAO,UAAUC,GAAG,EAAE;IACpB,IAAIC,CAAC;IACL;IACA,KAAKA,CAAC,GAAGF,SAAS,EAAEE,CAAC,GAAGJ,GAAG,EAAEI,CAAC,EAAE,EAAE;MAChC,IAAIL,QAAQ,GAAGL,SAAS,CAACU,CAAC,CAAC,CAACL,QAAQ;MACpC,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAII,GAAG,IAAIA,GAAG,IAAIJ,QAAQ,CAAC,CAAC,CAAC,EAAE;QAC5CG,SAAS,GAAGE,CAAC;QACb;MACF;IACF;IACA,IAAIA,CAAC,KAAKJ,GAAG,EAAE;MACb;MACA,KAAKI,CAAC,GAAGF,SAAS,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACnC,IAAIL,QAAQ,GAAGL,SAAS,CAACU,CAAC,CAAC,CAACL,QAAQ;QACpC,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAII,GAAG,IAAIA,GAAG,IAAIJ,QAAQ,CAAC,CAAC,CAAC,EAAE;UAC5CG,SAAS,GAAGE,CAAC;UACb;QACF;MACF;IACF;IACA,OAAOA,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGJ,GAAG,IAAIL,QAAQ,CAACS,CAAC,CAAC;EACzC,CAAC;AACH;AACA,SAASC,sBAAsBA,CAACZ,UAAU,EAAEa,KAAK,EAAE;EACjD,IAAIV,QAAQ,GAAGH,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;EAC5Ca,KAAK,GAAG,CAAC,CAACA,KAAK,CAAC,CAAC,CAAC,GAAGb,UAAU,CAAC,CAAC,CAAC,IAAIG,QAAQ,EAAE,CAACU,KAAK,CAAC,CAAC,CAAC,GAAGb,UAAU,CAAC,CAAC,CAAC,IAAIG,QAAQ,CAAC;EACtF,OAAO,UAAUO,GAAG,EAAE;IACpB,OAAOA,GAAG,IAAIG,KAAK,CAAC,CAAC,CAAC,IAAIH,GAAG,IAAIG,KAAK,CAAC,CAAC,CAAC;EAC3C,CAAC;AACH;AACA,SAASC,aAAaA,CAACC,QAAQ,EAAE;EAC/B,IAAIC,UAAU,GAAGD,QAAQ,CAACC,UAAU;EACpC;EACA,OAAOA,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,KAAK;AAC3D;AACA,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/C5B,SAAS,CAAC2B,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,WAAW,CAACK,IAAI;IAC7B,OAAOH,KAAK;EACd;EACAF,WAAW,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAClE,IAAIC,qBAAqB;IACzBF,OAAO,CAACG,aAAa,CAAC,WAAW,EAAE,UAAUC,SAAS,EAAE;MACtDA,SAAS,CAACC,gBAAgB,CAAC,UAAUC,YAAY,EAAE;QACjD,IAAIA,YAAY,KAAKP,WAAW,EAAE;UAChCG,qBAAqB,GAAGE,SAAS;QACnC;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACP,qBAAqB,EAAE;QAC1B,MAAM,IAAIQ,KAAK,CAAC,iCAAiC,CAAC;MACpD;IACF;IACA;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAIxB,QAAQ,GAAGU,WAAW,CAACe,gBAAgB;IAC3C,IAAIzB,QAAQ,CAACO,IAAI,KAAK,aAAa,IAAIP,QAAQ,CAACO,IAAI,KAAK,UAAU,EAAE;MACnE,IAAI,CAACmB,6BAA6B,CAAChB,WAAW,EAAEE,GAAG,EAAE,CAAC,EAAEF,WAAW,CAACiB,OAAO,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;IACxF,CAAC,MAAM,IAAI7B,aAAa,CAACC,QAAQ,CAAC,EAAE;MAClC,IAAI,CAAC6B,YAAY,CAAC7B,QAAQ,EAAEU,WAAW,EAAEG,qBAAqB,EAAED,GAAG,CAAC;IACtE;EACF,CAAC;EACDV,WAAW,CAACM,SAAS,CAACsB,wBAAwB,GAAG,UAAUpB,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACpF,IAAI,CAACW,KAAK,CAACC,SAAS,CAAC,CAAC;EACxB,CAAC;EACDtB,WAAW,CAACM,SAAS,CAACuB,iBAAiB,GAAG,UAAUC,MAAM,EAAEtB,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACrF,IAAIZ,QAAQ,GAAGU,WAAW,CAACe,gBAAgB;IAC3C,IAAIzB,QAAQ,EAAE;MACZ;MACA,IAAID,aAAa,CAACC,QAAQ,CAAC,EAAE;QAC3B,IAAI,CAACS,MAAM,CAACC,WAAW,EAAEC,OAAO,EAAEC,GAAG,CAAC;MACxC,CAAC,MAAM;QACL,IAAI,CAACU,eAAe,GAAG,EAAE;QACzB,IAAI,CAACI,6BAA6B,CAAChB,WAAW,EAAEE,GAAG,EAAEoB,MAAM,CAACC,KAAK,EAAED,MAAM,CAACE,GAAG,EAAE,IAAI,CAAC;MACtF;IACF;EACF,CAAC;EACDhC,WAAW,CAACM,SAAS,CAAC2B,YAAY,GAAG,UAAUC,EAAE,EAAE;IACjD5D,OAAO,CAAC6D,gBAAgB,CAAC,IAAI,CAACf,eAAe,IAAI,IAAI,CAACC,KAAK,EAAEa,EAAE,CAAC;EAClE,CAAC;EACDlC,WAAW,CAACM,SAAS,CAACkB,6BAA6B,GAAG,UAAUhB,WAAW,EAAEE,GAAG,EAAEqB,KAAK,EAAEC,GAAG,EAAEI,WAAW,EAAE;IACzG,IAAItC,QAAQ,GAAGU,WAAW,CAACe,gBAAgB;IAC3C,IAAIc,aAAa,GAAG1D,sBAAsB,CAACmB,QAAQ,EAAE,aAAa,CAAC;IACnE,IAAIwC,KAAK;IACT,IAAIC,MAAM;IACV,IAAIC,WAAW;IACf,IAAIC,WAAW;IACf,IAAIJ,aAAa,EAAE;MACjB,IAAIK,KAAK,GAAG5C,QAAQ,CAAC6C,OAAO,CAAC,GAAG,CAAC;MACjC,IAAIC,KAAK,GAAG9C,QAAQ,CAAC6C,OAAO,CAAC,GAAG,CAAC;MACjC,IAAI3B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,EAAEwB,KAAK,CAACrC,IAAI,KAAK,UAAU,IAAIuC,KAAK,CAACvC,IAAI,KAAK,UAAU,CAAC,EAAE;UAC7D,MAAM,IAAIc,KAAK,CAAC,kDAAkD,CAAC;QACrE;QACA,IAAI,EAAEuB,KAAK,CAACG,MAAM,IAAID,KAAK,CAACC,MAAM,CAAC,EAAE;UACnC,MAAM,IAAI1B,KAAK,CAAC,+DAA+D,CAAC;QAClF;MACF;MACA;MACAmB,KAAK,GAAGI,KAAK,CAACI,YAAY,CAAC,CAAC,GAAG,EAAE;MACjCP,MAAM,GAAGK,KAAK,CAACE,YAAY,CAAC,CAAC,GAAG,EAAE;MAClCN,WAAW,GAAGE,KAAK,CAACK,KAAK,CAACC,SAAS,CAAC,CAAC;MACrCP,WAAW,GAAGG,KAAK,CAACG,KAAK,CAACC,SAAS,CAAC,CAAC;IACvC;IACA,IAAI3B,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI4B,IAAI,GAAGzC,WAAW,CAACiB,OAAO,CAAC,CAAC;IAChC,IAAIyB,aAAa,GAAG1C,WAAW,CAAC2C,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IAClF,IAAIC,SAAS,GAAG7C,WAAW,CAAC2C,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IAC1E,IAAIE,WAAW,GAAG9C,WAAW,CAAC2C,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IAC9E,IAAIG,YAAY,GAAG/C,WAAW,CAACgD,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;IACjE,IAAIC,iBAAiB,GAAG5E,oBAAoB,CAAC2B,WAAW,CAAC;IACzD,IAAIkD,aAAa,GAAGlD,WAAW,CAAC2C,QAAQ,CAAC,UAAU,CAAC;IACpD,IAAIQ,KAAK,GAAGD,aAAa,CAACF,GAAG,CAAC,OAAO,CAAC;IACtC,IAAII,SAAS,GAAGF,aAAa,CAACF,GAAG,CAAC,WAAW,CAAC;IAC9C,IAAIK,gBAAgB,GAAGH,aAAa,CAACF,GAAG,CAAC,UAAU,CAAC;IACpD,IAAIM,QAAQ,GAAGzB,aAAa,GAAG,CAACY,IAAI,CAACc,YAAY,CAAC,GAAG,CAAC,EAAEd,IAAI,CAACc,YAAY,CAAC,GAAG,CAAC,EAAEd,IAAI,CAACc,YAAY,CAAC,OAAO,CAAC,CAAC,GAAG,CAACd,IAAI,CAACc,YAAY,CAAC,MAAM,CAAC,EAAEd,IAAI,CAACc,YAAY,CAAC,OAAO,CAAC,CAAC;IACrK,KAAK,IAAIC,GAAG,GAAGjC,KAAK,EAAEiC,GAAG,GAAGhC,GAAG,EAAEgC,GAAG,EAAE,EAAE;MACtC,IAAIC,IAAI,GAAG,KAAK,CAAC;MACjB,IAAIC,KAAK,GAAGjB,IAAI,CAACkB,aAAa,CAACH,GAAG,EAAE,OAAO,CAAC;MAC5C,IAAI3B,aAAa,EAAE;QACjB,IAAI+B,QAAQ,GAAGnB,IAAI,CAACO,GAAG,CAACM,QAAQ,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC;QACzC,IAAIK,QAAQ,GAAGpB,IAAI,CAACO,GAAG,CAACM,QAAQ,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC;QACzC;QACA,IAAIM,KAAK,CAACrB,IAAI,CAACO,GAAG,CAACM,QAAQ,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC,CAAC,IAAIM,KAAK,CAACF,QAAQ,CAAC,IAAIE,KAAK,CAACD,QAAQ,CAAC,IAAID,QAAQ,GAAG5B,WAAW,CAAC,CAAC,CAAC,IAAI4B,QAAQ,GAAG5B,WAAW,CAAC,CAAC,CAAC,IAAI6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC,CAAC,IAAI4B,QAAQ,GAAG5B,WAAW,CAAC,CAAC,CAAC,EAAE;UAC/L;QACF;QACA,IAAI8B,KAAK,GAAGzE,QAAQ,CAAC0E,WAAW,CAAC,CAACJ,QAAQ,EAAEC,QAAQ,CAAC,CAAC;QACtDJ,IAAI,GAAG,IAAI3F,OAAO,CAACmG,IAAI,CAAC;UACtBC,KAAK,EAAE;YACLC,CAAC,EAAEJ,KAAK,CAAC,CAAC,CAAC,GAAGjC,KAAK,GAAG,CAAC;YACvBsC,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC,GAAGhC,MAAM,GAAG,CAAC;YACxBD,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA;UACV,CAAC;UACD2B,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,IAAII,KAAK,CAACrB,IAAI,CAACO,GAAG,CAACM,QAAQ,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC,CAAC,EAAE;UACrC;QACF;QACAC,IAAI,GAAG,IAAI3F,OAAO,CAACmG,IAAI,CAAC;UACtBI,EAAE,EAAE,CAAC;UACLH,KAAK,EAAE5E,QAAQ,CAACgF,UAAU,CAAC,CAAC7B,IAAI,CAACO,GAAG,CAACM,QAAQ,CAAC,CAAC,CAAC,EAAEE,GAAG,CAAC,CAAC,CAAC,CAACe,YAAY;UACrEb,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ;MACA;MACA,IAAIjB,IAAI,CAAC+B,aAAa,EAAE;QACtB,IAAIC,SAAS,GAAGhC,IAAI,CAACiC,YAAY,CAAClB,GAAG,CAAC;QACtC,IAAImB,eAAe,GAAGF,SAAS,CAAC9B,QAAQ,CAAC,UAAU,CAAC;QACpDD,aAAa,GAAGiC,eAAe,CAAChC,QAAQ,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;QACpEC,SAAS,GAAG4B,SAAS,CAAC9B,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;QACpEE,WAAW,GAAG2B,SAAS,CAAC9B,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;QACxE;QACA;QACA;QACA;QACA;QACAG,YAAY,GAAG0B,SAAS,CAACzB,GAAG,CAAC,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAC3DG,KAAK,GAAGwB,eAAe,CAAC3B,GAAG,CAAC,OAAO,CAAC;QACpCI,SAAS,GAAGuB,eAAe,CAAC3B,GAAG,CAAC,WAAW,CAAC;QAC5CK,gBAAgB,GAAGsB,eAAe,CAAC3B,GAAG,CAAC,UAAU,CAAC;QAClDC,iBAAiB,GAAG5E,oBAAoB,CAACoG,SAAS,CAAC;MACrD;MACAhB,IAAI,CAACS,KAAK,CAACU,CAAC,GAAG7B,YAAY;MAC3B,IAAI8B,QAAQ,GAAG7E,WAAW,CAAC8E,WAAW,CAACtB,GAAG,CAAC;MAC3C,IAAIuB,WAAW,GAAG,GAAG;MACrB,IAAIF,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;QACnCE,WAAW,GAAGF,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE;MAChC;MACAzG,aAAa,CAACqF,IAAI,EAAER,iBAAiB,EAAE;QACrC+B,YAAY,EAAEhF,WAAW;QACzBiF,cAAc,EAAEzB,GAAG;QACnB0B,cAAc,EAAExB,KAAK,CAACyB,OAAO;QAC7BJ,WAAW,EAAEA;MACf,CAAC,CAAC;MACFtB,IAAI,CAAC2B,WAAW,CAAC,UAAU,CAAC,CAAC1B,KAAK,GAAGhB,aAAa;MAClDe,IAAI,CAAC2B,WAAW,CAAC,MAAM,CAAC,CAAC1B,KAAK,GAAGb,SAAS;MAC1CY,IAAI,CAAC2B,WAAW,CAAC,QAAQ,CAAC,CAAC1B,KAAK,GAAGZ,WAAW;MAC9C/E,mBAAmB,CAAC0F,IAAI,EAAEN,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC;MAC7DI,IAAI,CAAC7B,WAAW,GAAGA,WAAW;MAC9B;MACA,IAAIA,WAAW,EAAE;QACf;QACA6B,IAAI,CAAC4B,MAAM,CAACC,QAAQ,CAACC,UAAU,GAAG,IAAI;MACxC;MACA1E,KAAK,CAAC2E,GAAG,CAAC/B,IAAI,CAAC;MACfhB,IAAI,CAACgD,gBAAgB,CAACjC,GAAG,EAAEC,IAAI,CAAC;MAChC,IAAI,IAAI,CAAC7C,eAAe,EAAE;QACxB,IAAI,CAACA,eAAe,CAAC8E,IAAI,CAACjC,IAAI,CAAC;MACjC;IACF;EACF,CAAC;EACDjE,WAAW,CAACM,SAAS,CAACqB,YAAY,GAAG,UAAUwE,GAAG,EAAE3F,WAAW,EAAE4F,cAAc,EAAE1F,GAAG,EAAE;IACpF,IAAI2F,cAAc,GAAGD,cAAc,CAACE,aAAa,CAACC,OAAO;IACzD,IAAIC,iBAAiB,GAAGJ,cAAc,CAACE,aAAa,CAACG,UAAU;IAC/D;IACA;IACA;IACA,IAAIxD,IAAI,GAAGzC,WAAW,CAACiB,OAAO,CAAC,CAAC;IAChC,IAAIiF,OAAO,GAAG,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACA,QAAQ,IAAI,IAAInI,YAAY,CAAC,CAAC;IAClEkI,OAAO,CAACE,QAAQ,GAAGpG,WAAW,CAACgD,GAAG,CAAC,UAAU,CAAC;IAC9CkD,OAAO,CAACG,SAAS,GAAGrG,WAAW,CAACgD,GAAG,CAAC,WAAW,CAAC;IAChDkD,OAAO,CAACI,UAAU,GAAGtG,WAAW,CAACgD,GAAG,CAAC,YAAY,CAAC;IAClDkD,OAAO,CAACK,UAAU,GAAGvG,WAAW,CAACgD,GAAG,CAAC,YAAY,CAAC;IAClD,IAAIS,IAAI,GAAGkC,GAAG,CAACa,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACpC,IAAIC,aAAa,GAAGf,GAAG,CAACgB,gBAAgB,CAAC,CAAC;IAC1ClD,IAAI,CAACmD,cAAc,CAACF,aAAa,CAAC;IAClC;IACA,IAAIvC,CAAC,GAAG0C,IAAI,CAACC,GAAG,CAACrD,IAAI,CAACU,CAAC,EAAE,CAAC,CAAC;IAC3B,IAAIC,CAAC,GAAGyC,IAAI,CAACC,GAAG,CAACrD,IAAI,CAACW,CAAC,EAAE,CAAC,CAAC;IAC3B,IAAI2C,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACvD,IAAI,CAAC3B,KAAK,GAAG2B,IAAI,CAACU,CAAC,EAAEjE,GAAG,CAAC+G,QAAQ,CAAC,CAAC,CAAC;IACtD,IAAIC,EAAE,GAAGL,IAAI,CAACG,GAAG,CAACvD,IAAI,CAAC1B,MAAM,GAAG0B,IAAI,CAACW,CAAC,EAAElE,GAAG,CAACiH,SAAS,CAAC,CAAC,CAAC;IACxD,IAAIrF,KAAK,GAAGiF,EAAE,GAAG5C,CAAC;IAClB,IAAIpC,MAAM,GAAGmF,EAAE,GAAG9C,CAAC;IACnB,IAAIgD,IAAI,GAAG,CAAC3E,IAAI,CAACc,YAAY,CAAC,KAAK,CAAC,EAAEd,IAAI,CAACc,YAAY,CAAC,KAAK,CAAC,EAAEd,IAAI,CAACc,YAAY,CAAC,OAAO,CAAC,CAAC;IAC3F,IAAI8D,MAAM,GAAG5E,IAAI,CAAC6E,QAAQ,CAACF,IAAI,EAAE,UAAUG,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAE;MAC1D,IAAIC,EAAE,GAAG/B,GAAG,CAAC3B,WAAW,CAAC,CAACuD,GAAG,EAAEC,GAAG,CAAC,CAAC;MACpCE,EAAE,CAAC,CAAC,CAAC,IAAIvD,CAAC;MACVuD,EAAE,CAAC,CAAC,CAAC,IAAItD,CAAC;MACVsD,EAAE,CAAChC,IAAI,CAAC+B,KAAK,CAAC;MACd,OAAOC,EAAE;IACX,CAAC,CAAC;IACF,IAAInJ,UAAU,GAAGqH,cAAc,CAACpD,SAAS,CAAC,CAAC;IAC3C,IAAImF,SAAS,GAAG/B,cAAc,CAAC/F,IAAI,KAAK,sBAAsB,GAAGV,sBAAsB,CAACZ,UAAU,EAAEqH,cAAc,CAACgC,MAAM,CAACxI,KAAK,CAAC,GAAGd,qBAAqB,CAACC,UAAU,EAAEqH,cAAc,CAACiC,YAAY,CAAC,CAAC,EAAEjC,cAAc,CAACgC,MAAM,CAACnJ,QAAQ,CAAC;IACnOyH,OAAO,CAAC4B,MAAM,CAACT,MAAM,EAAEvF,KAAK,EAAEC,MAAM,EAAE8D,cAAc,CAACkC,KAAK,CAACC,aAAa,CAAC,CAAC,EAAE;MAC1EjC,OAAO,EAAEF,cAAc,CAACkC,KAAK,CAACE,cAAc,CAAC,CAAC;MAC9ChC,UAAU,EAAED,iBAAiB,CAAC+B,KAAK,CAACE,cAAc,CAAC;IACrD,CAAC,EAAEN,SAAS,CAAC;IACb,IAAIO,GAAG,GAAG,IAAIpK,OAAO,CAACqK,KAAK,CAAC;MAC1BzE,KAAK,EAAE;QACL5B,KAAK,EAAEA,KAAK;QACZC,MAAM,EAAEA,MAAM;QACdoC,CAAC,EAAEA,CAAC;QACJC,CAAC,EAAEA,CAAC;QACJgE,KAAK,EAAElC,OAAO,CAACmC;MACjB,CAAC;MACDC,MAAM,EAAE;IACV,CAAC,CAAC;IACF,IAAI,CAACzH,KAAK,CAAC2E,GAAG,CAAC0C,GAAG,CAAC;EACrB,CAAC;EACD1I,WAAW,CAACK,IAAI,GAAG,SAAS;EAC5B,OAAOL,WAAW;AACpB,CAAC,CAACtB,SAAS,CAAC;AACZ,eAAesB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}