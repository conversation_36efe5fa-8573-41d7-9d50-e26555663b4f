<template>
  <div id="app">
    <!-- 调试信息区域 -->
    <div style="position: fixed; top: 10px; right: 10px; z-index: 9999; background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-width: 300px; max-height: 300px; overflow: auto;">
      <!-- <button @click="testDetailRequest()">测试详情请求</button>
      <div v-if="datasets.length > 0">
        <p>数据集数量: {{ datasets.length }}</p>
        <p>第一个数据集ID: {{ datasets[0] && datasets[0].id }}</p>
      </div> -->
    </div>
    <div class="app-layout">
      <div class="sidebar">
        <div class="logo">
          <h2>Fast BI</h2>
        </div>
        <div class="menu">
          <div class="menu-item active" @click="resetToHome">
            <i class="el-icon-data-line"></i>
            <span>智能问数</span>
          </div>
        </div>
        <div class="recent-chats">
          <div class="chat-list">
            <div class="chat-item" v-for="(item, index) in recentChats" :key="index">
              {{ item.title }}
              <div class="chat-time">{{ item.time }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="main-content">
        <!-- 欢迎区域 - 带过渡动画 -->
        <transition name="welcome-fade" mode="out-in">
          <div v-if="!currentAnalysisDataset" class="header" key="welcome">
            <h2>您好，欢迎使用 <span class="highlight">智能问数</span></h2>
            <div class="header-actions" style="display: flex; align-items: center; margin-top: 10px;">

            </div>
            <p class="sub-title">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>
          </div>
        </transition>

        <!-- 数据选择区域 - 带过渡动画 -->
        <transition name="datasets-slide" mode="out-in">
          <div v-if="!currentAnalysisDataset" class="data-selection" key="datasets">
            <h3>可用数据</h3>
            <div class="data-sets">
              <div class="datasets-single-row">
                <transition-group name="card-stagger" tag="div" class="card-container-single-row">
                  <div
                    v-for="(table, idx) in datasets.slice(0, 3)"
                    :key="table.id + '_' + idx"
                    class="data-card-wrapper"
                    :style="{ transitionDelay: idx * 100 + 'ms' }">
                    <el-card class="data-card" style="background-color: #f9f9f9;">

                      <div class="data-header">
                        <span class="sample-tag">样例</span>
                        <span class="data-title" :title="table.name">{{ table.name }}</span>

                        <span class="common-tag" v-if="table.common">常用</span>
                      </div>
                      <div class="data-fields">
                        <el-tag
                          v-for="(field, idx) in (table.fields ? table.fields.slice(0, 3) : [])"
                          :key="field.id || idx"
                          size="mini"
                          type="info"
                          class="field-tag-single-line"
                        >
                          {{ field.name || field }}
                        </el-tag>
                        <span v-if="table.fields && table.fields.length > 3" class="more-fields-indicator">...</span>
                      </div>

                      <!-- 悬停按钮 -->
                      <div class="card-hover-buttons">
                        <button class="card-btn preview-btn" @click.stop="showDatasetDetail(table)">
                          预览
                        </button>
                        <button class="card-btn ask-btn" @click.stop="quickAnalyzeDataset(table)">
                          提问
                        </button>
                      </div>
                    </el-card>
                  </div>
                </transition-group>
              </div>
            </div>
          </div>
        </transition>
        
        <!-- 聊天消息列表区域 - 瞬间切换 -->
        <div class="message-list" ref="messageListRef" :class="{ 'expanded-position': currentAnalysisDataset }">
          <div
            v-for="(message, index) in messages"
            :key="index"
            :class="message.isUser ? 'message user-message' : 'message bot-message'"
          >
            <!-- 聊天图标 -->
            <div class="avatar-container" v-if="!message.isUser">
              <div class="bot-avatar">
                FastBI
              </div>
            </div>
            <!-- 消息内容 -->
            <div class="message-content">
              <div v-html="message.content"></div>
              <!-- 如果消息中包含图表配置，则显示图表和导出按钮 -->
              <div v-if="message.chartConfig" class="chart-container">
                <div class="chart-actions">
                  <!-- 图表切换下拉选择器 -->
                  <div class="chart-switcher-dropdown">
                    <div class="chart-dropdown-wrapper" @click="toggleChartDropdown(message)" :class="{ 'open': message.dropdownOpen }">
                      <!-- 当前选中的图表类型按钮 -->
                      <div class="chart-current-btn">
                        <div class="chart-icon-wrapper">
                          <i :class="getCurrentChartIcon(message)"></i>
                          <div v-if="message.switchingChart" class="switching-loader">
                            <i class="el-icon-loading"></i>
                          </div>
                        </div>
                        <span class="chart-type-text">图表切换</span>
                        <i class="el-icon-arrow-down dropdown-arrow"></i>
                      </div>

                      <!-- 下拉选项 -->
                      <div v-show="message.dropdownOpen" class="chart-dropdown-options">
                        <div class="chart-type-grid">
                          <button
                            v-for="chartType in availableChartTypes"
                            :key="chartType.type"
                            :class="['chart-type-btn', {
                              'active': (message.currentChartType || message.chartConfig.type) === chartType.type
                            }]"
                            :disabled="message.switchingChart"
                            @click.stop="selectChartType(message, chartType.type)"
                            :title="chartType.name"
                          >
                            <div class="chart-icon-wrapper">
                              <i :class="chartType.icon"></i>
                            </div>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <el-button size="mini" type="primary" icon="el-icon-download"
                            @click="exportToPDF(message)" :loading="message.exporting"
                            class="export-pdf-btn">
                    导出PDF
                  </el-button>
                </div>
                <chart-display
                  :chart-config="message.chartConfig"
                  :show-chart-switcher="false"
                  ref="chartDisplay"
                  @chart-type-changed="onChartTypeChanged(message, $event)"
                ></chart-display>
              </div>
              <!-- loading动画 -->
              <span
                class="loading-dots"
                v-if="message.isTyping"
              >
                <span class="dot"></span>
                <span class="dot"></span>
                <span class="dot"></span>
              </span>
            </div>
            <!-- 用户消息不显示头像 -->
          </div>
        </div>

        <!-- 底部问题输入区域（移至message-list下方） -->
        <div class="question-input-container">
          <!-- 字段选择区域 - 带入场动画 -->
          <transition name="field-selection-slide" mode="in-out">
            <div v-if="currentAnalysisDataset" class="field-selection-mini" key="field-selection">
              <!-- 第一行：关键指标（仅在有指标字段时显示） -->
              <transition name="field-row-fade" appear v-if="indicatorFields.length > 0">
                <div class="field-mini-row">
                  <span class="field-mini-label">关键指标</span>
                  <div class="field-tags-mini">
                    <transition-group name="field-tag-stagger" tag="div" class="field-tags-container">
                      <el-tag
                        v-for="(field, index) in indicatorFields"
                        :key="field.id"
                        :class="{ 'selected': selectedIndicators.includes(field.id) }"
                        @click="toggleFieldSelection('indicator', field)"
                        class="field-tag-mini indicator-tag"
                        size="mini"
                        :title="field.name"
                        :style="{ transitionDelay: index * 50 + 'ms' }">
                        {{ field.name }}
                      </el-tag>
                    </transition-group>
                  </div>
                </div>
              </transition>

              <!-- 第二行：分析维度（仅在有维度字段时显示） -->
              <transition name="field-row-fade" appear v-if="dimensionFields.length > 0">
                <div class="field-mini-row" style="transition-delay: 100ms;">
                  <span class="field-mini-label">分析维度</span>
                  <div class="field-tags-mini">
                    <transition-group name="field-tag-stagger" tag="div" class="field-tags-container">
                      <el-tag
                        v-for="(field, index) in dimensionFields"
                        :key="field.id"
                        :class="{ 'selected': selectedDimensions.includes(field.id) }"
                        @click="toggleFieldSelection('dimension', field)"
                        class="field-tag-mini dimension-tag"
                        size="mini"
                        :title="field.name"
                        :style="{ transitionDelay: (index + indicatorFields.length) * 50 + 200 + 'ms' }">
                        {{ field.name }}
                      </el-tag>
                    </transition-group>
                  </div>
                </div>
              </transition>
            </div>
          </transition>

          <!-- 提示文字 - 带过渡动画 -->
          <transition name="hint-fade" mode="out-in">
            <span v-if="!currentAnalysisDataset" key="hint">👋直接问我问题，或在上方选择一个主题/数据开始！</span>
          </transition>
          <div class="question-input-wrapper">
            <el-button type="text"
            class="header-action-btn select-data-btn"
            size="small"
            @click="SelectDataList"
            icon="el-icon-upload2">
            选择数据
          </el-button>
            <el-button
              type="text"
              class="header-action-btn export-btn"
              size="small"
              icon="el-icon-bottom"
              @click="exportAllConversation"
              :loading="exportingAll"
              :disabled="messages.length <= 1">
              导出完整指标
            </el-button>
            <el-input
              style="margin-bottom: 12px;width: 850px;"
              v-model="question"
              placeholder="请直接向我提问，或输入/唤起快捷提问吧"
              class="question-input large-input"
              @keyup.enter.native="submitQuestion"
              @focus="onInputFocus"
              @blur="onInputBlur"
              :disabled="isSending">
            </el-input>
            <div class="input-actions">
              <button class="action-btn" @click="showSuggestions">
                <i class="el-icon-magic-stick"></i>
              </button>
              <button class="action-btn test-btn" @click="testChart" title="测试图表功能">
                <i class="el-icon-data-line"></i>
              </button>
              <button class="action-btn test-btn" @click="testRealData" title="测试实际数据">
                <i class="el-icon-s-data"></i>
              </button>
              <button class="action-btn debug-btn" @click="showRawResponse" title="显示AI原始响应">
                <i class="el-icon-monitor"></i>
              </button>
              <button class="action-btn send-btn" @click="submitQuestion" :disabled="isSending">
                <i class="el-icon-position"></i>
              </button>
              
            </div>
          </div>
          
          <!-- 建议问题弹出层 -->
          <div v-if="showSuggestionsPanel" class="suggestions-panel">
            <div class="suggestions-title">
              <i class="el-icon-s-promotion"></i> 官方推荐
            </div>
            <div class="suggestions-list">
              <div 
                v-for="(suggestion, index) in suggestedQuestions" 
                :key="index"
                class="suggestion-item"
                @click="useQuestion(suggestion)">
                {{ suggestion }}
              </div>
            </div>
          </div>
          
          <!-- AI原始响应弹出层 -->
          <div v-if="showRawResponsePanel" class="raw-response-panel">
            <div class="raw-response-title">
              <i class="el-icon-monitor"></i> AI原始响应
              <button class="close-btn" @click="showRawResponsePanel = false">
                <i class="el-icon-close"></i>
              </button>
            </div>
            <pre class="raw-response-content">{{ lastRawResponse }}</pre>
          </div>
        </div>
      </div>
    </div>
    
    <el-drawer
      title="数据详情"
      :visible.sync="dialogVisible"
      direction="rtl"
      size="42%"
      class="dataset-detail-drawer">

      <div class="detail-content" v-if="currentDatasetDetail">
        <!-- 头部信息区域 -->
        <div class="detail-header">
          <div class="dataset-info-unified">
            <div class="dataset-title-row">
              <i class="el-icon-data-board dataset-icon"></i>
              <h2 class="dataset-name">{{ currentDatasetDetail.name }}</h2>
            </div>
            <div class="dataset-stats">
              <span class="stat-badge">
                <i class="el-icon-files"></i>
                {{ datasetFields.length }} 个字段
              </span>
              <span class="stat-badge">
                <i class="el-icon-document"></i>
                {{ datasetData.length }} 条记录
              </span>
            </div>
          </div>

          <!-- 悬浮提问按钮 -->
          <el-button
            type="primary"
            @click="analyzeDataset"
            icon="el-icon-chat-dot-round"
            class="floating-ask-btn">
            提问
          </el-button>
        </div>

        <!-- 标签页切换区域 -->
        <div class="detail-tabs">
          <el-tabs v-model="activeTab" type="card" class="dataset-tabs">
            <el-tab-pane label="字段信息" name="fields">
              <template slot="label">
                <span>
                  <i class="el-icon-menu"></i>
                  字段信息
                </span>
              </template>

              <!-- 字段信息区域 -->
              <div class="fields-section">
                <div class="section-header">
                  <div class="field-stats">
                    <span class="stat-item dimension">
                      <i class="el-icon-s-grid"></i>
                      维度字段 {{ datasetFields.filter(f => f.groupType === 'd').length }}
                    </span>
                    <span class="stat-item measure">
                      <i class="el-icon-s-data"></i>
                      度量字段 {{ datasetFields.filter(f => f.groupType === 'q').length }}
                    </span>
                  </div>
                </div>

                <!-- 字段表格 -->
                <div class="fields-table-container">
                  <!-- 搜索框 -->
                  <div class="table-search-bar">
                    <el-input
                      v-model="fieldSearchKeyword"
                      placeholder="搜索字段..."
                      prefix-icon="el-icon-search"
                      size="small"
                      clearable
                      class="field-search-input">
                    </el-input>
                  </div>

                  <!-- 表格 -->
                  <div class="clean-table">
                    <div class="table-header">
                      <div class="header-cell name-col">字段名称</div>
                      <div class="header-cell type-col">类型</div>
                      <div class="header-cell group-col">维度/度量</div>
                      <div class="header-cell desc-col">字段描述</div>
                    </div>

                    <div class="table-body-wrapper">
                      <div class="table-body">
                        <div
                          v-for="(field, index) in filteredFields"
                          :key="field.id || index"
                          class="table-row">

                        <div class="table-cell name-col">
                          <div class="field-name-wrapper">
                            <div class="field-type-icon" :class="getFieldIconClass(field)">
                              <i :class="getFieldIconName(field)"></i>
                            </div>
                            <span class="field-name-text">{{ field.name }}</span>
                          </div>
                        </div>

                        <div class="table-cell type-col">
                          <span class="field-type-text">{{ getFieldTypeLabel(field.type) }}</span>
                        </div>

                        <div class="table-cell group-col">
                          <span class="group-type-text" :class="field.groupType === 'd' ? 'dimension-text' : 'measure-text'">
                            {{ field.groupType === 'd' ? '维度' : '度量' }}
                          </span>
                        </div>

                        <div class="table-cell desc-col">
                          <span class="field-desc-text">{{ field.name }}</span>
                        </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 底部统计 -->
                  <div class="table-footer">
                    <div class="field-stats-summary">
                      <span class="total-count">总字段数 {{ datasetFields.length }}</span>
                      <div class="type-counts">
                        <span class="dimension-count">
                          <span class="count-dot dimension-dot"></span>
                          分析维度 {{ dimensionFieldsCount }}
                        </span>
                        <span class="measure-count">
                          <span class="count-dot measure-dot"></span>
                          关键指标 {{ measureFieldsCount }}
                        </span>
                      </div>
                      <span class="field-count-info" v-if="filteredFields.length <= 10">
                        显示全部 {{ filteredFields.length }} 个字段
                      </span>
                      <span class="scroll-hint" v-if="filteredFields.length > 10">
                        <i class="el-icon-mouse"></i>
                        可滚动查看全部 {{ filteredFields.length }} 个字段
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="数据预览" name="preview">
              <template slot="label">
                <span>
                  <i class="el-icon-view"></i>
                  数据预览
                </span>
              </template>

              <!-- 数据预览区域 -->
              <div class="preview-section">
                <div class="preview-header">
                  <div class="preview-stats">
                    <span class="total-records">
                      共 {{ totalRecords }} 条记录
                    </span>
                    <span class="current-page-info">
                      当前显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalRecords) }} 条
                    </span>
                  </div>
                  <div class="preview-pagination">
                    <el-pagination
                      @current-change="handlePageChange"
                      :current-page="currentPage"
                      :page-size="pageSize"
                      :total="totalRecords"
                      layout="prev, pager, next"
                      :small="true">
                    </el-pagination>
                  </div>
                </div>

                <div class="preview-table-wrapper">
                  <el-table
                    :data="currentPageData"
                    class="preview-table"
                    stripe
                    border
                    size="small"
                    style="width: 100%"
                    height="auto">
                    <el-table-column
                      type="index"
                      label="序号"
                      width="60"
                      :index="getRowIndex">
                    </el-table-column>
                    <el-table-column
                      v-for="field in datasetFields"
                      :key="field.id || field.name"
                      :prop="field.dataeaseName || field.name"
                      :label="field.name"
                      :min-width="120"
                      show-overflow-tooltip>
                      <template slot-scope="scope">
                        <span class="cell-content">{{ getCellValue(scope.row, field) }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-drawer>
    <el-drawer
      title="数据集列表"
      :visible.sync="drawer"
      direction="rtl"
      size="45%"
      class="dataset-drawer">
      <div class="drawer-content">
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索数据集名称..."
            @input="onSearchDataset"
            class="dataset-search-input"
            prefix-icon="el-icon-search"
            clearable
            size="medium"
          />
          <div class="search-stats" v-if="searchKeyword">
            找到 {{ filteredDatasets.length }} 个数据集
          </div>
        </div>

        <div class="datasets-grid">
          <div v-if="filteredDatasets.length === 0" class="empty-state">
            <i class="el-icon-search"></i>
            <p v-if="searchKeyword">未找到包含 "{{ searchKeyword }}" 的数据集</p>
            <p v-else>暂无可用数据集</p>
          </div>
          <el-row :gutter="20" v-else>
            <el-col :span="8" v-for="(table, idx) in filteredDatasets" :key="table.id + '_' + idx">
              <el-card class="data-card drawer-data-card">
                <div class="data-header">
                  <span class="sample-tag">数据集</span>
                  <span class="data-title" :title="table.name">{{ table.name }}</span>
                  <span class="common-tag" v-if="table.common">常用</span>
                </div>
                <div class="data-fields">
                  <el-tag
                    v-for="(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])"
                    :key="field.id || idx"
                    size="mini"
                    type="info"
                    class="field-tag"
                  >
                    {{ field.name || field }}
                  </el-tag>
                  <span v-if="table.fields && table.fields.length > 4" class="more-fields">+{{ table.fields.length - 4 }}个字段</span>
                </div>

                <!-- 悬停按钮 -->
                <div class="card-hover-buttons">
                  <button class="card-btn preview-btn" @click.stop="showDatasetDetail(table)">
                    预览
                  </button>
                  <button class="card-btn ask-btn" @click.stop="quickAnalyzeDataset(table)">
                    提问
                  </button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-drawer>

  </div>
</template>

<script>
import { dataApi } from './api/index.js'
import { v4 as uuidv4 } from 'uuid'
import axios from 'axios'
import ChartDisplay from './components/ChartDisplay.vue'
// 导入PDF导出所需的库
import html2canvas from 'html2canvas'
import { jsPDF } from 'jspdf'
import * as echarts from 'echarts'
// import { log } from '@antv/g2plot/lib/utils/invariant.js'

export default {
  name: 'App',
  components: {
    ChartDisplay
  },
  data() {
    return {
      description: null,
      tablename: null,
      tables: [], // 原始树结构
      datasets: [], // 扁平化后的数据集（leaf: true）
      filteredDatasets: [], // 搜索过滤后的数据集
      searchKeyword: '',
      selectedTable: null,
      question: '',
      queryResult: null,
      showSuggestionsPanel: false,
      suggestedQuestions: [],
      recentChats: [],
      tableIndicators: [],
      dialogVisible: false,
      // 新增流式输出相关数据
      messages: [],
      memoryId: null,
      isSending: false,
      messageListRef: null,
      showRawResponsePanel: false, // 新增：控制原始响应弹出层
      lastRawResponse: '', // 新增：存储最后收到的原始响应
      drawer:false,    //抽屉展示
      direction: 'rtl', //默认抽屉从又往左
      currentDatasetDetail: null,
      datasetFields: [],
      datasetData: [],
      apiToken: 'eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2OTEyYzNiLTQ5ZmMtNDAyMC1iMzRiLWE3YWJlNmY1MTI0MSJ9.J4QCC6qh2GZ7ENDHuZQWQ1GvGOQ1HOr8gW34KXIHV9N_73Jppx-qmEwpq0AHlXM3DUjd5EAlpIVAvzJygm_ldg', // 示例token，实际应该从登录后存储
      currentAnalysisDataset: null, // 存储当前用于智能分析的数据集信息
      selectedIndicators: [], // 选中的指标字段ID
      selectedDimensions: [], // 选中的维度字段ID
      innerDrawer:false,
      tableList:[],
      exportingAll: false, // 新增：控制完整对话导出状态
      activeTab: 'fields', // 新增：控制数据集详情页面的标签页切换，默认显示字段信息
      fieldSearchKeyword: '', // 字段搜索关键词
      // 数据预览分页相关
      currentPage: 1, // 当前页码
      pageSize: 20, // 每页显示条数

      // 图表切换相关数据
      chartTypeConfig: {
        'bar': {
          name: '柱图',
          icon: 'chart-icon-bar',
          compatible: true
        },
        'line': {
          name: '线图',
          icon: 'chart-icon-line',
          compatible: true
        },
        'pie': {
          name: '饼图',
          icon: 'chart-icon-pie',
          compatible: true
        },
        'bar-horizontal': {
          name: '条形图',
          icon: 'chart-icon-bar-horizontal',
          compatible: true
        }
      }
    }
  },

  computed: {
    // 可用的图表类型列表
    availableChartTypes() {
      const allTypes = ['bar', 'line', 'pie', 'bar-horizontal'];

      return allTypes.map(type => ({
        type: type,
        name: this.chartTypeConfig[type].name,
        icon: this.chartTypeConfig[type].icon,
        compatible: this.chartTypeConfig[type].compatible
      }));
    },

    // 指标字段（数值类型）
    indicatorFields() {
      if (!this.currentAnalysisDataset || !this.currentAnalysisDataset.fields) {
        return [];
      }
      return this.currentAnalysisDataset.fields.filter(field =>
        field.deType === 1 || // 数值类型
        field.groupType === 'q' || // 度量字段
        ['LONG', 'DOUBLE', 'BIGINT', 'INT', 'FLOAT', 'DECIMAL'].includes(field.type?.toUpperCase())
      );
    },

    // 维度字段（文本、日期等）
    dimensionFields() {
      if (!this.currentAnalysisDataset || !this.currentAnalysisDataset.fields) {
        return [];
      }
      return this.currentAnalysisDataset.fields.filter(field =>
        field.deType === 0 || // 文本类型
        field.groupType === 'd' || // 维度字段
        ['TEXT', 'STRING', 'VARCHAR', 'DATE', 'DATETIME', 'TIMESTAMP'].includes(field.type?.toUpperCase())
      );
    },

    // 过滤后的字段列表
    filteredFields() {
      if (!this.fieldSearchKeyword) {
        return this.datasetFields;
      }
      return this.datasetFields.filter(field =>
        field.name.toLowerCase().includes(this.fieldSearchKeyword.toLowerCase()) ||
        (field.dataeaseName && field.dataeaseName.toLowerCase().includes(this.fieldSearchKeyword.toLowerCase()))
      );
    },

    // 维度字段数量
    dimensionFieldsCount() {
      return this.datasetFields.filter(field => field.groupType === 'd').length;
    },

    // 度量字段数量
    measureFieldsCount() {
      return this.datasetFields.filter(field => field.groupType === 'q').length;
    },

    // 数据预览分页相关计算属性
    totalRecords() {
      return Math.min(this.datasetData.length, 100); // 最多显示100条
    },

    currentPageData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.datasetData.slice(start, Math.min(end, 100)); // 确保不超过100条
    }
  },

  mounted() {
    // 存储token到localStorage，供API使用
    localStorage.setItem('de_token', this.apiToken);
    this.loadTables()
    this.initMemoryId()
    this.addWelcomeMessage()
    // 添加图表相关的建议问题
    this.suggestedQuestions = [
      "帮我生成一个销售额柱状图",
      "展示近六个月的销售趋势折线图",
      "按照区域统计销售量并生成饼图",
      "帮我做一个按产品类别的销量对比图"
    ]

    // 添加字段区域的事件监听
    this.$nextTick(() => {
      this.setupFieldSelectionEvents();
    });
  },
  updated() {
    this.scrollToBottom()
  },
  methods: {
    // 图表切换相关方法
    toggleChartDropdown(message) {
      // 关闭其他消息的下拉框
      this.messages.forEach(msg => {
        if (msg !== message) {
          this.$set(msg, 'dropdownOpen', false);
        }
      });

      // 切换当前消息的下拉框状态
      this.$set(message, 'dropdownOpen', !message.dropdownOpen);
    },

    selectChartType(message, chartType) {
      if (chartType === (message.currentChartType || message.chartConfig.type)) {
        this.$set(message, 'dropdownOpen', false);
        return;
      }

      console.log(`切换图表类型: ${message.currentChartType || message.chartConfig.type} -> ${chartType}`);

      this.$set(message, 'switchingChart', true);
      this.$set(message, 'dropdownOpen', false);

      try {
        // 创建新的图表配置
        const newChartConfig = {
          ...message.chartConfig,
          type: chartType
        };

        // 更新消息的图表配置和当前类型
        this.$set(message, 'chartConfig', newChartConfig);
        this.$set(message, 'currentChartType', chartType);

        console.log('图表类型切换成功:', newChartConfig);

      } catch (error) {
        console.error('切换图表类型失败:', error);
        this.$message.error('切换图表类型失败: ' + error.message);
      } finally {
        this.$set(message, 'switchingChart', false);
      }
    },

    getCurrentChartIcon(message) {
      const currentType = message.currentChartType || message.chartConfig.type;
      const chartType = this.availableChartTypes.find(type => type.type === currentType);
      return chartType ? chartType.icon : 'chart-icon-bar';
    },

    onChartTypeChanged(message, newChartConfig) {
      console.log('图表类型变化事件:', message.id, newChartConfig);
      // 这里可以添加额外的处理逻辑
    },

    // 输入框聚焦时的处理
    onInputFocus() {
      this.$nextTick(() => {
        const fieldSelection = document.querySelector('.field-selection-mini');
        if (fieldSelection) {
          fieldSelection.classList.add('input-focused');
        }
      });
    },

    // 输入框失焦时的处理
    onInputBlur() {
      this.$nextTick(() => {
        const fieldSelection = document.querySelector('.field-selection-mini');
        if (fieldSelection) {
          fieldSelection.classList.remove('input-focused');
        }
      });
    },

    // 设置字段选择区域的事件监听
    setupFieldSelectionEvents() {
      const fieldSelection = document.querySelector('.field-selection-mini');
      const inputWrapper = document.querySelector('.question-input-wrapper');

      if (fieldSelection && inputWrapper) {
        // 字段区域悬停时，输入框也显示悬停效果
        fieldSelection.addEventListener('mouseenter', () => {
          inputWrapper.classList.add('field-hover');
        });

        fieldSelection.addEventListener('mouseleave', () => {
          inputWrapper.classList.remove('field-hover');
        });

        // 输入框悬停时，字段区域也显示悬停效果
        inputWrapper.addEventListener('mouseenter', () => {
          fieldSelection.classList.add('input-hover');
        });

        inputWrapper.addEventListener('mouseleave', () => {
          fieldSelection.classList.remove('input-hover');
        });
      }
    },

    SelectDataList(){
      this.loadTables()
      this.drawer=true
    },
    // 初始化用户ID
    initMemoryId() {
      let storedMemoryId = localStorage.getItem('user_memory_id')
      if (!storedMemoryId) {
        storedMemoryId = this.uuidToNumber(uuidv4())
        localStorage.setItem('user_memory_id', storedMemoryId)
      }
      this.memoryId = storedMemoryId
    },
    
    // 将UUID转换为数字
    uuidToNumber(uuid) {
      let number = 0
      for (let i = 0; i < uuid.length && i < 6; i++) {
        const hexValue = uuid[i]
        number = number * 16 + (parseInt(hexValue, 16) || 0)
      }
      return number % 1000000
    },
    
    // 添加欢迎消息
    addWelcomeMessage() {
      this.messages.push({
        isUser: false,
        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,
        isTyping: false
      })
    },

    // 清除欢迎消息
    clearWelcomeMessage() {
      // 移除AI的欢迎消息（通常是第一条消息）
      this.messages = this.messages.filter(message =>
        message.isUser || !message.content.includes('您好！我是数据助手')
      );
    },
    
    // 滚动到底部
    scrollToBottom() {
      if (this.$refs.messageListRef) {
        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight
      }
    },
    
    async loadTables() {
      try {
        const res = await dataApi.getAllTables()
        if (res.data && res.data.code === 0) {
          this.tables = res.data.data
          this.datasets = this.flattenDatasets(this.tables)
          this.filteredDatasets = this.datasets
        } else {
          this.tables = []
          this.datasets = []
          this.filteredDatasets = []
        }
      } catch (e) {
        this.tables = []
        this.datasets = []
        this.filteredDatasets = []
      }
    },
    // 递归扁平化树结构，只保留leaf: true的数据集
    flattenDatasets(tree) {
      let result = []
      for (const node of tree) {
        if (node.leaf) {
          result.push(node)
        } else if (node.children && node.children.length > 0) {
          result = result.concat(this.flattenDatasets(node.children))
        }
      }
      return result
    },
    // 搜索功能
    onSearchDataset() {
      const keyword = this.searchKeyword.trim().toLowerCase()
      if (!keyword) {
        this.filteredDatasets = this.datasets
      } else {
        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword))
      }
    },
    // selectDataset(dataset) {
    //   this.selectedTable = dataset;
    //   this.drawer = true;
    // },
    
    // 测试详情请求方法
    // async testDetailRequest() {
    //   if (this.datasets.length > 0) {
    //     const testDataset = this.datasets[0];
    //     console.log('测试数据集:', testDataset);
    //     alert(`正在请求数据集详情，ID: ${testDataset.id}`);
    //     await this.showDatasetDetail(testDataset);
      //   } else {
    //     alert('没有可用的数据集');
    //   }
    // },

    // 智能分析数据集
    analyzeDataset(dataset = null) {
      // 如果传入了数据集参数，先设置为当前数据集
      if (dataset) {
        this.showDatasetDetail(dataset);
        // 等待数据加载完成后再进行分析
        this.$nextTick(() => {
          this.performAnalysis();
        });
        return;
      }

      this.performAnalysis();
    },

    // 重置到首页状态
    resetToHome() {
      // 清除当前选择的数据集
      this.currentAnalysisDataset = null;
      this.currentDatasetDetail = null;
      this.datasetFields = [];
      this.datasetData = [];

      // 关闭所有抽屉和对话框
      this.dialogVisible = false;
      this.drawerVisible = false;

      // 清除聊天记录（可选，根据需求决定）
      // this.messages = [];

      // 重置其他状态
      this.activeTab = 'fields';
      this.currentPage = 1;
      this.selectedIndicators = [];
      this.selectedDimensions = [];

      // 显示欢迎消息
      this.showWelcomeMessage();
    },

    // 显示欢迎消息
    showWelcomeMessage() {
      // 如果没有消息或者第一条不是欢迎消息，则添加欢迎消息
      if (this.messages.length === 0 || this.messages[0].type !== 'bot' || !this.messages[0].content.includes('欢迎')) {
        this.messages.unshift({
          type: 'bot',
          content: '您好！欢迎使用智能问数系统。请先选择一个数据集，然后您就可以用自然语言提问了。',
          timestamp: new Date().toLocaleTimeString()
        });
      }
    },

    // 快速分析数据集（从卡片悬停按钮触发）
    async quickAnalyzeDataset(dataset) {
      // 准备请求headers
      const headers = {
        'X-DE-TOKEN': this.apiToken,
        'out_auth_platform': 'default',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'
      };

      try {
        // 获取数据集详情和字段信息
        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);

        if (res.data && res.data.code === 0) {
          const detail = res.data.data;
          // 获取字段信息
          const fields = detail.allFields || (detail.data && detail.data.fields) || [];

          // 提取所需的字段信息
          const fieldsInfo = fields.map(field => {
            return {
              id: field.id,
              originName: field.originName || '',
              name: field.name || '',
              dataeaseName: field.dataeaseName || '',
              groupType: field.groupType || '',
              type: field.type || '',
              datasourceId: field.datasourceId || '',
              datasetTableId: field.datasetTableId || '',
              datasetGroupId: field.datasetGroupId || ''
            };
          });

          // 存储当前数据集信息
          this.currentAnalysisDataset = {
            id: dataset.id,
            name: dataset.name,
            fields: fieldsInfo
          };

          // 关闭所有抽屉和对话框
          this.dialogVisible = false;  // 关闭详情对话框
          this.drawer = false;        // 关闭数据集列表抽屉

          // 清除AI的欢迎消息
          this.clearWelcomeMessage();

          // 重新设置字段选择区域的事件监听
          this.$nextTick(() => {
            this.setupFieldSelectionEvents();
          });

          // 移除提示消息

          // 自动聚焦到问题输入框
          this.$nextTick(() => {
            const inputEl = document.querySelector('.question-input input');
            if (inputEl) inputEl.focus();
          });
        } else {
          console.error('获取数据集信息失败');
        }
      } catch (error) {
        console.error('获取数据集字段失败:', error);
      }
    },

    // 执行分析的具体逻辑
    performAnalysis() {
      console.log('performAnalysis 被调用');
      console.log('currentDatasetDetail:', this.currentDatasetDetail);
      console.log('datasetFields:', this.datasetFields);

      if (!this.currentDatasetDetail || !this.datasetFields.length) {
        console.log('数据集或字段信息缺失');
        return;
      }

      // 提取所需的字段信息
      const fieldsInfo = this.datasetFields.map(field => {
        // 只保留需要的字段属性
        return {
          id: field.id,
          originName: field.originName || '',
          name: field.name || '',
          dataeaseName: field.dataeaseName || '',
          groupType: field.groupType || '',
          type: field.type || '',
          datasourceId: field.datasourceId || '',
          datasetTableId: field.datasetTableId || '',
          datasetGroupId: field.datasetGroupId || ''
        };
      });

      // 存储当前数据集信息，包括ID和字段
      this.currentAnalysisDataset = {
        id: this.currentDatasetDetail.id,
        name: this.currentDatasetDetail.name,
        fields: fieldsInfo
      };

      // 选择数据集后清除AI的欢迎消息
      this.clearWelcomeMessage();

      // 重新设置字段选择区域的事件监听
      this.$nextTick(() => {
        this.setupFieldSelectionEvents();
      });

      // 关闭详情抽屉，返回到聊天界面
      this.dialogVisible = false;

      // 移除提示消息

      // 自动聚焦到问题输入框
      this.$nextTick(() => {
        const inputEl = document.querySelector('.question-input input');
        if (inputEl) inputEl.focus();
      });
    },

    // 切换字段选择
    toggleFieldSelection(type, field) {
      if (type === 'indicator') {
        const index = this.selectedIndicators.indexOf(field.id);
        if (index > -1) {
          this.selectedIndicators.splice(index, 1);
        } else {
          this.selectedIndicators.push(field.id);
        }
      } else if (type === 'dimension') {
        const index = this.selectedDimensions.indexOf(field.id);
        if (index > -1) {
          this.selectedDimensions.splice(index, 1);
        } else {
          this.selectedDimensions.push(field.id);
        }
      }
    },

    // 切换数据集
    switchDataset() {
      this.currentAnalysisDataset = null;
      this.selectedIndicators = [];
      this.selectedDimensions = [];
    },
  
    async showDatasetDetail(dataset) {
      // alert(`showDatasetDetail 被调用，数据集ID: ${dataset?.id || '未知'}`);
      console.log('showDatasetDetail 被调用，参数:', dataset);
      
      // 验证参数
      if (!dataset || !dataset.id) {
        console.error('无效的数据集参数:', dataset);
        // alert('数据集参数无效，缺少ID');
        return;
      }
      
      // 准备请求headers
      const headers = {
        'X-DE-TOKEN': this.apiToken,
        'out_auth_platform': 'default',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'
      };
      
      try {
        console.log(`开始请求数据集详情，ID: ${dataset.id}`);
        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);
        console.log('数据集详情API响应:', res);
        
        if (res.data && res.data.code === 0) {
          const detail = res.data.data;
          console.log('数据集详情数据:', detail);
          this.currentDatasetDetail = detail;
          // 字段信息优先allFields，否则data.fields
          this.datasetFields = detail.allFields || (detail.data && detail.data.fields) || [];
          // 数据内容
          this.datasetData = (detail.data && detail.data.data) || [];
          // 重置标签页到字段信息
          this.activeTab = 'fields';
          // 重置分页状态
          this.currentPage = 1;
          // 调试打印
          console.log(`字段信息: ${this.datasetFields.length}个字段`);
          console.log(`数据内容: ${this.datasetData.length}条记录`);

          if (this.datasetFields.length === 0) {
            console.warn('未找到字段信息');
          }

          if (this.datasetData.length === 0) {
            console.warn('未找到数据内容');
          }
        } else {
          console.error('API返回错误:', res.data);
          alert(`API返回错误: ${res.data.msg || '未知错误'}`);
          this.currentDatasetDetail = null;
          this.datasetFields = [];
          this.datasetData = [];
          this.activeTab = 'fields'; // 重置标签页
        }
        // 关闭数据集列表抽屉，打开详情抽屉
        this.drawer = false;
        this.dialogVisible = true;
      } catch (e) {
        console.error('请求数据集详情失败:', e);
        alert(`请求失败: ${e.message || '未知错误'}`);
        this.currentDatasetDetail = null;
        this.datasetFields = [];
        this.datasetData = [];
        this.dialogVisible = true;
      }
    },
    showSuggestions() {
      this.showSuggestionsPanel = !this.showSuggestionsPanel
    },
    useQuestion(q) {
      this.question = q
      this.showSuggestionsPanel = false
    },
    getTableFields(table) {
      if (table.tableCode === 'SALES') {
        return []
      }
      return []
    },
    
    // 修改为流式输出的问题提交
    async submitQuestion() {
      if (!this.question.trim() || this.isSending) {
        return
      }
      
      // 获取当前选中的数据集信息
      // 构建发送给AI的完整消息
      let aiMessage = this.question.trim();
      
      // 如果有当前分析的数据集，添加数据集信息
      if (this.currentAnalysisDataset) {
        // 构建AI需要的格式
        const datasetInfo = {
          datasetId: this.currentAnalysisDataset.id,
          datasetName: this.currentAnalysisDataset.name,
          fields: this.currentAnalysisDataset.fields
        };
        
        // 将数据集信息添加到消息中
        aiMessage = JSON.stringify({
          question: this.question.trim(),
          dataset: datasetInfo
        });
      }
      
      // 添加用户消息
      const userMsg = {
        isUser: true,
        content: this.question.trim(),
        isTyping: false
      }
      this.messages.push(userMsg)
      
      // 添加机器人占位符消息
      const botMsg = {
        isUser: false,
        content: '',
        isTyping: true
      }
      this.messages.push(botMsg)
      
      // 获取最后一条消息的引用
      const lastMsg = this.messages[this.messages.length - 1]
      
      // 保存问题并清空输入框
      const question = this.question
      this.question = ''
      this.isSending = true
      
      try {
        // 构建发送的消息，包含当前数据集信息
        const message = this.currentAnalysisDataset 
          ? aiMessage 
          : `${question}。当前数据集：未选择具体数据集`;
        
        // 发送请求
        await axios.post(
          'http://localhost:8088/api/indicator/chat',
          { memoryId: this.memoryId, message },
          {
            responseType: 'stream',
            onDownloadProgress: (e) => {
              const fullText = e.event.target.responseText // 累积的完整文本
              let newText = fullText.substring(lastMsg.content.length)
              lastMsg.content += newText // 增量更新
              this.scrollToBottom() // 实时滚动
              
              // 保存原始响应
              this.lastRawResponse = fullText
            }
          }
        )
        
        // 请求完成后，解析可能的图表配置
        lastMsg.isTyping = false
        this.isSending = false
        
        // 尝试从回复中解析图表配置
        this.parseChartConfig(lastMsg);
      } catch (error) {
        console.error('请求出错:', error)
        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'
        lastMsg.isTyping = false
        this.isSending = false
      }
    },
    
    // 解析响应中的图表配置
    parseChartConfig(message) {
      console.log('开始解析图表配置，原始消息内容:', message.content);
      
      // 首先尝试查找图表数据ID
      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\/chart_data_id>/);
      if (chartDataIdMatch && chartDataIdMatch[1]) {
        const chartDataId = chartDataIdMatch[1];
        console.log('找到图表数据ID:', chartDataId);
        
        // 使用ID从后端获取完整图表数据
        this.fetchChartDataById(chartDataId, message);
        return;
      }
      
      // 如果没有找到图表数据ID，尝试查找JSON代码块
      const chartConfigMatch = message.content.match(/```json\s*([\s\S]*?)\s*```/);
      if (chartConfigMatch && chartConfigMatch[1]) {
        console.log('找到JSON代码块:', chartConfigMatch[1]);
        
        try {
          let chartConfig = JSON.parse(chartConfigMatch[1]);
          console.log('解析后的JSON对象:', chartConfig);
          
          // 检查是否是API响应格式（包含code和data字段）
          if (chartConfig.code === 0 && chartConfig.data) {
            console.log('检测到API响应格式，提取data字段:', chartConfig.data);
            chartConfig = chartConfig.data;
          }
          
          // 如果包含必要的图表配置字段，则视为有效的图表配置
          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {
            console.log('找到有效的图表配置:', chartConfig);
            message.chartConfig = chartConfig;
            
            // 可以选择性地从消息中移除JSON代码块
            message.content = message.content.replace(/```json\s*[\s\S]*?\s*```/, 
              '<div class="chart-notice"></div>');
            
            return; // 找到有效配置后直接返回
          } else {
            console.log('图表配置缺少必要字段:', chartConfig);
          }
        } catch (error) {
          console.error('JSON解析错误:', error);
        }
      }
      
      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');
      
      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON
      try {
        // 尝试查找可能的JSON字符串
        const jsonRegex = /\{.*code\s*:\s*0.*data\s*:.*\}/s;
        const jsonMatch = message.content.match(jsonRegex);
        
        if (jsonMatch) {
          console.log('找到可能的JSON字符串:', jsonMatch[0]);
          
          // 尝试将字符串转换为JSON对象
          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式
          const jsonStr = jsonMatch[0].replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3');
          console.log('处理后的JSON字符串:', jsonStr);
          
          try {
            const chartConfig = JSON.parse(jsonStr);
            console.log('解析后的JSON对象:', chartConfig);
            
            if (chartConfig.code === 0 && chartConfig.data) {
              const data = chartConfig.data;
              console.log('提取的图表数据:', data);
              
              // 检查是否包含必要的图表字段
              if (data.type && (data.tableId || data.data)) {
                console.log('找到有效的图表配置:', data);
                message.chartConfig = data;
                message.content = message.content.replace(jsonMatch[0], 
                  '<div class="chart-notice"></div>');
                return; // 找到有效配置后直接返回
              }
            }
          } catch (parseError) {
            console.log('JSON解析错误:', parseError);
          }
        }
      } catch (error) {
        console.log('解析过程中出错:', error);
      }
      
      // 最后的尝试：检查消息中是否包含图表数据的关键词
      if (message.content.includes('图表数据已成功获取') || 
          message.content.includes('已生成图表')) {
        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');
        
        // 构建一个默认的图表配置
        const defaultConfig = {
          id: Date.now().toString(),
          type: 'bar',
          title: '数据图表',
          tableId: this.selectedTable?.id || '1114644377738809344'
        };
        
        console.log('构建的默认图表配置:', defaultConfig);
        message.chartConfig = defaultConfig;
      }
    },
    
    // 从后端获取完整图表数据
    async fetchChartDataById(chartDataId, message) {
      try {
        console.log('开始从后端获取图表数据, ID:', chartDataId);
        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);
        
        if (response.data && response.status === 200) {
          console.log('成功获取图表数据:', response.data);
          
          // 如果响应包含code和data字段，则提取data字段
          let chartConfig = response.data;
          if (chartConfig.code === 0 && chartConfig.data) {
            chartConfig = chartConfig.data;
          }
          
          // 将图表配置添加到消息对象
          message.chartConfig = chartConfig;
          
          // 在消息中添加图表提示
          if (!message.content.includes('已生成图表')) {
            message.content += '<div class="chart-notice">↓ 已生成图表 ↓</div>';
          }
          
          // 强制更新视图
          this.$forceUpdate();
        } else {
          console.error('获取图表数据失败:', response);
          message.content += '<div class="chart-error">获取图表数据失败</div>';
        }
      } catch (error) {
        console.error('获取图表数据出错:', error);
        message.content += `<div class="chart-error">获取图表数据失败: ${error.message}</div>`;
      }
    },
    
    // 测试图表功能的方法（仅用于开发测试）
    testChart() {
      // 基本测试配置
      const testChartConfig = {
        id: "test-chart-001",
        type: "bar",
        title: "测试销售数据图表",
        datasetId: "test-dataset",
        tableId: "test-table"
      };
      
      // 创建测试消息
      const botMsg = {
        isUser: false,
        content: '这是一个测试图表：<div class="chart-notice">↓ 已生成图表 ↓</div>',
        isTyping: false,
        chartConfig: testChartConfig
      };
      
      this.messages.push(botMsg);
      
      // 保存原始响应
      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);
      
      // 打印当前消息列表，检查图表配置是否正确传递
      console.log('当前消息列表:', this.messages);
      
      // 模拟API响应格式的测试
      const testApiResponse = {
        code: 0,
        msg: 'success',
        data: {
          type: 'bar',
          data: [
            { field: '类别1', value: 100 },
            { field: '类别2', value: 200 },
            { field: '类别3', value: 150 }
          ],
          metrics: [{ name: '数值' }]
        }
      };
      
      // 将API响应格式添加到消息中，测试解析功能
      const jsonContent = '```json\n' + JSON.stringify(testApiResponse, null, 2) + '\n```';
      const apiResponseMsg = {
        isUser: false,
        content: `测试API响应格式: ${jsonContent}`,
        isTyping: false
      };
      
      this.messages.push(apiResponseMsg);
      this.parseChartConfig(apiResponseMsg);
      
      // 保存原始响应
      this.lastRawResponse = jsonContent;
      
      console.log('API响应解析后的消息:', apiResponseMsg);
    },
    
    // 测试后端实际返回的数据格式
    testRealData() {
      console.log('测试后端实际返回的数据格式');
      
      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）
      const realData = {
        code: 0,
        msg: null,
        data: {
          id: "1719830816000",
          title: "按名称分组的记录数柱状图",
          tableId: "1114644377738809344",
          type: "bar",
          data: {
            data: [
              {value: 1, field: "神朔", name: "神朔", category: "记录数*"},
              {value: 1, field: "甘泉", name: "甘泉", category: "记录数*"},
              {value: 1, field: "包神", name: "包神", category: "记录数*"}
            ],
            fields: [
              {id: "1746787308487", name: "名称", groupType: "d"},
              {id: "-1", name: "记录数*", groupType: "q"}
            ]
          }
        }
      };
      
      // 创建包含实际数据的消息
      const realDataMsg = {
        isUser: false,
        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',
        isTyping: false
      };
      
      // 直接设置图表配置
      realDataMsg.chartConfig = realData.data;
      
      // 添加到消息列表
      this.messages.push(realDataMsg);
      
      // 保存原始响应
      this.lastRawResponse = JSON.stringify(realData, null, 2);
      
      console.log('添加了实际数据的消息:', realDataMsg);
    },

    // 显示AI原始响应的方法
    showRawResponse() {
      this.showRawResponsePanel = true;
      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message
    },

    // 获取字段类型的CSS类名
    getFieldTypeClass(type) {
      const typeMap = {
        'TEXT': 'text-type',
        'STRING': 'text-type',
        'VARCHAR': 'text-type',
        'INT': 'number-type',
        'INTEGER': 'number-type',
        'BIGINT': 'number-type',
        'DECIMAL': 'number-type',
        'DOUBLE': 'number-type',
        'FLOAT': 'number-type',
        'DATE': 'date-type',
        'DATETIME': 'date-type',
        'TIMESTAMP': 'date-type',
        'BOOLEAN': 'boolean-type'
      };
      return typeMap[type?.toUpperCase()] || 'default-type';
    },

    // 获取字段类型的显示标签
    getFieldTypeLabel(type) {
      const labelMap = {
        'TEXT': '文本',
        'STRING': '文本',
        'VARCHAR': '文本',
        'INT': '数值',
        'INTEGER': '数值',
        'BIGINT': '数值',
        'DECIMAL': '数值',
        'DOUBLE': '数值',
        'FLOAT': '数值',
        'DATE': '日期',
        'DATETIME': '日期时间',
        'TIMESTAMP': '时间戳',
        'BOOLEAN': '布尔'
      };
      return labelMap[type?.toUpperCase()] || type || '未知';
    },

    // 获取字段描述
    getFieldDescription(field) {
      if (field.description) {
        return field.description;
      }

      // 根据字段类型和分组类型生成默认描述
      const typeDesc = this.getFieldTypeLabel(field.type);
      const groupDesc = field.groupType === 'd' ? '维度' : '度量';

      // 根据字段名称推测用途
      const fieldName = (field.name || '').toLowerCase();
      let purposeDesc = '';

      if (fieldName.includes('id') || fieldName.includes('编号')) {
        purposeDesc = '，用于唯一标识';
      } else if (fieldName.includes('name') || fieldName.includes('名称')) {
        purposeDesc = '，用于分类标识';
      } else if (fieldName.includes('date') || fieldName.includes('时间') || fieldName.includes('日期')) {
        purposeDesc = '，用于时间分析';
      } else if (fieldName.includes('amount') || fieldName.includes('金额') || fieldName.includes('价格')) {
        purposeDesc = '，用于金额统计';
      } else if (fieldName.includes('count') || fieldName.includes('数量') || fieldName.includes('个数')) {
        purposeDesc = '，用于数量统计';
      } else if (field.groupType === 'q') {
        purposeDesc = '，用于数值分析';
      } else {
        purposeDesc = '，用于数据分组';
      }

      return `${typeDesc}类型的${groupDesc}字段${purposeDesc}`;
    },

    // 获取字段图标类名
    getFieldIconClass(field) {
      const typeMap = {
        'TEXT': 'text-icon',
        'STRING': 'text-icon',
        'VARCHAR': 'text-icon',
        'INT': 'number-icon',
        'INTEGER': 'number-icon',
        'BIGINT': 'number-icon',
        'DECIMAL': 'number-icon',
        'DOUBLE': 'number-icon',
        'FLOAT': 'number-icon',
        'DATE': 'date-icon',
        'DATETIME': 'date-icon',
        'TIMESTAMP': 'date-icon',
        'BOOLEAN': 'boolean-icon'
      };
      return typeMap[field.type?.toUpperCase()] || 'default-icon';
    },

    // 获取字段图标名称
    getFieldIconName(field) {
      const iconMap = {
        'TEXT': 'el-icon-document',
        'STRING': 'el-icon-document',
        'VARCHAR': 'el-icon-document',
        'INT': 'el-icon-s-data',
        'INTEGER': 'el-icon-s-data',
        'BIGINT': 'el-icon-s-data',
        'DECIMAL': 'el-icon-s-data',
        'DOUBLE': 'el-icon-s-data',
        'FLOAT': 'el-icon-s-data',
        'DATE': 'el-icon-date',
        'DATETIME': 'el-icon-time',
        'TIMESTAMP': 'el-icon-time',
        'BOOLEAN': 'el-icon-switch-button'
      };
      return iconMap[field.type?.toUpperCase()] || 'el-icon-info';
    },

    // 数据预览相关方法
    handlePageChange(page) {
      this.currentPage = page;
    },

    getRowIndex(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },

    getCellValue(row, field) {
      const fieldName = field.dataeaseName || field.name;
      const value = row[fieldName];

      // 处理null、undefined等值
      if (value === null || value === undefined) {
        return '-';
      }

      // 处理日期类型
      if (field.type && ['DATE', 'DATETIME', 'TIMESTAMP'].includes(field.type.toUpperCase())) {
        if (value) {
          try {
            return new Date(value).toLocaleString();
          } catch (e) {
            return value;
          }
        }
      }

      // 处理数值类型
      if (field.type && ['INT', 'INTEGER', 'BIGINT', 'DECIMAL', 'DOUBLE', 'FLOAT'].includes(field.type.toUpperCase())) {
        if (typeof value === 'number') {
          return value.toLocaleString();
        }
      }

      return value;
    },

    // PDF导出方法
    async exportToPDF(message) {
      if (!message.chartConfig) return;
      
      try {
        // 设置导出状态
        this.$set(message, 'exporting', true);
        
        // 1. 创建临时容器
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.width = '800px'; // 固定宽度
        tempContainer.style.background = '#fff';
        tempContainer.style.padding = '20px';
        document.body.appendChild(tempContainer);
        
        // 2. 构建导出内容
        // 标题 - 使用数据集名称
        const title = message.chartConfig.title || '数据分析报告';
        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';
        const currentDate = new Date().toLocaleString();
        
        // 提取AI描述文本 (去除HTML标签和chart_data_id)
        let description = message.content
          .replace(/<chart_data_id>.*?<\/chart_data_id>/g, '')
          .replace(/<div class="chart-notice">.*?<\/div>/g, '')
          .trim();
          
        // 如果描述中有HTML标签，创建临时元素解析纯文本
        if (description.includes('<')) {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = description;
          description = tempDiv.textContent || tempDiv.innerText || '';
        }
        
        // 构建HTML内容
        tempContainer.innerHTML = `
          <div style="font-family: Arial, sans-serif;">
            <h1 style="text-align: center; color: #333;">${title}</h1>
            <p style="text-align: right; color: #666;">数据集: ${datasetName}</p>
            <p style="text-align: right; color: #666;">生成时间: ${currentDate}</p>
            <hr style="margin: 20px 0;">
            
            <div style="margin: 20px 0;">
              <h3>分析描述:</h3>
              <p>${description}</p>
            </div>
            
            <div id="chart-container" style="height: 400px; margin: 20px 0;"></div>
            
            <div style="margin: 20px 0;">
              <h3>数据表格:</h3>
              <div id="data-table"></div>
            </div>
          </div>
        `;
        
        // 3. 渲染图表
        const chartContainer = tempContainer.querySelector('#chart-container');
        const chart = echarts.init(chartContainer);
        
        // 直接使用与问答界面相同的逻辑
        const chartConfig = message.chartConfig;
        let options;
        
        // 根据图表类型选择对应的处理函数
        switch(chartConfig.type) {
          case 'bar':
            options = this.getBarChartOptions(chartConfig);
            break;
          case 'line':
            options = this.getLineChartOptions(chartConfig);
            break;
          case 'pie':
            options = this.getPieChartOptions(chartConfig);
            break;
          case 'bar-horizontal':
            options = this.getBarHorizontalOptions(chartConfig);
            break;
          default:
            options = this.getDefaultOptions(chartConfig);
        }
        
        // 设置图表配置
        chart.setOption(options);
        console.log('PDF导出: 图表配置已设置', options);
        
        // 等待足够长的时间确保渲染完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 4. 渲染数据表格
        const dataTableContainer = tempContainer.querySelector('#data-table');
        this.renderDataTable(dataTableContainer, message.chartConfig);
        
        // 5. 使用html2canvas捕获内容
        const canvas = await html2canvas(tempContainer, {
          scale: 2, // 提高清晰度
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff'
        });
        
        // 6. 创建PDF
        const imgData = canvas.toDataURL('image/png');
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4'
        });
        
        // 计算适当的宽度和高度以适应A4页面
        const imgWidth = 210 - 40; // A4宽度减去边距
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        
        // 添加图像到PDF
        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);
        
        // 7. 保存PDF
        pdf.save(`${title}-${new Date().getTime()}.pdf`);
        
        // 8. 清理
        document.body.removeChild(tempContainer);
        chart.dispose();
        
        // 重置导出状态
        this.$set(message, 'exporting', false);
        
        // PDF导出完成
        
      } catch (error) {
        console.error('PDF导出失败:', error);
        this.$set(message, 'exporting', false);
      }
    },
    
    // 导出完整对话方法
    async exportAllConversation() {
      try {
        // 设置导出状态
        this.exportingAll = true;
        
        // 1. 提取所有图表消息
        let chartMessages = [];
        
        // 先找出所有包含图表的消息
        for (let i = 0; i < this.messages.length; i++) {
          const message = this.messages[i];
          if (!message.isUser && message.chartConfig) {
            chartMessages.push({
              message: message,
              index: i
            });
          }
        }
        
        // 如果没有图表，直接返回
        if (chartMessages.length === 0) {
          this.exportingAll = false;
          return;
        }
        
        // 2. 创建PDF
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4'
        });
        
        // 3. 处理每个图表，每个图表单独一页
        for (let i = 0; i < chartMessages.length; i++) {
          // 如果不是第一页，添加新页面
          if (i > 0) {
            pdf.addPage();
          }
          
          const chartMessage = chartMessages[i].message;
          const chartConfig = chartMessage.chartConfig;
          
          // 创建临时容器
          const tempContainer = document.createElement('div');
          tempContainer.style.position = 'absolute';
          tempContainer.style.left = '-9999px';
          tempContainer.style.width = '800px';
          tempContainer.style.background = '#fff';
          tempContainer.style.padding = '20px';
          document.body.appendChild(tempContainer);
          
          // 构建PDF标题和基本信息
          const title = chartConfig.title || '数据分析图表';
          const currentDate = new Date().toLocaleString();
          const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';
          
          let htmlContent = `
            <div style="font-family: Arial, sans-serif; padding: 20px;">
              <h1 style="text-align: center; color: #333; font-size: 24px; margin-bottom: 30px;">${title}</h1>
              <div style="text-align: right; margin-bottom: 20px;">
                /*<p style="color: #666; margin: 5px 0;">数据集: ${datasetName}</p>*/
                <p style="color: #666; margin: 5px 0;">生成时间: ${currentDate}</p>
              </div>
              
              <div style="margin: 20px 0;">
                <h2 style="color: #333; font-size: 20px; margin-bottom: 15px;">分析描述:</h2>
          `;
          
          // 添加描述
          let description = chartMessage.content;
          
          // 清理HTML标签，保留纯文本
          if (description.includes('<')) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = description;
            description = tempDiv.textContent || tempDiv.innerText || '';
          }
          
          htmlContent += `
            <p style="margin: 15px 0; color: #333; line-height: 1.6;">${description}</p>
            
            <div style="margin: 30px 0;">
              <div id="chart-container" style="height: 400px; margin: 30px 0;"></div>
              
              <div style="margin: 30px 0;">
                <h3 style="color: #333; font-size: 18px; margin-bottom: 15px;">数据表格:</h3>
                <div id="data-table"></div>
              </div>
            </div>
          `;
          
          htmlContent += `</div>`;
          
          // 设置HTML内容
          tempContainer.innerHTML = htmlContent;
          
          // 渲染图表
          const chartContainer = tempContainer.querySelector('#chart-container');
          const dataTableContainer = tempContainer.querySelector('#data-table');
          
          if (chartContainer && dataTableContainer) {
            // 初始化图表
            const chartInstance = echarts.init(chartContainer);
            let options;
            
            // 复用你现有的图表处理逻辑
            switch(chartConfig.type) {
              case 'bar':
                options = this.getBarChartOptions(chartConfig);
                break;
              case 'line':
                options = this.getLineChartOptions(chartConfig);
                break;
              case 'pie':
                options = this.getPieChartOptions(chartConfig);
                break;
              case 'bar-horizontal':
                options = this.getBarHorizontalOptions(chartConfig);
                break;
              default:
                options = this.getDefaultOptions(chartConfig);
            }
            
            chartInstance.setOption(options);
            
            // 渲染数据表格
            this.renderDataTable(dataTableContainer, chartConfig);
            
            // 等待图表渲染完成
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 使用html2canvas捕获当前页面内容
            const canvas = await html2canvas(tempContainer, {
              scale: 2,
              useCORS: true,
              allowTaint: true,
              backgroundColor: '#ffffff'
            });
            
            // 将canvas转为图像
            const imgData = canvas.toDataURL('image/png');
            
            // 计算适当的宽度和高度以适应A4页面
            const pageWidth = pdf.internal.pageSize.getWidth();
            const pageHeight = pdf.internal.pageSize.getHeight();
            const imgWidth = pageWidth - 40; // 左右各20mm边距
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            
            // 如果图像高度超过页面高度，进行缩放
            let finalImgHeight = imgHeight;
            let finalImgWidth = imgWidth;
            
            if (imgHeight > pageHeight - 40) { // 上下各20mm边距
              finalImgHeight = pageHeight - 40;
              finalImgWidth = (canvas.width * finalImgHeight) / canvas.height;
            }
            
            // 添加图像到PDF，居中显示
            const xPos = (pageWidth - finalImgWidth) / 2;
            const yPos = 20; // 顶部边距
            
            pdf.addImage(imgData, 'PNG', xPos, yPos, finalImgWidth, finalImgHeight);
            
            // 清理
            chartInstance.dispose();
            document.body.removeChild(tempContainer);
          }
        }
        
        // 4. 保存PDF
        const fileName = `指标分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;
        pdf.save(fileName);
        
        // 5. 重置状态
        this.exportingAll = false;
        
      } catch (error) {
        console.error('指标导出失败:', error);
        this.exportingAll = false;
      }
    },
    
    // 创建基本图表配置
    createBasicChartOptions(chartConfig) {
      const type = chartConfig.type || 'bar';
      let data = [];
      let categories = [];
      
      // 尝试提取数据
      if (chartConfig.data && chartConfig.data.data) {
        data = chartConfig.data.data;
        categories = data.map(item => item.field || item.name);
      }
      
      // 创建基本配置
      const options = {
        title: {
          text: chartConfig.title || '数据图表'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: categories
        },
        yAxis: {
          type: 'value'
        },
        series: []
      };
      
      // 添加系列数据
      if (data.length > 0) {
        if (data[0].series) {
          // 多系列数据
          const seriesMap = {};
          
          // 收集所有系列
          data.forEach(item => {
            if (item.series) {
              item.series.forEach(s => {
                if (!seriesMap[s.category]) {
                  seriesMap[s.category] = {
                    name: s.category,
                    type: type,
                    data: Array(categories.length).fill(null)
                  };
                }
              });
            }
          });
          
          // 填充数据
          data.forEach((item, index) => {
            if (item.series) {
              item.series.forEach(s => {
                seriesMap[s.category].data[index] = s.value;
              });
            }
          });
          
          // 添加到series
          options.series = Object.values(seriesMap);
        } else {
          // 单系列数据
          options.series.push({
            name: '数值',
            type: type,
            data: data.map(item => item.value)
          });
        }
      }
      
      return options;
    },
    
    // 从ChartDisplay组件复制的图表处理函数
    // 柱状图选项生成
    getBarChartOptions(chartData) {
      console.log('生成柱状图选项，数据:', chartData);
      
      // 提取数据
      let data = [];
      let metrics = [];
      
      // 处理标准格式
      if (chartData.data && Array.isArray(chartData.data)) {
        data = chartData.data;
        metrics = chartData.metrics || [];
      } 
      // 处理DataEase返回的格式
      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
        data = chartData.data.data;
        metrics = chartData.data.fields ? 
          chartData.data.fields.filter(f => f.groupType === 'q') : [];
      }
      
      console.log('处理后的数据:', data);
      console.log('指标:', metrics);
      
      // 提取X轴数据（维度）
      const xAxisData = data.map(item => item.field || item.name);
      
      // 提取系列数据（指标）
      const series = [];
      if (data.length > 0 && data[0].series) {
        // 多系列情况
        const categoriesSet = new Set();
        data.forEach(item => {
          if (item.series) {
            item.series.forEach(s => categoriesSet.add(s.category));
          }
        });
        
        const categories = Array.from(categoriesSet);
        categories.forEach(category => {
          const seriesData = data.map(item => {
            const series = item.series?.find(s => s.category === category);
            return series ? series.value : null;
          });
          
          series.push({
            name: category,
            type: 'bar',
            data: seriesData
          });
        });
      } else {
        // 单系列情况
        series.push({
          name: metrics[0]?.name || '数值',
          type: 'bar',
          data: data.map(item => item.value)
        });
      }
      
      return {
        title: {
          text: chartData.title || ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: series.map(s => s.name)
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: series
      };
    },
    
    // 折线图选项生成
    getLineChartOptions(chartData) {
      let data = [];
      let metrics = [];
      
      // 处理标准格式
      if (chartData.data && Array.isArray(chartData.data)) {
        data = chartData.data;
        metrics = chartData.metrics || [];
      } 
      // 处理DataEase返回的格式
      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
        data = chartData.data.data;
        metrics = chartData.data.fields ? 
          chartData.data.fields.filter(f => f.groupType === 'q') : [];
      }
      
      // 提取X轴数据（维度）
      const xAxisData = data.map(item => item.field || item.name);
      
      // 提取系列数据（指标）
      const series = [];
      if (data.length > 0 && data[0].series) {
        // 多系列情况
        const categoriesSet = new Set();
        data.forEach(item => {
          if (item.series) {
            item.series.forEach(s => categoriesSet.add(s.category));
          }
        });
        
        const categories = Array.from(categoriesSet);
        categories.forEach(category => {
          const seriesData = data.map(item => {
            const series = item.series?.find(s => s.category === category);
            return series ? series.value : null;
          });
          
          series.push({
            name: category,
            type: 'line',
            data: seriesData
          });
        });
      } else {
        // 单系列情况
        series.push({
          name: metrics[0]?.name || '数值',
          type: 'line',
          data: data.map(item => item.value)
        });
      }
      
      return {
        title: {
          text: chartData.title || ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: series.map(s => s.name)
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: series
      };
    },
    
    // 饼图选项生成
    getPieChartOptions(chartData) {
      let data = [];
      
      // 处理标准格式
      if (chartData.data && Array.isArray(chartData.data)) {
        data = chartData.data;
      } 
      // 处理DataEase返回的格式
      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
        data = chartData.data.data;
      }
      
      const seriesData = data.map(item => ({
        name: item.field || item.name,
        value: item.value
      }));
      
      return {
        title: {
          text: chartData.title || ''
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: seriesData.map(item => item.name)
        },
        series: [{
          name: '数据',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: seriesData
        }]
      };
    },
    
    // 条形图选项生成（水平柱状图）
    getBarHorizontalOptions(chartData) {
      // 提取数据
      let data = [];
      let metrics = [];
      
      // 处理标准格式
      if (chartData.data && Array.isArray(chartData.data)) {
        data = chartData.data;
        metrics = chartData.metrics || [];
      } 
      // 处理DataEase返回的格式
      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
        data = chartData.data.data;
        metrics = chartData.data.fields ? 
          chartData.data.fields.filter(f => f.groupType === 'q') : [];
      }
      
      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴
      const yAxisData = data.map(item => item.field || item.name);
      
      // 提取系列数据（指标）
      const series = [];
      if (data.length > 0 && data[0].series) {
        // 多系列情况
        const categoriesSet = new Set();
        data.forEach(item => {
          if (item.series) {
            item.series.forEach(s => categoriesSet.add(s.category));
          }
        });
        
        const categories = Array.from(categoriesSet);
        categories.forEach(category => {
          const seriesData = data.map(item => {
            const series = item.series?.find(s => s.category === category);
            return series ? series.value : null;
          });
          
          series.push({
            name: category,
            type: 'bar',  // 使用标准的'bar'类型
            data: seriesData
          });
        });
      } else {
        // 单系列情况
        series.push({
          name: metrics[0]?.name || '数值',
          type: 'bar',  // 使用标准的'bar'类型
          data: data.map(item => item.value)
        });
      }
      
      return {
        title: {
          text: chartData.title || ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: series.map(s => s.name)
        },
        // 关键区别：交换X轴和Y轴的配置
        xAxis: {
          type: 'value'  // 值轴在X轴
        },
        yAxis: {
          type: 'category',  // 类别轴在Y轴
          data: yAxisData
        },
        series: series
      };
    },
    
    // 获取默认图表选项
    getDefaultOptions(chartData) {
      return {
        title: {
          text: chartData.title || '数据图表'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['暂无数据']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: chartData.type || 'bar',
          data: [0]
        }]
      };
    },
    
    // 渲染数据表格
    renderDataTable(container, chartConfig) {
      // 提取数据
      let data = [];
      let headers = [];
      
      // 处理不同的数据格式
      if (chartConfig.data && chartConfig.data.data) {
        data = chartConfig.data.data;
        
        // 尝试从数据中提取表头
        if (data.length > 0) {
          if (data[0].series) {
            // 多系列数据
            headers = ['维度'];
            const firstItem = data[0];
            if (firstItem.series && Array.isArray(firstItem.series)) {
              firstItem.series.forEach(s => {
                headers.push(s.category || '数值');
              });
            }
          } else {
            // 单系列数据
            headers = ['维度', '数值'];
          }
        }
      }
      
      // 创建表格HTML
      let tableHTML = '<table style="width:100%; border-collapse: collapse;">';
      
      // 添加表头
      tableHTML += '<thead><tr>';
      headers.forEach(header => {
        tableHTML += `<th style="border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;">${header}</th>`;
      });
      tableHTML += '</tr></thead>';
      
      // 添加数据行
      tableHTML += '<tbody>';
      data.forEach(item => {
        tableHTML += '<tr>';
        
        // 添加维度列
        tableHTML += `<td style="border: 1px solid #ddd; padding: 8px;">${item.field || item.name || ''}</td>`;
        
        // 添加数值列
        if (item.series) {
          // 多系列数据
          item.series.forEach(s => {
            tableHTML += `<td style="border: 1px solid #ddd; padding: 8px;">${s.value !== undefined ? s.value : ''}</td>`;
          });
        } else {
          // 单系列数据
          tableHTML += `<td style="border: 1px solid #ddd; padding: 8px;">${item.value !== undefined ? item.value : ''}</td>`;
        }
        
        tableHTML += '</tr>';
      });
      tableHTML += '</tbody></table>';
      
      // 设置表格HTML
      container.innerHTML = tableHTML;
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  position: relative;
  height: 100vh;
  overflow: hidden;
  background-color: #ffffff;
}

.app-layout {
  display: flex;
  height: 100vh; /* 占满整个视口高度 */
  overflow: hidden;
  background-color: #ffffff;
}

.sidebar {
  width: 200px;
  border-right: 1px solid #ebeef5;
  background-color: #fff;
  height: 100%;
  overflow-y: auto;
}

.logo {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.logo h2 {
  margin: 0;
  font-size: 25px;
  text-align: center;
  white-space: nowrap;
  letter-spacing: 2px;
  font-weight: 900;
}

.menu {
  padding: 10px 0;
}

.menu-item {
  padding: 10px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.menu-item:hover {
  background-color: #f5f7fa;
}

.menu-item i {
  margin-right: 5px;
}

.menu-item.active {
  background-color: #ecf5ff;
  color: #409eff;
}

.main-content {
  flex: 1;
  padding: 20px;
  overflow: visible; /* 允许子元素特效溢出 */
  display: flex;
  flex-direction: column; /* 垂直排列子元素 */
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  height: 100vh; /* 占满整个视口高度 */
  box-sizing: border-box; /* 包含padding和border */
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1); /* 平滑过渡 */
  position: relative; /* 为子元素定位 */
  background-color: #ffffff; /* 纯白背景色 */
}

.header {
  margin-bottom: 15px;
  width: 100%;
  text-align: center;
  padding: 20px 0;
}

.header h2 {
  margin: 0;
  font-size: 30px;
  color: #2c3e50;
  font-weight: 600;
}

.highlight {
  background: linear-gradient(135deg, #667eea 0%, #6432e4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

.highlight::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  opacity: 0.7;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.highlight:hover::after {
  transform: scaleX(1);
}

.data-selection {
  margin: 0 auto; /* 居中显示 */
  max-width: 900px; /* 与输入框容器宽度一致 */
  padding: 25px 30px; /* 减少垂直padding，增加水平padding */
  border-radius: 25px; /* 与输入框圆角保持一致 */
  overflow: visible; /* 允许子元素特效溢出 */
  position: relative;
  z-index: 1; /* 确保在正确层级 */
}

.data-selection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(118, 120, 240, 0.08);
  border-radius: 25px; /* 与容器圆角保持一致 */
  z-index: -1;
}

.data-selection h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #2c3e50;
  font-weight: 600;
}

.data-sets {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  overflow: visible; /* 允许卡片特效溢出 */
  position: relative;
}

.data-sets .el-row {
  justify-content: center;
}

/* 欢迎容器 */
.welcome-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  gap: 20px;
}

.welcome-icon {
  font-size: 48px;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.welcome-content {
  text-align: left;
}

.welcome-title {
  margin: 0;
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.2;
  margin-bottom: 8px;
}

.highlight-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  position: relative;
}

.highlight-gradient::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  opacity: 0.6;
}

.welcome-subtitle {
  color: #666;
  font-size: 16px;
  margin: 0;
  line-height: 1.5;
  max-width: 500px;
}

/* 功能徽章 */
.feature-badges {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
  transition: all 0.3s ease;
  cursor: default;
}

.badge:nth-child(2) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.badge:nth-child(3) {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #8b4513;
  box-shadow: 0 4px 12px rgba(252, 182, 159, 0.3);
}

.badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);
}

.badge:nth-child(2):hover {
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.badge:nth-child(3):hover {
  box-shadow: 0 6px 20px rgba(252, 182, 159, 0.4);
}

.badge-icon {
  font-size: 16px;
}

.badge-text {
  font-weight: 600;
  letter-spacing: 0.5px;
}

.data-card {
  cursor: pointer;
  margin-bottom: 15px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  height: 110px;
  background: #f8f9fa;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 26px !important;
  overflow: hidden; /* 隐藏按钮溢出 */
  position: relative;
  z-index: 2; /* 确保悬停时在上层 */
}

.data-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.03) 50%, rgba(255, 255, 255, 0.02) 100%);
  border-radius: 28px;
  z-index: -1;
}


.data-card:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 20%, #f1f3f4 50%, #e9ecef 100%);
  z-index: 10; /* 悬停时提升层级 */
}

/* 悬停按钮容器 */
.card-hover-buttons {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.03) 0%, rgba(255, 255, 255, 0.02) 0%);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 0 4%;
  transform: translateY(100%); /* 默认隐藏 */
  opacity: 0; /* 增加透明度控制 */
  visibility: hidden; /* 增加可见性控制 */
  transition: all 0.36s cubic-bezier(0.1, 0.2, 0.2, 0.3);
}
.card-btn.preview-btn {
  font-size: 13px;
}

/* 只有真正悬停时才显示按钮 */
.data-card:hover .card-hover-buttons {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

/* 确保鼠标离开后立即隐藏 */
.data-card:not(:hover) .card-hover-buttons {
  transform: translateY(100%);
  opacity: 0;
  visibility: hidden;
}

/* 悬停按钮样式 */
.card-btn {
  flex: 1;
  height: 32px;
  border: none;
  border-radius: 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.preview-btn {
  background: white;
  color: #666;
  border: 1px solid #e1e8ed;
}

.preview-btn:hover {
  background: #f8f9fa;
  border-color: #d1d9e0;
}

.ask-btn {
  background: #6b6bec;
  color: rgb(240, 243, 247);
}

.ask-btn:hover {
  background: #6366f1;
}

.data-header {
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  min-height: 28px;
}

.sample-tag {
  background-color: rgba(64, 158, 255, 0.9);
  color: #ffffff;
  padding: 2px 6px;
  font-size: 12px;
  border-radius: 4px;
  margin-right: 8px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
  white-space: nowrap;
  flex-shrink: 0;
}

.data-title {
  margin-left: 5px;
  margin-right: 5px;
  font-weight: bold;
  color: #2c3e50;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  display: inline-block;
  vertical-align: middle;
}

.common-tag {
  background-color: rgba(144, 147, 153, 0.9);
  color: #ffffff;
  padding: 2px 6px;
  font-size: 12px;
  border-radius: 4px;
  margin-left: 8px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(144, 147, 153, 0.3);
  white-space: nowrap;
  flex-shrink: 0;
}

.data-fields {
  display: flex;
  flex-wrap: nowrap;
  margin-top: 10px;
  position: relative;
  z-index: 2;
  overflow: hidden;
  align-items: center;
  gap: 4px;
}

.field-tag-single-line {
  margin-right: 0 !important;
  margin-bottom: 0 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
  flex-shrink: 0;
}

.more-fields-indicator {
  color: #909399;
  font-size: 12px;
  margin-left: 4px;
  flex-shrink: 0;
}

.field-item {
  background-color: #f5f7fa;
  padding: 2px 8px;
  margin: 4px;
  border-radius: 2px;
  font-size: 12px;
}

.result-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.answer-text {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 15px;
}

.recent-chats {
  margin-top: 20px;
}

.recent-chats .title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  font-size: 14px;
  color: #606266;
}

.chat-list {
  margin-top: 5px;
}

.chat-item {
  padding: 8px 15px;
  font-size: 12px;
  color: #303133;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #ebeef5;
}

.chat-time {
  font-size: 10px;
  color: #909399;
  margin-top: 4px;
}

.multi-line-input .el-input__inner {
  white-space: pre-wrap;
  word-wrap: break-word;
  height: auto !important;
  line-height: inherit;
  padding: 6px 10px;
  min-height: 40px;
}

/* 底部问题输入区域（关键修改） */
.question-input-container {
  width: 100%;
  padding: 25px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  transition: all 0.8s cubic-bezier(0.22, 1, 0.36, 1); /* 更长的过渡时间，更强的缓动曲线 */
  min-height: 120px;
  position: relative;
  z-index: 5; /* 确保在其他元素之上 */
}

/* 移除展开状态样式，保持简洁 */

.question-input-container > span {
  margin-bottom: 18px; /* 增大10%: 16px * 1.1 ≈ 18px */
  color: #606266;
  font-size: 15px; /* 增大10%: 14px * 1.1 ≈ 15px */
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.question-input-wrapper {
  align-items: center;
  width: 100%;
  max-width: 900px; /* 调整为900px */
  margin: 0 auto;
  background: #fff;
  border-radius: 35px; /* 更大的圆角 */
  padding: 18px 28px; /* 更大的内边距 */
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12),
              0 4px 15px rgba(0, 0, 0, 0.08),
              0 0 0 1px rgba(64, 158, 255, 0.05); /* 增强阴影效果 */
  border: 2px solid rgba(64, 158, 255, 0.1); /* 更明显的边框 */
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

/* 当有字段选择区域时，调整输入框的上边框圆角 */
.field-selection-mini ~ .question-input-wrapper {
  border-radius: 0 0 35px 35px; /* 与字段选择区域圆角保持一致 */
  border-top: none; /* 移除顶部边框，避免重叠 */
  margin-top: -4px; /* 增加负边距，完全消除中间的线 */
}

.question-input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
}

.question-input-wrapper:hover {
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15),
              0 6px 20px rgba(0, 0, 0, 0.08),
              0 0 0 1px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
  border-color: rgba(64, 158, 255, 0.2);
}

.question-input-wrapper:focus-within {
  box-shadow: 0 15px 40px rgba(64, 158, 255, 0.2),
              0 8px 25px rgba(64, 158, 255, 0.12),
              0 0 0 2px rgba(64, 158, 255, 0.15);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateY(-3px);
}

/* 当输入框聚焦时，字段选择区域的联动效果 */
.field-selection-mini.input-focused {
  box-shadow: 0 15px 40px rgba(64, 158, 255, 0.2),
              0 8px 25px rgba(64, 158, 255, 0.12),
              0 0 0 2px rgba(64, 158, 255, 0.15);
  border-color: rgba(64, 158, 255, 0.4);
}

/* 输入框悬停时，字段区域的联动效果 */
.field-selection-mini.input-hover {
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15),
              0 6px 20px rgba(0, 0, 0, 0.08),
              0 0 0 1px rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

/* 字段区域悬停时，输入框的联动效果 */
.question-input-wrapper.field-hover {
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15),
              0 6px 20px rgba(0, 0, 0, 0.08),
              0 0 0 1px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
  border-color: rgba(64, 158, 255, 0.2);
}

.input-prefix {
  margin-right: 10px;
}

.question-input {
  flex: 1;
}

.question-input .el-input__inner {
  border: none;
  background-color: transparent;
  font-size: 17px; /* 增大10%: 15px * 1.1 ≈ 17px */
  font-weight: 400;
  color: #2c3e50;
  padding: 9px 13px; /* 增大10%: 8px * 1.1 ≈ 9px, 12px * 1.1 ≈ 13px */
  transition: all 0.2s ease;
}

/* 大输入框特殊样式 */
.question-input.large-input .el-input__inner {
  font-size: 18px; /* 更大的字体 */
  padding: 15px 20px; /* 更大的内边距 */
  min-height: 60px; /* 最小高度 */
  line-height: 1.5;
}

.question-input .el-input__inner::placeholder {
  color: #a0a8b0;
  font-weight: 400;
}

.question-input .el-input__inner:focus {
  color: #1a202c;
}

.input-actions {
  display: flex;
  align-items: center;
  float: right;
  gap: 9px; /* 增大10%: 8px * 1.1 ≈ 9px */
}

.action-btn {
  width: 40px; /* 增大10%: 36px * 1.1 = 40px */
  height: 40px; /* 增大10%: 36px * 1.1 = 40px */
  border-radius: 50%;
  background: #fff; /* 与卡片相同的纯白色背景 */
  border: 1px solid rgba(0, 0, 0, 0.1); /* 与卡片相同的边框 */
  margin-left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  outline: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 与卡片相同的过渡时间 */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03); /* 与卡片相同的极浅阴影 */
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-8px) scale(1.05); /* 与卡片相同的悬停效果 */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2); /* 与卡片相同的阴影 */
  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%); /* 与卡片相同的悬停渐变 */
}

.action-btn:hover::before {
  opacity: 1;
}

.action-btn:active {
  transform: translateY(-1px) scale(1.02);
  transition: all 0.1s ease;
}

.send-btn {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3),
              0 2px 8px rgba(64, 158, 255, 0.2);
}

.send-btn:hover {
  background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
  box-shadow: 0 15px 35px rgba(64, 158, 255, 0.4); /* 与卡片相同的阴影强度 */
  transform: translateY(-8px) scale(1.05); /* 与卡片相同的悬停效果 */
}

.test-btn {
  background: #fff; /* 与卡片相同的纯白色背景 */
  color: #333333; /* 统一文字颜色 */
  border: 1px solid rgba(0, 0, 0, 0.1); /* 与卡片相同的边框 */
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03); /* 与卡片相同的极浅阴影 */
}

.test-btn:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%); /* 与卡片相同的悬停渐变 */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2); /* 与卡片相同的阴影 */
  transform: translateY(-8px) scale(1.05); /* 与卡片相同的悬停效果 */
}

.debug-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 30%, #e9ecef 70%, #ced4da 100%); /* 与卡片相同的渐变 */
  color: #333333; /* 统一文字颜色 */
  border: 1px solid rgba(0, 0, 0, 0.1); /* 与卡片相同的边框 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 与卡片相同的阴影 */
}

.debug-btn:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%); /* 与卡片相同的悬停渐变 */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2); /* 与卡片相同的阴影 */
  transform: translateY(-8px) scale(1.05); /* 与卡片相同的悬停效果 */
}

.send-btn:disabled {
  background: linear-gradient(135deg, #c0c4cc 0%, #d3d4d6 100%);
  cursor: not-allowed;
  box-shadow: 0 2px 8px rgba(192, 196, 204, 0.2);
  transform: none !important;
}

.send-btn:disabled:hover {
  transform: none !important;
  box-shadow: 0 2px 8px rgba(192, 196, 204, 0.2);
}

/* 按钮图标样式优化 */
.action-btn i {
  font-size: 18px; /* 增大10%: 16px * 1.1 ≈ 18px */
  transition: all 0.3s ease;
}

.action-btn:hover i {
  transform: scale(1.1);
}

.send-btn i {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.test-btn i {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.debug-btn i {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 输入框容器的微光效果 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.question-input-wrapper:focus-within::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(64, 158, 255, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  pointer-events: none;
  border-radius: 28px; /* 稍小于容器圆角，避免边框遮挡 */
}

/* 字段选择区域的内部特效 - 与输入框保持一致 */
.field-selection-mini:focus-within::after,
.field-selection-mini.input-focused::after,
.field-selection-mini:hover::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(64, 158, 255, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  pointer-events: none;
  border-radius: 34px 34px 0 0; /* 只有顶部圆角 */
  z-index: 1;
}

/* 当有字段选择区域时，调整输入框特效的定位和圆角 */
.field-selection-mini ~ .question-input-wrapper:focus-within::after {
  top: 0; /* 顶部没有边框，从0开始 */
  border-radius: 0 0 34px 34px; /* 只有底部圆角，与容器保持一致 */
}

/* 当字段区域有特效时，输入框也同步显示特效 */
.field-selection-mini:focus-within ~ .question-input-wrapper::after,
.field-selection-mini:hover ~ .question-input-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(64, 158, 255, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  pointer-events: none;
  border-radius: 0 0 34px 34px;
  z-index: 1;
}

/* 头部操作按钮样式优化 - 与卡片统一 */
.header-action-btn {
  margin-left: 13px !important; /* 增大10%: 12px * 1.1 ≈ 13px */
  padding: 9px 18px !important; /* 增大10%: 8px * 1.1 ≈ 9px, 16px * 1.1 ≈ 18px */
  border-radius: 22px !important; /* 增大10%: 20px * 1.1 = 22px */
  font-weight: 500 !important;
  font-size: 15px !important; /* 增大10%: 默认14px * 1.1 ≈ 15px */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important; /* 与卡片相同的过渡时间 */
  position: relative !important;
  overflow: hidden !important;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03) !important; /* 与卡片相同的极浅阴影 */
  border: 1px solid rgba(0, 0, 0, 0.1) !important; /* 与卡片相同的边框 */
  background: #fff !important; /* 纯白色背景 */
  color: #333333 !important; /* 统一文字颜色 */
}

.header-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #eef4f8 20%, #eef4f8 50%, #dff0f9 100%);

  /* background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent); */
  transition: left 0.5s ease;
}

.header-action-btn:hover::before {
  left: 100%;
}

.select-data-btn:hover {
  background: linear-gradient(135deg, #ffffff 0%, #eef4f8 20%, #eef4f8 50%, #dff0f9 100%);

  /* background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%) !important; 与卡片相同的悬停渐变 */
  color: #333333 !important;
  transform: translateY(-8px) scale(1.05) !important; /* 与卡片相同的悬停效果 */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important; /* 与卡片相同的阴影 */
}

.export-btn:hover {
  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%) !important; /* 与卡片相同的悬停渐变 */
  color: #333333 !important;
  transform: translateY(-8px) scale(1.05) !important; /* 与卡片相同的悬停效果 */
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important; /* 与卡片相同的阴影 */
}

.header-action-btn:disabled {
  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%) !important;
  color: #bdbdbd !important;
  border-color: rgba(189, 189, 189, 0.3) !important;
  transform: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.header-action-btn i {
  margin-right: 6px;
  transition: transform 0.3s ease;
}

.header-action-btn:hover i {
  transform: scale(1.1) rotate(5deg);
}

/* 数据集抽屉样式优化 */
.dataset-drawer .el-drawer__header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 20px 24px;
  margin-bottom: 0;
}

.dataset-drawer .el-drawer__title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.drawer-content {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100%;
}

.search-section {
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.dataset-search-input {
  margin-bottom: 12px;
}

.dataset-search-input .el-input__inner {
  border-radius: 8px;
  border: 2px solid #e9ecef;
  padding: 12px 16px;
  font-size: 15px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.dataset-search-input .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
  background: #ffffff;
}

.dataset-search-input .el-input__prefix {
  color: #909399;
  font-size: 16px;
}

.search-stats {
  color: #606266;
  font-size: 13px;
  font-weight: 500;
  text-align: right;
  margin-top: 8px;
  padding: 4px 8px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 12px;
  float: right;
}

.datasets-grid {
  background: transparent;
}

.drawer-data-card {
  margin-bottom: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden; /* 隐藏按钮溢出 */
  cursor: pointer;
}

.drawer-data-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.03) 50%, rgba(255, 255, 255, 0.02) 100%);
  border-radius: 6px;
  z-index: -1;
}

.drawer-data-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  z-index: 10; /* 悬停时提升层级 */
}

/* 抽屉里的数据集卡片悬停按钮显示 */
.drawer-data-card:hover .card-hover-buttons {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

/* 确保抽屉里的卡片鼠标离开后立即隐藏按钮 */
.drawer-data-card:not(:hover) .card-hover-buttons {
  transform: translateY(100%);
  opacity: 0;
  visibility: hidden;
}

.field-tag {
  margin-right: 6px !important;
  margin-bottom: 6px !important;
  background: rgba(64, 158, 255, 0.1) !important;
  border: 1px solid rgba(64, 158, 255, 0.2) !important;
  color: #409eff !important;
  font-weight: 500 !important;
}

.more-fields {
  color: #909399;
  font-size: 12px;
  font-style: italic;
  margin-left: 4px;
}

/* 抽屉整体样式优化 */
.dataset-drawer .el-drawer {
  border-radius: 12px 0 0 12px;
  overflow: hidden;
}

.dataset-drawer .el-drawer__body {
  padding: 0;
  background: #fafbfc;
}

/* 优化滚动条样式 */
.dataset-drawer .el-drawer__body::-webkit-scrollbar {
  width: 6px;
}

.dataset-drawer .el-drawer__body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.dataset-drawer .el-drawer__body::-webkit-scrollbar-thumb {
  background: rgba(64, 158, 255, 0.3);
  border-radius: 3px;
}

.dataset-drawer .el-drawer__body::-webkit-scrollbar-thumb:hover {
  background: rgba(64, 158, 255, 0.5);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #c0c4cc;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
}

/* 新的字段表格样式 - 干净整齐的设计 */
.fields-table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
  border: 1px solid #e9ecef;
  margin: 16px;
}

/* 搜索栏样式 */
.table-search-bar {
  padding: 12px 16px;
  border-bottom: 1px solid #f1f3f4;
  background: #fafbfc;
  display: flex;
  justify-content: flex-end;
}

.field-search-input {
  width: 180px;
}

.field-search-input .el-input__inner {
  border-radius: 16px;
  border: 1px solid #e1e8ed;
  background: white;
  font-size: 13px;
  height: 32px;
}

/* 表格样式 */
.clean-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 2fr;
  background: #f8f9fa;
  border: 1px solid #ebeef5; /* 添加完整边框 */
  border-bottom: 1px solid #e9ecef;
}

.header-cell {
  padding: 10px 14px;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  border-right: 1px solid #e9ecef;
  display: flex;
  align-items: center;
}

.header-cell:last-child {
  border-right: none;
}

/* 表格body包装器 */
.table-body-wrapper {
  position: relative;
  border: 1px solid #ebeef5;
  border-top: none;
}

/* 滚动渐变遮罩 - 当内容可滚动时显示 */
.table-body-wrapper::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95), transparent);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.table-body-wrapper:hover::after {
  opacity: 1;
}

.table-body {
  background: white;
  max-height: calc(100vh - 350px); /* 根据视窗高度动态计算，预留头部和底部空间 */
  overflow-y: auto; /* 启用垂直滚动 */
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 2fr;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;
  background-color: white;
}

.table-row:nth-child(even) {
  background-color: #fafbfc;
}

.table-row:hover {
  background-color: #e8f4fd !important;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-cell {
  padding: 8px 14px;
  border-right: 1px solid #f1f3f4;
  display: flex;
  align-items: center;
  min-height: 40px;
}

.table-cell:last-child {
  border-right: none;
}

/* 字段名称列样式 */
.field-name-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-type-icon {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 11px;
}

.field-type-icon.text-icon {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.field-type-icon.number-icon {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.field-type-icon.date-icon {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.field-type-icon.boolean-icon {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.field-type-icon.default-icon {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.field-name-text {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 类型列样式 */
.field-type-text {
  font-size: 13px;
  color: #495057;
  font-weight: 400;
}

/* 维度/度量列样式 */
.group-type-text {
  font-size: 12px;
  font-weight: 500;
  padding: 3px 6px;
  border-radius: 10px;
  display: inline-block;
}

.group-type-text.dimension-text {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.group-type-text.measure-text {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

/* 字段描述列样式 */
.field-desc-text {
  font-size: 13px;
  color: #495057;
  font-weight: 400;
}

.field-type-badge {
  padding: 3px 10px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.field-type-badge.text-type {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.field-type-badge.number-type {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

.field-type-badge.date-type {
  background: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
  border: 1px solid rgba(230, 162, 60, 0.3);
}

.field-type-badge.default-type {
  background: rgba(144, 147, 153, 0.1);
  color: #909399;
  border: 1px solid rgba(144, 147, 153, 0.3);
}

.field-group-type {
  font-size: 12px;
  padding: 1px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.field-group-type.dimension-type {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.field-group-type.measure-type {
  background: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}



/* 字段描述列样式 */
.field-desc-content {
  width: 100%;
}

.field-desc {
  font-size: 15px;
  color: #606266;
  line-height: 1.4;
  word-break: break-word;
}

.no-desc {
  font-size: 15px;
  color: #909399;
  font-style: italic;
  line-height: 1.4;
}

/* 底部统计区域样式 */
.table-footer {
  padding: 10px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.field-stats-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  color: #495057;
}

.total-count {
  font-weight: 600;
  color: #2c3e50;
}

.type-counts {
  display: flex;
  gap: 16px;
}

.dimension-count,
.measure-count {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.count-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.dimension-dot {
  background: #409eff;
}

.measure-dot {
  background: #67c23a;
}

.preview-limit {
  color: #6c757d;
  font-size: 12px;
}

/* 字段计数信息样式 */
.field-count-info {
  color: #6c757d;
  font-size: 12px;
  font-weight: 500;
}

/* 滚动提示样式 */
.scroll-hint {
  color: #409eff;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  animation: scroll-hint-pulse 2s infinite;
}

.scroll-hint i {
  font-size: 14px;
}

@keyframes scroll-hint-pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

/* 表格滚动条优化 */
.table-body::-webkit-scrollbar {
  width: 8px;
}

.table-body::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

.table-body::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

.table-body::-webkit-scrollbar-corner {
  background: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .table-header,
  .table-row {
    grid-template-columns: 1.5fr 1fr 1fr 1.5fr;
  }

  .field-name-text {
    font-size: 12px;
  }

  .field-desc-text {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
  }

  .table-cell {
    border-right: none;
    border-bottom: 1px solid #f1f3f4;
    padding: 8px 12px;
    min-height: auto;
  }

  .header-cell::before {
    content: attr(data-label);
    font-weight: 600;
    margin-right: 8px;
  }

  .field-stats-summary {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .type-counts {
    gap: 12px;
  }
}

/* 数据详情抽屉样式 */
.dataset-detail-drawer .el-drawer__header {
  
  color: #2c3e50;
  padding: 20px 24px;
  position: relative;
  transition: all 0.3s ease;
}

.dataset-detail-drawer .el-drawer__header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.dataset-detail-drawer .el-drawer__header:hover::before {
  left: 100%;
}

.dataset-detail-drawer .el-drawer__title {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.dataset-detail-drawer .el-drawer__body {
  padding: 0;
  background: #f8f9fa;
  overflow-y: auto;
  height: 100%;
}

/* 让数据详情内容自然展示，通过抽屉本身的滚动来处理溢出 */
.detail-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 头部信息区域 */
.detail-header {
  background: white;
  padding: 20px;
  border-bottom: none;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

/* 统一的数据集信息区域 */
.dataset-info-unified {
  flex: 1;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
}

.dataset-title-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.dataset-icon {
  color: #667eea;
  font-size: 20px;
}

.dataset-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.dataset-stats {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #495057;
  background: white;
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 500;
}

.stat-badge i {
  color: #6c757d;
  font-size: 12px;
}

/* 悬浮提问按钮 */
.floating-ask-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 25px;
  padding: 10px 24px;
  font-weight: 500;
  color: white;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  position: absolute;
  top: 16px;
  right: 16px;
  overflow: hidden;
  font-size: 14px;
  z-index: 10;
}

.floating-ask-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.floating-ask-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.floating-ask-btn:hover::before {
  left: 100%;
}

.floating-ask-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* 字段信息区域 */
.fields-section {
  background: transparent;
  margin: 0;
  padding: 0;
}

/* 字段头部样式 */
.fields-header {
  padding: 20px 20px 16px 20px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.fields-summary {
  flex: 1;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-title i {
  color: #667eea;
}

.summary-stats {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 500;
}

.stat-badge.dimension {
  background: #e3f2fd;
  color: #1976d2;
}

.stat-badge.measure {
  background: #e8f5e8;
  color: #388e3c;
}

.stat-badge.total {
  background: #f3e5f5;
  color: #7b1fa2;
}

.fields-search {
  flex-shrink: 0;
}

.field-search-input {
  width: 200px;
}

/* 字段卡片列表 */
.fields-cards {
  padding: 0 20px 20px 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.field-card {
  background: #fafbfc;
  border-radius: 6px;
  padding: 16px;
  transition: all 0.2s ease;
}

.field-card:hover {
  background: #f1f3f4;
}

.field-card-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.field-info {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  flex: 1;
}

.field-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
  flex-shrink: 0;
}

.field-details {
  flex: 1;
}

.field-name {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 2px;
}

.field-type {
  font-size: 12px;
  color: #6c757d;
}

.field-badge {
  flex-shrink: 0;
}

.group-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 10px;
  font-weight: 500;
}

.group-badge.dimension {
  background: #e3f2fd;
  color: #1976d2;
}

.group-badge.measure {
  background: #e8f5e8;
  color: #388e3c;
}

.field-description {
  font-size: 12px;
  color: #6c757d;
  line-height: 1.4;
  padding-top: 8px;
  margin-top: 8px;
}

.section-header h3 i {
  color: #667eea;
}

.field-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.stat-item.dimension {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.stat-item.measure {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.fields-grid {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.field-card {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.field-card::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.03) 50%, rgba(255, 255, 255, 0.02) 100%);
  border-radius: 10px;
  z-index: -1;
}

.field-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #e9ecef;
  transition: all 0.3s ease;
}

.field-card.dimension-field::before {
  background: linear-gradient(180deg, #3498db 0%, #2980b9 100%);
}

.field-card.measure-field::before {
  background: linear-gradient(180deg, #2ecc71 0%, #27ae60 100%);
}

.field-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #d1ecf1;
}

.field-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.field-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.dimension-field .field-icon {
  background: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.measure-field .field-icon {
  background: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.field-info {
  flex: 1;
  min-width: 0;
}

.field-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.field-origin {
  font-size: 12px;
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.field-type-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.field-type-badge.text-type {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}

.field-type-badge.number-type {
  background: rgba(255, 193, 7, 0.1);
  color: #f39c12;
}

.field-type-badge.date-type {
  background: rgba(156, 39, 176, 0.1);
  color: #9c27b0;
}

.field-type-badge.boolean-type {
  background: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.field-type-badge.default-type {
  background: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.field-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.detail-label {
  color: #6c757d;
  font-weight: 500;
}

.detail-value {
  color: #495057;
  font-weight: 400;
}

/* 数据预览区域 */
.preview-section {
  background: transparent;
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 200px);
}

/* 数据预览头部 */
.preview-header {
  padding: 20px 20px 16px 20px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.preview-stats {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.total-records {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.current-page-info {
  color: #6c757d;
  font-size: 13px;
}

.preview-pagination {
  flex-shrink: 0;
}

/* 数据预览表格容器 */
.preview-table-wrapper {
  padding: 0;
  overflow: auto;
  flex: 1;
  min-height: 0;
}

.preview-table {
  width: 100%;
  background: white;
}

.preview-table .cell-content {
  font-size: 13px;
  color: #495057;
  word-break: break-word;
}

.preview-table .el-table__header {
  background: #f8f9fa;
}

.preview-table .el-table__header th {
  background: #f8f9fa;
  color: #495057;
  font-weight: 600;
  font-size: 15px;
  border-bottom: 2px solid #dee2e6;
}

.preview-table .el-table__body tr:hover {
  background: rgba(102, 126, 234, 0.05);
}

.preview-table .el-table__body td {
  border-bottom: 1px solid #f1f3f4;
  font-size: 15px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-list {
    max-width: 100%;
    margin: 10px auto 0 auto;
    padding: 10px;
  }

  .message {
    max-width: 95%;
    margin-left: 20px;
  }

  .bot-message {
    margin-left: 0px; /* 移动端AI消息贴左边 */
  }

  /* 移动端输入框调整 */
  .question-input-wrapper {
    max-width: 95%;
    padding: 12px 16px;
    border-radius: 25px;
  }

  .question-input.large-input {
    width: 100% !important;
  }

  .question-input.large-input .el-input__inner {
    font-size: 16px;
    padding: 12px 16px;
    min-height: 50px;
  }

  /* 移动端数据集区域调整 */
  .data-selection {
    max-width: 95%;
    padding: 20px 15px;
    border-radius: 20px;
  }

  /* 移动端字段选择区域调整 */
  .field-selection-mini {
    max-width: 95%;
    padding: 12px 16px 8px 16px;
    border-radius: 25px 25px 0 0;
    transform: translateY(1px); /* 移动端稍微减少偏移 */
  }

  .fields-grid {
    grid-template-columns: 1fr;
    padding: 16px;
  }

  .detail-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    padding: 16px;
  }

  .floating-ask-btn {
    position: static;
    align-self: flex-end;
    margin-top: 12px;
  }

  .preview-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .preview-stats {
    justify-content: center;
  }

  .preview-pagination {
    align-self: center;
  }

  .dataset-meta {
    flex-direction: column;
    gap: 8px;
  }
}

/* Suggested Questions Panel */
.suggestions-panel {
  position: absolute;
  bottom: 75px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 600px;
  background-color: #e8f4f8;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.suggestions-title {
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
  font-size: 14px;
  color: #606266;
}

.suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.suggestion-item:hover {
  background-color: #f5f7fa;
}

/* Message list style - 瞬间位置切换 */
.message-list {
  width: 100%;
  max-width: 1200px; /* 增加最大宽度限制 */
  margin: 20px auto 0 auto; /* 居中显示 */
  max-height: calc(100vh - 250px); /* 修改为响应式高度，减去头部和底部的高度 */
  overflow-y: auto;
  padding: 20px;
  background-color: transparent;
  border-radius: 8px;
  flex: 1; /* 让消息列表占据剩余空间 */
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 添加平滑过渡 */
  position: relative;

  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.message-list::-webkit-scrollbar {
  display: none;
}

/* 选择数据集后，消息列表平滑过渡到顶部位置 */
.message-list.expanded-position {
  margin: 0 auto; /* 保持居中，移动到顶部 */
  padding-top: 20px;
  max-height: calc(100vh - 180px); /* 调整高度，为字段选择区域留空间 */
  animation: list-highlight 1s ease-out; /* 添加微妙高亮动画 */
}

.message {
  margin-bottom: 20px;
  display: flex;
  position: relative;
  max-width: 90%; /* 增加消息宽度从80%到90% */
  margin-left: 80px; /* 减少左边距从150px到80px */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.message-content {
  padding: 12px 15px;
  border-radius: 6px;
  line-height: 1.5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  position: relative;
  isolation: isolate;
  z-index: 1;
}

.user-message {
  flex-direction: row-reverse;
  align-self: flex-end;
  margin-left: auto;
}

.user-message .message-content {
  background-color: #ecf5ff;
  margin-right: 0; /* 移除右边距，因为没有头像了 */
}

.bot-message {
  align-self: flex-start;
  margin-left: 0px; /* AI消息贴左边 */
}

.bot-message .message-content {
  background-color: #f5f7fa;
  margin-left: 10px;
}

.avatar-container {
  display: flex;
  align-items: flex-start;
}

.bot-avatar {
  width: 60px; /* 增大宽度以容纳FastBI文字 */
  height: 36px;
  border-radius: 18px; /* 椭圆形：高度的一半 */
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff; /* 白底 */
  color: #333333; /* 黑字 */
  border: 2px solid #e1e8ed; /* 添加边框 */
  font-size: 12px; /* 稍大字体 */
  font-weight: 600; /* 加粗 */
  font-family: 'Arial', sans-serif; /* 清晰字体 */
}

/* 用户头像样式已移除 */

/* Loading animation */
.loading-dots {
  display: inline-block;
}

.dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #999;
  border-radius: 50%;
  margin: 0 2px;
  animation: pulse 1.2s infinite ease-in-out both;
}

.dot:nth-child(2) {
  animation-delay: -0.4s;
}

.dot:nth-child(3) {
  animation-delay: -0.8s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(0.6);
    opacity: 0.4;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Styles related to charts */
.message-content .chart-container {
  position: relative;
  margin: 15px 0;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 10px;
  background-color: #e8f4f8;
  overflow: hidden;
  z-index: 5;
  box-sizing: border-box;
  max-width: 100%; /* 确保不超出消息容器 */
}

/* 确保图表容器有足够高度 */
.message-content .chart-canvas {
  min-height: 300px;
  position: relative;
  z-index: 10;
}

.chart-notice {
  color: #409eff;
  font-size: 14px;
  margin: 10px 0;
  font-weight: bold;
}

/* AI original response popup layer style */
.raw-response-panel {
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 800px;
  background-color: #e8f4f8;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  max-height: 60%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.raw-response-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
  font-size: 16px;
  color: #303133;
  background-color: #f5f7fa;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.raw-response-title .el-icon-monitor {
  margin-right: 8px;
}

.raw-response-title .close-btn {
  background-color: #f5f7fa;
  border: 1px solid #ebeef5;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.raw-response-title .close-btn:hover {
  background-color: #ebeef5;
}

.raw-response-content {
  flex: 1;
  padding: 15px;
  font-size: 14px;
  line-height: 1.6;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
  overflow-wrap: break-word;
}

/* 添加PDF导出相关样式 */
.chart-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

/* 数据限制提示样式 */
.data-limit-notice {
  margin-top: 10px;
  padding: 8px;
  background-color: #f0f9eb;
  color: #67c23a;
  border-radius: 4px;
  font-size: 13px;
  text-align: center;
}

/* 对话框浮动悬停效果 - 增强版 */
.message:hover {
  transform: translateY(-4px) scale(1.02);
}

.message:hover .message-content {
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.18) !important;
}

.user-message:hover .message-content {
  box-shadow: 0 12px 35px rgba(64, 158, 255, 0.25) !important;
  background-color: #e1f3ff !important;
}

.bot-message:hover .message-content {
  box-shadow: 0 12px 35px rgba(103, 194, 58, 0.25) !important;
  background-color: #f0f9eb !important;
}

/* 数据集详情标签页样式 - 简洁版本 */
.detail-tabs {
  margin-top: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.dataset-tabs {
  background-color: transparent;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.dataset-tabs .el-tabs__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.dataset-tabs .el-tab-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.dataset-tabs .el-tabs__header {
  margin: 0 0 20px 0;
  border-bottom: none;
}

.dataset-tabs .el-tabs__nav {
  border: none;
}

.dataset-tabs .el-tabs__item {
  height: 40px;
  line-height: 40px;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 500;
  color: #909399;
  border: none;
  margin-right: 0;
  border-radius: 0;
  background-color: transparent;
  transition: all 0.3s ease;
}

.dataset-tabs .el-tabs__item:hover {
  color: #409eff;
}

.dataset-tabs .el-tabs__item.is-active {
  color: #409eff;
  background-color: transparent;
  font-weight: 600;
}

.dataset-tabs .el-tabs__content {
  padding: 0;
  background-color: transparent;
  border: none;
  box-shadow: none;
}

/* 移除标签页最小高度限制，让内容自然展示 */

/* 字段信息区域样式优化 */
.fields-section .section-header {
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.fields-section .field-stats {
  display: flex;
  gap: 15px;
  align-items: center;
  font-size: 14px;
}

/* 数据预览区域样式优化 */
.preview-section .section-header {
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-section .preview-stats {
  color: #909399;
  font-size: 15px;
}

.preview-section .preview-table {
  border-radius: 6px;
  overflow: hidden;
}

/* 标签页图标样式 */
.dataset-tabs .el-tabs__item i {
  margin-right: 8px;
  font-size: 16px;
}

/* 紧凑型字段选择区域样式 */
.field-selection-compact {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 12px;
  width: 100%;
  max-width: 800px;
}

.field-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 12px;
}

.field-row:last-child {
  margin-bottom: 0;
}

.field-row-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  line-height: 24px;
  flex-shrink: 0;
}

.field-tags-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  flex: 1;
}

.dataset-info-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.dataset-name-compact {
  background: #409eff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.switch-btn-compact {
  color: #409eff;
  padding: 2px 4px;
  font-size: 12px;
}

.switch-btn-compact:hover {
  background: rgba(64, 158, 255, 0.1);
}

/* 超紧凑字段选择区域样式 - 从输入框弹出效果 */
.field-selection-mini {
  background: #fff;
  border: 2px solid rgba(64, 158, 255, 0.1); /* 与输入框边框颜色一致 */
  border-radius: 35px 35px 0 0; /* 与输入框圆角一致 */
  border-bottom: none; /* 完全移除底部边框 */
  padding: 18px 28px 12px 28px; /* 与输入框内边距一致 */
  margin-bottom: 0;
  width: 100%;
  max-width: 900px; /* 与输入框容器宽度完全一致 */
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12),
              0 4px 15px rgba(0, 0, 0, 0.08),
              0 0 0 1px rgba(64, 158, 255, 0.05),
              0 0 0 0 transparent; /* 确保底部没有阴影 */
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 12px; /* 增加行间距，适应多行字段 */
  transform: translateY(2px); /* 轻微向下偏移，营造连接感 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 添加平滑过渡 */
}

/* 字段选择区域悬停特效 - 与输入框保持一致 */
.field-selection-mini:hover {
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15),
              0 6px 20px rgba(0, 0, 0, 0.08),
              0 0 0 1px rgba(64, 158, 255, 0.1);
  transform: translateY(0px); /* 保持与输入框的连接，不向上移动 */
  border-color: rgba(64, 158, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 当字段区域悬停时，同时影响输入框 */
.field-selection-mini:hover ~ .question-input-wrapper {
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15),
              0 6px 20px rgba(0, 0, 0, 0.08),
              0 0 0 1px rgba(64, 158, 255, 0.1);
  transform: translateY(-2px);
  border-color: rgba(64, 158, 255, 0.2);
}

/* 字段选择区域聚焦特效（当内部有元素被点击时） */
.field-selection-mini:focus-within,
.field-selection-mini.active {
  box-shadow: 0 15px 40px rgba(64, 158, 255, 0.2),
              0 8px 25px rgba(64, 158, 255, 0.12),
              0 0 0 2px rgba(64, 158, 255, 0.15);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateY(0px);
}

/* 当字段区域聚焦时，同时影响输入框 */
.field-selection-mini:focus-within ~ .question-input-wrapper,
.field-selection-mini.active ~ .question-input-wrapper {
  box-shadow: 0 15px 40px rgba(64, 158, 255, 0.2),
              0 8px 25px rgba(64, 158, 255, 0.12),
              0 0 0 2px rgba(64, 158, 255, 0.15);
  border-color: rgba(64, 158, 255, 0.4);
  transform: translateY(-3px);
}

.field-mini-row {
  display: flex;
  align-items: flex-start; /* 改为顶部对齐，适应标签换行 */
  justify-content: flex-start; /* 左对齐 */
  margin-bottom: 0; /* 移除底部边距，使用父容器的gap */
  gap: 12px; /* 增大间距: 8px * 1.5 = 12px */
  width: 100%;
  min-height: 32px; /* 设置最小高度，保持一致性 */
}

.field-mini-row:last-child {
  margin-bottom: 0;
}

.field-mini-label {
  font-size: 14px; /* 增大字体: 12px * 1.17 ≈ 14px */
  font-weight: 600; /* 加粗字体 */
  color: #606266;
  min-width: 80px; /* 增大标签宽度: 60px * 1.33 ≈ 80px */
  flex-shrink: 0;
  text-align: left; /* 标签文字左对齐 */
  line-height: 32px; /* 与字段标签高度对齐 */
  align-self: flex-start; /* 确保标签在顶部对齐 */
}

.field-tags-mini {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 6px; /* 行间距8px，列间距6px */
  flex: 1;
  align-items: flex-start; /* 改为顶部对齐，适应多行 */
  justify-content: flex-start; /* 标签左对齐 */
  width: 100%;
  /* 移除max-width限制，让字段根据实际容器宽度展示 */
}

.more-fields {
  font-size: 11px;
  color: #909399;
  margin-left: 4px;
}

/* 数据集显示样式已移除 */

/* 切换按钮样式已移除，使用"选择数据"按钮代替 */

/* 过渡动画样式 */

/* 欢迎区域淡入淡出动画 - 加速消失 */
.welcome-fade-enter-active {
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.welcome-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.6, 0, 0.4, 1);
}

.welcome-fade-enter, .welcome-fade-leave-to {
  opacity: 0;
  transform: translateY(-30px) scale(0.95);
}

.welcome-fade-enter-to, .welcome-fade-leave {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 数据集卡片滑动动画 - 加速消失 */
.datasets-slide-enter-active {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.datasets-slide-leave-active {
  transition: all 0.4s cubic-bezier(0.6, 0, 0.4, 1);
}

.datasets-slide-enter, .datasets-slide-leave-to {
  opacity: 0;
  transform: translateY(50px);
}

.datasets-slide-enter-to, .datasets-slide-leave {
  opacity: 1;
  transform: translateY(0);
}

/* 单行数据集布局 - 允许特效溢出 */
.datasets-single-row {
  width: 100%;
  overflow: visible; /* 允许卡片特效溢出 */
  padding: 10px 0; /* 为特效留出空间 */
}

.card-container-single-row {
  display: flex;
  flex-wrap: nowrap;
  gap: 20px;
  justify-content: center;
  align-items: stretch;
  max-width: 1200px;
  margin: 0 auto;
  overflow: visible; /* 确保容器也允许溢出 */
}

.data-card-wrapper {
  flex: 1;
  max-width: 320px;
  min-width: 280px;
  position: relative; /* 为特效定位 */
  z-index: 1; /* 确保在正确层级 */
}

/* 数据卡片错位动画 */
.card-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.card-stagger-enter-active, .card-stagger-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-stagger-enter, .card-stagger-leave-to {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}

.card-stagger-enter-to, .card-stagger-leave {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 字段选择区域滑入动画 - 优化流畅度 */
.field-selection-slide-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1); /* 缩短动画时长，使用更流畅的曲线 */
}

.field-selection-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 缩短离开动画时长 */
}

.field-selection-slide-enter {
  opacity: 0;
  transform: translateY(-60px) scale(0.85); /* 加大初始位移和缩放 */
}

.field-selection-slide-leave-to {
  opacity: 0;
  transform: translateY(-30px) scale(0.9);
}

.field-selection-slide-enter-to, .field-selection-slide-leave {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 字段行淡入动画 - 优化流畅度 */
.field-row-fade-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1); /* 缩短动画时间 */
}

.field-row-fade-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.field-row-fade-enter-to {
  opacity: 1;
  transform: translateX(0);
}

/* 字段标签容器 */
.field-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px; /* 增大间距: 4px * 1.5 = 6px */
  align-items: center;
  justify-content: flex-start; /* 标签左对齐 */
  width: 100%;
}

/* 字段标签错位动画 - 优化流畅度 */
.field-tag-stagger-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1); /* 缩短时间，使用流畅曲线 */
}

.field-tag-stagger-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); /* 缩短离开动画时间 */
}

.field-tag-stagger-enter {
  opacity: 0;
  transform: translateY(-20px) scale(0.7); /* 加大位移和缩放 */
}

.field-tag-stagger-leave-to {
  opacity: 0;
  transform: translateY(15px) scale(0.8);
}

.field-tag-stagger-enter-to, .field-tag-stagger-leave {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 新增标签脉冲动画 */
@keyframes tag-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);
  }
}

/* 提示文字淡入淡出动画 */
.hint-fade-enter-active, .hint-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.hint-fade-enter, .hint-fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

.hint-fade-enter-to, .hint-fade-leave {
  opacity: 1;
  transform: translateY(0);
}

/* 增强字段选择区域的入场特效 */
.field-selection-mini {
  animation: fieldSelectionGlow 2s ease-out;
}

/* 新增消息列表的高亮动画 */
@keyframes list-highlight {
  0% {
    background-color: rgba(64, 158, 255, 0.05);
    box-shadow: 0 0 20px rgba(64, 158, 255, 0.1);
  }
  70% {
    background-color: rgba(64, 158, 255, 0.02);
    box-shadow: 0 0 15px rgba(64, 158, 255, 0.05);
  }
  100% {
    background-color: transparent;
    box-shadow: none;
  }
}

@keyframes fieldSelectionGlow {
  0% {
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  }
  50% {
    box-shadow: 0 -4px 20px rgba(64, 158, 255, 0.2), 0 -2px 8px rgba(0, 0, 0, 0.1);
  }
  100% {
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  }
}

/* 重复样式已合并到上面的 .message-list 中 */

.field-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.dataset-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dataset-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.dataset-name {
  background: #409eff;
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
}

.switch-btn {
  color: #409eff;
  padding: 4px 8px;
}

.switch-btn:hover {
  background: rgba(64, 158, 255, 0.1);
}

.field-categories {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-category {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-label {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.field-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 紧凑型字段标签样式 */
.field-tag-compact {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  font-weight: 500;
  margin: 0;
}

.field-tag-compact:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 指标字段样式 - 绿色 */
.field-tag-compact.indicator-tag {
  background: #f0f9ff;
  color: #059669;
  border-color: #10b981;
}

.field-tag-compact.indicator-tag:hover {
  background: #ecfdf5;
  border-color: #059669;
}

.field-tag-compact.indicator-tag.selected {
  background: #10b981;
  color: white;
  border-color: #059669;
}

/* 维度字段样式 - 蓝色 */
.field-tag-compact.dimension-tag {
  background: #eff6ff;
  color: #2563eb;
  border-color: #3b82f6;
}

.field-tag-compact.dimension-tag:hover {
  background: #dbeafe;
  border-color: #2563eb;
}

.field-tag-compact.dimension-tag.selected {
  background: #3b82f6;
  color: white;
  border-color: #2563eb;
}

/* 超紧凑字段标签样式 - 增大字体和尺寸 */
.field-tag-mini {
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1); /* 增强过渡效果 */
  border: 1px solid transparent;
  font-weight: 500;
  margin: 0;
  font-size: 13px !important; /* 增大字体: 11px * 1.18 ≈ 13px */
  height: 28px !important; /* 稍微增加高度以适应更好的视觉效果 */
  line-height: 26px !important; /* 对应调整行高 */
  padding: 0 10px !important; /* 稍微增加内边距 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px; /* 增加最大宽度，让字段名称显示更完整 */
  flex-shrink: 1;
  position: relative;
  z-index: 1;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.field-tag-mini::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0) 0%, 
    rgba(255, 255, 255, 0.4) 50%, 
    rgba(255, 255, 255, 0) 100%);
  transition: all 0.8s ease;
  z-index: -1;
}

.field-tag-mini:hover {
  transform: translateY(-2px) scale(1.05); /* 增大悬停效果 */
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.field-tag-mini:hover::after {
  left: 100%; /* 光效扫过 */
}

/* 超紧凑指标字段样式 - 绿色 */
.field-tag-mini.indicator-tag {
  background: linear-gradient(145deg, #f0f9ff, #ecfdf5);
  color: #059669;
  border-color: #10b981;
  animation: indicator-appear 0.8s ease-out;
}

.field-tag-mini.indicator-tag:hover {
  background: linear-gradient(145deg, #ecfdf5, #d1fae5);
  border-color: #059669;
  animation: indicator-glow 1.5s infinite alternate;
}

.field-tag-mini.indicator-tag.selected {
  background: linear-gradient(145deg, #10b981, #059669);
  color: white;
  border-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* 超紧凑维度字段样式 - 蓝色 */
.field-tag-mini.dimension-tag {
  background: linear-gradient(145deg, #eff6ff, #dbeafe);
  color: #2563eb;
  border-color: #3b82f6;
  animation: dimension-appear 0.8s ease-out;
}

.field-tag-mini.dimension-tag:hover {
  background: linear-gradient(145deg, #dbeafe, #bfdbfe);
  border-color: #2563eb;
  animation: dimension-glow 1.5s infinite alternate;
}

.field-tag-mini.dimension-tag.selected {
  background: linear-gradient(145deg, #3b82f6, #2563eb);
  color: white;
  border-color: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 新增指标和维度的出现动画 */
@keyframes indicator-appear {
  0% {
    opacity: 0;
    transform: translateY(-15px) scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: translateY(4px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dimension-appear {
  0% {
    opacity: 0;
    transform: translateY(-15px) scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: translateY(4px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes indicator-glow {
  0% {
    box-shadow: 0 0 2px rgba(16, 185, 129, 0.4);
  }
  100% {
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.6), 0 0 12px rgba(16, 185, 129, 0.2);
  }
}

@keyframes dimension-glow {
  0% {
    box-shadow: 0 0 2px rgba(59, 130, 246, 0.4);
  }
  100% {
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.6), 0 0 12px rgba(59, 130, 246, 0.2);
  }
}

/* 头像悬停效果 */
.message:hover .bot-avatar {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15); /* 调整为适合白底的阴影 */
}

/* 用户头像悬停效果已移除 */

.bot-avatar {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.key-metrics, .key-dimensions {
  width: 100%;
  max-width: 650px; /* 统一组件宽度 */
  margin: 0 auto;
}

/* 优化图表在消息流中的显示 */
.chart-container {
  border-radius: 8px;
  margin-top: 12px;
  margin-bottom: 12px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  background-color: #fff;
  position: relative;
  display: block;
  clear: both;
}

/* 修复消息中图表的层级问题 */
.message .chart-container {
  position: relative;
  z-index: 10;
  transform: translateZ(0);
  will-change: transform;
  overflow: visible !important;
}

/* 应用程序其他样式... */

/* 自定义Drawer过渡动画持续时间 - 优化流畅度 */
.el-drawer__container {
  --el-transition-duration: 0.3s !important;  /* 缩短过渡动画时间 */
  --el-transition-all: var(--el-transition-function-ease-in-out-bezier) var(--el-transition-duration);
}

.el-drawer__body {
  transition: opacity 0.2s ease 0.1s; /* 缩短透明度过渡时间 */
}

.el-drawer.rtl {
  animation-duration: 0.3s !important; /* 缩短右侧抽屉滑入动画时间 */
  animation-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1) !important; /* 使用更流畅的缓动函数 */
}

/* 对话框强调动画 */
.question-input-wrapper {
  animation: subtle-pulse 2s infinite alternate;
}

@keyframes subtle-pulse {
  0% {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.025), 0 1px 2px rgba(0, 0, 0, 0.015);
  }
  100% {
    box-shadow: 0 4px 15px rgba(64, 158, 255, 0.15), 0 2px 8px rgba(64, 158, 255, 0.1);
  }
}

/* 图表切换相关样式 */
.chart-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  margin-top: 15px;
}

.chart-switcher-dropdown {
  position: relative;
  display: inline-block;
  margin-right: 10px;
}

.chart-dropdown-wrapper {
  position: relative;
  display: inline-block;
}

.chart-current-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  height: 28px;
  padding: 0 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #409eff;
  color: #fff;
  font-size: 12px;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s ease;
  box-sizing: border-box;
  min-width: 80px;
}

.chart-current-btn:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

.chart-dropdown-wrapper.open .chart-current-btn {
  background: #66b1ff;
  border-color: #66b1ff;
}

.chart-type-text {
  font-size: 12px;
  color: inherit;
}

.dropdown-arrow {
  font-size: 10px;
  color: inherit;
  transition: transform 0.3s ease;
}

.chart-dropdown-wrapper.open .dropdown-arrow {
  transform: rotate(180deg);
}

.chart-dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
  padding: 8px;
  min-width: 120px;
}

.chart-type-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.chart-type-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-type-btn:hover {
  border-color: #409eff;
  background: #ecf5ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.chart-type-btn.active {
  border-color: #409eff;
  background: #409eff;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.chart-type-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.chart-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.switching-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.switching-loader i {
  font-size: 12px;
  animation: rotating 1s linear infinite;
}

@keyframes rotating {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 自定义图表图标样式 */
.chart-icon-bar::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background: currentColor;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M7 17h2v-7H7v7zm4 0h2V7h-2v10zm4 0h2v-4h-2v4z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.chart-icon-line::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background: currentColor;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.chart-icon-pie::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background: currentColor;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M11 2v20c-5.07-.5-9-4.79-9-10s3.93-9.5 9-10zm2.03 0v8.99H22c-.47-4.74-4.24-8.52-8.97-8.99zm0 11.01V22c4.74-.47 8.5-4.25 8.97-8.99h-8.97z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

.chart-icon-bar-horizontal::before {
  content: '';
  display: inline-block;
  width: 24px;
  height: 24px;
  background: currentColor;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z' transform='rotate(90 12 12)'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* 导出PDF按钮样式 - 与图表切换按钮保持一致 */
.export-pdf-btn {
  background: #fff !important;
  border-color: #dcdfe6 !important;
  color: #606266 !important;
  height: 28px !important;
  padding: 0 12px !important;
  font-size: 12px !important;
  border-radius: 4px !important;
  min-width: 80px !important;
  box-sizing: border-box !important;
}

.export-pdf-btn:hover {
  background: #ecf5ff !important;
  border-color: #409eff !important;
  color: #409eff !important;
}

.export-pdf-btn:focus {
  background: #ecf5ff !important;
  border-color: #409eff !important;
  color: #409eff !important;
}

</style>