{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport axios from 'axios';\n// 假设 chart-display 组件已正确引入\nimport ChartDisplay from './ChartDisplay.vue'; // 请根据实际路径调整\n// 假设 dataApi 已正确引入，或者我们直接使用 axios\nimport dataApi from '../api/dataApi'; // 请根据实际路径调整\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      recentChats: [{\n        title: '分析销售额趋势',\n        time: '10:30'\n      }, {\n        title: '各区域利润对比',\n        time: '昨天'\n      }],\n      tables: [],\n      // 初始展示的可用数据表\n      questionInput: '',\n      messages: [],\n      showAIResponse: false,\n      chatWidth: '80%',\n      // 确保聊天区域宽度和数据集展示区域宽度一致\n\n      // 新增数据属性用于抽屉功能\n      drawerVisible: false,\n      allDatasets: [],\n      // 存储所有扁平化数据集列表\n      filteredDatasets: [],\n      // 存储过滤后的数据集列表\n      searchDatasetInput: '',\n      // 搜索输入\n      selectedDataset: null,\n      // 选中的数据集概览\n      selectedDatasetDetails: null,\n      // 选中的数据集的详细信息\n      showDatasetDetail: false // 控制抽屉内显示列表还是详情\n    };\n  },\n  mounted() {\n    this.getAllTables();\n    this.fetchAllDatasetsForDrawer(); // 在组件加载时就获取所有数据集，以便抽屉使用\n  },\n  methods: {\n    // 获取初始可用的数据表\n    async getAllTables() {\n      try {\n        const response = await dataApi.getAllTables(); // 假设这个API返回的是用于初始展示的表格列表\n        if (response.code === 0 && response.data) {\n          this.tables = response.data;\n          // 对于初始展示的表格，可能也需要填充一些模拟的description\n          this.tables.forEach(table => {\n            if (!table.description) {\n              table.description = `这是关于 ${table.tableName} 的描述。`;\n            }\n          });\n        } else {\n          console.error('获取初始数据表失败:', response.msg);\n        }\n      } catch (error) {\n        console.error('调用 getAllTables API 出错:', error);\n      }\n    },\n    // 新增方法：获取所有数据集（扁平化）用于抽屉\n    async fetchAllDatasetsForDrawer() {\n      try {\n        const response = await dataApi.getAllTables(); // 假设这个API也能获取所有数据集，可能需要处理成扁平结构\n        if (response.code === 0 && response.data) {\n          // 这里需要一个扁平化逻辑，因为 tree结构的请求与响应.txt 是树形结构\n          // 假设 dataApi.getAllTables() 实际上返回的是类似 tree结构的请求与响应.txt 的树形结构\n          this.allDatasets = this.flattenTreeData(response.data);\n          this.filteredDatasets = this.allDatasets; // 初始化过滤列表\n        } else {\n          console.error('获取所有数据集失败:', response.msg);\n        }\n      } catch (error) {\n        console.error('调用 fetchAllDatasetsForDrawer API 出错:', error);\n      }\n    },\n    // 辅助方法：扁平化树形结构数据\n    // 假设 datasetTree 结构类似 tree结构的请求与响应.txt 中的 data 数组\n    flattenTreeData(datasetTree) {\n      const flatList = [];\n      const traverse = nodes => {\n        nodes.forEach(node => {\n          // 只有当节点是叶子节点或者明确是一个可选择的数据集时才加入\n          // 根据需求，用户希望直接展示具体的数据集，所以只要是可点击的都加入\n          // 这里我们简单地将所有非root节点加入，如果只需要叶子节点，可以添加 node.leaf === true\n          if (node.id !== \"0\") {\n            // 排除根节点\n            flatList.push({\n              id: node.id,\n              name: node.name\n              // 可以添加其他需要展示的属性\n            });\n          }\n          if (node.children && node.children.length > 0) {\n            traverse(node.children);\n          }\n        });\n      };\n      if (datasetTree && datasetTree.length > 0) {\n        // tree结构的请求与响应.txt 的根节点是 data[0] 的 children\n        if (datasetTree[0] && datasetTree[0].children) {\n          traverse(datasetTree[0].children);\n        }\n      }\n      return flatList;\n    },\n    // 点击初始展示的数据集卡片\n    async handleInitialDatasetCardClick(dataset) {\n      // 可以在这里直接打开抽屉并显示该数据集的详情\n      this.drawerVisible = true;\n      this.showDatasetDetail = false; // 先切换到列表再加载详情\n      this.selectedDatasetDetails = null; // 清空旧详情\n      // 找到对应的扁平化数据集对象，然后调用详情加载\n      const foundDataset = this.allDatasets.find(d => d.id === dataset.data.id);\n      if (foundDataset) {\n        await this.fetchDatasetDetails(foundDataset.id);\n      } else {\n        console.warn('初始点击的数据集在全部列表中未找到:', dataset);\n        // Fallback: If not found in allDatasets, try fetching directly\n        try {\n          const response = await dataApi.getDatasetDetails(dataset.data.id);\n          if (response.code === 0 && response.data) {\n            this.selectedDatasetDetails = response.data;\n            this.showDatasetDetail = true;\n          }\n        } catch (error) {\n          console.error('直接获取数据集详情失败:', error);\n        }\n      }\n    },\n    // 点击“选择数据”按钮\n    selectData() {\n      this.drawerVisible = true;\n      this.showDatasetDetail = false; // 每次打开抽屉都先显示列表\n      this.selectedDatasetDetails = null; // 清空上一次的详情\n      this.searchDatasetInput = ''; // 清空搜索框\n      this.filteredDatasets = this.allDatasets; // 重置过滤\n      // 确保 allDatasets 已经加载\n      if (this.allDatasets.length === 0) {\n        this.fetchAllDatasetsForDrawer();\n      }\n    },\n    // 过滤数据集列表\n    filterDatasets() {\n      const query = this.searchDatasetInput.toLowerCase();\n      this.filteredDatasets = this.allDatasets.filter(dataset => dataset.name.toLowerCase().includes(query) || dataset.id.includes(query));\n    },\n    // 点击抽屉中的数据集卡片\n    async handleDatasetClick(dataset) {\n      this.selectedDataset = dataset;\n      await this.fetchDatasetDetails(dataset.id);\n    },\n    // 获取特定数据集的详细信息\n    async fetchDatasetDetails(datasetId) {\n      try {\n        // 假设 dataApi 有一个 getDatasetDetails 方法，返回类似 数据集具体响应结果.txt 的数据\n        const response = await dataApi.getDatasetDetails(datasetId);\n        if (response.code === 0 && response.data) {\n          this.selectedDatasetDetails = response.data;\n          this.showDatasetDetail = true;\n        } else {\n          console.error(`获取数据集 ${datasetId} 详情失败:`, response.msg);\n          this.selectedDatasetDetails = null;\n          this.showDatasetDetail = false;\n          this.$message.error(`获取数据集 ${datasetId} 详情失败: ${response.msg}`);\n        }\n      } catch (error) {\n        console.error(`调用 getDatasetDetails API 出错 (ID: ${datasetId}):`, error);\n        this.selectedDatasetDetails = null;\n        this.showDatasetDetail = false;\n        this.$message.error('获取数据集详情时发生错误，请稍后再试。');\n      }\n    },\n    // 从详情返回列表\n    goBackToDatasetList() {\n      this.showDatasetDetail = false;\n      this.selectedDatasetDetails = null;\n    },\n    // 提交问题到AI\n    async submitQuestion() {\n      if (!this.questionInput.trim()) return;\n      const userMessage = {\n        sender: 'user',\n        type: 'text',\n        text: this.questionInput\n      };\n      this.messages.push(userMessage);\n      this.questionInput = ''; // 清空输入框\n\n      let botMessage = {\n        sender: 'bot',\n        type: 'text',\n        text: '',\n        chartConfig: null\n      };\n      this.messages.push(botMessage); // 预留一个位置给AI的回复\n\n      try {\n        const response = await axios.post('/api/indicator/chat', {\n          question: userMessage.text\n        }, {\n          responseType: 'stream',\n          // 启用流式响应\n          onDownloadProgress: progressEvent => {\n            const rawResponse = progressEvent.currentTarget.responseText;\n            // 假设AI的原始响应可能包含JSON数据，我们尝试解析\n            try {\n              // 尝试从流中提取完整的JSON对象\n              const matches = rawResponse.match(/```json\\s*(\\{[\\s\\S]*?\\})\\s*```/);\n              if (matches && matches[1]) {\n                const jsonStr = matches[1];\n                const chartConfig = JSON.parse(jsonStr);\n                // 如果有 chart_data_id，则需要去获取数据\n                if (chartConfig.chart_data_id) {\n                  // 可以在这里再次调用 getData API 获取图表数据\n                  this.getChartData({\n                    chartDataId: chartConfig.chart_data_id\n                  }).then(chartResponse => {\n                    if (chartResponse && chartResponse.code === 0 && chartResponse.data) {\n                      botMessage.type = 'chart';\n                      botMessage.chartConfig = chartResponse.data;\n                    } else {\n                      botMessage.type = 'text';\n                      botMessage.text = 'AI生成了图表数据ID但获取失败：' + chartResponse.msg;\n                    }\n                    // 更新消息以触发UI更新\n                    this.$set(this.messages, this.messages.length - 1, botMessage);\n                  }).catch(e => {\n                    console.error('获取图表数据失败:', e);\n                    botMessage.type = 'text';\n                    botMessage.text = 'AI生成了图表数据ID但获取失败。';\n                    this.$set(this.messages, this.messages.length - 1, botMessage);\n                  });\n                } else if (chartConfig.chart_type && chartConfig.data) {\n                  // 如果直接返回了完整的图表配置\n                  botMessage.type = 'chart';\n                  botMessage.chartConfig = chartConfig;\n                }\n              } else {\n                // 否则，认为是纯文本回复\n                botMessage.type = 'text';\n                botMessage.text = rawResponse;\n              }\n            } catch (parseError) {\n              // 如果解析失败，说明不是完整的JSON，按文本处理\n              botMessage.type = 'text';\n              botMessage.text = rawResponse;\n            }\n            this.$set(this.messages, this.messages.length - 1, botMessage); // 更新UI\n          }\n        });\n\n        // 确保最终消息内容正确\n        if (botMessage.text === '' && !botMessage.chartConfig) {\n          botMessage.text = 'AI没有给出明确回复或回复格式无法识别。';\n          this.$set(this.messages, this.messages.length - 1, botMessage);\n        }\n      } catch (error) {\n        console.error('提交问题出错:', error);\n        botMessage.type = 'text';\n        botMessage.text = 'AI服务请求失败，请稍后再试。';\n        this.$set(this.messages, this.messages.length - 1, botMessage);\n      }\n    },\n    // 获取图表数据（可能通过chartDataId或直接的数据对象）\n    async getChartData(data) {\n      if (!data) {\n        console.error('getData API: 没有传入数据');\n        return {\n          code: -1,\n          msg: '没有传入数据',\n          data: null\n        };\n      }\n      try {\n        // 如果是直接传入的完整数据对象（从AI响应中提取的）\n        if (data.code === 0 && data.data) {\n          console.log('检测到完整API响应格式，直接返回');\n          return data;\n        }\n\n        // 如果包含chartDataId字段，使用专门的API获取数据\n        if (data.chartDataId) {\n          console.log('检测到chartDataId，从Redis获取数据:', data.chartDataId);\n          const response = await dataApi.getChartDataById(data.chartDataId);\n          console.log('Redis数据获取结果:', response);\n          return response.data;\n        }\n\n        // 正常API调用\n        console.log('发起API请求:', data);\n        const response = await axios.post('/api/chartData/getData-1', data);\n        console.log('getData API响应结果:', response);\n\n        // 检查响应格式\n        if (response.data) {\n          console.log('API返回数据:', response.data);\n          return response.data;\n        } else {\n          console.error('响应数据为空');\n          return {\n            code: -1,\n            msg: '响应数据为空',\n            data: null\n          };\n        }\n      } catch (error) {\n        console.error('getData API调用出错:', error);\n        return {\n          code: -1,\n          msg: error.message,\n          data: null\n        };\n      }\n    },\n    exportToPdf() {\n      // 导出PDF逻辑\n      alert('导出PDF功能待实现');\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "ChartDisplay", "dataApi", "name", "components", "data", "recentChats", "title", "time", "tables", "questionInput", "messages", "showAIResponse", "chat<PERSON><PERSON><PERSON>", "drawerVisible", "allDatasets", "filteredDatasets", "searchDatasetInput", "selectedDataset", "selectedDatasetDetails", "showDatasetDetail", "mounted", "getAllTables", "fetchAllDatasetsForDrawer", "methods", "response", "code", "for<PERSON>ach", "table", "description", "tableName", "console", "error", "msg", "flattenTreeData", "datasetTree", "flatList", "traverse", "nodes", "node", "id", "push", "children", "length", "handleInitialDatasetCardClick", "dataset", "<PERSON><PERSON><PERSON><PERSON>", "find", "d", "fetchDatasetDetails", "warn", "getDatasetDetails", "selectData", "filterDatasets", "query", "toLowerCase", "filter", "includes", "handleDatasetClick", "datasetId", "$message", "goBackToDatasetList", "submitQuestion", "trim", "userMessage", "sender", "type", "text", "botMessage", "chartConfig", "post", "question", "responseType", "onDownloadProgress", "progressEvent", "rawResponse", "currentTarget", "responseText", "matches", "match", "jsonStr", "JSON", "parse", "chart_data_id", "getChartData", "chartDataId", "then", "chartResponse", "$set", "catch", "e", "chart_type", "parseError", "log", "getChartDataById", "message", "exportToPdf", "alert"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"main-content\">\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n        </div>\n\n        <div class=\"data-selection\">\n          <h3>目前可用数据</h3>\n          <div class=\"data-sets\" :style=\"{ width: chatWidth }\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\" v-for=\"table in tables\" :key=\"table.data.id\">\n                <el-card class=\"data-card\" @click=\"handleInitialDatasetCardClick(table)\">\n                  <div class=\"data-header\">\n                    <div class=\"data-icon\">\n                      <i class=\"el-icon-folder\"></i>\n                    </div>\n                    <div class=\"data-info\">\n                      <div class=\"table-name\">{{ table.tableName }}</div>\n                      <div class=\"table-desc\">{{ table.description }}</div>\n                    </div>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n          </div>\n          <el-button type=\"primary\" @click=\"selectData\">选择数据</el-button>\n        </div>\n\n        <div class=\"chat-interface\">\n          <div class=\"messages\">\n            <div\n              class=\"message-item\"\n              :class=\"message.sender\"\n              v-for=\"(message, index) in messages\"\n              :key=\"index\"\n            >\n              <div class=\"message-avatar\" v-if=\"message.sender === 'bot'\">AI</div>\n              <div class=\"message-content\">\n                <p v-if=\"message.type === 'text'\">{{ message.text }}</p>\n                <div v-if=\"message.type === 'chart' && message.chartConfig\">\n                  <chart-display :chartConfig=\"message.chartConfig\"></chart-display>\n                  <el-button type=\"primary\" size=\"small\" @click=\"exportToPdf\"\n                    >导出PDF</el-button\n                  >\n                </div>\n              </div>\n              <div class=\"message-avatar\" v-if=\"message.sender === 'user'\">You</div>\n            </div>\n          </div>\n          <div class=\"input-area\">\n            <el-input\n              placeholder=\"请输入您的问题...\"\n              v-model=\"questionInput\"\n              @keyup.enter.native=\"submitQuestion\"\n            ></el-input>\n            <el-button type=\"primary\" @click=\"submitQuestion\">发送</el-button>\n            <el-button @click=\"showAIResponse = !showAIResponse\"\n              >显示AI原始响应</el-button\n            >\n          </div>\n        </div>\n      </div>\n\n      <el-drawer\n        title=\"选择数据集\"\n        :visible.sync=\"drawerVisible\"\n        direction=\"rtl\"\n        size=\"50%\"\n      >\n        <div class=\"drawer-content\">\n          <div v-if=\"!showDatasetDetail\">\n            <el-input\n              placeholder=\"搜索数据集...\"\n              v-model=\"searchDatasetInput\"\n              clearable\n              @input=\"filterDatasets\"\n            ></el-input>\n            <div class=\"dataset-list\">\n              <el-card\n                class=\"data-card\"\n                v-for=\"dataset in filteredDatasets\"\n                :key=\"dataset.id\"\n                @click=\"handleDatasetClick(dataset)\"\n              >\n                <div class=\"data-info\">\n                  <div class=\"table-name\">{{ dataset.name }}</div>\n                  <div class=\"table-desc\">ID: {{ dataset.id }}</div>\n                </div>\n              </el-card>\n              <p v-if=\"filteredDatasets.length === 0 && searchDatasetInput\">\n                没有找到匹配的数据集。\n              </p>\n            </div>\n          </div>\n          <div v-else class=\"dataset-detail\">\n            <el-button icon=\"el-icon-back\" @click=\"goBackToDatasetList\"\n              >返回数据集列表</el-button\n            >\n            <h3>{{ selectedDatasetDetails.name }} 详情</h3>\n            <div v-if=\"selectedDatasetDetails.info\">\n              <p>\n                **数据源表名:**\n                {{ JSON.parse(selectedDatasetDetails.info).currentDs.tableName }}\n              </p>\n              <h4>字段详情:</h4>\n              <el-table\n                :data=\"JSON.parse(selectedDatasetDetails.info).currentDsFields\"\n                border\n                style=\"width: 100%\"\n              >\n                <el-table-column prop=\"name\" label=\"字段名称\"></el-table-column>\n                <el-table-column prop=\"originName\" label=\"原始名称\"></el-table-column>\n                <el-table-column prop=\"type\" label=\"数据类型\"></el-table-column>\n                <el-table-column prop=\"groupType\" label=\"分组类型\"></el-table-column>\n              </el-table>\n            </div>\n            <p v-else>未能加载详细字段信息。</p>\n          </div>\n        </div>\n      </el-drawer>\n    </div>\n  </div>\n</template>\n\n<script>\nimport axios from 'axios';\n// 假设 chart-display 组件已正确引入\nimport ChartDisplay from './ChartDisplay.vue'; // 请根据实际路径调整\n// 假设 dataApi 已正确引入，或者我们直接使用 axios\nimport dataApi from '../api/dataApi'; // 请根据实际路径调整\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay,\n  },\n  data() {\n    return {\n      recentChats: [\n        { title: '分析销售额趋势', time: '10:30' },\n        { title: '各区域利润对比', time: '昨天' },\n      ],\n      tables: [], // 初始展示的可用数据表\n      questionInput: '',\n      messages: [],\n      showAIResponse: false,\n      chatWidth: '80%', // 确保聊天区域宽度和数据集展示区域宽度一致\n\n      // 新增数据属性用于抽屉功能\n      drawerVisible: false,\n      allDatasets: [], // 存储所有扁平化数据集列表\n      filteredDatasets: [], // 存储过滤后的数据集列表\n      searchDatasetInput: '', // 搜索输入\n      selectedDataset: null, // 选中的数据集概览\n      selectedDatasetDetails: null, // 选中的数据集的详细信息\n      showDatasetDetail: false, // 控制抽屉内显示列表还是详情\n    };\n  },\n  mounted() {\n    this.getAllTables();\n    this.fetchAllDatasetsForDrawer(); // 在组件加载时就获取所有数据集，以便抽屉使用\n  },\n  methods: {\n    // 获取初始可用的数据表\n    async getAllTables() {\n      try {\n        const response = await dataApi.getAllTables(); // 假设这个API返回的是用于初始展示的表格列表\n        if (response.code === 0 && response.data) {\n          this.tables = response.data;\n          // 对于初始展示的表格，可能也需要填充一些模拟的description\n          this.tables.forEach(table => {\n            if (!table.description) {\n              table.description = `这是关于 ${table.tableName} 的描述。`;\n            }\n          });\n        } else {\n          console.error('获取初始数据表失败:', response.msg);\n        }\n      } catch (error) {\n        console.error('调用 getAllTables API 出错:', error);\n      }\n    },\n\n    // 新增方法：获取所有数据集（扁平化）用于抽屉\n    async fetchAllDatasetsForDrawer() {\n      try {\n        const response = await dataApi.getAllTables(); // 假设这个API也能获取所有数据集，可能需要处理成扁平结构\n        if (response.code === 0 && response.data) {\n          // 这里需要一个扁平化逻辑，因为 tree结构的请求与响应.txt 是树形结构\n          // 假设 dataApi.getAllTables() 实际上返回的是类似 tree结构的请求与响应.txt 的树形结构\n          this.allDatasets = this.flattenTreeData(response.data);\n          this.filteredDatasets = this.allDatasets; // 初始化过滤列表\n        } else {\n          console.error('获取所有数据集失败:', response.msg);\n        }\n      } catch (error) {\n        console.error('调用 fetchAllDatasetsForDrawer API 出错:', error);\n      }\n    },\n\n    // 辅助方法：扁平化树形结构数据\n    // 假设 datasetTree 结构类似 tree结构的请求与响应.txt 中的 data 数组\n    flattenTreeData(datasetTree) {\n      const flatList = [];\n      const traverse = (nodes) => {\n        nodes.forEach(node => {\n          // 只有当节点是叶子节点或者明确是一个可选择的数据集时才加入\n          // 根据需求，用户希望直接展示具体的数据集，所以只要是可点击的都加入\n          // 这里我们简单地将所有非root节点加入，如果只需要叶子节点，可以添加 node.leaf === true\n          if (node.id !== \"0\") { // 排除根节点\n            flatList.push({\n              id: node.id,\n              name: node.name,\n              // 可以添加其他需要展示的属性\n            });\n          }\n          if (node.children && node.children.length > 0) {\n            traverse(node.children);\n          }\n        });\n      };\n      if (datasetTree && datasetTree.length > 0) {\n        // tree结构的请求与响应.txt 的根节点是 data[0] 的 children\n        if (datasetTree[0] && datasetTree[0].children) {\n          traverse(datasetTree[0].children);\n        }\n      }\n      return flatList;\n    },\n\n    // 点击初始展示的数据集卡片\n    async handleInitialDatasetCardClick(dataset) {\n      // 可以在这里直接打开抽屉并显示该数据集的详情\n      this.drawerVisible = true;\n      this.showDatasetDetail = false; // 先切换到列表再加载详情\n      this.selectedDatasetDetails = null; // 清空旧详情\n      // 找到对应的扁平化数据集对象，然后调用详情加载\n      const foundDataset = this.allDatasets.find(d => d.id === dataset.data.id);\n      if (foundDataset) {\n        await this.fetchDatasetDetails(foundDataset.id);\n      } else {\n        console.warn('初始点击的数据集在全部列表中未找到:', dataset);\n        // Fallback: If not found in allDatasets, try fetching directly\n        try {\n          const response = await dataApi.getDatasetDetails(dataset.data.id);\n          if (response.code === 0 && response.data) {\n            this.selectedDatasetDetails = response.data;\n            this.showDatasetDetail = true;\n          }\n        } catch (error) {\n          console.error('直接获取数据集详情失败:', error);\n        }\n      }\n    },\n\n    // 点击“选择数据”按钮\n    selectData() {\n      this.drawerVisible = true;\n      this.showDatasetDetail = false; // 每次打开抽屉都先显示列表\n      this.selectedDatasetDetails = null; // 清空上一次的详情\n      this.searchDatasetInput = ''; // 清空搜索框\n      this.filteredDatasets = this.allDatasets; // 重置过滤\n      // 确保 allDatasets 已经加载\n      if (this.allDatasets.length === 0) {\n        this.fetchAllDatasetsForDrawer();\n      }\n    },\n\n    // 过滤数据集列表\n    filterDatasets() {\n      const query = this.searchDatasetInput.toLowerCase();\n      this.filteredDatasets = this.allDatasets.filter(dataset =>\n        dataset.name.toLowerCase().includes(query) || dataset.id.includes(query)\n      );\n    },\n\n    // 点击抽屉中的数据集卡片\n    async handleDatasetClick(dataset) {\n      this.selectedDataset = dataset;\n      await this.fetchDatasetDetails(dataset.id);\n    },\n\n    // 获取特定数据集的详细信息\n    async fetchDatasetDetails(datasetId) {\n      try {\n        // 假设 dataApi 有一个 getDatasetDetails 方法，返回类似 数据集具体响应结果.txt 的数据\n        const response = await dataApi.getDatasetDetails(datasetId);\n        if (response.code === 0 && response.data) {\n          this.selectedDatasetDetails = response.data;\n          this.showDatasetDetail = true;\n        } else {\n          console.error(`获取数据集 ${datasetId} 详情失败:`, response.msg);\n          this.selectedDatasetDetails = null;\n          this.showDatasetDetail = false;\n          this.$message.error(`获取数据集 ${datasetId} 详情失败: ${response.msg}`);\n        }\n      } catch (error) {\n        console.error(`调用 getDatasetDetails API 出错 (ID: ${datasetId}):`, error);\n        this.selectedDatasetDetails = null;\n        this.showDatasetDetail = false;\n        this.$message.error('获取数据集详情时发生错误，请稍后再试。');\n      }\n    },\n\n    // 从详情返回列表\n    goBackToDatasetList() {\n      this.showDatasetDetail = false;\n      this.selectedDatasetDetails = null;\n    },\n\n    // 提交问题到AI\n    async submitQuestion() {\n      if (!this.questionInput.trim()) return;\n\n      const userMessage = { sender: 'user', type: 'text', text: this.questionInput };\n      this.messages.push(userMessage);\n      this.questionInput = ''; // 清空输入框\n\n      let botMessage = { sender: 'bot', type: 'text', text: '', chartConfig: null };\n      this.messages.push(botMessage); // 预留一个位置给AI的回复\n\n      try {\n        const response = await axios.post('/api/indicator/chat', {\n          question: userMessage.text,\n        }, {\n          responseType: 'stream', // 启用流式响应\n          onDownloadProgress: (progressEvent) => {\n            const rawResponse = progressEvent.currentTarget.responseText;\n            // 假设AI的原始响应可能包含JSON数据，我们尝试解析\n            try {\n              // 尝试从流中提取完整的JSON对象\n              const matches = rawResponse.match(/```json\\s*(\\{[\\s\\S]*?\\})\\s*```/);\n              if (matches && matches[1]) {\n                const jsonStr = matches[1];\n                const chartConfig = JSON.parse(jsonStr);\n                // 如果有 chart_data_id，则需要去获取数据\n                if (chartConfig.chart_data_id) {\n                  // 可以在这里再次调用 getData API 获取图表数据\n                  this.getChartData({ chartDataId: chartConfig.chart_data_id }).then(\n                    (chartResponse) => {\n                      if (chartResponse && chartResponse.code === 0 && chartResponse.data) {\n                        botMessage.type = 'chart';\n                        botMessage.chartConfig = chartResponse.data;\n                      } else {\n                        botMessage.type = 'text';\n                        botMessage.text = 'AI生成了图表数据ID但获取失败：' + chartResponse.msg;\n                      }\n                      // 更新消息以触发UI更新\n                      this.$set(this.messages, this.messages.length - 1, botMessage);\n                    }\n                  ).catch(e => {\n                    console.error('获取图表数据失败:', e);\n                    botMessage.type = 'text';\n                    botMessage.text = 'AI生成了图表数据ID但获取失败。';\n                    this.$set(this.messages, this.messages.length - 1, botMessage);\n                  });\n                } else if (chartConfig.chart_type && chartConfig.data) {\n                  // 如果直接返回了完整的图表配置\n                  botMessage.type = 'chart';\n                  botMessage.chartConfig = chartConfig;\n                }\n              } else {\n                // 否则，认为是纯文本回复\n                botMessage.type = 'text';\n                botMessage.text = rawResponse;\n              }\n            } catch (parseError) {\n              // 如果解析失败，说明不是完整的JSON，按文本处理\n              botMessage.type = 'text';\n              botMessage.text = rawResponse;\n            }\n            this.$set(this.messages, this.messages.length - 1, botMessage); // 更新UI\n          },\n        });\n\n        // 确保最终消息内容正确\n        if (botMessage.text === '' && !botMessage.chartConfig) {\n          botMessage.text = 'AI没有给出明确回复或回复格式无法识别。';\n          this.$set(this.messages, this.messages.length - 1, botMessage);\n        }\n\n      } catch (error) {\n        console.error('提交问题出错:', error);\n        botMessage.type = 'text';\n        botMessage.text = 'AI服务请求失败，请稍后再试。';\n        this.$set(this.messages, this.messages.length - 1, botMessage);\n      }\n    },\n\n    // 获取图表数据（可能通过chartDataId或直接的数据对象）\n    async getChartData(data) {\n      if (!data) {\n        console.error('getData API: 没有传入数据');\n        return { code: -1, msg: '没有传入数据', data: null };\n      }\n\n      try {\n        // 如果是直接传入的完整数据对象（从AI响应中提取的）\n        if (data.code === 0 && data.data) {\n          console.log('检测到完整API响应格式，直接返回');\n          return data;\n        }\n\n        // 如果包含chartDataId字段，使用专门的API获取数据\n        if (data.chartDataId) {\n          console.log('检测到chartDataId，从Redis获取数据:', data.chartDataId);\n          const response = await dataApi.getChartDataById(data.chartDataId);\n          console.log('Redis数据获取结果:', response);\n          return response.data;\n        }\n\n        // 正常API调用\n        console.log('发起API请求:', data);\n        const response = await axios.post('/api/chartData/getData-1', data);\n        console.log('getData API响应结果:', response);\n\n        // 检查响应格式\n        if (response.data) {\n          console.log('API返回数据:', response.data);\n          return response.data;\n        } else {\n          console.error('响应数据为空');\n          return { code: -1, msg: '响应数据为空', data: null };\n        }\n      } catch (error) {\n        console.error('getData API调用出错:', error);\n        return { code: -1, msg: error.message, data: null };\n      }\n    },\n\n    exportToPdf() {\n      // 导出PDF逻辑\n      alert('导出PDF功能待实现');\n    },\n  },\n};\n</script>\n\n<style scoped>\n/* 保持原有的样式不变 */\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 0px;\n  display: flex;\n  height: 100vh;\n}\n\n.app-layout {\n  display: flex;\n  width: 100%;\n}\n\n.sidebar {\n  width: 250px;\n  background-color: #f4f7f6;\n  padding: 20px;\n  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);\n  display: flex;\n  flex-direction: column;\n}\n\n.logo {\n  text-align: center;\n  margin-bottom: 30px;\n  color: #3498db;\n}\n\n.menu {\n  margin-bottom: 30px;\n}\n\n.menu-item {\n  display: flex;\n  align-items: center;\n  padding: 10px 15px;\n  cursor: pointer;\n  border-radius: 5px;\n  transition: background-color 0.3s;\n}\n\n.menu-item:hover {\n  background-color: #e9eef2;\n}\n\n.menu-item.active {\n  background-color: #3498db;\n  color: white;\n}\n\n.menu-item i {\n  margin-right: 10px;\n}\n\n.recent-chats {\n  flex-grow: 1;\n  overflow-y: auto;\n}\n\n.chat-list {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.chat-item {\n  padding: 10px;\n  background-color: #ffffff;\n  border-radius: 5px;\n  text-align: left;\n  font-size: 0.9em;\n  position: relative;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.chat-time {\n  position: absolute;\n  top: 5px;\n  right: 5px;\n  font-size: 0.7em;\n  color: #999;\n}\n\n.main-content {\n  flex-grow: 1;\n  padding: 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.header {\n  margin-bottom: 30px;\n  text-align: center;\n}\n\n.highlight {\n  color: #3498db;\n}\n\n.sub-title {\n  color: #666;\n  font-size: 0.9em;\n  margin-top: 5px;\n}\n\n.data-selection {\n  width: 100%;\n  max-width: 900px; /* 限制宽度以适应内容 */\n  margin-bottom: 30px;\n  text-align: left;\n}\n\n.data-selection h3 {\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.data-sets {\n  margin-bottom: 20px;\n}\n\n.data-card {\n  margin-bottom: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: calc(100% - 20px); /* 保持与对话宽度一致 */\n}\n\n.data-card:hover {\n  transform: translateY(-5px);\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\n}\n\n.data-header {\n  display: flex;\n  align-items: center;\n}\n\n.data-icon {\n  font-size: 2em;\n  color: #3498db;\n  margin-right: 15px;\n}\n\n.data-info {\n  text-align: left;\n}\n\n.table-name {\n  font-size: 1.1em;\n  font-weight: bold;\n  color: #333;\n}\n\n.table-desc {\n  font-size: 0.85em;\n  color: #777;\n  margin-top: 5px;\n}\n\n.chat-interface {\n  width: 100%;\n  max-width: 900px; /* 保持与数据集展示区域宽度一致 */\n  border: 1px solid #e0e0e0;\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  height: 600px; /* 固定聊天区域高度 */\n}\n\n.messages {\n  flex-grow: 1;\n  padding: 20px;\n  overflow-y: auto;\n  background-color: #f9f9f9;\n}\n\n.message-item {\n  display: flex;\n  margin-bottom: 15px;\n  align-items: flex-start;\n}\n\n.message-item.user {\n  justify-content: flex-end;\n}\n\n.message-item.bot {\n  justify-content: flex-start;\n}\n\n.message-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: #3498db;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n  flex-shrink: 0;\n  margin: 0 10px;\n}\n\n.message-item.user .message-avatar {\n  background-color: #2ecc71;\n}\n\n.message-content {\n  max-width: 70%;\n  padding: 12px 18px;\n  border-radius: 18px;\n  line-height: 1.5;\n  text-align: left;\n  word-wrap: break-word;\n}\n\n.message-item.user .message-content {\n  background-color: #e1ffc7;\n  margin-right: 10px;\n}\n\n.message-item.bot .message-content {\n  background-color: #ffffff;\n  border: 1px solid #e0e0e0;\n  margin-left: 10px;\n}\n\n.input-area {\n  display: flex;\n  padding: 15px;\n  border-top: 1px solid #e0e0e0;\n  background-color: #fff;\n  gap: 10px;\n}\n\n.input-area .el-input {\n  flex-grow: 1;\n}\n\n/* 抽屉样式 */\n.drawer-content {\n  padding: 20px;\n  height: calc(100% - 40px); /* 减去padding */\n  overflow-y: auto;\n}\n\n.drawer-content .dataset-list {\n  margin-top: 15px;\n}\n\n.drawer-content .data-card {\n  margin-bottom: 10px;\n  cursor: pointer;\n}\n\n.drawer-content .data-card:hover {\n  background-color: #f0f8ff;\n}\n\n.drawer-content .dataset-detail {\n  padding: 10px;\n}\n\n.drawer-content .dataset-detail h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  color: #333;\n}\n\n.drawer-content .dataset-detail h4 {\n  margin-top: 20px;\n  margin-bottom: 10px;\n  color: #555;\n}\n</style>"], "mappings": ";;;;;AAoJA,OAAAA,KAAA;AACA;AACA,OAAAC,YAAA;AACA;AACA,OAAAC,OAAA;;AAEA;EACAC,IAAA;EACAC,UAAA;IACAH;EACA;EACAI,KAAA;IACA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,IAAA;MAAA,EACA;MACAC,MAAA;MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,cAAA;MACAC,SAAA;MAAA;;MAEA;MACAC,aAAA;MACAC,WAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,sBAAA;MAAA;MACAC,iBAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,yBAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAF,aAAA;MACA;QACA,MAAAG,QAAA,SAAAvB,OAAA,CAAAoB,YAAA;QACA,IAAAG,QAAA,CAAAC,IAAA,UAAAD,QAAA,CAAApB,IAAA;UACA,KAAAI,MAAA,GAAAgB,QAAA,CAAApB,IAAA;UACA;UACA,KAAAI,MAAA,CAAAkB,OAAA,CAAAC,KAAA;YACA,KAAAA,KAAA,CAAAC,WAAA;cACAD,KAAA,CAAAC,WAAA,WAAAD,KAAA,CAAAE,SAAA;YACA;UACA;QACA;UACAC,OAAA,CAAAC,KAAA,eAAAP,QAAA,CAAAQ,GAAA;QACA;MACA,SAAAD,KAAA;QACAD,OAAA,CAAAC,KAAA,4BAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAT,0BAAA;MACA;QACA,MAAAE,QAAA,SAAAvB,OAAA,CAAAoB,YAAA;QACA,IAAAG,QAAA,CAAAC,IAAA,UAAAD,QAAA,CAAApB,IAAA;UACA;UACA;UACA,KAAAU,WAAA,QAAAmB,eAAA,CAAAT,QAAA,CAAApB,IAAA;UACA,KAAAW,gBAAA,QAAAD,WAAA;QACA;UACAgB,OAAA,CAAAC,KAAA,eAAAP,QAAA,CAAAQ,GAAA;QACA;MACA,SAAAD,KAAA;QACAD,OAAA,CAAAC,KAAA,yCAAAA,KAAA;MACA;IACA;IAEA;IACA;IACAE,gBAAAC,WAAA;MACA,MAAAC,QAAA;MACA,MAAAC,QAAA,GAAAC,KAAA;QACAA,KAAA,CAAAX,OAAA,CAAAY,IAAA;UACA;UACA;UACA;UACA,IAAAA,IAAA,CAAAC,EAAA;YAAA;YACAJ,QAAA,CAAAK,IAAA;cACAD,EAAA,EAAAD,IAAA,CAAAC,EAAA;cACArC,IAAA,EAAAoC,IAAA,CAAApC;cACA;YACA;UACA;UACA,IAAAoC,IAAA,CAAAG,QAAA,IAAAH,IAAA,CAAAG,QAAA,CAAAC,MAAA;YACAN,QAAA,CAAAE,IAAA,CAAAG,QAAA;UACA;QACA;MACA;MACA,IAAAP,WAAA,IAAAA,WAAA,CAAAQ,MAAA;QACA;QACA,IAAAR,WAAA,OAAAA,WAAA,IAAAO,QAAA;UACAL,QAAA,CAAAF,WAAA,IAAAO,QAAA;QACA;MACA;MACA,OAAAN,QAAA;IACA;IAEA;IACA,MAAAQ,8BAAAC,OAAA;MACA;MACA,KAAA/B,aAAA;MACA,KAAAM,iBAAA;MACA,KAAAD,sBAAA;MACA;MACA,MAAA2B,YAAA,QAAA/B,WAAA,CAAAgC,IAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAR,EAAA,KAAAK,OAAA,CAAAxC,IAAA,CAAAmC,EAAA;MACA,IAAAM,YAAA;QACA,WAAAG,mBAAA,CAAAH,YAAA,CAAAN,EAAA;MACA;QACAT,OAAA,CAAAmB,IAAA,uBAAAL,OAAA;QACA;QACA;UACA,MAAApB,QAAA,SAAAvB,OAAA,CAAAiD,iBAAA,CAAAN,OAAA,CAAAxC,IAAA,CAAAmC,EAAA;UACA,IAAAf,QAAA,CAAAC,IAAA,UAAAD,QAAA,CAAApB,IAAA;YACA,KAAAc,sBAAA,GAAAM,QAAA,CAAApB,IAAA;YACA,KAAAe,iBAAA;UACA;QACA,SAAAY,KAAA;UACAD,OAAA,CAAAC,KAAA,iBAAAA,KAAA;QACA;MACA;IACA;IAEA;IACAoB,WAAA;MACA,KAAAtC,aAAA;MACA,KAAAM,iBAAA;MACA,KAAAD,sBAAA;MACA,KAAAF,kBAAA;MACA,KAAAD,gBAAA,QAAAD,WAAA;MACA;MACA,SAAAA,WAAA,CAAA4B,MAAA;QACA,KAAApB,yBAAA;MACA;IACA;IAEA;IACA8B,eAAA;MACA,MAAAC,KAAA,QAAArC,kBAAA,CAAAsC,WAAA;MACA,KAAAvC,gBAAA,QAAAD,WAAA,CAAAyC,MAAA,CAAAX,OAAA,IACAA,OAAA,CAAA1C,IAAA,CAAAoD,WAAA,GAAAE,QAAA,CAAAH,KAAA,KAAAT,OAAA,CAAAL,EAAA,CAAAiB,QAAA,CAAAH,KAAA,CACA;IACA;IAEA;IACA,MAAAI,mBAAAb,OAAA;MACA,KAAA3B,eAAA,GAAA2B,OAAA;MACA,WAAAI,mBAAA,CAAAJ,OAAA,CAAAL,EAAA;IACA;IAEA;IACA,MAAAS,oBAAAU,SAAA;MACA;QACA;QACA,MAAAlC,QAAA,SAAAvB,OAAA,CAAAiD,iBAAA,CAAAQ,SAAA;QACA,IAAAlC,QAAA,CAAAC,IAAA,UAAAD,QAAA,CAAApB,IAAA;UACA,KAAAc,sBAAA,GAAAM,QAAA,CAAApB,IAAA;UACA,KAAAe,iBAAA;QACA;UACAW,OAAA,CAAAC,KAAA,UAAA2B,SAAA,UAAAlC,QAAA,CAAAQ,GAAA;UACA,KAAAd,sBAAA;UACA,KAAAC,iBAAA;UACA,KAAAwC,QAAA,CAAA5B,KAAA,UAAA2B,SAAA,UAAAlC,QAAA,CAAAQ,GAAA;QACA;MACA,SAAAD,KAAA;QACAD,OAAA,CAAAC,KAAA,qCAAA2B,SAAA,MAAA3B,KAAA;QACA,KAAAb,sBAAA;QACA,KAAAC,iBAAA;QACA,KAAAwC,QAAA,CAAA5B,KAAA;MACA;IACA;IAEA;IACA6B,oBAAA;MACA,KAAAzC,iBAAA;MACA,KAAAD,sBAAA;IACA;IAEA;IACA,MAAA2C,eAAA;MACA,UAAApD,aAAA,CAAAqD,IAAA;MAEA,MAAAC,WAAA;QAAAC,MAAA;QAAAC,IAAA;QAAAC,IAAA,OAAAzD;MAAA;MACA,KAAAC,QAAA,CAAA8B,IAAA,CAAAuB,WAAA;MACA,KAAAtD,aAAA;;MAEA,IAAA0D,UAAA;QAAAH,MAAA;QAAAC,IAAA;QAAAC,IAAA;QAAAE,WAAA;MAAA;MACA,KAAA1D,QAAA,CAAA8B,IAAA,CAAA2B,UAAA;;MAEA;QACA,MAAA3C,QAAA,SAAAzB,KAAA,CAAAsE,IAAA;UACAC,QAAA,EAAAP,WAAA,CAAAG;QACA;UACAK,YAAA;UAAA;UACAC,kBAAA,EAAAC,aAAA;YACA,MAAAC,WAAA,GAAAD,aAAA,CAAAE,aAAA,CAAAC,YAAA;YACA;YACA;cACA;cACA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,KAAA;cACA,IAAAD,OAAA,IAAAA,OAAA;gBACA,MAAAE,OAAA,GAAAF,OAAA;gBACA,MAAAT,WAAA,GAAAY,IAAA,CAAAC,KAAA,CAAAF,OAAA;gBACA;gBACA,IAAAX,WAAA,CAAAc,aAAA;kBACA;kBACA,KAAAC,YAAA;oBAAAC,WAAA,EAAAhB,WAAA,CAAAc;kBAAA,GAAAG,IAAA,CACAC,aAAA;oBACA,IAAAA,aAAA,IAAAA,aAAA,CAAA7D,IAAA,UAAA6D,aAAA,CAAAlF,IAAA;sBACA+D,UAAA,CAAAF,IAAA;sBACAE,UAAA,CAAAC,WAAA,GAAAkB,aAAA,CAAAlF,IAAA;oBACA;sBACA+D,UAAA,CAAAF,IAAA;sBACAE,UAAA,CAAAD,IAAA,yBAAAoB,aAAA,CAAAtD,GAAA;oBACA;oBACA;oBACA,KAAAuD,IAAA,MAAA7E,QAAA,OAAAA,QAAA,CAAAgC,MAAA,MAAAyB,UAAA;kBACA,CACA,EAAAqB,KAAA,CAAAC,CAAA;oBACA3D,OAAA,CAAAC,KAAA,cAAA0D,CAAA;oBACAtB,UAAA,CAAAF,IAAA;oBACAE,UAAA,CAAAD,IAAA;oBACA,KAAAqB,IAAA,MAAA7E,QAAA,OAAAA,QAAA,CAAAgC,MAAA,MAAAyB,UAAA;kBACA;gBACA,WAAAC,WAAA,CAAAsB,UAAA,IAAAtB,WAAA,CAAAhE,IAAA;kBACA;kBACA+D,UAAA,CAAAF,IAAA;kBACAE,UAAA,CAAAC,WAAA,GAAAA,WAAA;gBACA;cACA;gBACA;gBACAD,UAAA,CAAAF,IAAA;gBACAE,UAAA,CAAAD,IAAA,GAAAQ,WAAA;cACA;YACA,SAAAiB,UAAA;cACA;cACAxB,UAAA,CAAAF,IAAA;cACAE,UAAA,CAAAD,IAAA,GAAAQ,WAAA;YACA;YACA,KAAAa,IAAA,MAAA7E,QAAA,OAAAA,QAAA,CAAAgC,MAAA,MAAAyB,UAAA;UACA;QACA;;QAEA;QACA,IAAAA,UAAA,CAAAD,IAAA,YAAAC,UAAA,CAAAC,WAAA;UACAD,UAAA,CAAAD,IAAA;UACA,KAAAqB,IAAA,MAAA7E,QAAA,OAAAA,QAAA,CAAAgC,MAAA,MAAAyB,UAAA;QACA;MAEA,SAAApC,KAAA;QACAD,OAAA,CAAAC,KAAA,YAAAA,KAAA;QACAoC,UAAA,CAAAF,IAAA;QACAE,UAAA,CAAAD,IAAA;QACA,KAAAqB,IAAA,MAAA7E,QAAA,OAAAA,QAAA,CAAAgC,MAAA,MAAAyB,UAAA;MACA;IACA;IAEA;IACA,MAAAgB,aAAA/E,IAAA;MACA,KAAAA,IAAA;QACA0B,OAAA,CAAAC,KAAA;QACA;UAAAN,IAAA;UAAAO,GAAA;UAAA5B,IAAA;QAAA;MACA;MAEA;QACA;QACA,IAAAA,IAAA,CAAAqB,IAAA,UAAArB,IAAA,CAAAA,IAAA;UACA0B,OAAA,CAAA8D,GAAA;UACA,OAAAxF,IAAA;QACA;;QAEA;QACA,IAAAA,IAAA,CAAAgF,WAAA;UACAtD,OAAA,CAAA8D,GAAA,+BAAAxF,IAAA,CAAAgF,WAAA;UACA,MAAA5D,QAAA,SAAAvB,OAAA,CAAA4F,gBAAA,CAAAzF,IAAA,CAAAgF,WAAA;UACAtD,OAAA,CAAA8D,GAAA,iBAAApE,QAAA;UACA,OAAAA,QAAA,CAAApB,IAAA;QACA;;QAEA;QACA0B,OAAA,CAAA8D,GAAA,aAAAxF,IAAA;QACA,MAAAoB,QAAA,SAAAzB,KAAA,CAAAsE,IAAA,6BAAAjE,IAAA;QACA0B,OAAA,CAAA8D,GAAA,qBAAApE,QAAA;;QAEA;QACA,IAAAA,QAAA,CAAApB,IAAA;UACA0B,OAAA,CAAA8D,GAAA,aAAApE,QAAA,CAAApB,IAAA;UACA,OAAAoB,QAAA,CAAApB,IAAA;QACA;UACA0B,OAAA,CAAAC,KAAA;UACA;YAAAN,IAAA;YAAAO,GAAA;YAAA5B,IAAA;UAAA;QACA;MACA,SAAA2B,KAAA;QACAD,OAAA,CAAAC,KAAA,qBAAAA,KAAA;QACA;UAAAN,IAAA;UAAAO,GAAA,EAAAD,KAAA,CAAA+D,OAAA;UAAA1F,IAAA;QAAA;MACA;IACA;IAEA2F,YAAA;MACA;MACAC,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}