{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nvar Eventful = function () {\n  function Eventful(eventProcessors) {\n    if (eventProcessors) {\n      this._$eventProcessor = eventProcessors;\n    }\n  }\n  Eventful.prototype.on = function (event, query, handler, context) {\n    if (!this._$handlers) {\n      this._$handlers = {};\n    }\n    var _h = this._$handlers;\n    if (typeof query === 'function') {\n      context = handler;\n      handler = query;\n      query = null;\n    }\n    if (!handler || !event) {\n      return this;\n    }\n    var eventProcessor = this._$eventProcessor;\n    if (query != null && eventProcessor && eventProcessor.normalizeQuery) {\n      query = eventProcessor.normalizeQuery(query);\n    }\n    if (!_h[event]) {\n      _h[event] = [];\n    }\n    for (var i = 0; i < _h[event].length; i++) {\n      if (_h[event][i].h === handler) {\n        return this;\n      }\n    }\n    var wrap = {\n      h: handler,\n      query: query,\n      ctx: context || this,\n      callAtLast: handler.zrEventfulCallAtLast\n    };\n    var lastIndex = _h[event].length - 1;\n    var lastWrap = _h[event][lastIndex];\n    lastWrap && lastWrap.callAtLast ? _h[event].splice(lastIndex, 0, wrap) : _h[event].push(wrap);\n    return this;\n  };\n  Eventful.prototype.isSilent = function (eventName) {\n    var _h = this._$handlers;\n    return !_h || !_h[eventName] || !_h[eventName].length;\n  };\n  Eventful.prototype.off = function (eventType, handler) {\n    var _h = this._$handlers;\n    if (!_h) {\n      return this;\n    }\n    if (!eventType) {\n      this._$handlers = {};\n      return this;\n    }\n    if (handler) {\n      if (_h[eventType]) {\n        var newList = [];\n        for (var i = 0, l = _h[eventType].length; i < l; i++) {\n          if (_h[eventType][i].h !== handler) {\n            newList.push(_h[eventType][i]);\n          }\n        }\n        _h[eventType] = newList;\n      }\n      if (_h[eventType] && _h[eventType].length === 0) {\n        delete _h[eventType];\n      }\n    } else {\n      delete _h[eventType];\n    }\n    return this;\n  };\n  Eventful.prototype.trigger = function (eventType) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    if (!this._$handlers) {\n      return this;\n    }\n    var _h = this._$handlers[eventType];\n    var eventProcessor = this._$eventProcessor;\n    if (_h) {\n      var argLen = args.length;\n      var len = _h.length;\n      for (var i = 0; i < len; i++) {\n        var hItem = _h[i];\n        if (eventProcessor && eventProcessor.filter && hItem.query != null && !eventProcessor.filter(eventType, hItem.query)) {\n          continue;\n        }\n        switch (argLen) {\n          case 0:\n            hItem.h.call(hItem.ctx);\n            break;\n          case 1:\n            hItem.h.call(hItem.ctx, args[0]);\n            break;\n          case 2:\n            hItem.h.call(hItem.ctx, args[0], args[1]);\n            break;\n          default:\n            hItem.h.apply(hItem.ctx, args);\n            break;\n        }\n      }\n    }\n    eventProcessor && eventProcessor.afterTrigger && eventProcessor.afterTrigger(eventType);\n    return this;\n  };\n  Eventful.prototype.triggerWithContext = function (type) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    if (!this._$handlers) {\n      return this;\n    }\n    var _h = this._$handlers[type];\n    var eventProcessor = this._$eventProcessor;\n    if (_h) {\n      var argLen = args.length;\n      var ctx = args[argLen - 1];\n      var len = _h.length;\n      for (var i = 0; i < len; i++) {\n        var hItem = _h[i];\n        if (eventProcessor && eventProcessor.filter && hItem.query != null && !eventProcessor.filter(type, hItem.query)) {\n          continue;\n        }\n        switch (argLen) {\n          case 0:\n            hItem.h.call(ctx);\n            break;\n          case 1:\n            hItem.h.call(ctx, args[0]);\n            break;\n          case 2:\n            hItem.h.call(ctx, args[0], args[1]);\n            break;\n          default:\n            hItem.h.apply(ctx, args.slice(1, argLen - 1));\n            break;\n        }\n      }\n    }\n    eventProcessor && eventProcessor.afterTrigger && eventProcessor.afterTrigger(type);\n    return this;\n  };\n  return Eventful;\n}();\nexport default Eventful;", "map": {"version": 3, "names": ["Eventful", "eventProcessors", "_$eventProcessor", "prototype", "on", "event", "query", "handler", "context", "_$handlers", "_h", "eventProcessor", "normalizeQuery", "i", "length", "h", "wrap", "ctx", "callAtLast", "zrEventfulCallAtLast", "lastIndex", "lastWrap", "splice", "push", "isSilent", "eventName", "off", "eventType", "newList", "l", "trigger", "args", "_i", "arguments", "argLen", "len", "hItem", "filter", "call", "apply", "afterTrigger", "triggerWithContext", "type", "slice"], "sources": ["E:/indicator-qa-service/datafront/node_modules/zrender/lib/core/Eventful.js"], "sourcesContent": ["var Eventful = (function () {\n    function Eventful(eventProcessors) {\n        if (eventProcessors) {\n            this._$eventProcessor = eventProcessors;\n        }\n    }\n    Eventful.prototype.on = function (event, query, handler, context) {\n        if (!this._$handlers) {\n            this._$handlers = {};\n        }\n        var _h = this._$handlers;\n        if (typeof query === 'function') {\n            context = handler;\n            handler = query;\n            query = null;\n        }\n        if (!handler || !event) {\n            return this;\n        }\n        var eventProcessor = this._$eventProcessor;\n        if (query != null && eventProcessor && eventProcessor.normalizeQuery) {\n            query = eventProcessor.normalizeQuery(query);\n        }\n        if (!_h[event]) {\n            _h[event] = [];\n        }\n        for (var i = 0; i < _h[event].length; i++) {\n            if (_h[event][i].h === handler) {\n                return this;\n            }\n        }\n        var wrap = {\n            h: handler,\n            query: query,\n            ctx: (context || this),\n            callAtLast: handler.zrEventfulCallAtLast\n        };\n        var lastIndex = _h[event].length - 1;\n        var lastWrap = _h[event][lastIndex];\n        (lastWrap && lastWrap.callAtLast)\n            ? _h[event].splice(lastIndex, 0, wrap)\n            : _h[event].push(wrap);\n        return this;\n    };\n    Eventful.prototype.isSilent = function (eventName) {\n        var _h = this._$handlers;\n        return !_h || !_h[eventName] || !_h[eventName].length;\n    };\n    Eventful.prototype.off = function (eventType, handler) {\n        var _h = this._$handlers;\n        if (!_h) {\n            return this;\n        }\n        if (!eventType) {\n            this._$handlers = {};\n            return this;\n        }\n        if (handler) {\n            if (_h[eventType]) {\n                var newList = [];\n                for (var i = 0, l = _h[eventType].length; i < l; i++) {\n                    if (_h[eventType][i].h !== handler) {\n                        newList.push(_h[eventType][i]);\n                    }\n                }\n                _h[eventType] = newList;\n            }\n            if (_h[eventType] && _h[eventType].length === 0) {\n                delete _h[eventType];\n            }\n        }\n        else {\n            delete _h[eventType];\n        }\n        return this;\n    };\n    Eventful.prototype.trigger = function (eventType) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (!this._$handlers) {\n            return this;\n        }\n        var _h = this._$handlers[eventType];\n        var eventProcessor = this._$eventProcessor;\n        if (_h) {\n            var argLen = args.length;\n            var len = _h.length;\n            for (var i = 0; i < len; i++) {\n                var hItem = _h[i];\n                if (eventProcessor\n                    && eventProcessor.filter\n                    && hItem.query != null\n                    && !eventProcessor.filter(eventType, hItem.query)) {\n                    continue;\n                }\n                switch (argLen) {\n                    case 0:\n                        hItem.h.call(hItem.ctx);\n                        break;\n                    case 1:\n                        hItem.h.call(hItem.ctx, args[0]);\n                        break;\n                    case 2:\n                        hItem.h.call(hItem.ctx, args[0], args[1]);\n                        break;\n                    default:\n                        hItem.h.apply(hItem.ctx, args);\n                        break;\n                }\n            }\n        }\n        eventProcessor && eventProcessor.afterTrigger\n            && eventProcessor.afterTrigger(eventType);\n        return this;\n    };\n    Eventful.prototype.triggerWithContext = function (type) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        if (!this._$handlers) {\n            return this;\n        }\n        var _h = this._$handlers[type];\n        var eventProcessor = this._$eventProcessor;\n        if (_h) {\n            var argLen = args.length;\n            var ctx = args[argLen - 1];\n            var len = _h.length;\n            for (var i = 0; i < len; i++) {\n                var hItem = _h[i];\n                if (eventProcessor\n                    && eventProcessor.filter\n                    && hItem.query != null\n                    && !eventProcessor.filter(type, hItem.query)) {\n                    continue;\n                }\n                switch (argLen) {\n                    case 0:\n                        hItem.h.call(ctx);\n                        break;\n                    case 1:\n                        hItem.h.call(ctx, args[0]);\n                        break;\n                    case 2:\n                        hItem.h.call(ctx, args[0], args[1]);\n                        break;\n                    default:\n                        hItem.h.apply(ctx, args.slice(1, argLen - 1));\n                        break;\n                }\n            }\n        }\n        eventProcessor && eventProcessor.afterTrigger\n            && eventProcessor.afterTrigger(type);\n        return this;\n    };\n    return Eventful;\n}());\nexport default Eventful;\n"], "mappings": ";;;AAAA,IAAIA,QAAQ,GAAI,YAAY;EACxB,SAASA,QAAQA,CAACC,eAAe,EAAE;IAC/B,IAAIA,eAAe,EAAE;MACjB,IAAI,CAACC,gBAAgB,GAAGD,eAAe;IAC3C;EACJ;EACAD,QAAQ,CAACG,SAAS,CAACC,EAAE,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAC9D,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,CAAC,CAAC;IACxB;IACA,IAAIC,EAAE,GAAG,IAAI,CAACD,UAAU;IACxB,IAAI,OAAOH,KAAK,KAAK,UAAU,EAAE;MAC7BE,OAAO,GAAGD,OAAO;MACjBA,OAAO,GAAGD,KAAK;MACfA,KAAK,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,OAAO,IAAI,CAACF,KAAK,EAAE;MACpB,OAAO,IAAI;IACf;IACA,IAAIM,cAAc,GAAG,IAAI,CAACT,gBAAgB;IAC1C,IAAII,KAAK,IAAI,IAAI,IAAIK,cAAc,IAAIA,cAAc,CAACC,cAAc,EAAE;MAClEN,KAAK,GAAGK,cAAc,CAACC,cAAc,CAACN,KAAK,CAAC;IAChD;IACA,IAAI,CAACI,EAAE,CAACL,KAAK,CAAC,EAAE;MACZK,EAAE,CAACL,KAAK,CAAC,GAAG,EAAE;IAClB;IACA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,EAAE,CAACL,KAAK,CAAC,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,IAAIH,EAAE,CAACL,KAAK,CAAC,CAACQ,CAAC,CAAC,CAACE,CAAC,KAAKR,OAAO,EAAE;QAC5B,OAAO,IAAI;MACf;IACJ;IACA,IAAIS,IAAI,GAAG;MACPD,CAAC,EAAER,OAAO;MACVD,KAAK,EAAEA,KAAK;MACZW,GAAG,EAAGT,OAAO,IAAI,IAAK;MACtBU,UAAU,EAAEX,OAAO,CAACY;IACxB,CAAC;IACD,IAAIC,SAAS,GAAGV,EAAE,CAACL,KAAK,CAAC,CAACS,MAAM,GAAG,CAAC;IACpC,IAAIO,QAAQ,GAAGX,EAAE,CAACL,KAAK,CAAC,CAACe,SAAS,CAAC;IAClCC,QAAQ,IAAIA,QAAQ,CAACH,UAAU,GAC1BR,EAAE,CAACL,KAAK,CAAC,CAACiB,MAAM,CAACF,SAAS,EAAE,CAAC,EAAEJ,IAAI,CAAC,GACpCN,EAAE,CAACL,KAAK,CAAC,CAACkB,IAAI,CAACP,IAAI,CAAC;IAC1B,OAAO,IAAI;EACf,CAAC;EACDhB,QAAQ,CAACG,SAAS,CAACqB,QAAQ,GAAG,UAAUC,SAAS,EAAE;IAC/C,IAAIf,EAAE,GAAG,IAAI,CAACD,UAAU;IACxB,OAAO,CAACC,EAAE,IAAI,CAACA,EAAE,CAACe,SAAS,CAAC,IAAI,CAACf,EAAE,CAACe,SAAS,CAAC,CAACX,MAAM;EACzD,CAAC;EACDd,QAAQ,CAACG,SAAS,CAACuB,GAAG,GAAG,UAAUC,SAAS,EAAEpB,OAAO,EAAE;IACnD,IAAIG,EAAE,GAAG,IAAI,CAACD,UAAU;IACxB,IAAI,CAACC,EAAE,EAAE;MACL,OAAO,IAAI;IACf;IACA,IAAI,CAACiB,SAAS,EAAE;MACZ,IAAI,CAAClB,UAAU,GAAG,CAAC,CAAC;MACpB,OAAO,IAAI;IACf;IACA,IAAIF,OAAO,EAAE;MACT,IAAIG,EAAE,CAACiB,SAAS,CAAC,EAAE;QACf,IAAIC,OAAO,GAAG,EAAE;QAChB,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEgB,CAAC,GAAGnB,EAAE,CAACiB,SAAS,CAAC,CAACb,MAAM,EAAED,CAAC,GAAGgB,CAAC,EAAEhB,CAAC,EAAE,EAAE;UAClD,IAAIH,EAAE,CAACiB,SAAS,CAAC,CAACd,CAAC,CAAC,CAACE,CAAC,KAAKR,OAAO,EAAE;YAChCqB,OAAO,CAACL,IAAI,CAACb,EAAE,CAACiB,SAAS,CAAC,CAACd,CAAC,CAAC,CAAC;UAClC;QACJ;QACAH,EAAE,CAACiB,SAAS,CAAC,GAAGC,OAAO;MAC3B;MACA,IAAIlB,EAAE,CAACiB,SAAS,CAAC,IAAIjB,EAAE,CAACiB,SAAS,CAAC,CAACb,MAAM,KAAK,CAAC,EAAE;QAC7C,OAAOJ,EAAE,CAACiB,SAAS,CAAC;MACxB;IACJ,CAAC,MACI;MACD,OAAOjB,EAAE,CAACiB,SAAS,CAAC;IACxB;IACA,OAAO,IAAI;EACf,CAAC;EACD3B,QAAQ,CAACG,SAAS,CAAC2B,OAAO,GAAG,UAAUH,SAAS,EAAE;IAC9C,IAAII,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACnB,MAAM,EAAEkB,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAChC;IACA,IAAI,CAAC,IAAI,CAACvB,UAAU,EAAE;MAClB,OAAO,IAAI;IACf;IACA,IAAIC,EAAE,GAAG,IAAI,CAACD,UAAU,CAACkB,SAAS,CAAC;IACnC,IAAIhB,cAAc,GAAG,IAAI,CAACT,gBAAgB;IAC1C,IAAIQ,EAAE,EAAE;MACJ,IAAIwB,MAAM,GAAGH,IAAI,CAACjB,MAAM;MACxB,IAAIqB,GAAG,GAAGzB,EAAE,CAACI,MAAM;MACnB,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,GAAG,EAAEtB,CAAC,EAAE,EAAE;QAC1B,IAAIuB,KAAK,GAAG1B,EAAE,CAACG,CAAC,CAAC;QACjB,IAAIF,cAAc,IACXA,cAAc,CAAC0B,MAAM,IACrBD,KAAK,CAAC9B,KAAK,IAAI,IAAI,IACnB,CAACK,cAAc,CAAC0B,MAAM,CAACV,SAAS,EAAES,KAAK,CAAC9B,KAAK,CAAC,EAAE;UACnD;QACJ;QACA,QAAQ4B,MAAM;UACV,KAAK,CAAC;YACFE,KAAK,CAACrB,CAAC,CAACuB,IAAI,CAACF,KAAK,CAACnB,GAAG,CAAC;YACvB;UACJ,KAAK,CAAC;YACFmB,KAAK,CAACrB,CAAC,CAACuB,IAAI,CAACF,KAAK,CAACnB,GAAG,EAAEc,IAAI,CAAC,CAAC,CAAC,CAAC;YAChC;UACJ,KAAK,CAAC;YACFK,KAAK,CAACrB,CAAC,CAACuB,IAAI,CAACF,KAAK,CAACnB,GAAG,EAAEc,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;YACzC;UACJ;YACIK,KAAK,CAACrB,CAAC,CAACwB,KAAK,CAACH,KAAK,CAACnB,GAAG,EAAEc,IAAI,CAAC;YAC9B;QACR;MACJ;IACJ;IACApB,cAAc,IAAIA,cAAc,CAAC6B,YAAY,IACtC7B,cAAc,CAAC6B,YAAY,CAACb,SAAS,CAAC;IAC7C,OAAO,IAAI;EACf,CAAC;EACD3B,QAAQ,CAACG,SAAS,CAACsC,kBAAkB,GAAG,UAAUC,IAAI,EAAE;IACpD,IAAIX,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACnB,MAAM,EAAEkB,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAChC;IACA,IAAI,CAAC,IAAI,CAACvB,UAAU,EAAE;MAClB,OAAO,IAAI;IACf;IACA,IAAIC,EAAE,GAAG,IAAI,CAACD,UAAU,CAACiC,IAAI,CAAC;IAC9B,IAAI/B,cAAc,GAAG,IAAI,CAACT,gBAAgB;IAC1C,IAAIQ,EAAE,EAAE;MACJ,IAAIwB,MAAM,GAAGH,IAAI,CAACjB,MAAM;MACxB,IAAIG,GAAG,GAAGc,IAAI,CAACG,MAAM,GAAG,CAAC,CAAC;MAC1B,IAAIC,GAAG,GAAGzB,EAAE,CAACI,MAAM;MACnB,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,GAAG,EAAEtB,CAAC,EAAE,EAAE;QAC1B,IAAIuB,KAAK,GAAG1B,EAAE,CAACG,CAAC,CAAC;QACjB,IAAIF,cAAc,IACXA,cAAc,CAAC0B,MAAM,IACrBD,KAAK,CAAC9B,KAAK,IAAI,IAAI,IACnB,CAACK,cAAc,CAAC0B,MAAM,CAACK,IAAI,EAAEN,KAAK,CAAC9B,KAAK,CAAC,EAAE;UAC9C;QACJ;QACA,QAAQ4B,MAAM;UACV,KAAK,CAAC;YACFE,KAAK,CAACrB,CAAC,CAACuB,IAAI,CAACrB,GAAG,CAAC;YACjB;UACJ,KAAK,CAAC;YACFmB,KAAK,CAACrB,CAAC,CAACuB,IAAI,CAACrB,GAAG,EAAEc,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1B;UACJ,KAAK,CAAC;YACFK,KAAK,CAACrB,CAAC,CAACuB,IAAI,CAACrB,GAAG,EAAEc,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;YACnC;UACJ;YACIK,KAAK,CAACrB,CAAC,CAACwB,KAAK,CAACtB,GAAG,EAAEc,IAAI,CAACY,KAAK,CAAC,CAAC,EAAET,MAAM,GAAG,CAAC,CAAC,CAAC;YAC7C;QACR;MACJ;IACJ;IACAvB,cAAc,IAAIA,cAAc,CAAC6B,YAAY,IACtC7B,cAAc,CAAC6B,YAAY,CAACE,IAAI,CAAC;IACxC,OAAO,IAAI;EACf,CAAC;EACD,OAAO1C,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}