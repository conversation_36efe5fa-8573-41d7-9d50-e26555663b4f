{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar _a, _b, _c;\n// TODO\n// ??? refactor? check the outer usage of data provider.\n// merge with defaultDimValueGetter?\nimport { isTypedArray, extend, assert, each, isObject, bind } from 'zrender/lib/core/util.js';\nimport { getDataItemValue } from '../../util/model.js';\nimport { createSourceFromSeriesDataOption, isSourceInstance } from '../Source.js';\nimport { SOURCE_FORMAT_ORIGINAL, SOURCE_FORMAT_OBJECT_ROWS, SOURCE_FORMAT_KEYED_COLUMNS, SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ARRAY_ROWS, SERIES_LAYOUT_BY_COLUMN, SERIES_LAYOUT_BY_ROW } from '../../util/types.js';\nvar providerMethods;\nvar mountMethods;\n/**\r\n * If normal array used, mutable chunk size is supported.\r\n * If typed array used, chunk size must be fixed.\r\n */\nvar DefaultDataProvider = /** @class */function () {\n  function DefaultDataProvider(sourceParam, dimSize) {\n    // let source: Source;\n    var source = !isSourceInstance(sourceParam) ? createSourceFromSeriesDataOption(sourceParam) : sourceParam;\n    // declare source is Source;\n    this._source = source;\n    var data = this._data = source.data;\n    // Typed array. TODO IE10+?\n    if (source.sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (dimSize == null) {\n          throw new Error('Typed array data must specify dimension size');\n        }\n      }\n      this._offset = 0;\n      this._dimSize = dimSize;\n      this._data = data;\n    }\n    mountMethods(this, data, source);\n  }\n  DefaultDataProvider.prototype.getSource = function () {\n    return this._source;\n  };\n  DefaultDataProvider.prototype.count = function () {\n    return 0;\n  };\n  DefaultDataProvider.prototype.getItem = function (idx, out) {\n    return;\n  };\n  DefaultDataProvider.prototype.appendData = function (newData) {};\n  DefaultDataProvider.prototype.clean = function () {};\n  DefaultDataProvider.protoInitialize = function () {\n    // PENDING: To avoid potential incompat (e.g., prototype\n    // is visited somewhere), still init them on prototype.\n    var proto = DefaultDataProvider.prototype;\n    proto.pure = false;\n    proto.persistent = true;\n  }();\n  DefaultDataProvider.internalField = function () {\n    var _a;\n    mountMethods = function (provider, data, source) {\n      var sourceFormat = source.sourceFormat;\n      var seriesLayoutBy = source.seriesLayoutBy;\n      var startIndex = source.startIndex;\n      var dimsDef = source.dimensionsDefine;\n      var methods = providerMethods[getMethodMapKey(sourceFormat, seriesLayoutBy)];\n      if (process.env.NODE_ENV !== 'production') {\n        assert(methods, 'Invalide sourceFormat: ' + sourceFormat);\n      }\n      extend(provider, methods);\n      if (sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n        provider.getItem = getItemForTypedArray;\n        provider.count = countForTypedArray;\n        provider.fillStorage = fillStorageForTypedArray;\n      } else {\n        var rawItemGetter = getRawSourceItemGetter(sourceFormat, seriesLayoutBy);\n        provider.getItem = bind(rawItemGetter, null, data, startIndex, dimsDef);\n        var rawCounter = getRawSourceDataCounter(sourceFormat, seriesLayoutBy);\n        provider.count = bind(rawCounter, null, data, startIndex, dimsDef);\n      }\n    };\n    var getItemForTypedArray = function (idx, out) {\n      idx = idx - this._offset;\n      out = out || [];\n      var data = this._data;\n      var dimSize = this._dimSize;\n      var offset = dimSize * idx;\n      for (var i = 0; i < dimSize; i++) {\n        out[i] = data[offset + i];\n      }\n      return out;\n    };\n    var fillStorageForTypedArray = function (start, end, storage, extent) {\n      var data = this._data;\n      var dimSize = this._dimSize;\n      for (var dim = 0; dim < dimSize; dim++) {\n        var dimExtent = extent[dim];\n        var min = dimExtent[0] == null ? Infinity : dimExtent[0];\n        var max = dimExtent[1] == null ? -Infinity : dimExtent[1];\n        var count = end - start;\n        var arr = storage[dim];\n        for (var i = 0; i < count; i++) {\n          // appendData with TypedArray will always do replace in provider.\n          var val = data[i * dimSize + dim];\n          arr[start + i] = val;\n          val < min && (min = val);\n          val > max && (max = val);\n        }\n        dimExtent[0] = min;\n        dimExtent[1] = max;\n      }\n    };\n    var countForTypedArray = function () {\n      return this._data ? this._data.length / this._dimSize : 0;\n    };\n    providerMethods = (_a = {}, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_COLUMN] = {\n      pure: true,\n      appendData: appendDataSimply\n    }, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_ROW] = {\n      pure: true,\n      appendData: function () {\n        throw new Error('Do not support appendData when set seriesLayoutBy: \"row\".');\n      }\n    }, _a[SOURCE_FORMAT_OBJECT_ROWS] = {\n      pure: true,\n      appendData: appendDataSimply\n    }, _a[SOURCE_FORMAT_KEYED_COLUMNS] = {\n      pure: true,\n      appendData: function (newData) {\n        var data = this._data;\n        each(newData, function (newCol, key) {\n          var oldCol = data[key] || (data[key] = []);\n          for (var i = 0; i < (newCol || []).length; i++) {\n            oldCol.push(newCol[i]);\n          }\n        });\n      }\n    }, _a[SOURCE_FORMAT_ORIGINAL] = {\n      appendData: appendDataSimply\n    }, _a[SOURCE_FORMAT_TYPED_ARRAY] = {\n      persistent: false,\n      pure: true,\n      appendData: function (newData) {\n        if (process.env.NODE_ENV !== 'production') {\n          assert(isTypedArray(newData), 'Added data must be TypedArray if data in initialization is TypedArray');\n        }\n        this._data = newData;\n      },\n      // Clean self if data is already used.\n      clean: function () {\n        // PENDING\n        this._offset += this.count();\n        this._data = null;\n      }\n    }, _a);\n    function appendDataSimply(newData) {\n      for (var i = 0; i < newData.length; i++) {\n        this._data.push(newData[i]);\n      }\n    }\n  }();\n  return DefaultDataProvider;\n}();\nexport { DefaultDataProvider };\nvar getItemSimply = function (rawData, startIndex, dimsDef, idx) {\n  return rawData[idx];\n};\nvar rawSourceItemGetterMap = (_a = {}, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_COLUMN] = function (rawData, startIndex, dimsDef, idx) {\n  return rawData[idx + startIndex];\n}, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_ROW] = function (rawData, startIndex, dimsDef, idx, out) {\n  idx += startIndex;\n  var item = out || [];\n  var data = rawData;\n  for (var i = 0; i < data.length; i++) {\n    var row = data[i];\n    item[i] = row ? row[idx] : null;\n  }\n  return item;\n}, _a[SOURCE_FORMAT_OBJECT_ROWS] = getItemSimply, _a[SOURCE_FORMAT_KEYED_COLUMNS] = function (rawData, startIndex, dimsDef, idx, out) {\n  var item = out || [];\n  for (var i = 0; i < dimsDef.length; i++) {\n    var dimName = dimsDef[i].name;\n    if (process.env.NODE_ENV !== 'production') {\n      if (dimName == null) {\n        throw new Error();\n      }\n    }\n    var col = rawData[dimName];\n    item[i] = col ? col[idx] : null;\n  }\n  return item;\n}, _a[SOURCE_FORMAT_ORIGINAL] = getItemSimply, _a);\nexport function getRawSourceItemGetter(sourceFormat, seriesLayoutBy) {\n  var method = rawSourceItemGetterMap[getMethodMapKey(sourceFormat, seriesLayoutBy)];\n  if (process.env.NODE_ENV !== 'production') {\n    assert(method, 'Do not support get item on \"' + sourceFormat + '\", \"' + seriesLayoutBy + '\".');\n  }\n  return method;\n}\nvar countSimply = function (rawData, startIndex, dimsDef) {\n  return rawData.length;\n};\nvar rawSourceDataCounterMap = (_b = {}, _b[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_COLUMN] = function (rawData, startIndex, dimsDef) {\n  return Math.max(0, rawData.length - startIndex);\n}, _b[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_ROW] = function (rawData, startIndex, dimsDef) {\n  var row = rawData[0];\n  return row ? Math.max(0, row.length - startIndex) : 0;\n}, _b[SOURCE_FORMAT_OBJECT_ROWS] = countSimply, _b[SOURCE_FORMAT_KEYED_COLUMNS] = function (rawData, startIndex, dimsDef) {\n  var dimName = dimsDef[0].name;\n  if (process.env.NODE_ENV !== 'production') {\n    if (dimName == null) {\n      throw new Error();\n    }\n  }\n  var col = rawData[dimName];\n  return col ? col.length : 0;\n}, _b[SOURCE_FORMAT_ORIGINAL] = countSimply, _b);\nexport function getRawSourceDataCounter(sourceFormat, seriesLayoutBy) {\n  var method = rawSourceDataCounterMap[getMethodMapKey(sourceFormat, seriesLayoutBy)];\n  if (process.env.NODE_ENV !== 'production') {\n    assert(method, 'Do not support count on \"' + sourceFormat + '\", \"' + seriesLayoutBy + '\".');\n  }\n  return method;\n}\nvar getRawValueSimply = function (dataItem, dimIndex, property) {\n  return dataItem[dimIndex];\n};\nvar rawSourceValueGetterMap = (_c = {}, _c[SOURCE_FORMAT_ARRAY_ROWS] = getRawValueSimply, _c[SOURCE_FORMAT_OBJECT_ROWS] = function (dataItem, dimIndex, property) {\n  return dataItem[property];\n}, _c[SOURCE_FORMAT_KEYED_COLUMNS] = getRawValueSimply, _c[SOURCE_FORMAT_ORIGINAL] = function (dataItem, dimIndex, property) {\n  // FIXME: In some case (markpoint in geo (geo-map.html)),\n  // dataItem is {coord: [...]}\n  var value = getDataItemValue(dataItem);\n  return !(value instanceof Array) ? value : value[dimIndex];\n}, _c[SOURCE_FORMAT_TYPED_ARRAY] = getRawValueSimply, _c);\nexport function getRawSourceValueGetter(sourceFormat) {\n  var method = rawSourceValueGetterMap[sourceFormat];\n  if (process.env.NODE_ENV !== 'production') {\n    assert(method, 'Do not support get value on \"' + sourceFormat + '\".');\n  }\n  return method;\n}\nfunction getMethodMapKey(sourceFormat, seriesLayoutBy) {\n  return sourceFormat === SOURCE_FORMAT_ARRAY_ROWS ? sourceFormat + '_' + seriesLayoutBy : sourceFormat;\n}\n// ??? FIXME can these logic be more neat: getRawValue, getRawDataItem,\n// Consider persistent.\n// Caution: why use raw value to display on label or tooltip?\n// A reason is to avoid format. For example time value we do not know\n// how to format is expected. More over, if stack is used, calculated\n// value may be 0.91000000001, which have brings trouble to display.\n// TODO: consider how to treat null/undefined/NaN when display?\nexport function retrieveRawValue(data, dataIndex,\n// If dimIndex is null/undefined, return OptionDataItem.\n// Otherwise, return OptionDataValue.\ndim) {\n  if (!data) {\n    return;\n  }\n  // Consider data may be not persistent.\n  var dataItem = data.getRawDataItem(dataIndex);\n  if (dataItem == null) {\n    return;\n  }\n  var store = data.getStore();\n  var sourceFormat = store.getSource().sourceFormat;\n  if (dim != null) {\n    var dimIndex = data.getDimensionIndex(dim);\n    var property = store.getDimensionProperty(dimIndex);\n    return getRawSourceValueGetter(sourceFormat)(dataItem, dimIndex, property);\n  } else {\n    var result = dataItem;\n    if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n      result = getDataItemValue(dataItem);\n    }\n    return result;\n  }\n}\n/**\r\n * Compatible with some cases (in pie, map) like:\r\n * data: [{name: 'xx', value: 5, selected: true}, ...]\r\n * where only sourceFormat is 'original' and 'objectRows' supported.\r\n *\r\n * // TODO\r\n * Supported detail options in data item when using 'arrayRows'.\r\n *\r\n * @param data\r\n * @param dataIndex\r\n * @param attr like 'selected'\r\n */\nexport function retrieveRawAttr(data, dataIndex, attr) {\n  if (!data) {\n    return;\n  }\n  var sourceFormat = data.getStore().getSource().sourceFormat;\n  if (sourceFormat !== SOURCE_FORMAT_ORIGINAL && sourceFormat !== SOURCE_FORMAT_OBJECT_ROWS) {\n    return;\n  }\n  var dataItem = data.getRawDataItem(dataIndex);\n  if (sourceFormat === SOURCE_FORMAT_ORIGINAL && !isObject(dataItem)) {\n    dataItem = null;\n  }\n  if (dataItem) {\n    return dataItem[attr];\n  }\n}", "map": {"version": 3, "names": ["_a", "_b", "_c", "isTypedArray", "extend", "assert", "each", "isObject", "bind", "getDataItemValue", "createSourceFromSeriesDataOption", "isSourceInstance", "SOURCE_FORMAT_ORIGINAL", "SOURCE_FORMAT_OBJECT_ROWS", "SOURCE_FORMAT_KEYED_COLUMNS", "SOURCE_FORMAT_TYPED_ARRAY", "SOURCE_FORMAT_ARRAY_ROWS", "SERIES_LAYOUT_BY_COLUMN", "SERIES_LAYOUT_BY_ROW", "providerMethods", "mountMethods", "DefaultDataProvider", "sourceParam", "dimSize", "source", "_source", "data", "_data", "sourceFormat", "process", "env", "NODE_ENV", "Error", "_offset", "_dimSize", "prototype", "getSource", "count", "getItem", "idx", "out", "appendData", "newData", "clean", "protoInitialize", "proto", "pure", "persistent", "internalField", "provider", "seriesLayoutBy", "startIndex", "dimsDef", "dimensionsDefine", "methods", "getMethodMapKey", "getItemForTypedArray", "countForTypedArray", "fillStorage", "fillStorageForTypedArray", "rawItemGetter", "getRawSourceItemGetter", "rawCounter", "getRawSourceDataCounter", "offset", "i", "start", "end", "storage", "extent", "dim", "dimExtent", "min", "Infinity", "max", "arr", "val", "length", "appendDataSimply", "newCol", "key", "oldCol", "push", "getItemSimply", "rawData", "rawSourceItemGetterMap", "item", "row", "dimName", "name", "col", "method", "countSimply", "rawSourceDataCounterMap", "Math", "getRawValueSimply", "dataItem", "dimIndex", "property", "rawSourceValueGetterMap", "value", "Array", "getRawSourceValueGetter", "retrieveRawValue", "dataIndex", "getRawDataItem", "store", "getStore", "getDimensionIndex", "getDimensionProperty", "result", "retrieveRawAttr", "attr"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/data/helper/dataProvider.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar _a, _b, _c;\n// TODO\n// ??? refactor? check the outer usage of data provider.\n// merge with defaultDimValueGetter?\nimport { isTypedArray, extend, assert, each, isObject, bind } from 'zrender/lib/core/util.js';\nimport { getDataItemValue } from '../../util/model.js';\nimport { createSourceFromSeriesDataOption, isSourceInstance } from '../Source.js';\nimport { SOURCE_FORMAT_ORIGINAL, SOURCE_FORMAT_OBJECT_ROWS, SOURCE_FORMAT_KEYED_COLUMNS, SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ARRAY_ROWS, SERIES_LAYOUT_BY_COLUMN, SERIES_LAYOUT_BY_ROW } from '../../util/types.js';\nvar providerMethods;\nvar mountMethods;\n/**\r\n * If normal array used, mutable chunk size is supported.\r\n * If typed array used, chunk size must be fixed.\r\n */\nvar DefaultDataProvider = /** @class */function () {\n  function DefaultDataProvider(sourceParam, dimSize) {\n    // let source: Source;\n    var source = !isSourceInstance(sourceParam) ? createSourceFromSeriesDataOption(sourceParam) : sourceParam;\n    // declare source is Source;\n    this._source = source;\n    var data = this._data = source.data;\n    // Typed array. TODO IE10+?\n    if (source.sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (dimSize == null) {\n          throw new Error('Typed array data must specify dimension size');\n        }\n      }\n      this._offset = 0;\n      this._dimSize = dimSize;\n      this._data = data;\n    }\n    mountMethods(this, data, source);\n  }\n  DefaultDataProvider.prototype.getSource = function () {\n    return this._source;\n  };\n  DefaultDataProvider.prototype.count = function () {\n    return 0;\n  };\n  DefaultDataProvider.prototype.getItem = function (idx, out) {\n    return;\n  };\n  DefaultDataProvider.prototype.appendData = function (newData) {};\n  DefaultDataProvider.prototype.clean = function () {};\n  DefaultDataProvider.protoInitialize = function () {\n    // PENDING: To avoid potential incompat (e.g., prototype\n    // is visited somewhere), still init them on prototype.\n    var proto = DefaultDataProvider.prototype;\n    proto.pure = false;\n    proto.persistent = true;\n  }();\n  DefaultDataProvider.internalField = function () {\n    var _a;\n    mountMethods = function (provider, data, source) {\n      var sourceFormat = source.sourceFormat;\n      var seriesLayoutBy = source.seriesLayoutBy;\n      var startIndex = source.startIndex;\n      var dimsDef = source.dimensionsDefine;\n      var methods = providerMethods[getMethodMapKey(sourceFormat, seriesLayoutBy)];\n      if (process.env.NODE_ENV !== 'production') {\n        assert(methods, 'Invalide sourceFormat: ' + sourceFormat);\n      }\n      extend(provider, methods);\n      if (sourceFormat === SOURCE_FORMAT_TYPED_ARRAY) {\n        provider.getItem = getItemForTypedArray;\n        provider.count = countForTypedArray;\n        provider.fillStorage = fillStorageForTypedArray;\n      } else {\n        var rawItemGetter = getRawSourceItemGetter(sourceFormat, seriesLayoutBy);\n        provider.getItem = bind(rawItemGetter, null, data, startIndex, dimsDef);\n        var rawCounter = getRawSourceDataCounter(sourceFormat, seriesLayoutBy);\n        provider.count = bind(rawCounter, null, data, startIndex, dimsDef);\n      }\n    };\n    var getItemForTypedArray = function (idx, out) {\n      idx = idx - this._offset;\n      out = out || [];\n      var data = this._data;\n      var dimSize = this._dimSize;\n      var offset = dimSize * idx;\n      for (var i = 0; i < dimSize; i++) {\n        out[i] = data[offset + i];\n      }\n      return out;\n    };\n    var fillStorageForTypedArray = function (start, end, storage, extent) {\n      var data = this._data;\n      var dimSize = this._dimSize;\n      for (var dim = 0; dim < dimSize; dim++) {\n        var dimExtent = extent[dim];\n        var min = dimExtent[0] == null ? Infinity : dimExtent[0];\n        var max = dimExtent[1] == null ? -Infinity : dimExtent[1];\n        var count = end - start;\n        var arr = storage[dim];\n        for (var i = 0; i < count; i++) {\n          // appendData with TypedArray will always do replace in provider.\n          var val = data[i * dimSize + dim];\n          arr[start + i] = val;\n          val < min && (min = val);\n          val > max && (max = val);\n        }\n        dimExtent[0] = min;\n        dimExtent[1] = max;\n      }\n    };\n    var countForTypedArray = function () {\n      return this._data ? this._data.length / this._dimSize : 0;\n    };\n    providerMethods = (_a = {}, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_COLUMN] = {\n      pure: true,\n      appendData: appendDataSimply\n    }, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_ROW] = {\n      pure: true,\n      appendData: function () {\n        throw new Error('Do not support appendData when set seriesLayoutBy: \"row\".');\n      }\n    }, _a[SOURCE_FORMAT_OBJECT_ROWS] = {\n      pure: true,\n      appendData: appendDataSimply\n    }, _a[SOURCE_FORMAT_KEYED_COLUMNS] = {\n      pure: true,\n      appendData: function (newData) {\n        var data = this._data;\n        each(newData, function (newCol, key) {\n          var oldCol = data[key] || (data[key] = []);\n          for (var i = 0; i < (newCol || []).length; i++) {\n            oldCol.push(newCol[i]);\n          }\n        });\n      }\n    }, _a[SOURCE_FORMAT_ORIGINAL] = {\n      appendData: appendDataSimply\n    }, _a[SOURCE_FORMAT_TYPED_ARRAY] = {\n      persistent: false,\n      pure: true,\n      appendData: function (newData) {\n        if (process.env.NODE_ENV !== 'production') {\n          assert(isTypedArray(newData), 'Added data must be TypedArray if data in initialization is TypedArray');\n        }\n        this._data = newData;\n      },\n      // Clean self if data is already used.\n      clean: function () {\n        // PENDING\n        this._offset += this.count();\n        this._data = null;\n      }\n    }, _a);\n    function appendDataSimply(newData) {\n      for (var i = 0; i < newData.length; i++) {\n        this._data.push(newData[i]);\n      }\n    }\n  }();\n  return DefaultDataProvider;\n}();\nexport { DefaultDataProvider };\nvar getItemSimply = function (rawData, startIndex, dimsDef, idx) {\n  return rawData[idx];\n};\nvar rawSourceItemGetterMap = (_a = {}, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_COLUMN] = function (rawData, startIndex, dimsDef, idx) {\n  return rawData[idx + startIndex];\n}, _a[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_ROW] = function (rawData, startIndex, dimsDef, idx, out) {\n  idx += startIndex;\n  var item = out || [];\n  var data = rawData;\n  for (var i = 0; i < data.length; i++) {\n    var row = data[i];\n    item[i] = row ? row[idx] : null;\n  }\n  return item;\n}, _a[SOURCE_FORMAT_OBJECT_ROWS] = getItemSimply, _a[SOURCE_FORMAT_KEYED_COLUMNS] = function (rawData, startIndex, dimsDef, idx, out) {\n  var item = out || [];\n  for (var i = 0; i < dimsDef.length; i++) {\n    var dimName = dimsDef[i].name;\n    if (process.env.NODE_ENV !== 'production') {\n      if (dimName == null) {\n        throw new Error();\n      }\n    }\n    var col = rawData[dimName];\n    item[i] = col ? col[idx] : null;\n  }\n  return item;\n}, _a[SOURCE_FORMAT_ORIGINAL] = getItemSimply, _a);\nexport function getRawSourceItemGetter(sourceFormat, seriesLayoutBy) {\n  var method = rawSourceItemGetterMap[getMethodMapKey(sourceFormat, seriesLayoutBy)];\n  if (process.env.NODE_ENV !== 'production') {\n    assert(method, 'Do not support get item on \"' + sourceFormat + '\", \"' + seriesLayoutBy + '\".');\n  }\n  return method;\n}\nvar countSimply = function (rawData, startIndex, dimsDef) {\n  return rawData.length;\n};\nvar rawSourceDataCounterMap = (_b = {}, _b[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_COLUMN] = function (rawData, startIndex, dimsDef) {\n  return Math.max(0, rawData.length - startIndex);\n}, _b[SOURCE_FORMAT_ARRAY_ROWS + '_' + SERIES_LAYOUT_BY_ROW] = function (rawData, startIndex, dimsDef) {\n  var row = rawData[0];\n  return row ? Math.max(0, row.length - startIndex) : 0;\n}, _b[SOURCE_FORMAT_OBJECT_ROWS] = countSimply, _b[SOURCE_FORMAT_KEYED_COLUMNS] = function (rawData, startIndex, dimsDef) {\n  var dimName = dimsDef[0].name;\n  if (process.env.NODE_ENV !== 'production') {\n    if (dimName == null) {\n      throw new Error();\n    }\n  }\n  var col = rawData[dimName];\n  return col ? col.length : 0;\n}, _b[SOURCE_FORMAT_ORIGINAL] = countSimply, _b);\nexport function getRawSourceDataCounter(sourceFormat, seriesLayoutBy) {\n  var method = rawSourceDataCounterMap[getMethodMapKey(sourceFormat, seriesLayoutBy)];\n  if (process.env.NODE_ENV !== 'production') {\n    assert(method, 'Do not support count on \"' + sourceFormat + '\", \"' + seriesLayoutBy + '\".');\n  }\n  return method;\n}\nvar getRawValueSimply = function (dataItem, dimIndex, property) {\n  return dataItem[dimIndex];\n};\nvar rawSourceValueGetterMap = (_c = {}, _c[SOURCE_FORMAT_ARRAY_ROWS] = getRawValueSimply, _c[SOURCE_FORMAT_OBJECT_ROWS] = function (dataItem, dimIndex, property) {\n  return dataItem[property];\n}, _c[SOURCE_FORMAT_KEYED_COLUMNS] = getRawValueSimply, _c[SOURCE_FORMAT_ORIGINAL] = function (dataItem, dimIndex, property) {\n  // FIXME: In some case (markpoint in geo (geo-map.html)),\n  // dataItem is {coord: [...]}\n  var value = getDataItemValue(dataItem);\n  return !(value instanceof Array) ? value : value[dimIndex];\n}, _c[SOURCE_FORMAT_TYPED_ARRAY] = getRawValueSimply, _c);\nexport function getRawSourceValueGetter(sourceFormat) {\n  var method = rawSourceValueGetterMap[sourceFormat];\n  if (process.env.NODE_ENV !== 'production') {\n    assert(method, 'Do not support get value on \"' + sourceFormat + '\".');\n  }\n  return method;\n}\nfunction getMethodMapKey(sourceFormat, seriesLayoutBy) {\n  return sourceFormat === SOURCE_FORMAT_ARRAY_ROWS ? sourceFormat + '_' + seriesLayoutBy : sourceFormat;\n}\n// ??? FIXME can these logic be more neat: getRawValue, getRawDataItem,\n// Consider persistent.\n// Caution: why use raw value to display on label or tooltip?\n// A reason is to avoid format. For example time value we do not know\n// how to format is expected. More over, if stack is used, calculated\n// value may be 0.91000000001, which have brings trouble to display.\n// TODO: consider how to treat null/undefined/NaN when display?\nexport function retrieveRawValue(data, dataIndex,\n// If dimIndex is null/undefined, return OptionDataItem.\n// Otherwise, return OptionDataValue.\ndim) {\n  if (!data) {\n    return;\n  }\n  // Consider data may be not persistent.\n  var dataItem = data.getRawDataItem(dataIndex);\n  if (dataItem == null) {\n    return;\n  }\n  var store = data.getStore();\n  var sourceFormat = store.getSource().sourceFormat;\n  if (dim != null) {\n    var dimIndex = data.getDimensionIndex(dim);\n    var property = store.getDimensionProperty(dimIndex);\n    return getRawSourceValueGetter(sourceFormat)(dataItem, dimIndex, property);\n  } else {\n    var result = dataItem;\n    if (sourceFormat === SOURCE_FORMAT_ORIGINAL) {\n      result = getDataItemValue(dataItem);\n    }\n    return result;\n  }\n}\n/**\r\n * Compatible with some cases (in pie, map) like:\r\n * data: [{name: 'xx', value: 5, selected: true}, ...]\r\n * where only sourceFormat is 'original' and 'objectRows' supported.\r\n *\r\n * // TODO\r\n * Supported detail options in data item when using 'arrayRows'.\r\n *\r\n * @param data\r\n * @param dataIndex\r\n * @param attr like 'selected'\r\n */\nexport function retrieveRawAttr(data, dataIndex, attr) {\n  if (!data) {\n    return;\n  }\n  var sourceFormat = data.getStore().getSource().sourceFormat;\n  if (sourceFormat !== SOURCE_FORMAT_ORIGINAL && sourceFormat !== SOURCE_FORMAT_OBJECT_ROWS) {\n    return;\n  }\n  var dataItem = data.getRawDataItem(dataIndex);\n  if (sourceFormat === SOURCE_FORMAT_ORIGINAL && !isObject(dataItem)) {\n    dataItem = null;\n  }\n  if (dataItem) {\n    return dataItem[attr];\n  }\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,EAAE,EAAEC,EAAE,EAAEC,EAAE;AACd;AACA;AACA;AACA,SAASC,YAAY,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,0BAA0B;AAC7F,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,gCAAgC,EAAEC,gBAAgB,QAAQ,cAAc;AACjF,SAASC,sBAAsB,EAAEC,yBAAyB,EAAEC,2BAA2B,EAAEC,yBAAyB,EAAEC,wBAAwB,EAAEC,uBAAuB,EAAEC,oBAAoB,QAAQ,qBAAqB;AACxN,IAAIC,eAAe;AACnB,IAAIC,YAAY;AAChB;AACA;AACA;AACA;AACA,IAAIC,mBAAmB,GAAG,aAAa,YAAY;EACjD,SAASA,mBAAmBA,CAACC,WAAW,EAAEC,OAAO,EAAE;IACjD;IACA,IAAIC,MAAM,GAAG,CAACb,gBAAgB,CAACW,WAAW,CAAC,GAAGZ,gCAAgC,CAACY,WAAW,CAAC,GAAGA,WAAW;IACzG;IACA,IAAI,CAACG,OAAO,GAAGD,MAAM;IACrB,IAAIE,IAAI,GAAG,IAAI,CAACC,KAAK,GAAGH,MAAM,CAACE,IAAI;IACnC;IACA,IAAIF,MAAM,CAACI,YAAY,KAAKb,yBAAyB,EAAE;MACrD,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIR,OAAO,IAAI,IAAI,EAAE;UACnB,MAAM,IAAIS,KAAK,CAAC,8CAA8C,CAAC;QACjE;MACF;MACA,IAAI,CAACC,OAAO,GAAG,CAAC;MAChB,IAAI,CAACC,QAAQ,GAAGX,OAAO;MACvB,IAAI,CAACI,KAAK,GAAGD,IAAI;IACnB;IACAN,YAAY,CAAC,IAAI,EAAEM,IAAI,EAAEF,MAAM,CAAC;EAClC;EACAH,mBAAmB,CAACc,SAAS,CAACC,SAAS,GAAG,YAAY;IACpD,OAAO,IAAI,CAACX,OAAO;EACrB,CAAC;EACDJ,mBAAmB,CAACc,SAAS,CAACE,KAAK,GAAG,YAAY;IAChD,OAAO,CAAC;EACV,CAAC;EACDhB,mBAAmB,CAACc,SAAS,CAACG,OAAO,GAAG,UAAUC,GAAG,EAAEC,GAAG,EAAE;IAC1D;EACF,CAAC;EACDnB,mBAAmB,CAACc,SAAS,CAACM,UAAU,GAAG,UAAUC,OAAO,EAAE,CAAC,CAAC;EAChErB,mBAAmB,CAACc,SAAS,CAACQ,KAAK,GAAG,YAAY,CAAC,CAAC;EACpDtB,mBAAmB,CAACuB,eAAe,GAAG,YAAY;IAChD;IACA;IACA,IAAIC,KAAK,GAAGxB,mBAAmB,CAACc,SAAS;IACzCU,KAAK,CAACC,IAAI,GAAG,KAAK;IAClBD,KAAK,CAACE,UAAU,GAAG,IAAI;EACzB,CAAC,CAAC,CAAC;EACH1B,mBAAmB,CAAC2B,aAAa,GAAG,YAAY;IAC9C,IAAIhD,EAAE;IACNoB,YAAY,GAAG,SAAAA,CAAU6B,QAAQ,EAAEvB,IAAI,EAAEF,MAAM,EAAE;MAC/C,IAAII,YAAY,GAAGJ,MAAM,CAACI,YAAY;MACtC,IAAIsB,cAAc,GAAG1B,MAAM,CAAC0B,cAAc;MAC1C,IAAIC,UAAU,GAAG3B,MAAM,CAAC2B,UAAU;MAClC,IAAIC,OAAO,GAAG5B,MAAM,CAAC6B,gBAAgB;MACrC,IAAIC,OAAO,GAAGnC,eAAe,CAACoC,eAAe,CAAC3B,YAAY,EAAEsB,cAAc,CAAC,CAAC;MAC5E,IAAIrB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC1B,MAAM,CAACiD,OAAO,EAAE,yBAAyB,GAAG1B,YAAY,CAAC;MAC3D;MACAxB,MAAM,CAAC6C,QAAQ,EAAEK,OAAO,CAAC;MACzB,IAAI1B,YAAY,KAAKb,yBAAyB,EAAE;QAC9CkC,QAAQ,CAACX,OAAO,GAAGkB,oBAAoB;QACvCP,QAAQ,CAACZ,KAAK,GAAGoB,kBAAkB;QACnCR,QAAQ,CAACS,WAAW,GAAGC,wBAAwB;MACjD,CAAC,MAAM;QACL,IAAIC,aAAa,GAAGC,sBAAsB,CAACjC,YAAY,EAAEsB,cAAc,CAAC;QACxED,QAAQ,CAACX,OAAO,GAAG9B,IAAI,CAACoD,aAAa,EAAE,IAAI,EAAElC,IAAI,EAAEyB,UAAU,EAAEC,OAAO,CAAC;QACvE,IAAIU,UAAU,GAAGC,uBAAuB,CAACnC,YAAY,EAAEsB,cAAc,CAAC;QACtED,QAAQ,CAACZ,KAAK,GAAG7B,IAAI,CAACsD,UAAU,EAAE,IAAI,EAAEpC,IAAI,EAAEyB,UAAU,EAAEC,OAAO,CAAC;MACpE;IACF,CAAC;IACD,IAAII,oBAAoB,GAAG,SAAAA,CAAUjB,GAAG,EAAEC,GAAG,EAAE;MAC7CD,GAAG,GAAGA,GAAG,GAAG,IAAI,CAACN,OAAO;MACxBO,GAAG,GAAGA,GAAG,IAAI,EAAE;MACf,IAAId,IAAI,GAAG,IAAI,CAACC,KAAK;MACrB,IAAIJ,OAAO,GAAG,IAAI,CAACW,QAAQ;MAC3B,IAAI8B,MAAM,GAAGzC,OAAO,GAAGgB,GAAG;MAC1B,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1C,OAAO,EAAE0C,CAAC,EAAE,EAAE;QAChCzB,GAAG,CAACyB,CAAC,CAAC,GAAGvC,IAAI,CAACsC,MAAM,GAAGC,CAAC,CAAC;MAC3B;MACA,OAAOzB,GAAG;IACZ,CAAC;IACD,IAAImB,wBAAwB,GAAG,SAAAA,CAAUO,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,MAAM,EAAE;MACpE,IAAI3C,IAAI,GAAG,IAAI,CAACC,KAAK;MACrB,IAAIJ,OAAO,GAAG,IAAI,CAACW,QAAQ;MAC3B,KAAK,IAAIoC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG/C,OAAO,EAAE+C,GAAG,EAAE,EAAE;QACtC,IAAIC,SAAS,GAAGF,MAAM,CAACC,GAAG,CAAC;QAC3B,IAAIE,GAAG,GAAGD,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,GAAGE,QAAQ,GAAGF,SAAS,CAAC,CAAC,CAAC;QACxD,IAAIG,GAAG,GAAGH,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,CAACE,QAAQ,GAAGF,SAAS,CAAC,CAAC,CAAC;QACzD,IAAIlC,KAAK,GAAG8B,GAAG,GAAGD,KAAK;QACvB,IAAIS,GAAG,GAAGP,OAAO,CAACE,GAAG,CAAC;QACtB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,KAAK,EAAE4B,CAAC,EAAE,EAAE;UAC9B;UACA,IAAIW,GAAG,GAAGlD,IAAI,CAACuC,CAAC,GAAG1C,OAAO,GAAG+C,GAAG,CAAC;UACjCK,GAAG,CAACT,KAAK,GAAGD,CAAC,CAAC,GAAGW,GAAG;UACpBA,GAAG,GAAGJ,GAAG,KAAKA,GAAG,GAAGI,GAAG,CAAC;UACxBA,GAAG,GAAGF,GAAG,KAAKA,GAAG,GAAGE,GAAG,CAAC;QAC1B;QACAL,SAAS,CAAC,CAAC,CAAC,GAAGC,GAAG;QAClBD,SAAS,CAAC,CAAC,CAAC,GAAGG,GAAG;MACpB;IACF,CAAC;IACD,IAAIjB,kBAAkB,GAAG,SAAAA,CAAA,EAAY;MACnC,OAAO,IAAI,CAAC9B,KAAK,GAAG,IAAI,CAACA,KAAK,CAACkD,MAAM,GAAG,IAAI,CAAC3C,QAAQ,GAAG,CAAC;IAC3D,CAAC;IACDf,eAAe,IAAInB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACgB,wBAAwB,GAAG,GAAG,GAAGC,uBAAuB,CAAC,GAAG;MACzF6B,IAAI,EAAE,IAAI;MACVL,UAAU,EAAEqC;IACd,CAAC,EAAE9E,EAAE,CAACgB,wBAAwB,GAAG,GAAG,GAAGE,oBAAoB,CAAC,GAAG;MAC7D4B,IAAI,EAAE,IAAI;MACVL,UAAU,EAAE,SAAAA,CAAA,EAAY;QACtB,MAAM,IAAIT,KAAK,CAAC,2DAA2D,CAAC;MAC9E;IACF,CAAC,EAAEhC,EAAE,CAACa,yBAAyB,CAAC,GAAG;MACjCiC,IAAI,EAAE,IAAI;MACVL,UAAU,EAAEqC;IACd,CAAC,EAAE9E,EAAE,CAACc,2BAA2B,CAAC,GAAG;MACnCgC,IAAI,EAAE,IAAI;MACVL,UAAU,EAAE,SAAAA,CAAUC,OAAO,EAAE;QAC7B,IAAIhB,IAAI,GAAG,IAAI,CAACC,KAAK;QACrBrB,IAAI,CAACoC,OAAO,EAAE,UAAUqC,MAAM,EAAEC,GAAG,EAAE;UACnC,IAAIC,MAAM,GAAGvD,IAAI,CAACsD,GAAG,CAAC,KAAKtD,IAAI,CAACsD,GAAG,CAAC,GAAG,EAAE,CAAC;UAC1C,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAACc,MAAM,IAAI,EAAE,EAAEF,MAAM,EAAEZ,CAAC,EAAE,EAAE;YAC9CgB,MAAM,CAACC,IAAI,CAACH,MAAM,CAACd,CAAC,CAAC,CAAC;UACxB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,EAAEjE,EAAE,CAACY,sBAAsB,CAAC,GAAG;MAC9B6B,UAAU,EAAEqC;IACd,CAAC,EAAE9E,EAAE,CAACe,yBAAyB,CAAC,GAAG;MACjCgC,UAAU,EAAE,KAAK;MACjBD,IAAI,EAAE,IAAI;MACVL,UAAU,EAAE,SAAAA,CAAUC,OAAO,EAAE;QAC7B,IAAIb,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC1B,MAAM,CAACF,YAAY,CAACuC,OAAO,CAAC,EAAE,uEAAuE,CAAC;QACxG;QACA,IAAI,CAACf,KAAK,GAAGe,OAAO;MACtB,CAAC;MACD;MACAC,KAAK,EAAE,SAAAA,CAAA,EAAY;QACjB;QACA,IAAI,CAACV,OAAO,IAAI,IAAI,CAACI,KAAK,CAAC,CAAC;QAC5B,IAAI,CAACV,KAAK,GAAG,IAAI;MACnB;IACF,CAAC,EAAE3B,EAAE,CAAC;IACN,SAAS8E,gBAAgBA,CAACpC,OAAO,EAAE;MACjC,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,OAAO,CAACmC,MAAM,EAAEZ,CAAC,EAAE,EAAE;QACvC,IAAI,CAACtC,KAAK,CAACuD,IAAI,CAACxC,OAAO,CAACuB,CAAC,CAAC,CAAC;MAC7B;IACF;EACF,CAAC,CAAC,CAAC;EACH,OAAO5C,mBAAmB;AAC5B,CAAC,CAAC,CAAC;AACH,SAASA,mBAAmB;AAC5B,IAAI8D,aAAa,GAAG,SAAAA,CAAUC,OAAO,EAAEjC,UAAU,EAAEC,OAAO,EAAEb,GAAG,EAAE;EAC/D,OAAO6C,OAAO,CAAC7C,GAAG,CAAC;AACrB,CAAC;AACD,IAAI8C,sBAAsB,IAAIrF,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACgB,wBAAwB,GAAG,GAAG,GAAGC,uBAAuB,CAAC,GAAG,UAAUmE,OAAO,EAAEjC,UAAU,EAAEC,OAAO,EAAEb,GAAG,EAAE;EACjJ,OAAO6C,OAAO,CAAC7C,GAAG,GAAGY,UAAU,CAAC;AAClC,CAAC,EAAEnD,EAAE,CAACgB,wBAAwB,GAAG,GAAG,GAAGE,oBAAoB,CAAC,GAAG,UAAUkE,OAAO,EAAEjC,UAAU,EAAEC,OAAO,EAAEb,GAAG,EAAEC,GAAG,EAAE;EAC/GD,GAAG,IAAIY,UAAU;EACjB,IAAImC,IAAI,GAAG9C,GAAG,IAAI,EAAE;EACpB,IAAId,IAAI,GAAG0D,OAAO;EAClB,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,IAAI,CAACmD,MAAM,EAAEZ,CAAC,EAAE,EAAE;IACpC,IAAIsB,GAAG,GAAG7D,IAAI,CAACuC,CAAC,CAAC;IACjBqB,IAAI,CAACrB,CAAC,CAAC,GAAGsB,GAAG,GAAGA,GAAG,CAAChD,GAAG,CAAC,GAAG,IAAI;EACjC;EACA,OAAO+C,IAAI;AACb,CAAC,EAAEtF,EAAE,CAACa,yBAAyB,CAAC,GAAGsE,aAAa,EAAEnF,EAAE,CAACc,2BAA2B,CAAC,GAAG,UAAUsE,OAAO,EAAEjC,UAAU,EAAEC,OAAO,EAAEb,GAAG,EAAEC,GAAG,EAAE;EACpI,IAAI8C,IAAI,GAAG9C,GAAG,IAAI,EAAE;EACpB,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,OAAO,CAACyB,MAAM,EAAEZ,CAAC,EAAE,EAAE;IACvC,IAAIuB,OAAO,GAAGpC,OAAO,CAACa,CAAC,CAAC,CAACwB,IAAI;IAC7B,IAAI5D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIyD,OAAO,IAAI,IAAI,EAAE;QACnB,MAAM,IAAIxD,KAAK,CAAC,CAAC;MACnB;IACF;IACA,IAAI0D,GAAG,GAAGN,OAAO,CAACI,OAAO,CAAC;IAC1BF,IAAI,CAACrB,CAAC,CAAC,GAAGyB,GAAG,GAAGA,GAAG,CAACnD,GAAG,CAAC,GAAG,IAAI;EACjC;EACA,OAAO+C,IAAI;AACb,CAAC,EAAEtF,EAAE,CAACY,sBAAsB,CAAC,GAAGuE,aAAa,EAAEnF,EAAE,CAAC;AAClD,OAAO,SAAS6D,sBAAsBA,CAACjC,YAAY,EAAEsB,cAAc,EAAE;EACnE,IAAIyC,MAAM,GAAGN,sBAAsB,CAAC9B,eAAe,CAAC3B,YAAY,EAAEsB,cAAc,CAAC,CAAC;EAClF,IAAIrB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC1B,MAAM,CAACsF,MAAM,EAAE,8BAA8B,GAAG/D,YAAY,GAAG,MAAM,GAAGsB,cAAc,GAAG,IAAI,CAAC;EAChG;EACA,OAAOyC,MAAM;AACf;AACA,IAAIC,WAAW,GAAG,SAAAA,CAAUR,OAAO,EAAEjC,UAAU,EAAEC,OAAO,EAAE;EACxD,OAAOgC,OAAO,CAACP,MAAM;AACvB,CAAC;AACD,IAAIgB,uBAAuB,IAAI5F,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACe,wBAAwB,GAAG,GAAG,GAAGC,uBAAuB,CAAC,GAAG,UAAUmE,OAAO,EAAEjC,UAAU,EAAEC,OAAO,EAAE;EAC7I,OAAO0C,IAAI,CAACpB,GAAG,CAAC,CAAC,EAAEU,OAAO,CAACP,MAAM,GAAG1B,UAAU,CAAC;AACjD,CAAC,EAAElD,EAAE,CAACe,wBAAwB,GAAG,GAAG,GAAGE,oBAAoB,CAAC,GAAG,UAAUkE,OAAO,EAAEjC,UAAU,EAAEC,OAAO,EAAE;EACrG,IAAImC,GAAG,GAAGH,OAAO,CAAC,CAAC,CAAC;EACpB,OAAOG,GAAG,GAAGO,IAAI,CAACpB,GAAG,CAAC,CAAC,EAAEa,GAAG,CAACV,MAAM,GAAG1B,UAAU,CAAC,GAAG,CAAC;AACvD,CAAC,EAAElD,EAAE,CAACY,yBAAyB,CAAC,GAAG+E,WAAW,EAAE3F,EAAE,CAACa,2BAA2B,CAAC,GAAG,UAAUsE,OAAO,EAAEjC,UAAU,EAAEC,OAAO,EAAE;EACxH,IAAIoC,OAAO,GAAGpC,OAAO,CAAC,CAAC,CAAC,CAACqC,IAAI;EAC7B,IAAI5D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIyD,OAAO,IAAI,IAAI,EAAE;MACnB,MAAM,IAAIxD,KAAK,CAAC,CAAC;IACnB;EACF;EACA,IAAI0D,GAAG,GAAGN,OAAO,CAACI,OAAO,CAAC;EAC1B,OAAOE,GAAG,GAAGA,GAAG,CAACb,MAAM,GAAG,CAAC;AAC7B,CAAC,EAAE5E,EAAE,CAACW,sBAAsB,CAAC,GAAGgF,WAAW,EAAE3F,EAAE,CAAC;AAChD,OAAO,SAAS8D,uBAAuBA,CAACnC,YAAY,EAAEsB,cAAc,EAAE;EACpE,IAAIyC,MAAM,GAAGE,uBAAuB,CAACtC,eAAe,CAAC3B,YAAY,EAAEsB,cAAc,CAAC,CAAC;EACnF,IAAIrB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC1B,MAAM,CAACsF,MAAM,EAAE,2BAA2B,GAAG/D,YAAY,GAAG,MAAM,GAAGsB,cAAc,GAAG,IAAI,CAAC;EAC7F;EACA,OAAOyC,MAAM;AACf;AACA,IAAII,iBAAiB,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAC9D,OAAOF,QAAQ,CAACC,QAAQ,CAAC;AAC3B,CAAC;AACD,IAAIE,uBAAuB,IAAIjG,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACc,wBAAwB,CAAC,GAAG+E,iBAAiB,EAAE7F,EAAE,CAACW,yBAAyB,CAAC,GAAG,UAAUmF,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAChK,OAAOF,QAAQ,CAACE,QAAQ,CAAC;AAC3B,CAAC,EAAEhG,EAAE,CAACY,2BAA2B,CAAC,GAAGiF,iBAAiB,EAAE7F,EAAE,CAACU,sBAAsB,CAAC,GAAG,UAAUoF,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAC3H;EACA;EACA,IAAIE,KAAK,GAAG3F,gBAAgB,CAACuF,QAAQ,CAAC;EACtC,OAAO,EAAEI,KAAK,YAAYC,KAAK,CAAC,GAAGD,KAAK,GAAGA,KAAK,CAACH,QAAQ,CAAC;AAC5D,CAAC,EAAE/F,EAAE,CAACa,yBAAyB,CAAC,GAAGgF,iBAAiB,EAAE7F,EAAE,CAAC;AACzD,OAAO,SAASoG,uBAAuBA,CAAC1E,YAAY,EAAE;EACpD,IAAI+D,MAAM,GAAGQ,uBAAuB,CAACvE,YAAY,CAAC;EAClD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC1B,MAAM,CAACsF,MAAM,EAAE,+BAA+B,GAAG/D,YAAY,GAAG,IAAI,CAAC;EACvE;EACA,OAAO+D,MAAM;AACf;AACA,SAASpC,eAAeA,CAAC3B,YAAY,EAAEsB,cAAc,EAAE;EACrD,OAAOtB,YAAY,KAAKZ,wBAAwB,GAAGY,YAAY,GAAG,GAAG,GAAGsB,cAAc,GAAGtB,YAAY;AACvG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2E,gBAAgBA,CAAC7E,IAAI,EAAE8E,SAAS;AAChD;AACA;AACAlC,GAAG,EAAE;EACH,IAAI,CAAC5C,IAAI,EAAE;IACT;EACF;EACA;EACA,IAAIsE,QAAQ,GAAGtE,IAAI,CAAC+E,cAAc,CAACD,SAAS,CAAC;EAC7C,IAAIR,QAAQ,IAAI,IAAI,EAAE;IACpB;EACF;EACA,IAAIU,KAAK,GAAGhF,IAAI,CAACiF,QAAQ,CAAC,CAAC;EAC3B,IAAI/E,YAAY,GAAG8E,KAAK,CAACtE,SAAS,CAAC,CAAC,CAACR,YAAY;EACjD,IAAI0C,GAAG,IAAI,IAAI,EAAE;IACf,IAAI2B,QAAQ,GAAGvE,IAAI,CAACkF,iBAAiB,CAACtC,GAAG,CAAC;IAC1C,IAAI4B,QAAQ,GAAGQ,KAAK,CAACG,oBAAoB,CAACZ,QAAQ,CAAC;IACnD,OAAOK,uBAAuB,CAAC1E,YAAY,CAAC,CAACoE,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,CAAC;EAC5E,CAAC,MAAM;IACL,IAAIY,MAAM,GAAGd,QAAQ;IACrB,IAAIpE,YAAY,KAAKhB,sBAAsB,EAAE;MAC3CkG,MAAM,GAAGrG,gBAAgB,CAACuF,QAAQ,CAAC;IACrC;IACA,OAAOc,MAAM;EACf;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACrF,IAAI,EAAE8E,SAAS,EAAEQ,IAAI,EAAE;EACrD,IAAI,CAACtF,IAAI,EAAE;IACT;EACF;EACA,IAAIE,YAAY,GAAGF,IAAI,CAACiF,QAAQ,CAAC,CAAC,CAACvE,SAAS,CAAC,CAAC,CAACR,YAAY;EAC3D,IAAIA,YAAY,KAAKhB,sBAAsB,IAAIgB,YAAY,KAAKf,yBAAyB,EAAE;IACzF;EACF;EACA,IAAImF,QAAQ,GAAGtE,IAAI,CAAC+E,cAAc,CAACD,SAAS,CAAC;EAC7C,IAAI5E,YAAY,KAAKhB,sBAAsB,IAAI,CAACL,QAAQ,CAACyF,QAAQ,CAAC,EAAE;IAClEA,QAAQ,GAAG,IAAI;EACjB;EACA,IAAIA,QAAQ,EAAE;IACZ,OAAOA,QAAQ,CAACgB,IAAI,CAAC;EACvB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}