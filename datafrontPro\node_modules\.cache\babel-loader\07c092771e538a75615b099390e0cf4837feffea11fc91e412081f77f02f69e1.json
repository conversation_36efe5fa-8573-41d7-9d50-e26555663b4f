{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nvar CartesianAxisModel = /** @class */function (_super) {\n  __extends(CartesianAxisModel, _super);\n  function CartesianAxisModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  CartesianAxisModel.prototype.getCoordSysModel = function () {\n    return this.getReferringComponents('grid', SINGLE_REFERRING).models[0];\n  };\n  CartesianAxisModel.type = 'cartesian2dAxis';\n  return CartesianAxisModel;\n}(ComponentModel);\nexport { CartesianAxisModel };\nzrUtil.mixin(CartesianAxisModel, AxisModelCommonMixin);\nexport default CartesianAxisModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "ComponentModel", "AxisModelCommonMixin", "SINGLE_REFERRING", "CartesianAxisModel", "_super", "apply", "arguments", "prototype", "getCoordSysModel", "getReferringComponents", "models", "type", "mixin"], "sources": ["E:/AllProject/datafront/node_modules/echarts/lib/coord/cartesian/AxisModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ComponentModel from '../../model/Component.js';\nimport { AxisModelCommonMixin } from '../axisModelCommonMixin.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nvar CartesianAxisModel = /** @class */function (_super) {\n  __extends(CartesianAxisModel, _super);\n  function CartesianAxisModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  CartesianAxisModel.prototype.getCoordSysModel = function () {\n    return this.getReferringComponents('grid', SINGLE_REFERRING).models[0];\n  };\n  CartesianAxisModel.type = 'cartesian2dAxis';\n  return CartesianAxisModel;\n}(ComponentModel);\nexport { CartesianAxisModel };\nzrUtil.mixin(CartesianAxisModel, AxisModelCommonMixin);\nexport default CartesianAxisModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SAASC,oBAAoB,QAAQ,4BAA4B;AACjE,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,IAAIC,kBAAkB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACtDN,SAAS,CAACK,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAH,kBAAkB,CAACI,SAAS,CAACC,gBAAgB,GAAG,YAAY;IAC1D,OAAO,IAAI,CAACC,sBAAsB,CAAC,MAAM,EAAEP,gBAAgB,CAAC,CAACQ,MAAM,CAAC,CAAC,CAAC;EACxE,CAAC;EACDP,kBAAkB,CAACQ,IAAI,GAAG,iBAAiB;EAC3C,OAAOR,kBAAkB;AAC3B,CAAC,CAACH,cAAc,CAAC;AACjB,SAASG,kBAAkB;AAC3BJ,MAAM,CAACa,KAAK,CAACT,kBAAkB,EAAEF,oBAAoB,CAAC;AACtD,eAAeE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}