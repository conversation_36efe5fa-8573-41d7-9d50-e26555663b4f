{"ast": null, "code": "module.exports = {\n  \"default\": require(\"core-js/library/fn/symbol\"),\n  __esModule: true\n};", "map": {"version": 3, "names": ["module", "exports", "require", "__esModule"], "sources": ["E:/indicator-qa-service/datafront/node_modules/babel-runtime/core-js/symbol.js"], "sourcesContent": ["module.exports = { \"default\": require(\"core-js/library/fn/symbol\"), __esModule: true };"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG;EAAE,SAAS,EAAEC,OAAO,CAAC,2BAA2B,CAAC;EAAEC,UAAU,EAAE;AAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}