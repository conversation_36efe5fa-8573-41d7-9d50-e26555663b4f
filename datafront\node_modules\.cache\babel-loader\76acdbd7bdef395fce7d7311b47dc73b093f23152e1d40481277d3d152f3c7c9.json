{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport ComponentModel from '../../model/Component.js';\nimport { copyLayoutParams, mergeLayoutParam } from '../../util/layout.js';\n;\n;\n;\nexport function setKeyInfoToNewElOption(resultItem, newElOption) {\n  var existElOption = resultItem.existing;\n  // Set id and type after id assigned.\n  newElOption.id = resultItem.keyInfo.id;\n  !newElOption.type && existElOption && (newElOption.type = existElOption.type);\n  // Set parent id if not specified\n  if (newElOption.parentId == null) {\n    var newElParentOption = newElOption.parentOption;\n    if (newElParentOption) {\n      newElOption.parentId = newElParentOption.id;\n    } else if (existElOption) {\n      newElOption.parentId = existElOption.parentId;\n    }\n  }\n  // Clear\n  newElOption.parentOption = null;\n}\nfunction isSetLoc(obj, props) {\n  var isSet;\n  zrUtil.each(props, function (prop) {\n    obj[prop] != null && obj[prop] !== 'auto' && (isSet = true);\n  });\n  return isSet;\n}\nfunction mergeNewElOptionToExist(existList, index, newElOption) {\n  // Update existing options, for `getOption` feature.\n  var newElOptCopy = zrUtil.extend({}, newElOption);\n  var existElOption = existList[index];\n  var $action = newElOption.$action || 'merge';\n  if ($action === 'merge') {\n    if (existElOption) {\n      if (process.env.NODE_ENV !== 'production') {\n        var newType = newElOption.type;\n        zrUtil.assert(!newType || existElOption.type === newType, 'Please set $action: \"replace\" to change `type`');\n      }\n      // We can ensure that newElOptCopy and existElOption are not\n      // the same object, so `merge` will not change newElOptCopy.\n      zrUtil.merge(existElOption, newElOptCopy, true);\n      // Rigid body, use ignoreSize.\n      mergeLayoutParam(existElOption, newElOptCopy, {\n        ignoreSize: true\n      });\n      // Will be used in render.\n      copyLayoutParams(newElOption, existElOption);\n      // Copy transition info to new option so it can be used in the transition.\n      // DO IT AFTER merge\n      copyTransitionInfo(newElOption, existElOption);\n      copyTransitionInfo(newElOption, existElOption, 'shape');\n      copyTransitionInfo(newElOption, existElOption, 'style');\n      copyTransitionInfo(newElOption, existElOption, 'extra');\n      // Copy clipPath\n      newElOption.clipPath = existElOption.clipPath;\n    } else {\n      existList[index] = newElOptCopy;\n    }\n  } else if ($action === 'replace') {\n    existList[index] = newElOptCopy;\n  } else if ($action === 'remove') {\n    // null will be cleaned later.\n    existElOption && (existList[index] = null);\n  }\n}\nvar TRANSITION_PROPS_TO_COPY = ['transition', 'enterFrom', 'leaveTo'];\nvar ROOT_TRANSITION_PROPS_TO_COPY = TRANSITION_PROPS_TO_COPY.concat(['enterAnimation', 'updateAnimation', 'leaveAnimation']);\nfunction copyTransitionInfo(target, source, targetProp) {\n  if (targetProp) {\n    if (!target[targetProp] && source[targetProp]) {\n      // TODO avoid creating this empty object when there is no transition configuration.\n      target[targetProp] = {};\n    }\n    target = target[targetProp];\n    source = source[targetProp];\n  }\n  if (!target || !source) {\n    return;\n  }\n  var props = targetProp ? TRANSITION_PROPS_TO_COPY : ROOT_TRANSITION_PROPS_TO_COPY;\n  for (var i = 0; i < props.length; i++) {\n    var prop = props[i];\n    if (target[prop] == null && source[prop] != null) {\n      target[prop] = source[prop];\n    }\n  }\n}\nfunction setLayoutInfoToExist(existItem, newElOption) {\n  if (!existItem) {\n    return;\n  }\n  existItem.hv = newElOption.hv = [\n  // Rigid body, don't care about `width`.\n  isSetLoc(newElOption, ['left', 'right']),\n  // Rigid body, don't care about `height`.\n  isSetLoc(newElOption, ['top', 'bottom'])];\n  // Give default group size. Otherwise layout error may occur.\n  if (existItem.type === 'group') {\n    var existingGroupOpt = existItem;\n    var newGroupOpt = newElOption;\n    existingGroupOpt.width == null && (existingGroupOpt.width = newGroupOpt.width = 0);\n    existingGroupOpt.height == null && (existingGroupOpt.height = newGroupOpt.height = 0);\n  }\n}\nvar GraphicComponentModel = /** @class */function (_super) {\n  __extends(GraphicComponentModel, _super);\n  function GraphicComponentModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphicComponentModel.type;\n    _this.preventAutoZ = true;\n    return _this;\n  }\n  GraphicComponentModel.prototype.mergeOption = function (option, ecModel) {\n    // Prevent default merge to elements\n    var elements = this.option.elements;\n    this.option.elements = null;\n    _super.prototype.mergeOption.call(this, option, ecModel);\n    this.option.elements = elements;\n  };\n  GraphicComponentModel.prototype.optionUpdated = function (newOption, isInit) {\n    var thisOption = this.option;\n    var newList = (isInit ? thisOption : newOption).elements;\n    var existList = thisOption.elements = isInit ? [] : thisOption.elements;\n    var flattenedList = [];\n    this._flatten(newList, flattenedList, null);\n    var mappingResult = modelUtil.mappingToExists(existList, flattenedList, 'normalMerge');\n    // Clear elOptionsToUpdate\n    var elOptionsToUpdate = this._elOptionsToUpdate = [];\n    zrUtil.each(mappingResult, function (resultItem, index) {\n      var newElOption = resultItem.newOption;\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.assert(zrUtil.isObject(newElOption) || resultItem.existing, 'Empty graphic option definition');\n      }\n      if (!newElOption) {\n        return;\n      }\n      elOptionsToUpdate.push(newElOption);\n      setKeyInfoToNewElOption(resultItem, newElOption);\n      mergeNewElOptionToExist(existList, index, newElOption);\n      setLayoutInfoToExist(existList[index], newElOption);\n    }, this);\n    // Clean\n    thisOption.elements = zrUtil.filter(existList, function (item) {\n      // $action should be volatile, otherwise option gotten from\n      // `getOption` will contain unexpected $action.\n      item && delete item.$action;\n      return item != null;\n    });\n  };\n  /**\r\n   * Convert\r\n   * [{\r\n   *  type: 'group',\r\n   *  id: 'xx',\r\n   *  children: [{type: 'circle'}, {type: 'polygon'}]\r\n   * }]\r\n   * to\r\n   * [\r\n   *  {type: 'group', id: 'xx'},\r\n   *  {type: 'circle', parentId: 'xx'},\r\n   *  {type: 'polygon', parentId: 'xx'}\r\n   * ]\r\n   */\n  GraphicComponentModel.prototype._flatten = function (optionList, result, parentOption) {\n    zrUtil.each(optionList, function (option) {\n      if (!option) {\n        return;\n      }\n      if (parentOption) {\n        option.parentOption = parentOption;\n      }\n      result.push(option);\n      var children = option.children;\n      // here we don't judge if option.type is `group`\n      // when new option doesn't provide `type`, it will cause that the children can't be updated.\n      if (children && children.length) {\n        this._flatten(children, result, option);\n      }\n      // Deleting for JSON output, and for not affecting group creation.\n      delete option.children;\n    }, this);\n  };\n  // FIXME\n  // Pass to view using payload? setOption has a payload?\n  GraphicComponentModel.prototype.useElOptionsToUpdate = function () {\n    var els = this._elOptionsToUpdate;\n    // Clear to avoid render duplicately when zooming.\n    this._elOptionsToUpdate = null;\n    return els;\n  };\n  GraphicComponentModel.type = 'graphic';\n  GraphicComponentModel.defaultOption = {\n    elements: []\n    // parentId: null\n  };\n  return GraphicComponentModel;\n}(ComponentModel);\nexport { GraphicComponentModel };", "map": {"version": 3, "names": ["__extends", "zrUtil", "modelUtil", "ComponentModel", "copyLayoutParams", "mergeLayoutParam", "setKeyInfoToNewElOption", "resultItem", "newElOption", "existElOption", "existing", "id", "keyInfo", "type", "parentId", "newElParentOption", "parentOption", "isSetLoc", "obj", "props", "isSet", "each", "prop", "mergeNewElOptionToExist", "existList", "index", "newElOptCopy", "extend", "$action", "process", "env", "NODE_ENV", "newType", "assert", "merge", "ignoreSize", "copyTransitionInfo", "clipPath", "TRANSITION_PROPS_TO_COPY", "ROOT_TRANSITION_PROPS_TO_COPY", "concat", "target", "source", "targetProp", "i", "length", "setLayoutInfoToExist", "existItem", "hv", "existingGroupOpt", "newGroupOpt", "width", "height", "GraphicComponentModel", "_super", "_this", "apply", "arguments", "preventAutoZ", "prototype", "mergeOption", "option", "ecModel", "elements", "call", "optionUpdated", "newOption", "isInit", "thisOption", "newList", "flattenedList", "_flatten", "mappingResult", "mappingToExists", "elOptionsToUpdate", "_elOptionsToUpdate", "isObject", "push", "filter", "item", "optionList", "result", "children", "useElOptionsToUpdate", "els", "defaultOption"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/graphic/GraphicModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport ComponentModel from '../../model/Component.js';\nimport { copyLayoutParams, mergeLayoutParam } from '../../util/layout.js';\n;\n;\n;\nexport function setKeyInfoToNewElOption(resultItem, newElOption) {\n  var existElOption = resultItem.existing;\n  // Set id and type after id assigned.\n  newElOption.id = resultItem.keyInfo.id;\n  !newElOption.type && existElOption && (newElOption.type = existElOption.type);\n  // Set parent id if not specified\n  if (newElOption.parentId == null) {\n    var newElParentOption = newElOption.parentOption;\n    if (newElParentOption) {\n      newElOption.parentId = newElParentOption.id;\n    } else if (existElOption) {\n      newElOption.parentId = existElOption.parentId;\n    }\n  }\n  // Clear\n  newElOption.parentOption = null;\n}\nfunction isSetLoc(obj, props) {\n  var isSet;\n  zrUtil.each(props, function (prop) {\n    obj[prop] != null && obj[prop] !== 'auto' && (isSet = true);\n  });\n  return isSet;\n}\nfunction mergeNewElOptionToExist(existList, index, newElOption) {\n  // Update existing options, for `getOption` feature.\n  var newElOptCopy = zrUtil.extend({}, newElOption);\n  var existElOption = existList[index];\n  var $action = newElOption.$action || 'merge';\n  if ($action === 'merge') {\n    if (existElOption) {\n      if (process.env.NODE_ENV !== 'production') {\n        var newType = newElOption.type;\n        zrUtil.assert(!newType || existElOption.type === newType, 'Please set $action: \"replace\" to change `type`');\n      }\n      // We can ensure that newElOptCopy and existElOption are not\n      // the same object, so `merge` will not change newElOptCopy.\n      zrUtil.merge(existElOption, newElOptCopy, true);\n      // Rigid body, use ignoreSize.\n      mergeLayoutParam(existElOption, newElOptCopy, {\n        ignoreSize: true\n      });\n      // Will be used in render.\n      copyLayoutParams(newElOption, existElOption);\n      // Copy transition info to new option so it can be used in the transition.\n      // DO IT AFTER merge\n      copyTransitionInfo(newElOption, existElOption);\n      copyTransitionInfo(newElOption, existElOption, 'shape');\n      copyTransitionInfo(newElOption, existElOption, 'style');\n      copyTransitionInfo(newElOption, existElOption, 'extra');\n      // Copy clipPath\n      newElOption.clipPath = existElOption.clipPath;\n    } else {\n      existList[index] = newElOptCopy;\n    }\n  } else if ($action === 'replace') {\n    existList[index] = newElOptCopy;\n  } else if ($action === 'remove') {\n    // null will be cleaned later.\n    existElOption && (existList[index] = null);\n  }\n}\nvar TRANSITION_PROPS_TO_COPY = ['transition', 'enterFrom', 'leaveTo'];\nvar ROOT_TRANSITION_PROPS_TO_COPY = TRANSITION_PROPS_TO_COPY.concat(['enterAnimation', 'updateAnimation', 'leaveAnimation']);\nfunction copyTransitionInfo(target, source, targetProp) {\n  if (targetProp) {\n    if (!target[targetProp] && source[targetProp]) {\n      // TODO avoid creating this empty object when there is no transition configuration.\n      target[targetProp] = {};\n    }\n    target = target[targetProp];\n    source = source[targetProp];\n  }\n  if (!target || !source) {\n    return;\n  }\n  var props = targetProp ? TRANSITION_PROPS_TO_COPY : ROOT_TRANSITION_PROPS_TO_COPY;\n  for (var i = 0; i < props.length; i++) {\n    var prop = props[i];\n    if (target[prop] == null && source[prop] != null) {\n      target[prop] = source[prop];\n    }\n  }\n}\nfunction setLayoutInfoToExist(existItem, newElOption) {\n  if (!existItem) {\n    return;\n  }\n  existItem.hv = newElOption.hv = [\n  // Rigid body, don't care about `width`.\n  isSetLoc(newElOption, ['left', 'right']),\n  // Rigid body, don't care about `height`.\n  isSetLoc(newElOption, ['top', 'bottom'])];\n  // Give default group size. Otherwise layout error may occur.\n  if (existItem.type === 'group') {\n    var existingGroupOpt = existItem;\n    var newGroupOpt = newElOption;\n    existingGroupOpt.width == null && (existingGroupOpt.width = newGroupOpt.width = 0);\n    existingGroupOpt.height == null && (existingGroupOpt.height = newGroupOpt.height = 0);\n  }\n}\nvar GraphicComponentModel = /** @class */function (_super) {\n  __extends(GraphicComponentModel, _super);\n  function GraphicComponentModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphicComponentModel.type;\n    _this.preventAutoZ = true;\n    return _this;\n  }\n  GraphicComponentModel.prototype.mergeOption = function (option, ecModel) {\n    // Prevent default merge to elements\n    var elements = this.option.elements;\n    this.option.elements = null;\n    _super.prototype.mergeOption.call(this, option, ecModel);\n    this.option.elements = elements;\n  };\n  GraphicComponentModel.prototype.optionUpdated = function (newOption, isInit) {\n    var thisOption = this.option;\n    var newList = (isInit ? thisOption : newOption).elements;\n    var existList = thisOption.elements = isInit ? [] : thisOption.elements;\n    var flattenedList = [];\n    this._flatten(newList, flattenedList, null);\n    var mappingResult = modelUtil.mappingToExists(existList, flattenedList, 'normalMerge');\n    // Clear elOptionsToUpdate\n    var elOptionsToUpdate = this._elOptionsToUpdate = [];\n    zrUtil.each(mappingResult, function (resultItem, index) {\n      var newElOption = resultItem.newOption;\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.assert(zrUtil.isObject(newElOption) || resultItem.existing, 'Empty graphic option definition');\n      }\n      if (!newElOption) {\n        return;\n      }\n      elOptionsToUpdate.push(newElOption);\n      setKeyInfoToNewElOption(resultItem, newElOption);\n      mergeNewElOptionToExist(existList, index, newElOption);\n      setLayoutInfoToExist(existList[index], newElOption);\n    }, this);\n    // Clean\n    thisOption.elements = zrUtil.filter(existList, function (item) {\n      // $action should be volatile, otherwise option gotten from\n      // `getOption` will contain unexpected $action.\n      item && delete item.$action;\n      return item != null;\n    });\n  };\n  /**\r\n   * Convert\r\n   * [{\r\n   *  type: 'group',\r\n   *  id: 'xx',\r\n   *  children: [{type: 'circle'}, {type: 'polygon'}]\r\n   * }]\r\n   * to\r\n   * [\r\n   *  {type: 'group', id: 'xx'},\r\n   *  {type: 'circle', parentId: 'xx'},\r\n   *  {type: 'polygon', parentId: 'xx'}\r\n   * ]\r\n   */\n  GraphicComponentModel.prototype._flatten = function (optionList, result, parentOption) {\n    zrUtil.each(optionList, function (option) {\n      if (!option) {\n        return;\n      }\n      if (parentOption) {\n        option.parentOption = parentOption;\n      }\n      result.push(option);\n      var children = option.children;\n      // here we don't judge if option.type is `group`\n      // when new option doesn't provide `type`, it will cause that the children can't be updated.\n      if (children && children.length) {\n        this._flatten(children, result, option);\n      }\n      // Deleting for JSON output, and for not affecting group creation.\n      delete option.children;\n    }, this);\n  };\n  // FIXME\n  // Pass to view using payload? setOption has a payload?\n  GraphicComponentModel.prototype.useElOptionsToUpdate = function () {\n    var els = this._elOptionsToUpdate;\n    // Clear to avoid render duplicately when zooming.\n    this._elOptionsToUpdate = null;\n    return els;\n  };\n  GraphicComponentModel.type = 'graphic';\n  GraphicComponentModel.defaultOption = {\n    elements: []\n    // parentId: null\n  };\n  return GraphicComponentModel;\n}(ComponentModel);\nexport { GraphicComponentModel };"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,sBAAsB;AACzE;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,UAAU,EAAEC,WAAW,EAAE;EAC/D,IAAIC,aAAa,GAAGF,UAAU,CAACG,QAAQ;EACvC;EACAF,WAAW,CAACG,EAAE,GAAGJ,UAAU,CAACK,OAAO,CAACD,EAAE;EACtC,CAACH,WAAW,CAACK,IAAI,IAAIJ,aAAa,KAAKD,WAAW,CAACK,IAAI,GAAGJ,aAAa,CAACI,IAAI,CAAC;EAC7E;EACA,IAAIL,WAAW,CAACM,QAAQ,IAAI,IAAI,EAAE;IAChC,IAAIC,iBAAiB,GAAGP,WAAW,CAACQ,YAAY;IAChD,IAAID,iBAAiB,EAAE;MACrBP,WAAW,CAACM,QAAQ,GAAGC,iBAAiB,CAACJ,EAAE;IAC7C,CAAC,MAAM,IAAIF,aAAa,EAAE;MACxBD,WAAW,CAACM,QAAQ,GAAGL,aAAa,CAACK,QAAQ;IAC/C;EACF;EACA;EACAN,WAAW,CAACQ,YAAY,GAAG,IAAI;AACjC;AACA,SAASC,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAC5B,IAAIC,KAAK;EACTnB,MAAM,CAACoB,IAAI,CAACF,KAAK,EAAE,UAAUG,IAAI,EAAE;IACjCJ,GAAG,CAACI,IAAI,CAAC,IAAI,IAAI,IAAIJ,GAAG,CAACI,IAAI,CAAC,KAAK,MAAM,KAAKF,KAAK,GAAG,IAAI,CAAC;EAC7D,CAAC,CAAC;EACF,OAAOA,KAAK;AACd;AACA,SAASG,uBAAuBA,CAACC,SAAS,EAAEC,KAAK,EAAEjB,WAAW,EAAE;EAC9D;EACA,IAAIkB,YAAY,GAAGzB,MAAM,CAAC0B,MAAM,CAAC,CAAC,CAAC,EAAEnB,WAAW,CAAC;EACjD,IAAIC,aAAa,GAAGe,SAAS,CAACC,KAAK,CAAC;EACpC,IAAIG,OAAO,GAAGpB,WAAW,CAACoB,OAAO,IAAI,OAAO;EAC5C,IAAIA,OAAO,KAAK,OAAO,EAAE;IACvB,IAAInB,aAAa,EAAE;MACjB,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIC,OAAO,GAAGxB,WAAW,CAACK,IAAI;QAC9BZ,MAAM,CAACgC,MAAM,CAAC,CAACD,OAAO,IAAIvB,aAAa,CAACI,IAAI,KAAKmB,OAAO,EAAE,gDAAgD,CAAC;MAC7G;MACA;MACA;MACA/B,MAAM,CAACiC,KAAK,CAACzB,aAAa,EAAEiB,YAAY,EAAE,IAAI,CAAC;MAC/C;MACArB,gBAAgB,CAACI,aAAa,EAAEiB,YAAY,EAAE;QAC5CS,UAAU,EAAE;MACd,CAAC,CAAC;MACF;MACA/B,gBAAgB,CAACI,WAAW,EAAEC,aAAa,CAAC;MAC5C;MACA;MACA2B,kBAAkB,CAAC5B,WAAW,EAAEC,aAAa,CAAC;MAC9C2B,kBAAkB,CAAC5B,WAAW,EAAEC,aAAa,EAAE,OAAO,CAAC;MACvD2B,kBAAkB,CAAC5B,WAAW,EAAEC,aAAa,EAAE,OAAO,CAAC;MACvD2B,kBAAkB,CAAC5B,WAAW,EAAEC,aAAa,EAAE,OAAO,CAAC;MACvD;MACAD,WAAW,CAAC6B,QAAQ,GAAG5B,aAAa,CAAC4B,QAAQ;IAC/C,CAAC,MAAM;MACLb,SAAS,CAACC,KAAK,CAAC,GAAGC,YAAY;IACjC;EACF,CAAC,MAAM,IAAIE,OAAO,KAAK,SAAS,EAAE;IAChCJ,SAAS,CAACC,KAAK,CAAC,GAAGC,YAAY;EACjC,CAAC,MAAM,IAAIE,OAAO,KAAK,QAAQ,EAAE;IAC/B;IACAnB,aAAa,KAAKe,SAAS,CAACC,KAAK,CAAC,GAAG,IAAI,CAAC;EAC5C;AACF;AACA,IAAIa,wBAAwB,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,SAAS,CAAC;AACrE,IAAIC,6BAA6B,GAAGD,wBAAwB,CAACE,MAAM,CAAC,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;AAC5H,SAASJ,kBAAkBA,CAACK,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAE;EACtD,IAAIA,UAAU,EAAE;IACd,IAAI,CAACF,MAAM,CAACE,UAAU,CAAC,IAAID,MAAM,CAACC,UAAU,CAAC,EAAE;MAC7C;MACAF,MAAM,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC;IACzB;IACAF,MAAM,GAAGA,MAAM,CAACE,UAAU,CAAC;IAC3BD,MAAM,GAAGA,MAAM,CAACC,UAAU,CAAC;EAC7B;EACA,IAAI,CAACF,MAAM,IAAI,CAACC,MAAM,EAAE;IACtB;EACF;EACA,IAAIvB,KAAK,GAAGwB,UAAU,GAAGL,wBAAwB,GAAGC,6BAA6B;EACjF,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,KAAK,CAAC0B,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAItB,IAAI,GAAGH,KAAK,CAACyB,CAAC,CAAC;IACnB,IAAIH,MAAM,CAACnB,IAAI,CAAC,IAAI,IAAI,IAAIoB,MAAM,CAACpB,IAAI,CAAC,IAAI,IAAI,EAAE;MAChDmB,MAAM,CAACnB,IAAI,CAAC,GAAGoB,MAAM,CAACpB,IAAI,CAAC;IAC7B;EACF;AACF;AACA,SAASwB,oBAAoBA,CAACC,SAAS,EAAEvC,WAAW,EAAE;EACpD,IAAI,CAACuC,SAAS,EAAE;IACd;EACF;EACAA,SAAS,CAACC,EAAE,GAAGxC,WAAW,CAACwC,EAAE,GAAG;EAChC;EACA/B,QAAQ,CAACT,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;EACxC;EACAS,QAAQ,CAACT,WAAW,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;EACzC;EACA,IAAIuC,SAAS,CAAClC,IAAI,KAAK,OAAO,EAAE;IAC9B,IAAIoC,gBAAgB,GAAGF,SAAS;IAChC,IAAIG,WAAW,GAAG1C,WAAW;IAC7ByC,gBAAgB,CAACE,KAAK,IAAI,IAAI,KAAKF,gBAAgB,CAACE,KAAK,GAAGD,WAAW,CAACC,KAAK,GAAG,CAAC,CAAC;IAClFF,gBAAgB,CAACG,MAAM,IAAI,IAAI,KAAKH,gBAAgB,CAACG,MAAM,GAAGF,WAAW,CAACE,MAAM,GAAG,CAAC,CAAC;EACvF;AACF;AACA,IAAIC,qBAAqB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACzDtD,SAAS,CAACqD,qBAAqB,EAAEC,MAAM,CAAC;EACxC,SAASD,qBAAqBA,CAAA,EAAG;IAC/B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAAC1C,IAAI,GAAGwC,qBAAqB,CAACxC,IAAI;IACvC0C,KAAK,CAACG,YAAY,GAAG,IAAI;IACzB,OAAOH,KAAK;EACd;EACAF,qBAAqB,CAACM,SAAS,CAACC,WAAW,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACvE;IACA,IAAIC,QAAQ,GAAG,IAAI,CAACF,MAAM,CAACE,QAAQ;IACnC,IAAI,CAACF,MAAM,CAACE,QAAQ,GAAG,IAAI;IAC3BT,MAAM,CAACK,SAAS,CAACC,WAAW,CAACI,IAAI,CAAC,IAAI,EAAEH,MAAM,EAAEC,OAAO,CAAC;IACxD,IAAI,CAACD,MAAM,CAACE,QAAQ,GAAGA,QAAQ;EACjC,CAAC;EACDV,qBAAqB,CAACM,SAAS,CAACM,aAAa,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;IAC3E,IAAIC,UAAU,GAAG,IAAI,CAACP,MAAM;IAC5B,IAAIQ,OAAO,GAAG,CAACF,MAAM,GAAGC,UAAU,GAAGF,SAAS,EAAEH,QAAQ;IACxD,IAAIvC,SAAS,GAAG4C,UAAU,CAACL,QAAQ,GAAGI,MAAM,GAAG,EAAE,GAAGC,UAAU,CAACL,QAAQ;IACvE,IAAIO,aAAa,GAAG,EAAE;IACtB,IAAI,CAACC,QAAQ,CAACF,OAAO,EAAEC,aAAa,EAAE,IAAI,CAAC;IAC3C,IAAIE,aAAa,GAAGtE,SAAS,CAACuE,eAAe,CAACjD,SAAS,EAAE8C,aAAa,EAAE,aAAa,CAAC;IACtF;IACA,IAAII,iBAAiB,GAAG,IAAI,CAACC,kBAAkB,GAAG,EAAE;IACpD1E,MAAM,CAACoB,IAAI,CAACmD,aAAa,EAAE,UAAUjE,UAAU,EAAEkB,KAAK,EAAE;MACtD,IAAIjB,WAAW,GAAGD,UAAU,CAAC2D,SAAS;MACtC,IAAIrC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC9B,MAAM,CAACgC,MAAM,CAAChC,MAAM,CAAC2E,QAAQ,CAACpE,WAAW,CAAC,IAAID,UAAU,CAACG,QAAQ,EAAE,iCAAiC,CAAC;MACvG;MACA,IAAI,CAACF,WAAW,EAAE;QAChB;MACF;MACAkE,iBAAiB,CAACG,IAAI,CAACrE,WAAW,CAAC;MACnCF,uBAAuB,CAACC,UAAU,EAAEC,WAAW,CAAC;MAChDe,uBAAuB,CAACC,SAAS,EAAEC,KAAK,EAAEjB,WAAW,CAAC;MACtDsC,oBAAoB,CAACtB,SAAS,CAACC,KAAK,CAAC,EAAEjB,WAAW,CAAC;IACrD,CAAC,EAAE,IAAI,CAAC;IACR;IACA4D,UAAU,CAACL,QAAQ,GAAG9D,MAAM,CAAC6E,MAAM,CAACtD,SAAS,EAAE,UAAUuD,IAAI,EAAE;MAC7D;MACA;MACAA,IAAI,IAAI,OAAOA,IAAI,CAACnD,OAAO;MAC3B,OAAOmD,IAAI,IAAI,IAAI;IACrB,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE1B,qBAAqB,CAACM,SAAS,CAACY,QAAQ,GAAG,UAAUS,UAAU,EAAEC,MAAM,EAAEjE,YAAY,EAAE;IACrFf,MAAM,CAACoB,IAAI,CAAC2D,UAAU,EAAE,UAAUnB,MAAM,EAAE;MACxC,IAAI,CAACA,MAAM,EAAE;QACX;MACF;MACA,IAAI7C,YAAY,EAAE;QAChB6C,MAAM,CAAC7C,YAAY,GAAGA,YAAY;MACpC;MACAiE,MAAM,CAACJ,IAAI,CAAChB,MAAM,CAAC;MACnB,IAAIqB,QAAQ,GAAGrB,MAAM,CAACqB,QAAQ;MAC9B;MACA;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACrC,MAAM,EAAE;QAC/B,IAAI,CAAC0B,QAAQ,CAACW,QAAQ,EAAED,MAAM,EAAEpB,MAAM,CAAC;MACzC;MACA;MACA,OAAOA,MAAM,CAACqB,QAAQ;IACxB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;EACA;EACA7B,qBAAqB,CAACM,SAAS,CAACwB,oBAAoB,GAAG,YAAY;IACjE,IAAIC,GAAG,GAAG,IAAI,CAACT,kBAAkB;IACjC;IACA,IAAI,CAACA,kBAAkB,GAAG,IAAI;IAC9B,OAAOS,GAAG;EACZ,CAAC;EACD/B,qBAAqB,CAACxC,IAAI,GAAG,SAAS;EACtCwC,qBAAqB,CAACgC,aAAa,GAAG;IACpCtB,QAAQ,EAAE;IACV;EACF,CAAC;EACD,OAAOV,qBAAqB;AAC9B,CAAC,CAAClD,cAAc,CAAC;AACjB,SAASkD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}