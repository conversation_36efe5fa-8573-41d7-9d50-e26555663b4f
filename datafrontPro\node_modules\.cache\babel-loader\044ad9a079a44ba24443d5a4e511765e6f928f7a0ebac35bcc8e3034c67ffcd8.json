{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Single coordinate system creator.\r\n */\nimport Single, { singleDimensions } from './Single.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\n/**\r\n * Create single coordinate system and inject it into seriesModel.\r\n */\nfunction create(ecModel, api) {\n  var singles = [];\n  ecModel.eachComponent('singleAxis', function (axisModel, idx) {\n    var single = new Single(axisModel, ecModel, api);\n    single.name = 'single_' + idx;\n    single.resize(axisModel, api);\n    axisModel.coordinateSystem = single;\n    singles.push(single);\n  });\n  ecModel.eachSeries(function (seriesModel) {\n    if (seriesModel.get('coordinateSystem') === 'singleAxis') {\n      var singleAxisModel = seriesModel.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n      seriesModel.coordinateSystem = singleAxisModel && singleAxisModel.coordinateSystem;\n    }\n  });\n  return singles;\n}\nvar singleCreator = {\n  create: create,\n  dimensions: singleDimensions\n};\nexport default singleCreator;", "map": {"version": 3, "names": ["Single", "singleDimensions", "SINGLE_REFERRING", "create", "ecModel", "api", "singles", "eachComponent", "axisModel", "idx", "single", "name", "resize", "coordinateSystem", "push", "eachSeries", "seriesModel", "get", "singleAxisModel", "getReferringComponents", "models", "singleCreator", "dimensions"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/coord/single/singleCreator.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Single coordinate system creator.\r\n */\nimport Single, { singleDimensions } from './Single.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\n/**\r\n * Create single coordinate system and inject it into seriesModel.\r\n */\nfunction create(ecModel, api) {\n  var singles = [];\n  ecModel.eachComponent('singleAxis', function (axisModel, idx) {\n    var single = new Single(axisModel, ecModel, api);\n    single.name = 'single_' + idx;\n    single.resize(axisModel, api);\n    axisModel.coordinateSystem = single;\n    singles.push(single);\n  });\n  ecModel.eachSeries(function (seriesModel) {\n    if (seriesModel.get('coordinateSystem') === 'singleAxis') {\n      var singleAxisModel = seriesModel.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n      seriesModel.coordinateSystem = singleAxisModel && singleAxisModel.coordinateSystem;\n    }\n  });\n  return singles;\n}\nvar singleCreator = {\n  create: create,\n  dimensions: singleDimensions\n};\nexport default singleCreator;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,MAAM,IAAIC,gBAAgB,QAAQ,aAAa;AACtD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD;AACA;AACA;AACA,SAASC,MAAMA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAC5B,IAAIC,OAAO,GAAG,EAAE;EAChBF,OAAO,CAACG,aAAa,CAAC,YAAY,EAAE,UAAUC,SAAS,EAAEC,GAAG,EAAE;IAC5D,IAAIC,MAAM,GAAG,IAAIV,MAAM,CAACQ,SAAS,EAAEJ,OAAO,EAAEC,GAAG,CAAC;IAChDK,MAAM,CAACC,IAAI,GAAG,SAAS,GAAGF,GAAG;IAC7BC,MAAM,CAACE,MAAM,CAACJ,SAAS,EAAEH,GAAG,CAAC;IAC7BG,SAAS,CAACK,gBAAgB,GAAGH,MAAM;IACnCJ,OAAO,CAACQ,IAAI,CAACJ,MAAM,CAAC;EACtB,CAAC,CAAC;EACFN,OAAO,CAACW,UAAU,CAAC,UAAUC,WAAW,EAAE;IACxC,IAAIA,WAAW,CAACC,GAAG,CAAC,kBAAkB,CAAC,KAAK,YAAY,EAAE;MACxD,IAAIC,eAAe,GAAGF,WAAW,CAACG,sBAAsB,CAAC,YAAY,EAAEjB,gBAAgB,CAAC,CAACkB,MAAM,CAAC,CAAC,CAAC;MAClGJ,WAAW,CAACH,gBAAgB,GAAGK,eAAe,IAAIA,eAAe,CAACL,gBAAgB;IACpF;EACF,CAAC,CAAC;EACF,OAAOP,OAAO;AAChB;AACA,IAAIe,aAAa,GAAG;EAClBlB,MAAM,EAAEA,MAAM;EACdmB,UAAU,EAAErB;AACd,CAAC;AACD,eAAeoB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}