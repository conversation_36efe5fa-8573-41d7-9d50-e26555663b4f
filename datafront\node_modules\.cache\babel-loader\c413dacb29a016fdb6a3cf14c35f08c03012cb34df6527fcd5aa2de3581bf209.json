{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar IRRELEVANT_EXCLUDES = {\n  'axisPointer': 1,\n  'tooltip': 1,\n  'brush': 1\n};\n/**\r\n * Avoid that: mouse click on a elements that is over geo or graph,\r\n * but roam is triggered.\r\n */\nexport function onIrrelevantElement(e, api, targetCoordSysModel) {\n  var model = api.getComponentByElement(e.topTarget);\n  // If model is axisModel, it works only if it is injected with coordinateSystem.\n  var coordSys = model && model.coordinateSystem;\n  return model && model !== targetCoordSysModel && !IRRELEVANT_EXCLUDES.hasOwnProperty(model.mainType) && coordSys && coordSys.model !== targetCoordSysModel;\n}", "map": {"version": 3, "names": ["IRRELEVANT_EXCLUDES", "onIrrelevantElement", "e", "api", "targetCoordSysModel", "model", "getComponentByElement", "topTarget", "coordSys", "coordinateSystem", "hasOwnProperty", "mainType"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/helper/cursorHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar IRRELEVANT_EXCLUDES = {\n  'axisPointer': 1,\n  'tooltip': 1,\n  'brush': 1\n};\n/**\r\n * Avoid that: mouse click on a elements that is over geo or graph,\r\n * but roam is triggered.\r\n */\nexport function onIrrelevantElement(e, api, targetCoordSysModel) {\n  var model = api.getComponentByElement(e.topTarget);\n  // If model is axisModel, it works only if it is injected with coordinateSystem.\n  var coordSys = model && model.coordinateSystem;\n  return model && model !== targetCoordSysModel && !IRRELEVANT_EXCLUDES.hasOwnProperty(model.mainType) && coordSys && coordSys.model !== targetCoordSysModel;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,mBAAmB,GAAG;EACxB,aAAa,EAAE,CAAC;EAChB,SAAS,EAAE,CAAC;EACZ,OAAO,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,OAAO,SAASC,mBAAmBA,CAACC,CAAC,EAAEC,GAAG,EAAEC,mBAAmB,EAAE;EAC/D,IAAIC,KAAK,GAAGF,GAAG,CAACG,qBAAqB,CAACJ,CAAC,CAACK,SAAS,CAAC;EAClD;EACA,IAAIC,QAAQ,GAAGH,KAAK,IAAIA,KAAK,CAACI,gBAAgB;EAC9C,OAAOJ,KAAK,IAAIA,KAAK,KAAKD,mBAAmB,IAAI,CAACJ,mBAAmB,CAACU,cAAc,CAACL,KAAK,CAACM,QAAQ,CAAC,IAAIH,QAAQ,IAAIA,QAAQ,CAACH,KAAK,KAAKD,mBAAmB;AAC5J", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}