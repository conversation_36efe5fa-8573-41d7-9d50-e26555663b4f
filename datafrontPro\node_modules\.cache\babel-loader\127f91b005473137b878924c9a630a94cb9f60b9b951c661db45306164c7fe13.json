{"ast": null, "code": "import validate from './validate.js';\nfunction version(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n  return parseInt(uuid.slice(14, 15), 16);\n}\nexport default version;", "map": {"version": 3, "names": ["validate", "version", "uuid", "TypeError", "parseInt", "slice"], "sources": ["E:/AllProject/datafront/node_modules/uuid/dist/esm-browser/version.js"], "sourcesContent": ["import validate from './validate.js';\nfunction version(uuid) {\n    if (!validate(uuid)) {\n        throw TypeError('Invalid UUID');\n    }\n    return parseInt(uuid.slice(14, 15), 16);\n}\nexport default version;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,SAASC,OAAOA,CAACC,IAAI,EAAE;EACnB,IAAI,CAACF,QAAQ,CAACE,IAAI,CAAC,EAAE;IACjB,MAAMC,SAAS,CAAC,cAAc,CAAC;EACnC;EACA,OAAOC,QAAQ,CAACF,IAAI,CAACG,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;AAC3C;AACA,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}