# shebang-regex [![Build Status](https://travis-ci.org/sindresorhus/shebang-regex.svg?branch=master)](https://travis-ci.org/sindresorhus/shebang-regex)

> Regular expression for matching a [shebang](https://en.wikipedia.org/wiki/Shebang_(Unix))


## Install

```
$ npm install --save shebang-regex
```


## Usage

```js
var shebangRegex = require('shebang-regex');
var str = '#!/usr/bin/env node\nconsole.log("unicorns");';

shebangRegex.test(str);
//=> true

shebangRegex.exec(str)[0];
//=> '#!/usr/bin/env node'
```


## License

MIT © [Sindre Sorhus](http://sindresorhus.com)
