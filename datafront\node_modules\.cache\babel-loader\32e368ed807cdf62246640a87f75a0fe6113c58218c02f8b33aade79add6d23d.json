{"ast": null, "code": "import axios from 'axios';\nexport const dataApi = {\n  getAllTables() {\n    return axios.post('http://localhost:8080/api/dataease/datasets', {\n      busiFlag: \"dataset\"\n    });\n  },\n  getDatasetDetail(datasetId, needAllData = false, customHeaders = {}) {\n    console.log('调用getDatasetDetail, ID:', datasetId, '参数:', needAllData);\n\n    // 默认headers\n    const defaultHeaders = {\n      'Content-Type': 'application/json',\n      'X-DE-TOKEN': localStorage.getItem('de_token') || '',\n      'out_auth_platform': 'default'\n    };\n\n    // 合并默认headers和自定义headers\n    const headers = {\n      ...defaultHeaders,\n      ...customHeaders\n    };\n\n    // 打印完整请求信息\n    console.log('请求URL:', `http://localhost:8080/api/dataease/dataset/detail/${datasetId}`);\n    console.log('请求Headers:', headers);\n    return axios.get(`http://localhost:8080/api/dataease/dataset/detail/${datasetId}`, {\n      params: {\n        needAllData\n      },\n      headers: headers\n    }).catch(error => {\n      console.error('获取数据集详情失败:', error);\n      throw error;\n    });\n  },\n  getTableSample(tableCode) {\n    return axios.get(`/data/tables/${tableCode}/sample`);\n  },\n  queryByQuestion(tableCode, question) {\n    return axios.get('/analytics/query', {\n      params: {\n        tableCode,\n        question\n      }\n    });\n  },\n  // 新增：从Redis获取图表数据的API\n  getChartDataById(id) {\n    return axios.get(`http://localhost:8080/api/chart/data/${id}`);\n  }\n};\nexport const getData = async data => {\n  console.log('调用getData API，请求参数:', data);\n\n  // 如果传入的配置本身就包含完整数据，直接返回格式化的结果\n  if (data.data) {\n    console.log('配置中已包含数据，直接返回');\n    return {\n      code: 0,\n      msg: 'success',\n      data: data\n    };\n  }\n\n  // 添加测试模式，如果请求参数包含test-dataset，则返回模拟数据\n  if (data.datasetId === 'test-dataset' || data.tableId === 'test-table') {\n    console.log('使用测试模式，返回模拟数据');\n    return getMockChartData(data.type);\n  }\n  try {\n    // 如果是直接传入的完整数据对象（从AI响应中提取的）\n    if (data.code === 0 && data.data) {\n      console.log('检测到完整API响应格式，直接返回');\n      return data;\n    }\n\n    // 如果包含chartDataId字段，使用专门的API获取数据\n    if (data.chartDataId) {\n      console.log('检测到chartDataId，从Redis获取数据:', data.chartDataId);\n      const response = await dataApi.getChartDataById(data.chartDataId);\n      console.log('Redis数据获取结果:', response);\n      return response.data;\n    }\n\n    // 正常API调用\n    console.log('发起API请求:', data);\n    const response = await axios.post('/api/chartData/getData-1', data);\n    console.log('getData API响应结果:', response);\n\n    // 检查响应格式\n    if (response.data) {\n      console.log('API返回数据:', response.data);\n      return response.data;\n    } else {\n      console.error('响应数据为空');\n      return {\n        code: -1,\n        msg: '响应数据为空',\n        data: null\n      };\n    }\n  } catch (error) {\n    console.error('getData API调用出错:', error);\n    return {\n      code: -1,\n      msg: error.message,\n      data: null\n    };\n  }\n};\n\n// 生成模拟图表数据，用于测试\nfunction getMockChartData(chartType) {\n  const mockData = {\n    code: 0,\n    msg: 'success',\n    data: {\n      type: chartType || 'bar',\n      data: [{\n        field: '类别A',\n        value: 120\n      }, {\n        field: '类别B',\n        value: 200\n      }, {\n        field: '类别C',\n        value: 150\n      }, {\n        field: '类别D',\n        value: 80\n      }, {\n        field: '类别E',\n        value: 170\n      }],\n      metrics: [{\n        name: '销售额'\n      }]\n    }\n  };\n  console.log('生成的模拟数据:', mockData);\n  return mockData;\n}", "map": {"version": 3, "names": ["axios", "dataApi", "getAllTables", "post", "busiFlag", "getDatasetDetail", "datasetId", "needAllData", "customHeaders", "console", "log", "defaultHeaders", "localStorage", "getItem", "headers", "get", "params", "catch", "error", "getTableSample", "tableCode", "queryByQuestion", "question", "getChartDataById", "id", "getData", "data", "code", "msg", "tableId", "getMockChartData", "type", "chartDataId", "response", "message", "chartType", "mockData", "field", "value", "metrics", "name"], "sources": ["D:/FastBI/datafront/src/api/index.js"], "sourcesContent": ["import axios from 'axios'\n\nexport const dataApi = {\n  getAllTables() {\n    return axios.post('http://localhost:8080/api/dataease/datasets',{\n      busiFlag:\"dataset\"\n    })\n  },\n  getDatasetDetail(datasetId, needAllData = false, customHeaders = {}) {\n    console.log('调用getDatasetDetail, ID:', datasetId, '参数:', needAllData);\n    \n    // 默认headers\n    const defaultHeaders = {\n      'Content-Type': 'application/json',\n      'X-DE-TOKEN': localStorage.getItem('de_token') || '',\n      'out_auth_platform': 'default'\n    };\n    \n    // 合并默认headers和自定义headers\n    const headers = { ...defaultHeaders, ...customHeaders };\n    \n    // 打印完整请求信息\n    console.log('请求URL:', `http://localhost:8080/api/dataease/dataset/detail/${datasetId}`);\n    console.log('请求Headers:', headers);\n    \n    return axios.get(`http://localhost:8080/api/dataease/dataset/detail/${datasetId}`, {\n      params: { needAllData },\n      headers: headers\n    }).catch(error => {\n      console.error('获取数据集详情失败:', error);\n      throw error;\n    });\n    },\n  getTableSample(tableCode) {\n    return axios.get(`/data/tables/${tableCode}/sample`)\n  },\n  queryByQuestion(tableCode, question) {\n    return axios.get('/analytics/query', {\n      params: {\n        tableCode,\n        question\n      }\n    })\n  },\n  // 新增：从Redis获取图表数据的API\n  getChartDataById(id) {\n    return axios.get(`http://localhost:8080/api/chart/data/${id}`)\n  }\n}\n\nexport const getData = async (data) => {\n  console.log('调用getData API，请求参数:', data);\n  \n  // 如果传入的配置本身就包含完整数据，直接返回格式化的结果\n  if (data.data) {\n    console.log('配置中已包含数据，直接返回');\n    return {\n      code: 0,\n      msg: 'success',\n      data: data\n    };\n  }\n  \n  // 添加测试模式，如果请求参数包含test-dataset，则返回模拟数据\n  if (data.datasetId === 'test-dataset' || data.tableId === 'test-table') {\n    console.log('使用测试模式，返回模拟数据');\n    return getMockChartData(data.type);\n  }\n  \n  try {\n    // 如果是直接传入的完整数据对象（从AI响应中提取的）\n    if (data.code === 0 && data.data) {\n      console.log('检测到完整API响应格式，直接返回');\n      return data;\n    }\n    \n    // 如果包含chartDataId字段，使用专门的API获取数据\n    if (data.chartDataId) {\n      console.log('检测到chartDataId，从Redis获取数据:', data.chartDataId);\n      const response = await dataApi.getChartDataById(data.chartDataId);\n      console.log('Redis数据获取结果:', response);\n      return response.data;\n    }\n    \n    // 正常API调用\n    console.log('发起API请求:', data);\n    const response = await axios.post('/api/chartData/getData-1', data);\n    console.log('getData API响应结果:', response);\n    \n    // 检查响应格式\n    if (response.data) {\n      console.log('API返回数据:', response.data);\n      return response.data;\n    } else {\n      console.error('响应数据为空');\n      return { code: -1, msg: '响应数据为空', data: null };\n    }\n  } catch (error) {\n    console.error('getData API调用出错:', error);\n    return { code: -1, msg: error.message, data: null };\n    }\n}\n\n// 生成模拟图表数据，用于测试\nfunction getMockChartData(chartType) {\n  const mockData = {\n    code: 0,\n    msg: 'success',\n    data: {\n      type: chartType || 'bar',\n      data: [\n        { field: '类别A', value: 120 },\n        { field: '类别B', value: 200 },\n        { field: '类别C', value: 150 },\n        { field: '类别D', value: 80 },\n        { field: '类别E', value: 170 }\n      ],\n      metrics: [{ name: '销售额' }]\n    }\n  };\n  \n  console.log('生成的模拟数据:', mockData);\n  return mockData;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,MAAMC,OAAO,GAAG;EACrBC,YAAYA,CAAA,EAAG;IACb,OAAOF,KAAK,CAACG,IAAI,CAAC,6CAA6C,EAAC;MAC9DC,QAAQ,EAAC;IACX,CAAC,CAAC;EACJ,CAAC;EACDC,gBAAgBA,CAACC,SAAS,EAAEC,WAAW,GAAG,KAAK,EAAEC,aAAa,GAAG,CAAC,CAAC,EAAE;IACnEC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,SAAS,EAAE,KAAK,EAAEC,WAAW,CAAC;;IAErE;IACA,MAAMI,cAAc,GAAG;MACrB,cAAc,EAAE,kBAAkB;MAClC,YAAY,EAAEC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;MACpD,mBAAmB,EAAE;IACvB,CAAC;;IAED;IACA,MAAMC,OAAO,GAAG;MAAE,GAAGH,cAAc;MAAE,GAAGH;IAAc,CAAC;;IAEvD;IACAC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,qDAAqDJ,SAAS,EAAE,CAAC;IACvFG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEI,OAAO,CAAC;IAElC,OAAOd,KAAK,CAACe,GAAG,CAAC,qDAAqDT,SAAS,EAAE,EAAE;MACjFU,MAAM,EAAE;QAAET;MAAY,CAAC;MACvBO,OAAO,EAAEA;IACX,CAAC,CAAC,CAACG,KAAK,CAACC,KAAK,IAAI;MAChBT,OAAO,CAACS,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAMA,KAAK;IACb,CAAC,CAAC;EACF,CAAC;EACHC,cAAcA,CAACC,SAAS,EAAE;IACxB,OAAOpB,KAAK,CAACe,GAAG,CAAC,gBAAgBK,SAAS,SAAS,CAAC;EACtD,CAAC;EACDC,eAAeA,CAACD,SAAS,EAAEE,QAAQ,EAAE;IACnC,OAAOtB,KAAK,CAACe,GAAG,CAAC,kBAAkB,EAAE;MACnCC,MAAM,EAAE;QACNI,SAAS;QACTE;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACnB,OAAOxB,KAAK,CAACe,GAAG,CAAC,wCAAwCS,EAAE,EAAE,CAAC;EAChE;AACF,CAAC;AAED,OAAO,MAAMC,OAAO,GAAG,MAAOC,IAAI,IAAK;EACrCjB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgB,IAAI,CAAC;;EAExC;EACA,IAAIA,IAAI,CAACA,IAAI,EAAE;IACbjB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B,OAAO;MACLiB,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,SAAS;MACdF,IAAI,EAAEA;IACR,CAAC;EACH;;EAEA;EACA,IAAIA,IAAI,CAACpB,SAAS,KAAK,cAAc,IAAIoB,IAAI,CAACG,OAAO,KAAK,YAAY,EAAE;IACtEpB,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B,OAAOoB,gBAAgB,CAACJ,IAAI,CAACK,IAAI,CAAC;EACpC;EAEA,IAAI;IACF;IACA,IAAIL,IAAI,CAACC,IAAI,KAAK,CAAC,IAAID,IAAI,CAACA,IAAI,EAAE;MAChCjB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,OAAOgB,IAAI;IACb;;IAEA;IACA,IAAIA,IAAI,CAACM,WAAW,EAAE;MACpBvB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgB,IAAI,CAACM,WAAW,CAAC;MAC3D,MAAMC,QAAQ,GAAG,MAAMhC,OAAO,CAACsB,gBAAgB,CAACG,IAAI,CAACM,WAAW,CAAC;MACjEvB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEuB,QAAQ,CAAC;MACrC,OAAOA,QAAQ,CAACP,IAAI;IACtB;;IAEA;IACAjB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEgB,IAAI,CAAC;IAC7B,MAAMO,QAAQ,GAAG,MAAMjC,KAAK,CAACG,IAAI,CAAC,0BAA0B,EAAEuB,IAAI,CAAC;IACnEjB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEuB,QAAQ,CAAC;;IAEzC;IACA,IAAIA,QAAQ,CAACP,IAAI,EAAE;MACjBjB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEuB,QAAQ,CAACP,IAAI,CAAC;MACtC,OAAOO,QAAQ,CAACP,IAAI;IACtB,CAAC,MAAM;MACLjB,OAAO,CAACS,KAAK,CAAC,QAAQ,CAAC;MACvB,OAAO;QAAES,IAAI,EAAE,CAAC,CAAC;QAAEC,GAAG,EAAE,QAAQ;QAAEF,IAAI,EAAE;MAAK,CAAC;IAChD;EACF,CAAC,CAAC,OAAOR,KAAK,EAAE;IACdT,OAAO,CAACS,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IACxC,OAAO;MAAES,IAAI,EAAE,CAAC,CAAC;MAAEC,GAAG,EAAEV,KAAK,CAACgB,OAAO;MAAER,IAAI,EAAE;IAAK,CAAC;EACnD;AACJ,CAAC;;AAED;AACA,SAASI,gBAAgBA,CAACK,SAAS,EAAE;EACnC,MAAMC,QAAQ,GAAG;IACfT,IAAI,EAAE,CAAC;IACPC,GAAG,EAAE,SAAS;IACdF,IAAI,EAAE;MACJK,IAAI,EAAEI,SAAS,IAAI,KAAK;MACxBT,IAAI,EAAE,CACJ;QAAEW,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAED,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAED,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAED,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAED,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI,CAAC,CAC7B;MACDC,OAAO,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAM,CAAC;IAC3B;EACF,CAAC;EAED/B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE0B,QAAQ,CAAC;EACjC,OAAOA,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}