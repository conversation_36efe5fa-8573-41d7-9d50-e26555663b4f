{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport Tree from '../../data/Tree.js';\nimport Model from '../../model/Model.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nimport enableAriaDecalForTree from '../helper/enableAriaDecalForTree.js';\nvar TreemapSeriesModel = /** @class */function (_super) {\n  __extends(TreemapSeriesModel, _super);\n  function TreemapSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreemapSeriesModel.type;\n    _this.preventUsingHoverLayer = true;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TreemapSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    // Create a virtual root.\n    var root = {\n      name: option.name,\n      children: option.data\n    };\n    completeTreeValue(root);\n    var levels = option.levels || [];\n    // Used in \"visual priority\" in `treemapVisual.js`.\n    // This way is a little tricky, must satisfy the precondition:\n    //   1. There is no `treeNode.getModel('itemStyle.xxx')` used.\n    //   2. The `Model.prototype.getModel()` will not use any clone-like way.\n    var designatedVisualItemStyle = this.designatedVisualItemStyle = {};\n    var designatedVisualModel = new Model({\n      itemStyle: designatedVisualItemStyle\n    }, this, ecModel);\n    levels = option.levels = setDefault(levels, ecModel);\n    var levelModels = zrUtil.map(levels || [], function (levelDefine) {\n      return new Model(levelDefine, designatedVisualModel, ecModel);\n    }, this);\n    // Make sure always a new tree is created when setOption,\n    // in TreemapView, we check whether oldTree === newTree\n    // to choose mappings approach among old shapes and new shapes.\n    var tree = Tree.createTree(root, this, beforeLink);\n    function beforeLink(nodeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var node = tree.getNodeByDataIndex(idx);\n        var levelModel = node ? levelModels[node.depth] : null;\n        // If no levelModel, we also need `designatedVisualModel`.\n        model.parentModel = levelModel || designatedVisualModel;\n        return model;\n      });\n    }\n    return tree.data;\n  };\n  TreemapSeriesModel.prototype.optionUpdated = function () {\n    this.resetViewRoot();\n  };\n  /**\r\n   * @override\r\n   * @param {number} dataIndex\r\n   * @param {boolean} [mutipleSeries=false]\r\n   */\n  TreemapSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var value = this.getRawValue(dataIndex);\n    var name = data.getName(dataIndex);\n    return createTooltipMarkup('nameValue', {\n      name: name,\n      value: value\n    });\n  };\n  /**\r\n   * Add tree path to tooltip param\r\n   *\r\n   * @override\r\n   * @param {number} dataIndex\r\n   * @return {Object}\r\n   */\n  TreemapSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var params = _super.prototype.getDataParams.apply(this, arguments);\n    var node = this.getData().tree.getNodeByDataIndex(dataIndex);\n    params.treeAncestors = wrapTreePathInfo(node, this);\n    // compatitable the previous code.\n    params.treePathInfo = params.treeAncestors;\n    return params;\n  };\n  /**\r\n   * @public\r\n   * @param {Object} layoutInfo {\r\n   *                                x: containerGroup x\r\n   *                                y: containerGroup y\r\n   *                                width: containerGroup width\r\n   *                                height: containerGroup height\r\n   *                            }\r\n   */\n  TreemapSeriesModel.prototype.setLayoutInfo = function (layoutInfo) {\n    /**\r\n     * @readOnly\r\n     * @type {Object}\r\n     */\n    this.layoutInfo = this.layoutInfo || {};\n    zrUtil.extend(this.layoutInfo, layoutInfo);\n  };\n  /**\r\n   * @param  {string} id\r\n   * @return {number} index\r\n   */\n  TreemapSeriesModel.prototype.mapIdToIndex = function (id) {\n    // A feature is implemented:\n    // index is monotone increasing with the sequence of\n    // input id at the first time.\n    // This feature can make sure that each data item and its\n    // mapped color have the same index between data list and\n    // color list at the beginning, which is useful for user\n    // to adjust data-color mapping.\n    /**\r\n     * @private\r\n     * @type {Object}\r\n     */\n    var idIndexMap = this._idIndexMap;\n    if (!idIndexMap) {\n      idIndexMap = this._idIndexMap = zrUtil.createHashMap();\n      /**\r\n       * @private\r\n       * @type {number}\r\n       */\n      this._idIndexMapCount = 0;\n    }\n    var index = idIndexMap.get(id);\n    if (index == null) {\n      idIndexMap.set(id, index = this._idIndexMapCount++);\n    }\n    return index;\n  };\n  TreemapSeriesModel.prototype.getViewRoot = function () {\n    return this._viewRoot;\n  };\n  TreemapSeriesModel.prototype.resetViewRoot = function (viewRoot) {\n    viewRoot ? this._viewRoot = viewRoot : viewRoot = this._viewRoot;\n    var root = this.getRawData().tree.root;\n    if (!viewRoot || viewRoot !== root && !root.contains(viewRoot)) {\n      this._viewRoot = root;\n    }\n  };\n  TreemapSeriesModel.prototype.enableAriaDecal = function () {\n    enableAriaDecalForTree(this);\n  };\n  TreemapSeriesModel.type = 'series.treemap';\n  TreemapSeriesModel.layoutMode = 'box';\n  TreemapSeriesModel.defaultOption = {\n    // Disable progressive rendering\n    progressive: 0,\n    // size: ['80%', '80%'],            // deprecated, compatible with ec2.\n    left: 'center',\n    top: 'middle',\n    width: '80%',\n    height: '80%',\n    sort: true,\n    clipWindow: 'origin',\n    squareRatio: 0.5 * (1 + Math.sqrt(5)),\n    leafDepth: null,\n    drillDownIcon: '▶',\n    // to align specialized icon. ▷▶❒❐▼✚\n    zoomToNodeRatio: 0.32 * 0.32,\n    scaleLimit: null,\n    roam: true,\n    nodeClick: 'zoomToNode',\n    animation: true,\n    animationDurationUpdate: 900,\n    animationEasing: 'quinticInOut',\n    breadcrumb: {\n      show: true,\n      height: 22,\n      left: 'center',\n      top: 'bottom',\n      // right\n      // bottom\n      emptyItemWidth: 25,\n      itemStyle: {\n        color: 'rgba(0,0,0,0.7)',\n        textStyle: {\n          color: '#fff'\n        }\n      },\n      emphasis: {\n        itemStyle: {\n          color: 'rgba(0,0,0,0.9)' // '#5793f3',\n        }\n      }\n    },\n    label: {\n      show: true,\n      // Do not use textDistance, for ellipsis rect just the same as treemap node rect.\n      distance: 0,\n      padding: 5,\n      position: 'inside',\n      // formatter: null,\n      color: '#fff',\n      overflow: 'truncate'\n      // align\n      // verticalAlign\n    },\n    upperLabel: {\n      show: false,\n      position: [0, '50%'],\n      height: 20,\n      // formatter: null,\n      // color: '#fff',\n      overflow: 'truncate',\n      // align: null,\n      verticalAlign: 'middle'\n    },\n    itemStyle: {\n      color: null,\n      colorAlpha: null,\n      colorSaturation: null,\n      borderWidth: 0,\n      gapWidth: 0,\n      borderColor: '#fff',\n      borderColorSaturation: null // If specified, borderColor will be ineffective, and the\n      // border color is evaluated by color of current node and\n      // borderColorSaturation.\n    },\n    emphasis: {\n      upperLabel: {\n        show: true,\n        position: [0, '50%'],\n        overflow: 'truncate',\n        verticalAlign: 'middle'\n      }\n    },\n    visualDimension: 0,\n    visualMin: null,\n    visualMax: null,\n    color: [],\n    // level[n].color (if necessary).\n    // + Specify color list of each level. level[0].color would be global\n    // color list if not specified. (see method `setDefault`).\n    // + But set as a empty array to forbid fetch color from global palette\n    // when using nodeModel.get('color'), otherwise nodes on deep level\n    // will always has color palette set and are not able to inherit color\n    // from parent node.\n    // + TreemapSeries.color can not be set as 'none', otherwise effect\n    // legend color fetching (see seriesColor.js).\n    colorAlpha: null,\n    colorSaturation: null,\n    colorMappingBy: 'index',\n    visibleMin: 10,\n    // be rendered. Only works when sort is 'asc' or 'desc'.\n    childrenVisibleMin: null,\n    // grandchildren will not show.\n    // Why grandchildren? If not grandchildren but children,\n    // some siblings show children and some not,\n    // the appearance may be mess and not consistent,\n    levels: [] // Each item: {\n    //     visibleMin, itemStyle, visualDimension, label\n    // }\n  };\n  return TreemapSeriesModel;\n}(SeriesModel);\n/**\r\n * @param {Object} dataNode\r\n */\nfunction completeTreeValue(dataNode) {\n  // Postorder travel tree.\n  // If value of none-leaf node is not set,\n  // calculate it by suming up the value of all children.\n  var sum = 0;\n  zrUtil.each(dataNode.children, function (child) {\n    completeTreeValue(child);\n    var childValue = child.value;\n    zrUtil.isArray(childValue) && (childValue = childValue[0]);\n    sum += childValue;\n  });\n  var thisValue = dataNode.value;\n  if (zrUtil.isArray(thisValue)) {\n    thisValue = thisValue[0];\n  }\n  if (thisValue == null || isNaN(thisValue)) {\n    thisValue = sum;\n  }\n  // Value should not less than 0.\n  if (thisValue < 0) {\n    thisValue = 0;\n  }\n  zrUtil.isArray(dataNode.value) ? dataNode.value[0] = thisValue : dataNode.value = thisValue;\n}\n/**\r\n * set default to level configuration\r\n */\nfunction setDefault(levels, ecModel) {\n  var globalColorList = normalizeToArray(ecModel.get('color'));\n  var globalDecalList = normalizeToArray(ecModel.get(['aria', 'decal', 'decals']));\n  if (!globalColorList) {\n    return;\n  }\n  levels = levels || [];\n  var hasColorDefine;\n  var hasDecalDefine;\n  zrUtil.each(levels, function (levelDefine) {\n    var model = new Model(levelDefine);\n    var modelColor = model.get('color');\n    var modelDecal = model.get('decal');\n    if (model.get(['itemStyle', 'color']) || modelColor && modelColor !== 'none') {\n      hasColorDefine = true;\n    }\n    if (model.get(['itemStyle', 'decal']) || modelDecal && modelDecal !== 'none') {\n      hasDecalDefine = true;\n    }\n  });\n  var level0 = levels[0] || (levels[0] = {});\n  if (!hasColorDefine) {\n    level0.color = globalColorList.slice();\n  }\n  if (!hasDecalDefine && globalDecalList) {\n    level0.decal = globalDecalList.slice();\n  }\n  return levels;\n}\nexport default TreemapSeriesModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "SeriesModel", "Tree", "Model", "wrapTreePathInfo", "normalizeToArray", "createTooltipMarkup", "enableAriaDecalForTree", "TreemapSeriesModel", "_super", "_this", "apply", "arguments", "type", "preventUsingHoverLayer", "prototype", "getInitialData", "option", "ecModel", "root", "name", "children", "data", "completeTreeValue", "levels", "designatedVisualItemStyle", "designatedVisualModel", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "levelModels", "map", "levelDefine", "tree", "createTree", "beforeLink", "nodeData", "wrapMethod", "model", "idx", "node", "getNodeByDataIndex", "levelModel", "depth", "parentModel", "optionUpdated", "resetViewRoot", "formatTooltip", "dataIndex", "multipleSeries", "dataType", "getData", "value", "getRawValue", "getName", "getDataParams", "params", "treeAncestors", "treePathInfo", "setLayoutInfo", "layoutInfo", "extend", "mapIdToIndex", "id", "idIndexMap", "_idIndexMap", "createHashMap", "_idIndexMapCount", "index", "get", "set", "getViewRoot", "_viewRoot", "viewRoot", "getRawData", "contains", "enableAriaDecal", "layoutMode", "defaultOption", "progressive", "left", "top", "width", "height", "sort", "clipWindow", "squareRatio", "Math", "sqrt", "leafDepth", "drillDownIcon", "zoomToNodeRatio", "scaleLimit", "roam", "nodeClick", "animation", "animationDurationUpdate", "animationEasing", "breadcrumb", "show", "emptyItemWidth", "color", "textStyle", "emphasis", "label", "distance", "padding", "position", "overflow", "upperLabel", "verticalAlign", "colorAlpha", "colorSaturation", "borderWidth", "gapWidth", "borderColor", "borderColorSaturation", "visualDimension", "visualMin", "visualMax", "colorMappingBy", "visibleMin", "childrenVisibleMin", "dataNode", "sum", "each", "child", "childValue", "isArray", "thisValue", "isNaN", "globalColorList", "globalDecalList", "hasColorDefine", "hasDecalDefine", "modelColor", "modelDecal", "level0", "slice", "decal"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/treemap/TreemapSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport Tree from '../../data/Tree.js';\nimport Model from '../../model/Model.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nimport enableAriaDecalForTree from '../helper/enableAriaDecalForTree.js';\nvar TreemapSeriesModel = /** @class */function (_super) {\n  __extends(TreemapSeriesModel, _super);\n  function TreemapSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreemapSeriesModel.type;\n    _this.preventUsingHoverLayer = true;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TreemapSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    // Create a virtual root.\n    var root = {\n      name: option.name,\n      children: option.data\n    };\n    completeTreeValue(root);\n    var levels = option.levels || [];\n    // Used in \"visual priority\" in `treemapVisual.js`.\n    // This way is a little tricky, must satisfy the precondition:\n    //   1. There is no `treeNode.getModel('itemStyle.xxx')` used.\n    //   2. The `Model.prototype.getModel()` will not use any clone-like way.\n    var designatedVisualItemStyle = this.designatedVisualItemStyle = {};\n    var designatedVisualModel = new Model({\n      itemStyle: designatedVisualItemStyle\n    }, this, ecModel);\n    levels = option.levels = setDefault(levels, ecModel);\n    var levelModels = zrUtil.map(levels || [], function (levelDefine) {\n      return new Model(levelDefine, designatedVisualModel, ecModel);\n    }, this);\n    // Make sure always a new tree is created when setOption,\n    // in TreemapView, we check whether oldTree === newTree\n    // to choose mappings approach among old shapes and new shapes.\n    var tree = Tree.createTree(root, this, beforeLink);\n    function beforeLink(nodeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var node = tree.getNodeByDataIndex(idx);\n        var levelModel = node ? levelModels[node.depth] : null;\n        // If no levelModel, we also need `designatedVisualModel`.\n        model.parentModel = levelModel || designatedVisualModel;\n        return model;\n      });\n    }\n    return tree.data;\n  };\n  TreemapSeriesModel.prototype.optionUpdated = function () {\n    this.resetViewRoot();\n  };\n  /**\r\n   * @override\r\n   * @param {number} dataIndex\r\n   * @param {boolean} [mutipleSeries=false]\r\n   */\n  TreemapSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var value = this.getRawValue(dataIndex);\n    var name = data.getName(dataIndex);\n    return createTooltipMarkup('nameValue', {\n      name: name,\n      value: value\n    });\n  };\n  /**\r\n   * Add tree path to tooltip param\r\n   *\r\n   * @override\r\n   * @param {number} dataIndex\r\n   * @return {Object}\r\n   */\n  TreemapSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var params = _super.prototype.getDataParams.apply(this, arguments);\n    var node = this.getData().tree.getNodeByDataIndex(dataIndex);\n    params.treeAncestors = wrapTreePathInfo(node, this);\n    // compatitable the previous code.\n    params.treePathInfo = params.treeAncestors;\n    return params;\n  };\n  /**\r\n   * @public\r\n   * @param {Object} layoutInfo {\r\n   *                                x: containerGroup x\r\n   *                                y: containerGroup y\r\n   *                                width: containerGroup width\r\n   *                                height: containerGroup height\r\n   *                            }\r\n   */\n  TreemapSeriesModel.prototype.setLayoutInfo = function (layoutInfo) {\n    /**\r\n     * @readOnly\r\n     * @type {Object}\r\n     */\n    this.layoutInfo = this.layoutInfo || {};\n    zrUtil.extend(this.layoutInfo, layoutInfo);\n  };\n  /**\r\n   * @param  {string} id\r\n   * @return {number} index\r\n   */\n  TreemapSeriesModel.prototype.mapIdToIndex = function (id) {\n    // A feature is implemented:\n    // index is monotone increasing with the sequence of\n    // input id at the first time.\n    // This feature can make sure that each data item and its\n    // mapped color have the same index between data list and\n    // color list at the beginning, which is useful for user\n    // to adjust data-color mapping.\n    /**\r\n     * @private\r\n     * @type {Object}\r\n     */\n    var idIndexMap = this._idIndexMap;\n    if (!idIndexMap) {\n      idIndexMap = this._idIndexMap = zrUtil.createHashMap();\n      /**\r\n       * @private\r\n       * @type {number}\r\n       */\n      this._idIndexMapCount = 0;\n    }\n    var index = idIndexMap.get(id);\n    if (index == null) {\n      idIndexMap.set(id, index = this._idIndexMapCount++);\n    }\n    return index;\n  };\n  TreemapSeriesModel.prototype.getViewRoot = function () {\n    return this._viewRoot;\n  };\n  TreemapSeriesModel.prototype.resetViewRoot = function (viewRoot) {\n    viewRoot ? this._viewRoot = viewRoot : viewRoot = this._viewRoot;\n    var root = this.getRawData().tree.root;\n    if (!viewRoot || viewRoot !== root && !root.contains(viewRoot)) {\n      this._viewRoot = root;\n    }\n  };\n  TreemapSeriesModel.prototype.enableAriaDecal = function () {\n    enableAriaDecalForTree(this);\n  };\n  TreemapSeriesModel.type = 'series.treemap';\n  TreemapSeriesModel.layoutMode = 'box';\n  TreemapSeriesModel.defaultOption = {\n    // Disable progressive rendering\n    progressive: 0,\n    // size: ['80%', '80%'],            // deprecated, compatible with ec2.\n    left: 'center',\n    top: 'middle',\n    width: '80%',\n    height: '80%',\n    sort: true,\n    clipWindow: 'origin',\n    squareRatio: 0.5 * (1 + Math.sqrt(5)),\n    leafDepth: null,\n    drillDownIcon: '▶',\n    // to align specialized icon. ▷▶❒❐▼✚\n    zoomToNodeRatio: 0.32 * 0.32,\n    scaleLimit: null,\n    roam: true,\n    nodeClick: 'zoomToNode',\n    animation: true,\n    animationDurationUpdate: 900,\n    animationEasing: 'quinticInOut',\n    breadcrumb: {\n      show: true,\n      height: 22,\n      left: 'center',\n      top: 'bottom',\n      // right\n      // bottom\n      emptyItemWidth: 25,\n      itemStyle: {\n        color: 'rgba(0,0,0,0.7)',\n        textStyle: {\n          color: '#fff'\n        }\n      },\n      emphasis: {\n        itemStyle: {\n          color: 'rgba(0,0,0,0.9)' // '#5793f3',\n        }\n      }\n    },\n    label: {\n      show: true,\n      // Do not use textDistance, for ellipsis rect just the same as treemap node rect.\n      distance: 0,\n      padding: 5,\n      position: 'inside',\n      // formatter: null,\n      color: '#fff',\n      overflow: 'truncate'\n      // align\n      // verticalAlign\n    },\n    upperLabel: {\n      show: false,\n      position: [0, '50%'],\n      height: 20,\n      // formatter: null,\n      // color: '#fff',\n      overflow: 'truncate',\n      // align: null,\n      verticalAlign: 'middle'\n    },\n    itemStyle: {\n      color: null,\n      colorAlpha: null,\n      colorSaturation: null,\n      borderWidth: 0,\n      gapWidth: 0,\n      borderColor: '#fff',\n      borderColorSaturation: null // If specified, borderColor will be ineffective, and the\n      // border color is evaluated by color of current node and\n      // borderColorSaturation.\n    },\n    emphasis: {\n      upperLabel: {\n        show: true,\n        position: [0, '50%'],\n        overflow: 'truncate',\n        verticalAlign: 'middle'\n      }\n    },\n    visualDimension: 0,\n    visualMin: null,\n    visualMax: null,\n    color: [],\n    // level[n].color (if necessary).\n    // + Specify color list of each level. level[0].color would be global\n    // color list if not specified. (see method `setDefault`).\n    // + But set as a empty array to forbid fetch color from global palette\n    // when using nodeModel.get('color'), otherwise nodes on deep level\n    // will always has color palette set and are not able to inherit color\n    // from parent node.\n    // + TreemapSeries.color can not be set as 'none', otherwise effect\n    // legend color fetching (see seriesColor.js).\n    colorAlpha: null,\n    colorSaturation: null,\n    colorMappingBy: 'index',\n    visibleMin: 10,\n    // be rendered. Only works when sort is 'asc' or 'desc'.\n    childrenVisibleMin: null,\n    // grandchildren will not show.\n    // Why grandchildren? If not grandchildren but children,\n    // some siblings show children and some not,\n    // the appearance may be mess and not consistent,\n    levels: [] // Each item: {\n    //     visibleMin, itemStyle, visualDimension, label\n    // }\n  };\n  return TreemapSeriesModel;\n}(SeriesModel);\n/**\r\n * @param {Object} dataNode\r\n */\nfunction completeTreeValue(dataNode) {\n  // Postorder travel tree.\n  // If value of none-leaf node is not set,\n  // calculate it by suming up the value of all children.\n  var sum = 0;\n  zrUtil.each(dataNode.children, function (child) {\n    completeTreeValue(child);\n    var childValue = child.value;\n    zrUtil.isArray(childValue) && (childValue = childValue[0]);\n    sum += childValue;\n  });\n  var thisValue = dataNode.value;\n  if (zrUtil.isArray(thisValue)) {\n    thisValue = thisValue[0];\n  }\n  if (thisValue == null || isNaN(thisValue)) {\n    thisValue = sum;\n  }\n  // Value should not less than 0.\n  if (thisValue < 0) {\n    thisValue = 0;\n  }\n  zrUtil.isArray(dataNode.value) ? dataNode.value[0] = thisValue : dataNode.value = thisValue;\n}\n/**\r\n * set default to level configuration\r\n */\nfunction setDefault(levels, ecModel) {\n  var globalColorList = normalizeToArray(ecModel.get('color'));\n  var globalDecalList = normalizeToArray(ecModel.get(['aria', 'decal', 'decals']));\n  if (!globalColorList) {\n    return;\n  }\n  levels = levels || [];\n  var hasColorDefine;\n  var hasDecalDefine;\n  zrUtil.each(levels, function (levelDefine) {\n    var model = new Model(levelDefine);\n    var modelColor = model.get('color');\n    var modelDecal = model.get('decal');\n    if (model.get(['itemStyle', 'color']) || modelColor && modelColor !== 'none') {\n      hasColorDefine = true;\n    }\n    if (model.get(['itemStyle', 'decal']) || modelDecal && modelDecal !== 'none') {\n      hasDecalDefine = true;\n    }\n  });\n  var level0 = levels[0] || (levels[0] = {});\n  if (!hasColorDefine) {\n    level0.color = globalColorList.slice();\n  }\n  if (!hasDecalDefine && globalDecalList) {\n    level0.decal = globalDecalList.slice();\n  }\n  return levels;\n}\nexport default TreemapSeriesModel;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,IAAIC,kBAAkB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACtDV,SAAS,CAACS,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,kBAAkB,CAACK,IAAI;IACpCH,KAAK,CAACI,sBAAsB,GAAG,IAAI;IACnC,OAAOJ,KAAK;EACd;EACA;AACF;AACA;EACEF,kBAAkB,CAACO,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,EAAEC,OAAO,EAAE;IACvE;IACA,IAAIC,IAAI,GAAG;MACTC,IAAI,EAAEH,MAAM,CAACG,IAAI;MACjBC,QAAQ,EAAEJ,MAAM,CAACK;IACnB,CAAC;IACDC,iBAAiB,CAACJ,IAAI,CAAC;IACvB,IAAIK,MAAM,GAAGP,MAAM,CAACO,MAAM,IAAI,EAAE;IAChC;IACA;IACA;IACA;IACA,IAAIC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,GAAG,CAAC,CAAC;IACnE,IAAIC,qBAAqB,GAAG,IAAIvB,KAAK,CAAC;MACpCwB,SAAS,EAAEF;IACb,CAAC,EAAE,IAAI,EAAEP,OAAO,CAAC;IACjBM,MAAM,GAAGP,MAAM,CAACO,MAAM,GAAGI,UAAU,CAACJ,MAAM,EAAEN,OAAO,CAAC;IACpD,IAAIW,WAAW,GAAG7B,MAAM,CAAC8B,GAAG,CAACN,MAAM,IAAI,EAAE,EAAE,UAAUO,WAAW,EAAE;MAChE,OAAO,IAAI5B,KAAK,CAAC4B,WAAW,EAAEL,qBAAqB,EAAER,OAAO,CAAC;IAC/D,CAAC,EAAE,IAAI,CAAC;IACR;IACA;IACA;IACA,IAAIc,IAAI,GAAG9B,IAAI,CAAC+B,UAAU,CAACd,IAAI,EAAE,IAAI,EAAEe,UAAU,CAAC;IAClD,SAASA,UAAUA,CAACC,QAAQ,EAAE;MAC5BA,QAAQ,CAACC,UAAU,CAAC,cAAc,EAAE,UAAUC,KAAK,EAAEC,GAAG,EAAE;QACxD,IAAIC,IAAI,GAAGP,IAAI,CAACQ,kBAAkB,CAACF,GAAG,CAAC;QACvC,IAAIG,UAAU,GAAGF,IAAI,GAAGV,WAAW,CAACU,IAAI,CAACG,KAAK,CAAC,GAAG,IAAI;QACtD;QACAL,KAAK,CAACM,WAAW,GAAGF,UAAU,IAAIf,qBAAqB;QACvD,OAAOW,KAAK;MACd,CAAC,CAAC;IACJ;IACA,OAAOL,IAAI,CAACV,IAAI;EAClB,CAAC;EACDd,kBAAkB,CAACO,SAAS,CAAC6B,aAAa,GAAG,YAAY;IACvD,IAAI,CAACC,aAAa,CAAC,CAAC;EACtB,CAAC;EACD;AACF;AACA;AACA;AACA;EACErC,kBAAkB,CAACO,SAAS,CAAC+B,aAAa,GAAG,UAAUC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IAC1F,IAAI3B,IAAI,GAAG,IAAI,CAAC4B,OAAO,CAAC,CAAC;IACzB,IAAIC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACL,SAAS,CAAC;IACvC,IAAI3B,IAAI,GAAGE,IAAI,CAAC+B,OAAO,CAACN,SAAS,CAAC;IAClC,OAAOzC,mBAAmB,CAAC,WAAW,EAAE;MACtCc,IAAI,EAAEA,IAAI;MACV+B,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACE3C,kBAAkB,CAACO,SAAS,CAACuC,aAAa,GAAG,UAAUP,SAAS,EAAE;IAChE,IAAIQ,MAAM,GAAG9C,MAAM,CAACM,SAAS,CAACuC,aAAa,CAAC3C,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAClE,IAAI2B,IAAI,GAAG,IAAI,CAACW,OAAO,CAAC,CAAC,CAAClB,IAAI,CAACQ,kBAAkB,CAACO,SAAS,CAAC;IAC5DQ,MAAM,CAACC,aAAa,GAAGpD,gBAAgB,CAACmC,IAAI,EAAE,IAAI,CAAC;IACnD;IACAgB,MAAM,CAACE,YAAY,GAAGF,MAAM,CAACC,aAAa;IAC1C,OAAOD,MAAM;EACf,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE/C,kBAAkB,CAACO,SAAS,CAAC2C,aAAa,GAAG,UAAUC,UAAU,EAAE;IACjE;AACJ;AACA;AACA;IACI,IAAI,CAACA,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,CAAC,CAAC;IACvC3D,MAAM,CAAC4D,MAAM,CAAC,IAAI,CAACD,UAAU,EAAEA,UAAU,CAAC;EAC5C,CAAC;EACD;AACF;AACA;AACA;EACEnD,kBAAkB,CAACO,SAAS,CAAC8C,YAAY,GAAG,UAAUC,EAAE,EAAE;IACxD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACJ;AACA;AACA;IACI,IAAIC,UAAU,GAAG,IAAI,CAACC,WAAW;IACjC,IAAI,CAACD,UAAU,EAAE;MACfA,UAAU,GAAG,IAAI,CAACC,WAAW,GAAGhE,MAAM,CAACiE,aAAa,CAAC,CAAC;MACtD;AACN;AACA;AACA;MACM,IAAI,CAACC,gBAAgB,GAAG,CAAC;IAC3B;IACA,IAAIC,KAAK,GAAGJ,UAAU,CAACK,GAAG,CAACN,EAAE,CAAC;IAC9B,IAAIK,KAAK,IAAI,IAAI,EAAE;MACjBJ,UAAU,CAACM,GAAG,CAACP,EAAE,EAAEK,KAAK,GAAG,IAAI,CAACD,gBAAgB,EAAE,CAAC;IACrD;IACA,OAAOC,KAAK;EACd,CAAC;EACD3D,kBAAkB,CAACO,SAAS,CAACuD,WAAW,GAAG,YAAY;IACrD,OAAO,IAAI,CAACC,SAAS;EACvB,CAAC;EACD/D,kBAAkB,CAACO,SAAS,CAAC8B,aAAa,GAAG,UAAU2B,QAAQ,EAAE;IAC/DA,QAAQ,GAAG,IAAI,CAACD,SAAS,GAAGC,QAAQ,GAAGA,QAAQ,GAAG,IAAI,CAACD,SAAS;IAChE,IAAIpD,IAAI,GAAG,IAAI,CAACsD,UAAU,CAAC,CAAC,CAACzC,IAAI,CAACb,IAAI;IACtC,IAAI,CAACqD,QAAQ,IAAIA,QAAQ,KAAKrD,IAAI,IAAI,CAACA,IAAI,CAACuD,QAAQ,CAACF,QAAQ,CAAC,EAAE;MAC9D,IAAI,CAACD,SAAS,GAAGpD,IAAI;IACvB;EACF,CAAC;EACDX,kBAAkB,CAACO,SAAS,CAAC4D,eAAe,GAAG,YAAY;IACzDpE,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EACDC,kBAAkB,CAACK,IAAI,GAAG,gBAAgB;EAC1CL,kBAAkB,CAACoE,UAAU,GAAG,KAAK;EACrCpE,kBAAkB,CAACqE,aAAa,GAAG;IACjC;IACAC,WAAW,EAAE,CAAC;IACd;IACAC,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,IAAI;IACVC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,GAAG,IAAI,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;IACrCC,SAAS,EAAE,IAAI;IACfC,aAAa,EAAE,GAAG;IAClB;IACAC,eAAe,EAAE,IAAI,GAAG,IAAI;IAC5BC,UAAU,EAAE,IAAI;IAChBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,IAAI;IACfC,uBAAuB,EAAE,GAAG;IAC5BC,eAAe,EAAE,cAAc;IAC/BC,UAAU,EAAE;MACVC,IAAI,EAAE,IAAI;MACVhB,MAAM,EAAE,EAAE;MACVH,IAAI,EAAE,QAAQ;MACdC,GAAG,EAAE,QAAQ;MACb;MACA;MACAmB,cAAc,EAAE,EAAE;MAClBxE,SAAS,EAAE;QACTyE,KAAK,EAAE,iBAAiB;QACxBC,SAAS,EAAE;UACTD,KAAK,EAAE;QACT;MACF,CAAC;MACDE,QAAQ,EAAE;QACR3E,SAAS,EAAE;UACTyE,KAAK,EAAE,iBAAiB,CAAC;QAC3B;MACF;IACF,CAAC;IACDG,KAAK,EAAE;MACLL,IAAI,EAAE,IAAI;MACV;MACAM,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,QAAQ;MAClB;MACAN,KAAK,EAAE,MAAM;MACbO,QAAQ,EAAE;MACV;MACA;IACF,CAAC;IACDC,UAAU,EAAE;MACVV,IAAI,EAAE,KAAK;MACXQ,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;MACpBxB,MAAM,EAAE,EAAE;MACV;MACA;MACAyB,QAAQ,EAAE,UAAU;MACpB;MACAE,aAAa,EAAE;IACjB,CAAC;IACDlF,SAAS,EAAE;MACTyE,KAAK,EAAE,IAAI;MACXU,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,IAAI;MACrBC,WAAW,EAAE,CAAC;MACdC,QAAQ,EAAE,CAAC;MACXC,WAAW,EAAE,MAAM;MACnBC,qBAAqB,EAAE,IAAI,CAAC;MAC5B;MACA;IACF,CAAC;IACDb,QAAQ,EAAE;MACRM,UAAU,EAAE;QACVV,IAAI,EAAE,IAAI;QACVQ,QAAQ,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;QACpBC,QAAQ,EAAE,UAAU;QACpBE,aAAa,EAAE;MACjB;IACF,CAAC;IACDO,eAAe,EAAE,CAAC;IAClBC,SAAS,EAAE,IAAI;IACfC,SAAS,EAAE,IAAI;IACflB,KAAK,EAAE,EAAE;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAU,UAAU,EAAE,IAAI;IAChBC,eAAe,EAAE,IAAI;IACrBQ,cAAc,EAAE,OAAO;IACvBC,UAAU,EAAE,EAAE;IACd;IACAC,kBAAkB,EAAE,IAAI;IACxB;IACA;IACA;IACA;IACAjG,MAAM,EAAE,EAAE,CAAC;IACX;IACA;EACF,CAAC;EACD,OAAOhB,kBAAkB;AAC3B,CAAC,CAACP,WAAW,CAAC;AACd;AACA;AACA;AACA,SAASsB,iBAAiBA,CAACmG,QAAQ,EAAE;EACnC;EACA;EACA;EACA,IAAIC,GAAG,GAAG,CAAC;EACX3H,MAAM,CAAC4H,IAAI,CAACF,QAAQ,CAACrG,QAAQ,EAAE,UAAUwG,KAAK,EAAE;IAC9CtG,iBAAiB,CAACsG,KAAK,CAAC;IACxB,IAAIC,UAAU,GAAGD,KAAK,CAAC1E,KAAK;IAC5BnD,MAAM,CAAC+H,OAAO,CAACD,UAAU,CAAC,KAAKA,UAAU,GAAGA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC1DH,GAAG,IAAIG,UAAU;EACnB,CAAC,CAAC;EACF,IAAIE,SAAS,GAAGN,QAAQ,CAACvE,KAAK;EAC9B,IAAInD,MAAM,CAAC+H,OAAO,CAACC,SAAS,CAAC,EAAE;IAC7BA,SAAS,GAAGA,SAAS,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIA,SAAS,IAAI,IAAI,IAAIC,KAAK,CAACD,SAAS,CAAC,EAAE;IACzCA,SAAS,GAAGL,GAAG;EACjB;EACA;EACA,IAAIK,SAAS,GAAG,CAAC,EAAE;IACjBA,SAAS,GAAG,CAAC;EACf;EACAhI,MAAM,CAAC+H,OAAO,CAACL,QAAQ,CAACvE,KAAK,CAAC,GAAGuE,QAAQ,CAACvE,KAAK,CAAC,CAAC,CAAC,GAAG6E,SAAS,GAAGN,QAAQ,CAACvE,KAAK,GAAG6E,SAAS;AAC7F;AACA;AACA;AACA;AACA,SAASpG,UAAUA,CAACJ,MAAM,EAAEN,OAAO,EAAE;EACnC,IAAIgH,eAAe,GAAG7H,gBAAgB,CAACa,OAAO,CAACkD,GAAG,CAAC,OAAO,CAAC,CAAC;EAC5D,IAAI+D,eAAe,GAAG9H,gBAAgB,CAACa,OAAO,CAACkD,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;EAChF,IAAI,CAAC8D,eAAe,EAAE;IACpB;EACF;EACA1G,MAAM,GAAGA,MAAM,IAAI,EAAE;EACrB,IAAI4G,cAAc;EAClB,IAAIC,cAAc;EAClBrI,MAAM,CAAC4H,IAAI,CAACpG,MAAM,EAAE,UAAUO,WAAW,EAAE;IACzC,IAAIM,KAAK,GAAG,IAAIlC,KAAK,CAAC4B,WAAW,CAAC;IAClC,IAAIuG,UAAU,GAAGjG,KAAK,CAAC+B,GAAG,CAAC,OAAO,CAAC;IACnC,IAAImE,UAAU,GAAGlG,KAAK,CAAC+B,GAAG,CAAC,OAAO,CAAC;IACnC,IAAI/B,KAAK,CAAC+B,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,IAAIkE,UAAU,IAAIA,UAAU,KAAK,MAAM,EAAE;MAC5EF,cAAc,GAAG,IAAI;IACvB;IACA,IAAI/F,KAAK,CAAC+B,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,IAAImE,UAAU,IAAIA,UAAU,KAAK,MAAM,EAAE;MAC5EF,cAAc,GAAG,IAAI;IACvB;EACF,CAAC,CAAC;EACF,IAAIG,MAAM,GAAGhH,MAAM,CAAC,CAAC,CAAC,KAAKA,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1C,IAAI,CAAC4G,cAAc,EAAE;IACnBI,MAAM,CAACpC,KAAK,GAAG8B,eAAe,CAACO,KAAK,CAAC,CAAC;EACxC;EACA,IAAI,CAACJ,cAAc,IAAIF,eAAe,EAAE;IACtCK,MAAM,CAACE,KAAK,GAAGP,eAAe,CAACM,KAAK,CAAC,CAAC;EACxC;EACA,OAAOjH,MAAM;AACf;AACA,eAAehB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}