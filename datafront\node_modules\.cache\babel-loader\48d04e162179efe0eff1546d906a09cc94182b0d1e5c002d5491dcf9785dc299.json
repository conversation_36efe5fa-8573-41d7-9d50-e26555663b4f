{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Layout helpers for each component positioning\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { parsePercent } from './number.js';\nimport * as formatUtil from './format.js';\nvar each = zrUtil.each;\n/**\r\n * @public\r\n */\nexport var LOCATION_PARAMS = ['left', 'right', 'top', 'bottom', 'width', 'height'];\n/**\r\n * @public\r\n */\nexport var HV_NAMES = [['width', 'left', 'right'], ['height', 'top', 'bottom']];\nfunction boxLayout(orient, group, gap, maxWidth, maxHeight) {\n  var x = 0;\n  var y = 0;\n  if (maxWidth == null) {\n    maxWidth = Infinity;\n  }\n  if (maxHeight == null) {\n    maxHeight = Infinity;\n  }\n  var currentLineMaxSize = 0;\n  group.eachChild(function (child, idx) {\n    var rect = child.getBoundingRect();\n    var nextChild = group.childAt(idx + 1);\n    var nextChildRect = nextChild && nextChild.getBoundingRect();\n    var nextX;\n    var nextY;\n    if (orient === 'horizontal') {\n      var moveX = rect.width + (nextChildRect ? -nextChildRect.x + rect.x : 0);\n      nextX = x + moveX;\n      // Wrap when width exceeds maxWidth or meet a `newline` group\n      // FIXME compare before adding gap?\n      if (nextX > maxWidth || child.newline) {\n        x = 0;\n        nextX = moveX;\n        y += currentLineMaxSize + gap;\n        currentLineMaxSize = rect.height;\n      } else {\n        // FIXME: consider rect.y is not `0`?\n        currentLineMaxSize = Math.max(currentLineMaxSize, rect.height);\n      }\n    } else {\n      var moveY = rect.height + (nextChildRect ? -nextChildRect.y + rect.y : 0);\n      nextY = y + moveY;\n      // Wrap when width exceeds maxHeight or meet a `newline` group\n      if (nextY > maxHeight || child.newline) {\n        x += currentLineMaxSize + gap;\n        y = 0;\n        nextY = moveY;\n        currentLineMaxSize = rect.width;\n      } else {\n        currentLineMaxSize = Math.max(currentLineMaxSize, rect.width);\n      }\n    }\n    if (child.newline) {\n      return;\n    }\n    child.x = x;\n    child.y = y;\n    child.markRedraw();\n    orient === 'horizontal' ? x = nextX + gap : y = nextY + gap;\n  });\n}\n/**\r\n * VBox or HBox layouting\r\n * @param {string} orient\r\n * @param {module:zrender/graphic/Group} group\r\n * @param {number} gap\r\n * @param {number} [width=Infinity]\r\n * @param {number} [height=Infinity]\r\n */\nexport var box = boxLayout;\n/**\r\n * VBox layouting\r\n * @param {module:zrender/graphic/Group} group\r\n * @param {number} gap\r\n * @param {number} [width=Infinity]\r\n * @param {number} [height=Infinity]\r\n */\nexport var vbox = zrUtil.curry(boxLayout, 'vertical');\n/**\r\n * HBox layouting\r\n * @param {module:zrender/graphic/Group} group\r\n * @param {number} gap\r\n * @param {number} [width=Infinity]\r\n * @param {number} [height=Infinity]\r\n */\nexport var hbox = zrUtil.curry(boxLayout, 'horizontal');\n/**\r\n * If x or x2 is not specified or 'center' 'left' 'right',\r\n * the width would be as long as possible.\r\n * If y or y2 is not specified or 'middle' 'top' 'bottom',\r\n * the height would be as long as possible.\r\n */\nexport function getAvailableSize(positionInfo, containerRect, margin) {\n  var containerWidth = containerRect.width;\n  var containerHeight = containerRect.height;\n  var x = parsePercent(positionInfo.left, containerWidth);\n  var y = parsePercent(positionInfo.top, containerHeight);\n  var x2 = parsePercent(positionInfo.right, containerWidth);\n  var y2 = parsePercent(positionInfo.bottom, containerHeight);\n  (isNaN(x) || isNaN(parseFloat(positionInfo.left))) && (x = 0);\n  (isNaN(x2) || isNaN(parseFloat(positionInfo.right))) && (x2 = containerWidth);\n  (isNaN(y) || isNaN(parseFloat(positionInfo.top))) && (y = 0);\n  (isNaN(y2) || isNaN(parseFloat(positionInfo.bottom))) && (y2 = containerHeight);\n  margin = formatUtil.normalizeCssArray(margin || 0);\n  return {\n    width: Math.max(x2 - x - margin[1] - margin[3], 0),\n    height: Math.max(y2 - y - margin[0] - margin[2], 0)\n  };\n}\n/**\r\n * Parse position info.\r\n */\nexport function getLayoutRect(positionInfo, containerRect, margin) {\n  margin = formatUtil.normalizeCssArray(margin || 0);\n  var containerWidth = containerRect.width;\n  var containerHeight = containerRect.height;\n  var left = parsePercent(positionInfo.left, containerWidth);\n  var top = parsePercent(positionInfo.top, containerHeight);\n  var right = parsePercent(positionInfo.right, containerWidth);\n  var bottom = parsePercent(positionInfo.bottom, containerHeight);\n  var width = parsePercent(positionInfo.width, containerWidth);\n  var height = parsePercent(positionInfo.height, containerHeight);\n  var verticalMargin = margin[2] + margin[0];\n  var horizontalMargin = margin[1] + margin[3];\n  var aspect = positionInfo.aspect;\n  // If width is not specified, calculate width from left and right\n  if (isNaN(width)) {\n    width = containerWidth - right - horizontalMargin - left;\n  }\n  if (isNaN(height)) {\n    height = containerHeight - bottom - verticalMargin - top;\n  }\n  if (aspect != null) {\n    // If width and height are not given\n    // 1. Graph should not exceeds the container\n    // 2. Aspect must be keeped\n    // 3. Graph should take the space as more as possible\n    // FIXME\n    // Margin is not considered, because there is no case that both\n    // using margin and aspect so far.\n    if (isNaN(width) && isNaN(height)) {\n      if (aspect > containerWidth / containerHeight) {\n        width = containerWidth * 0.8;\n      } else {\n        height = containerHeight * 0.8;\n      }\n    }\n    // Calculate width or height with given aspect\n    if (isNaN(width)) {\n      width = aspect * height;\n    }\n    if (isNaN(height)) {\n      height = width / aspect;\n    }\n  }\n  // If left is not specified, calculate left from right and width\n  if (isNaN(left)) {\n    left = containerWidth - right - width - horizontalMargin;\n  }\n  if (isNaN(top)) {\n    top = containerHeight - bottom - height - verticalMargin;\n  }\n  // Align left and top\n  switch (positionInfo.left || positionInfo.right) {\n    case 'center':\n      left = containerWidth / 2 - width / 2 - margin[3];\n      break;\n    case 'right':\n      left = containerWidth - width - horizontalMargin;\n      break;\n  }\n  switch (positionInfo.top || positionInfo.bottom) {\n    case 'middle':\n    case 'center':\n      top = containerHeight / 2 - height / 2 - margin[0];\n      break;\n    case 'bottom':\n      top = containerHeight - height - verticalMargin;\n      break;\n  }\n  // If something is wrong and left, top, width, height are calculated as NaN\n  left = left || 0;\n  top = top || 0;\n  if (isNaN(width)) {\n    // Width may be NaN if only one value is given except width\n    width = containerWidth - horizontalMargin - left - (right || 0);\n  }\n  if (isNaN(height)) {\n    // Height may be NaN if only one value is given except height\n    height = containerHeight - verticalMargin - top - (bottom || 0);\n  }\n  var rect = new BoundingRect(left + margin[3], top + margin[0], width, height);\n  rect.margin = margin;\n  return rect;\n}\n/**\r\n * Position a zr element in viewport\r\n *  Group position is specified by either\r\n *  {left, top}, {right, bottom}\r\n *  If all properties exists, right and bottom will be igonred.\r\n *\r\n * Logic:\r\n *     1. Scale (against origin point in parent coord)\r\n *     2. Rotate (against origin point in parent coord)\r\n *     3. Translate (with el.position by this method)\r\n * So this method only fixes the last step 'Translate', which does not affect\r\n * scaling and rotating.\r\n *\r\n * If be called repeatedly with the same input el, the same result will be gotten.\r\n *\r\n * Return true if the layout happened.\r\n *\r\n * @param el Should have `getBoundingRect` method.\r\n * @param positionInfo\r\n * @param positionInfo.left\r\n * @param positionInfo.top\r\n * @param positionInfo.right\r\n * @param positionInfo.bottom\r\n * @param positionInfo.width Only for opt.boundingModel: 'raw'\r\n * @param positionInfo.height Only for opt.boundingModel: 'raw'\r\n * @param containerRect\r\n * @param margin\r\n * @param opt\r\n * @param opt.hv Only horizontal or only vertical. Default to be [1, 1]\r\n * @param opt.boundingMode\r\n *        Specify how to calculate boundingRect when locating.\r\n *        'all': Position the boundingRect that is transformed and uioned\r\n *               both itself and its descendants.\r\n *               This mode simplies confine the elements in the bounding\r\n *               of their container (e.g., using 'right: 0').\r\n *        'raw': Position the boundingRect that is not transformed and only itself.\r\n *               This mode is useful when you want a element can overflow its\r\n *               container. (Consider a rotated circle needs to be located in a corner.)\r\n *               In this mode positionInfo.width/height can only be number.\r\n */\nexport function positionElement(el, positionInfo, containerRect, margin, opt, out) {\n  var h = !opt || !opt.hv || opt.hv[0];\n  var v = !opt || !opt.hv || opt.hv[1];\n  var boundingMode = opt && opt.boundingMode || 'all';\n  out = out || el;\n  out.x = el.x;\n  out.y = el.y;\n  if (!h && !v) {\n    return false;\n  }\n  var rect;\n  if (boundingMode === 'raw') {\n    rect = el.type === 'group' ? new BoundingRect(0, 0, +positionInfo.width || 0, +positionInfo.height || 0) : el.getBoundingRect();\n  } else {\n    rect = el.getBoundingRect();\n    if (el.needLocalTransform()) {\n      var transform = el.getLocalTransform();\n      // Notice: raw rect may be inner object of el,\n      // which should not be modified.\n      rect = rect.clone();\n      rect.applyTransform(transform);\n    }\n  }\n  // The real width and height can not be specified but calculated by the given el.\n  var layoutRect = getLayoutRect(zrUtil.defaults({\n    width: rect.width,\n    height: rect.height\n  }, positionInfo), containerRect, margin);\n  // Because 'tranlate' is the last step in transform\n  // (see zrender/core/Transformable#getLocalTransform),\n  // we can just only modify el.position to get final result.\n  var dx = h ? layoutRect.x - rect.x : 0;\n  var dy = v ? layoutRect.y - rect.y : 0;\n  if (boundingMode === 'raw') {\n    out.x = dx;\n    out.y = dy;\n  } else {\n    out.x += dx;\n    out.y += dy;\n  }\n  if (out === el) {\n    el.markRedraw();\n  }\n  return true;\n}\n/**\r\n * @param option Contains some of the properties in HV_NAMES.\r\n * @param hvIdx 0: horizontal; 1: vertical.\r\n */\nexport function sizeCalculable(option, hvIdx) {\n  return option[HV_NAMES[hvIdx][0]] != null || option[HV_NAMES[hvIdx][1]] != null && option[HV_NAMES[hvIdx][2]] != null;\n}\nexport function fetchLayoutMode(ins) {\n  var layoutMode = ins.layoutMode || ins.constructor.layoutMode;\n  return zrUtil.isObject(layoutMode) ? layoutMode : layoutMode ? {\n    type: layoutMode\n  } : null;\n}\n/**\r\n * Consider Case:\r\n * When default option has {left: 0, width: 100}, and we set {right: 0}\r\n * through setOption or media query, using normal zrUtil.merge will cause\r\n * {right: 0} does not take effect.\r\n *\r\n * @example\r\n * ComponentModel.extend({\r\n *     init: function () {\r\n *         ...\r\n *         let inputPositionParams = layout.getLayoutParams(option);\r\n *         this.mergeOption(inputPositionParams);\r\n *     },\r\n *     mergeOption: function (newOption) {\r\n *         newOption && zrUtil.merge(thisOption, newOption, true);\r\n *         layout.mergeLayoutParam(thisOption, newOption);\r\n *     }\r\n * });\r\n *\r\n * @param targetOption\r\n * @param newOption\r\n * @param opt\r\n */\nexport function mergeLayoutParam(targetOption, newOption, opt) {\n  var ignoreSize = opt && opt.ignoreSize;\n  !zrUtil.isArray(ignoreSize) && (ignoreSize = [ignoreSize, ignoreSize]);\n  var hResult = merge(HV_NAMES[0], 0);\n  var vResult = merge(HV_NAMES[1], 1);\n  copy(HV_NAMES[0], targetOption, hResult);\n  copy(HV_NAMES[1], targetOption, vResult);\n  function merge(names, hvIdx) {\n    var newParams = {};\n    var newValueCount = 0;\n    var merged = {};\n    var mergedValueCount = 0;\n    var enoughParamNumber = 2;\n    each(names, function (name) {\n      merged[name] = targetOption[name];\n    });\n    each(names, function (name) {\n      // Consider case: newOption.width is null, which is\n      // set by user for removing width setting.\n      hasProp(newOption, name) && (newParams[name] = merged[name] = newOption[name]);\n      hasValue(newParams, name) && newValueCount++;\n      hasValue(merged, name) && mergedValueCount++;\n    });\n    if (ignoreSize[hvIdx]) {\n      // Only one of left/right is premitted to exist.\n      if (hasValue(newOption, names[1])) {\n        merged[names[2]] = null;\n      } else if (hasValue(newOption, names[2])) {\n        merged[names[1]] = null;\n      }\n      return merged;\n    }\n    // Case: newOption: {width: ..., right: ...},\n    // or targetOption: {right: ...} and newOption: {width: ...},\n    // There is no conflict when merged only has params count\n    // little than enoughParamNumber.\n    if (mergedValueCount === enoughParamNumber || !newValueCount) {\n      return merged;\n    }\n    // Case: newOption: {width: ..., right: ...},\n    // Than we can make sure user only want those two, and ignore\n    // all origin params in targetOption.\n    else if (newValueCount >= enoughParamNumber) {\n      return newParams;\n    } else {\n      // Chose another param from targetOption by priority.\n      for (var i = 0; i < names.length; i++) {\n        var name_1 = names[i];\n        if (!hasProp(newParams, name_1) && hasProp(targetOption, name_1)) {\n          newParams[name_1] = targetOption[name_1];\n          break;\n        }\n      }\n      return newParams;\n    }\n  }\n  function hasProp(obj, name) {\n    return obj.hasOwnProperty(name);\n  }\n  function hasValue(obj, name) {\n    return obj[name] != null && obj[name] !== 'auto';\n  }\n  function copy(names, target, source) {\n    each(names, function (name) {\n      target[name] = source[name];\n    });\n  }\n}\n/**\r\n * Retrieve 'left', 'right', 'top', 'bottom', 'width', 'height' from object.\r\n */\nexport function getLayoutParams(source) {\n  return copyLayoutParams({}, source);\n}\n/**\r\n * Retrieve 'left', 'right', 'top', 'bottom', 'width', 'height' from object.\r\n * @param {Object} source\r\n * @return {Object} Result contains those props.\r\n */\nexport function copyLayoutParams(target, source) {\n  source && target && each(LOCATION_PARAMS, function (name) {\n    source.hasOwnProperty(name) && (target[name] = source[name]);\n  });\n  return target;\n}", "map": {"version": 3, "names": ["zrUtil", "BoundingRect", "parsePercent", "formatUtil", "each", "LOCATION_PARAMS", "HV_NAMES", "boxLayout", "orient", "group", "gap", "max<PERSON><PERSON><PERSON>", "maxHeight", "x", "y", "Infinity", "currentLineMaxSize", "<PERSON><PERSON><PERSON><PERSON>", "child", "idx", "rect", "getBoundingRect", "<PERSON><PERSON><PERSON><PERSON>", "childAt", "nextChildRect", "nextX", "nextY", "moveX", "width", "newline", "height", "Math", "max", "moveY", "mark<PERSON><PERSON><PERSON>", "box", "vbox", "curry", "hbox", "getAvailableSize", "positionInfo", "containerRect", "margin", "containerWidth", "containerHeight", "left", "top", "x2", "right", "y2", "bottom", "isNaN", "parseFloat", "normalizeCssArray", "getLayoutRect", "verticalMargin", "<PERSON><PERSON><PERSON><PERSON>", "aspect", "positionElement", "el", "opt", "out", "h", "hv", "v", "boundingMode", "type", "needLocalTransform", "transform", "getLocalTransform", "clone", "applyTransform", "layoutRect", "defaults", "dx", "dy", "sizeCalculable", "option", "hvIdx", "fetchLayoutMode", "ins", "layoutMode", "constructor", "isObject", "mergeLayoutParam", "targetOption", "newOption", "ignoreSize", "isArray", "hResult", "merge", "vResult", "copy", "names", "newParams", "newValueCount", "merged", "mergedValueCount", "enoughParamNumber", "name", "hasProp", "hasValue", "i", "length", "name_1", "obj", "hasOwnProperty", "target", "source", "getLayoutParams", "copyLayoutParams"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/util/layout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// Layout helpers for each component positioning\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { parsePercent } from './number.js';\nimport * as formatUtil from './format.js';\nvar each = zrUtil.each;\n/**\r\n * @public\r\n */\nexport var LOCATION_PARAMS = ['left', 'right', 'top', 'bottom', 'width', 'height'];\n/**\r\n * @public\r\n */\nexport var HV_NAMES = [['width', 'left', 'right'], ['height', 'top', 'bottom']];\nfunction boxLayout(orient, group, gap, maxWidth, maxHeight) {\n  var x = 0;\n  var y = 0;\n  if (maxWidth == null) {\n    maxWidth = Infinity;\n  }\n  if (maxHeight == null) {\n    maxHeight = Infinity;\n  }\n  var currentLineMaxSize = 0;\n  group.eachChild(function (child, idx) {\n    var rect = child.getBoundingRect();\n    var nextChild = group.childAt(idx + 1);\n    var nextChildRect = nextChild && nextChild.getBoundingRect();\n    var nextX;\n    var nextY;\n    if (orient === 'horizontal') {\n      var moveX = rect.width + (nextChildRect ? -nextChildRect.x + rect.x : 0);\n      nextX = x + moveX;\n      // Wrap when width exceeds maxWidth or meet a `newline` group\n      // FIXME compare before adding gap?\n      if (nextX > maxWidth || child.newline) {\n        x = 0;\n        nextX = moveX;\n        y += currentLineMaxSize + gap;\n        currentLineMaxSize = rect.height;\n      } else {\n        // FIXME: consider rect.y is not `0`?\n        currentLineMaxSize = Math.max(currentLineMaxSize, rect.height);\n      }\n    } else {\n      var moveY = rect.height + (nextChildRect ? -nextChildRect.y + rect.y : 0);\n      nextY = y + moveY;\n      // Wrap when width exceeds maxHeight or meet a `newline` group\n      if (nextY > maxHeight || child.newline) {\n        x += currentLineMaxSize + gap;\n        y = 0;\n        nextY = moveY;\n        currentLineMaxSize = rect.width;\n      } else {\n        currentLineMaxSize = Math.max(currentLineMaxSize, rect.width);\n      }\n    }\n    if (child.newline) {\n      return;\n    }\n    child.x = x;\n    child.y = y;\n    child.markRedraw();\n    orient === 'horizontal' ? x = nextX + gap : y = nextY + gap;\n  });\n}\n/**\r\n * VBox or HBox layouting\r\n * @param {string} orient\r\n * @param {module:zrender/graphic/Group} group\r\n * @param {number} gap\r\n * @param {number} [width=Infinity]\r\n * @param {number} [height=Infinity]\r\n */\nexport var box = boxLayout;\n/**\r\n * VBox layouting\r\n * @param {module:zrender/graphic/Group} group\r\n * @param {number} gap\r\n * @param {number} [width=Infinity]\r\n * @param {number} [height=Infinity]\r\n */\nexport var vbox = zrUtil.curry(boxLayout, 'vertical');\n/**\r\n * HBox layouting\r\n * @param {module:zrender/graphic/Group} group\r\n * @param {number} gap\r\n * @param {number} [width=Infinity]\r\n * @param {number} [height=Infinity]\r\n */\nexport var hbox = zrUtil.curry(boxLayout, 'horizontal');\n/**\r\n * If x or x2 is not specified or 'center' 'left' 'right',\r\n * the width would be as long as possible.\r\n * If y or y2 is not specified or 'middle' 'top' 'bottom',\r\n * the height would be as long as possible.\r\n */\nexport function getAvailableSize(positionInfo, containerRect, margin) {\n  var containerWidth = containerRect.width;\n  var containerHeight = containerRect.height;\n  var x = parsePercent(positionInfo.left, containerWidth);\n  var y = parsePercent(positionInfo.top, containerHeight);\n  var x2 = parsePercent(positionInfo.right, containerWidth);\n  var y2 = parsePercent(positionInfo.bottom, containerHeight);\n  (isNaN(x) || isNaN(parseFloat(positionInfo.left))) && (x = 0);\n  (isNaN(x2) || isNaN(parseFloat(positionInfo.right))) && (x2 = containerWidth);\n  (isNaN(y) || isNaN(parseFloat(positionInfo.top))) && (y = 0);\n  (isNaN(y2) || isNaN(parseFloat(positionInfo.bottom))) && (y2 = containerHeight);\n  margin = formatUtil.normalizeCssArray(margin || 0);\n  return {\n    width: Math.max(x2 - x - margin[1] - margin[3], 0),\n    height: Math.max(y2 - y - margin[0] - margin[2], 0)\n  };\n}\n/**\r\n * Parse position info.\r\n */\nexport function getLayoutRect(positionInfo, containerRect, margin) {\n  margin = formatUtil.normalizeCssArray(margin || 0);\n  var containerWidth = containerRect.width;\n  var containerHeight = containerRect.height;\n  var left = parsePercent(positionInfo.left, containerWidth);\n  var top = parsePercent(positionInfo.top, containerHeight);\n  var right = parsePercent(positionInfo.right, containerWidth);\n  var bottom = parsePercent(positionInfo.bottom, containerHeight);\n  var width = parsePercent(positionInfo.width, containerWidth);\n  var height = parsePercent(positionInfo.height, containerHeight);\n  var verticalMargin = margin[2] + margin[0];\n  var horizontalMargin = margin[1] + margin[3];\n  var aspect = positionInfo.aspect;\n  // If width is not specified, calculate width from left and right\n  if (isNaN(width)) {\n    width = containerWidth - right - horizontalMargin - left;\n  }\n  if (isNaN(height)) {\n    height = containerHeight - bottom - verticalMargin - top;\n  }\n  if (aspect != null) {\n    // If width and height are not given\n    // 1. Graph should not exceeds the container\n    // 2. Aspect must be keeped\n    // 3. Graph should take the space as more as possible\n    // FIXME\n    // Margin is not considered, because there is no case that both\n    // using margin and aspect so far.\n    if (isNaN(width) && isNaN(height)) {\n      if (aspect > containerWidth / containerHeight) {\n        width = containerWidth * 0.8;\n      } else {\n        height = containerHeight * 0.8;\n      }\n    }\n    // Calculate width or height with given aspect\n    if (isNaN(width)) {\n      width = aspect * height;\n    }\n    if (isNaN(height)) {\n      height = width / aspect;\n    }\n  }\n  // If left is not specified, calculate left from right and width\n  if (isNaN(left)) {\n    left = containerWidth - right - width - horizontalMargin;\n  }\n  if (isNaN(top)) {\n    top = containerHeight - bottom - height - verticalMargin;\n  }\n  // Align left and top\n  switch (positionInfo.left || positionInfo.right) {\n    case 'center':\n      left = containerWidth / 2 - width / 2 - margin[3];\n      break;\n    case 'right':\n      left = containerWidth - width - horizontalMargin;\n      break;\n  }\n  switch (positionInfo.top || positionInfo.bottom) {\n    case 'middle':\n    case 'center':\n      top = containerHeight / 2 - height / 2 - margin[0];\n      break;\n    case 'bottom':\n      top = containerHeight - height - verticalMargin;\n      break;\n  }\n  // If something is wrong and left, top, width, height are calculated as NaN\n  left = left || 0;\n  top = top || 0;\n  if (isNaN(width)) {\n    // Width may be NaN if only one value is given except width\n    width = containerWidth - horizontalMargin - left - (right || 0);\n  }\n  if (isNaN(height)) {\n    // Height may be NaN if only one value is given except height\n    height = containerHeight - verticalMargin - top - (bottom || 0);\n  }\n  var rect = new BoundingRect(left + margin[3], top + margin[0], width, height);\n  rect.margin = margin;\n  return rect;\n}\n/**\r\n * Position a zr element in viewport\r\n *  Group position is specified by either\r\n *  {left, top}, {right, bottom}\r\n *  If all properties exists, right and bottom will be igonred.\r\n *\r\n * Logic:\r\n *     1. Scale (against origin point in parent coord)\r\n *     2. Rotate (against origin point in parent coord)\r\n *     3. Translate (with el.position by this method)\r\n * So this method only fixes the last step 'Translate', which does not affect\r\n * scaling and rotating.\r\n *\r\n * If be called repeatedly with the same input el, the same result will be gotten.\r\n *\r\n * Return true if the layout happened.\r\n *\r\n * @param el Should have `getBoundingRect` method.\r\n * @param positionInfo\r\n * @param positionInfo.left\r\n * @param positionInfo.top\r\n * @param positionInfo.right\r\n * @param positionInfo.bottom\r\n * @param positionInfo.width Only for opt.boundingModel: 'raw'\r\n * @param positionInfo.height Only for opt.boundingModel: 'raw'\r\n * @param containerRect\r\n * @param margin\r\n * @param opt\r\n * @param opt.hv Only horizontal or only vertical. Default to be [1, 1]\r\n * @param opt.boundingMode\r\n *        Specify how to calculate boundingRect when locating.\r\n *        'all': Position the boundingRect that is transformed and uioned\r\n *               both itself and its descendants.\r\n *               This mode simplies confine the elements in the bounding\r\n *               of their container (e.g., using 'right: 0').\r\n *        'raw': Position the boundingRect that is not transformed and only itself.\r\n *               This mode is useful when you want a element can overflow its\r\n *               container. (Consider a rotated circle needs to be located in a corner.)\r\n *               In this mode positionInfo.width/height can only be number.\r\n */\nexport function positionElement(el, positionInfo, containerRect, margin, opt, out) {\n  var h = !opt || !opt.hv || opt.hv[0];\n  var v = !opt || !opt.hv || opt.hv[1];\n  var boundingMode = opt && opt.boundingMode || 'all';\n  out = out || el;\n  out.x = el.x;\n  out.y = el.y;\n  if (!h && !v) {\n    return false;\n  }\n  var rect;\n  if (boundingMode === 'raw') {\n    rect = el.type === 'group' ? new BoundingRect(0, 0, +positionInfo.width || 0, +positionInfo.height || 0) : el.getBoundingRect();\n  } else {\n    rect = el.getBoundingRect();\n    if (el.needLocalTransform()) {\n      var transform = el.getLocalTransform();\n      // Notice: raw rect may be inner object of el,\n      // which should not be modified.\n      rect = rect.clone();\n      rect.applyTransform(transform);\n    }\n  }\n  // The real width and height can not be specified but calculated by the given el.\n  var layoutRect = getLayoutRect(zrUtil.defaults({\n    width: rect.width,\n    height: rect.height\n  }, positionInfo), containerRect, margin);\n  // Because 'tranlate' is the last step in transform\n  // (see zrender/core/Transformable#getLocalTransform),\n  // we can just only modify el.position to get final result.\n  var dx = h ? layoutRect.x - rect.x : 0;\n  var dy = v ? layoutRect.y - rect.y : 0;\n  if (boundingMode === 'raw') {\n    out.x = dx;\n    out.y = dy;\n  } else {\n    out.x += dx;\n    out.y += dy;\n  }\n  if (out === el) {\n    el.markRedraw();\n  }\n  return true;\n}\n/**\r\n * @param option Contains some of the properties in HV_NAMES.\r\n * @param hvIdx 0: horizontal; 1: vertical.\r\n */\nexport function sizeCalculable(option, hvIdx) {\n  return option[HV_NAMES[hvIdx][0]] != null || option[HV_NAMES[hvIdx][1]] != null && option[HV_NAMES[hvIdx][2]] != null;\n}\nexport function fetchLayoutMode(ins) {\n  var layoutMode = ins.layoutMode || ins.constructor.layoutMode;\n  return zrUtil.isObject(layoutMode) ? layoutMode : layoutMode ? {\n    type: layoutMode\n  } : null;\n}\n/**\r\n * Consider Case:\r\n * When default option has {left: 0, width: 100}, and we set {right: 0}\r\n * through setOption or media query, using normal zrUtil.merge will cause\r\n * {right: 0} does not take effect.\r\n *\r\n * @example\r\n * ComponentModel.extend({\r\n *     init: function () {\r\n *         ...\r\n *         let inputPositionParams = layout.getLayoutParams(option);\r\n *         this.mergeOption(inputPositionParams);\r\n *     },\r\n *     mergeOption: function (newOption) {\r\n *         newOption && zrUtil.merge(thisOption, newOption, true);\r\n *         layout.mergeLayoutParam(thisOption, newOption);\r\n *     }\r\n * });\r\n *\r\n * @param targetOption\r\n * @param newOption\r\n * @param opt\r\n */\nexport function mergeLayoutParam(targetOption, newOption, opt) {\n  var ignoreSize = opt && opt.ignoreSize;\n  !zrUtil.isArray(ignoreSize) && (ignoreSize = [ignoreSize, ignoreSize]);\n  var hResult = merge(HV_NAMES[0], 0);\n  var vResult = merge(HV_NAMES[1], 1);\n  copy(HV_NAMES[0], targetOption, hResult);\n  copy(HV_NAMES[1], targetOption, vResult);\n  function merge(names, hvIdx) {\n    var newParams = {};\n    var newValueCount = 0;\n    var merged = {};\n    var mergedValueCount = 0;\n    var enoughParamNumber = 2;\n    each(names, function (name) {\n      merged[name] = targetOption[name];\n    });\n    each(names, function (name) {\n      // Consider case: newOption.width is null, which is\n      // set by user for removing width setting.\n      hasProp(newOption, name) && (newParams[name] = merged[name] = newOption[name]);\n      hasValue(newParams, name) && newValueCount++;\n      hasValue(merged, name) && mergedValueCount++;\n    });\n    if (ignoreSize[hvIdx]) {\n      // Only one of left/right is premitted to exist.\n      if (hasValue(newOption, names[1])) {\n        merged[names[2]] = null;\n      } else if (hasValue(newOption, names[2])) {\n        merged[names[1]] = null;\n      }\n      return merged;\n    }\n    // Case: newOption: {width: ..., right: ...},\n    // or targetOption: {right: ...} and newOption: {width: ...},\n    // There is no conflict when merged only has params count\n    // little than enoughParamNumber.\n    if (mergedValueCount === enoughParamNumber || !newValueCount) {\n      return merged;\n    }\n    // Case: newOption: {width: ..., right: ...},\n    // Than we can make sure user only want those two, and ignore\n    // all origin params in targetOption.\n    else if (newValueCount >= enoughParamNumber) {\n      return newParams;\n    } else {\n      // Chose another param from targetOption by priority.\n      for (var i = 0; i < names.length; i++) {\n        var name_1 = names[i];\n        if (!hasProp(newParams, name_1) && hasProp(targetOption, name_1)) {\n          newParams[name_1] = targetOption[name_1];\n          break;\n        }\n      }\n      return newParams;\n    }\n  }\n  function hasProp(obj, name) {\n    return obj.hasOwnProperty(name);\n  }\n  function hasValue(obj, name) {\n    return obj[name] != null && obj[name] !== 'auto';\n  }\n  function copy(names, target, source) {\n    each(names, function (name) {\n      target[name] = source[name];\n    });\n  }\n}\n/**\r\n * Retrieve 'left', 'right', 'top', 'bottom', 'width', 'height' from object.\r\n */\nexport function getLayoutParams(source) {\n  return copyLayoutParams({}, source);\n}\n/**\r\n * Retrieve 'left', 'right', 'top', 'bottom', 'width', 'height' from object.\r\n * @param {Object} source\r\n * @return {Object} Result contains those props.\r\n */\nexport function copyLayoutParams(target, source) {\n  source && target && each(LOCATION_PARAMS, function (name) {\n    source.hasOwnProperty(name) && (target[name] = source[name]);\n  });\n  return target;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,SAASC,YAAY,QAAQ,aAAa;AAC1C,OAAO,KAAKC,UAAU,MAAM,aAAa;AACzC,IAAIC,IAAI,GAAGJ,MAAM,CAACI,IAAI;AACtB;AACA;AACA;AACA,OAAO,IAAIC,eAAe,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;AAClF;AACA;AACA;AACA,OAAO,IAAIC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC/E,SAASC,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EAC1D,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAG,CAAC;EACT,IAAIH,QAAQ,IAAI,IAAI,EAAE;IACpBA,QAAQ,GAAGI,QAAQ;EACrB;EACA,IAAIH,SAAS,IAAI,IAAI,EAAE;IACrBA,SAAS,GAAGG,QAAQ;EACtB;EACA,IAAIC,kBAAkB,GAAG,CAAC;EAC1BP,KAAK,CAACQ,SAAS,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;IACpC,IAAIC,IAAI,GAAGF,KAAK,CAACG,eAAe,CAAC,CAAC;IAClC,IAAIC,SAAS,GAAGb,KAAK,CAACc,OAAO,CAACJ,GAAG,GAAG,CAAC,CAAC;IACtC,IAAIK,aAAa,GAAGF,SAAS,IAAIA,SAAS,CAACD,eAAe,CAAC,CAAC;IAC5D,IAAII,KAAK;IACT,IAAIC,KAAK;IACT,IAAIlB,MAAM,KAAK,YAAY,EAAE;MAC3B,IAAImB,KAAK,GAAGP,IAAI,CAACQ,KAAK,IAAIJ,aAAa,GAAG,CAACA,aAAa,CAACX,CAAC,GAAGO,IAAI,CAACP,CAAC,GAAG,CAAC,CAAC;MACxEY,KAAK,GAAGZ,CAAC,GAAGc,KAAK;MACjB;MACA;MACA,IAAIF,KAAK,GAAGd,QAAQ,IAAIO,KAAK,CAACW,OAAO,EAAE;QACrChB,CAAC,GAAG,CAAC;QACLY,KAAK,GAAGE,KAAK;QACbb,CAAC,IAAIE,kBAAkB,GAAGN,GAAG;QAC7BM,kBAAkB,GAAGI,IAAI,CAACU,MAAM;MAClC,CAAC,MAAM;QACL;QACAd,kBAAkB,GAAGe,IAAI,CAACC,GAAG,CAAChB,kBAAkB,EAAEI,IAAI,CAACU,MAAM,CAAC;MAChE;IACF,CAAC,MAAM;MACL,IAAIG,KAAK,GAAGb,IAAI,CAACU,MAAM,IAAIN,aAAa,GAAG,CAACA,aAAa,CAACV,CAAC,GAAGM,IAAI,CAACN,CAAC,GAAG,CAAC,CAAC;MACzEY,KAAK,GAAGZ,CAAC,GAAGmB,KAAK;MACjB;MACA,IAAIP,KAAK,GAAGd,SAAS,IAAIM,KAAK,CAACW,OAAO,EAAE;QACtChB,CAAC,IAAIG,kBAAkB,GAAGN,GAAG;QAC7BI,CAAC,GAAG,CAAC;QACLY,KAAK,GAAGO,KAAK;QACbjB,kBAAkB,GAAGI,IAAI,CAACQ,KAAK;MACjC,CAAC,MAAM;QACLZ,kBAAkB,GAAGe,IAAI,CAACC,GAAG,CAAChB,kBAAkB,EAAEI,IAAI,CAACQ,KAAK,CAAC;MAC/D;IACF;IACA,IAAIV,KAAK,CAACW,OAAO,EAAE;MACjB;IACF;IACAX,KAAK,CAACL,CAAC,GAAGA,CAAC;IACXK,KAAK,CAACJ,CAAC,GAAGA,CAAC;IACXI,KAAK,CAACgB,UAAU,CAAC,CAAC;IAClB1B,MAAM,KAAK,YAAY,GAAGK,CAAC,GAAGY,KAAK,GAAGf,GAAG,GAAGI,CAAC,GAAGY,KAAK,GAAGhB,GAAG;EAC7D,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIyB,GAAG,GAAG5B,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI6B,IAAI,GAAGpC,MAAM,CAACqC,KAAK,CAAC9B,SAAS,EAAE,UAAU,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI+B,IAAI,GAAGtC,MAAM,CAACqC,KAAK,CAAC9B,SAAS,EAAE,YAAY,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgC,gBAAgBA,CAACC,YAAY,EAAEC,aAAa,EAAEC,MAAM,EAAE;EACpE,IAAIC,cAAc,GAAGF,aAAa,CAACb,KAAK;EACxC,IAAIgB,eAAe,GAAGH,aAAa,CAACX,MAAM;EAC1C,IAAIjB,CAAC,GAAGX,YAAY,CAACsC,YAAY,CAACK,IAAI,EAAEF,cAAc,CAAC;EACvD,IAAI7B,CAAC,GAAGZ,YAAY,CAACsC,YAAY,CAACM,GAAG,EAAEF,eAAe,CAAC;EACvD,IAAIG,EAAE,GAAG7C,YAAY,CAACsC,YAAY,CAACQ,KAAK,EAAEL,cAAc,CAAC;EACzD,IAAIM,EAAE,GAAG/C,YAAY,CAACsC,YAAY,CAACU,MAAM,EAAEN,eAAe,CAAC;EAC3D,CAACO,KAAK,CAACtC,CAAC,CAAC,IAAIsC,KAAK,CAACC,UAAU,CAACZ,YAAY,CAACK,IAAI,CAAC,CAAC,MAAMhC,CAAC,GAAG,CAAC,CAAC;EAC7D,CAACsC,KAAK,CAACJ,EAAE,CAAC,IAAII,KAAK,CAACC,UAAU,CAACZ,YAAY,CAACQ,KAAK,CAAC,CAAC,MAAMD,EAAE,GAAGJ,cAAc,CAAC;EAC7E,CAACQ,KAAK,CAACrC,CAAC,CAAC,IAAIqC,KAAK,CAACC,UAAU,CAACZ,YAAY,CAACM,GAAG,CAAC,CAAC,MAAMhC,CAAC,GAAG,CAAC,CAAC;EAC5D,CAACqC,KAAK,CAACF,EAAE,CAAC,IAAIE,KAAK,CAACC,UAAU,CAACZ,YAAY,CAACU,MAAM,CAAC,CAAC,MAAMD,EAAE,GAAGL,eAAe,CAAC;EAC/EF,MAAM,GAAGvC,UAAU,CAACkD,iBAAiB,CAACX,MAAM,IAAI,CAAC,CAAC;EAClD,OAAO;IACLd,KAAK,EAAEG,IAAI,CAACC,GAAG,CAACe,EAAE,GAAGlC,CAAC,GAAG6B,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClDZ,MAAM,EAAEC,IAAI,CAACC,GAAG,CAACiB,EAAE,GAAGnC,CAAC,GAAG4B,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;EACpD,CAAC;AACH;AACA;AACA;AACA;AACA,OAAO,SAASY,aAAaA,CAACd,YAAY,EAAEC,aAAa,EAAEC,MAAM,EAAE;EACjEA,MAAM,GAAGvC,UAAU,CAACkD,iBAAiB,CAACX,MAAM,IAAI,CAAC,CAAC;EAClD,IAAIC,cAAc,GAAGF,aAAa,CAACb,KAAK;EACxC,IAAIgB,eAAe,GAAGH,aAAa,CAACX,MAAM;EAC1C,IAAIe,IAAI,GAAG3C,YAAY,CAACsC,YAAY,CAACK,IAAI,EAAEF,cAAc,CAAC;EAC1D,IAAIG,GAAG,GAAG5C,YAAY,CAACsC,YAAY,CAACM,GAAG,EAAEF,eAAe,CAAC;EACzD,IAAII,KAAK,GAAG9C,YAAY,CAACsC,YAAY,CAACQ,KAAK,EAAEL,cAAc,CAAC;EAC5D,IAAIO,MAAM,GAAGhD,YAAY,CAACsC,YAAY,CAACU,MAAM,EAAEN,eAAe,CAAC;EAC/D,IAAIhB,KAAK,GAAG1B,YAAY,CAACsC,YAAY,CAACZ,KAAK,EAAEe,cAAc,CAAC;EAC5D,IAAIb,MAAM,GAAG5B,YAAY,CAACsC,YAAY,CAACV,MAAM,EAAEc,eAAe,CAAC;EAC/D,IAAIW,cAAc,GAAGb,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;EAC1C,IAAIc,gBAAgB,GAAGd,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;EAC5C,IAAIe,MAAM,GAAGjB,YAAY,CAACiB,MAAM;EAChC;EACA,IAAIN,KAAK,CAACvB,KAAK,CAAC,EAAE;IAChBA,KAAK,GAAGe,cAAc,GAAGK,KAAK,GAAGQ,gBAAgB,GAAGX,IAAI;EAC1D;EACA,IAAIM,KAAK,CAACrB,MAAM,CAAC,EAAE;IACjBA,MAAM,GAAGc,eAAe,GAAGM,MAAM,GAAGK,cAAc,GAAGT,GAAG;EAC1D;EACA,IAAIW,MAAM,IAAI,IAAI,EAAE;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIN,KAAK,CAACvB,KAAK,CAAC,IAAIuB,KAAK,CAACrB,MAAM,CAAC,EAAE;MACjC,IAAI2B,MAAM,GAAGd,cAAc,GAAGC,eAAe,EAAE;QAC7ChB,KAAK,GAAGe,cAAc,GAAG,GAAG;MAC9B,CAAC,MAAM;QACLb,MAAM,GAAGc,eAAe,GAAG,GAAG;MAChC;IACF;IACA;IACA,IAAIO,KAAK,CAACvB,KAAK,CAAC,EAAE;MAChBA,KAAK,GAAG6B,MAAM,GAAG3B,MAAM;IACzB;IACA,IAAIqB,KAAK,CAACrB,MAAM,CAAC,EAAE;MACjBA,MAAM,GAAGF,KAAK,GAAG6B,MAAM;IACzB;EACF;EACA;EACA,IAAIN,KAAK,CAACN,IAAI,CAAC,EAAE;IACfA,IAAI,GAAGF,cAAc,GAAGK,KAAK,GAAGpB,KAAK,GAAG4B,gBAAgB;EAC1D;EACA,IAAIL,KAAK,CAACL,GAAG,CAAC,EAAE;IACdA,GAAG,GAAGF,eAAe,GAAGM,MAAM,GAAGpB,MAAM,GAAGyB,cAAc;EAC1D;EACA;EACA,QAAQf,YAAY,CAACK,IAAI,IAAIL,YAAY,CAACQ,KAAK;IAC7C,KAAK,QAAQ;MACXH,IAAI,GAAGF,cAAc,GAAG,CAAC,GAAGf,KAAK,GAAG,CAAC,GAAGc,MAAM,CAAC,CAAC,CAAC;MACjD;IACF,KAAK,OAAO;MACVG,IAAI,GAAGF,cAAc,GAAGf,KAAK,GAAG4B,gBAAgB;MAChD;EACJ;EACA,QAAQhB,YAAY,CAACM,GAAG,IAAIN,YAAY,CAACU,MAAM;IAC7C,KAAK,QAAQ;IACb,KAAK,QAAQ;MACXJ,GAAG,GAAGF,eAAe,GAAG,CAAC,GAAGd,MAAM,GAAG,CAAC,GAAGY,MAAM,CAAC,CAAC,CAAC;MAClD;IACF,KAAK,QAAQ;MACXI,GAAG,GAAGF,eAAe,GAAGd,MAAM,GAAGyB,cAAc;MAC/C;EACJ;EACA;EACAV,IAAI,GAAGA,IAAI,IAAI,CAAC;EAChBC,GAAG,GAAGA,GAAG,IAAI,CAAC;EACd,IAAIK,KAAK,CAACvB,KAAK,CAAC,EAAE;IAChB;IACAA,KAAK,GAAGe,cAAc,GAAGa,gBAAgB,GAAGX,IAAI,IAAIG,KAAK,IAAI,CAAC,CAAC;EACjE;EACA,IAAIG,KAAK,CAACrB,MAAM,CAAC,EAAE;IACjB;IACAA,MAAM,GAAGc,eAAe,GAAGW,cAAc,GAAGT,GAAG,IAAII,MAAM,IAAI,CAAC,CAAC;EACjE;EACA,IAAI9B,IAAI,GAAG,IAAInB,YAAY,CAAC4C,IAAI,GAAGH,MAAM,CAAC,CAAC,CAAC,EAAEI,GAAG,GAAGJ,MAAM,CAAC,CAAC,CAAC,EAAEd,KAAK,EAAEE,MAAM,CAAC;EAC7EV,IAAI,CAACsB,MAAM,GAAGA,MAAM;EACpB,OAAOtB,IAAI;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsC,eAAeA,CAACC,EAAE,EAAEnB,YAAY,EAAEC,aAAa,EAAEC,MAAM,EAAEkB,GAAG,EAAEC,GAAG,EAAE;EACjF,IAAIC,CAAC,GAAG,CAACF,GAAG,IAAI,CAACA,GAAG,CAACG,EAAE,IAAIH,GAAG,CAACG,EAAE,CAAC,CAAC,CAAC;EACpC,IAAIC,CAAC,GAAG,CAACJ,GAAG,IAAI,CAACA,GAAG,CAACG,EAAE,IAAIH,GAAG,CAACG,EAAE,CAAC,CAAC,CAAC;EACpC,IAAIE,YAAY,GAAGL,GAAG,IAAIA,GAAG,CAACK,YAAY,IAAI,KAAK;EACnDJ,GAAG,GAAGA,GAAG,IAAIF,EAAE;EACfE,GAAG,CAAChD,CAAC,GAAG8C,EAAE,CAAC9C,CAAC;EACZgD,GAAG,CAAC/C,CAAC,GAAG6C,EAAE,CAAC7C,CAAC;EACZ,IAAI,CAACgD,CAAC,IAAI,CAACE,CAAC,EAAE;IACZ,OAAO,KAAK;EACd;EACA,IAAI5C,IAAI;EACR,IAAI6C,YAAY,KAAK,KAAK,EAAE;IAC1B7C,IAAI,GAAGuC,EAAE,CAACO,IAAI,KAAK,OAAO,GAAG,IAAIjE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAACuC,YAAY,CAACZ,KAAK,IAAI,CAAC,EAAE,CAACY,YAAY,CAACV,MAAM,IAAI,CAAC,CAAC,GAAG6B,EAAE,CAACtC,eAAe,CAAC,CAAC;EACjI,CAAC,MAAM;IACLD,IAAI,GAAGuC,EAAE,CAACtC,eAAe,CAAC,CAAC;IAC3B,IAAIsC,EAAE,CAACQ,kBAAkB,CAAC,CAAC,EAAE;MAC3B,IAAIC,SAAS,GAAGT,EAAE,CAACU,iBAAiB,CAAC,CAAC;MACtC;MACA;MACAjD,IAAI,GAAGA,IAAI,CAACkD,KAAK,CAAC,CAAC;MACnBlD,IAAI,CAACmD,cAAc,CAACH,SAAS,CAAC;IAChC;EACF;EACA;EACA,IAAII,UAAU,GAAGlB,aAAa,CAACtD,MAAM,CAACyE,QAAQ,CAAC;IAC7C7C,KAAK,EAAER,IAAI,CAACQ,KAAK;IACjBE,MAAM,EAAEV,IAAI,CAACU;EACf,CAAC,EAAEU,YAAY,CAAC,EAAEC,aAAa,EAAEC,MAAM,CAAC;EACxC;EACA;EACA;EACA,IAAIgC,EAAE,GAAGZ,CAAC,GAAGU,UAAU,CAAC3D,CAAC,GAAGO,IAAI,CAACP,CAAC,GAAG,CAAC;EACtC,IAAI8D,EAAE,GAAGX,CAAC,GAAGQ,UAAU,CAAC1D,CAAC,GAAGM,IAAI,CAACN,CAAC,GAAG,CAAC;EACtC,IAAImD,YAAY,KAAK,KAAK,EAAE;IAC1BJ,GAAG,CAAChD,CAAC,GAAG6D,EAAE;IACVb,GAAG,CAAC/C,CAAC,GAAG6D,EAAE;EACZ,CAAC,MAAM;IACLd,GAAG,CAAChD,CAAC,IAAI6D,EAAE;IACXb,GAAG,CAAC/C,CAAC,IAAI6D,EAAE;EACb;EACA,IAAId,GAAG,KAAKF,EAAE,EAAE;IACdA,EAAE,CAACzB,UAAU,CAAC,CAAC;EACjB;EACA,OAAO,IAAI;AACb;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS0C,cAAcA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC5C,OAAOD,MAAM,CAACvE,QAAQ,CAACwE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAID,MAAM,CAACvE,QAAQ,CAACwE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAID,MAAM,CAACvE,QAAQ,CAACwE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AACvH;AACA,OAAO,SAASC,eAAeA,CAACC,GAAG,EAAE;EACnC,IAAIC,UAAU,GAAGD,GAAG,CAACC,UAAU,IAAID,GAAG,CAACE,WAAW,CAACD,UAAU;EAC7D,OAAOjF,MAAM,CAACmF,QAAQ,CAACF,UAAU,CAAC,GAAGA,UAAU,GAAGA,UAAU,GAAG;IAC7Df,IAAI,EAAEe;EACR,CAAC,GAAG,IAAI;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,gBAAgBA,CAACC,YAAY,EAAEC,SAAS,EAAE1B,GAAG,EAAE;EAC7D,IAAI2B,UAAU,GAAG3B,GAAG,IAAIA,GAAG,CAAC2B,UAAU;EACtC,CAACvF,MAAM,CAACwF,OAAO,CAACD,UAAU,CAAC,KAAKA,UAAU,GAAG,CAACA,UAAU,EAAEA,UAAU,CAAC,CAAC;EACtE,IAAIE,OAAO,GAAGC,KAAK,CAACpF,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACnC,IAAIqF,OAAO,GAAGD,KAAK,CAACpF,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACnCsF,IAAI,CAACtF,QAAQ,CAAC,CAAC,CAAC,EAAE+E,YAAY,EAAEI,OAAO,CAAC;EACxCG,IAAI,CAACtF,QAAQ,CAAC,CAAC,CAAC,EAAE+E,YAAY,EAAEM,OAAO,CAAC;EACxC,SAASD,KAAKA,CAACG,KAAK,EAAEf,KAAK,EAAE;IAC3B,IAAIgB,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,MAAM,GAAG,CAAC,CAAC;IACf,IAAIC,gBAAgB,GAAG,CAAC;IACxB,IAAIC,iBAAiB,GAAG,CAAC;IACzB9F,IAAI,CAACyF,KAAK,EAAE,UAAUM,IAAI,EAAE;MAC1BH,MAAM,CAACG,IAAI,CAAC,GAAGd,YAAY,CAACc,IAAI,CAAC;IACnC,CAAC,CAAC;IACF/F,IAAI,CAACyF,KAAK,EAAE,UAAUM,IAAI,EAAE;MAC1B;MACA;MACAC,OAAO,CAACd,SAAS,EAAEa,IAAI,CAAC,KAAKL,SAAS,CAACK,IAAI,CAAC,GAAGH,MAAM,CAACG,IAAI,CAAC,GAAGb,SAAS,CAACa,IAAI,CAAC,CAAC;MAC9EE,QAAQ,CAACP,SAAS,EAAEK,IAAI,CAAC,IAAIJ,aAAa,EAAE;MAC5CM,QAAQ,CAACL,MAAM,EAAEG,IAAI,CAAC,IAAIF,gBAAgB,EAAE;IAC9C,CAAC,CAAC;IACF,IAAIV,UAAU,CAACT,KAAK,CAAC,EAAE;MACrB;MACA,IAAIuB,QAAQ,CAACf,SAAS,EAAEO,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QACjCG,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;MACzB,CAAC,MAAM,IAAIQ,QAAQ,CAACf,SAAS,EAAEO,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;QACxCG,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;MACzB;MACA,OAAOG,MAAM;IACf;IACA;IACA;IACA;IACA;IACA,IAAIC,gBAAgB,KAAKC,iBAAiB,IAAI,CAACH,aAAa,EAAE;MAC5D,OAAOC,MAAM;IACf;IACA;IACA;IACA;IAAA,KACK,IAAID,aAAa,IAAIG,iBAAiB,EAAE;MAC3C,OAAOJ,SAAS;IAClB,CAAC,MAAM;MACL;MACA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,IAAIE,MAAM,GAAGX,KAAK,CAACS,CAAC,CAAC;QACrB,IAAI,CAACF,OAAO,CAACN,SAAS,EAAEU,MAAM,CAAC,IAAIJ,OAAO,CAACf,YAAY,EAAEmB,MAAM,CAAC,EAAE;UAChEV,SAAS,CAACU,MAAM,CAAC,GAAGnB,YAAY,CAACmB,MAAM,CAAC;UACxC;QACF;MACF;MACA,OAAOV,SAAS;IAClB;EACF;EACA,SAASM,OAAOA,CAACK,GAAG,EAAEN,IAAI,EAAE;IAC1B,OAAOM,GAAG,CAACC,cAAc,CAACP,IAAI,CAAC;EACjC;EACA,SAASE,QAAQA,CAACI,GAAG,EAAEN,IAAI,EAAE;IAC3B,OAAOM,GAAG,CAACN,IAAI,CAAC,IAAI,IAAI,IAAIM,GAAG,CAACN,IAAI,CAAC,KAAK,MAAM;EAClD;EACA,SAASP,IAAIA,CAACC,KAAK,EAAEc,MAAM,EAAEC,MAAM,EAAE;IACnCxG,IAAI,CAACyF,KAAK,EAAE,UAAUM,IAAI,EAAE;MAC1BQ,MAAM,CAACR,IAAI,CAAC,GAAGS,MAAM,CAACT,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ;AACF;AACA;AACA;AACA;AACA,OAAO,SAASU,eAAeA,CAACD,MAAM,EAAE;EACtC,OAAOE,gBAAgB,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,gBAAgBA,CAACH,MAAM,EAAEC,MAAM,EAAE;EAC/CA,MAAM,IAAID,MAAM,IAAIvG,IAAI,CAACC,eAAe,EAAE,UAAU8F,IAAI,EAAE;IACxDS,MAAM,CAACF,cAAc,CAACP,IAAI,CAAC,KAAKQ,MAAM,CAACR,IAAI,CAAC,GAAGS,MAAM,CAACT,IAAI,CAAC,CAAC;EAC9D,CAAC,CAAC;EACF,OAAOQ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}