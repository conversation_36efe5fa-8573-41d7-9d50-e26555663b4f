{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport _extends from 'babel-runtime/helpers/extends';\nimport _typeof from 'babel-runtime/helpers/typeof';\nimport { format, complementError, asyncMap, warning, deepMerge } from './util';\nimport validators from './validator/';\nimport { messages as defaultMessages, newMessages } from './messages';\n\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */\nfunction Schema(descriptor) {\n  this.rules = null;\n  this._messages = defaultMessages;\n  this.define(descriptor);\n}\nSchema.prototype = {\n  messages: function messages(_messages) {\n    if (_messages) {\n      this._messages = deepMerge(newMessages(), _messages);\n    }\n    return this._messages;\n  },\n  define: function define(rules) {\n    if (!rules) {\n      throw new Error('Cannot configure a schema with no rules');\n    }\n    if ((typeof rules === 'undefined' ? 'undefined' : _typeof(rules)) !== 'object' || Array.isArray(rules)) {\n      throw new Error('Rules must be an object');\n    }\n    this.rules = {};\n    var z = void 0;\n    var item = void 0;\n    for (z in rules) {\n      if (rules.hasOwnProperty(z)) {\n        item = rules[z];\n        this.rules[z] = Array.isArray(item) ? item : [item];\n      }\n    }\n  },\n  validate: function validate(source_) {\n    var _this = this;\n    var o = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var oc = arguments[2];\n    var source = source_;\n    var options = o;\n    var callback = oc;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!this.rules || Object.keys(this.rules).length === 0) {\n      if (callback) {\n        callback();\n      }\n      return;\n    }\n    function complete(results) {\n      var i = void 0;\n      var field = void 0;\n      var errors = [];\n      var fields = {};\n      function add(e) {\n        if (Array.isArray(e)) {\n          errors = errors.concat.apply(errors, e);\n        } else {\n          errors.push(e);\n        }\n      }\n      for (i = 0; i < results.length; i++) {\n        add(results[i]);\n      }\n      if (!errors.length) {\n        errors = null;\n        fields = null;\n      } else {\n        for (i = 0; i < errors.length; i++) {\n          field = errors[i].field;\n          fields[field] = fields[field] || [];\n          fields[field].push(errors[i]);\n        }\n      }\n      callback(errors, fields);\n    }\n    if (options.messages) {\n      var messages = this.messages();\n      if (messages === defaultMessages) {\n        messages = newMessages();\n      }\n      deepMerge(messages, options.messages);\n      options.messages = messages;\n    } else {\n      options.messages = this.messages();\n    }\n    var arr = void 0;\n    var value = void 0;\n    var series = {};\n    var keys = options.keys || Object.keys(this.rules);\n    keys.forEach(function (z) {\n      arr = _this.rules[z];\n      value = source[z];\n      arr.forEach(function (r) {\n        var rule = r;\n        if (typeof rule.transform === 'function') {\n          if (source === source_) {\n            source = _extends({}, source);\n          }\n          value = source[z] = rule.transform(value);\n        }\n        if (typeof rule === 'function') {\n          rule = {\n            validator: rule\n          };\n        } else {\n          rule = _extends({}, rule);\n        }\n        rule.validator = _this.getValidationMethod(rule);\n        rule.field = z;\n        rule.fullField = rule.fullField || z;\n        rule.type = _this.getType(rule);\n        if (!rule.validator) {\n          return;\n        }\n        series[z] = series[z] || [];\n        series[z].push({\n          rule: rule,\n          value: value,\n          source: source,\n          field: z\n        });\n      });\n    });\n    var errorFields = {};\n    asyncMap(series, options, function (data, doIt) {\n      var rule = data.rule;\n      var deep = (rule.type === 'object' || rule.type === 'array') && (_typeof(rule.fields) === 'object' || _typeof(rule.defaultField) === 'object');\n      deep = deep && (rule.required || !rule.required && data.value);\n      rule.field = data.field;\n      function addFullfield(key, schema) {\n        return _extends({}, schema, {\n          fullField: rule.fullField + '.' + key\n        });\n      }\n      function cb() {\n        var e = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n        var errors = e;\n        if (!Array.isArray(errors)) {\n          errors = [errors];\n        }\n        if (errors.length) {\n          warning('async-validator:', errors);\n        }\n        if (errors.length && rule.message) {\n          errors = [].concat(rule.message);\n        }\n        errors = errors.map(complementError(rule));\n        if (options.first && errors.length) {\n          errorFields[rule.field] = 1;\n          return doIt(errors);\n        }\n        if (!deep) {\n          doIt(errors);\n        } else {\n          // if rule is required but the target object\n          // does not exist fail at the rule level and don't\n          // go deeper\n          if (rule.required && !data.value) {\n            if (rule.message) {\n              errors = [].concat(rule.message).map(complementError(rule));\n            } else if (options.error) {\n              errors = [options.error(rule, format(options.messages.required, rule.field))];\n            } else {\n              errors = [];\n            }\n            return doIt(errors);\n          }\n          var fieldsSchema = {};\n          if (rule.defaultField) {\n            for (var k in data.value) {\n              if (data.value.hasOwnProperty(k)) {\n                fieldsSchema[k] = rule.defaultField;\n              }\n            }\n          }\n          fieldsSchema = _extends({}, fieldsSchema, data.rule.fields);\n          for (var f in fieldsSchema) {\n            if (fieldsSchema.hasOwnProperty(f)) {\n              var fieldSchema = Array.isArray(fieldsSchema[f]) ? fieldsSchema[f] : [fieldsSchema[f]];\n              fieldsSchema[f] = fieldSchema.map(addFullfield.bind(null, f));\n            }\n          }\n          var schema = new Schema(fieldsSchema);\n          schema.messages(options.messages);\n          if (data.rule.options) {\n            data.rule.options.messages = options.messages;\n            data.rule.options.error = options.error;\n          }\n          schema.validate(data.value, data.rule.options || options, function (errs) {\n            doIt(errs && errs.length ? errors.concat(errs) : errs);\n          });\n        }\n      }\n      var res = rule.validator(rule, data.value, cb, data.source, options);\n      if (res && res.then) {\n        res.then(function () {\n          return cb();\n        }, function (e) {\n          return cb(e);\n        });\n      }\n    }, function (results) {\n      complete(results);\n    });\n  },\n  getType: function getType(rule) {\n    if (rule.type === undefined && rule.pattern instanceof RegExp) {\n      rule.type = 'pattern';\n    }\n    if (typeof rule.validator !== 'function' && rule.type && !validators.hasOwnProperty(rule.type)) {\n      throw new Error(format('Unknown rule type %s', rule.type));\n    }\n    return rule.type || 'string';\n  },\n  getValidationMethod: function getValidationMethod(rule) {\n    if (typeof rule.validator === 'function') {\n      return rule.validator;\n    }\n    var keys = Object.keys(rule);\n    var messageIndex = keys.indexOf('message');\n    if (messageIndex !== -1) {\n      keys.splice(messageIndex, 1);\n    }\n    if (keys.length === 1 && keys[0] === 'required') {\n      return validators.required;\n    }\n    return validators[this.getType(rule)] || false;\n  }\n};\nSchema.register = function register(type, validator) {\n  if (typeof validator !== 'function') {\n    throw new Error('Cannot register a validator by type, validator is not a function');\n  }\n  validators[type] = validator;\n};\nSchema.messages = defaultMessages;\nexport default Schema;", "map": {"version": 3, "names": ["_extends", "_typeof", "format", "complementError", "asyncMap", "warning", "deepMerge", "validators", "messages", "defaultMessages", "newMessages", "<PERSON><PERSON><PERSON>", "descriptor", "rules", "_messages", "define", "prototype", "Error", "Array", "isArray", "z", "item", "hasOwnProperty", "validate", "source_", "_this", "o", "arguments", "length", "undefined", "oc", "source", "options", "callback", "Object", "keys", "complete", "results", "i", "field", "errors", "fields", "add", "e", "concat", "apply", "push", "arr", "value", "series", "for<PERSON>ach", "r", "rule", "transform", "validator", "getValidationMethod", "fullField", "type", "getType", "errorFields", "data", "doIt", "deep", "defaultField", "required", "add<PERSON><PERSON><PERSON>", "key", "schema", "cb", "message", "map", "first", "error", "fieldsSchema", "k", "f", "fieldSchema", "bind", "errs", "res", "then", "pattern", "RegExp", "messageIndex", "indexOf", "splice", "register"], "sources": ["D:/FastBI/datafront/node_modules/async-validator/es/index.js"], "sourcesContent": ["import _extends from 'babel-runtime/helpers/extends';\nimport _typeof from 'babel-runtime/helpers/typeof';\nimport { format, complementError, asyncMap, warning, deepMerge } from './util';\nimport validators from './validator/';\nimport { messages as defaultMessages, newMessages } from './messages';\n\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */\nfunction Schema(descriptor) {\n  this.rules = null;\n  this._messages = defaultMessages;\n  this.define(descriptor);\n}\n\nSchema.prototype = {\n  messages: function messages(_messages) {\n    if (_messages) {\n      this._messages = deepMerge(newMessages(), _messages);\n    }\n    return this._messages;\n  },\n  define: function define(rules) {\n    if (!rules) {\n      throw new Error('Cannot configure a schema with no rules');\n    }\n    if ((typeof rules === 'undefined' ? 'undefined' : _typeof(rules)) !== 'object' || Array.isArray(rules)) {\n      throw new Error('Rules must be an object');\n    }\n    this.rules = {};\n    var z = void 0;\n    var item = void 0;\n    for (z in rules) {\n      if (rules.hasOwnProperty(z)) {\n        item = rules[z];\n        this.rules[z] = Array.isArray(item) ? item : [item];\n      }\n    }\n  },\n  validate: function validate(source_) {\n    var _this = this;\n\n    var o = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var oc = arguments[2];\n\n    var source = source_;\n    var options = o;\n    var callback = oc;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (!this.rules || Object.keys(this.rules).length === 0) {\n      if (callback) {\n        callback();\n      }\n      return;\n    }\n    function complete(results) {\n      var i = void 0;\n      var field = void 0;\n      var errors = [];\n      var fields = {};\n\n      function add(e) {\n        if (Array.isArray(e)) {\n          errors = errors.concat.apply(errors, e);\n        } else {\n          errors.push(e);\n        }\n      }\n\n      for (i = 0; i < results.length; i++) {\n        add(results[i]);\n      }\n      if (!errors.length) {\n        errors = null;\n        fields = null;\n      } else {\n        for (i = 0; i < errors.length; i++) {\n          field = errors[i].field;\n          fields[field] = fields[field] || [];\n          fields[field].push(errors[i]);\n        }\n      }\n      callback(errors, fields);\n    }\n\n    if (options.messages) {\n      var messages = this.messages();\n      if (messages === defaultMessages) {\n        messages = newMessages();\n      }\n      deepMerge(messages, options.messages);\n      options.messages = messages;\n    } else {\n      options.messages = this.messages();\n    }\n    var arr = void 0;\n    var value = void 0;\n    var series = {};\n    var keys = options.keys || Object.keys(this.rules);\n    keys.forEach(function (z) {\n      arr = _this.rules[z];\n      value = source[z];\n      arr.forEach(function (r) {\n        var rule = r;\n        if (typeof rule.transform === 'function') {\n          if (source === source_) {\n            source = _extends({}, source);\n          }\n          value = source[z] = rule.transform(value);\n        }\n        if (typeof rule === 'function') {\n          rule = {\n            validator: rule\n          };\n        } else {\n          rule = _extends({}, rule);\n        }\n        rule.validator = _this.getValidationMethod(rule);\n        rule.field = z;\n        rule.fullField = rule.fullField || z;\n        rule.type = _this.getType(rule);\n        if (!rule.validator) {\n          return;\n        }\n        series[z] = series[z] || [];\n        series[z].push({\n          rule: rule,\n          value: value,\n          source: source,\n          field: z\n        });\n      });\n    });\n    var errorFields = {};\n    asyncMap(series, options, function (data, doIt) {\n      var rule = data.rule;\n      var deep = (rule.type === 'object' || rule.type === 'array') && (_typeof(rule.fields) === 'object' || _typeof(rule.defaultField) === 'object');\n      deep = deep && (rule.required || !rule.required && data.value);\n      rule.field = data.field;\n      function addFullfield(key, schema) {\n        return _extends({}, schema, {\n          fullField: rule.fullField + '.' + key\n        });\n      }\n\n      function cb() {\n        var e = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n\n        var errors = e;\n        if (!Array.isArray(errors)) {\n          errors = [errors];\n        }\n        if (errors.length) {\n          warning('async-validator:', errors);\n        }\n        if (errors.length && rule.message) {\n          errors = [].concat(rule.message);\n        }\n\n        errors = errors.map(complementError(rule));\n\n        if (options.first && errors.length) {\n          errorFields[rule.field] = 1;\n          return doIt(errors);\n        }\n        if (!deep) {\n          doIt(errors);\n        } else {\n          // if rule is required but the target object\n          // does not exist fail at the rule level and don't\n          // go deeper\n          if (rule.required && !data.value) {\n            if (rule.message) {\n              errors = [].concat(rule.message).map(complementError(rule));\n            } else if (options.error) {\n              errors = [options.error(rule, format(options.messages.required, rule.field))];\n            } else {\n              errors = [];\n            }\n            return doIt(errors);\n          }\n\n          var fieldsSchema = {};\n          if (rule.defaultField) {\n            for (var k in data.value) {\n              if (data.value.hasOwnProperty(k)) {\n                fieldsSchema[k] = rule.defaultField;\n              }\n            }\n          }\n          fieldsSchema = _extends({}, fieldsSchema, data.rule.fields);\n          for (var f in fieldsSchema) {\n            if (fieldsSchema.hasOwnProperty(f)) {\n              var fieldSchema = Array.isArray(fieldsSchema[f]) ? fieldsSchema[f] : [fieldsSchema[f]];\n              fieldsSchema[f] = fieldSchema.map(addFullfield.bind(null, f));\n            }\n          }\n          var schema = new Schema(fieldsSchema);\n          schema.messages(options.messages);\n          if (data.rule.options) {\n            data.rule.options.messages = options.messages;\n            data.rule.options.error = options.error;\n          }\n          schema.validate(data.value, data.rule.options || options, function (errs) {\n            doIt(errs && errs.length ? errors.concat(errs) : errs);\n          });\n        }\n      }\n\n      var res = rule.validator(rule, data.value, cb, data.source, options);\n      if (res && res.then) {\n        res.then(function () {\n          return cb();\n        }, function (e) {\n          return cb(e);\n        });\n      }\n    }, function (results) {\n      complete(results);\n    });\n  },\n  getType: function getType(rule) {\n    if (rule.type === undefined && rule.pattern instanceof RegExp) {\n      rule.type = 'pattern';\n    }\n    if (typeof rule.validator !== 'function' && rule.type && !validators.hasOwnProperty(rule.type)) {\n      throw new Error(format('Unknown rule type %s', rule.type));\n    }\n    return rule.type || 'string';\n  },\n  getValidationMethod: function getValidationMethod(rule) {\n    if (typeof rule.validator === 'function') {\n      return rule.validator;\n    }\n    var keys = Object.keys(rule);\n    var messageIndex = keys.indexOf('message');\n    if (messageIndex !== -1) {\n      keys.splice(messageIndex, 1);\n    }\n    if (keys.length === 1 && keys[0] === 'required') {\n      return validators.required;\n    }\n    return validators[this.getType(rule)] || false;\n  }\n};\n\nSchema.register = function register(type, validator) {\n  if (typeof validator !== 'function') {\n    throw new Error('Cannot register a validator by type, validator is not a function');\n  }\n  validators[type] = validator;\n};\n\nSchema.messages = defaultMessages;\n\nexport default Schema;"], "mappings": ";;;;AAAA,OAAOA,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,OAAO,MAAM,8BAA8B;AAClD,SAASC,MAAM,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,QAAQ,QAAQ;AAC9E,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,QAAQ,IAAIC,eAAe,EAAEC,WAAW,QAAQ,YAAY;;AAErE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,UAAU,EAAE;EAC1B,IAAI,CAACC,KAAK,GAAG,IAAI;EACjB,IAAI,CAACC,SAAS,GAAGL,eAAe;EAChC,IAAI,CAACM,MAAM,CAACH,UAAU,CAAC;AACzB;AAEAD,MAAM,CAACK,SAAS,GAAG;EACjBR,QAAQ,EAAE,SAASA,QAAQA,CAACM,SAAS,EAAE;IACrC,IAAIA,SAAS,EAAE;MACb,IAAI,CAACA,SAAS,GAAGR,SAAS,CAACI,WAAW,CAAC,CAAC,EAAEI,SAAS,CAAC;IACtD;IACA,OAAO,IAAI,CAACA,SAAS;EACvB,CAAC;EACDC,MAAM,EAAE,SAASA,MAAMA,CAACF,KAAK,EAAE;IAC7B,IAAI,CAACA,KAAK,EAAE;MACV,MAAM,IAAII,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IACA,IAAI,CAAC,OAAOJ,KAAK,KAAK,WAAW,GAAG,WAAW,GAAGZ,OAAO,CAACY,KAAK,CAAC,MAAM,QAAQ,IAAIK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,EAAE;MACtG,MAAM,IAAII,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACA,IAAI,CAACJ,KAAK,GAAG,CAAC,CAAC;IACf,IAAIO,CAAC,GAAG,KAAK,CAAC;IACd,IAAIC,IAAI,GAAG,KAAK,CAAC;IACjB,KAAKD,CAAC,IAAIP,KAAK,EAAE;MACf,IAAIA,KAAK,CAACS,cAAc,CAACF,CAAC,CAAC,EAAE;QAC3BC,IAAI,GAAGR,KAAK,CAACO,CAAC,CAAC;QACf,IAAI,CAACP,KAAK,CAACO,CAAC,CAAC,GAAGF,KAAK,CAACC,OAAO,CAACE,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;MACrD;IACF;EACF,CAAC;EACDE,QAAQ,EAAE,SAASA,QAAQA,CAACC,OAAO,EAAE;IACnC,IAAIC,KAAK,GAAG,IAAI;IAEhB,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC9E,IAAIG,EAAE,GAAGH,SAAS,CAAC,CAAC,CAAC;IAErB,IAAII,MAAM,GAAGP,OAAO;IACpB,IAAIQ,OAAO,GAAGN,CAAC;IACf,IAAIO,QAAQ,GAAGH,EAAE;IACjB,IAAI,OAAOE,OAAO,KAAK,UAAU,EAAE;MACjCC,QAAQ,GAAGD,OAAO;MAClBA,OAAO,GAAG,CAAC,CAAC;IACd;IACA,IAAI,CAAC,IAAI,CAACnB,KAAK,IAAIqB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,KAAK,CAAC,CAACe,MAAM,KAAK,CAAC,EAAE;MACvD,IAAIK,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;MACA;IACF;IACA,SAASG,QAAQA,CAACC,OAAO,EAAE;MACzB,IAAIC,CAAC,GAAG,KAAK,CAAC;MACd,IAAIC,KAAK,GAAG,KAAK,CAAC;MAClB,IAAIC,MAAM,GAAG,EAAE;MACf,IAAIC,MAAM,GAAG,CAAC,CAAC;MAEf,SAASC,GAAGA,CAACC,CAAC,EAAE;QACd,IAAIzB,KAAK,CAACC,OAAO,CAACwB,CAAC,CAAC,EAAE;UACpBH,MAAM,GAAGA,MAAM,CAACI,MAAM,CAACC,KAAK,CAACL,MAAM,EAAEG,CAAC,CAAC;QACzC,CAAC,MAAM;UACLH,MAAM,CAACM,IAAI,CAACH,CAAC,CAAC;QAChB;MACF;MAEA,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAACT,MAAM,EAAEU,CAAC,EAAE,EAAE;QACnCI,GAAG,CAACL,OAAO,CAACC,CAAC,CAAC,CAAC;MACjB;MACA,IAAI,CAACE,MAAM,CAACZ,MAAM,EAAE;QAClBY,MAAM,GAAG,IAAI;QACbC,MAAM,GAAG,IAAI;MACf,CAAC,MAAM;QACL,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,CAACZ,MAAM,EAAEU,CAAC,EAAE,EAAE;UAClCC,KAAK,GAAGC,MAAM,CAACF,CAAC,CAAC,CAACC,KAAK;UACvBE,MAAM,CAACF,KAAK,CAAC,GAAGE,MAAM,CAACF,KAAK,CAAC,IAAI,EAAE;UACnCE,MAAM,CAACF,KAAK,CAAC,CAACO,IAAI,CAACN,MAAM,CAACF,CAAC,CAAC,CAAC;QAC/B;MACF;MACAL,QAAQ,CAACO,MAAM,EAAEC,MAAM,CAAC;IAC1B;IAEA,IAAIT,OAAO,CAACxB,QAAQ,EAAE;MACpB,IAAIA,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;MAC9B,IAAIA,QAAQ,KAAKC,eAAe,EAAE;QAChCD,QAAQ,GAAGE,WAAW,CAAC,CAAC;MAC1B;MACAJ,SAAS,CAACE,QAAQ,EAAEwB,OAAO,CAACxB,QAAQ,CAAC;MACrCwB,OAAO,CAACxB,QAAQ,GAAGA,QAAQ;IAC7B,CAAC,MAAM;MACLwB,OAAO,CAACxB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;IACpC;IACA,IAAIuC,GAAG,GAAG,KAAK,CAAC;IAChB,IAAIC,KAAK,GAAG,KAAK,CAAC;IAClB,IAAIC,MAAM,GAAG,CAAC,CAAC;IACf,IAAId,IAAI,GAAGH,OAAO,CAACG,IAAI,IAAID,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtB,KAAK,CAAC;IAClDsB,IAAI,CAACe,OAAO,CAAC,UAAU9B,CAAC,EAAE;MACxB2B,GAAG,GAAGtB,KAAK,CAACZ,KAAK,CAACO,CAAC,CAAC;MACpB4B,KAAK,GAAGjB,MAAM,CAACX,CAAC,CAAC;MACjB2B,GAAG,CAACG,OAAO,CAAC,UAAUC,CAAC,EAAE;QACvB,IAAIC,IAAI,GAAGD,CAAC;QACZ,IAAI,OAAOC,IAAI,CAACC,SAAS,KAAK,UAAU,EAAE;UACxC,IAAItB,MAAM,KAAKP,OAAO,EAAE;YACtBO,MAAM,GAAG/B,QAAQ,CAAC,CAAC,CAAC,EAAE+B,MAAM,CAAC;UAC/B;UACAiB,KAAK,GAAGjB,MAAM,CAACX,CAAC,CAAC,GAAGgC,IAAI,CAACC,SAAS,CAACL,KAAK,CAAC;QAC3C;QACA,IAAI,OAAOI,IAAI,KAAK,UAAU,EAAE;UAC9BA,IAAI,GAAG;YACLE,SAAS,EAAEF;UACb,CAAC;QACH,CAAC,MAAM;UACLA,IAAI,GAAGpD,QAAQ,CAAC,CAAC,CAAC,EAAEoD,IAAI,CAAC;QAC3B;QACAA,IAAI,CAACE,SAAS,GAAG7B,KAAK,CAAC8B,mBAAmB,CAACH,IAAI,CAAC;QAChDA,IAAI,CAACb,KAAK,GAAGnB,CAAC;QACdgC,IAAI,CAACI,SAAS,GAAGJ,IAAI,CAACI,SAAS,IAAIpC,CAAC;QACpCgC,IAAI,CAACK,IAAI,GAAGhC,KAAK,CAACiC,OAAO,CAACN,IAAI,CAAC;QAC/B,IAAI,CAACA,IAAI,CAACE,SAAS,EAAE;UACnB;QACF;QACAL,MAAM,CAAC7B,CAAC,CAAC,GAAG6B,MAAM,CAAC7B,CAAC,CAAC,IAAI,EAAE;QAC3B6B,MAAM,CAAC7B,CAAC,CAAC,CAAC0B,IAAI,CAAC;UACbM,IAAI,EAAEA,IAAI;UACVJ,KAAK,EAAEA,KAAK;UACZjB,MAAM,EAAEA,MAAM;UACdQ,KAAK,EAAEnB;QACT,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAIuC,WAAW,GAAG,CAAC,CAAC;IACpBvD,QAAQ,CAAC6C,MAAM,EAAEjB,OAAO,EAAE,UAAU4B,IAAI,EAAEC,IAAI,EAAE;MAC9C,IAAIT,IAAI,GAAGQ,IAAI,CAACR,IAAI;MACpB,IAAIU,IAAI,GAAG,CAACV,IAAI,CAACK,IAAI,KAAK,QAAQ,IAAIL,IAAI,CAACK,IAAI,KAAK,OAAO,MAAMxD,OAAO,CAACmD,IAAI,CAACX,MAAM,CAAC,KAAK,QAAQ,IAAIxC,OAAO,CAACmD,IAAI,CAACW,YAAY,CAAC,KAAK,QAAQ,CAAC;MAC9ID,IAAI,GAAGA,IAAI,KAAKV,IAAI,CAACY,QAAQ,IAAI,CAACZ,IAAI,CAACY,QAAQ,IAAIJ,IAAI,CAACZ,KAAK,CAAC;MAC9DI,IAAI,CAACb,KAAK,GAAGqB,IAAI,CAACrB,KAAK;MACvB,SAAS0B,YAAYA,CAACC,GAAG,EAAEC,MAAM,EAAE;QACjC,OAAOnE,QAAQ,CAAC,CAAC,CAAC,EAAEmE,MAAM,EAAE;UAC1BX,SAAS,EAAEJ,IAAI,CAACI,SAAS,GAAG,GAAG,GAAGU;QACpC,CAAC,CAAC;MACJ;MAEA,SAASE,EAAEA,CAAA,EAAG;QACZ,IAAIzB,CAAC,GAAGhB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;QAE9E,IAAIa,MAAM,GAAGG,CAAC;QACd,IAAI,CAACzB,KAAK,CAACC,OAAO,CAACqB,MAAM,CAAC,EAAE;UAC1BA,MAAM,GAAG,CAACA,MAAM,CAAC;QACnB;QACA,IAAIA,MAAM,CAACZ,MAAM,EAAE;UACjBvB,OAAO,CAAC,kBAAkB,EAAEmC,MAAM,CAAC;QACrC;QACA,IAAIA,MAAM,CAACZ,MAAM,IAAIwB,IAAI,CAACiB,OAAO,EAAE;UACjC7B,MAAM,GAAG,EAAE,CAACI,MAAM,CAACQ,IAAI,CAACiB,OAAO,CAAC;QAClC;QAEA7B,MAAM,GAAGA,MAAM,CAAC8B,GAAG,CAACnE,eAAe,CAACiD,IAAI,CAAC,CAAC;QAE1C,IAAIpB,OAAO,CAACuC,KAAK,IAAI/B,MAAM,CAACZ,MAAM,EAAE;UAClC+B,WAAW,CAACP,IAAI,CAACb,KAAK,CAAC,GAAG,CAAC;UAC3B,OAAOsB,IAAI,CAACrB,MAAM,CAAC;QACrB;QACA,IAAI,CAACsB,IAAI,EAAE;UACTD,IAAI,CAACrB,MAAM,CAAC;QACd,CAAC,MAAM;UACL;UACA;UACA;UACA,IAAIY,IAAI,CAACY,QAAQ,IAAI,CAACJ,IAAI,CAACZ,KAAK,EAAE;YAChC,IAAII,IAAI,CAACiB,OAAO,EAAE;cAChB7B,MAAM,GAAG,EAAE,CAACI,MAAM,CAACQ,IAAI,CAACiB,OAAO,CAAC,CAACC,GAAG,CAACnE,eAAe,CAACiD,IAAI,CAAC,CAAC;YAC7D,CAAC,MAAM,IAAIpB,OAAO,CAACwC,KAAK,EAAE;cACxBhC,MAAM,GAAG,CAACR,OAAO,CAACwC,KAAK,CAACpB,IAAI,EAAElD,MAAM,CAAC8B,OAAO,CAACxB,QAAQ,CAACwD,QAAQ,EAAEZ,IAAI,CAACb,KAAK,CAAC,CAAC,CAAC;YAC/E,CAAC,MAAM;cACLC,MAAM,GAAG,EAAE;YACb;YACA,OAAOqB,IAAI,CAACrB,MAAM,CAAC;UACrB;UAEA,IAAIiC,YAAY,GAAG,CAAC,CAAC;UACrB,IAAIrB,IAAI,CAACW,YAAY,EAAE;YACrB,KAAK,IAAIW,CAAC,IAAId,IAAI,CAACZ,KAAK,EAAE;cACxB,IAAIY,IAAI,CAACZ,KAAK,CAAC1B,cAAc,CAACoD,CAAC,CAAC,EAAE;gBAChCD,YAAY,CAACC,CAAC,CAAC,GAAGtB,IAAI,CAACW,YAAY;cACrC;YACF;UACF;UACAU,YAAY,GAAGzE,QAAQ,CAAC,CAAC,CAAC,EAAEyE,YAAY,EAAEb,IAAI,CAACR,IAAI,CAACX,MAAM,CAAC;UAC3D,KAAK,IAAIkC,CAAC,IAAIF,YAAY,EAAE;YAC1B,IAAIA,YAAY,CAACnD,cAAc,CAACqD,CAAC,CAAC,EAAE;cAClC,IAAIC,WAAW,GAAG1D,KAAK,CAACC,OAAO,CAACsD,YAAY,CAACE,CAAC,CAAC,CAAC,GAAGF,YAAY,CAACE,CAAC,CAAC,GAAG,CAACF,YAAY,CAACE,CAAC,CAAC,CAAC;cACtFF,YAAY,CAACE,CAAC,CAAC,GAAGC,WAAW,CAACN,GAAG,CAACL,YAAY,CAACY,IAAI,CAAC,IAAI,EAAEF,CAAC,CAAC,CAAC;YAC/D;UACF;UACA,IAAIR,MAAM,GAAG,IAAIxD,MAAM,CAAC8D,YAAY,CAAC;UACrCN,MAAM,CAAC3D,QAAQ,CAACwB,OAAO,CAACxB,QAAQ,CAAC;UACjC,IAAIoD,IAAI,CAACR,IAAI,CAACpB,OAAO,EAAE;YACrB4B,IAAI,CAACR,IAAI,CAACpB,OAAO,CAACxB,QAAQ,GAAGwB,OAAO,CAACxB,QAAQ;YAC7CoD,IAAI,CAACR,IAAI,CAACpB,OAAO,CAACwC,KAAK,GAAGxC,OAAO,CAACwC,KAAK;UACzC;UACAL,MAAM,CAAC5C,QAAQ,CAACqC,IAAI,CAACZ,KAAK,EAAEY,IAAI,CAACR,IAAI,CAACpB,OAAO,IAAIA,OAAO,EAAE,UAAU8C,IAAI,EAAE;YACxEjB,IAAI,CAACiB,IAAI,IAAIA,IAAI,CAAClD,MAAM,GAAGY,MAAM,CAACI,MAAM,CAACkC,IAAI,CAAC,GAAGA,IAAI,CAAC;UACxD,CAAC,CAAC;QACJ;MACF;MAEA,IAAIC,GAAG,GAAG3B,IAAI,CAACE,SAAS,CAACF,IAAI,EAAEQ,IAAI,CAACZ,KAAK,EAAEoB,EAAE,EAAER,IAAI,CAAC7B,MAAM,EAAEC,OAAO,CAAC;MACpE,IAAI+C,GAAG,IAAIA,GAAG,CAACC,IAAI,EAAE;QACnBD,GAAG,CAACC,IAAI,CAAC,YAAY;UACnB,OAAOZ,EAAE,CAAC,CAAC;QACb,CAAC,EAAE,UAAUzB,CAAC,EAAE;UACd,OAAOyB,EAAE,CAACzB,CAAC,CAAC;QACd,CAAC,CAAC;MACJ;IACF,CAAC,EAAE,UAAUN,OAAO,EAAE;MACpBD,QAAQ,CAACC,OAAO,CAAC;IACnB,CAAC,CAAC;EACJ,CAAC;EACDqB,OAAO,EAAE,SAASA,OAAOA,CAACN,IAAI,EAAE;IAC9B,IAAIA,IAAI,CAACK,IAAI,KAAK5B,SAAS,IAAIuB,IAAI,CAAC6B,OAAO,YAAYC,MAAM,EAAE;MAC7D9B,IAAI,CAACK,IAAI,GAAG,SAAS;IACvB;IACA,IAAI,OAAOL,IAAI,CAACE,SAAS,KAAK,UAAU,IAAIF,IAAI,CAACK,IAAI,IAAI,CAAClD,UAAU,CAACe,cAAc,CAAC8B,IAAI,CAACK,IAAI,CAAC,EAAE;MAC9F,MAAM,IAAIxC,KAAK,CAACf,MAAM,CAAC,sBAAsB,EAAEkD,IAAI,CAACK,IAAI,CAAC,CAAC;IAC5D;IACA,OAAOL,IAAI,CAACK,IAAI,IAAI,QAAQ;EAC9B,CAAC;EACDF,mBAAmB,EAAE,SAASA,mBAAmBA,CAACH,IAAI,EAAE;IACtD,IAAI,OAAOA,IAAI,CAACE,SAAS,KAAK,UAAU,EAAE;MACxC,OAAOF,IAAI,CAACE,SAAS;IACvB;IACA,IAAInB,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACiB,IAAI,CAAC;IAC5B,IAAI+B,YAAY,GAAGhD,IAAI,CAACiD,OAAO,CAAC,SAAS,CAAC;IAC1C,IAAID,YAAY,KAAK,CAAC,CAAC,EAAE;MACvBhD,IAAI,CAACkD,MAAM,CAACF,YAAY,EAAE,CAAC,CAAC;IAC9B;IACA,IAAIhD,IAAI,CAACP,MAAM,KAAK,CAAC,IAAIO,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;MAC/C,OAAO5B,UAAU,CAACyD,QAAQ;IAC5B;IACA,OAAOzD,UAAU,CAAC,IAAI,CAACmD,OAAO,CAACN,IAAI,CAAC,CAAC,IAAI,KAAK;EAChD;AACF,CAAC;AAEDzC,MAAM,CAAC2E,QAAQ,GAAG,SAASA,QAAQA,CAAC7B,IAAI,EAAEH,SAAS,EAAE;EACnD,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;IACnC,MAAM,IAAIrC,KAAK,CAAC,kEAAkE,CAAC;EACrF;EACAV,UAAU,CAACkD,IAAI,CAAC,GAAGH,SAAS;AAC9B,CAAC;AAED3C,MAAM,CAACH,QAAQ,GAAGC,eAAe;AAEjC,eAAeE,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}