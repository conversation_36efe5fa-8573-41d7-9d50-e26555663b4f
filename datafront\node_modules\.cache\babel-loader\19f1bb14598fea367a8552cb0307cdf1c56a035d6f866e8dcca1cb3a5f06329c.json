{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport { deprecateLog, deprecateReplaceLog } from '../../util/log.js';\nvar each = zrUtil.each;\nvar isObject = zrUtil.isObject;\nvar POSSIBLE_STYLES = ['areaStyle', 'lineStyle', 'nodeStyle', 'linkStyle', 'chordStyle', 'label', 'labelLine'];\nfunction compatEC2ItemStyle(opt) {\n  var itemStyleOpt = opt && opt.itemStyle;\n  if (!itemStyleOpt) {\n    return;\n  }\n  for (var i = 0, len = POSSIBLE_STYLES.length; i < len; i++) {\n    var styleName = POSSIBLE_STYLES[i];\n    var normalItemStyleOpt = itemStyleOpt.normal;\n    var emphasisItemStyleOpt = itemStyleOpt.emphasis;\n    if (normalItemStyleOpt && normalItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.normal.\" + styleName, styleName);\n      }\n      opt[styleName] = opt[styleName] || {};\n      if (!opt[styleName].normal) {\n        opt[styleName].normal = normalItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].normal, normalItemStyleOpt[styleName]);\n      }\n      normalItemStyleOpt[styleName] = null;\n    }\n    if (emphasisItemStyleOpt && emphasisItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.emphasis.\" + styleName, \"emphasis.\" + styleName);\n      }\n      opt[styleName] = opt[styleName] || {};\n      if (!opt[styleName].emphasis) {\n        opt[styleName].emphasis = emphasisItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].emphasis, emphasisItemStyleOpt[styleName]);\n      }\n      emphasisItemStyleOpt[styleName] = null;\n    }\n  }\n}\nfunction convertNormalEmphasis(opt, optType, useExtend) {\n  if (opt && opt[optType] && (opt[optType].normal || opt[optType].emphasis)) {\n    var normalOpt = opt[optType].normal;\n    var emphasisOpt = opt[optType].emphasis;\n    if (normalOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        // eslint-disable-next-line max-len\n        deprecateLog(\"'normal' hierarchy in \" + optType + \" has been removed since 4.0. All style properties are configured in \" + optType + \" directly now.\");\n      }\n      // Timeline controlStyle has other properties besides normal and emphasis\n      if (useExtend) {\n        opt[optType].normal = opt[optType].emphasis = null;\n        zrUtil.defaults(opt[optType], normalOpt);\n      } else {\n        opt[optType] = normalOpt;\n      }\n    }\n    if (emphasisOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog(optType + \".emphasis has been changed to emphasis.\" + optType + \" since 4.0\");\n      }\n      opt.emphasis = opt.emphasis || {};\n      opt.emphasis[optType] = emphasisOpt;\n      // Also compat the case user mix the style and focus together in ec3 style\n      // for example: { itemStyle: { normal: {}, emphasis: {focus, shadowBlur} } }\n      if (emphasisOpt.focus) {\n        opt.emphasis.focus = emphasisOpt.focus;\n      }\n      if (emphasisOpt.blurScope) {\n        opt.emphasis.blurScope = emphasisOpt.blurScope;\n      }\n    }\n  }\n}\nfunction removeEC3NormalStatus(opt) {\n  convertNormalEmphasis(opt, 'itemStyle');\n  convertNormalEmphasis(opt, 'lineStyle');\n  convertNormalEmphasis(opt, 'areaStyle');\n  convertNormalEmphasis(opt, 'label');\n  convertNormalEmphasis(opt, 'labelLine');\n  // treemap\n  convertNormalEmphasis(opt, 'upperLabel');\n  // graph\n  convertNormalEmphasis(opt, 'edgeLabel');\n}\nfunction compatTextStyle(opt, propName) {\n  // Check whether is not object (string\\null\\undefined ...)\n  var labelOptSingle = isObject(opt) && opt[propName];\n  var textStyle = isObject(labelOptSingle) && labelOptSingle.textStyle;\n  if (textStyle) {\n    if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line max-len\n      deprecateLog(\"textStyle hierarchy in \" + propName + \" has been removed since 4.0. All textStyle properties are configured in \" + propName + \" directly now.\");\n    }\n    for (var i = 0, len = modelUtil.TEXT_STYLE_OPTIONS.length; i < len; i++) {\n      var textPropName = modelUtil.TEXT_STYLE_OPTIONS[i];\n      if (textStyle.hasOwnProperty(textPropName)) {\n        labelOptSingle[textPropName] = textStyle[textPropName];\n      }\n    }\n  }\n}\nfunction compatEC3CommonStyles(opt) {\n  if (opt) {\n    removeEC3NormalStatus(opt);\n    compatTextStyle(opt, 'label');\n    opt.emphasis && compatTextStyle(opt.emphasis, 'label');\n  }\n}\nfunction processSeries(seriesOpt) {\n  if (!isObject(seriesOpt)) {\n    return;\n  }\n  compatEC2ItemStyle(seriesOpt);\n  removeEC3NormalStatus(seriesOpt);\n  compatTextStyle(seriesOpt, 'label');\n  // treemap\n  compatTextStyle(seriesOpt, 'upperLabel');\n  // graph\n  compatTextStyle(seriesOpt, 'edgeLabel');\n  if (seriesOpt.emphasis) {\n    compatTextStyle(seriesOpt.emphasis, 'label');\n    // treemap\n    compatTextStyle(seriesOpt.emphasis, 'upperLabel');\n    // graph\n    compatTextStyle(seriesOpt.emphasis, 'edgeLabel');\n  }\n  var markPoint = seriesOpt.markPoint;\n  if (markPoint) {\n    compatEC2ItemStyle(markPoint);\n    compatEC3CommonStyles(markPoint);\n  }\n  var markLine = seriesOpt.markLine;\n  if (markLine) {\n    compatEC2ItemStyle(markLine);\n    compatEC3CommonStyles(markLine);\n  }\n  var markArea = seriesOpt.markArea;\n  if (markArea) {\n    compatEC3CommonStyles(markArea);\n  }\n  var data = seriesOpt.data;\n  // Break with ec3: if `setOption` again, there may be no `type` in option,\n  // then the backward compat based on option type will not be performed.\n  if (seriesOpt.type === 'graph') {\n    data = data || seriesOpt.nodes;\n    var edgeData = seriesOpt.links || seriesOpt.edges;\n    if (edgeData && !zrUtil.isTypedArray(edgeData)) {\n      for (var i = 0; i < edgeData.length; i++) {\n        compatEC3CommonStyles(edgeData[i]);\n      }\n    }\n    zrUtil.each(seriesOpt.categories, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  }\n  if (data && !zrUtil.isTypedArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      compatEC3CommonStyles(data[i]);\n    }\n  }\n  // mark point data\n  markPoint = seriesOpt.markPoint;\n  if (markPoint && markPoint.data) {\n    var mpData = markPoint.data;\n    for (var i = 0; i < mpData.length; i++) {\n      compatEC3CommonStyles(mpData[i]);\n    }\n  }\n  // mark line data\n  markLine = seriesOpt.markLine;\n  if (markLine && markLine.data) {\n    var mlData = markLine.data;\n    for (var i = 0; i < mlData.length; i++) {\n      if (zrUtil.isArray(mlData[i])) {\n        compatEC3CommonStyles(mlData[i][0]);\n        compatEC3CommonStyles(mlData[i][1]);\n      } else {\n        compatEC3CommonStyles(mlData[i]);\n      }\n    }\n  }\n  // Series\n  if (seriesOpt.type === 'gauge') {\n    compatTextStyle(seriesOpt, 'axisLabel');\n    compatTextStyle(seriesOpt, 'title');\n    compatTextStyle(seriesOpt, 'detail');\n  } else if (seriesOpt.type === 'treemap') {\n    convertNormalEmphasis(seriesOpt.breadcrumb, 'itemStyle');\n    zrUtil.each(seriesOpt.levels, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  } else if (seriesOpt.type === 'tree') {\n    removeEC3NormalStatus(seriesOpt.leaves);\n  }\n  // sunburst starts from ec4, so it does not need to compat levels.\n}\nfunction toArr(o) {\n  return zrUtil.isArray(o) ? o : o ? [o] : [];\n}\nfunction toObj(o) {\n  return (zrUtil.isArray(o) ? o[0] : o) || {};\n}\nexport default function globalCompatStyle(option, isTheme) {\n  each(toArr(option.series), function (seriesOpt) {\n    isObject(seriesOpt) && processSeries(seriesOpt);\n  });\n  var axes = ['xAxis', 'yAxis', 'radiusAxis', 'angleAxis', 'singleAxis', 'parallelAxis', 'radar'];\n  isTheme && axes.push('valueAxis', 'categoryAxis', 'logAxis', 'timeAxis');\n  each(axes, function (axisName) {\n    each(toArr(option[axisName]), function (axisOpt) {\n      if (axisOpt) {\n        compatTextStyle(axisOpt, 'axisLabel');\n        compatTextStyle(axisOpt.axisPointer, 'label');\n      }\n    });\n  });\n  each(toArr(option.parallel), function (parallelOpt) {\n    var parallelAxisDefault = parallelOpt && parallelOpt.parallelAxisDefault;\n    compatTextStyle(parallelAxisDefault, 'axisLabel');\n    compatTextStyle(parallelAxisDefault && parallelAxisDefault.axisPointer, 'label');\n  });\n  each(toArr(option.calendar), function (calendarOpt) {\n    convertNormalEmphasis(calendarOpt, 'itemStyle');\n    compatTextStyle(calendarOpt, 'dayLabel');\n    compatTextStyle(calendarOpt, 'monthLabel');\n    compatTextStyle(calendarOpt, 'yearLabel');\n  });\n  // radar.name.textStyle\n  each(toArr(option.radar), function (radarOpt) {\n    compatTextStyle(radarOpt, 'name');\n    // Use axisName instead of name because component has name property\n    if (radarOpt.name && radarOpt.axisName == null) {\n      radarOpt.axisName = radarOpt.name;\n      delete radarOpt.name;\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('name property in radar component has been changed to axisName');\n      }\n    }\n    if (radarOpt.nameGap != null && radarOpt.axisNameGap == null) {\n      radarOpt.axisNameGap = radarOpt.nameGap;\n      delete radarOpt.nameGap;\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('nameGap property in radar component has been changed to axisNameGap');\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      each(radarOpt.indicator, function (indicatorOpt) {\n        if (indicatorOpt.text) {\n          deprecateReplaceLog('text', 'name', 'radar.indicator');\n        }\n      });\n    }\n  });\n  each(toArr(option.geo), function (geoOpt) {\n    if (isObject(geoOpt)) {\n      compatEC3CommonStyles(geoOpt);\n      each(toArr(geoOpt.regions), function (regionObj) {\n        compatEC3CommonStyles(regionObj);\n      });\n    }\n  });\n  each(toArr(option.timeline), function (timelineOpt) {\n    compatEC3CommonStyles(timelineOpt);\n    convertNormalEmphasis(timelineOpt, 'label');\n    convertNormalEmphasis(timelineOpt, 'itemStyle');\n    convertNormalEmphasis(timelineOpt, 'controlStyle', true);\n    var data = timelineOpt.data;\n    zrUtil.isArray(data) && zrUtil.each(data, function (item) {\n      if (zrUtil.isObject(item)) {\n        convertNormalEmphasis(item, 'label');\n        convertNormalEmphasis(item, 'itemStyle');\n      }\n    });\n  });\n  each(toArr(option.toolbox), function (toolboxOpt) {\n    convertNormalEmphasis(toolboxOpt, 'iconStyle');\n    each(toolboxOpt.feature, function (featureOpt) {\n      convertNormalEmphasis(featureOpt, 'iconStyle');\n    });\n  });\n  compatTextStyle(toObj(option.axisPointer), 'label');\n  compatTextStyle(toObj(option.tooltip).axisPointer, 'label');\n  // Clean logs\n  // storedLogs = {};\n}", "map": {"version": 3, "names": ["zrUtil", "modelUtil", "deprecateLog", "deprecateReplaceLog", "each", "isObject", "POSSIBLE_STYLES", "compatEC2ItemStyle", "opt", "itemStyleOpt", "itemStyle", "i", "len", "length", "styleName", "normalItemStyleOpt", "normal", "emphasisItemStyleOpt", "emphasis", "process", "env", "NODE_ENV", "merge", "convertNormalEmphasis", "optType", "useExtend", "normalOpt", "emphasisOpt", "defaults", "focus", "blurScope", "removeEC3NormalStatus", "compatTextStyle", "propName", "labelOptSingle", "textStyle", "TEXT_STYLE_OPTIONS", "textPropName", "hasOwnProperty", "compatEC3CommonStyles", "processSeries", "seriesOpt", "markPoint", "markLine", "<PERSON><PERSON><PERSON>", "data", "type", "nodes", "edgeData", "links", "edges", "isTypedArray", "categories", "mpData", "mlData", "isArray", "breadcrumb", "levels", "leaves", "toArr", "o", "to<PERSON><PERSON><PERSON>", "globalCompatStyle", "option", "isTheme", "series", "axes", "push", "axisName", "axisOpt", "axisPointer", "parallel", "parallelOpt", "parallelAxisDefault", "calendar", "calendarOpt", "radar", "radarOpt", "name", "nameGap", "axisNameGap", "indicator", "indicatorOpt", "text", "geo", "geoOpt", "regions", "regionObj", "timeline", "timelineOpt", "item", "toolbox", "toolboxOpt", "feature", "featureOpt", "tooltip"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/preprocessor/helper/compatStyle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\nimport { deprecateLog, deprecateReplaceLog } from '../../util/log.js';\nvar each = zrUtil.each;\nvar isObject = zrUtil.isObject;\nvar POSSIBLE_STYLES = ['areaStyle', 'lineStyle', 'nodeStyle', 'linkStyle', 'chordStyle', 'label', 'labelLine'];\nfunction compatEC2ItemStyle(opt) {\n  var itemStyleOpt = opt && opt.itemStyle;\n  if (!itemStyleOpt) {\n    return;\n  }\n  for (var i = 0, len = POSSIBLE_STYLES.length; i < len; i++) {\n    var styleName = POSSIBLE_STYLES[i];\n    var normalItemStyleOpt = itemStyleOpt.normal;\n    var emphasisItemStyleOpt = itemStyleOpt.emphasis;\n    if (normalItemStyleOpt && normalItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.normal.\" + styleName, styleName);\n      }\n      opt[styleName] = opt[styleName] || {};\n      if (!opt[styleName].normal) {\n        opt[styleName].normal = normalItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].normal, normalItemStyleOpt[styleName]);\n      }\n      normalItemStyleOpt[styleName] = null;\n    }\n    if (emphasisItemStyleOpt && emphasisItemStyleOpt[styleName]) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateReplaceLog(\"itemStyle.emphasis.\" + styleName, \"emphasis.\" + styleName);\n      }\n      opt[styleName] = opt[styleName] || {};\n      if (!opt[styleName].emphasis) {\n        opt[styleName].emphasis = emphasisItemStyleOpt[styleName];\n      } else {\n        zrUtil.merge(opt[styleName].emphasis, emphasisItemStyleOpt[styleName]);\n      }\n      emphasisItemStyleOpt[styleName] = null;\n    }\n  }\n}\nfunction convertNormalEmphasis(opt, optType, useExtend) {\n  if (opt && opt[optType] && (opt[optType].normal || opt[optType].emphasis)) {\n    var normalOpt = opt[optType].normal;\n    var emphasisOpt = opt[optType].emphasis;\n    if (normalOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        // eslint-disable-next-line max-len\n        deprecateLog(\"'normal' hierarchy in \" + optType + \" has been removed since 4.0. All style properties are configured in \" + optType + \" directly now.\");\n      }\n      // Timeline controlStyle has other properties besides normal and emphasis\n      if (useExtend) {\n        opt[optType].normal = opt[optType].emphasis = null;\n        zrUtil.defaults(opt[optType], normalOpt);\n      } else {\n        opt[optType] = normalOpt;\n      }\n    }\n    if (emphasisOpt) {\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog(optType + \".emphasis has been changed to emphasis.\" + optType + \" since 4.0\");\n      }\n      opt.emphasis = opt.emphasis || {};\n      opt.emphasis[optType] = emphasisOpt;\n      // Also compat the case user mix the style and focus together in ec3 style\n      // for example: { itemStyle: { normal: {}, emphasis: {focus, shadowBlur} } }\n      if (emphasisOpt.focus) {\n        opt.emphasis.focus = emphasisOpt.focus;\n      }\n      if (emphasisOpt.blurScope) {\n        opt.emphasis.blurScope = emphasisOpt.blurScope;\n      }\n    }\n  }\n}\nfunction removeEC3NormalStatus(opt) {\n  convertNormalEmphasis(opt, 'itemStyle');\n  convertNormalEmphasis(opt, 'lineStyle');\n  convertNormalEmphasis(opt, 'areaStyle');\n  convertNormalEmphasis(opt, 'label');\n  convertNormalEmphasis(opt, 'labelLine');\n  // treemap\n  convertNormalEmphasis(opt, 'upperLabel');\n  // graph\n  convertNormalEmphasis(opt, 'edgeLabel');\n}\nfunction compatTextStyle(opt, propName) {\n  // Check whether is not object (string\\null\\undefined ...)\n  var labelOptSingle = isObject(opt) && opt[propName];\n  var textStyle = isObject(labelOptSingle) && labelOptSingle.textStyle;\n  if (textStyle) {\n    if (process.env.NODE_ENV !== 'production') {\n      // eslint-disable-next-line max-len\n      deprecateLog(\"textStyle hierarchy in \" + propName + \" has been removed since 4.0. All textStyle properties are configured in \" + propName + \" directly now.\");\n    }\n    for (var i = 0, len = modelUtil.TEXT_STYLE_OPTIONS.length; i < len; i++) {\n      var textPropName = modelUtil.TEXT_STYLE_OPTIONS[i];\n      if (textStyle.hasOwnProperty(textPropName)) {\n        labelOptSingle[textPropName] = textStyle[textPropName];\n      }\n    }\n  }\n}\nfunction compatEC3CommonStyles(opt) {\n  if (opt) {\n    removeEC3NormalStatus(opt);\n    compatTextStyle(opt, 'label');\n    opt.emphasis && compatTextStyle(opt.emphasis, 'label');\n  }\n}\nfunction processSeries(seriesOpt) {\n  if (!isObject(seriesOpt)) {\n    return;\n  }\n  compatEC2ItemStyle(seriesOpt);\n  removeEC3NormalStatus(seriesOpt);\n  compatTextStyle(seriesOpt, 'label');\n  // treemap\n  compatTextStyle(seriesOpt, 'upperLabel');\n  // graph\n  compatTextStyle(seriesOpt, 'edgeLabel');\n  if (seriesOpt.emphasis) {\n    compatTextStyle(seriesOpt.emphasis, 'label');\n    // treemap\n    compatTextStyle(seriesOpt.emphasis, 'upperLabel');\n    // graph\n    compatTextStyle(seriesOpt.emphasis, 'edgeLabel');\n  }\n  var markPoint = seriesOpt.markPoint;\n  if (markPoint) {\n    compatEC2ItemStyle(markPoint);\n    compatEC3CommonStyles(markPoint);\n  }\n  var markLine = seriesOpt.markLine;\n  if (markLine) {\n    compatEC2ItemStyle(markLine);\n    compatEC3CommonStyles(markLine);\n  }\n  var markArea = seriesOpt.markArea;\n  if (markArea) {\n    compatEC3CommonStyles(markArea);\n  }\n  var data = seriesOpt.data;\n  // Break with ec3: if `setOption` again, there may be no `type` in option,\n  // then the backward compat based on option type will not be performed.\n  if (seriesOpt.type === 'graph') {\n    data = data || seriesOpt.nodes;\n    var edgeData = seriesOpt.links || seriesOpt.edges;\n    if (edgeData && !zrUtil.isTypedArray(edgeData)) {\n      for (var i = 0; i < edgeData.length; i++) {\n        compatEC3CommonStyles(edgeData[i]);\n      }\n    }\n    zrUtil.each(seriesOpt.categories, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  }\n  if (data && !zrUtil.isTypedArray(data)) {\n    for (var i = 0; i < data.length; i++) {\n      compatEC3CommonStyles(data[i]);\n    }\n  }\n  // mark point data\n  markPoint = seriesOpt.markPoint;\n  if (markPoint && markPoint.data) {\n    var mpData = markPoint.data;\n    for (var i = 0; i < mpData.length; i++) {\n      compatEC3CommonStyles(mpData[i]);\n    }\n  }\n  // mark line data\n  markLine = seriesOpt.markLine;\n  if (markLine && markLine.data) {\n    var mlData = markLine.data;\n    for (var i = 0; i < mlData.length; i++) {\n      if (zrUtil.isArray(mlData[i])) {\n        compatEC3CommonStyles(mlData[i][0]);\n        compatEC3CommonStyles(mlData[i][1]);\n      } else {\n        compatEC3CommonStyles(mlData[i]);\n      }\n    }\n  }\n  // Series\n  if (seriesOpt.type === 'gauge') {\n    compatTextStyle(seriesOpt, 'axisLabel');\n    compatTextStyle(seriesOpt, 'title');\n    compatTextStyle(seriesOpt, 'detail');\n  } else if (seriesOpt.type === 'treemap') {\n    convertNormalEmphasis(seriesOpt.breadcrumb, 'itemStyle');\n    zrUtil.each(seriesOpt.levels, function (opt) {\n      removeEC3NormalStatus(opt);\n    });\n  } else if (seriesOpt.type === 'tree') {\n    removeEC3NormalStatus(seriesOpt.leaves);\n  }\n  // sunburst starts from ec4, so it does not need to compat levels.\n}\nfunction toArr(o) {\n  return zrUtil.isArray(o) ? o : o ? [o] : [];\n}\nfunction toObj(o) {\n  return (zrUtil.isArray(o) ? o[0] : o) || {};\n}\nexport default function globalCompatStyle(option, isTheme) {\n  each(toArr(option.series), function (seriesOpt) {\n    isObject(seriesOpt) && processSeries(seriesOpt);\n  });\n  var axes = ['xAxis', 'yAxis', 'radiusAxis', 'angleAxis', 'singleAxis', 'parallelAxis', 'radar'];\n  isTheme && axes.push('valueAxis', 'categoryAxis', 'logAxis', 'timeAxis');\n  each(axes, function (axisName) {\n    each(toArr(option[axisName]), function (axisOpt) {\n      if (axisOpt) {\n        compatTextStyle(axisOpt, 'axisLabel');\n        compatTextStyle(axisOpt.axisPointer, 'label');\n      }\n    });\n  });\n  each(toArr(option.parallel), function (parallelOpt) {\n    var parallelAxisDefault = parallelOpt && parallelOpt.parallelAxisDefault;\n    compatTextStyle(parallelAxisDefault, 'axisLabel');\n    compatTextStyle(parallelAxisDefault && parallelAxisDefault.axisPointer, 'label');\n  });\n  each(toArr(option.calendar), function (calendarOpt) {\n    convertNormalEmphasis(calendarOpt, 'itemStyle');\n    compatTextStyle(calendarOpt, 'dayLabel');\n    compatTextStyle(calendarOpt, 'monthLabel');\n    compatTextStyle(calendarOpt, 'yearLabel');\n  });\n  // radar.name.textStyle\n  each(toArr(option.radar), function (radarOpt) {\n    compatTextStyle(radarOpt, 'name');\n    // Use axisName instead of name because component has name property\n    if (radarOpt.name && radarOpt.axisName == null) {\n      radarOpt.axisName = radarOpt.name;\n      delete radarOpt.name;\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('name property in radar component has been changed to axisName');\n      }\n    }\n    if (radarOpt.nameGap != null && radarOpt.axisNameGap == null) {\n      radarOpt.axisNameGap = radarOpt.nameGap;\n      delete radarOpt.nameGap;\n      if (process.env.NODE_ENV !== 'production') {\n        deprecateLog('nameGap property in radar component has been changed to axisNameGap');\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      each(radarOpt.indicator, function (indicatorOpt) {\n        if (indicatorOpt.text) {\n          deprecateReplaceLog('text', 'name', 'radar.indicator');\n        }\n      });\n    }\n  });\n  each(toArr(option.geo), function (geoOpt) {\n    if (isObject(geoOpt)) {\n      compatEC3CommonStyles(geoOpt);\n      each(toArr(geoOpt.regions), function (regionObj) {\n        compatEC3CommonStyles(regionObj);\n      });\n    }\n  });\n  each(toArr(option.timeline), function (timelineOpt) {\n    compatEC3CommonStyles(timelineOpt);\n    convertNormalEmphasis(timelineOpt, 'label');\n    convertNormalEmphasis(timelineOpt, 'itemStyle');\n    convertNormalEmphasis(timelineOpt, 'controlStyle', true);\n    var data = timelineOpt.data;\n    zrUtil.isArray(data) && zrUtil.each(data, function (item) {\n      if (zrUtil.isObject(item)) {\n        convertNormalEmphasis(item, 'label');\n        convertNormalEmphasis(item, 'itemStyle');\n      }\n    });\n  });\n  each(toArr(option.toolbox), function (toolboxOpt) {\n    convertNormalEmphasis(toolboxOpt, 'iconStyle');\n    each(toolboxOpt.feature, function (featureOpt) {\n      convertNormalEmphasis(featureOpt, 'iconStyle');\n    });\n  });\n  compatTextStyle(toObj(option.axisPointer), 'label');\n  compatTextStyle(toObj(option.tooltip).axisPointer, 'label');\n  // Clean logs\n  // storedLogs = {};\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,SAAS,MAAM,qBAAqB;AAChD,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,mBAAmB;AACrE,IAAIC,IAAI,GAAGJ,MAAM,CAACI,IAAI;AACtB,IAAIC,QAAQ,GAAGL,MAAM,CAACK,QAAQ;AAC9B,IAAIC,eAAe,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,WAAW,CAAC;AAC9G,SAASC,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,IAAIC,YAAY,GAAGD,GAAG,IAAIA,GAAG,CAACE,SAAS;EACvC,IAAI,CAACD,YAAY,EAAE;IACjB;EACF;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGN,eAAe,CAACO,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC1D,IAAIG,SAAS,GAAGR,eAAe,CAACK,CAAC,CAAC;IAClC,IAAII,kBAAkB,GAAGN,YAAY,CAACO,MAAM;IAC5C,IAAIC,oBAAoB,GAAGR,YAAY,CAACS,QAAQ;IAChD,IAAIH,kBAAkB,IAAIA,kBAAkB,CAACD,SAAS,CAAC,EAAE;MACvD,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzClB,mBAAmB,CAAC,mBAAmB,GAAGW,SAAS,EAAEA,SAAS,CAAC;MACjE;MACAN,GAAG,CAACM,SAAS,CAAC,GAAGN,GAAG,CAACM,SAAS,CAAC,IAAI,CAAC,CAAC;MACrC,IAAI,CAACN,GAAG,CAACM,SAAS,CAAC,CAACE,MAAM,EAAE;QAC1BR,GAAG,CAACM,SAAS,CAAC,CAACE,MAAM,GAAGD,kBAAkB,CAACD,SAAS,CAAC;MACvD,CAAC,MAAM;QACLd,MAAM,CAACsB,KAAK,CAACd,GAAG,CAACM,SAAS,CAAC,CAACE,MAAM,EAAED,kBAAkB,CAACD,SAAS,CAAC,CAAC;MACpE;MACAC,kBAAkB,CAACD,SAAS,CAAC,GAAG,IAAI;IACtC;IACA,IAAIG,oBAAoB,IAAIA,oBAAoB,CAACH,SAAS,CAAC,EAAE;MAC3D,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzClB,mBAAmB,CAAC,qBAAqB,GAAGW,SAAS,EAAE,WAAW,GAAGA,SAAS,CAAC;MACjF;MACAN,GAAG,CAACM,SAAS,CAAC,GAAGN,GAAG,CAACM,SAAS,CAAC,IAAI,CAAC,CAAC;MACrC,IAAI,CAACN,GAAG,CAACM,SAAS,CAAC,CAACI,QAAQ,EAAE;QAC5BV,GAAG,CAACM,SAAS,CAAC,CAACI,QAAQ,GAAGD,oBAAoB,CAACH,SAAS,CAAC;MAC3D,CAAC,MAAM;QACLd,MAAM,CAACsB,KAAK,CAACd,GAAG,CAACM,SAAS,CAAC,CAACI,QAAQ,EAAED,oBAAoB,CAACH,SAAS,CAAC,CAAC;MACxE;MACAG,oBAAoB,CAACH,SAAS,CAAC,GAAG,IAAI;IACxC;EACF;AACF;AACA,SAASS,qBAAqBA,CAACf,GAAG,EAAEgB,OAAO,EAAEC,SAAS,EAAE;EACtD,IAAIjB,GAAG,IAAIA,GAAG,CAACgB,OAAO,CAAC,KAAKhB,GAAG,CAACgB,OAAO,CAAC,CAACR,MAAM,IAAIR,GAAG,CAACgB,OAAO,CAAC,CAACN,QAAQ,CAAC,EAAE;IACzE,IAAIQ,SAAS,GAAGlB,GAAG,CAACgB,OAAO,CAAC,CAACR,MAAM;IACnC,IAAIW,WAAW,GAAGnB,GAAG,CAACgB,OAAO,CAAC,CAACN,QAAQ;IACvC,IAAIQ,SAAS,EAAE;MACb,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC;QACAnB,YAAY,CAAC,wBAAwB,GAAGsB,OAAO,GAAG,sEAAsE,GAAGA,OAAO,GAAG,gBAAgB,CAAC;MACxJ;MACA;MACA,IAAIC,SAAS,EAAE;QACbjB,GAAG,CAACgB,OAAO,CAAC,CAACR,MAAM,GAAGR,GAAG,CAACgB,OAAO,CAAC,CAACN,QAAQ,GAAG,IAAI;QAClDlB,MAAM,CAAC4B,QAAQ,CAACpB,GAAG,CAACgB,OAAO,CAAC,EAAEE,SAAS,CAAC;MAC1C,CAAC,MAAM;QACLlB,GAAG,CAACgB,OAAO,CAAC,GAAGE,SAAS;MAC1B;IACF;IACA,IAAIC,WAAW,EAAE;MACf,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCnB,YAAY,CAACsB,OAAO,GAAG,yCAAyC,GAAGA,OAAO,GAAG,YAAY,CAAC;MAC5F;MACAhB,GAAG,CAACU,QAAQ,GAAGV,GAAG,CAACU,QAAQ,IAAI,CAAC,CAAC;MACjCV,GAAG,CAACU,QAAQ,CAACM,OAAO,CAAC,GAAGG,WAAW;MACnC;MACA;MACA,IAAIA,WAAW,CAACE,KAAK,EAAE;QACrBrB,GAAG,CAACU,QAAQ,CAACW,KAAK,GAAGF,WAAW,CAACE,KAAK;MACxC;MACA,IAAIF,WAAW,CAACG,SAAS,EAAE;QACzBtB,GAAG,CAACU,QAAQ,CAACY,SAAS,GAAGH,WAAW,CAACG,SAAS;MAChD;IACF;EACF;AACF;AACA,SAASC,qBAAqBA,CAACvB,GAAG,EAAE;EAClCe,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC;EACvCe,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC;EACvCe,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC;EACvCe,qBAAqB,CAACf,GAAG,EAAE,OAAO,CAAC;EACnCe,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC;EACvC;EACAe,qBAAqB,CAACf,GAAG,EAAE,YAAY,CAAC;EACxC;EACAe,qBAAqB,CAACf,GAAG,EAAE,WAAW,CAAC;AACzC;AACA,SAASwB,eAAeA,CAACxB,GAAG,EAAEyB,QAAQ,EAAE;EACtC;EACA,IAAIC,cAAc,GAAG7B,QAAQ,CAACG,GAAG,CAAC,IAAIA,GAAG,CAACyB,QAAQ,CAAC;EACnD,IAAIE,SAAS,GAAG9B,QAAQ,CAAC6B,cAAc,CAAC,IAAIA,cAAc,CAACC,SAAS;EACpE,IAAIA,SAAS,EAAE;IACb,IAAIhB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACAnB,YAAY,CAAC,yBAAyB,GAAG+B,QAAQ,GAAG,0EAA0E,GAAGA,QAAQ,GAAG,gBAAgB,CAAC;IAC/J;IACA,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGX,SAAS,CAACmC,kBAAkB,CAACvB,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MACvE,IAAI0B,YAAY,GAAGpC,SAAS,CAACmC,kBAAkB,CAACzB,CAAC,CAAC;MAClD,IAAIwB,SAAS,CAACG,cAAc,CAACD,YAAY,CAAC,EAAE;QAC1CH,cAAc,CAACG,YAAY,CAAC,GAAGF,SAAS,CAACE,YAAY,CAAC;MACxD;IACF;EACF;AACF;AACA,SAASE,qBAAqBA,CAAC/B,GAAG,EAAE;EAClC,IAAIA,GAAG,EAAE;IACPuB,qBAAqB,CAACvB,GAAG,CAAC;IAC1BwB,eAAe,CAACxB,GAAG,EAAE,OAAO,CAAC;IAC7BA,GAAG,CAACU,QAAQ,IAAIc,eAAe,CAACxB,GAAG,CAACU,QAAQ,EAAE,OAAO,CAAC;EACxD;AACF;AACA,SAASsB,aAAaA,CAACC,SAAS,EAAE;EAChC,IAAI,CAACpC,QAAQ,CAACoC,SAAS,CAAC,EAAE;IACxB;EACF;EACAlC,kBAAkB,CAACkC,SAAS,CAAC;EAC7BV,qBAAqB,CAACU,SAAS,CAAC;EAChCT,eAAe,CAACS,SAAS,EAAE,OAAO,CAAC;EACnC;EACAT,eAAe,CAACS,SAAS,EAAE,YAAY,CAAC;EACxC;EACAT,eAAe,CAACS,SAAS,EAAE,WAAW,CAAC;EACvC,IAAIA,SAAS,CAACvB,QAAQ,EAAE;IACtBc,eAAe,CAACS,SAAS,CAACvB,QAAQ,EAAE,OAAO,CAAC;IAC5C;IACAc,eAAe,CAACS,SAAS,CAACvB,QAAQ,EAAE,YAAY,CAAC;IACjD;IACAc,eAAe,CAACS,SAAS,CAACvB,QAAQ,EAAE,WAAW,CAAC;EAClD;EACA,IAAIwB,SAAS,GAAGD,SAAS,CAACC,SAAS;EACnC,IAAIA,SAAS,EAAE;IACbnC,kBAAkB,CAACmC,SAAS,CAAC;IAC7BH,qBAAqB,CAACG,SAAS,CAAC;EAClC;EACA,IAAIC,QAAQ,GAAGF,SAAS,CAACE,QAAQ;EACjC,IAAIA,QAAQ,EAAE;IACZpC,kBAAkB,CAACoC,QAAQ,CAAC;IAC5BJ,qBAAqB,CAACI,QAAQ,CAAC;EACjC;EACA,IAAIC,QAAQ,GAAGH,SAAS,CAACG,QAAQ;EACjC,IAAIA,QAAQ,EAAE;IACZL,qBAAqB,CAACK,QAAQ,CAAC;EACjC;EACA,IAAIC,IAAI,GAAGJ,SAAS,CAACI,IAAI;EACzB;EACA;EACA,IAAIJ,SAAS,CAACK,IAAI,KAAK,OAAO,EAAE;IAC9BD,IAAI,GAAGA,IAAI,IAAIJ,SAAS,CAACM,KAAK;IAC9B,IAAIC,QAAQ,GAAGP,SAAS,CAACQ,KAAK,IAAIR,SAAS,CAACS,KAAK;IACjD,IAAIF,QAAQ,IAAI,CAAChD,MAAM,CAACmD,YAAY,CAACH,QAAQ,CAAC,EAAE;MAC9C,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,QAAQ,CAACnC,MAAM,EAAEF,CAAC,EAAE,EAAE;QACxC4B,qBAAqB,CAACS,QAAQ,CAACrC,CAAC,CAAC,CAAC;MACpC;IACF;IACAX,MAAM,CAACI,IAAI,CAACqC,SAAS,CAACW,UAAU,EAAE,UAAU5C,GAAG,EAAE;MAC/CuB,qBAAqB,CAACvB,GAAG,CAAC;IAC5B,CAAC,CAAC;EACJ;EACA,IAAIqC,IAAI,IAAI,CAAC7C,MAAM,CAACmD,YAAY,CAACN,IAAI,CAAC,EAAE;IACtC,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,IAAI,CAAChC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACpC4B,qBAAqB,CAACM,IAAI,CAAClC,CAAC,CAAC,CAAC;IAChC;EACF;EACA;EACA+B,SAAS,GAAGD,SAAS,CAACC,SAAS;EAC/B,IAAIA,SAAS,IAAIA,SAAS,CAACG,IAAI,EAAE;IAC/B,IAAIQ,MAAM,GAAGX,SAAS,CAACG,IAAI;IAC3B,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,MAAM,CAACxC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACtC4B,qBAAqB,CAACc,MAAM,CAAC1C,CAAC,CAAC,CAAC;IAClC;EACF;EACA;EACAgC,QAAQ,GAAGF,SAAS,CAACE,QAAQ;EAC7B,IAAIA,QAAQ,IAAIA,QAAQ,CAACE,IAAI,EAAE;IAC7B,IAAIS,MAAM,GAAGX,QAAQ,CAACE,IAAI;IAC1B,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,MAAM,CAACzC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACtC,IAAIX,MAAM,CAACuD,OAAO,CAACD,MAAM,CAAC3C,CAAC,CAAC,CAAC,EAAE;QAC7B4B,qBAAqB,CAACe,MAAM,CAAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnC4B,qBAAqB,CAACe,MAAM,CAAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,MAAM;QACL4B,qBAAqB,CAACe,MAAM,CAAC3C,CAAC,CAAC,CAAC;MAClC;IACF;EACF;EACA;EACA,IAAI8B,SAAS,CAACK,IAAI,KAAK,OAAO,EAAE;IAC9Bd,eAAe,CAACS,SAAS,EAAE,WAAW,CAAC;IACvCT,eAAe,CAACS,SAAS,EAAE,OAAO,CAAC;IACnCT,eAAe,CAACS,SAAS,EAAE,QAAQ,CAAC;EACtC,CAAC,MAAM,IAAIA,SAAS,CAACK,IAAI,KAAK,SAAS,EAAE;IACvCvB,qBAAqB,CAACkB,SAAS,CAACe,UAAU,EAAE,WAAW,CAAC;IACxDxD,MAAM,CAACI,IAAI,CAACqC,SAAS,CAACgB,MAAM,EAAE,UAAUjD,GAAG,EAAE;MAC3CuB,qBAAqB,CAACvB,GAAG,CAAC;IAC5B,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIiC,SAAS,CAACK,IAAI,KAAK,MAAM,EAAE;IACpCf,qBAAqB,CAACU,SAAS,CAACiB,MAAM,CAAC;EACzC;EACA;AACF;AACA,SAASC,KAAKA,CAACC,CAAC,EAAE;EAChB,OAAO5D,MAAM,CAACuD,OAAO,CAACK,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC,CAAC,GAAG,EAAE;AAC7C;AACA,SAASC,KAAKA,CAACD,CAAC,EAAE;EAChB,OAAO,CAAC5D,MAAM,CAACuD,OAAO,CAACK,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,KAAK,CAAC,CAAC;AAC7C;AACA,eAAe,SAASE,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACzD5D,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACE,MAAM,CAAC,EAAE,UAAUxB,SAAS,EAAE;IAC9CpC,QAAQ,CAACoC,SAAS,CAAC,IAAID,aAAa,CAACC,SAAS,CAAC;EACjD,CAAC,CAAC;EACF,IAAIyB,IAAI,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,OAAO,CAAC;EAC/FF,OAAO,IAAIE,IAAI,CAACC,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,CAAC;EACxE/D,IAAI,CAAC8D,IAAI,EAAE,UAAUE,QAAQ,EAAE;IAC7BhE,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACK,QAAQ,CAAC,CAAC,EAAE,UAAUC,OAAO,EAAE;MAC/C,IAAIA,OAAO,EAAE;QACXrC,eAAe,CAACqC,OAAO,EAAE,WAAW,CAAC;QACrCrC,eAAe,CAACqC,OAAO,CAACC,WAAW,EAAE,OAAO,CAAC;MAC/C;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFlE,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACQ,QAAQ,CAAC,EAAE,UAAUC,WAAW,EAAE;IAClD,IAAIC,mBAAmB,GAAGD,WAAW,IAAIA,WAAW,CAACC,mBAAmB;IACxEzC,eAAe,CAACyC,mBAAmB,EAAE,WAAW,CAAC;IACjDzC,eAAe,CAACyC,mBAAmB,IAAIA,mBAAmB,CAACH,WAAW,EAAE,OAAO,CAAC;EAClF,CAAC,CAAC;EACFlE,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACW,QAAQ,CAAC,EAAE,UAAUC,WAAW,EAAE;IAClDpD,qBAAqB,CAACoD,WAAW,EAAE,WAAW,CAAC;IAC/C3C,eAAe,CAAC2C,WAAW,EAAE,UAAU,CAAC;IACxC3C,eAAe,CAAC2C,WAAW,EAAE,YAAY,CAAC;IAC1C3C,eAAe,CAAC2C,WAAW,EAAE,WAAW,CAAC;EAC3C,CAAC,CAAC;EACF;EACAvE,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACa,KAAK,CAAC,EAAE,UAAUC,QAAQ,EAAE;IAC5C7C,eAAe,CAAC6C,QAAQ,EAAE,MAAM,CAAC;IACjC;IACA,IAAIA,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACT,QAAQ,IAAI,IAAI,EAAE;MAC9CS,QAAQ,CAACT,QAAQ,GAAGS,QAAQ,CAACC,IAAI;MACjC,OAAOD,QAAQ,CAACC,IAAI;MACpB,IAAI3D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCnB,YAAY,CAAC,+DAA+D,CAAC;MAC/E;IACF;IACA,IAAI2E,QAAQ,CAACE,OAAO,IAAI,IAAI,IAAIF,QAAQ,CAACG,WAAW,IAAI,IAAI,EAAE;MAC5DH,QAAQ,CAACG,WAAW,GAAGH,QAAQ,CAACE,OAAO;MACvC,OAAOF,QAAQ,CAACE,OAAO;MACvB,IAAI5D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCnB,YAAY,CAAC,qEAAqE,CAAC;MACrF;IACF;IACA,IAAIiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCjB,IAAI,CAACyE,QAAQ,CAACI,SAAS,EAAE,UAAUC,YAAY,EAAE;QAC/C,IAAIA,YAAY,CAACC,IAAI,EAAE;UACrBhF,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,iBAAiB,CAAC;QACxD;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACFC,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACqB,GAAG,CAAC,EAAE,UAAUC,MAAM,EAAE;IACxC,IAAIhF,QAAQ,CAACgF,MAAM,CAAC,EAAE;MACpB9C,qBAAqB,CAAC8C,MAAM,CAAC;MAC7BjF,IAAI,CAACuD,KAAK,CAAC0B,MAAM,CAACC,OAAO,CAAC,EAAE,UAAUC,SAAS,EAAE;QAC/ChD,qBAAqB,CAACgD,SAAS,CAAC;MAClC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACFnF,IAAI,CAACuD,KAAK,CAACI,MAAM,CAACyB,QAAQ,CAAC,EAAE,UAAUC,WAAW,EAAE;IAClDlD,qBAAqB,CAACkD,WAAW,CAAC;IAClClE,qBAAqB,CAACkE,WAAW,EAAE,OAAO,CAAC;IAC3ClE,qBAAqB,CAACkE,WAAW,EAAE,WAAW,CAAC;IAC/ClE,qBAAqB,CAACkE,WAAW,EAAE,cAAc,EAAE,IAAI,CAAC;IACxD,IAAI5C,IAAI,GAAG4C,WAAW,CAAC5C,IAAI;IAC3B7C,MAAM,CAACuD,OAAO,CAACV,IAAI,CAAC,IAAI7C,MAAM,CAACI,IAAI,CAACyC,IAAI,EAAE,UAAU6C,IAAI,EAAE;MACxD,IAAI1F,MAAM,CAACK,QAAQ,CAACqF,IAAI,CAAC,EAAE;QACzBnE,qBAAqB,CAACmE,IAAI,EAAE,OAAO,CAAC;QACpCnE,qBAAqB,CAACmE,IAAI,EAAE,WAAW,CAAC;MAC1C;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACFtF,IAAI,CAACuD,KAAK,CAACI,MAAM,CAAC4B,OAAO,CAAC,EAAE,UAAUC,UAAU,EAAE;IAChDrE,qBAAqB,CAACqE,UAAU,EAAE,WAAW,CAAC;IAC9CxF,IAAI,CAACwF,UAAU,CAACC,OAAO,EAAE,UAAUC,UAAU,EAAE;MAC7CvE,qBAAqB,CAACuE,UAAU,EAAE,WAAW,CAAC;IAChD,CAAC,CAAC;EACJ,CAAC,CAAC;EACF9D,eAAe,CAAC6B,KAAK,CAACE,MAAM,CAACO,WAAW,CAAC,EAAE,OAAO,CAAC;EACnDtC,eAAe,CAAC6B,KAAK,CAACE,MAAM,CAACgC,OAAO,CAAC,CAACzB,WAAW,EAAE,OAAO,CAAC;EAC3D;EACA;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}