{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport SymbolClz from './Symbol.js';\nimport { isObject } from 'zrender/lib/core/util.js';\nimport { getLabelStatesModels } from '../../label/labelStyle.js';\nfunction symbolNeedsDraw(data, point, idx, opt) {\n  return point && !isNaN(point[0]) && !isNaN(point[1]) && !(opt.isIgnore && opt.isIgnore(idx))\n  // We do not set clipShape on group, because it will cut part of\n  // the symbol element shape. We use the same clip shape here as\n  // the line clip.\n  && !(opt.clipShape && !opt.clipShape.contain(point[0], point[1])) && data.getItemVisual(idx, 'symbol') !== 'none';\n}\nfunction normalizeUpdateOpt(opt) {\n  if (opt != null && !isObject(opt)) {\n    opt = {\n      isIgnore: opt\n    };\n  }\n  return opt || {};\n}\nfunction makeSeriesScope(data) {\n  var seriesModel = data.hostModel;\n  var emphasisModel = seriesModel.getModel('emphasis');\n  return {\n    emphasisItemStyle: emphasisModel.getModel('itemStyle').getItemStyle(),\n    blurItemStyle: seriesModel.getModel(['blur', 'itemStyle']).getItemStyle(),\n    selectItemStyle: seriesModel.getModel(['select', 'itemStyle']).getItemStyle(),\n    focus: emphasisModel.get('focus'),\n    blurScope: emphasisModel.get('blurScope'),\n    emphasisDisabled: emphasisModel.get('disabled'),\n    hoverScale: emphasisModel.get('scale'),\n    labelStatesModels: getLabelStatesModels(seriesModel),\n    cursorStyle: seriesModel.get('cursor')\n  };\n}\nvar SymbolDraw = /** @class */function () {\n  function SymbolDraw(SymbolCtor) {\n    this.group = new graphic.Group();\n    this._SymbolCtor = SymbolCtor || SymbolClz;\n  }\n  /**\r\n   * Update symbols draw by new data\r\n   */\n  SymbolDraw.prototype.updateData = function (data, opt) {\n    // Remove progressive els.\n    this._progressiveEls = null;\n    opt = normalizeUpdateOpt(opt);\n    var group = this.group;\n    var seriesModel = data.hostModel;\n    var oldData = this._data;\n    var SymbolCtor = this._SymbolCtor;\n    var disableAnimation = opt.disableAnimation;\n    var seriesScope = makeSeriesScope(data);\n    var symbolUpdateOpt = {\n      disableAnimation: disableAnimation\n    };\n    var getSymbolPoint = opt.getSymbolPoint || function (idx) {\n      return data.getItemLayout(idx);\n    };\n    // There is no oldLineData only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n    if (!oldData) {\n      group.removeAll();\n    }\n    data.diff(oldData).add(function (newIdx) {\n      var point = getSymbolPoint(newIdx);\n      if (symbolNeedsDraw(data, point, newIdx, opt)) {\n        var symbolEl = new SymbolCtor(data, newIdx, seriesScope, symbolUpdateOpt);\n        symbolEl.setPosition(point);\n        data.setItemGraphicEl(newIdx, symbolEl);\n        group.add(symbolEl);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      var point = getSymbolPoint(newIdx);\n      if (!symbolNeedsDraw(data, point, newIdx, opt)) {\n        group.remove(symbolEl);\n        return;\n      }\n      var newSymbolType = data.getItemVisual(newIdx, 'symbol') || 'circle';\n      var oldSymbolType = symbolEl && symbolEl.getSymbolType && symbolEl.getSymbolType();\n      if (!symbolEl\n      // Create a new if symbol type changed.\n      || oldSymbolType && oldSymbolType !== newSymbolType) {\n        group.remove(symbolEl);\n        symbolEl = new SymbolCtor(data, newIdx, seriesScope, symbolUpdateOpt);\n        symbolEl.setPosition(point);\n      } else {\n        symbolEl.updateData(data, newIdx, seriesScope, symbolUpdateOpt);\n        var target = {\n          x: point[0],\n          y: point[1]\n        };\n        disableAnimation ? symbolEl.attr(target) : graphic.updateProps(symbolEl, target, seriesModel);\n      }\n      // Add back\n      group.add(symbolEl);\n      data.setItemGraphicEl(newIdx, symbolEl);\n    }).remove(function (oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      el && el.fadeOut(function () {\n        group.remove(el);\n      }, seriesModel);\n    }).execute();\n    this._getSymbolPoint = getSymbolPoint;\n    this._data = data;\n  };\n  ;\n  SymbolDraw.prototype.updateLayout = function () {\n    var _this = this;\n    var data = this._data;\n    if (data) {\n      // Not use animation\n      data.eachItemGraphicEl(function (el, idx) {\n        var point = _this._getSymbolPoint(idx);\n        el.setPosition(point);\n        el.markRedraw();\n      });\n    }\n  };\n  ;\n  SymbolDraw.prototype.incrementalPrepareUpdate = function (data) {\n    this._seriesScope = makeSeriesScope(data);\n    this._data = null;\n    this.group.removeAll();\n  };\n  ;\n  /**\r\n   * Update symbols draw by new data\r\n   */\n  SymbolDraw.prototype.incrementalUpdate = function (taskParams, data, opt) {\n    // Clear\n    this._progressiveEls = [];\n    opt = normalizeUpdateOpt(opt);\n    function updateIncrementalAndHover(el) {\n      if (!el.isGroup) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n    for (var idx = taskParams.start; idx < taskParams.end; idx++) {\n      var point = data.getItemLayout(idx);\n      if (symbolNeedsDraw(data, point, idx, opt)) {\n        var el = new this._SymbolCtor(data, idx, this._seriesScope);\n        el.traverse(updateIncrementalAndHover);\n        el.setPosition(point);\n        this.group.add(el);\n        data.setItemGraphicEl(idx, el);\n        this._progressiveEls.push(el);\n      }\n    }\n  };\n  ;\n  SymbolDraw.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  SymbolDraw.prototype.remove = function (enableAnimation) {\n    var group = this.group;\n    var data = this._data;\n    // Incremental model do not have this._data.\n    if (data && enableAnimation) {\n      data.eachItemGraphicEl(function (el) {\n        el.fadeOut(function () {\n          group.remove(el);\n        }, data.hostModel);\n      });\n    } else {\n      group.removeAll();\n    }\n  };\n  ;\n  return SymbolDraw;\n}();\nexport default SymbolDraw;", "map": {"version": 3, "names": ["graphic", "SymbolClz", "isObject", "getLabelStatesModels", "symbolNeedsDraw", "data", "point", "idx", "opt", "isNaN", "isIgnore", "clipShape", "contain", "getItemVisual", "normalizeUpdateOpt", "makeSeriesScope", "seriesModel", "hostModel", "emphasisModel", "getModel", "emphasisItemStyle", "getItemStyle", "blurItemStyle", "selectItemStyle", "focus", "get", "blurScope", "emphasisDisabled", "hoverScale", "labelStatesModels", "cursorStyle", "SymbolDraw", "SymbolCtor", "group", "Group", "_SymbolCtor", "prototype", "updateData", "_progressiveEls", "oldData", "_data", "disableAnimation", "seriesScope", "symbolUpdateOpt", "getSymbolPoint", "getItemLayout", "removeAll", "diff", "add", "newIdx", "symbolEl", "setPosition", "setItemGraphicEl", "update", "oldIdx", "getItemGraphicEl", "remove", "newSymbolType", "oldSymbolType", "getSymbolType", "target", "x", "y", "attr", "updateProps", "el", "fadeOut", "execute", "_getSymbolPoint", "updateLayout", "_this", "eachItemGraphicEl", "mark<PERSON><PERSON><PERSON>", "incrementalPrepareUpdate", "_seriesScope", "incrementalUpdate", "taskParams", "updateIncrementalAndHover", "isGroup", "incremental", "ensureState", "hoverLayer", "start", "end", "traverse", "push", "eachRendered", "cb", "traverseElements", "enableAnimation"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/helper/SymbolDraw.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport SymbolClz from './Symbol.js';\nimport { isObject } from 'zrender/lib/core/util.js';\nimport { getLabelStatesModels } from '../../label/labelStyle.js';\nfunction symbolNeedsDraw(data, point, idx, opt) {\n  return point && !isNaN(point[0]) && !isNaN(point[1]) && !(opt.isIgnore && opt.isIgnore(idx))\n  // We do not set clipShape on group, because it will cut part of\n  // the symbol element shape. We use the same clip shape here as\n  // the line clip.\n  && !(opt.clipShape && !opt.clipShape.contain(point[0], point[1])) && data.getItemVisual(idx, 'symbol') !== 'none';\n}\nfunction normalizeUpdateOpt(opt) {\n  if (opt != null && !isObject(opt)) {\n    opt = {\n      isIgnore: opt\n    };\n  }\n  return opt || {};\n}\nfunction makeSeriesScope(data) {\n  var seriesModel = data.hostModel;\n  var emphasisModel = seriesModel.getModel('emphasis');\n  return {\n    emphasisItemStyle: emphasisModel.getModel('itemStyle').getItemStyle(),\n    blurItemStyle: seriesModel.getModel(['blur', 'itemStyle']).getItemStyle(),\n    selectItemStyle: seriesModel.getModel(['select', 'itemStyle']).getItemStyle(),\n    focus: emphasisModel.get('focus'),\n    blurScope: emphasisModel.get('blurScope'),\n    emphasisDisabled: emphasisModel.get('disabled'),\n    hoverScale: emphasisModel.get('scale'),\n    labelStatesModels: getLabelStatesModels(seriesModel),\n    cursorStyle: seriesModel.get('cursor')\n  };\n}\nvar SymbolDraw = /** @class */function () {\n  function SymbolDraw(SymbolCtor) {\n    this.group = new graphic.Group();\n    this._SymbolCtor = SymbolCtor || SymbolClz;\n  }\n  /**\r\n   * Update symbols draw by new data\r\n   */\n  SymbolDraw.prototype.updateData = function (data, opt) {\n    // Remove progressive els.\n    this._progressiveEls = null;\n    opt = normalizeUpdateOpt(opt);\n    var group = this.group;\n    var seriesModel = data.hostModel;\n    var oldData = this._data;\n    var SymbolCtor = this._SymbolCtor;\n    var disableAnimation = opt.disableAnimation;\n    var seriesScope = makeSeriesScope(data);\n    var symbolUpdateOpt = {\n      disableAnimation: disableAnimation\n    };\n    var getSymbolPoint = opt.getSymbolPoint || function (idx) {\n      return data.getItemLayout(idx);\n    };\n    // There is no oldLineData only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n    if (!oldData) {\n      group.removeAll();\n    }\n    data.diff(oldData).add(function (newIdx) {\n      var point = getSymbolPoint(newIdx);\n      if (symbolNeedsDraw(data, point, newIdx, opt)) {\n        var symbolEl = new SymbolCtor(data, newIdx, seriesScope, symbolUpdateOpt);\n        symbolEl.setPosition(point);\n        data.setItemGraphicEl(newIdx, symbolEl);\n        group.add(symbolEl);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var symbolEl = oldData.getItemGraphicEl(oldIdx);\n      var point = getSymbolPoint(newIdx);\n      if (!symbolNeedsDraw(data, point, newIdx, opt)) {\n        group.remove(symbolEl);\n        return;\n      }\n      var newSymbolType = data.getItemVisual(newIdx, 'symbol') || 'circle';\n      var oldSymbolType = symbolEl && symbolEl.getSymbolType && symbolEl.getSymbolType();\n      if (!symbolEl\n      // Create a new if symbol type changed.\n      || oldSymbolType && oldSymbolType !== newSymbolType) {\n        group.remove(symbolEl);\n        symbolEl = new SymbolCtor(data, newIdx, seriesScope, symbolUpdateOpt);\n        symbolEl.setPosition(point);\n      } else {\n        symbolEl.updateData(data, newIdx, seriesScope, symbolUpdateOpt);\n        var target = {\n          x: point[0],\n          y: point[1]\n        };\n        disableAnimation ? symbolEl.attr(target) : graphic.updateProps(symbolEl, target, seriesModel);\n      }\n      // Add back\n      group.add(symbolEl);\n      data.setItemGraphicEl(newIdx, symbolEl);\n    }).remove(function (oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      el && el.fadeOut(function () {\n        group.remove(el);\n      }, seriesModel);\n    }).execute();\n    this._getSymbolPoint = getSymbolPoint;\n    this._data = data;\n  };\n  ;\n  SymbolDraw.prototype.updateLayout = function () {\n    var _this = this;\n    var data = this._data;\n    if (data) {\n      // Not use animation\n      data.eachItemGraphicEl(function (el, idx) {\n        var point = _this._getSymbolPoint(idx);\n        el.setPosition(point);\n        el.markRedraw();\n      });\n    }\n  };\n  ;\n  SymbolDraw.prototype.incrementalPrepareUpdate = function (data) {\n    this._seriesScope = makeSeriesScope(data);\n    this._data = null;\n    this.group.removeAll();\n  };\n  ;\n  /**\r\n   * Update symbols draw by new data\r\n   */\n  SymbolDraw.prototype.incrementalUpdate = function (taskParams, data, opt) {\n    // Clear\n    this._progressiveEls = [];\n    opt = normalizeUpdateOpt(opt);\n    function updateIncrementalAndHover(el) {\n      if (!el.isGroup) {\n        el.incremental = true;\n        el.ensureState('emphasis').hoverLayer = true;\n      }\n    }\n    for (var idx = taskParams.start; idx < taskParams.end; idx++) {\n      var point = data.getItemLayout(idx);\n      if (symbolNeedsDraw(data, point, idx, opt)) {\n        var el = new this._SymbolCtor(data, idx, this._seriesScope);\n        el.traverse(updateIncrementalAndHover);\n        el.setPosition(point);\n        this.group.add(el);\n        data.setItemGraphicEl(idx, el);\n        this._progressiveEls.push(el);\n      }\n    }\n  };\n  ;\n  SymbolDraw.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  SymbolDraw.prototype.remove = function (enableAnimation) {\n    var group = this.group;\n    var data = this._data;\n    // Incremental model do not have this._data.\n    if (data && enableAnimation) {\n      data.eachItemGraphicEl(function (el) {\n        el.fadeOut(function () {\n          group.remove(el);\n        }, data.hostModel);\n      });\n    } else {\n      group.removeAll();\n    }\n  };\n  ;\n  return SymbolDraw;\n}();\nexport default SymbolDraw;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,OAAO,MAAM,uBAAuB;AAChD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,eAAeA,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9C,OAAOF,KAAK,IAAI,CAACG,KAAK,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAACG,KAAK,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAEE,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACE,QAAQ,CAACH,GAAG,CAAC;EAC3F;EACA;EACA;EAAA,GACG,EAAEC,GAAG,CAACG,SAAS,IAAI,CAACH,GAAG,CAACG,SAAS,CAACC,OAAO,CAACN,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAID,IAAI,CAACQ,aAAa,CAACN,GAAG,EAAE,QAAQ,CAAC,KAAK,MAAM;AACnH;AACA,SAASO,kBAAkBA,CAACN,GAAG,EAAE;EAC/B,IAAIA,GAAG,IAAI,IAAI,IAAI,CAACN,QAAQ,CAACM,GAAG,CAAC,EAAE;IACjCA,GAAG,GAAG;MACJE,QAAQ,EAAEF;IACZ,CAAC;EACH;EACA,OAAOA,GAAG,IAAI,CAAC,CAAC;AAClB;AACA,SAASO,eAAeA,CAACV,IAAI,EAAE;EAC7B,IAAIW,WAAW,GAAGX,IAAI,CAACY,SAAS;EAChC,IAAIC,aAAa,GAAGF,WAAW,CAACG,QAAQ,CAAC,UAAU,CAAC;EACpD,OAAO;IACLC,iBAAiB,EAAEF,aAAa,CAACC,QAAQ,CAAC,WAAW,CAAC,CAACE,YAAY,CAAC,CAAC;IACrEC,aAAa,EAAEN,WAAW,CAACG,QAAQ,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;IACzEE,eAAe,EAAEP,WAAW,CAACG,QAAQ,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;IAC7EG,KAAK,EAAEN,aAAa,CAACO,GAAG,CAAC,OAAO,CAAC;IACjCC,SAAS,EAAER,aAAa,CAACO,GAAG,CAAC,WAAW,CAAC;IACzCE,gBAAgB,EAAET,aAAa,CAACO,GAAG,CAAC,UAAU,CAAC;IAC/CG,UAAU,EAAEV,aAAa,CAACO,GAAG,CAAC,OAAO,CAAC;IACtCI,iBAAiB,EAAE1B,oBAAoB,CAACa,WAAW,CAAC;IACpDc,WAAW,EAAEd,WAAW,CAACS,GAAG,CAAC,QAAQ;EACvC,CAAC;AACH;AACA,IAAIM,UAAU,GAAG,aAAa,YAAY;EACxC,SAASA,UAAUA,CAACC,UAAU,EAAE;IAC9B,IAAI,CAACC,KAAK,GAAG,IAAIjC,OAAO,CAACkC,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,WAAW,GAAGH,UAAU,IAAI/B,SAAS;EAC5C;EACA;AACF;AACA;EACE8B,UAAU,CAACK,SAAS,CAACC,UAAU,GAAG,UAAUhC,IAAI,EAAEG,GAAG,EAAE;IACrD;IACA,IAAI,CAAC8B,eAAe,GAAG,IAAI;IAC3B9B,GAAG,GAAGM,kBAAkB,CAACN,GAAG,CAAC;IAC7B,IAAIyB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIjB,WAAW,GAAGX,IAAI,CAACY,SAAS;IAChC,IAAIsB,OAAO,GAAG,IAAI,CAACC,KAAK;IACxB,IAAIR,UAAU,GAAG,IAAI,CAACG,WAAW;IACjC,IAAIM,gBAAgB,GAAGjC,GAAG,CAACiC,gBAAgB;IAC3C,IAAIC,WAAW,GAAG3B,eAAe,CAACV,IAAI,CAAC;IACvC,IAAIsC,eAAe,GAAG;MACpBF,gBAAgB,EAAEA;IACpB,CAAC;IACD,IAAIG,cAAc,GAAGpC,GAAG,CAACoC,cAAc,IAAI,UAAUrC,GAAG,EAAE;MACxD,OAAOF,IAAI,CAACwC,aAAa,CAACtC,GAAG,CAAC;IAChC,CAAC;IACD;IACA;IACA,IAAI,CAACgC,OAAO,EAAE;MACZN,KAAK,CAACa,SAAS,CAAC,CAAC;IACnB;IACAzC,IAAI,CAAC0C,IAAI,CAACR,OAAO,CAAC,CAACS,GAAG,CAAC,UAAUC,MAAM,EAAE;MACvC,IAAI3C,KAAK,GAAGsC,cAAc,CAACK,MAAM,CAAC;MAClC,IAAI7C,eAAe,CAACC,IAAI,EAAEC,KAAK,EAAE2C,MAAM,EAAEzC,GAAG,CAAC,EAAE;QAC7C,IAAI0C,QAAQ,GAAG,IAAIlB,UAAU,CAAC3B,IAAI,EAAE4C,MAAM,EAAEP,WAAW,EAAEC,eAAe,CAAC;QACzEO,QAAQ,CAACC,WAAW,CAAC7C,KAAK,CAAC;QAC3BD,IAAI,CAAC+C,gBAAgB,CAACH,MAAM,EAAEC,QAAQ,CAAC;QACvCjB,KAAK,CAACe,GAAG,CAACE,QAAQ,CAAC;MACrB;IACF,CAAC,CAAC,CAACG,MAAM,CAAC,UAAUJ,MAAM,EAAEK,MAAM,EAAE;MAClC,IAAIJ,QAAQ,GAAGX,OAAO,CAACgB,gBAAgB,CAACD,MAAM,CAAC;MAC/C,IAAIhD,KAAK,GAAGsC,cAAc,CAACK,MAAM,CAAC;MAClC,IAAI,CAAC7C,eAAe,CAACC,IAAI,EAAEC,KAAK,EAAE2C,MAAM,EAAEzC,GAAG,CAAC,EAAE;QAC9CyB,KAAK,CAACuB,MAAM,CAACN,QAAQ,CAAC;QACtB;MACF;MACA,IAAIO,aAAa,GAAGpD,IAAI,CAACQ,aAAa,CAACoC,MAAM,EAAE,QAAQ,CAAC,IAAI,QAAQ;MACpE,IAAIS,aAAa,GAAGR,QAAQ,IAAIA,QAAQ,CAACS,aAAa,IAAIT,QAAQ,CAACS,aAAa,CAAC,CAAC;MAClF,IAAI,CAACT;MACL;MAAA,GACGQ,aAAa,IAAIA,aAAa,KAAKD,aAAa,EAAE;QACnDxB,KAAK,CAACuB,MAAM,CAACN,QAAQ,CAAC;QACtBA,QAAQ,GAAG,IAAIlB,UAAU,CAAC3B,IAAI,EAAE4C,MAAM,EAAEP,WAAW,EAAEC,eAAe,CAAC;QACrEO,QAAQ,CAACC,WAAW,CAAC7C,KAAK,CAAC;MAC7B,CAAC,MAAM;QACL4C,QAAQ,CAACb,UAAU,CAAChC,IAAI,EAAE4C,MAAM,EAAEP,WAAW,EAAEC,eAAe,CAAC;QAC/D,IAAIiB,MAAM,GAAG;UACXC,CAAC,EAAEvD,KAAK,CAAC,CAAC,CAAC;UACXwD,CAAC,EAAExD,KAAK,CAAC,CAAC;QACZ,CAAC;QACDmC,gBAAgB,GAAGS,QAAQ,CAACa,IAAI,CAACH,MAAM,CAAC,GAAG5D,OAAO,CAACgE,WAAW,CAACd,QAAQ,EAAEU,MAAM,EAAE5C,WAAW,CAAC;MAC/F;MACA;MACAiB,KAAK,CAACe,GAAG,CAACE,QAAQ,CAAC;MACnB7C,IAAI,CAAC+C,gBAAgB,CAACH,MAAM,EAAEC,QAAQ,CAAC;IACzC,CAAC,CAAC,CAACM,MAAM,CAAC,UAAUF,MAAM,EAAE;MAC1B,IAAIW,EAAE,GAAG1B,OAAO,CAACgB,gBAAgB,CAACD,MAAM,CAAC;MACzCW,EAAE,IAAIA,EAAE,CAACC,OAAO,CAAC,YAAY;QAC3BjC,KAAK,CAACuB,MAAM,CAACS,EAAE,CAAC;MAClB,CAAC,EAAEjD,WAAW,CAAC;IACjB,CAAC,CAAC,CAACmD,OAAO,CAAC,CAAC;IACZ,IAAI,CAACC,eAAe,GAAGxB,cAAc;IACrC,IAAI,CAACJ,KAAK,GAAGnC,IAAI;EACnB,CAAC;EACD;EACA0B,UAAU,CAACK,SAAS,CAACiC,YAAY,GAAG,YAAY;IAC9C,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIjE,IAAI,GAAG,IAAI,CAACmC,KAAK;IACrB,IAAInC,IAAI,EAAE;MACR;MACAA,IAAI,CAACkE,iBAAiB,CAAC,UAAUN,EAAE,EAAE1D,GAAG,EAAE;QACxC,IAAID,KAAK,GAAGgE,KAAK,CAACF,eAAe,CAAC7D,GAAG,CAAC;QACtC0D,EAAE,CAACd,WAAW,CAAC7C,KAAK,CAAC;QACrB2D,EAAE,CAACO,UAAU,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ;EACF,CAAC;EACD;EACAzC,UAAU,CAACK,SAAS,CAACqC,wBAAwB,GAAG,UAAUpE,IAAI,EAAE;IAC9D,IAAI,CAACqE,YAAY,GAAG3D,eAAe,CAACV,IAAI,CAAC;IACzC,IAAI,CAACmC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACP,KAAK,CAACa,SAAS,CAAC,CAAC;EACxB,CAAC;EACD;EACA;AACF;AACA;EACEf,UAAU,CAACK,SAAS,CAACuC,iBAAiB,GAAG,UAAUC,UAAU,EAAEvE,IAAI,EAAEG,GAAG,EAAE;IACxE;IACA,IAAI,CAAC8B,eAAe,GAAG,EAAE;IACzB9B,GAAG,GAAGM,kBAAkB,CAACN,GAAG,CAAC;IAC7B,SAASqE,yBAAyBA,CAACZ,EAAE,EAAE;MACrC,IAAI,CAACA,EAAE,CAACa,OAAO,EAAE;QACfb,EAAE,CAACc,WAAW,GAAG,IAAI;QACrBd,EAAE,CAACe,WAAW,CAAC,UAAU,CAAC,CAACC,UAAU,GAAG,IAAI;MAC9C;IACF;IACA,KAAK,IAAI1E,GAAG,GAAGqE,UAAU,CAACM,KAAK,EAAE3E,GAAG,GAAGqE,UAAU,CAACO,GAAG,EAAE5E,GAAG,EAAE,EAAE;MAC5D,IAAID,KAAK,GAAGD,IAAI,CAACwC,aAAa,CAACtC,GAAG,CAAC;MACnC,IAAIH,eAAe,CAACC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,CAAC,EAAE;QAC1C,IAAIyD,EAAE,GAAG,IAAI,IAAI,CAAC9B,WAAW,CAAC9B,IAAI,EAAEE,GAAG,EAAE,IAAI,CAACmE,YAAY,CAAC;QAC3DT,EAAE,CAACmB,QAAQ,CAACP,yBAAyB,CAAC;QACtCZ,EAAE,CAACd,WAAW,CAAC7C,KAAK,CAAC;QACrB,IAAI,CAAC2B,KAAK,CAACe,GAAG,CAACiB,EAAE,CAAC;QAClB5D,IAAI,CAAC+C,gBAAgB,CAAC7C,GAAG,EAAE0D,EAAE,CAAC;QAC9B,IAAI,CAAC3B,eAAe,CAAC+C,IAAI,CAACpB,EAAE,CAAC;MAC/B;IACF;EACF,CAAC;EACD;EACAlC,UAAU,CAACK,SAAS,CAACkD,YAAY,GAAG,UAAUC,EAAE,EAAE;IAChDvF,OAAO,CAACwF,gBAAgB,CAAC,IAAI,CAAClD,eAAe,IAAI,IAAI,CAACL,KAAK,EAAEsD,EAAE,CAAC;EAClE,CAAC;EACDxD,UAAU,CAACK,SAAS,CAACoB,MAAM,GAAG,UAAUiC,eAAe,EAAE;IACvD,IAAIxD,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI5B,IAAI,GAAG,IAAI,CAACmC,KAAK;IACrB;IACA,IAAInC,IAAI,IAAIoF,eAAe,EAAE;MAC3BpF,IAAI,CAACkE,iBAAiB,CAAC,UAAUN,EAAE,EAAE;QACnCA,EAAE,CAACC,OAAO,CAAC,YAAY;UACrBjC,KAAK,CAACuB,MAAM,CAACS,EAAE,CAAC;QAClB,CAAC,EAAE5D,IAAI,CAACY,SAAS,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLgB,KAAK,CAACa,SAAS,CAAC,CAAC;IACnB;EACF,CAAC;EACD;EACA,OAAOf,UAAU;AACnB,CAAC,CAAC,CAAC;AACH,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}