{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner } from '../../util/model.js';\n/**\r\n * @return {string} If large mode changed, return string 'reset';\r\n */\nexport default function createRenderPlanner() {\n  var inner = makeInner();\n  return function (seriesModel) {\n    var fields = inner(seriesModel);\n    var pipelineContext = seriesModel.pipelineContext;\n    var originalLarge = !!fields.large;\n    var originalProgressive = !!fields.progressiveRender;\n    // FIXME: if the planner works on a filtered series, `pipelineContext` does not\n    // exists. See #11611 . Probably we need to modify this structure, see the comment\n    // on `performRawSeries` in `Schedular.js`.\n    var large = fields.large = !!(pipelineContext && pipelineContext.large);\n    var progressive = fields.progressiveRender = !!(pipelineContext && pipelineContext.progressiveRender);\n    return !!(originalLarge !== large || originalProgressive !== progressive) && 'reset';\n  };\n}", "map": {"version": 3, "names": ["makeInner", "createRenderPlanner", "inner", "seriesModel", "fields", "pipelineContext", "originalLarge", "large", "originalProgressive", "progressiveRender", "progressive"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/helper/createRenderPlanner.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner } from '../../util/model.js';\n/**\r\n * @return {string} If large mode changed, return string 'reset';\r\n */\nexport default function createRenderPlanner() {\n  var inner = makeInner();\n  return function (seriesModel) {\n    var fields = inner(seriesModel);\n    var pipelineContext = seriesModel.pipelineContext;\n    var originalLarge = !!fields.large;\n    var originalProgressive = !!fields.progressiveRender;\n    // FIXME: if the planner works on a filtered series, `pipelineContext` does not\n    // exists. See #11611 . Probably we need to modify this structure, see the comment\n    // on `performRawSeries` in `Schedular.js`.\n    var large = fields.large = !!(pipelineContext && pipelineContext.large);\n    var progressive = fields.progressiveRender = !!(pipelineContext && pipelineContext.progressiveRender);\n    return !!(originalLarge !== large || originalProgressive !== progressive) && 'reset';\n  };\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,qBAAqB;AAC/C;AACA;AACA;AACA,eAAe,SAASC,mBAAmBA,CAAA,EAAG;EAC5C,IAAIC,KAAK,GAAGF,SAAS,CAAC,CAAC;EACvB,OAAO,UAAUG,WAAW,EAAE;IAC5B,IAAIC,MAAM,GAAGF,KAAK,CAACC,WAAW,CAAC;IAC/B,IAAIE,eAAe,GAAGF,WAAW,CAACE,eAAe;IACjD,IAAIC,aAAa,GAAG,CAAC,CAACF,MAAM,CAACG,KAAK;IAClC,IAAIC,mBAAmB,GAAG,CAAC,CAACJ,MAAM,CAACK,iBAAiB;IACpD;IACA;IACA;IACA,IAAIF,KAAK,GAAGH,MAAM,CAACG,KAAK,GAAG,CAAC,EAAEF,eAAe,IAAIA,eAAe,CAACE,KAAK,CAAC;IACvE,IAAIG,WAAW,GAAGN,MAAM,CAACK,iBAAiB,GAAG,CAAC,EAAEJ,eAAe,IAAIA,eAAe,CAACI,iBAAiB,CAAC;IACrG,OAAO,CAAC,EAAEH,aAAa,KAAKC,KAAK,IAAIC,mBAAmB,KAAKE,WAAW,CAAC,IAAI,OAAO;EACtF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}