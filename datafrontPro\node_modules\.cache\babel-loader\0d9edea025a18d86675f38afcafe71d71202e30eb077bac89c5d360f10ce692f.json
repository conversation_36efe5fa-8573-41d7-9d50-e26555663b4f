{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as util from '../util';\n\n/**\n *  Rule for validating a regular expression pattern.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction pattern(rule, value, source, errors, options) {\n  if (rule.pattern) {\n    if (rule.pattern instanceof RegExp) {\n      // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n      // flag is accidentally set to `true`, which in a validation scenario\n      // is not necessary and the result might be misleading\n      rule.pattern.lastIndex = 0;\n      if (!rule.pattern.test(value)) {\n        errors.push(util.format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    } else if (typeof rule.pattern === 'string') {\n      var _pattern = new RegExp(rule.pattern);\n      if (!_pattern.test(value)) {\n        errors.push(util.format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    }\n  }\n}\nexport default pattern;", "map": {"version": 3, "names": ["util", "pattern", "rule", "value", "source", "errors", "options", "RegExp", "lastIndex", "test", "push", "format", "messages", "mismatch", "fullField", "_pattern"], "sources": ["E:/AllProject/datafront/node_modules/async-validator/es/rule/pattern.js"], "sourcesContent": ["import * as util from '../util';\n\n/**\n *  Rule for validating a regular expression pattern.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction pattern(rule, value, source, errors, options) {\n  if (rule.pattern) {\n    if (rule.pattern instanceof RegExp) {\n      // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n      // flag is accidentally set to `true`, which in a validation scenario\n      // is not necessary and the result might be misleading\n      rule.pattern.lastIndex = 0;\n      if (!rule.pattern.test(value)) {\n        errors.push(util.format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    } else if (typeof rule.pattern === 'string') {\n      var _pattern = new RegExp(rule.pattern);\n      if (!_pattern.test(value)) {\n        errors.push(util.format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    }\n  }\n}\n\nexport default pattern;"], "mappings": ";AAAA,OAAO,KAAKA,IAAI,MAAM,SAAS;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrD,IAAIJ,IAAI,CAACD,OAAO,EAAE;IAChB,IAAIC,IAAI,CAACD,OAAO,YAAYM,MAAM,EAAE;MAClC;MACA;MACA;MACAL,IAAI,CAACD,OAAO,CAACO,SAAS,GAAG,CAAC;MAC1B,IAAI,CAACN,IAAI,CAACD,OAAO,CAACQ,IAAI,CAACN,KAAK,CAAC,EAAE;QAC7BE,MAAM,CAACK,IAAI,CAACV,IAAI,CAACW,MAAM,CAACL,OAAO,CAACM,QAAQ,CAACX,OAAO,CAACY,QAAQ,EAAEX,IAAI,CAACY,SAAS,EAAEX,KAAK,EAAED,IAAI,CAACD,OAAO,CAAC,CAAC;MAClG;IACF,CAAC,MAAM,IAAI,OAAOC,IAAI,CAACD,OAAO,KAAK,QAAQ,EAAE;MAC3C,IAAIc,QAAQ,GAAG,IAAIR,MAAM,CAACL,IAAI,CAACD,OAAO,CAAC;MACvC,IAAI,CAACc,QAAQ,CAACN,IAAI,CAACN,KAAK,CAAC,EAAE;QACzBE,MAAM,CAACK,IAAI,CAACV,IAAI,CAACW,MAAM,CAACL,OAAO,CAACM,QAAQ,CAACX,OAAO,CAACY,QAAQ,EAAEX,IAAI,CAACY,SAAS,EAAEX,KAAK,EAAED,IAAI,CAACD,OAAO,CAAC,CAAC;MAClG;IACF;EACF;AACF;AAEA,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}