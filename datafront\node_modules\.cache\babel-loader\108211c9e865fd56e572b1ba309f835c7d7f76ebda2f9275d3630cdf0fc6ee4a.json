{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { cubicSubdivide } from '../core/curve.js';\nimport Path from '../graphic/Path.js';\nimport { defaults, map } from '../core/util.js';\nimport { lerp } from '../core/vector.js';\nimport { clonePath } from './path.js';\nimport Transformable from '../core/Transformable.js';\nimport { split } from './dividePath.js';\nimport { pathToBezierCurves } from './convertPath.js';\nfunction alignSubpath(subpath1, subpath2) {\n  var len1 = subpath1.length;\n  var len2 = subpath2.length;\n  if (len1 === len2) {\n    return [subpath1, subpath2];\n  }\n  var tmpSegX = [];\n  var tmpSegY = [];\n  var shorterPath = len1 < len2 ? subpath1 : subpath2;\n  var shorterLen = Math.min(len1, len2);\n  var diff = Math.abs(len2 - len1) / 6;\n  var shorterBezierCount = (shorterLen - 2) / 6;\n  var eachCurveSubDivCount = Math.ceil(diff / shorterBezierCount) + 1;\n  var newSubpath = [shorterPath[0], shorterPath[1]];\n  var remained = diff;\n  for (var i = 2; i < shorterLen;) {\n    var x0 = shorterPath[i - 2];\n    var y0 = shorterPath[i - 1];\n    var x1 = shorterPath[i++];\n    var y1 = shorterPath[i++];\n    var x2 = shorterPath[i++];\n    var y2 = shorterPath[i++];\n    var x3 = shorterPath[i++];\n    var y3 = shorterPath[i++];\n    if (remained <= 0) {\n      newSubpath.push(x1, y1, x2, y2, x3, y3);\n      continue;\n    }\n    var actualSubDivCount = Math.min(remained, eachCurveSubDivCount - 1) + 1;\n    for (var k = 1; k <= actualSubDivCount; k++) {\n      var p = k / actualSubDivCount;\n      cubicSubdivide(x0, x1, x2, x3, p, tmpSegX);\n      cubicSubdivide(y0, y1, y2, y3, p, tmpSegY);\n      x0 = tmpSegX[3];\n      y0 = tmpSegY[3];\n      newSubpath.push(tmpSegX[1], tmpSegY[1], tmpSegX[2], tmpSegY[2], x0, y0);\n      x1 = tmpSegX[5];\n      y1 = tmpSegY[5];\n      x2 = tmpSegX[6];\n      y2 = tmpSegY[6];\n    }\n    remained -= actualSubDivCount - 1;\n  }\n  return shorterPath === subpath1 ? [newSubpath, subpath2] : [subpath1, newSubpath];\n}\nfunction createSubpath(lastSubpathSubpath, otherSubpath) {\n  var len = lastSubpathSubpath.length;\n  var lastX = lastSubpathSubpath[len - 2];\n  var lastY = lastSubpathSubpath[len - 1];\n  var newSubpath = [];\n  for (var i = 0; i < otherSubpath.length;) {\n    newSubpath[i++] = lastX;\n    newSubpath[i++] = lastY;\n  }\n  return newSubpath;\n}\nexport function alignBezierCurves(array1, array2) {\n  var _a;\n  var lastSubpath1;\n  var lastSubpath2;\n  var newArray1 = [];\n  var newArray2 = [];\n  for (var i = 0; i < Math.max(array1.length, array2.length); i++) {\n    var subpath1 = array1[i];\n    var subpath2 = array2[i];\n    var newSubpath1 = void 0;\n    var newSubpath2 = void 0;\n    if (!subpath1) {\n      newSubpath1 = createSubpath(lastSubpath1 || subpath2, subpath2);\n      newSubpath2 = subpath2;\n    } else if (!subpath2) {\n      newSubpath2 = createSubpath(lastSubpath2 || subpath1, subpath1);\n      newSubpath1 = subpath1;\n    } else {\n      _a = alignSubpath(subpath1, subpath2), newSubpath1 = _a[0], newSubpath2 = _a[1];\n      lastSubpath1 = newSubpath1;\n      lastSubpath2 = newSubpath2;\n    }\n    newArray1.push(newSubpath1);\n    newArray2.push(newSubpath2);\n  }\n  return [newArray1, newArray2];\n}\nexport function centroid(array) {\n  var signedArea = 0;\n  var cx = 0;\n  var cy = 0;\n  var len = array.length;\n  for (var i = 0, j = len - 2; i < len; j = i, i += 2) {\n    var x0 = array[j];\n    var y0 = array[j + 1];\n    var x1 = array[i];\n    var y1 = array[i + 1];\n    var a = x0 * y1 - x1 * y0;\n    signedArea += a;\n    cx += (x0 + x1) * a;\n    cy += (y0 + y1) * a;\n  }\n  if (signedArea === 0) {\n    return [array[0] || 0, array[1] || 0];\n  }\n  return [cx / signedArea / 3, cy / signedArea / 3, signedArea];\n}\nfunction findBestRingOffset(fromSubBeziers, toSubBeziers, fromCp, toCp) {\n  var bezierCount = (fromSubBeziers.length - 2) / 6;\n  var bestScore = Infinity;\n  var bestOffset = 0;\n  var len = fromSubBeziers.length;\n  var len2 = len - 2;\n  for (var offset = 0; offset < bezierCount; offset++) {\n    var cursorOffset = offset * 6;\n    var score = 0;\n    for (var k = 0; k < len; k += 2) {\n      var idx = k === 0 ? cursorOffset : (cursorOffset + k - 2) % len2 + 2;\n      var x0 = fromSubBeziers[idx] - fromCp[0];\n      var y0 = fromSubBeziers[idx + 1] - fromCp[1];\n      var x1 = toSubBeziers[k] - toCp[0];\n      var y1 = toSubBeziers[k + 1] - toCp[1];\n      var dx = x1 - x0;\n      var dy = y1 - y0;\n      score += dx * dx + dy * dy;\n    }\n    if (score < bestScore) {\n      bestScore = score;\n      bestOffset = offset;\n    }\n  }\n  return bestOffset;\n}\nfunction reverse(array) {\n  var newArr = [];\n  var len = array.length;\n  for (var i = 0; i < len; i += 2) {\n    newArr[i] = array[len - i - 2];\n    newArr[i + 1] = array[len - i - 1];\n  }\n  return newArr;\n}\nfunction findBestMorphingRotation(fromArr, toArr, searchAngleIteration, searchAngleRange) {\n  var result = [];\n  var fromNeedsReverse;\n  for (var i = 0; i < fromArr.length; i++) {\n    var fromSubpathBezier = fromArr[i];\n    var toSubpathBezier = toArr[i];\n    var fromCp = centroid(fromSubpathBezier);\n    var toCp = centroid(toSubpathBezier);\n    if (fromNeedsReverse == null) {\n      fromNeedsReverse = fromCp[2] < 0 !== toCp[2] < 0;\n    }\n    var newFromSubpathBezier = [];\n    var newToSubpathBezier = [];\n    var bestAngle = 0;\n    var bestScore = Infinity;\n    var tmpArr = [];\n    var len = fromSubpathBezier.length;\n    if (fromNeedsReverse) {\n      fromSubpathBezier = reverse(fromSubpathBezier);\n    }\n    var offset = findBestRingOffset(fromSubpathBezier, toSubpathBezier, fromCp, toCp) * 6;\n    var len2 = len - 2;\n    for (var k = 0; k < len2; k += 2) {\n      var idx = (offset + k) % len2 + 2;\n      newFromSubpathBezier[k + 2] = fromSubpathBezier[idx] - fromCp[0];\n      newFromSubpathBezier[k + 3] = fromSubpathBezier[idx + 1] - fromCp[1];\n    }\n    newFromSubpathBezier[0] = fromSubpathBezier[offset] - fromCp[0];\n    newFromSubpathBezier[1] = fromSubpathBezier[offset + 1] - fromCp[1];\n    if (searchAngleIteration > 0) {\n      var step = searchAngleRange / searchAngleIteration;\n      for (var angle = -searchAngleRange / 2; angle <= searchAngleRange / 2; angle += step) {\n        var sa = Math.sin(angle);\n        var ca = Math.cos(angle);\n        var score = 0;\n        for (var k = 0; k < fromSubpathBezier.length; k += 2) {\n          var x0 = newFromSubpathBezier[k];\n          var y0 = newFromSubpathBezier[k + 1];\n          var x1 = toSubpathBezier[k] - toCp[0];\n          var y1 = toSubpathBezier[k + 1] - toCp[1];\n          var newX1 = x1 * ca - y1 * sa;\n          var newY1 = x1 * sa + y1 * ca;\n          tmpArr[k] = newX1;\n          tmpArr[k + 1] = newY1;\n          var dx = newX1 - x0;\n          var dy = newY1 - y0;\n          score += dx * dx + dy * dy;\n        }\n        if (score < bestScore) {\n          bestScore = score;\n          bestAngle = angle;\n          for (var m = 0; m < tmpArr.length; m++) {\n            newToSubpathBezier[m] = tmpArr[m];\n          }\n        }\n      }\n    } else {\n      for (var i_1 = 0; i_1 < len; i_1 += 2) {\n        newToSubpathBezier[i_1] = toSubpathBezier[i_1] - toCp[0];\n        newToSubpathBezier[i_1 + 1] = toSubpathBezier[i_1 + 1] - toCp[1];\n      }\n    }\n    result.push({\n      from: newFromSubpathBezier,\n      to: newToSubpathBezier,\n      fromCp: fromCp,\n      toCp: toCp,\n      rotation: -bestAngle\n    });\n  }\n  return result;\n}\nexport function isCombineMorphing(path) {\n  return path.__isCombineMorphing;\n}\nexport function isMorphing(el) {\n  return el.__morphT >= 0;\n}\nvar SAVED_METHOD_PREFIX = '__mOriginal_';\nfunction saveAndModifyMethod(obj, methodName, modifiers) {\n  var savedMethodName = SAVED_METHOD_PREFIX + methodName;\n  var originalMethod = obj[savedMethodName] || obj[methodName];\n  if (!obj[savedMethodName]) {\n    obj[savedMethodName] = obj[methodName];\n  }\n  var replace = modifiers.replace;\n  var after = modifiers.after;\n  var before = modifiers.before;\n  obj[methodName] = function () {\n    var args = arguments;\n    var res;\n    before && before.apply(this, args);\n    if (replace) {\n      res = replace.apply(this, args);\n    } else {\n      res = originalMethod.apply(this, args);\n    }\n    after && after.apply(this, args);\n    return res;\n  };\n}\nfunction restoreMethod(obj, methodName) {\n  var savedMethodName = SAVED_METHOD_PREFIX + methodName;\n  if (obj[savedMethodName]) {\n    obj[methodName] = obj[savedMethodName];\n    obj[savedMethodName] = null;\n  }\n}\nfunction applyTransformOnBeziers(bezierCurves, mm) {\n  for (var i = 0; i < bezierCurves.length; i++) {\n    var subBeziers = bezierCurves[i];\n    for (var k = 0; k < subBeziers.length;) {\n      var x = subBeziers[k];\n      var y = subBeziers[k + 1];\n      subBeziers[k++] = mm[0] * x + mm[2] * y + mm[4];\n      subBeziers[k++] = mm[1] * x + mm[3] * y + mm[5];\n    }\n  }\n}\nfunction prepareMorphPath(fromPath, toPath) {\n  var fromPathProxy = fromPath.getUpdatedPathProxy();\n  var toPathProxy = toPath.getUpdatedPathProxy();\n  var _a = alignBezierCurves(pathToBezierCurves(fromPathProxy), pathToBezierCurves(toPathProxy)),\n    fromBezierCurves = _a[0],\n    toBezierCurves = _a[1];\n  var fromPathTransform = fromPath.getComputedTransform();\n  var toPathTransform = toPath.getComputedTransform();\n  function updateIdentityTransform() {\n    this.transform = null;\n  }\n  fromPathTransform && applyTransformOnBeziers(fromBezierCurves, fromPathTransform);\n  toPathTransform && applyTransformOnBeziers(toBezierCurves, toPathTransform);\n  saveAndModifyMethod(toPath, 'updateTransform', {\n    replace: updateIdentityTransform\n  });\n  toPath.transform = null;\n  var morphingData = findBestMorphingRotation(fromBezierCurves, toBezierCurves, 10, Math.PI);\n  var tmpArr = [];\n  saveAndModifyMethod(toPath, 'buildPath', {\n    replace: function (path) {\n      var t = toPath.__morphT;\n      var onet = 1 - t;\n      var newCp = [];\n      for (var i = 0; i < morphingData.length; i++) {\n        var item = morphingData[i];\n        var from = item.from;\n        var to = item.to;\n        var angle = item.rotation * t;\n        var fromCp = item.fromCp;\n        var toCp = item.toCp;\n        var sa = Math.sin(angle);\n        var ca = Math.cos(angle);\n        lerp(newCp, fromCp, toCp, t);\n        for (var m = 0; m < from.length; m += 2) {\n          var x0_1 = from[m];\n          var y0_1 = from[m + 1];\n          var x1 = to[m];\n          var y1 = to[m + 1];\n          var x = x0_1 * onet + x1 * t;\n          var y = y0_1 * onet + y1 * t;\n          tmpArr[m] = x * ca - y * sa + newCp[0];\n          tmpArr[m + 1] = x * sa + y * ca + newCp[1];\n        }\n        var x0 = tmpArr[0];\n        var y0 = tmpArr[1];\n        path.moveTo(x0, y0);\n        for (var m = 2; m < from.length;) {\n          var x1 = tmpArr[m++];\n          var y1 = tmpArr[m++];\n          var x2 = tmpArr[m++];\n          var y2 = tmpArr[m++];\n          var x3 = tmpArr[m++];\n          var y3 = tmpArr[m++];\n          if (x0 === x1 && y0 === y1 && x2 === x3 && y2 === y3) {\n            path.lineTo(x3, y3);\n          } else {\n            path.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n          }\n          x0 = x3;\n          y0 = y3;\n        }\n      }\n    }\n  });\n}\nexport function morphPath(fromPath, toPath, animationOpts) {\n  if (!fromPath || !toPath) {\n    return toPath;\n  }\n  var oldDone = animationOpts.done;\n  var oldDuring = animationOpts.during;\n  prepareMorphPath(fromPath, toPath);\n  toPath.__morphT = 0;\n  function restoreToPath() {\n    restoreMethod(toPath, 'buildPath');\n    restoreMethod(toPath, 'updateTransform');\n    toPath.__morphT = -1;\n    toPath.createPathProxy();\n    toPath.dirtyShape();\n  }\n  toPath.animateTo({\n    __morphT: 1\n  }, defaults({\n    during: function (p) {\n      toPath.dirtyShape();\n      oldDuring && oldDuring(p);\n    },\n    done: function () {\n      restoreToPath();\n      oldDone && oldDone();\n    }\n  }, animationOpts));\n  return toPath;\n}\nfunction hilbert(x, y, minX, minY, maxX, maxY) {\n  var bits = 16;\n  x = maxX === minX ? 0 : Math.round(32767 * (x - minX) / (maxX - minX));\n  y = maxY === minY ? 0 : Math.round(32767 * (y - minY) / (maxY - minY));\n  var d = 0;\n  var tmp;\n  for (var s = (1 << bits) / 2; s > 0; s /= 2) {\n    var rx = 0;\n    var ry = 0;\n    if ((x & s) > 0) {\n      rx = 1;\n    }\n    if ((y & s) > 0) {\n      ry = 1;\n    }\n    d += s * s * (3 * rx ^ ry);\n    if (ry === 0) {\n      if (rx === 1) {\n        x = s - 1 - x;\n        y = s - 1 - y;\n      }\n      tmp = x;\n      x = y;\n      y = tmp;\n    }\n  }\n  return d;\n}\nfunction sortPaths(pathList) {\n  var xMin = Infinity;\n  var yMin = Infinity;\n  var xMax = -Infinity;\n  var yMax = -Infinity;\n  var cps = map(pathList, function (path) {\n    var rect = path.getBoundingRect();\n    var m = path.getComputedTransform();\n    var x = rect.x + rect.width / 2 + (m ? m[4] : 0);\n    var y = rect.y + rect.height / 2 + (m ? m[5] : 0);\n    xMin = Math.min(x, xMin);\n    yMin = Math.min(y, yMin);\n    xMax = Math.max(x, xMax);\n    yMax = Math.max(y, yMax);\n    return [x, y];\n  });\n  var items = map(cps, function (cp, idx) {\n    return {\n      cp: cp,\n      z: hilbert(cp[0], cp[1], xMin, yMin, xMax, yMax),\n      path: pathList[idx]\n    };\n  });\n  return items.sort(function (a, b) {\n    return a.z - b.z;\n  }).map(function (item) {\n    return item.path;\n  });\n}\n;\nfunction defaultDividePath(param) {\n  return split(param.path, param.count);\n}\nfunction createEmptyReturn() {\n  return {\n    fromIndividuals: [],\n    toIndividuals: [],\n    count: 0\n  };\n}\nexport function combineMorph(fromList, toPath, animationOpts) {\n  var fromPathList = [];\n  function addFromPath(fromList) {\n    for (var i = 0; i < fromList.length; i++) {\n      var from = fromList[i];\n      if (isCombineMorphing(from)) {\n        addFromPath(from.childrenRef());\n      } else if (from instanceof Path) {\n        fromPathList.push(from);\n      }\n    }\n  }\n  addFromPath(fromList);\n  var separateCount = fromPathList.length;\n  if (!separateCount) {\n    return createEmptyReturn();\n  }\n  var dividePath = animationOpts.dividePath || defaultDividePath;\n  var toSubPathList = dividePath({\n    path: toPath,\n    count: separateCount\n  });\n  if (toSubPathList.length !== separateCount) {\n    console.error('Invalid morphing: unmatched splitted path');\n    return createEmptyReturn();\n  }\n  fromPathList = sortPaths(fromPathList);\n  toSubPathList = sortPaths(toSubPathList);\n  var oldDone = animationOpts.done;\n  var oldDuring = animationOpts.during;\n  var individualDelay = animationOpts.individualDelay;\n  var identityTransform = new Transformable();\n  for (var i = 0; i < separateCount; i++) {\n    var from = fromPathList[i];\n    var to = toSubPathList[i];\n    to.parent = toPath;\n    to.copyTransform(identityTransform);\n    if (!individualDelay) {\n      prepareMorphPath(from, to);\n    }\n  }\n  toPath.__isCombineMorphing = true;\n  toPath.childrenRef = function () {\n    return toSubPathList;\n  };\n  function addToSubPathListToZr(zr) {\n    for (var i = 0; i < toSubPathList.length; i++) {\n      toSubPathList[i].addSelfToZr(zr);\n    }\n  }\n  saveAndModifyMethod(toPath, 'addSelfToZr', {\n    after: function (zr) {\n      addToSubPathListToZr(zr);\n    }\n  });\n  saveAndModifyMethod(toPath, 'removeSelfFromZr', {\n    after: function (zr) {\n      for (var i = 0; i < toSubPathList.length; i++) {\n        toSubPathList[i].removeSelfFromZr(zr);\n      }\n    }\n  });\n  function restoreToPath() {\n    toPath.__isCombineMorphing = false;\n    toPath.__morphT = -1;\n    toPath.childrenRef = null;\n    restoreMethod(toPath, 'addSelfToZr');\n    restoreMethod(toPath, 'removeSelfFromZr');\n  }\n  var toLen = toSubPathList.length;\n  if (individualDelay) {\n    var animating_1 = toLen;\n    var eachDone = function () {\n      animating_1--;\n      if (animating_1 === 0) {\n        restoreToPath();\n        oldDone && oldDone();\n      }\n    };\n    for (var i = 0; i < toLen; i++) {\n      var indivdualAnimationOpts = individualDelay ? defaults({\n        delay: (animationOpts.delay || 0) + individualDelay(i, toLen, fromPathList[i], toSubPathList[i]),\n        done: eachDone\n      }, animationOpts) : animationOpts;\n      morphPath(fromPathList[i], toSubPathList[i], indivdualAnimationOpts);\n    }\n  } else {\n    toPath.__morphT = 0;\n    toPath.animateTo({\n      __morphT: 1\n    }, defaults({\n      during: function (p) {\n        for (var i = 0; i < toLen; i++) {\n          var child = toSubPathList[i];\n          child.__morphT = toPath.__morphT;\n          child.dirtyShape();\n        }\n        oldDuring && oldDuring(p);\n      },\n      done: function () {\n        restoreToPath();\n        for (var i = 0; i < fromList.length; i++) {\n          restoreMethod(fromList[i], 'updateTransform');\n        }\n        oldDone && oldDone();\n      }\n    }, animationOpts));\n  }\n  if (toPath.__zr) {\n    addToSubPathListToZr(toPath.__zr);\n  }\n  return {\n    fromIndividuals: fromPathList,\n    toIndividuals: toSubPathList,\n    count: toLen\n  };\n}\nexport function separateMorph(fromPath, toPathList, animationOpts) {\n  var toLen = toPathList.length;\n  var fromPathList = [];\n  var dividePath = animationOpts.dividePath || defaultDividePath;\n  function addFromPath(fromList) {\n    for (var i = 0; i < fromList.length; i++) {\n      var from = fromList[i];\n      if (isCombineMorphing(from)) {\n        addFromPath(from.childrenRef());\n      } else if (from instanceof Path) {\n        fromPathList.push(from);\n      }\n    }\n  }\n  if (isCombineMorphing(fromPath)) {\n    addFromPath(fromPath.childrenRef());\n    var fromLen = fromPathList.length;\n    if (fromLen < toLen) {\n      var k = 0;\n      for (var i = fromLen; i < toLen; i++) {\n        fromPathList.push(clonePath(fromPathList[k++ % fromLen]));\n      }\n    }\n    fromPathList.length = toLen;\n  } else {\n    fromPathList = dividePath({\n      path: fromPath,\n      count: toLen\n    });\n    var fromPathTransform = fromPath.getComputedTransform();\n    for (var i = 0; i < fromPathList.length; i++) {\n      fromPathList[i].setLocalTransform(fromPathTransform);\n    }\n    if (fromPathList.length !== toLen) {\n      console.error('Invalid morphing: unmatched splitted path');\n      return createEmptyReturn();\n    }\n  }\n  fromPathList = sortPaths(fromPathList);\n  toPathList = sortPaths(toPathList);\n  var individualDelay = animationOpts.individualDelay;\n  for (var i = 0; i < toLen; i++) {\n    var indivdualAnimationOpts = individualDelay ? defaults({\n      delay: (animationOpts.delay || 0) + individualDelay(i, toLen, fromPathList[i], toPathList[i])\n    }, animationOpts) : animationOpts;\n    morphPath(fromPathList[i], toPathList[i], indivdualAnimationOpts);\n  }\n  return {\n    fromIndividuals: fromPathList,\n    toIndividuals: toPathList,\n    count: toPathList.length\n  };\n}\nexport { split as defaultDividePath };", "map": {"version": 3, "names": ["cubicSubdivide", "Path", "defaults", "map", "lerp", "<PERSON><PERSON><PERSON>", "Transformable", "split", "pathToBezierCurves", "alignSubpath", "subpath1", "subpath2", "len1", "length", "len2", "tmpSegX", "tmpSegY", "shorterPath", "shorter<PERSON>en", "Math", "min", "diff", "abs", "shorterBezierCount", "eachCurveSubDivCount", "ceil", "newSubpath", "remained", "i", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "push", "actualSubDivCount", "k", "p", "createSubpath", "lastSubpathSubpath", "otherSubpath", "len", "lastX", "lastY", "alignBezierCurves", "array1", "array2", "_a", "lastSubpath1", "lastSubpath2", "newArray1", "newArray2", "max", "newSubpath1", "newSubpath2", "centroid", "array", "signedArea", "cx", "cy", "j", "a", "findBestRingOffset", "fromSubBeziers", "toSubBeziers", "fromCp", "toCp", "bezierCount", "bestScore", "Infinity", "bestOffset", "offset", "cursorOffset", "score", "idx", "dx", "dy", "reverse", "newArr", "findBestMorphingRotation", "fromArr", "toArr", "searchAngleIteration", "searchAngleRange", "result", "fromNeedsReverse", "fromSubpathBezier", "toSubpathBezier", "newFromSubpathBezier", "newToSubpathBezier", "bestAngle", "tmpArr", "step", "angle", "sa", "sin", "ca", "cos", "newX1", "newY1", "m", "i_1", "from", "to", "rotation", "isCombineMorphing", "path", "__isCombineMorphing", "isMorphing", "el", "__morphT", "SAVED_METHOD_PREFIX", "saveAndModifyMethod", "obj", "methodName", "modifiers", "savedMethodName", "originalMethod", "replace", "after", "before", "args", "arguments", "res", "apply", "restoreMethod", "applyTransformOnBeziers", "bezierCurves", "mm", "subBeziers", "x", "y", "prepareMorphPath", "fromPath", "to<PERSON><PERSON>", "fromPathProxy", "getUpdatedPathProxy", "toPathProxy", "fromBezierCurves", "toBezierCurves", "fromPathTransform", "getComputedTransform", "toPathTransform", "updateIdentityTransform", "transform", "morphingData", "PI", "t", "onet", "newCp", "item", "x0_1", "y0_1", "moveTo", "lineTo", "bezierCurveTo", "morph<PERSON>ath", "animationOpts", "oldDone", "done", "oldDuring", "during", "restoreToPath", "createPathProxy", "dirtyShape", "animateTo", "hilbert", "minX", "minY", "maxX", "maxY", "bits", "round", "d", "tmp", "s", "rx", "ry", "sortPaths", "pathList", "xMin", "yMin", "xMax", "yMax", "cps", "rect", "getBoundingRect", "width", "height", "items", "cp", "z", "sort", "b", "defaultDividePath", "param", "count", "createEmptyReturn", "fromIndividuals", "toIndividuals", "combineMorph", "fromList", "fromPathList", "addFromPath", "childrenRef", "separateCount", "dividePath", "toSubPathList", "console", "error", "individualDelay", "identityTransform", "parent", "copyTransform", "addToSubPathListToZr", "zr", "addSelfToZr", "removeSelfFromZr", "toLen", "animating_1", "eachDone", "indivdualAnimationOpts", "delay", "child", "__zr", "separateMorph", "toPathList", "fromLen", "setLocalTransform"], "sources": ["D:/FastBI/datafront/node_modules/zrender/lib/tool/morphPath.js"], "sourcesContent": ["import { cubicSubdivide } from '../core/curve.js';\nimport Path from '../graphic/Path.js';\nimport { defaults, map } from '../core/util.js';\nimport { lerp } from '../core/vector.js';\nimport { clonePath } from './path.js';\nimport Transformable from '../core/Transformable.js';\nimport { split } from './dividePath.js';\nimport { pathToBezierCurves } from './convertPath.js';\nfunction alignSubpath(subpath1, subpath2) {\n    var len1 = subpath1.length;\n    var len2 = subpath2.length;\n    if (len1 === len2) {\n        return [subpath1, subpath2];\n    }\n    var tmpSegX = [];\n    var tmpSegY = [];\n    var shorterPath = len1 < len2 ? subpath1 : subpath2;\n    var shorterLen = Math.min(len1, len2);\n    var diff = Math.abs(len2 - len1) / 6;\n    var shorterBezierCount = (shorterLen - 2) / 6;\n    var eachCurveSubDivCount = Math.ceil(diff / shorterBezierCount) + 1;\n    var newSubpath = [shorterPath[0], shorterPath[1]];\n    var remained = diff;\n    for (var i = 2; i < shorterLen;) {\n        var x0 = shorterPath[i - 2];\n        var y0 = shorterPath[i - 1];\n        var x1 = shorterPath[i++];\n        var y1 = shorterPath[i++];\n        var x2 = shorterPath[i++];\n        var y2 = shorterPath[i++];\n        var x3 = shorterPath[i++];\n        var y3 = shorterPath[i++];\n        if (remained <= 0) {\n            newSubpath.push(x1, y1, x2, y2, x3, y3);\n            continue;\n        }\n        var actualSubDivCount = Math.min(remained, eachCurveSubDivCount - 1) + 1;\n        for (var k = 1; k <= actualSubDivCount; k++) {\n            var p = k / actualSubDivCount;\n            cubicSubdivide(x0, x1, x2, x3, p, tmpSegX);\n            cubicSubdivide(y0, y1, y2, y3, p, tmpSegY);\n            x0 = tmpSegX[3];\n            y0 = tmpSegY[3];\n            newSubpath.push(tmpSegX[1], tmpSegY[1], tmpSegX[2], tmpSegY[2], x0, y0);\n            x1 = tmpSegX[5];\n            y1 = tmpSegY[5];\n            x2 = tmpSegX[6];\n            y2 = tmpSegY[6];\n        }\n        remained -= actualSubDivCount - 1;\n    }\n    return shorterPath === subpath1 ? [newSubpath, subpath2] : [subpath1, newSubpath];\n}\nfunction createSubpath(lastSubpathSubpath, otherSubpath) {\n    var len = lastSubpathSubpath.length;\n    var lastX = lastSubpathSubpath[len - 2];\n    var lastY = lastSubpathSubpath[len - 1];\n    var newSubpath = [];\n    for (var i = 0; i < otherSubpath.length;) {\n        newSubpath[i++] = lastX;\n        newSubpath[i++] = lastY;\n    }\n    return newSubpath;\n}\nexport function alignBezierCurves(array1, array2) {\n    var _a;\n    var lastSubpath1;\n    var lastSubpath2;\n    var newArray1 = [];\n    var newArray2 = [];\n    for (var i = 0; i < Math.max(array1.length, array2.length); i++) {\n        var subpath1 = array1[i];\n        var subpath2 = array2[i];\n        var newSubpath1 = void 0;\n        var newSubpath2 = void 0;\n        if (!subpath1) {\n            newSubpath1 = createSubpath(lastSubpath1 || subpath2, subpath2);\n            newSubpath2 = subpath2;\n        }\n        else if (!subpath2) {\n            newSubpath2 = createSubpath(lastSubpath2 || subpath1, subpath1);\n            newSubpath1 = subpath1;\n        }\n        else {\n            _a = alignSubpath(subpath1, subpath2), newSubpath1 = _a[0], newSubpath2 = _a[1];\n            lastSubpath1 = newSubpath1;\n            lastSubpath2 = newSubpath2;\n        }\n        newArray1.push(newSubpath1);\n        newArray2.push(newSubpath2);\n    }\n    return [newArray1, newArray2];\n}\nexport function centroid(array) {\n    var signedArea = 0;\n    var cx = 0;\n    var cy = 0;\n    var len = array.length;\n    for (var i = 0, j = len - 2; i < len; j = i, i += 2) {\n        var x0 = array[j];\n        var y0 = array[j + 1];\n        var x1 = array[i];\n        var y1 = array[i + 1];\n        var a = x0 * y1 - x1 * y0;\n        signedArea += a;\n        cx += (x0 + x1) * a;\n        cy += (y0 + y1) * a;\n    }\n    if (signedArea === 0) {\n        return [array[0] || 0, array[1] || 0];\n    }\n    return [cx / signedArea / 3, cy / signedArea / 3, signedArea];\n}\nfunction findBestRingOffset(fromSubBeziers, toSubBeziers, fromCp, toCp) {\n    var bezierCount = (fromSubBeziers.length - 2) / 6;\n    var bestScore = Infinity;\n    var bestOffset = 0;\n    var len = fromSubBeziers.length;\n    var len2 = len - 2;\n    for (var offset = 0; offset < bezierCount; offset++) {\n        var cursorOffset = offset * 6;\n        var score = 0;\n        for (var k = 0; k < len; k += 2) {\n            var idx = k === 0 ? cursorOffset : ((cursorOffset + k - 2) % len2 + 2);\n            var x0 = fromSubBeziers[idx] - fromCp[0];\n            var y0 = fromSubBeziers[idx + 1] - fromCp[1];\n            var x1 = toSubBeziers[k] - toCp[0];\n            var y1 = toSubBeziers[k + 1] - toCp[1];\n            var dx = x1 - x0;\n            var dy = y1 - y0;\n            score += dx * dx + dy * dy;\n        }\n        if (score < bestScore) {\n            bestScore = score;\n            bestOffset = offset;\n        }\n    }\n    return bestOffset;\n}\nfunction reverse(array) {\n    var newArr = [];\n    var len = array.length;\n    for (var i = 0; i < len; i += 2) {\n        newArr[i] = array[len - i - 2];\n        newArr[i + 1] = array[len - i - 1];\n    }\n    return newArr;\n}\nfunction findBestMorphingRotation(fromArr, toArr, searchAngleIteration, searchAngleRange) {\n    var result = [];\n    var fromNeedsReverse;\n    for (var i = 0; i < fromArr.length; i++) {\n        var fromSubpathBezier = fromArr[i];\n        var toSubpathBezier = toArr[i];\n        var fromCp = centroid(fromSubpathBezier);\n        var toCp = centroid(toSubpathBezier);\n        if (fromNeedsReverse == null) {\n            fromNeedsReverse = fromCp[2] < 0 !== toCp[2] < 0;\n        }\n        var newFromSubpathBezier = [];\n        var newToSubpathBezier = [];\n        var bestAngle = 0;\n        var bestScore = Infinity;\n        var tmpArr = [];\n        var len = fromSubpathBezier.length;\n        if (fromNeedsReverse) {\n            fromSubpathBezier = reverse(fromSubpathBezier);\n        }\n        var offset = findBestRingOffset(fromSubpathBezier, toSubpathBezier, fromCp, toCp) * 6;\n        var len2 = len - 2;\n        for (var k = 0; k < len2; k += 2) {\n            var idx = (offset + k) % len2 + 2;\n            newFromSubpathBezier[k + 2] = fromSubpathBezier[idx] - fromCp[0];\n            newFromSubpathBezier[k + 3] = fromSubpathBezier[idx + 1] - fromCp[1];\n        }\n        newFromSubpathBezier[0] = fromSubpathBezier[offset] - fromCp[0];\n        newFromSubpathBezier[1] = fromSubpathBezier[offset + 1] - fromCp[1];\n        if (searchAngleIteration > 0) {\n            var step = searchAngleRange / searchAngleIteration;\n            for (var angle = -searchAngleRange / 2; angle <= searchAngleRange / 2; angle += step) {\n                var sa = Math.sin(angle);\n                var ca = Math.cos(angle);\n                var score = 0;\n                for (var k = 0; k < fromSubpathBezier.length; k += 2) {\n                    var x0 = newFromSubpathBezier[k];\n                    var y0 = newFromSubpathBezier[k + 1];\n                    var x1 = toSubpathBezier[k] - toCp[0];\n                    var y1 = toSubpathBezier[k + 1] - toCp[1];\n                    var newX1 = x1 * ca - y1 * sa;\n                    var newY1 = x1 * sa + y1 * ca;\n                    tmpArr[k] = newX1;\n                    tmpArr[k + 1] = newY1;\n                    var dx = newX1 - x0;\n                    var dy = newY1 - y0;\n                    score += dx * dx + dy * dy;\n                }\n                if (score < bestScore) {\n                    bestScore = score;\n                    bestAngle = angle;\n                    for (var m = 0; m < tmpArr.length; m++) {\n                        newToSubpathBezier[m] = tmpArr[m];\n                    }\n                }\n            }\n        }\n        else {\n            for (var i_1 = 0; i_1 < len; i_1 += 2) {\n                newToSubpathBezier[i_1] = toSubpathBezier[i_1] - toCp[0];\n                newToSubpathBezier[i_1 + 1] = toSubpathBezier[i_1 + 1] - toCp[1];\n            }\n        }\n        result.push({\n            from: newFromSubpathBezier,\n            to: newToSubpathBezier,\n            fromCp: fromCp,\n            toCp: toCp,\n            rotation: -bestAngle\n        });\n    }\n    return result;\n}\nexport function isCombineMorphing(path) {\n    return path.__isCombineMorphing;\n}\nexport function isMorphing(el) {\n    return el.__morphT >= 0;\n}\nvar SAVED_METHOD_PREFIX = '__mOriginal_';\nfunction saveAndModifyMethod(obj, methodName, modifiers) {\n    var savedMethodName = SAVED_METHOD_PREFIX + methodName;\n    var originalMethod = obj[savedMethodName] || obj[methodName];\n    if (!obj[savedMethodName]) {\n        obj[savedMethodName] = obj[methodName];\n    }\n    var replace = modifiers.replace;\n    var after = modifiers.after;\n    var before = modifiers.before;\n    obj[methodName] = function () {\n        var args = arguments;\n        var res;\n        before && before.apply(this, args);\n        if (replace) {\n            res = replace.apply(this, args);\n        }\n        else {\n            res = originalMethod.apply(this, args);\n        }\n        after && after.apply(this, args);\n        return res;\n    };\n}\nfunction restoreMethod(obj, methodName) {\n    var savedMethodName = SAVED_METHOD_PREFIX + methodName;\n    if (obj[savedMethodName]) {\n        obj[methodName] = obj[savedMethodName];\n        obj[savedMethodName] = null;\n    }\n}\nfunction applyTransformOnBeziers(bezierCurves, mm) {\n    for (var i = 0; i < bezierCurves.length; i++) {\n        var subBeziers = bezierCurves[i];\n        for (var k = 0; k < subBeziers.length;) {\n            var x = subBeziers[k];\n            var y = subBeziers[k + 1];\n            subBeziers[k++] = mm[0] * x + mm[2] * y + mm[4];\n            subBeziers[k++] = mm[1] * x + mm[3] * y + mm[5];\n        }\n    }\n}\nfunction prepareMorphPath(fromPath, toPath) {\n    var fromPathProxy = fromPath.getUpdatedPathProxy();\n    var toPathProxy = toPath.getUpdatedPathProxy();\n    var _a = alignBezierCurves(pathToBezierCurves(fromPathProxy), pathToBezierCurves(toPathProxy)), fromBezierCurves = _a[0], toBezierCurves = _a[1];\n    var fromPathTransform = fromPath.getComputedTransform();\n    var toPathTransform = toPath.getComputedTransform();\n    function updateIdentityTransform() {\n        this.transform = null;\n    }\n    fromPathTransform && applyTransformOnBeziers(fromBezierCurves, fromPathTransform);\n    toPathTransform && applyTransformOnBeziers(toBezierCurves, toPathTransform);\n    saveAndModifyMethod(toPath, 'updateTransform', { replace: updateIdentityTransform });\n    toPath.transform = null;\n    var morphingData = findBestMorphingRotation(fromBezierCurves, toBezierCurves, 10, Math.PI);\n    var tmpArr = [];\n    saveAndModifyMethod(toPath, 'buildPath', { replace: function (path) {\n            var t = toPath.__morphT;\n            var onet = 1 - t;\n            var newCp = [];\n            for (var i = 0; i < morphingData.length; i++) {\n                var item = morphingData[i];\n                var from = item.from;\n                var to = item.to;\n                var angle = item.rotation * t;\n                var fromCp = item.fromCp;\n                var toCp = item.toCp;\n                var sa = Math.sin(angle);\n                var ca = Math.cos(angle);\n                lerp(newCp, fromCp, toCp, t);\n                for (var m = 0; m < from.length; m += 2) {\n                    var x0_1 = from[m];\n                    var y0_1 = from[m + 1];\n                    var x1 = to[m];\n                    var y1 = to[m + 1];\n                    var x = x0_1 * onet + x1 * t;\n                    var y = y0_1 * onet + y1 * t;\n                    tmpArr[m] = (x * ca - y * sa) + newCp[0];\n                    tmpArr[m + 1] = (x * sa + y * ca) + newCp[1];\n                }\n                var x0 = tmpArr[0];\n                var y0 = tmpArr[1];\n                path.moveTo(x0, y0);\n                for (var m = 2; m < from.length;) {\n                    var x1 = tmpArr[m++];\n                    var y1 = tmpArr[m++];\n                    var x2 = tmpArr[m++];\n                    var y2 = tmpArr[m++];\n                    var x3 = tmpArr[m++];\n                    var y3 = tmpArr[m++];\n                    if (x0 === x1 && y0 === y1 && x2 === x3 && y2 === y3) {\n                        path.lineTo(x3, y3);\n                    }\n                    else {\n                        path.bezierCurveTo(x1, y1, x2, y2, x3, y3);\n                    }\n                    x0 = x3;\n                    y0 = y3;\n                }\n            }\n        } });\n}\nexport function morphPath(fromPath, toPath, animationOpts) {\n    if (!fromPath || !toPath) {\n        return toPath;\n    }\n    var oldDone = animationOpts.done;\n    var oldDuring = animationOpts.during;\n    prepareMorphPath(fromPath, toPath);\n    toPath.__morphT = 0;\n    function restoreToPath() {\n        restoreMethod(toPath, 'buildPath');\n        restoreMethod(toPath, 'updateTransform');\n        toPath.__morphT = -1;\n        toPath.createPathProxy();\n        toPath.dirtyShape();\n    }\n    toPath.animateTo({\n        __morphT: 1\n    }, defaults({\n        during: function (p) {\n            toPath.dirtyShape();\n            oldDuring && oldDuring(p);\n        },\n        done: function () {\n            restoreToPath();\n            oldDone && oldDone();\n        }\n    }, animationOpts));\n    return toPath;\n}\nfunction hilbert(x, y, minX, minY, maxX, maxY) {\n    var bits = 16;\n    x = (maxX === minX) ? 0 : Math.round(32767 * (x - minX) / (maxX - minX));\n    y = (maxY === minY) ? 0 : Math.round(32767 * (y - minY) / (maxY - minY));\n    var d = 0;\n    var tmp;\n    for (var s = (1 << bits) / 2; s > 0; s /= 2) {\n        var rx = 0;\n        var ry = 0;\n        if ((x & s) > 0) {\n            rx = 1;\n        }\n        if ((y & s) > 0) {\n            ry = 1;\n        }\n        d += s * s * ((3 * rx) ^ ry);\n        if (ry === 0) {\n            if (rx === 1) {\n                x = s - 1 - x;\n                y = s - 1 - y;\n            }\n            tmp = x;\n            x = y;\n            y = tmp;\n        }\n    }\n    return d;\n}\nfunction sortPaths(pathList) {\n    var xMin = Infinity;\n    var yMin = Infinity;\n    var xMax = -Infinity;\n    var yMax = -Infinity;\n    var cps = map(pathList, function (path) {\n        var rect = path.getBoundingRect();\n        var m = path.getComputedTransform();\n        var x = rect.x + rect.width / 2 + (m ? m[4] : 0);\n        var y = rect.y + rect.height / 2 + (m ? m[5] : 0);\n        xMin = Math.min(x, xMin);\n        yMin = Math.min(y, yMin);\n        xMax = Math.max(x, xMax);\n        yMax = Math.max(y, yMax);\n        return [x, y];\n    });\n    var items = map(cps, function (cp, idx) {\n        return {\n            cp: cp,\n            z: hilbert(cp[0], cp[1], xMin, yMin, xMax, yMax),\n            path: pathList[idx]\n        };\n    });\n    return items.sort(function (a, b) { return a.z - b.z; }).map(function (item) { return item.path; });\n}\n;\nfunction defaultDividePath(param) {\n    return split(param.path, param.count);\n}\nfunction createEmptyReturn() {\n    return {\n        fromIndividuals: [],\n        toIndividuals: [],\n        count: 0\n    };\n}\nexport function combineMorph(fromList, toPath, animationOpts) {\n    var fromPathList = [];\n    function addFromPath(fromList) {\n        for (var i = 0; i < fromList.length; i++) {\n            var from = fromList[i];\n            if (isCombineMorphing(from)) {\n                addFromPath(from.childrenRef());\n            }\n            else if (from instanceof Path) {\n                fromPathList.push(from);\n            }\n        }\n    }\n    addFromPath(fromList);\n    var separateCount = fromPathList.length;\n    if (!separateCount) {\n        return createEmptyReturn();\n    }\n    var dividePath = animationOpts.dividePath || defaultDividePath;\n    var toSubPathList = dividePath({\n        path: toPath, count: separateCount\n    });\n    if (toSubPathList.length !== separateCount) {\n        console.error('Invalid morphing: unmatched splitted path');\n        return createEmptyReturn();\n    }\n    fromPathList = sortPaths(fromPathList);\n    toSubPathList = sortPaths(toSubPathList);\n    var oldDone = animationOpts.done;\n    var oldDuring = animationOpts.during;\n    var individualDelay = animationOpts.individualDelay;\n    var identityTransform = new Transformable();\n    for (var i = 0; i < separateCount; i++) {\n        var from = fromPathList[i];\n        var to = toSubPathList[i];\n        to.parent = toPath;\n        to.copyTransform(identityTransform);\n        if (!individualDelay) {\n            prepareMorphPath(from, to);\n        }\n    }\n    toPath.__isCombineMorphing = true;\n    toPath.childrenRef = function () {\n        return toSubPathList;\n    };\n    function addToSubPathListToZr(zr) {\n        for (var i = 0; i < toSubPathList.length; i++) {\n            toSubPathList[i].addSelfToZr(zr);\n        }\n    }\n    saveAndModifyMethod(toPath, 'addSelfToZr', {\n        after: function (zr) {\n            addToSubPathListToZr(zr);\n        }\n    });\n    saveAndModifyMethod(toPath, 'removeSelfFromZr', {\n        after: function (zr) {\n            for (var i = 0; i < toSubPathList.length; i++) {\n                toSubPathList[i].removeSelfFromZr(zr);\n            }\n        }\n    });\n    function restoreToPath() {\n        toPath.__isCombineMorphing = false;\n        toPath.__morphT = -1;\n        toPath.childrenRef = null;\n        restoreMethod(toPath, 'addSelfToZr');\n        restoreMethod(toPath, 'removeSelfFromZr');\n    }\n    var toLen = toSubPathList.length;\n    if (individualDelay) {\n        var animating_1 = toLen;\n        var eachDone = function () {\n            animating_1--;\n            if (animating_1 === 0) {\n                restoreToPath();\n                oldDone && oldDone();\n            }\n        };\n        for (var i = 0; i < toLen; i++) {\n            var indivdualAnimationOpts = individualDelay ? defaults({\n                delay: (animationOpts.delay || 0) + individualDelay(i, toLen, fromPathList[i], toSubPathList[i]),\n                done: eachDone\n            }, animationOpts) : animationOpts;\n            morphPath(fromPathList[i], toSubPathList[i], indivdualAnimationOpts);\n        }\n    }\n    else {\n        toPath.__morphT = 0;\n        toPath.animateTo({\n            __morphT: 1\n        }, defaults({\n            during: function (p) {\n                for (var i = 0; i < toLen; i++) {\n                    var child = toSubPathList[i];\n                    child.__morphT = toPath.__morphT;\n                    child.dirtyShape();\n                }\n                oldDuring && oldDuring(p);\n            },\n            done: function () {\n                restoreToPath();\n                for (var i = 0; i < fromList.length; i++) {\n                    restoreMethod(fromList[i], 'updateTransform');\n                }\n                oldDone && oldDone();\n            }\n        }, animationOpts));\n    }\n    if (toPath.__zr) {\n        addToSubPathListToZr(toPath.__zr);\n    }\n    return {\n        fromIndividuals: fromPathList,\n        toIndividuals: toSubPathList,\n        count: toLen\n    };\n}\nexport function separateMorph(fromPath, toPathList, animationOpts) {\n    var toLen = toPathList.length;\n    var fromPathList = [];\n    var dividePath = animationOpts.dividePath || defaultDividePath;\n    function addFromPath(fromList) {\n        for (var i = 0; i < fromList.length; i++) {\n            var from = fromList[i];\n            if (isCombineMorphing(from)) {\n                addFromPath(from.childrenRef());\n            }\n            else if (from instanceof Path) {\n                fromPathList.push(from);\n            }\n        }\n    }\n    if (isCombineMorphing(fromPath)) {\n        addFromPath(fromPath.childrenRef());\n        var fromLen = fromPathList.length;\n        if (fromLen < toLen) {\n            var k = 0;\n            for (var i = fromLen; i < toLen; i++) {\n                fromPathList.push(clonePath(fromPathList[k++ % fromLen]));\n            }\n        }\n        fromPathList.length = toLen;\n    }\n    else {\n        fromPathList = dividePath({ path: fromPath, count: toLen });\n        var fromPathTransform = fromPath.getComputedTransform();\n        for (var i = 0; i < fromPathList.length; i++) {\n            fromPathList[i].setLocalTransform(fromPathTransform);\n        }\n        if (fromPathList.length !== toLen) {\n            console.error('Invalid morphing: unmatched splitted path');\n            return createEmptyReturn();\n        }\n    }\n    fromPathList = sortPaths(fromPathList);\n    toPathList = sortPaths(toPathList);\n    var individualDelay = animationOpts.individualDelay;\n    for (var i = 0; i < toLen; i++) {\n        var indivdualAnimationOpts = individualDelay ? defaults({\n            delay: (animationOpts.delay || 0) + individualDelay(i, toLen, fromPathList[i], toPathList[i])\n        }, animationOpts) : animationOpts;\n        morphPath(fromPathList[i], toPathList[i], indivdualAnimationOpts);\n    }\n    return {\n        fromIndividuals: fromPathList,\n        toIndividuals: toPathList,\n        count: toPathList.length\n    };\n}\nexport { split as defaultDividePath };\n"], "mappings": ";;;AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,QAAQ,EAAEC,GAAG,QAAQ,iBAAiB;AAC/C,SAASC,IAAI,QAAQ,mBAAmB;AACxC,SAASC,SAAS,QAAQ,WAAW;AACrC,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,KAAK,QAAQ,iBAAiB;AACvC,SAASC,kBAAkB,QAAQ,kBAAkB;AACrD,SAASC,YAAYA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;EACtC,IAAIC,IAAI,GAAGF,QAAQ,CAACG,MAAM;EAC1B,IAAIC,IAAI,GAAGH,QAAQ,CAACE,MAAM;EAC1B,IAAID,IAAI,KAAKE,IAAI,EAAE;IACf,OAAO,CAACJ,QAAQ,EAAEC,QAAQ,CAAC;EAC/B;EACA,IAAII,OAAO,GAAG,EAAE;EAChB,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,WAAW,GAAGL,IAAI,GAAGE,IAAI,GAAGJ,QAAQ,GAAGC,QAAQ;EACnD,IAAIO,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACR,IAAI,EAAEE,IAAI,CAAC;EACrC,IAAIO,IAAI,GAAGF,IAAI,CAACG,GAAG,CAACR,IAAI,GAAGF,IAAI,CAAC,GAAG,CAAC;EACpC,IAAIW,kBAAkB,GAAG,CAACL,UAAU,GAAG,CAAC,IAAI,CAAC;EAC7C,IAAIM,oBAAoB,GAAGL,IAAI,CAACM,IAAI,CAACJ,IAAI,GAAGE,kBAAkB,CAAC,GAAG,CAAC;EACnE,IAAIG,UAAU,GAAG,CAACT,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC;EACjD,IAAIU,QAAQ,GAAGN,IAAI;EACnB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,UAAU,GAAG;IAC7B,IAAIW,EAAE,GAAGZ,WAAW,CAACW,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAIE,EAAE,GAAGb,WAAW,CAACW,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAIG,EAAE,GAAGd,WAAW,CAACW,CAAC,EAAE,CAAC;IACzB,IAAII,EAAE,GAAGf,WAAW,CAACW,CAAC,EAAE,CAAC;IACzB,IAAIK,EAAE,GAAGhB,WAAW,CAACW,CAAC,EAAE,CAAC;IACzB,IAAIM,EAAE,GAAGjB,WAAW,CAACW,CAAC,EAAE,CAAC;IACzB,IAAIO,EAAE,GAAGlB,WAAW,CAACW,CAAC,EAAE,CAAC;IACzB,IAAIQ,EAAE,GAAGnB,WAAW,CAACW,CAAC,EAAE,CAAC;IACzB,IAAID,QAAQ,IAAI,CAAC,EAAE;MACfD,UAAU,CAACW,IAAI,CAACN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MACvC;IACJ;IACA,IAAIE,iBAAiB,GAAGnB,IAAI,CAACC,GAAG,CAACO,QAAQ,EAAEH,oBAAoB,GAAG,CAAC,CAAC,GAAG,CAAC;IACxE,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAID,iBAAiB,EAAEC,CAAC,EAAE,EAAE;MACzC,IAAIC,CAAC,GAAGD,CAAC,GAAGD,iBAAiB;MAC7BtC,cAAc,CAAC6B,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEK,CAAC,EAAEzB,OAAO,CAAC;MAC1Cf,cAAc,CAAC8B,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEI,CAAC,EAAExB,OAAO,CAAC;MAC1Ca,EAAE,GAAGd,OAAO,CAAC,CAAC,CAAC;MACfe,EAAE,GAAGd,OAAO,CAAC,CAAC,CAAC;MACfU,UAAU,CAACW,IAAI,CAACtB,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAEa,EAAE,EAAEC,EAAE,CAAC;MACvEC,EAAE,GAAGhB,OAAO,CAAC,CAAC,CAAC;MACfiB,EAAE,GAAGhB,OAAO,CAAC,CAAC,CAAC;MACfiB,EAAE,GAAGlB,OAAO,CAAC,CAAC,CAAC;MACfmB,EAAE,GAAGlB,OAAO,CAAC,CAAC,CAAC;IACnB;IACAW,QAAQ,IAAIW,iBAAiB,GAAG,CAAC;EACrC;EACA,OAAOrB,WAAW,KAAKP,QAAQ,GAAG,CAACgB,UAAU,EAAEf,QAAQ,CAAC,GAAG,CAACD,QAAQ,EAAEgB,UAAU,CAAC;AACrF;AACA,SAASe,aAAaA,CAACC,kBAAkB,EAAEC,YAAY,EAAE;EACrD,IAAIC,GAAG,GAAGF,kBAAkB,CAAC7B,MAAM;EACnC,IAAIgC,KAAK,GAAGH,kBAAkB,CAACE,GAAG,GAAG,CAAC,CAAC;EACvC,IAAIE,KAAK,GAAGJ,kBAAkB,CAACE,GAAG,GAAG,CAAC,CAAC;EACvC,IAAIlB,UAAU,GAAG,EAAE;EACnB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,YAAY,CAAC9B,MAAM,GAAG;IACtCa,UAAU,CAACE,CAAC,EAAE,CAAC,GAAGiB,KAAK;IACvBnB,UAAU,CAACE,CAAC,EAAE,CAAC,GAAGkB,KAAK;EAC3B;EACA,OAAOpB,UAAU;AACrB;AACA,OAAO,SAASqB,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC9C,IAAIC,EAAE;EACN,IAAIC,YAAY;EAChB,IAAIC,YAAY;EAChB,IAAIC,SAAS,GAAG,EAAE;EAClB,IAAIC,SAAS,GAAG,EAAE;EAClB,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,CAACoC,GAAG,CAACP,MAAM,CAACnC,MAAM,EAAEoC,MAAM,CAACpC,MAAM,CAAC,EAAEe,CAAC,EAAE,EAAE;IAC7D,IAAIlB,QAAQ,GAAGsC,MAAM,CAACpB,CAAC,CAAC;IACxB,IAAIjB,QAAQ,GAAGsC,MAAM,CAACrB,CAAC,CAAC;IACxB,IAAI4B,WAAW,GAAG,KAAK,CAAC;IACxB,IAAIC,WAAW,GAAG,KAAK,CAAC;IACxB,IAAI,CAAC/C,QAAQ,EAAE;MACX8C,WAAW,GAAGf,aAAa,CAACU,YAAY,IAAIxC,QAAQ,EAAEA,QAAQ,CAAC;MAC/D8C,WAAW,GAAG9C,QAAQ;IAC1B,CAAC,MACI,IAAI,CAACA,QAAQ,EAAE;MAChB8C,WAAW,GAAGhB,aAAa,CAACW,YAAY,IAAI1C,QAAQ,EAAEA,QAAQ,CAAC;MAC/D8C,WAAW,GAAG9C,QAAQ;IAC1B,CAAC,MACI;MACDwC,EAAE,GAAGzC,YAAY,CAACC,QAAQ,EAAEC,QAAQ,CAAC,EAAE6C,WAAW,GAAGN,EAAE,CAAC,CAAC,CAAC,EAAEO,WAAW,GAAGP,EAAE,CAAC,CAAC,CAAC;MAC/EC,YAAY,GAAGK,WAAW;MAC1BJ,YAAY,GAAGK,WAAW;IAC9B;IACAJ,SAAS,CAAChB,IAAI,CAACmB,WAAW,CAAC;IAC3BF,SAAS,CAACjB,IAAI,CAACoB,WAAW,CAAC;EAC/B;EACA,OAAO,CAACJ,SAAS,EAAEC,SAAS,CAAC;AACjC;AACA,OAAO,SAASI,QAAQA,CAACC,KAAK,EAAE;EAC5B,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIlB,GAAG,GAAGe,KAAK,CAAC9C,MAAM;EACtB,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEmC,CAAC,GAAGnB,GAAG,GAAG,CAAC,EAAEhB,CAAC,GAAGgB,GAAG,EAAEmB,CAAC,GAAGnC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IACjD,IAAIC,EAAE,GAAG8B,KAAK,CAACI,CAAC,CAAC;IACjB,IAAIjC,EAAE,GAAG6B,KAAK,CAACI,CAAC,GAAG,CAAC,CAAC;IACrB,IAAIhC,EAAE,GAAG4B,KAAK,CAAC/B,CAAC,CAAC;IACjB,IAAII,EAAE,GAAG2B,KAAK,CAAC/B,CAAC,GAAG,CAAC,CAAC;IACrB,IAAIoC,CAAC,GAAGnC,EAAE,GAAGG,EAAE,GAAGD,EAAE,GAAGD,EAAE;IACzB8B,UAAU,IAAII,CAAC;IACfH,EAAE,IAAI,CAAChC,EAAE,GAAGE,EAAE,IAAIiC,CAAC;IACnBF,EAAE,IAAI,CAAChC,EAAE,GAAGE,EAAE,IAAIgC,CAAC;EACvB;EACA,IAAIJ,UAAU,KAAK,CAAC,EAAE;IAClB,OAAO,CAACD,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACzC;EACA,OAAO,CAACE,EAAE,GAAGD,UAAU,GAAG,CAAC,EAAEE,EAAE,GAAGF,UAAU,GAAG,CAAC,EAAEA,UAAU,CAAC;AACjE;AACA,SAASK,kBAAkBA,CAACC,cAAc,EAAEC,YAAY,EAAEC,MAAM,EAAEC,IAAI,EAAE;EACpE,IAAIC,WAAW,GAAG,CAACJ,cAAc,CAACrD,MAAM,GAAG,CAAC,IAAI,CAAC;EACjD,IAAI0D,SAAS,GAAGC,QAAQ;EACxB,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAI7B,GAAG,GAAGsB,cAAc,CAACrD,MAAM;EAC/B,IAAIC,IAAI,GAAG8B,GAAG,GAAG,CAAC;EAClB,KAAK,IAAI8B,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGJ,WAAW,EAAEI,MAAM,EAAE,EAAE;IACjD,IAAIC,YAAY,GAAGD,MAAM,GAAG,CAAC;IAC7B,IAAIE,KAAK,GAAG,CAAC;IACb,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,GAAG,EAAEL,CAAC,IAAI,CAAC,EAAE;MAC7B,IAAIsC,GAAG,GAAGtC,CAAC,KAAK,CAAC,GAAGoC,YAAY,GAAI,CAACA,YAAY,GAAGpC,CAAC,GAAG,CAAC,IAAIzB,IAAI,GAAG,CAAE;MACtE,IAAIe,EAAE,GAAGqC,cAAc,CAACW,GAAG,CAAC,GAAGT,MAAM,CAAC,CAAC,CAAC;MACxC,IAAItC,EAAE,GAAGoC,cAAc,CAACW,GAAG,GAAG,CAAC,CAAC,GAAGT,MAAM,CAAC,CAAC,CAAC;MAC5C,IAAIrC,EAAE,GAAGoC,YAAY,CAAC5B,CAAC,CAAC,GAAG8B,IAAI,CAAC,CAAC,CAAC;MAClC,IAAIrC,EAAE,GAAGmC,YAAY,CAAC5B,CAAC,GAAG,CAAC,CAAC,GAAG8B,IAAI,CAAC,CAAC,CAAC;MACtC,IAAIS,EAAE,GAAG/C,EAAE,GAAGF,EAAE;MAChB,IAAIkD,EAAE,GAAG/C,EAAE,GAAGF,EAAE;MAChB8C,KAAK,IAAIE,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAC9B;IACA,IAAIH,KAAK,GAAGL,SAAS,EAAE;MACnBA,SAAS,GAAGK,KAAK;MACjBH,UAAU,GAAGC,MAAM;IACvB;EACJ;EACA,OAAOD,UAAU;AACrB;AACA,SAASO,OAAOA,CAACrB,KAAK,EAAE;EACpB,IAAIsB,MAAM,GAAG,EAAE;EACf,IAAIrC,GAAG,GAAGe,KAAK,CAAC9C,MAAM;EACtB,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,IAAI,CAAC,EAAE;IAC7BqD,MAAM,CAACrD,CAAC,CAAC,GAAG+B,KAAK,CAACf,GAAG,GAAGhB,CAAC,GAAG,CAAC,CAAC;IAC9BqD,MAAM,CAACrD,CAAC,GAAG,CAAC,CAAC,GAAG+B,KAAK,CAACf,GAAG,GAAGhB,CAAC,GAAG,CAAC,CAAC;EACtC;EACA,OAAOqD,MAAM;AACjB;AACA,SAASC,wBAAwBA,CAACC,OAAO,EAAEC,KAAK,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAE;EACtF,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,gBAAgB;EACpB,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuD,OAAO,CAACtE,MAAM,EAAEe,CAAC,EAAE,EAAE;IACrC,IAAI6D,iBAAiB,GAAGN,OAAO,CAACvD,CAAC,CAAC;IAClC,IAAI8D,eAAe,GAAGN,KAAK,CAACxD,CAAC,CAAC;IAC9B,IAAIwC,MAAM,GAAGV,QAAQ,CAAC+B,iBAAiB,CAAC;IACxC,IAAIpB,IAAI,GAAGX,QAAQ,CAACgC,eAAe,CAAC;IACpC,IAAIF,gBAAgB,IAAI,IAAI,EAAE;MAC1BA,gBAAgB,GAAGpB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;IACpD;IACA,IAAIsB,oBAAoB,GAAG,EAAE;IAC7B,IAAIC,kBAAkB,GAAG,EAAE;IAC3B,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAItB,SAAS,GAAGC,QAAQ;IACxB,IAAIsB,MAAM,GAAG,EAAE;IACf,IAAIlD,GAAG,GAAG6C,iBAAiB,CAAC5E,MAAM;IAClC,IAAI2E,gBAAgB,EAAE;MAClBC,iBAAiB,GAAGT,OAAO,CAACS,iBAAiB,CAAC;IAClD;IACA,IAAIf,MAAM,GAAGT,kBAAkB,CAACwB,iBAAiB,EAAEC,eAAe,EAAEtB,MAAM,EAAEC,IAAI,CAAC,GAAG,CAAC;IACrF,IAAIvD,IAAI,GAAG8B,GAAG,GAAG,CAAC;IAClB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzB,IAAI,EAAEyB,CAAC,IAAI,CAAC,EAAE;MAC9B,IAAIsC,GAAG,GAAG,CAACH,MAAM,GAAGnC,CAAC,IAAIzB,IAAI,GAAG,CAAC;MACjC6E,oBAAoB,CAACpD,CAAC,GAAG,CAAC,CAAC,GAAGkD,iBAAiB,CAACZ,GAAG,CAAC,GAAGT,MAAM,CAAC,CAAC,CAAC;MAChEuB,oBAAoB,CAACpD,CAAC,GAAG,CAAC,CAAC,GAAGkD,iBAAiB,CAACZ,GAAG,GAAG,CAAC,CAAC,GAAGT,MAAM,CAAC,CAAC,CAAC;IACxE;IACAuB,oBAAoB,CAAC,CAAC,CAAC,GAAGF,iBAAiB,CAACf,MAAM,CAAC,GAAGN,MAAM,CAAC,CAAC,CAAC;IAC/DuB,oBAAoB,CAAC,CAAC,CAAC,GAAGF,iBAAiB,CAACf,MAAM,GAAG,CAAC,CAAC,GAAGN,MAAM,CAAC,CAAC,CAAC;IACnE,IAAIiB,oBAAoB,GAAG,CAAC,EAAE;MAC1B,IAAIU,IAAI,GAAGT,gBAAgB,GAAGD,oBAAoB;MAClD,KAAK,IAAIW,KAAK,GAAG,CAACV,gBAAgB,GAAG,CAAC,EAAEU,KAAK,IAAIV,gBAAgB,GAAG,CAAC,EAAEU,KAAK,IAAID,IAAI,EAAE;QAClF,IAAIE,EAAE,GAAG9E,IAAI,CAAC+E,GAAG,CAACF,KAAK,CAAC;QACxB,IAAIG,EAAE,GAAGhF,IAAI,CAACiF,GAAG,CAACJ,KAAK,CAAC;QACxB,IAAIpB,KAAK,GAAG,CAAC;QACb,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkD,iBAAiB,CAAC5E,MAAM,EAAE0B,CAAC,IAAI,CAAC,EAAE;UAClD,IAAIV,EAAE,GAAG8D,oBAAoB,CAACpD,CAAC,CAAC;UAChC,IAAIT,EAAE,GAAG6D,oBAAoB,CAACpD,CAAC,GAAG,CAAC,CAAC;UACpC,IAAIR,EAAE,GAAG2D,eAAe,CAACnD,CAAC,CAAC,GAAG8B,IAAI,CAAC,CAAC,CAAC;UACrC,IAAIrC,EAAE,GAAG0D,eAAe,CAACnD,CAAC,GAAG,CAAC,CAAC,GAAG8B,IAAI,CAAC,CAAC,CAAC;UACzC,IAAIgC,KAAK,GAAGtE,EAAE,GAAGoE,EAAE,GAAGnE,EAAE,GAAGiE,EAAE;UAC7B,IAAIK,KAAK,GAAGvE,EAAE,GAAGkE,EAAE,GAAGjE,EAAE,GAAGmE,EAAE;UAC7BL,MAAM,CAACvD,CAAC,CAAC,GAAG8D,KAAK;UACjBP,MAAM,CAACvD,CAAC,GAAG,CAAC,CAAC,GAAG+D,KAAK;UACrB,IAAIxB,EAAE,GAAGuB,KAAK,GAAGxE,EAAE;UACnB,IAAIkD,EAAE,GAAGuB,KAAK,GAAGxE,EAAE;UACnB8C,KAAK,IAAIE,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;QAC9B;QACA,IAAIH,KAAK,GAAGL,SAAS,EAAE;UACnBA,SAAS,GAAGK,KAAK;UACjBiB,SAAS,GAAGG,KAAK;UACjB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACjF,MAAM,EAAE0F,CAAC,EAAE,EAAE;YACpCX,kBAAkB,CAACW,CAAC,CAAC,GAAGT,MAAM,CAACS,CAAC,CAAC;UACrC;QACJ;MACJ;IACJ,CAAC,MACI;MACD,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG5D,GAAG,EAAE4D,GAAG,IAAI,CAAC,EAAE;QACnCZ,kBAAkB,CAACY,GAAG,CAAC,GAAGd,eAAe,CAACc,GAAG,CAAC,GAAGnC,IAAI,CAAC,CAAC,CAAC;QACxDuB,kBAAkB,CAACY,GAAG,GAAG,CAAC,CAAC,GAAGd,eAAe,CAACc,GAAG,GAAG,CAAC,CAAC,GAAGnC,IAAI,CAAC,CAAC,CAAC;MACpE;IACJ;IACAkB,MAAM,CAAClD,IAAI,CAAC;MACRoE,IAAI,EAAEd,oBAAoB;MAC1Be,EAAE,EAAEd,kBAAkB;MACtBxB,MAAM,EAAEA,MAAM;MACdC,IAAI,EAAEA,IAAI;MACVsC,QAAQ,EAAE,CAACd;IACf,CAAC,CAAC;EACN;EACA,OAAON,MAAM;AACjB;AACA,OAAO,SAASqB,iBAAiBA,CAACC,IAAI,EAAE;EACpC,OAAOA,IAAI,CAACC,mBAAmB;AACnC;AACA,OAAO,SAASC,UAAUA,CAACC,EAAE,EAAE;EAC3B,OAAOA,EAAE,CAACC,QAAQ,IAAI,CAAC;AAC3B;AACA,IAAIC,mBAAmB,GAAG,cAAc;AACxC,SAASC,mBAAmBA,CAACC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAE;EACrD,IAAIC,eAAe,GAAGL,mBAAmB,GAAGG,UAAU;EACtD,IAAIG,cAAc,GAAGJ,GAAG,CAACG,eAAe,CAAC,IAAIH,GAAG,CAACC,UAAU,CAAC;EAC5D,IAAI,CAACD,GAAG,CAACG,eAAe,CAAC,EAAE;IACvBH,GAAG,CAACG,eAAe,CAAC,GAAGH,GAAG,CAACC,UAAU,CAAC;EAC1C;EACA,IAAII,OAAO,GAAGH,SAAS,CAACG,OAAO;EAC/B,IAAIC,KAAK,GAAGJ,SAAS,CAACI,KAAK;EAC3B,IAAIC,MAAM,GAAGL,SAAS,CAACK,MAAM;EAC7BP,GAAG,CAACC,UAAU,CAAC,GAAG,YAAY;IAC1B,IAAIO,IAAI,GAAGC,SAAS;IACpB,IAAIC,GAAG;IACPH,MAAM,IAAIA,MAAM,CAACI,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IAClC,IAAIH,OAAO,EAAE;MACTK,GAAG,GAAGL,OAAO,CAACM,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IACnC,CAAC,MACI;MACDE,GAAG,GAAGN,cAAc,CAACO,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IAC1C;IACAF,KAAK,IAAIA,KAAK,CAACK,KAAK,CAAC,IAAI,EAAEH,IAAI,CAAC;IAChC,OAAOE,GAAG;EACd,CAAC;AACL;AACA,SAASE,aAAaA,CAACZ,GAAG,EAAEC,UAAU,EAAE;EACpC,IAAIE,eAAe,GAAGL,mBAAmB,GAAGG,UAAU;EACtD,IAAID,GAAG,CAACG,eAAe,CAAC,EAAE;IACtBH,GAAG,CAACC,UAAU,CAAC,GAAGD,GAAG,CAACG,eAAe,CAAC;IACtCH,GAAG,CAACG,eAAe,CAAC,GAAG,IAAI;EAC/B;AACJ;AACA,SAASU,uBAAuBA,CAACC,YAAY,EAAEC,EAAE,EAAE;EAC/C,KAAK,IAAIvG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsG,YAAY,CAACrH,MAAM,EAAEe,CAAC,EAAE,EAAE;IAC1C,IAAIwG,UAAU,GAAGF,YAAY,CAACtG,CAAC,CAAC;IAChC,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6F,UAAU,CAACvH,MAAM,GAAG;MACpC,IAAIwH,CAAC,GAAGD,UAAU,CAAC7F,CAAC,CAAC;MACrB,IAAI+F,CAAC,GAAGF,UAAU,CAAC7F,CAAC,GAAG,CAAC,CAAC;MACzB6F,UAAU,CAAC7F,CAAC,EAAE,CAAC,GAAG4F,EAAE,CAAC,CAAC,CAAC,GAAGE,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAGG,CAAC,GAAGH,EAAE,CAAC,CAAC,CAAC;MAC/CC,UAAU,CAAC7F,CAAC,EAAE,CAAC,GAAG4F,EAAE,CAAC,CAAC,CAAC,GAAGE,CAAC,GAAGF,EAAE,CAAC,CAAC,CAAC,GAAGG,CAAC,GAAGH,EAAE,CAAC,CAAC,CAAC;IACnD;EACJ;AACJ;AACA,SAASI,gBAAgBA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EACxC,IAAIC,aAAa,GAAGF,QAAQ,CAACG,mBAAmB,CAAC,CAAC;EAClD,IAAIC,WAAW,GAAGH,MAAM,CAACE,mBAAmB,CAAC,CAAC;EAC9C,IAAIzF,EAAE,GAAGH,iBAAiB,CAACvC,kBAAkB,CAACkI,aAAa,CAAC,EAAElI,kBAAkB,CAACoI,WAAW,CAAC,CAAC;IAAEC,gBAAgB,GAAG3F,EAAE,CAAC,CAAC,CAAC;IAAE4F,cAAc,GAAG5F,EAAE,CAAC,CAAC,CAAC;EAChJ,IAAI6F,iBAAiB,GAAGP,QAAQ,CAACQ,oBAAoB,CAAC,CAAC;EACvD,IAAIC,eAAe,GAAGR,MAAM,CAACO,oBAAoB,CAAC,CAAC;EACnD,SAASE,uBAAuBA,CAAA,EAAG;IAC/B,IAAI,CAACC,SAAS,GAAG,IAAI;EACzB;EACAJ,iBAAiB,IAAId,uBAAuB,CAACY,gBAAgB,EAAEE,iBAAiB,CAAC;EACjFE,eAAe,IAAIhB,uBAAuB,CAACa,cAAc,EAAEG,eAAe,CAAC;EAC3E9B,mBAAmB,CAACsB,MAAM,EAAE,iBAAiB,EAAE;IAAEhB,OAAO,EAAEyB;EAAwB,CAAC,CAAC;EACpFT,MAAM,CAACU,SAAS,GAAG,IAAI;EACvB,IAAIC,YAAY,GAAGlE,wBAAwB,CAAC2D,gBAAgB,EAAEC,cAAc,EAAE,EAAE,EAAE3H,IAAI,CAACkI,EAAE,CAAC;EAC1F,IAAIvD,MAAM,GAAG,EAAE;EACfqB,mBAAmB,CAACsB,MAAM,EAAE,WAAW,EAAE;IAAEhB,OAAO,EAAE,SAAAA,CAAUZ,IAAI,EAAE;MAC5D,IAAIyC,CAAC,GAAGb,MAAM,CAACxB,QAAQ;MACvB,IAAIsC,IAAI,GAAG,CAAC,GAAGD,CAAC;MAChB,IAAIE,KAAK,GAAG,EAAE;MACd,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwH,YAAY,CAACvI,MAAM,EAAEe,CAAC,EAAE,EAAE;QAC1C,IAAI6H,IAAI,GAAGL,YAAY,CAACxH,CAAC,CAAC;QAC1B,IAAI6E,IAAI,GAAGgD,IAAI,CAAChD,IAAI;QACpB,IAAIC,EAAE,GAAG+C,IAAI,CAAC/C,EAAE;QAChB,IAAIV,KAAK,GAAGyD,IAAI,CAAC9C,QAAQ,GAAG2C,CAAC;QAC7B,IAAIlF,MAAM,GAAGqF,IAAI,CAACrF,MAAM;QACxB,IAAIC,IAAI,GAAGoF,IAAI,CAACpF,IAAI;QACpB,IAAI4B,EAAE,GAAG9E,IAAI,CAAC+E,GAAG,CAACF,KAAK,CAAC;QACxB,IAAIG,EAAE,GAAGhF,IAAI,CAACiF,GAAG,CAACJ,KAAK,CAAC;QACxB5F,IAAI,CAACoJ,KAAK,EAAEpF,MAAM,EAAEC,IAAI,EAAEiF,CAAC,CAAC;QAC5B,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,IAAI,CAAC5F,MAAM,EAAE0F,CAAC,IAAI,CAAC,EAAE;UACrC,IAAImD,IAAI,GAAGjD,IAAI,CAACF,CAAC,CAAC;UAClB,IAAIoD,IAAI,GAAGlD,IAAI,CAACF,CAAC,GAAG,CAAC,CAAC;UACtB,IAAIxE,EAAE,GAAG2E,EAAE,CAACH,CAAC,CAAC;UACd,IAAIvE,EAAE,GAAG0E,EAAE,CAACH,CAAC,GAAG,CAAC,CAAC;UAClB,IAAI8B,CAAC,GAAGqB,IAAI,GAAGH,IAAI,GAAGxH,EAAE,GAAGuH,CAAC;UAC5B,IAAIhB,CAAC,GAAGqB,IAAI,GAAGJ,IAAI,GAAGvH,EAAE,GAAGsH,CAAC;UAC5BxD,MAAM,CAACS,CAAC,CAAC,GAAI8B,CAAC,GAAGlC,EAAE,GAAGmC,CAAC,GAAGrC,EAAE,GAAIuD,KAAK,CAAC,CAAC,CAAC;UACxC1D,MAAM,CAACS,CAAC,GAAG,CAAC,CAAC,GAAI8B,CAAC,GAAGpC,EAAE,GAAGqC,CAAC,GAAGnC,EAAE,GAAIqD,KAAK,CAAC,CAAC,CAAC;QAChD;QACA,IAAI3H,EAAE,GAAGiE,MAAM,CAAC,CAAC,CAAC;QAClB,IAAIhE,EAAE,GAAGgE,MAAM,CAAC,CAAC,CAAC;QAClBe,IAAI,CAAC+C,MAAM,CAAC/H,EAAE,EAAEC,EAAE,CAAC;QACnB,KAAK,IAAIyE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,IAAI,CAAC5F,MAAM,GAAG;UAC9B,IAAIkB,EAAE,GAAG+D,MAAM,CAACS,CAAC,EAAE,CAAC;UACpB,IAAIvE,EAAE,GAAG8D,MAAM,CAACS,CAAC,EAAE,CAAC;UACpB,IAAItE,EAAE,GAAG6D,MAAM,CAACS,CAAC,EAAE,CAAC;UACpB,IAAIrE,EAAE,GAAG4D,MAAM,CAACS,CAAC,EAAE,CAAC;UACpB,IAAIpE,EAAE,GAAG2D,MAAM,CAACS,CAAC,EAAE,CAAC;UACpB,IAAInE,EAAE,GAAG0D,MAAM,CAACS,CAAC,EAAE,CAAC;UACpB,IAAI1E,EAAE,KAAKE,EAAE,IAAID,EAAE,KAAKE,EAAE,IAAIC,EAAE,KAAKE,EAAE,IAAID,EAAE,KAAKE,EAAE,EAAE;YAClDyE,IAAI,CAACgD,MAAM,CAAC1H,EAAE,EAAEC,EAAE,CAAC;UACvB,CAAC,MACI;YACDyE,IAAI,CAACiD,aAAa,CAAC/H,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;UAC9C;UACAP,EAAE,GAAGM,EAAE;UACPL,EAAE,GAAGM,EAAE;QACX;MACJ;IACJ;EAAE,CAAC,CAAC;AACZ;AACA,OAAO,SAAS2H,SAASA,CAACvB,QAAQ,EAAEC,MAAM,EAAEuB,aAAa,EAAE;EACvD,IAAI,CAACxB,QAAQ,IAAI,CAACC,MAAM,EAAE;IACtB,OAAOA,MAAM;EACjB;EACA,IAAIwB,OAAO,GAAGD,aAAa,CAACE,IAAI;EAChC,IAAIC,SAAS,GAAGH,aAAa,CAACI,MAAM;EACpC7B,gBAAgB,CAACC,QAAQ,EAAEC,MAAM,CAAC;EAClCA,MAAM,CAACxB,QAAQ,GAAG,CAAC;EACnB,SAASoD,aAAaA,CAAA,EAAG;IACrBrC,aAAa,CAACS,MAAM,EAAE,WAAW,CAAC;IAClCT,aAAa,CAACS,MAAM,EAAE,iBAAiB,CAAC;IACxCA,MAAM,CAACxB,QAAQ,GAAG,CAAC,CAAC;IACpBwB,MAAM,CAAC6B,eAAe,CAAC,CAAC;IACxB7B,MAAM,CAAC8B,UAAU,CAAC,CAAC;EACvB;EACA9B,MAAM,CAAC+B,SAAS,CAAC;IACbvD,QAAQ,EAAE;EACd,CAAC,EAAE/G,QAAQ,CAAC;IACRkK,MAAM,EAAE,SAAAA,CAAU5H,CAAC,EAAE;MACjBiG,MAAM,CAAC8B,UAAU,CAAC,CAAC;MACnBJ,SAAS,IAAIA,SAAS,CAAC3H,CAAC,CAAC;IAC7B,CAAC;IACD0H,IAAI,EAAE,SAAAA,CAAA,EAAY;MACdG,aAAa,CAAC,CAAC;MACfJ,OAAO,IAAIA,OAAO,CAAC,CAAC;IACxB;EACJ,CAAC,EAAED,aAAa,CAAC,CAAC;EAClB,OAAOvB,MAAM;AACjB;AACA,SAASgC,OAAOA,CAACpC,CAAC,EAAEC,CAAC,EAAEoC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC3C,IAAIC,IAAI,GAAG,EAAE;EACbzC,CAAC,GAAIuC,IAAI,KAAKF,IAAI,GAAI,CAAC,GAAGvJ,IAAI,CAAC4J,KAAK,CAAC,KAAK,IAAI1C,CAAC,GAAGqC,IAAI,CAAC,IAAIE,IAAI,GAAGF,IAAI,CAAC,CAAC;EACxEpC,CAAC,GAAIuC,IAAI,KAAKF,IAAI,GAAI,CAAC,GAAGxJ,IAAI,CAAC4J,KAAK,CAAC,KAAK,IAAIzC,CAAC,GAAGqC,IAAI,CAAC,IAAIE,IAAI,GAAGF,IAAI,CAAC,CAAC;EACxE,IAAIK,CAAC,GAAG,CAAC;EACT,IAAIC,GAAG;EACP,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,IAAIJ,IAAI,IAAI,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IACzC,IAAIC,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IACV,IAAI,CAAC/C,CAAC,GAAG6C,CAAC,IAAI,CAAC,EAAE;MACbC,EAAE,GAAG,CAAC;IACV;IACA,IAAI,CAAC7C,CAAC,GAAG4C,CAAC,IAAI,CAAC,EAAE;MACbE,EAAE,GAAG,CAAC;IACV;IACAJ,CAAC,IAAIE,CAAC,GAAGA,CAAC,IAAK,CAAC,GAAGC,EAAE,GAAIC,EAAE,CAAC;IAC5B,IAAIA,EAAE,KAAK,CAAC,EAAE;MACV,IAAID,EAAE,KAAK,CAAC,EAAE;QACV9C,CAAC,GAAG6C,CAAC,GAAG,CAAC,GAAG7C,CAAC;QACbC,CAAC,GAAG4C,CAAC,GAAG,CAAC,GAAG5C,CAAC;MACjB;MACA2C,GAAG,GAAG5C,CAAC;MACPA,CAAC,GAAGC,CAAC;MACLA,CAAC,GAAG2C,GAAG;IACX;EACJ;EACA,OAAOD,CAAC;AACZ;AACA,SAASK,SAASA,CAACC,QAAQ,EAAE;EACzB,IAAIC,IAAI,GAAG/G,QAAQ;EACnB,IAAIgH,IAAI,GAAGhH,QAAQ;EACnB,IAAIiH,IAAI,GAAG,CAACjH,QAAQ;EACpB,IAAIkH,IAAI,GAAG,CAAClH,QAAQ;EACpB,IAAImH,GAAG,GAAGxL,GAAG,CAACmL,QAAQ,EAAE,UAAUzE,IAAI,EAAE;IACpC,IAAI+E,IAAI,GAAG/E,IAAI,CAACgF,eAAe,CAAC,CAAC;IACjC,IAAItF,CAAC,GAAGM,IAAI,CAACmC,oBAAoB,CAAC,CAAC;IACnC,IAAIX,CAAC,GAAGuD,IAAI,CAACvD,CAAC,GAAGuD,IAAI,CAACE,KAAK,GAAG,CAAC,IAAIvF,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAChD,IAAI+B,CAAC,GAAGsD,IAAI,CAACtD,CAAC,GAAGsD,IAAI,CAACG,MAAM,GAAG,CAAC,IAAIxF,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IACjDgF,IAAI,GAAGpK,IAAI,CAACC,GAAG,CAACiH,CAAC,EAAEkD,IAAI,CAAC;IACxBC,IAAI,GAAGrK,IAAI,CAACC,GAAG,CAACkH,CAAC,EAAEkD,IAAI,CAAC;IACxBC,IAAI,GAAGtK,IAAI,CAACoC,GAAG,CAAC8E,CAAC,EAAEoD,IAAI,CAAC;IACxBC,IAAI,GAAGvK,IAAI,CAACoC,GAAG,CAAC+E,CAAC,EAAEoD,IAAI,CAAC;IACxB,OAAO,CAACrD,CAAC,EAAEC,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,IAAI0D,KAAK,GAAG7L,GAAG,CAACwL,GAAG,EAAE,UAAUM,EAAE,EAAEpH,GAAG,EAAE;IACpC,OAAO;MACHoH,EAAE,EAAEA,EAAE;MACNC,CAAC,EAAEzB,OAAO,CAACwB,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,EAAEV,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;MAChD7E,IAAI,EAAEyE,QAAQ,CAACzG,GAAG;IACtB,CAAC;EACL,CAAC,CAAC;EACF,OAAOmH,KAAK,CAACG,IAAI,CAAC,UAAUnI,CAAC,EAAEoI,CAAC,EAAE;IAAE,OAAOpI,CAAC,CAACkI,CAAC,GAAGE,CAAC,CAACF,CAAC;EAAE,CAAC,CAAC,CAAC/L,GAAG,CAAC,UAAUsJ,IAAI,EAAE;IAAE,OAAOA,IAAI,CAAC5C,IAAI;EAAE,CAAC,CAAC;AACvG;AACA;AACA,SAASwF,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,OAAO/L,KAAK,CAAC+L,KAAK,CAACzF,IAAI,EAAEyF,KAAK,CAACC,KAAK,CAAC;AACzC;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB,OAAO;IACHC,eAAe,EAAE,EAAE;IACnBC,aAAa,EAAE,EAAE;IACjBH,KAAK,EAAE;EACX,CAAC;AACL;AACA,OAAO,SAASI,YAAYA,CAACC,QAAQ,EAAEnE,MAAM,EAAEuB,aAAa,EAAE;EAC1D,IAAI6C,YAAY,GAAG,EAAE;EACrB,SAASC,WAAWA,CAACF,QAAQ,EAAE;IAC3B,KAAK,IAAIhL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgL,QAAQ,CAAC/L,MAAM,EAAEe,CAAC,EAAE,EAAE;MACtC,IAAI6E,IAAI,GAAGmG,QAAQ,CAAChL,CAAC,CAAC;MACtB,IAAIgF,iBAAiB,CAACH,IAAI,CAAC,EAAE;QACzBqG,WAAW,CAACrG,IAAI,CAACsG,WAAW,CAAC,CAAC,CAAC;MACnC,CAAC,MACI,IAAItG,IAAI,YAAYxG,IAAI,EAAE;QAC3B4M,YAAY,CAACxK,IAAI,CAACoE,IAAI,CAAC;MAC3B;IACJ;EACJ;EACAqG,WAAW,CAACF,QAAQ,CAAC;EACrB,IAAII,aAAa,GAAGH,YAAY,CAAChM,MAAM;EACvC,IAAI,CAACmM,aAAa,EAAE;IAChB,OAAOR,iBAAiB,CAAC,CAAC;EAC9B;EACA,IAAIS,UAAU,GAAGjD,aAAa,CAACiD,UAAU,IAAIZ,iBAAiB;EAC9D,IAAIa,aAAa,GAAGD,UAAU,CAAC;IAC3BpG,IAAI,EAAE4B,MAAM;IAAE8D,KAAK,EAAES;EACzB,CAAC,CAAC;EACF,IAAIE,aAAa,CAACrM,MAAM,KAAKmM,aAAa,EAAE;IACxCG,OAAO,CAACC,KAAK,CAAC,2CAA2C,CAAC;IAC1D,OAAOZ,iBAAiB,CAAC,CAAC;EAC9B;EACAK,YAAY,GAAGxB,SAAS,CAACwB,YAAY,CAAC;EACtCK,aAAa,GAAG7B,SAAS,CAAC6B,aAAa,CAAC;EACxC,IAAIjD,OAAO,GAAGD,aAAa,CAACE,IAAI;EAChC,IAAIC,SAAS,GAAGH,aAAa,CAACI,MAAM;EACpC,IAAIiD,eAAe,GAAGrD,aAAa,CAACqD,eAAe;EACnD,IAAIC,iBAAiB,GAAG,IAAIhN,aAAa,CAAC,CAAC;EAC3C,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoL,aAAa,EAAEpL,CAAC,EAAE,EAAE;IACpC,IAAI6E,IAAI,GAAGoG,YAAY,CAACjL,CAAC,CAAC;IAC1B,IAAI8E,EAAE,GAAGwG,aAAa,CAACtL,CAAC,CAAC;IACzB8E,EAAE,CAAC6G,MAAM,GAAG9E,MAAM;IAClB/B,EAAE,CAAC8G,aAAa,CAACF,iBAAiB,CAAC;IACnC,IAAI,CAACD,eAAe,EAAE;MAClB9E,gBAAgB,CAAC9B,IAAI,EAAEC,EAAE,CAAC;IAC9B;EACJ;EACA+B,MAAM,CAAC3B,mBAAmB,GAAG,IAAI;EACjC2B,MAAM,CAACsE,WAAW,GAAG,YAAY;IAC7B,OAAOG,aAAa;EACxB,CAAC;EACD,SAASO,oBAAoBA,CAACC,EAAE,EAAE;IAC9B,KAAK,IAAI9L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsL,aAAa,CAACrM,MAAM,EAAEe,CAAC,EAAE,EAAE;MAC3CsL,aAAa,CAACtL,CAAC,CAAC,CAAC+L,WAAW,CAACD,EAAE,CAAC;IACpC;EACJ;EACAvG,mBAAmB,CAACsB,MAAM,EAAE,aAAa,EAAE;IACvCf,KAAK,EAAE,SAAAA,CAAUgG,EAAE,EAAE;MACjBD,oBAAoB,CAACC,EAAE,CAAC;IAC5B;EACJ,CAAC,CAAC;EACFvG,mBAAmB,CAACsB,MAAM,EAAE,kBAAkB,EAAE;IAC5Cf,KAAK,EAAE,SAAAA,CAAUgG,EAAE,EAAE;MACjB,KAAK,IAAI9L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsL,aAAa,CAACrM,MAAM,EAAEe,CAAC,EAAE,EAAE;QAC3CsL,aAAa,CAACtL,CAAC,CAAC,CAACgM,gBAAgB,CAACF,EAAE,CAAC;MACzC;IACJ;EACJ,CAAC,CAAC;EACF,SAASrD,aAAaA,CAAA,EAAG;IACrB5B,MAAM,CAAC3B,mBAAmB,GAAG,KAAK;IAClC2B,MAAM,CAACxB,QAAQ,GAAG,CAAC,CAAC;IACpBwB,MAAM,CAACsE,WAAW,GAAG,IAAI;IACzB/E,aAAa,CAACS,MAAM,EAAE,aAAa,CAAC;IACpCT,aAAa,CAACS,MAAM,EAAE,kBAAkB,CAAC;EAC7C;EACA,IAAIoF,KAAK,GAAGX,aAAa,CAACrM,MAAM;EAChC,IAAIwM,eAAe,EAAE;IACjB,IAAIS,WAAW,GAAGD,KAAK;IACvB,IAAIE,QAAQ,GAAG,SAAAA,CAAA,EAAY;MACvBD,WAAW,EAAE;MACb,IAAIA,WAAW,KAAK,CAAC,EAAE;QACnBzD,aAAa,CAAC,CAAC;QACfJ,OAAO,IAAIA,OAAO,CAAC,CAAC;MACxB;IACJ,CAAC;IACD,KAAK,IAAIrI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiM,KAAK,EAAEjM,CAAC,EAAE,EAAE;MAC5B,IAAIoM,sBAAsB,GAAGX,eAAe,GAAGnN,QAAQ,CAAC;QACpD+N,KAAK,EAAE,CAACjE,aAAa,CAACiE,KAAK,IAAI,CAAC,IAAIZ,eAAe,CAACzL,CAAC,EAAEiM,KAAK,EAAEhB,YAAY,CAACjL,CAAC,CAAC,EAAEsL,aAAa,CAACtL,CAAC,CAAC,CAAC;QAChGsI,IAAI,EAAE6D;MACV,CAAC,EAAE/D,aAAa,CAAC,GAAGA,aAAa;MACjCD,SAAS,CAAC8C,YAAY,CAACjL,CAAC,CAAC,EAAEsL,aAAa,CAACtL,CAAC,CAAC,EAAEoM,sBAAsB,CAAC;IACxE;EACJ,CAAC,MACI;IACDvF,MAAM,CAACxB,QAAQ,GAAG,CAAC;IACnBwB,MAAM,CAAC+B,SAAS,CAAC;MACbvD,QAAQ,EAAE;IACd,CAAC,EAAE/G,QAAQ,CAAC;MACRkK,MAAM,EAAE,SAAAA,CAAU5H,CAAC,EAAE;QACjB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiM,KAAK,EAAEjM,CAAC,EAAE,EAAE;UAC5B,IAAIsM,KAAK,GAAGhB,aAAa,CAACtL,CAAC,CAAC;UAC5BsM,KAAK,CAACjH,QAAQ,GAAGwB,MAAM,CAACxB,QAAQ;UAChCiH,KAAK,CAAC3D,UAAU,CAAC,CAAC;QACtB;QACAJ,SAAS,IAAIA,SAAS,CAAC3H,CAAC,CAAC;MAC7B,CAAC;MACD0H,IAAI,EAAE,SAAAA,CAAA,EAAY;QACdG,aAAa,CAAC,CAAC;QACf,KAAK,IAAIzI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgL,QAAQ,CAAC/L,MAAM,EAAEe,CAAC,EAAE,EAAE;UACtCoG,aAAa,CAAC4E,QAAQ,CAAChL,CAAC,CAAC,EAAE,iBAAiB,CAAC;QACjD;QACAqI,OAAO,IAAIA,OAAO,CAAC,CAAC;MACxB;IACJ,CAAC,EAAED,aAAa,CAAC,CAAC;EACtB;EACA,IAAIvB,MAAM,CAAC0F,IAAI,EAAE;IACbV,oBAAoB,CAAChF,MAAM,CAAC0F,IAAI,CAAC;EACrC;EACA,OAAO;IACH1B,eAAe,EAAEI,YAAY;IAC7BH,aAAa,EAAEQ,aAAa;IAC5BX,KAAK,EAAEsB;EACX,CAAC;AACL;AACA,OAAO,SAASO,aAAaA,CAAC5F,QAAQ,EAAE6F,UAAU,EAAErE,aAAa,EAAE;EAC/D,IAAI6D,KAAK,GAAGQ,UAAU,CAACxN,MAAM;EAC7B,IAAIgM,YAAY,GAAG,EAAE;EACrB,IAAII,UAAU,GAAGjD,aAAa,CAACiD,UAAU,IAAIZ,iBAAiB;EAC9D,SAASS,WAAWA,CAACF,QAAQ,EAAE;IAC3B,KAAK,IAAIhL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgL,QAAQ,CAAC/L,MAAM,EAAEe,CAAC,EAAE,EAAE;MACtC,IAAI6E,IAAI,GAAGmG,QAAQ,CAAChL,CAAC,CAAC;MACtB,IAAIgF,iBAAiB,CAACH,IAAI,CAAC,EAAE;QACzBqG,WAAW,CAACrG,IAAI,CAACsG,WAAW,CAAC,CAAC,CAAC;MACnC,CAAC,MACI,IAAItG,IAAI,YAAYxG,IAAI,EAAE;QAC3B4M,YAAY,CAACxK,IAAI,CAACoE,IAAI,CAAC;MAC3B;IACJ;EACJ;EACA,IAAIG,iBAAiB,CAAC4B,QAAQ,CAAC,EAAE;IAC7BsE,WAAW,CAACtE,QAAQ,CAACuE,WAAW,CAAC,CAAC,CAAC;IACnC,IAAIuB,OAAO,GAAGzB,YAAY,CAAChM,MAAM;IACjC,IAAIyN,OAAO,GAAGT,KAAK,EAAE;MACjB,IAAItL,CAAC,GAAG,CAAC;MACT,KAAK,IAAIX,CAAC,GAAG0M,OAAO,EAAE1M,CAAC,GAAGiM,KAAK,EAAEjM,CAAC,EAAE,EAAE;QAClCiL,YAAY,CAACxK,IAAI,CAAChC,SAAS,CAACwM,YAAY,CAACtK,CAAC,EAAE,GAAG+L,OAAO,CAAC,CAAC,CAAC;MAC7D;IACJ;IACAzB,YAAY,CAAChM,MAAM,GAAGgN,KAAK;EAC/B,CAAC,MACI;IACDhB,YAAY,GAAGI,UAAU,CAAC;MAAEpG,IAAI,EAAE2B,QAAQ;MAAE+D,KAAK,EAAEsB;IAAM,CAAC,CAAC;IAC3D,IAAI9E,iBAAiB,GAAGP,QAAQ,CAACQ,oBAAoB,CAAC,CAAC;IACvD,KAAK,IAAIpH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiL,YAAY,CAAChM,MAAM,EAAEe,CAAC,EAAE,EAAE;MAC1CiL,YAAY,CAACjL,CAAC,CAAC,CAAC2M,iBAAiB,CAACxF,iBAAiB,CAAC;IACxD;IACA,IAAI8D,YAAY,CAAChM,MAAM,KAAKgN,KAAK,EAAE;MAC/BV,OAAO,CAACC,KAAK,CAAC,2CAA2C,CAAC;MAC1D,OAAOZ,iBAAiB,CAAC,CAAC;IAC9B;EACJ;EACAK,YAAY,GAAGxB,SAAS,CAACwB,YAAY,CAAC;EACtCwB,UAAU,GAAGhD,SAAS,CAACgD,UAAU,CAAC;EAClC,IAAIhB,eAAe,GAAGrD,aAAa,CAACqD,eAAe;EACnD,KAAK,IAAIzL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiM,KAAK,EAAEjM,CAAC,EAAE,EAAE;IAC5B,IAAIoM,sBAAsB,GAAGX,eAAe,GAAGnN,QAAQ,CAAC;MACpD+N,KAAK,EAAE,CAACjE,aAAa,CAACiE,KAAK,IAAI,CAAC,IAAIZ,eAAe,CAACzL,CAAC,EAAEiM,KAAK,EAAEhB,YAAY,CAACjL,CAAC,CAAC,EAAEyM,UAAU,CAACzM,CAAC,CAAC;IAChG,CAAC,EAAEoI,aAAa,CAAC,GAAGA,aAAa;IACjCD,SAAS,CAAC8C,YAAY,CAACjL,CAAC,CAAC,EAAEyM,UAAU,CAACzM,CAAC,CAAC,EAAEoM,sBAAsB,CAAC;EACrE;EACA,OAAO;IACHvB,eAAe,EAAEI,YAAY;IAC7BH,aAAa,EAAE2B,UAAU;IACzB9B,KAAK,EAAE8B,UAAU,CAACxN;EACtB,CAAC;AACL;AACA,SAASN,KAAK,IAAI8L,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}