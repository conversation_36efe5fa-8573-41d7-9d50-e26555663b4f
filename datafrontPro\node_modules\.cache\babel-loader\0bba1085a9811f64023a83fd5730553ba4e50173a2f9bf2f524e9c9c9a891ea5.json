{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport * as echarts from 'echarts';\nexport default {\n  name: 'ResultDisplay',\n  props: {\n    result: {\n      type: Object,\n      required: true\n    }\n  },\n  watch: {\n    result: {\n      handler() {\n        this.$nextTick(() => {\n          this.renderChart();\n        });\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    renderChart() {\n      if (!this.result || !this.result.data) return;\n      if (!this.$refs.chart) return;\n      const chart = echarts.init(this.$refs.chart);\n      let option = {};\n\n      // 根据返回的数据类型生成不同图表\n      if (this.result.chartType === 'bar') {\n        option = this.generateBarOption();\n      } else if (this.result.chartType === 'pie') {\n        option = this.generatePieOption();\n      } else if (this.result.chartType === 'line') {\n        option = this.generateLineOption();\n      }\n      chart.setOption(option);\n    },\n    generateBarOption() {\n      // 简单示例，实际应根据数据结构调整\n      return {\n        title: {\n          text: '数据分析结果'\n        },\n        tooltip: {},\n        xAxis: {\n          data: this.result.data.map(item => item.dimension || item.name)\n        },\n        yAxis: {},\n        series: [{\n          name: '值',\n          type: 'bar',\n          data: this.result.data.map(item => item.value)\n        }]\n      };\n    },\n    generatePieOption() {\n      return {\n        title: {\n          text: '数据分析结果'\n        },\n        tooltip: {},\n        series: [{\n          name: '占比',\n          type: 'pie',\n          radius: '60%',\n          data: this.result.data.map(item => ({\n            name: item.dimension || item.name,\n            value: item.value\n          }))\n        }]\n      };\n    },\n    generateLineOption() {\n      return {\n        title: {\n          text: '数据分析结果'\n        },\n        tooltip: {},\n        xAxis: {\n          data: this.result.data.map(item => item.dimension || item.name)\n        },\n        yAxis: {},\n        series: [{\n          name: '值',\n          type: 'line',\n          data: this.result.data.map(item => item.value)\n        }]\n      };\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "name", "props", "result", "type", "Object", "required", "watch", "handler", "$nextTick", "<PERSON><PERSON><PERSON>", "immediate", "methods", "data", "$refs", "chart", "init", "option", "chartType", "generateBarOption", "generatePieOption", "generateLineOption", "setOption", "title", "text", "tooltip", "xAxis", "map", "item", "dimension", "yAxis", "series", "value", "radius"], "sources": ["src/components/ResultDisplay.vue"], "sourcesContent": ["   <template>\r\n     <div class=\"result-display\">\r\n       <div class=\"answer-section\">\r\n         <h2>分析结果</h2>\r\n         <p class=\"answer-text\">{{ result.answer }}</p>\r\n       </div>\r\n       \r\n       <div v-if=\"result.data\" class=\"chart-section\">\r\n         <h2>数据可视化</h2>\r\n         <div class=\"chart\" ref=\"chart\"></div>\r\n       </div>\r\n       \r\n       <div class=\"action-buttons\">\r\n         <el-button type=\"primary\" @click=\"$emit('ask-again')\">再次提问</el-button>\r\n       </div>\r\n     </div>\r\n   </template>\r\n\r\n   <script>\r\n   import * as echarts from 'echarts'\r\n\r\n   export default {\r\n     name: 'ResultDisplay',\r\n     props: {\r\n       result: {\r\n         type: Object,\r\n         required: true\r\n       }\r\n     },\r\n     watch: {\r\n       result: {\r\n         handler() {\r\n           this.$nextTick(() => {\r\n             this.renderChart()\r\n           })\r\n         },\r\n         immediate: true\r\n       }\r\n     },\r\n     methods: {\r\n       renderChart() {\r\n         if (!this.result || !this.result.data) return\r\n         \r\n         if (!this.$refs.chart) return\r\n         \r\n         const chart = echarts.init(this.$refs.chart)\r\n         let option = {}\r\n         \r\n         // 根据返回的数据类型生成不同图表\r\n         if (this.result.chartType === 'bar') {\r\n           option = this.generateBarOption()\r\n         } else if (this.result.chartType === 'pie') {\r\n           option = this.generatePieOption()\r\n         } else if (this.result.chartType === 'line') {\r\n           option = this.generateLineOption()\r\n         }\r\n         \r\n         chart.setOption(option)\r\n       },\r\n       generateBarOption() {\r\n         // 简单示例，实际应根据数据结构调整\r\n         return {\r\n           title: { text: '数据分析结果' },\r\n           tooltip: {},\r\n           xAxis: { \r\n             data: this.result.data.map(item => item.dimension || item.name)\r\n           },\r\n           yAxis: {},\r\n           series: [{\r\n             name: '值',\r\n             type: 'bar',\r\n             data: this.result.data.map(item => item.value)\r\n           }]\r\n         }\r\n       },\r\n       generatePieOption() {\r\n         return {\r\n           title: { text: '数据分析结果' },\r\n           tooltip: {},\r\n           series: [{\r\n             name: '占比',\r\n             type: 'pie',\r\n             radius: '60%',\r\n             data: this.result.data.map(item => ({\r\n               name: item.dimension || item.name,\r\n               value: item.value\r\n             }))\r\n           }]\r\n         }\r\n       },\r\n       generateLineOption() {\r\n         return {\r\n           title: { text: '数据分析结果' },\r\n           tooltip: {},\r\n           xAxis: { \r\n             data: this.result.data.map(item => item.dimension || item.name)\r\n           },\r\n           yAxis: {},\r\n           series: [{\r\n             name: '值',\r\n             type: 'line',\r\n             data: this.result.data.map(item => item.value)\r\n           }]\r\n         }\r\n       }\r\n     }\r\n   }\r\n   </script>\r\n\r\n   <style scoped>\r\n   .result-display {\r\n     padding: 20px 0;\r\n   }\r\n   .answer-text {\r\n     font-size: 16px;\r\n     line-height: 1.6;\r\n     margin-bottom: 20px;\r\n   }\r\n   .chart {\r\n     height: 400px;\r\n     margin: 20px 0;\r\n   }\r\n   .action-buttons {\r\n     margin-top: 30px;\r\n   }\r\n   </style>"], "mappings": ";;AAmBA,YAAAA,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACAJ,MAAA;MACAK,QAAA;QACA,KAAAC,SAAA;UACA,KAAAC,WAAA;QACA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACAF,YAAA;MACA,UAAAP,MAAA,UAAAA,MAAA,CAAAU,IAAA;MAEA,UAAAC,KAAA,CAAAC,KAAA;MAEA,MAAAA,KAAA,GAAAf,OAAA,CAAAgB,IAAA,MAAAF,KAAA,CAAAC,KAAA;MACA,IAAAE,MAAA;;MAEA;MACA,SAAAd,MAAA,CAAAe,SAAA;QACAD,MAAA,QAAAE,iBAAA;MACA,gBAAAhB,MAAA,CAAAe,SAAA;QACAD,MAAA,QAAAG,iBAAA;MACA,gBAAAjB,MAAA,CAAAe,SAAA;QACAD,MAAA,QAAAI,kBAAA;MACA;MAEAN,KAAA,CAAAO,SAAA,CAAAL,MAAA;IACA;IACAE,kBAAA;MACA;MACA;QACAI,KAAA;UAAAC,IAAA;QAAA;QACAC,OAAA;QACAC,KAAA;UACAb,IAAA,OAAAV,MAAA,CAAAU,IAAA,CAAAc,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,SAAA,IAAAD,IAAA,CAAA3B,IAAA;QACA;QACA6B,KAAA;QACAC,MAAA;UACA9B,IAAA;UACAG,IAAA;UACAS,IAAA,OAAAV,MAAA,CAAAU,IAAA,CAAAc,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAI,KAAA;QACA;MACA;IACA;IACAZ,kBAAA;MACA;QACAG,KAAA;UAAAC,IAAA;QAAA;QACAC,OAAA;QACAM,MAAA;UACA9B,IAAA;UACAG,IAAA;UACA6B,MAAA;UACApB,IAAA,OAAAV,MAAA,CAAAU,IAAA,CAAAc,GAAA,CAAAC,IAAA;YACA3B,IAAA,EAAA2B,IAAA,CAAAC,SAAA,IAAAD,IAAA,CAAA3B,IAAA;YACA+B,KAAA,EAAAJ,IAAA,CAAAI;UACA;QACA;MACA;IACA;IACAX,mBAAA;MACA;QACAE,KAAA;UAAAC,IAAA;QAAA;QACAC,OAAA;QACAC,KAAA;UACAb,IAAA,OAAAV,MAAA,CAAAU,IAAA,CAAAc,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,SAAA,IAAAD,IAAA,CAAA3B,IAAA;QACA;QACA6B,KAAA;QACAC,MAAA;UACA9B,IAAA;UACAG,IAAA;UACAS,IAAA,OAAAV,MAAA,CAAAU,IAAA,CAAAc,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAI,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}