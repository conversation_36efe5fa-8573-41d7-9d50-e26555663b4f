{"ast": null, "code": "export default {\n  name: 'QuestionForm',\n  props: {\n    selectedTable: {\n      type: Object,\n      required: true\n    }\n  },\n  data() {\n    return {\n      question: '',\n      exampleQuestions: ['2024年第一季度销售额是多少？', '销量最高的产品是什么？', '各产品的销售占比是多少？']\n    };\n  },\n  methods: {\n    submitQuestion() {\n      if (this.question.trim()) {\n        this.$emit('submit-question', this.question);\n      } else {\n        this.$message.warning('请输入问题');\n      }\n    },\n    setQuestion(q) {\n      this.question = q;\n    },\n    goBack() {\n      this.$emit('go-back');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "selectedTable", "type", "Object", "required", "data", "question", "exampleQuestions", "methods", "submitQuestion", "trim", "$emit", "$message", "warning", "setQuestion", "q", "goBack"], "sources": ["src/components/QuestionForm.vue"], "sourcesContent": ["   <template>\r\n     <div class=\"question-form\">\r\n       <h2>问题提问 - {{ selectedTable.tableName }}</h2>\r\n       <p class=\"table-description\">{{ selectedTable.description }}</p>\r\n       \r\n       <div class=\"input-group\">\r\n         <el-input \r\n           v-model=\"question\" \r\n           placeholder=\"请输入您的问题，例如：第一季度销售额是多少？\"\r\n           @keyup.enter.native=\"submitQuestion\"\r\n         ></el-input>\r\n         <el-button type=\"primary\" @click=\"submitQuestion\">提问</el-button>\r\n         <el-button @click=\"goBack\">返回</el-button>\r\n       </div>\r\n       \r\n       <div class=\"example-questions\">\r\n         <p>示例问题：</p>\r\n         <el-tag \r\n           v-for=\"(q, index) in exampleQuestions\" \r\n           :key=\"index\"\r\n           @click=\"setQuestion(q)\"\r\n           class=\"question-tag\"\r\n         >{{ q }}</el-tag>\r\n       </div>\r\n     </div>\r\n   </template>\r\n\r\n   <script>\r\n   export default {\r\n     name: 'QuestionForm',\r\n     props: {\r\n       selectedTable: {\r\n         type: Object,\r\n         required: true\r\n       }\r\n     },\r\n     data() {\r\n       return {\r\n         question: '',\r\n         exampleQuestions: [\r\n           '2024年第一季度销售额是多少？',\r\n           '销量最高的产品是什么？',\r\n           '各产品的销售占比是多少？'\r\n         ]\r\n       }\r\n     },\r\n     methods: {\r\n       submitQuestion() {\r\n         if (this.question.trim()) {\r\n           this.$emit('submit-question', this.question)\r\n         } else {\r\n           this.$message.warning('请输入问题')\r\n         }\r\n       },\r\n       setQuestion(q) {\r\n         this.question = q\r\n       },\r\n       goBack() {\r\n         this.$emit('go-back')\r\n       }\r\n     }\r\n   }\r\n   </script>\r\n\r\n   <style scoped>\r\n   .question-form {\r\n     padding: 20px 0;\r\n   }\r\n   .table-description {\r\n     color: #606266;\r\n     margin-bottom: 20px;\r\n   }\r\n   .input-group {\r\n     display: flex;\r\n     margin-bottom: 20px;\r\n   }\r\n   .input-group .el-input {\r\n     margin-right: 10px;\r\n   }\r\n   .example-questions {\r\n     margin-top: 30px;\r\n   }\r\n   .question-tag {\r\n     margin: 5px;\r\n     cursor: pointer;\r\n   }\r\n   </style>"], "mappings": "AA4BA;EACAA,IAAA;EACAC,KAAA;IACAC,aAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,gBAAA,GACA,oBACA,eACA;IAEA;EACA;EACAC,OAAA;IACAC,eAAA;MACA,SAAAH,QAAA,CAAAI,IAAA;QACA,KAAAC,KAAA,yBAAAL,QAAA;MACA;QACA,KAAAM,QAAA,CAAAC,OAAA;MACA;IACA;IACAC,YAAAC,CAAA;MACA,KAAAT,QAAA,GAAAS,CAAA;IACA;IACAC,OAAA;MACA,KAAAL,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}