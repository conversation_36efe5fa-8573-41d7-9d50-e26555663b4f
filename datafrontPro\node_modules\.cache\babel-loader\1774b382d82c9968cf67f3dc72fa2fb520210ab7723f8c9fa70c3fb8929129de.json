{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport ComponentModel from '../../model/Component.js';\nimport ComponentView from '../../view/Component.js';\nimport { windowOpen } from '../../util/format.js';\nvar TitleModel = /** @class */function (_super) {\n  __extends(TitleModel, _super);\n  function TitleModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TitleModel.type;\n    _this.layoutMode = {\n      type: 'box',\n      ignoreSize: true\n    };\n    return _this;\n  }\n  TitleModel.type = 'title';\n  TitleModel.defaultOption = {\n    // zlevel: 0,\n    z: 6,\n    show: true,\n    text: '',\n    target: 'blank',\n    subtext: '',\n    subtarget: 'blank',\n    left: 0,\n    top: 0,\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    borderWidth: 0,\n    padding: 5,\n    itemGap: 10,\n    textStyle: {\n      fontSize: 18,\n      fontWeight: 'bold',\n      color: '#464646'\n    },\n    subtextStyle: {\n      fontSize: 12,\n      color: '#6E7079'\n    }\n  };\n  return TitleModel;\n}(ComponentModel);\n// View\nvar TitleView = /** @class */function (_super) {\n  __extends(TitleView, _super);\n  function TitleView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TitleView.type;\n    return _this;\n  }\n  TitleView.prototype.render = function (titleModel, ecModel, api) {\n    this.group.removeAll();\n    if (!titleModel.get('show')) {\n      return;\n    }\n    var group = this.group;\n    var textStyleModel = titleModel.getModel('textStyle');\n    var subtextStyleModel = titleModel.getModel('subtextStyle');\n    var textAlign = titleModel.get('textAlign');\n    var textVerticalAlign = zrUtil.retrieve2(titleModel.get('textBaseline'), titleModel.get('textVerticalAlign'));\n    var textEl = new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        text: titleModel.get('text'),\n        fill: textStyleModel.getTextColor()\n      }, {\n        disableBox: true\n      }),\n      z2: 10\n    });\n    var textRect = textEl.getBoundingRect();\n    var subText = titleModel.get('subtext');\n    var subTextEl = new graphic.Text({\n      style: createTextStyle(subtextStyleModel, {\n        text: subText,\n        fill: subtextStyleModel.getTextColor(),\n        y: textRect.height + titleModel.get('itemGap'),\n        verticalAlign: 'top'\n      }, {\n        disableBox: true\n      }),\n      z2: 10\n    });\n    var link = titleModel.get('link');\n    var sublink = titleModel.get('sublink');\n    var triggerEvent = titleModel.get('triggerEvent', true);\n    textEl.silent = !link && !triggerEvent;\n    subTextEl.silent = !sublink && !triggerEvent;\n    if (link) {\n      textEl.on('click', function () {\n        windowOpen(link, '_' + titleModel.get('target'));\n      });\n    }\n    if (sublink) {\n      subTextEl.on('click', function () {\n        windowOpen(sublink, '_' + titleModel.get('subtarget'));\n      });\n    }\n    getECData(textEl).eventData = getECData(subTextEl).eventData = triggerEvent ? {\n      componentType: 'title',\n      componentIndex: titleModel.componentIndex\n    } : null;\n    group.add(textEl);\n    subText && group.add(subTextEl);\n    // If no subText, but add subTextEl, there will be an empty line.\n    var groupRect = group.getBoundingRect();\n    var layoutOption = titleModel.getBoxLayoutParams();\n    layoutOption.width = groupRect.width;\n    layoutOption.height = groupRect.height;\n    var layoutRect = getLayoutRect(layoutOption, {\n      width: api.getWidth(),\n      height: api.getHeight()\n    }, titleModel.get('padding'));\n    // Adjust text align based on position\n    if (!textAlign) {\n      // Align left if title is on the left. center and right is same\n      textAlign = titleModel.get('left') || titleModel.get('right');\n      // @ts-ignore\n      if (textAlign === 'middle') {\n        textAlign = 'center';\n      }\n      // Adjust layout by text align\n      if (textAlign === 'right') {\n        layoutRect.x += layoutRect.width;\n      } else if (textAlign === 'center') {\n        layoutRect.x += layoutRect.width / 2;\n      }\n    }\n    if (!textVerticalAlign) {\n      textVerticalAlign = titleModel.get('top') || titleModel.get('bottom');\n      // @ts-ignore\n      if (textVerticalAlign === 'center') {\n        textVerticalAlign = 'middle';\n      }\n      if (textVerticalAlign === 'bottom') {\n        layoutRect.y += layoutRect.height;\n      } else if (textVerticalAlign === 'middle') {\n        layoutRect.y += layoutRect.height / 2;\n      }\n      textVerticalAlign = textVerticalAlign || 'top';\n    }\n    group.x = layoutRect.x;\n    group.y = layoutRect.y;\n    group.markRedraw();\n    var alignStyle = {\n      align: textAlign,\n      verticalAlign: textVerticalAlign\n    };\n    textEl.setStyle(alignStyle);\n    subTextEl.setStyle(alignStyle);\n    // Render background\n    // Get groupRect again because textAlign has been changed\n    groupRect = group.getBoundingRect();\n    var padding = layoutRect.margin;\n    var style = titleModel.getItemStyle(['color', 'opacity']);\n    style.fill = titleModel.get('backgroundColor');\n    var rect = new graphic.Rect({\n      shape: {\n        x: groupRect.x - padding[3],\n        y: groupRect.y - padding[0],\n        width: groupRect.width + padding[1] + padding[3],\n        height: groupRect.height + padding[0] + padding[2],\n        r: titleModel.get('borderRadius')\n      },\n      style: style,\n      subPixelOptimize: true,\n      silent: true\n    });\n    group.add(rect);\n  };\n  TitleView.type = 'title';\n  return TitleView;\n}(ComponentView);\nexport function install(registers) {\n  registers.registerComponentModel(TitleModel);\n  registers.registerComponentView(TitleView);\n}", "map": {"version": 3, "names": ["__extends", "zrUtil", "graphic", "getECData", "createTextStyle", "getLayoutRect", "ComponentModel", "ComponentView", "windowOpen", "TitleModel", "_super", "_this", "apply", "arguments", "type", "layoutMode", "ignoreSize", "defaultOption", "z", "show", "text", "target", "subtext", "subtarget", "left", "top", "backgroundColor", "borderColor", "borderWidth", "padding", "itemGap", "textStyle", "fontSize", "fontWeight", "color", "subtextStyle", "TitleView", "prototype", "render", "titleModel", "ecModel", "api", "group", "removeAll", "get", "textStyleModel", "getModel", "subtextStyleModel", "textAlign", "textVerticalAlign", "retrieve2", "textEl", "Text", "style", "fill", "getTextColor", "disableBox", "z2", "textRect", "getBoundingRect", "subText", "subTextEl", "y", "height", "verticalAlign", "link", "sublink", "triggerEvent", "silent", "on", "eventData", "componentType", "componentIndex", "add", "groupRect", "layoutOption", "getBoxLayoutParams", "width", "layoutRect", "getWidth", "getHeight", "x", "mark<PERSON><PERSON><PERSON>", "alignStyle", "align", "setStyle", "margin", "getItemStyle", "rect", "Rect", "shape", "r", "subPixelOptimize", "install", "registers", "registerComponentModel", "registerComponentView"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/component/title/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport ComponentModel from '../../model/Component.js';\nimport ComponentView from '../../view/Component.js';\nimport { windowOpen } from '../../util/format.js';\nvar TitleModel = /** @class */function (_super) {\n  __extends(TitleModel, _super);\n  function TitleModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TitleModel.type;\n    _this.layoutMode = {\n      type: 'box',\n      ignoreSize: true\n    };\n    return _this;\n  }\n  TitleModel.type = 'title';\n  TitleModel.defaultOption = {\n    // zlevel: 0,\n    z: 6,\n    show: true,\n    text: '',\n    target: 'blank',\n    subtext: '',\n    subtarget: 'blank',\n    left: 0,\n    top: 0,\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    borderWidth: 0,\n    padding: 5,\n    itemGap: 10,\n    textStyle: {\n      fontSize: 18,\n      fontWeight: 'bold',\n      color: '#464646'\n    },\n    subtextStyle: {\n      fontSize: 12,\n      color: '#6E7079'\n    }\n  };\n  return TitleModel;\n}(ComponentModel);\n// View\nvar TitleView = /** @class */function (_super) {\n  __extends(TitleView, _super);\n  function TitleView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TitleView.type;\n    return _this;\n  }\n  TitleView.prototype.render = function (titleModel, ecModel, api) {\n    this.group.removeAll();\n    if (!titleModel.get('show')) {\n      return;\n    }\n    var group = this.group;\n    var textStyleModel = titleModel.getModel('textStyle');\n    var subtextStyleModel = titleModel.getModel('subtextStyle');\n    var textAlign = titleModel.get('textAlign');\n    var textVerticalAlign = zrUtil.retrieve2(titleModel.get('textBaseline'), titleModel.get('textVerticalAlign'));\n    var textEl = new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        text: titleModel.get('text'),\n        fill: textStyleModel.getTextColor()\n      }, {\n        disableBox: true\n      }),\n      z2: 10\n    });\n    var textRect = textEl.getBoundingRect();\n    var subText = titleModel.get('subtext');\n    var subTextEl = new graphic.Text({\n      style: createTextStyle(subtextStyleModel, {\n        text: subText,\n        fill: subtextStyleModel.getTextColor(),\n        y: textRect.height + titleModel.get('itemGap'),\n        verticalAlign: 'top'\n      }, {\n        disableBox: true\n      }),\n      z2: 10\n    });\n    var link = titleModel.get('link');\n    var sublink = titleModel.get('sublink');\n    var triggerEvent = titleModel.get('triggerEvent', true);\n    textEl.silent = !link && !triggerEvent;\n    subTextEl.silent = !sublink && !triggerEvent;\n    if (link) {\n      textEl.on('click', function () {\n        windowOpen(link, '_' + titleModel.get('target'));\n      });\n    }\n    if (sublink) {\n      subTextEl.on('click', function () {\n        windowOpen(sublink, '_' + titleModel.get('subtarget'));\n      });\n    }\n    getECData(textEl).eventData = getECData(subTextEl).eventData = triggerEvent ? {\n      componentType: 'title',\n      componentIndex: titleModel.componentIndex\n    } : null;\n    group.add(textEl);\n    subText && group.add(subTextEl);\n    // If no subText, but add subTextEl, there will be an empty line.\n    var groupRect = group.getBoundingRect();\n    var layoutOption = titleModel.getBoxLayoutParams();\n    layoutOption.width = groupRect.width;\n    layoutOption.height = groupRect.height;\n    var layoutRect = getLayoutRect(layoutOption, {\n      width: api.getWidth(),\n      height: api.getHeight()\n    }, titleModel.get('padding'));\n    // Adjust text align based on position\n    if (!textAlign) {\n      // Align left if title is on the left. center and right is same\n      textAlign = titleModel.get('left') || titleModel.get('right');\n      // @ts-ignore\n      if (textAlign === 'middle') {\n        textAlign = 'center';\n      }\n      // Adjust layout by text align\n      if (textAlign === 'right') {\n        layoutRect.x += layoutRect.width;\n      } else if (textAlign === 'center') {\n        layoutRect.x += layoutRect.width / 2;\n      }\n    }\n    if (!textVerticalAlign) {\n      textVerticalAlign = titleModel.get('top') || titleModel.get('bottom');\n      // @ts-ignore\n      if (textVerticalAlign === 'center') {\n        textVerticalAlign = 'middle';\n      }\n      if (textVerticalAlign === 'bottom') {\n        layoutRect.y += layoutRect.height;\n      } else if (textVerticalAlign === 'middle') {\n        layoutRect.y += layoutRect.height / 2;\n      }\n      textVerticalAlign = textVerticalAlign || 'top';\n    }\n    group.x = layoutRect.x;\n    group.y = layoutRect.y;\n    group.markRedraw();\n    var alignStyle = {\n      align: textAlign,\n      verticalAlign: textVerticalAlign\n    };\n    textEl.setStyle(alignStyle);\n    subTextEl.setStyle(alignStyle);\n    // Render background\n    // Get groupRect again because textAlign has been changed\n    groupRect = group.getBoundingRect();\n    var padding = layoutRect.margin;\n    var style = titleModel.getItemStyle(['color', 'opacity']);\n    style.fill = titleModel.get('backgroundColor');\n    var rect = new graphic.Rect({\n      shape: {\n        x: groupRect.x - padding[3],\n        y: groupRect.y - padding[0],\n        width: groupRect.width + padding[1] + padding[3],\n        height: groupRect.height + padding[0] + padding[2],\n        r: titleModel.get('borderRadius')\n      },\n      style: style,\n      subPixelOptimize: true,\n      silent: true\n    });\n    group.add(rect);\n  };\n  TitleView.type = 'title';\n  return TitleView;\n}(ComponentView);\nexport function install(registers) {\n  registers.registerComponentModel(TitleModel);\n  registers.registerComponentView(TitleView);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,UAAU,QAAQ,sBAAsB;AACjD,IAAIC,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9CV,SAAS,CAACS,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,UAAU,CAACK,IAAI;IAC5BH,KAAK,CAACI,UAAU,GAAG;MACjBD,IAAI,EAAE,KAAK;MACXE,UAAU,EAAE;IACd,CAAC;IACD,OAAOL,KAAK;EACd;EACAF,UAAU,CAACK,IAAI,GAAG,OAAO;EACzBL,UAAU,CAACQ,aAAa,GAAG;IACzB;IACAC,CAAC,EAAE,CAAC;IACJC,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,OAAO;IACfC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,OAAO;IAClBC,IAAI,EAAE,CAAC;IACPC,GAAG,EAAE,CAAC;IACNC,eAAe,EAAE,eAAe;IAChCC,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,CAAC;IACdC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE;MACTC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,MAAM;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,YAAY,EAAE;MACZH,QAAQ,EAAE,EAAE;MACZE,KAAK,EAAE;IACT;EACF,CAAC;EACD,OAAOzB,UAAU;AACnB,CAAC,CAACH,cAAc,CAAC;AACjB;AACA,IAAI8B,SAAS,GAAG,aAAa,UAAU1B,MAAM,EAAE;EAC7CV,SAAS,CAACoC,SAAS,EAAE1B,MAAM,CAAC;EAC5B,SAAS0B,SAASA,CAAA,EAAG;IACnB,IAAIzB,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGsB,SAAS,CAACtB,IAAI;IAC3B,OAAOH,KAAK;EACd;EACAyB,SAAS,CAACC,SAAS,CAACC,MAAM,GAAG,UAAUC,UAAU,EAAEC,OAAO,EAAEC,GAAG,EAAE;IAC/D,IAAI,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAI,CAACJ,UAAU,CAACK,GAAG,CAAC,MAAM,CAAC,EAAE;MAC3B;IACF;IACA,IAAIF,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIG,cAAc,GAAGN,UAAU,CAACO,QAAQ,CAAC,WAAW,CAAC;IACrD,IAAIC,iBAAiB,GAAGR,UAAU,CAACO,QAAQ,CAAC,cAAc,CAAC;IAC3D,IAAIE,SAAS,GAAGT,UAAU,CAACK,GAAG,CAAC,WAAW,CAAC;IAC3C,IAAIK,iBAAiB,GAAGhD,MAAM,CAACiD,SAAS,CAACX,UAAU,CAACK,GAAG,CAAC,cAAc,CAAC,EAAEL,UAAU,CAACK,GAAG,CAAC,mBAAmB,CAAC,CAAC;IAC7G,IAAIO,MAAM,GAAG,IAAIjD,OAAO,CAACkD,IAAI,CAAC;MAC5BC,KAAK,EAAEjD,eAAe,CAACyC,cAAc,EAAE;QACrCzB,IAAI,EAAEmB,UAAU,CAACK,GAAG,CAAC,MAAM,CAAC;QAC5BU,IAAI,EAAET,cAAc,CAACU,YAAY,CAAC;MACpC,CAAC,EAAE;QACDC,UAAU,EAAE;MACd,CAAC,CAAC;MACFC,EAAE,EAAE;IACN,CAAC,CAAC;IACF,IAAIC,QAAQ,GAAGP,MAAM,CAACQ,eAAe,CAAC,CAAC;IACvC,IAAIC,OAAO,GAAGrB,UAAU,CAACK,GAAG,CAAC,SAAS,CAAC;IACvC,IAAIiB,SAAS,GAAG,IAAI3D,OAAO,CAACkD,IAAI,CAAC;MAC/BC,KAAK,EAAEjD,eAAe,CAAC2C,iBAAiB,EAAE;QACxC3B,IAAI,EAAEwC,OAAO;QACbN,IAAI,EAAEP,iBAAiB,CAACQ,YAAY,CAAC,CAAC;QACtCO,CAAC,EAAEJ,QAAQ,CAACK,MAAM,GAAGxB,UAAU,CAACK,GAAG,CAAC,SAAS,CAAC;QAC9CoB,aAAa,EAAE;MACjB,CAAC,EAAE;QACDR,UAAU,EAAE;MACd,CAAC,CAAC;MACFC,EAAE,EAAE;IACN,CAAC,CAAC;IACF,IAAIQ,IAAI,GAAG1B,UAAU,CAACK,GAAG,CAAC,MAAM,CAAC;IACjC,IAAIsB,OAAO,GAAG3B,UAAU,CAACK,GAAG,CAAC,SAAS,CAAC;IACvC,IAAIuB,YAAY,GAAG5B,UAAU,CAACK,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC;IACvDO,MAAM,CAACiB,MAAM,GAAG,CAACH,IAAI,IAAI,CAACE,YAAY;IACtCN,SAAS,CAACO,MAAM,GAAG,CAACF,OAAO,IAAI,CAACC,YAAY;IAC5C,IAAIF,IAAI,EAAE;MACRd,MAAM,CAACkB,EAAE,CAAC,OAAO,EAAE,YAAY;QAC7B7D,UAAU,CAACyD,IAAI,EAAE,GAAG,GAAG1B,UAAU,CAACK,GAAG,CAAC,QAAQ,CAAC,CAAC;MAClD,CAAC,CAAC;IACJ;IACA,IAAIsB,OAAO,EAAE;MACXL,SAAS,CAACQ,EAAE,CAAC,OAAO,EAAE,YAAY;QAChC7D,UAAU,CAAC0D,OAAO,EAAE,GAAG,GAAG3B,UAAU,CAACK,GAAG,CAAC,WAAW,CAAC,CAAC;MACxD,CAAC,CAAC;IACJ;IACAzC,SAAS,CAACgD,MAAM,CAAC,CAACmB,SAAS,GAAGnE,SAAS,CAAC0D,SAAS,CAAC,CAACS,SAAS,GAAGH,YAAY,GAAG;MAC5EI,aAAa,EAAE,OAAO;MACtBC,cAAc,EAAEjC,UAAU,CAACiC;IAC7B,CAAC,GAAG,IAAI;IACR9B,KAAK,CAAC+B,GAAG,CAACtB,MAAM,CAAC;IACjBS,OAAO,IAAIlB,KAAK,CAAC+B,GAAG,CAACZ,SAAS,CAAC;IAC/B;IACA,IAAIa,SAAS,GAAGhC,KAAK,CAACiB,eAAe,CAAC,CAAC;IACvC,IAAIgB,YAAY,GAAGpC,UAAU,CAACqC,kBAAkB,CAAC,CAAC;IAClDD,YAAY,CAACE,KAAK,GAAGH,SAAS,CAACG,KAAK;IACpCF,YAAY,CAACZ,MAAM,GAAGW,SAAS,CAACX,MAAM;IACtC,IAAIe,UAAU,GAAGzE,aAAa,CAACsE,YAAY,EAAE;MAC3CE,KAAK,EAAEpC,GAAG,CAACsC,QAAQ,CAAC,CAAC;MACrBhB,MAAM,EAAEtB,GAAG,CAACuC,SAAS,CAAC;IACxB,CAAC,EAAEzC,UAAU,CAACK,GAAG,CAAC,SAAS,CAAC,CAAC;IAC7B;IACA,IAAI,CAACI,SAAS,EAAE;MACd;MACAA,SAAS,GAAGT,UAAU,CAACK,GAAG,CAAC,MAAM,CAAC,IAAIL,UAAU,CAACK,GAAG,CAAC,OAAO,CAAC;MAC7D;MACA,IAAII,SAAS,KAAK,QAAQ,EAAE;QAC1BA,SAAS,GAAG,QAAQ;MACtB;MACA;MACA,IAAIA,SAAS,KAAK,OAAO,EAAE;QACzB8B,UAAU,CAACG,CAAC,IAAIH,UAAU,CAACD,KAAK;MAClC,CAAC,MAAM,IAAI7B,SAAS,KAAK,QAAQ,EAAE;QACjC8B,UAAU,CAACG,CAAC,IAAIH,UAAU,CAACD,KAAK,GAAG,CAAC;MACtC;IACF;IACA,IAAI,CAAC5B,iBAAiB,EAAE;MACtBA,iBAAiB,GAAGV,UAAU,CAACK,GAAG,CAAC,KAAK,CAAC,IAAIL,UAAU,CAACK,GAAG,CAAC,QAAQ,CAAC;MACrE;MACA,IAAIK,iBAAiB,KAAK,QAAQ,EAAE;QAClCA,iBAAiB,GAAG,QAAQ;MAC9B;MACA,IAAIA,iBAAiB,KAAK,QAAQ,EAAE;QAClC6B,UAAU,CAAChB,CAAC,IAAIgB,UAAU,CAACf,MAAM;MACnC,CAAC,MAAM,IAAId,iBAAiB,KAAK,QAAQ,EAAE;QACzC6B,UAAU,CAAChB,CAAC,IAAIgB,UAAU,CAACf,MAAM,GAAG,CAAC;MACvC;MACAd,iBAAiB,GAAGA,iBAAiB,IAAI,KAAK;IAChD;IACAP,KAAK,CAACuC,CAAC,GAAGH,UAAU,CAACG,CAAC;IACtBvC,KAAK,CAACoB,CAAC,GAAGgB,UAAU,CAAChB,CAAC;IACtBpB,KAAK,CAACwC,UAAU,CAAC,CAAC;IAClB,IAAIC,UAAU,GAAG;MACfC,KAAK,EAAEpC,SAAS;MAChBgB,aAAa,EAAEf;IACjB,CAAC;IACDE,MAAM,CAACkC,QAAQ,CAACF,UAAU,CAAC;IAC3BtB,SAAS,CAACwB,QAAQ,CAACF,UAAU,CAAC;IAC9B;IACA;IACAT,SAAS,GAAGhC,KAAK,CAACiB,eAAe,CAAC,CAAC;IACnC,IAAI9B,OAAO,GAAGiD,UAAU,CAACQ,MAAM;IAC/B,IAAIjC,KAAK,GAAGd,UAAU,CAACgD,YAAY,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IACzDlC,KAAK,CAACC,IAAI,GAAGf,UAAU,CAACK,GAAG,CAAC,iBAAiB,CAAC;IAC9C,IAAI4C,IAAI,GAAG,IAAItF,OAAO,CAACuF,IAAI,CAAC;MAC1BC,KAAK,EAAE;QACLT,CAAC,EAAEP,SAAS,CAACO,CAAC,GAAGpD,OAAO,CAAC,CAAC,CAAC;QAC3BiC,CAAC,EAAEY,SAAS,CAACZ,CAAC,GAAGjC,OAAO,CAAC,CAAC,CAAC;QAC3BgD,KAAK,EAAEH,SAAS,CAACG,KAAK,GAAGhD,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;QAChDkC,MAAM,EAAEW,SAAS,CAACX,MAAM,GAAGlC,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC;QAClD8D,CAAC,EAAEpD,UAAU,CAACK,GAAG,CAAC,cAAc;MAClC,CAAC;MACDS,KAAK,EAAEA,KAAK;MACZuC,gBAAgB,EAAE,IAAI;MACtBxB,MAAM,EAAE;IACV,CAAC,CAAC;IACF1B,KAAK,CAAC+B,GAAG,CAACe,IAAI,CAAC;EACjB,CAAC;EACDpD,SAAS,CAACtB,IAAI,GAAG,OAAO;EACxB,OAAOsB,SAAS;AAClB,CAAC,CAAC7B,aAAa,CAAC;AAChB,OAAO,SAASsF,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,sBAAsB,CAACtF,UAAU,CAAC;EAC5CqF,SAAS,CAACE,qBAAqB,CAAC5D,SAAS,CAAC;AAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}