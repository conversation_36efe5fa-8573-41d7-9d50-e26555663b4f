{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ChartView from '../../view/Chart.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nimport { getBorderColor, getColor } from './candlestickVisual.js';\nvar SKIP_PROPS = ['color', 'borderColor'];\nvar CandlestickView = /** @class */function (_super) {\n  __extends(CandlestickView, _super);\n  function CandlestickView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CandlestickView.type;\n    return _this;\n  }\n  CandlestickView.prototype.render = function (seriesModel, ecModel, api) {\n    // If there is clipPath created in large mode. Remove it.\n    this.group.removeClipPath();\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    this._updateDrawMode(seriesModel);\n    this._isLargeDraw ? this._renderLarge(seriesModel) : this._renderNormal(seriesModel);\n  };\n  CandlestickView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    this._clear();\n    this._updateDrawMode(seriesModel);\n  };\n  CandlestickView.prototype.incrementalRender = function (params, seriesModel, ecModel, api) {\n    this._progressiveEls = [];\n    this._isLargeDraw ? this._incrementalRenderLarge(params, seriesModel) : this._incrementalRenderNormal(params, seriesModel);\n  };\n  CandlestickView.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  CandlestickView.prototype._updateDrawMode = function (seriesModel) {\n    var isLargeDraw = seriesModel.pipelineContext.large;\n    if (this._isLargeDraw == null || isLargeDraw !== this._isLargeDraw) {\n      this._isLargeDraw = isLargeDraw;\n      this._clear();\n    }\n  };\n  CandlestickView.prototype._renderNormal = function (seriesModel) {\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var group = this.group;\n    var isSimpleBox = data.getLayout('isSimpleBox');\n    var needsClip = seriesModel.get('clip', true);\n    var coord = seriesModel.coordinateSystem;\n    var clipArea = coord.getArea && coord.getArea();\n    // There is no old data only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n    if (!this._data) {\n      group.removeAll();\n    }\n    data.diff(oldData).add(function (newIdx) {\n      if (data.hasValue(newIdx)) {\n        var itemLayout = data.getItemLayout(newIdx);\n        if (needsClip && isNormalBoxClipped(clipArea, itemLayout)) {\n          return;\n        }\n        var el = createNormalBox(itemLayout, newIdx, true);\n        graphic.initProps(el, {\n          shape: {\n            points: itemLayout.ends\n          }\n        }, seriesModel, newIdx);\n        setBoxCommon(el, data, newIdx, isSimpleBox);\n        group.add(el);\n        data.setItemGraphicEl(newIdx, el);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      // Empty data\n      if (!data.hasValue(newIdx)) {\n        group.remove(el);\n        return;\n      }\n      var itemLayout = data.getItemLayout(newIdx);\n      if (needsClip && isNormalBoxClipped(clipArea, itemLayout)) {\n        group.remove(el);\n        return;\n      }\n      if (!el) {\n        el = createNormalBox(itemLayout, newIdx);\n      } else {\n        graphic.updateProps(el, {\n          shape: {\n            points: itemLayout.ends\n          }\n        }, seriesModel, newIdx);\n        saveOldStyle(el);\n      }\n      setBoxCommon(el, data, newIdx, isSimpleBox);\n      group.add(el);\n      data.setItemGraphicEl(newIdx, el);\n    }).remove(function (oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      el && group.remove(el);\n    }).execute();\n    this._data = data;\n  };\n  CandlestickView.prototype._renderLarge = function (seriesModel) {\n    this._clear();\n    createLarge(seriesModel, this.group);\n    var clipPath = seriesModel.get('clip', true) ? createClipPath(seriesModel.coordinateSystem, false, seriesModel) : null;\n    if (clipPath) {\n      this.group.setClipPath(clipPath);\n    } else {\n      this.group.removeClipPath();\n    }\n  };\n  CandlestickView.prototype._incrementalRenderNormal = function (params, seriesModel) {\n    var data = seriesModel.getData();\n    var isSimpleBox = data.getLayout('isSimpleBox');\n    var dataIndex;\n    while ((dataIndex = params.next()) != null) {\n      var itemLayout = data.getItemLayout(dataIndex);\n      var el = createNormalBox(itemLayout, dataIndex);\n      setBoxCommon(el, data, dataIndex, isSimpleBox);\n      el.incremental = true;\n      this.group.add(el);\n      this._progressiveEls.push(el);\n    }\n  };\n  CandlestickView.prototype._incrementalRenderLarge = function (params, seriesModel) {\n    createLarge(seriesModel, this.group, this._progressiveEls, true);\n  };\n  CandlestickView.prototype.remove = function (ecModel) {\n    this._clear();\n  };\n  CandlestickView.prototype._clear = function () {\n    this.group.removeAll();\n    this._data = null;\n  };\n  CandlestickView.type = 'candlestick';\n  return CandlestickView;\n}(ChartView);\nvar NormalBoxPathShape = /** @class */function () {\n  function NormalBoxPathShape() {}\n  return NormalBoxPathShape;\n}();\nvar NormalBoxPath = /** @class */function (_super) {\n  __extends(NormalBoxPath, _super);\n  function NormalBoxPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'normalCandlestickBox';\n    return _this;\n  }\n  NormalBoxPath.prototype.getDefaultShape = function () {\n    return new NormalBoxPathShape();\n  };\n  NormalBoxPath.prototype.buildPath = function (ctx, shape) {\n    var ends = shape.points;\n    if (this.__simpleBox) {\n      ctx.moveTo(ends[4][0], ends[4][1]);\n      ctx.lineTo(ends[6][0], ends[6][1]);\n    } else {\n      ctx.moveTo(ends[0][0], ends[0][1]);\n      ctx.lineTo(ends[1][0], ends[1][1]);\n      ctx.lineTo(ends[2][0], ends[2][1]);\n      ctx.lineTo(ends[3][0], ends[3][1]);\n      ctx.closePath();\n      ctx.moveTo(ends[4][0], ends[4][1]);\n      ctx.lineTo(ends[5][0], ends[5][1]);\n      ctx.moveTo(ends[6][0], ends[6][1]);\n      ctx.lineTo(ends[7][0], ends[7][1]);\n    }\n  };\n  return NormalBoxPath;\n}(Path);\nfunction createNormalBox(itemLayout, dataIndex, isInit) {\n  var ends = itemLayout.ends;\n  return new NormalBoxPath({\n    shape: {\n      points: isInit ? transInit(ends, itemLayout) : ends\n    },\n    z2: 100\n  });\n}\nfunction isNormalBoxClipped(clipArea, itemLayout) {\n  var clipped = true;\n  for (var i = 0; i < itemLayout.ends.length; i++) {\n    // If any point are in the region.\n    if (clipArea.contain(itemLayout.ends[i][0], itemLayout.ends[i][1])) {\n      clipped = false;\n      break;\n    }\n  }\n  return clipped;\n}\nfunction setBoxCommon(el, data, dataIndex, isSimpleBox) {\n  var itemModel = data.getItemModel(dataIndex);\n  el.useStyle(data.getItemVisual(dataIndex, 'style'));\n  el.style.strokeNoScale = true;\n  el.__simpleBox = isSimpleBox;\n  setStatesStylesFromModel(el, itemModel);\n  var sign = data.getItemLayout(dataIndex).sign;\n  zrUtil.each(el.states, function (state, stateName) {\n    var stateModel = itemModel.getModel(stateName);\n    var color = getColor(sign, stateModel);\n    var borderColor = getBorderColor(sign, stateModel) || color;\n    var stateStyle = state.style || (state.style = {});\n    color && (stateStyle.fill = color);\n    borderColor && (stateStyle.stroke = borderColor);\n  });\n  var emphasisModel = itemModel.getModel('emphasis');\n  toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n}\nfunction transInit(points, itemLayout) {\n  return zrUtil.map(points, function (point) {\n    point = point.slice();\n    point[1] = itemLayout.initBaseline;\n    return point;\n  });\n}\nvar LargeBoxPathShape = /** @class */function () {\n  function LargeBoxPathShape() {}\n  return LargeBoxPathShape;\n}();\nvar LargeBoxPath = /** @class */function (_super) {\n  __extends(LargeBoxPath, _super);\n  function LargeBoxPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'largeCandlestickBox';\n    return _this;\n  }\n  LargeBoxPath.prototype.getDefaultShape = function () {\n    return new LargeBoxPathShape();\n  };\n  LargeBoxPath.prototype.buildPath = function (ctx, shape) {\n    // Drawing lines is more efficient than drawing\n    // a whole line or drawing rects.\n    var points = shape.points;\n    for (var i = 0; i < points.length;) {\n      if (this.__sign === points[i++]) {\n        var x = points[i++];\n        ctx.moveTo(x, points[i++]);\n        ctx.lineTo(x, points[i++]);\n      } else {\n        i += 3;\n      }\n    }\n  };\n  return LargeBoxPath;\n}(Path);\nfunction createLarge(seriesModel, group, progressiveEls, incremental) {\n  var data = seriesModel.getData();\n  var largePoints = data.getLayout('largePoints');\n  var elP = new LargeBoxPath({\n    shape: {\n      points: largePoints\n    },\n    __sign: 1,\n    ignoreCoarsePointer: true\n  });\n  group.add(elP);\n  var elN = new LargeBoxPath({\n    shape: {\n      points: largePoints\n    },\n    __sign: -1,\n    ignoreCoarsePointer: true\n  });\n  group.add(elN);\n  var elDoji = new LargeBoxPath({\n    shape: {\n      points: largePoints\n    },\n    __sign: 0,\n    ignoreCoarsePointer: true\n  });\n  group.add(elDoji);\n  setLargeStyle(1, elP, seriesModel, data);\n  setLargeStyle(-1, elN, seriesModel, data);\n  setLargeStyle(0, elDoji, seriesModel, data);\n  if (incremental) {\n    elP.incremental = true;\n    elN.incremental = true;\n  }\n  if (progressiveEls) {\n    progressiveEls.push(elP, elN);\n  }\n}\nfunction setLargeStyle(sign, el, seriesModel, data) {\n  // TODO put in visual?\n  var borderColor = getBorderColor(sign, seriesModel)\n  // Use color for border color by default.\n  || getColor(sign, seriesModel);\n  // Color must be excluded.\n  // Because symbol provide setColor individually to set fill and stroke\n  var itemStyle = seriesModel.getModel('itemStyle').getItemStyle(SKIP_PROPS);\n  el.useStyle(itemStyle);\n  el.style.fill = null;\n  el.style.stroke = borderColor;\n}\nexport default CandlestickView;", "map": {"version": 3, "names": ["__extends", "zrUtil", "ChartView", "graphic", "setStatesStylesFromModel", "toggleHoverEmphasis", "Path", "createClipPath", "saveOldStyle", "getBorderColor", "getColor", "SKIP_PROPS", "CandlestickView", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "seriesModel", "ecModel", "api", "group", "removeClip<PERSON>ath", "_progressiveEls", "_updateDrawMode", "_isLargeDraw", "_renderLarge", "_renderNormal", "incrementalPrepareRender", "_clear", "incrementalRender", "params", "_incrementalRenderLarge", "_incrementalRenderNormal", "eachRendered", "cb", "traverseElements", "isLargeDraw", "pipelineContext", "large", "data", "getData", "oldData", "_data", "isSimpleBox", "getLayout", "needsClip", "get", "coord", "coordinateSystem", "clipArea", "getArea", "removeAll", "diff", "add", "newIdx", "hasValue", "itemLayout", "getItemLayout", "isNormalBoxClipped", "el", "createNormalBox", "initProps", "shape", "points", "ends", "setBoxCommon", "setItemGraphicEl", "update", "oldIdx", "getItemGraphicEl", "remove", "updateProps", "execute", "createLarge", "clipPath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataIndex", "next", "incremental", "push", "NormalBoxPathShape", "NormalBoxPath", "opts", "call", "getDefaultShape", "buildPath", "ctx", "__simpleBox", "moveTo", "lineTo", "closePath", "isInit", "transInit", "z2", "clipped", "i", "length", "contain", "itemModel", "getItemModel", "useStyle", "getItemVisual", "style", "strokeNoScale", "sign", "each", "states", "state", "stateName", "stateModel", "getModel", "color", "borderColor", "stateStyle", "fill", "stroke", "emphasisModel", "map", "point", "slice", "initBaseline", "LargeBoxPathShape", "LargeBoxPath", "__sign", "x", "progressiveEls", "largePoints", "elP", "ignoreCoarsePointer", "elN", "el<PERSON><PERSON>ji", "setLargeStyle", "itemStyle", "getItemStyle"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/candlestick/CandlestickView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ChartView from '../../view/Chart.js';\nimport * as graphic from '../../util/graphic.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport Path from 'zrender/lib/graphic/Path.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nimport { getBorderColor, getColor } from './candlestickVisual.js';\nvar SKIP_PROPS = ['color', 'borderColor'];\nvar CandlestickView = /** @class */function (_super) {\n  __extends(CandlestickView, _super);\n  function CandlestickView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CandlestickView.type;\n    return _this;\n  }\n  CandlestickView.prototype.render = function (seriesModel, ecModel, api) {\n    // If there is clipPath created in large mode. Remove it.\n    this.group.removeClipPath();\n    // Clear previously rendered progressive elements.\n    this._progressiveEls = null;\n    this._updateDrawMode(seriesModel);\n    this._isLargeDraw ? this._renderLarge(seriesModel) : this._renderNormal(seriesModel);\n  };\n  CandlestickView.prototype.incrementalPrepareRender = function (seriesModel, ecModel, api) {\n    this._clear();\n    this._updateDrawMode(seriesModel);\n  };\n  CandlestickView.prototype.incrementalRender = function (params, seriesModel, ecModel, api) {\n    this._progressiveEls = [];\n    this._isLargeDraw ? this._incrementalRenderLarge(params, seriesModel) : this._incrementalRenderNormal(params, seriesModel);\n  };\n  CandlestickView.prototype.eachRendered = function (cb) {\n    graphic.traverseElements(this._progressiveEls || this.group, cb);\n  };\n  CandlestickView.prototype._updateDrawMode = function (seriesModel) {\n    var isLargeDraw = seriesModel.pipelineContext.large;\n    if (this._isLargeDraw == null || isLargeDraw !== this._isLargeDraw) {\n      this._isLargeDraw = isLargeDraw;\n      this._clear();\n    }\n  };\n  CandlestickView.prototype._renderNormal = function (seriesModel) {\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var group = this.group;\n    var isSimpleBox = data.getLayout('isSimpleBox');\n    var needsClip = seriesModel.get('clip', true);\n    var coord = seriesModel.coordinateSystem;\n    var clipArea = coord.getArea && coord.getArea();\n    // There is no old data only when first rendering or switching from\n    // stream mode to normal mode, where previous elements should be removed.\n    if (!this._data) {\n      group.removeAll();\n    }\n    data.diff(oldData).add(function (newIdx) {\n      if (data.hasValue(newIdx)) {\n        var itemLayout = data.getItemLayout(newIdx);\n        if (needsClip && isNormalBoxClipped(clipArea, itemLayout)) {\n          return;\n        }\n        var el = createNormalBox(itemLayout, newIdx, true);\n        graphic.initProps(el, {\n          shape: {\n            points: itemLayout.ends\n          }\n        }, seriesModel, newIdx);\n        setBoxCommon(el, data, newIdx, isSimpleBox);\n        group.add(el);\n        data.setItemGraphicEl(newIdx, el);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      // Empty data\n      if (!data.hasValue(newIdx)) {\n        group.remove(el);\n        return;\n      }\n      var itemLayout = data.getItemLayout(newIdx);\n      if (needsClip && isNormalBoxClipped(clipArea, itemLayout)) {\n        group.remove(el);\n        return;\n      }\n      if (!el) {\n        el = createNormalBox(itemLayout, newIdx);\n      } else {\n        graphic.updateProps(el, {\n          shape: {\n            points: itemLayout.ends\n          }\n        }, seriesModel, newIdx);\n        saveOldStyle(el);\n      }\n      setBoxCommon(el, data, newIdx, isSimpleBox);\n      group.add(el);\n      data.setItemGraphicEl(newIdx, el);\n    }).remove(function (oldIdx) {\n      var el = oldData.getItemGraphicEl(oldIdx);\n      el && group.remove(el);\n    }).execute();\n    this._data = data;\n  };\n  CandlestickView.prototype._renderLarge = function (seriesModel) {\n    this._clear();\n    createLarge(seriesModel, this.group);\n    var clipPath = seriesModel.get('clip', true) ? createClipPath(seriesModel.coordinateSystem, false, seriesModel) : null;\n    if (clipPath) {\n      this.group.setClipPath(clipPath);\n    } else {\n      this.group.removeClipPath();\n    }\n  };\n  CandlestickView.prototype._incrementalRenderNormal = function (params, seriesModel) {\n    var data = seriesModel.getData();\n    var isSimpleBox = data.getLayout('isSimpleBox');\n    var dataIndex;\n    while ((dataIndex = params.next()) != null) {\n      var itemLayout = data.getItemLayout(dataIndex);\n      var el = createNormalBox(itemLayout, dataIndex);\n      setBoxCommon(el, data, dataIndex, isSimpleBox);\n      el.incremental = true;\n      this.group.add(el);\n      this._progressiveEls.push(el);\n    }\n  };\n  CandlestickView.prototype._incrementalRenderLarge = function (params, seriesModel) {\n    createLarge(seriesModel, this.group, this._progressiveEls, true);\n  };\n  CandlestickView.prototype.remove = function (ecModel) {\n    this._clear();\n  };\n  CandlestickView.prototype._clear = function () {\n    this.group.removeAll();\n    this._data = null;\n  };\n  CandlestickView.type = 'candlestick';\n  return CandlestickView;\n}(ChartView);\nvar NormalBoxPathShape = /** @class */function () {\n  function NormalBoxPathShape() {}\n  return NormalBoxPathShape;\n}();\nvar NormalBoxPath = /** @class */function (_super) {\n  __extends(NormalBoxPath, _super);\n  function NormalBoxPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'normalCandlestickBox';\n    return _this;\n  }\n  NormalBoxPath.prototype.getDefaultShape = function () {\n    return new NormalBoxPathShape();\n  };\n  NormalBoxPath.prototype.buildPath = function (ctx, shape) {\n    var ends = shape.points;\n    if (this.__simpleBox) {\n      ctx.moveTo(ends[4][0], ends[4][1]);\n      ctx.lineTo(ends[6][0], ends[6][1]);\n    } else {\n      ctx.moveTo(ends[0][0], ends[0][1]);\n      ctx.lineTo(ends[1][0], ends[1][1]);\n      ctx.lineTo(ends[2][0], ends[2][1]);\n      ctx.lineTo(ends[3][0], ends[3][1]);\n      ctx.closePath();\n      ctx.moveTo(ends[4][0], ends[4][1]);\n      ctx.lineTo(ends[5][0], ends[5][1]);\n      ctx.moveTo(ends[6][0], ends[6][1]);\n      ctx.lineTo(ends[7][0], ends[7][1]);\n    }\n  };\n  return NormalBoxPath;\n}(Path);\nfunction createNormalBox(itemLayout, dataIndex, isInit) {\n  var ends = itemLayout.ends;\n  return new NormalBoxPath({\n    shape: {\n      points: isInit ? transInit(ends, itemLayout) : ends\n    },\n    z2: 100\n  });\n}\nfunction isNormalBoxClipped(clipArea, itemLayout) {\n  var clipped = true;\n  for (var i = 0; i < itemLayout.ends.length; i++) {\n    // If any point are in the region.\n    if (clipArea.contain(itemLayout.ends[i][0], itemLayout.ends[i][1])) {\n      clipped = false;\n      break;\n    }\n  }\n  return clipped;\n}\nfunction setBoxCommon(el, data, dataIndex, isSimpleBox) {\n  var itemModel = data.getItemModel(dataIndex);\n  el.useStyle(data.getItemVisual(dataIndex, 'style'));\n  el.style.strokeNoScale = true;\n  el.__simpleBox = isSimpleBox;\n  setStatesStylesFromModel(el, itemModel);\n  var sign = data.getItemLayout(dataIndex).sign;\n  zrUtil.each(el.states, function (state, stateName) {\n    var stateModel = itemModel.getModel(stateName);\n    var color = getColor(sign, stateModel);\n    var borderColor = getBorderColor(sign, stateModel) || color;\n    var stateStyle = state.style || (state.style = {});\n    color && (stateStyle.fill = color);\n    borderColor && (stateStyle.stroke = borderColor);\n  });\n  var emphasisModel = itemModel.getModel('emphasis');\n  toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n}\nfunction transInit(points, itemLayout) {\n  return zrUtil.map(points, function (point) {\n    point = point.slice();\n    point[1] = itemLayout.initBaseline;\n    return point;\n  });\n}\nvar LargeBoxPathShape = /** @class */function () {\n  function LargeBoxPathShape() {}\n  return LargeBoxPathShape;\n}();\nvar LargeBoxPath = /** @class */function (_super) {\n  __extends(LargeBoxPath, _super);\n  function LargeBoxPath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'largeCandlestickBox';\n    return _this;\n  }\n  LargeBoxPath.prototype.getDefaultShape = function () {\n    return new LargeBoxPathShape();\n  };\n  LargeBoxPath.prototype.buildPath = function (ctx, shape) {\n    // Drawing lines is more efficient than drawing\n    // a whole line or drawing rects.\n    var points = shape.points;\n    for (var i = 0; i < points.length;) {\n      if (this.__sign === points[i++]) {\n        var x = points[i++];\n        ctx.moveTo(x, points[i++]);\n        ctx.lineTo(x, points[i++]);\n      } else {\n        i += 3;\n      }\n    }\n  };\n  return LargeBoxPath;\n}(Path);\nfunction createLarge(seriesModel, group, progressiveEls, incremental) {\n  var data = seriesModel.getData();\n  var largePoints = data.getLayout('largePoints');\n  var elP = new LargeBoxPath({\n    shape: {\n      points: largePoints\n    },\n    __sign: 1,\n    ignoreCoarsePointer: true\n  });\n  group.add(elP);\n  var elN = new LargeBoxPath({\n    shape: {\n      points: largePoints\n    },\n    __sign: -1,\n    ignoreCoarsePointer: true\n  });\n  group.add(elN);\n  var elDoji = new LargeBoxPath({\n    shape: {\n      points: largePoints\n    },\n    __sign: 0,\n    ignoreCoarsePointer: true\n  });\n  group.add(elDoji);\n  setLargeStyle(1, elP, seriesModel, data);\n  setLargeStyle(-1, elN, seriesModel, data);\n  setLargeStyle(0, elDoji, seriesModel, data);\n  if (incremental) {\n    elP.incremental = true;\n    elN.incremental = true;\n  }\n  if (progressiveEls) {\n    progressiveEls.push(elP, elN);\n  }\n}\nfunction setLargeStyle(sign, el, seriesModel, data) {\n  // TODO put in visual?\n  var borderColor = getBorderColor(sign, seriesModel)\n  // Use color for border color by default.\n  || getColor(sign, seriesModel);\n  // Color must be excluded.\n  // Because symbol provide setColor individually to set fill and stroke\n  var itemStyle = seriesModel.getModel('itemStyle').getItemStyle(SKIP_PROPS);\n  el.useStyle(itemStyle);\n  el.style.fill = null;\n  el.style.stroke = borderColor;\n}\nexport default CandlestickView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,wBAAwB,EAAEC,mBAAmB,QAAQ,sBAAsB;AACpF,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,SAASC,cAAc,QAAQ,yCAAyC;AACxE,SAASC,YAAY,QAAQ,oCAAoC;AACjE,SAASC,cAAc,EAAEC,QAAQ,QAAQ,wBAAwB;AACjE,IAAIC,UAAU,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC;AACzC,IAAIC,eAAe,GAAG,aAAa,UAAUC,MAAM,EAAE;EACnDb,SAAS,CAACY,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAAA,EAAG;IACzB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,eAAe,CAACK,IAAI;IACjC,OAAOH,KAAK;EACd;EACAF,eAAe,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACtE;IACA,IAAI,CAACC,KAAK,CAACC,cAAc,CAAC,CAAC;IAC3B;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,eAAe,CAACN,WAAW,CAAC;IACjC,IAAI,CAACO,YAAY,GAAG,IAAI,CAACC,YAAY,CAACR,WAAW,CAAC,GAAG,IAAI,CAACS,aAAa,CAACT,WAAW,CAAC;EACtF,CAAC;EACDR,eAAe,CAACM,SAAS,CAACY,wBAAwB,GAAG,UAAUV,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACxF,IAAI,CAACS,MAAM,CAAC,CAAC;IACb,IAAI,CAACL,eAAe,CAACN,WAAW,CAAC;EACnC,CAAC;EACDR,eAAe,CAACM,SAAS,CAACc,iBAAiB,GAAG,UAAUC,MAAM,EAAEb,WAAW,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACzF,IAAI,CAACG,eAAe,GAAG,EAAE;IACzB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACO,uBAAuB,CAACD,MAAM,EAAEb,WAAW,CAAC,GAAG,IAAI,CAACe,wBAAwB,CAACF,MAAM,EAAEb,WAAW,CAAC;EAC5H,CAAC;EACDR,eAAe,CAACM,SAAS,CAACkB,YAAY,GAAG,UAAUC,EAAE,EAAE;IACrDlC,OAAO,CAACmC,gBAAgB,CAAC,IAAI,CAACb,eAAe,IAAI,IAAI,CAACF,KAAK,EAAEc,EAAE,CAAC;EAClE,CAAC;EACDzB,eAAe,CAACM,SAAS,CAACQ,eAAe,GAAG,UAAUN,WAAW,EAAE;IACjE,IAAImB,WAAW,GAAGnB,WAAW,CAACoB,eAAe,CAACC,KAAK;IACnD,IAAI,IAAI,CAACd,YAAY,IAAI,IAAI,IAAIY,WAAW,KAAK,IAAI,CAACZ,YAAY,EAAE;MAClE,IAAI,CAACA,YAAY,GAAGY,WAAW;MAC/B,IAAI,CAACR,MAAM,CAAC,CAAC;IACf;EACF,CAAC;EACDnB,eAAe,CAACM,SAAS,CAACW,aAAa,GAAG,UAAUT,WAAW,EAAE;IAC/D,IAAIsB,IAAI,GAAGtB,WAAW,CAACuB,OAAO,CAAC,CAAC;IAChC,IAAIC,OAAO,GAAG,IAAI,CAACC,KAAK;IACxB,IAAItB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIuB,WAAW,GAAGJ,IAAI,CAACK,SAAS,CAAC,aAAa,CAAC;IAC/C,IAAIC,SAAS,GAAG5B,WAAW,CAAC6B,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;IAC7C,IAAIC,KAAK,GAAG9B,WAAW,CAAC+B,gBAAgB;IACxC,IAAIC,QAAQ,GAAGF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACG,OAAO,CAAC,CAAC;IAC/C;IACA;IACA,IAAI,CAAC,IAAI,CAACR,KAAK,EAAE;MACftB,KAAK,CAAC+B,SAAS,CAAC,CAAC;IACnB;IACAZ,IAAI,CAACa,IAAI,CAACX,OAAO,CAAC,CAACY,GAAG,CAAC,UAAUC,MAAM,EAAE;MACvC,IAAIf,IAAI,CAACgB,QAAQ,CAACD,MAAM,CAAC,EAAE;QACzB,IAAIE,UAAU,GAAGjB,IAAI,CAACkB,aAAa,CAACH,MAAM,CAAC;QAC3C,IAAIT,SAAS,IAAIa,kBAAkB,CAACT,QAAQ,EAAEO,UAAU,CAAC,EAAE;UACzD;QACF;QACA,IAAIG,EAAE,GAAGC,eAAe,CAACJ,UAAU,EAAEF,MAAM,EAAE,IAAI,CAAC;QAClDtD,OAAO,CAAC6D,SAAS,CAACF,EAAE,EAAE;UACpBG,KAAK,EAAE;YACLC,MAAM,EAAEP,UAAU,CAACQ;UACrB;QACF,CAAC,EAAE/C,WAAW,EAAEqC,MAAM,CAAC;QACvBW,YAAY,CAACN,EAAE,EAAEpB,IAAI,EAAEe,MAAM,EAAEX,WAAW,CAAC;QAC3CvB,KAAK,CAACiC,GAAG,CAACM,EAAE,CAAC;QACbpB,IAAI,CAAC2B,gBAAgB,CAACZ,MAAM,EAAEK,EAAE,CAAC;MACnC;IACF,CAAC,CAAC,CAACQ,MAAM,CAAC,UAAUb,MAAM,EAAEc,MAAM,EAAE;MAClC,IAAIT,EAAE,GAAGlB,OAAO,CAAC4B,gBAAgB,CAACD,MAAM,CAAC;MACzC;MACA,IAAI,CAAC7B,IAAI,CAACgB,QAAQ,CAACD,MAAM,CAAC,EAAE;QAC1BlC,KAAK,CAACkD,MAAM,CAACX,EAAE,CAAC;QAChB;MACF;MACA,IAAIH,UAAU,GAAGjB,IAAI,CAACkB,aAAa,CAACH,MAAM,CAAC;MAC3C,IAAIT,SAAS,IAAIa,kBAAkB,CAACT,QAAQ,EAAEO,UAAU,CAAC,EAAE;QACzDpC,KAAK,CAACkD,MAAM,CAACX,EAAE,CAAC;QAChB;MACF;MACA,IAAI,CAACA,EAAE,EAAE;QACPA,EAAE,GAAGC,eAAe,CAACJ,UAAU,EAAEF,MAAM,CAAC;MAC1C,CAAC,MAAM;QACLtD,OAAO,CAACuE,WAAW,CAACZ,EAAE,EAAE;UACtBG,KAAK,EAAE;YACLC,MAAM,EAAEP,UAAU,CAACQ;UACrB;QACF,CAAC,EAAE/C,WAAW,EAAEqC,MAAM,CAAC;QACvBjD,YAAY,CAACsD,EAAE,CAAC;MAClB;MACAM,YAAY,CAACN,EAAE,EAAEpB,IAAI,EAAEe,MAAM,EAAEX,WAAW,CAAC;MAC3CvB,KAAK,CAACiC,GAAG,CAACM,EAAE,CAAC;MACbpB,IAAI,CAAC2B,gBAAgB,CAACZ,MAAM,EAAEK,EAAE,CAAC;IACnC,CAAC,CAAC,CAACW,MAAM,CAAC,UAAUF,MAAM,EAAE;MAC1B,IAAIT,EAAE,GAAGlB,OAAO,CAAC4B,gBAAgB,CAACD,MAAM,CAAC;MACzCT,EAAE,IAAIvC,KAAK,CAACkD,MAAM,CAACX,EAAE,CAAC;IACxB,CAAC,CAAC,CAACa,OAAO,CAAC,CAAC;IACZ,IAAI,CAAC9B,KAAK,GAAGH,IAAI;EACnB,CAAC;EACD9B,eAAe,CAACM,SAAS,CAACU,YAAY,GAAG,UAAUR,WAAW,EAAE;IAC9D,IAAI,CAACW,MAAM,CAAC,CAAC;IACb6C,WAAW,CAACxD,WAAW,EAAE,IAAI,CAACG,KAAK,CAAC;IACpC,IAAIsD,QAAQ,GAAGzD,WAAW,CAAC6B,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG1C,cAAc,CAACa,WAAW,CAAC+B,gBAAgB,EAAE,KAAK,EAAE/B,WAAW,CAAC,GAAG,IAAI;IACtH,IAAIyD,QAAQ,EAAE;MACZ,IAAI,CAACtD,KAAK,CAACuD,WAAW,CAACD,QAAQ,CAAC;IAClC,CAAC,MAAM;MACL,IAAI,CAACtD,KAAK,CAACC,cAAc,CAAC,CAAC;IAC7B;EACF,CAAC;EACDZ,eAAe,CAACM,SAAS,CAACiB,wBAAwB,GAAG,UAAUF,MAAM,EAAEb,WAAW,EAAE;IAClF,IAAIsB,IAAI,GAAGtB,WAAW,CAACuB,OAAO,CAAC,CAAC;IAChC,IAAIG,WAAW,GAAGJ,IAAI,CAACK,SAAS,CAAC,aAAa,CAAC;IAC/C,IAAIgC,SAAS;IACb,OAAO,CAACA,SAAS,GAAG9C,MAAM,CAAC+C,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;MAC1C,IAAIrB,UAAU,GAAGjB,IAAI,CAACkB,aAAa,CAACmB,SAAS,CAAC;MAC9C,IAAIjB,EAAE,GAAGC,eAAe,CAACJ,UAAU,EAAEoB,SAAS,CAAC;MAC/CX,YAAY,CAACN,EAAE,EAAEpB,IAAI,EAAEqC,SAAS,EAAEjC,WAAW,CAAC;MAC9CgB,EAAE,CAACmB,WAAW,GAAG,IAAI;MACrB,IAAI,CAAC1D,KAAK,CAACiC,GAAG,CAACM,EAAE,CAAC;MAClB,IAAI,CAACrC,eAAe,CAACyD,IAAI,CAACpB,EAAE,CAAC;IAC/B;EACF,CAAC;EACDlD,eAAe,CAACM,SAAS,CAACgB,uBAAuB,GAAG,UAAUD,MAAM,EAAEb,WAAW,EAAE;IACjFwD,WAAW,CAACxD,WAAW,EAAE,IAAI,CAACG,KAAK,EAAE,IAAI,CAACE,eAAe,EAAE,IAAI,CAAC;EAClE,CAAC;EACDb,eAAe,CAACM,SAAS,CAACuD,MAAM,GAAG,UAAUpD,OAAO,EAAE;IACpD,IAAI,CAACU,MAAM,CAAC,CAAC;EACf,CAAC;EACDnB,eAAe,CAACM,SAAS,CAACa,MAAM,GAAG,YAAY;IAC7C,IAAI,CAACR,KAAK,CAAC+B,SAAS,CAAC,CAAC;IACtB,IAAI,CAACT,KAAK,GAAG,IAAI;EACnB,CAAC;EACDjC,eAAe,CAACK,IAAI,GAAG,aAAa;EACpC,OAAOL,eAAe;AACxB,CAAC,CAACV,SAAS,CAAC;AACZ,IAAIiF,kBAAkB,GAAG,aAAa,YAAY;EAChD,SAASA,kBAAkBA,CAAA,EAAG,CAAC;EAC/B,OAAOA,kBAAkB;AAC3B,CAAC,CAAC,CAAC;AACH,IAAIC,aAAa,GAAG,aAAa,UAAUvE,MAAM,EAAE;EACjDb,SAAS,CAACoF,aAAa,EAAEvE,MAAM,CAAC;EAChC,SAASuE,aAAaA,CAACC,IAAI,EAAE;IAC3B,IAAIvE,KAAK,GAAGD,MAAM,CAACyE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;IAC3CvE,KAAK,CAACG,IAAI,GAAG,sBAAsB;IACnC,OAAOH,KAAK;EACd;EACAsE,aAAa,CAAClE,SAAS,CAACqE,eAAe,GAAG,YAAY;IACpD,OAAO,IAAIJ,kBAAkB,CAAC,CAAC;EACjC,CAAC;EACDC,aAAa,CAAClE,SAAS,CAACsE,SAAS,GAAG,UAAUC,GAAG,EAAExB,KAAK,EAAE;IACxD,IAAIE,IAAI,GAAGF,KAAK,CAACC,MAAM;IACvB,IAAI,IAAI,CAACwB,WAAW,EAAE;MACpBD,GAAG,CAACE,MAAM,CAACxB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCsB,GAAG,CAACG,MAAM,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,MAAM;MACLsB,GAAG,CAACE,MAAM,CAACxB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCsB,GAAG,CAACG,MAAM,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCsB,GAAG,CAACG,MAAM,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCsB,GAAG,CAACG,MAAM,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCsB,GAAG,CAACI,SAAS,CAAC,CAAC;MACfJ,GAAG,CAACE,MAAM,CAACxB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCsB,GAAG,CAACG,MAAM,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCsB,GAAG,CAACE,MAAM,CAACxB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCsB,GAAG,CAACG,MAAM,CAACzB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC;EACF,CAAC;EACD,OAAOiB,aAAa;AACtB,CAAC,CAAC9E,IAAI,CAAC;AACP,SAASyD,eAAeA,CAACJ,UAAU,EAAEoB,SAAS,EAAEe,MAAM,EAAE;EACtD,IAAI3B,IAAI,GAAGR,UAAU,CAACQ,IAAI;EAC1B,OAAO,IAAIiB,aAAa,CAAC;IACvBnB,KAAK,EAAE;MACLC,MAAM,EAAE4B,MAAM,GAAGC,SAAS,CAAC5B,IAAI,EAAER,UAAU,CAAC,GAAGQ;IACjD,CAAC;IACD6B,EAAE,EAAE;EACN,CAAC,CAAC;AACJ;AACA,SAASnC,kBAAkBA,CAACT,QAAQ,EAAEO,UAAU,EAAE;EAChD,IAAIsC,OAAO,GAAG,IAAI;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvC,UAAU,CAACQ,IAAI,CAACgC,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/C;IACA,IAAI9C,QAAQ,CAACgD,OAAO,CAACzC,UAAU,CAACQ,IAAI,CAAC+B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEvC,UAAU,CAACQ,IAAI,CAAC+B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAClED,OAAO,GAAG,KAAK;MACf;IACF;EACF;EACA,OAAOA,OAAO;AAChB;AACA,SAAS7B,YAAYA,CAACN,EAAE,EAAEpB,IAAI,EAAEqC,SAAS,EAAEjC,WAAW,EAAE;EACtD,IAAIuD,SAAS,GAAG3D,IAAI,CAAC4D,YAAY,CAACvB,SAAS,CAAC;EAC5CjB,EAAE,CAACyC,QAAQ,CAAC7D,IAAI,CAAC8D,aAAa,CAACzB,SAAS,EAAE,OAAO,CAAC,CAAC;EACnDjB,EAAE,CAAC2C,KAAK,CAACC,aAAa,GAAG,IAAI;EAC7B5C,EAAE,CAAC4B,WAAW,GAAG5C,WAAW;EAC5B1C,wBAAwB,CAAC0D,EAAE,EAAEuC,SAAS,CAAC;EACvC,IAAIM,IAAI,GAAGjE,IAAI,CAACkB,aAAa,CAACmB,SAAS,CAAC,CAAC4B,IAAI;EAC7C1G,MAAM,CAAC2G,IAAI,CAAC9C,EAAE,CAAC+C,MAAM,EAAE,UAAUC,KAAK,EAAEC,SAAS,EAAE;IACjD,IAAIC,UAAU,GAAGX,SAAS,CAACY,QAAQ,CAACF,SAAS,CAAC;IAC9C,IAAIG,KAAK,GAAGxG,QAAQ,CAACiG,IAAI,EAAEK,UAAU,CAAC;IACtC,IAAIG,WAAW,GAAG1G,cAAc,CAACkG,IAAI,EAAEK,UAAU,CAAC,IAAIE,KAAK;IAC3D,IAAIE,UAAU,GAAGN,KAAK,CAACL,KAAK,KAAKK,KAAK,CAACL,KAAK,GAAG,CAAC,CAAC,CAAC;IAClDS,KAAK,KAAKE,UAAU,CAACC,IAAI,GAAGH,KAAK,CAAC;IAClCC,WAAW,KAAKC,UAAU,CAACE,MAAM,GAAGH,WAAW,CAAC;EAClD,CAAC,CAAC;EACF,IAAII,aAAa,GAAGlB,SAAS,CAACY,QAAQ,CAAC,UAAU,CAAC;EAClD5G,mBAAmB,CAACyD,EAAE,EAAEyD,aAAa,CAACtE,GAAG,CAAC,OAAO,CAAC,EAAEsE,aAAa,CAACtE,GAAG,CAAC,WAAW,CAAC,EAAEsE,aAAa,CAACtE,GAAG,CAAC,UAAU,CAAC,CAAC;AACpH;AACA,SAAS8C,SAASA,CAAC7B,MAAM,EAAEP,UAAU,EAAE;EACrC,OAAO1D,MAAM,CAACuH,GAAG,CAACtD,MAAM,EAAE,UAAUuD,KAAK,EAAE;IACzCA,KAAK,GAAGA,KAAK,CAACC,KAAK,CAAC,CAAC;IACrBD,KAAK,CAAC,CAAC,CAAC,GAAG9D,UAAU,CAACgE,YAAY;IAClC,OAAOF,KAAK;EACd,CAAC,CAAC;AACJ;AACA,IAAIG,iBAAiB,GAAG,aAAa,YAAY;EAC/C,SAASA,iBAAiBA,CAAA,EAAG,CAAC;EAC9B,OAAOA,iBAAiB;AAC1B,CAAC,CAAC,CAAC;AACH,IAAIC,YAAY,GAAG,aAAa,UAAUhH,MAAM,EAAE;EAChDb,SAAS,CAAC6H,YAAY,EAAEhH,MAAM,CAAC;EAC/B,SAASgH,YAAYA,CAACxC,IAAI,EAAE;IAC1B,IAAIvE,KAAK,GAAGD,MAAM,CAACyE,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC,IAAI,IAAI;IAC3CvE,KAAK,CAACG,IAAI,GAAG,qBAAqB;IAClC,OAAOH,KAAK;EACd;EACA+G,YAAY,CAAC3G,SAAS,CAACqE,eAAe,GAAG,YAAY;IACnD,OAAO,IAAIqC,iBAAiB,CAAC,CAAC;EAChC,CAAC;EACDC,YAAY,CAAC3G,SAAS,CAACsE,SAAS,GAAG,UAAUC,GAAG,EAAExB,KAAK,EAAE;IACvD;IACA;IACA,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhC,MAAM,CAACiC,MAAM,GAAG;MAClC,IAAI,IAAI,CAAC2B,MAAM,KAAK5D,MAAM,CAACgC,CAAC,EAAE,CAAC,EAAE;QAC/B,IAAI6B,CAAC,GAAG7D,MAAM,CAACgC,CAAC,EAAE,CAAC;QACnBT,GAAG,CAACE,MAAM,CAACoC,CAAC,EAAE7D,MAAM,CAACgC,CAAC,EAAE,CAAC,CAAC;QAC1BT,GAAG,CAACG,MAAM,CAACmC,CAAC,EAAE7D,MAAM,CAACgC,CAAC,EAAE,CAAC,CAAC;MAC5B,CAAC,MAAM;QACLA,CAAC,IAAI,CAAC;MACR;IACF;EACF,CAAC;EACD,OAAO2B,YAAY;AACrB,CAAC,CAACvH,IAAI,CAAC;AACP,SAASsE,WAAWA,CAACxD,WAAW,EAAEG,KAAK,EAAEyG,cAAc,EAAE/C,WAAW,EAAE;EACpE,IAAIvC,IAAI,GAAGtB,WAAW,CAACuB,OAAO,CAAC,CAAC;EAChC,IAAIsF,WAAW,GAAGvF,IAAI,CAACK,SAAS,CAAC,aAAa,CAAC;EAC/C,IAAImF,GAAG,GAAG,IAAIL,YAAY,CAAC;IACzB5D,KAAK,EAAE;MACLC,MAAM,EAAE+D;IACV,CAAC;IACDH,MAAM,EAAE,CAAC;IACTK,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF5G,KAAK,CAACiC,GAAG,CAAC0E,GAAG,CAAC;EACd,IAAIE,GAAG,GAAG,IAAIP,YAAY,CAAC;IACzB5D,KAAK,EAAE;MACLC,MAAM,EAAE+D;IACV,CAAC;IACDH,MAAM,EAAE,CAAC,CAAC;IACVK,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF5G,KAAK,CAACiC,GAAG,CAAC4E,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,IAAIR,YAAY,CAAC;IAC5B5D,KAAK,EAAE;MACLC,MAAM,EAAE+D;IACV,CAAC;IACDH,MAAM,EAAE,CAAC;IACTK,mBAAmB,EAAE;EACvB,CAAC,CAAC;EACF5G,KAAK,CAACiC,GAAG,CAAC6E,MAAM,CAAC;EACjBC,aAAa,CAAC,CAAC,EAAEJ,GAAG,EAAE9G,WAAW,EAAEsB,IAAI,CAAC;EACxC4F,aAAa,CAAC,CAAC,CAAC,EAAEF,GAAG,EAAEhH,WAAW,EAAEsB,IAAI,CAAC;EACzC4F,aAAa,CAAC,CAAC,EAAED,MAAM,EAAEjH,WAAW,EAAEsB,IAAI,CAAC;EAC3C,IAAIuC,WAAW,EAAE;IACfiD,GAAG,CAACjD,WAAW,GAAG,IAAI;IACtBmD,GAAG,CAACnD,WAAW,GAAG,IAAI;EACxB;EACA,IAAI+C,cAAc,EAAE;IAClBA,cAAc,CAAC9C,IAAI,CAACgD,GAAG,EAAEE,GAAG,CAAC;EAC/B;AACF;AACA,SAASE,aAAaA,CAAC3B,IAAI,EAAE7C,EAAE,EAAE1C,WAAW,EAAEsB,IAAI,EAAE;EAClD;EACA,IAAIyE,WAAW,GAAG1G,cAAc,CAACkG,IAAI,EAAEvF,WAAW;EAClD;EAAA,GACGV,QAAQ,CAACiG,IAAI,EAAEvF,WAAW,CAAC;EAC9B;EACA;EACA,IAAImH,SAAS,GAAGnH,WAAW,CAAC6F,QAAQ,CAAC,WAAW,CAAC,CAACuB,YAAY,CAAC7H,UAAU,CAAC;EAC1EmD,EAAE,CAACyC,QAAQ,CAACgC,SAAS,CAAC;EACtBzE,EAAE,CAAC2C,KAAK,CAACY,IAAI,GAAG,IAAI;EACpBvD,EAAE,CAAC2C,KAAK,CAACa,MAAM,GAAGH,WAAW;AAC/B;AACA,eAAevG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}