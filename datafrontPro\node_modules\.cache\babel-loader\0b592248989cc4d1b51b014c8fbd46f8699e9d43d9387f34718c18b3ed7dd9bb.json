{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner, normalizeToArray } from '../../util/model.js';\nvar innerColor = makeInner();\nvar innerDecal = makeInner();\nvar PaletteMixin = /** @class */function () {\n  function PaletteMixin() {}\n  PaletteMixin.prototype.getColorFromPalette = function (name, scope, requestNum) {\n    var defaultPalette = normalizeToArray(this.get('color', true));\n    var layeredPalette = this.get('colorLayer', true);\n    return getFromPalette(this, innerColor, defaultPalette, layeredPalette, name, scope, requestNum);\n  };\n  PaletteMixin.prototype.clearColorPalette = function () {\n    clearPalette(this, innerColor);\n  };\n  return PaletteMixin;\n}();\nexport function getDecalFromPalette(ecModel, name, scope, requestNum) {\n  var defaultDecals = normalizeToArray(ecModel.get(['aria', 'decal', 'decals']));\n  return getFromPalette(ecModel, innerDecal, defaultDecals, null, name, scope, requestNum);\n}\nfunction getNearestPalette(palettes, requestColorNum) {\n  var paletteNum = palettes.length;\n  // TODO palettes must be in order\n  for (var i = 0; i < paletteNum; i++) {\n    if (palettes[i].length > requestColorNum) {\n      return palettes[i];\n    }\n  }\n  return palettes[paletteNum - 1];\n}\n/**\r\n * @param name MUST NOT be null/undefined. Otherwise call this function\r\n *             twise with the same parameters will get different result.\r\n * @param scope default this.\r\n * @return Can be null/undefined\r\n */\nfunction getFromPalette(that, inner, defaultPalette, layeredPalette, name, scope, requestNum) {\n  scope = scope || that;\n  var scopeFields = inner(scope);\n  var paletteIdx = scopeFields.paletteIdx || 0;\n  var paletteNameMap = scopeFields.paletteNameMap = scopeFields.paletteNameMap || {};\n  // Use `hasOwnProperty` to avoid conflict with Object.prototype.\n  if (paletteNameMap.hasOwnProperty(name)) {\n    return paletteNameMap[name];\n  }\n  var palette = requestNum == null || !layeredPalette ? defaultPalette : getNearestPalette(layeredPalette, requestNum);\n  // In case can't find in layered color palette.\n  palette = palette || defaultPalette;\n  if (!palette || !palette.length) {\n    return;\n  }\n  var pickedPaletteItem = palette[paletteIdx];\n  if (name) {\n    paletteNameMap[name] = pickedPaletteItem;\n  }\n  scopeFields.paletteIdx = (paletteIdx + 1) % palette.length;\n  return pickedPaletteItem;\n}\nfunction clearPalette(that, inner) {\n  inner(that).paletteIdx = 0;\n  inner(that).paletteNameMap = {};\n}\nexport { PaletteMixin };", "map": {"version": 3, "names": ["makeInner", "normalizeToArray", "innerColor", "innerDecal", "Palette<PERSON><PERSON>in", "prototype", "getColorFromPalette", "name", "scope", "requestNum", "defaultPalette", "get", "layeredPalette", "getFromPalette", "clearColorPalette", "clearPalette", "getDecalFromPalette", "ecModel", "defaultDecals", "getNearestPalette", "palettes", "requestColorNum", "paletteNum", "length", "i", "that", "inner", "scopeFields", "paletteIdx", "paletteNameMap", "hasOwnProperty", "palette", "pickedPaletteItem"], "sources": ["E:/AllProject/datafront/node_modules/echarts/lib/model/mixin/palette.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner, normalizeToArray } from '../../util/model.js';\nvar innerColor = makeInner();\nvar innerDecal = makeInner();\nvar PaletteMixin = /** @class */function () {\n  function PaletteMixin() {}\n  PaletteMixin.prototype.getColorFromPalette = function (name, scope, requestNum) {\n    var defaultPalette = normalizeToArray(this.get('color', true));\n    var layeredPalette = this.get('colorLayer', true);\n    return getFromPalette(this, innerColor, defaultPalette, layeredPalette, name, scope, requestNum);\n  };\n  PaletteMixin.prototype.clearColorPalette = function () {\n    clearPalette(this, innerColor);\n  };\n  return PaletteMixin;\n}();\nexport function getDecalFromPalette(ecModel, name, scope, requestNum) {\n  var defaultDecals = normalizeToArray(ecModel.get(['aria', 'decal', 'decals']));\n  return getFromPalette(ecModel, innerDecal, defaultDecals, null, name, scope, requestNum);\n}\nfunction getNearestPalette(palettes, requestColorNum) {\n  var paletteNum = palettes.length;\n  // TODO palettes must be in order\n  for (var i = 0; i < paletteNum; i++) {\n    if (palettes[i].length > requestColorNum) {\n      return palettes[i];\n    }\n  }\n  return palettes[paletteNum - 1];\n}\n/**\r\n * @param name MUST NOT be null/undefined. Otherwise call this function\r\n *             twise with the same parameters will get different result.\r\n * @param scope default this.\r\n * @return Can be null/undefined\r\n */\nfunction getFromPalette(that, inner, defaultPalette, layeredPalette, name, scope, requestNum) {\n  scope = scope || that;\n  var scopeFields = inner(scope);\n  var paletteIdx = scopeFields.paletteIdx || 0;\n  var paletteNameMap = scopeFields.paletteNameMap = scopeFields.paletteNameMap || {};\n  // Use `hasOwnProperty` to avoid conflict with Object.prototype.\n  if (paletteNameMap.hasOwnProperty(name)) {\n    return paletteNameMap[name];\n  }\n  var palette = requestNum == null || !layeredPalette ? defaultPalette : getNearestPalette(layeredPalette, requestNum);\n  // In case can't find in layered color palette.\n  palette = palette || defaultPalette;\n  if (!palette || !palette.length) {\n    return;\n  }\n  var pickedPaletteItem = palette[paletteIdx];\n  if (name) {\n    paletteNameMap[name] = pickedPaletteItem;\n  }\n  scopeFields.paletteIdx = (paletteIdx + 1) % palette.length;\n  return pickedPaletteItem;\n}\nfunction clearPalette(that, inner) {\n  inner(that).paletteIdx = 0;\n  inner(that).paletteNameMap = {};\n}\nexport { PaletteMixin };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,gBAAgB,QAAQ,qBAAqB;AACjE,IAAIC,UAAU,GAAGF,SAAS,CAAC,CAAC;AAC5B,IAAIG,UAAU,GAAGH,SAAS,CAAC,CAAC;AAC5B,IAAII,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG,CAAC;EACzBA,YAAY,CAACC,SAAS,CAACC,mBAAmB,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAE;IAC9E,IAAIC,cAAc,GAAGT,gBAAgB,CAAC,IAAI,CAACU,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC9D,IAAIC,cAAc,GAAG,IAAI,CAACD,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC;IACjD,OAAOE,cAAc,CAAC,IAAI,EAAEX,UAAU,EAAEQ,cAAc,EAAEE,cAAc,EAAEL,IAAI,EAAEC,KAAK,EAAEC,UAAU,CAAC;EAClG,CAAC;EACDL,YAAY,CAACC,SAAS,CAACS,iBAAiB,GAAG,YAAY;IACrDC,YAAY,CAAC,IAAI,EAAEb,UAAU,CAAC;EAChC,CAAC;EACD,OAAOE,YAAY;AACrB,CAAC,CAAC,CAAC;AACH,OAAO,SAASY,mBAAmBA,CAACC,OAAO,EAAEV,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACpE,IAAIS,aAAa,GAAGjB,gBAAgB,CAACgB,OAAO,CAACN,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;EAC9E,OAAOE,cAAc,CAACI,OAAO,EAAEd,UAAU,EAAEe,aAAa,EAAE,IAAI,EAAEX,IAAI,EAAEC,KAAK,EAAEC,UAAU,CAAC;AAC1F;AACA,SAASU,iBAAiBA,CAACC,QAAQ,EAAEC,eAAe,EAAE;EACpD,IAAIC,UAAU,GAAGF,QAAQ,CAACG,MAAM;EAChC;EACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,UAAU,EAAEE,CAAC,EAAE,EAAE;IACnC,IAAIJ,QAAQ,CAACI,CAAC,CAAC,CAACD,MAAM,GAAGF,eAAe,EAAE;MACxC,OAAOD,QAAQ,CAACI,CAAC,CAAC;IACpB;EACF;EACA,OAAOJ,QAAQ,CAACE,UAAU,GAAG,CAAC,CAAC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAST,cAAcA,CAACY,IAAI,EAAEC,KAAK,EAAEhB,cAAc,EAAEE,cAAc,EAAEL,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAE;EAC5FD,KAAK,GAAGA,KAAK,IAAIiB,IAAI;EACrB,IAAIE,WAAW,GAAGD,KAAK,CAAClB,KAAK,CAAC;EAC9B,IAAIoB,UAAU,GAAGD,WAAW,CAACC,UAAU,IAAI,CAAC;EAC5C,IAAIC,cAAc,GAAGF,WAAW,CAACE,cAAc,GAAGF,WAAW,CAACE,cAAc,IAAI,CAAC,CAAC;EAClF;EACA,IAAIA,cAAc,CAACC,cAAc,CAACvB,IAAI,CAAC,EAAE;IACvC,OAAOsB,cAAc,CAACtB,IAAI,CAAC;EAC7B;EACA,IAAIwB,OAAO,GAAGtB,UAAU,IAAI,IAAI,IAAI,CAACG,cAAc,GAAGF,cAAc,GAAGS,iBAAiB,CAACP,cAAc,EAAEH,UAAU,CAAC;EACpH;EACAsB,OAAO,GAAGA,OAAO,IAAIrB,cAAc;EACnC,IAAI,CAACqB,OAAO,IAAI,CAACA,OAAO,CAACR,MAAM,EAAE;IAC/B;EACF;EACA,IAAIS,iBAAiB,GAAGD,OAAO,CAACH,UAAU,CAAC;EAC3C,IAAIrB,IAAI,EAAE;IACRsB,cAAc,CAACtB,IAAI,CAAC,GAAGyB,iBAAiB;EAC1C;EACAL,WAAW,CAACC,UAAU,GAAG,CAACA,UAAU,GAAG,CAAC,IAAIG,OAAO,CAACR,MAAM;EAC1D,OAAOS,iBAAiB;AAC1B;AACA,SAASjB,YAAYA,CAACU,IAAI,EAAEC,KAAK,EAAE;EACjCA,KAAK,CAACD,IAAI,CAAC,CAACG,UAAU,GAAG,CAAC;EAC1BF,KAAK,CAACD,IAAI,CAAC,CAACI,cAAc,GAAG,CAAC,CAAC;AACjC;AACA,SAASzB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}