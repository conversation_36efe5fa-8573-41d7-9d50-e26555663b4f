{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { dataApi } from './api/index.js';\nimport { v4 as uuidv4 } from 'uuid';\nimport axios from 'axios';\nimport ChartDisplay from './components/ChartDisplay.vue';\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas';\nimport { jsPDF } from 'jspdf';\nimport * as echarts from 'echarts';\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      // 原始树结构\n      datasets: [],\n      // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [],\n      // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false,\n      // 新增：控制原始响应弹出层\n      lastRawResponse: '',\n      // 新增：存储最后收到的原始响应\n      drawer: false,\n      //抽屉展示\n      direction: 'rtl',\n      //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      apiToken: 'eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2OTEyYzNiLTQ5ZmMtNDAyMC1iMzRiLWE3YWJlNmY1MTI0MSJ9.J4QCC6qh2GZ7ENDHuZQWQ1GvGOQ1HOr8gW34KXIHV9N_73Jppx-qmEwpq0AHlXM3DUjd5EAlpIVAvzJygm_ldg',\n      // 示例token，实际应该从登录后存储\n      currentAnalysisDataset: null,\n      // 存储当前用于智能分析的数据集信息\n      innerDrawer: false,\n      tableList: [],\n      exportingAll: false,\n      // 新增：控制完整对话导出状态\n      hoveredCardId: null // 当前悬停的卡片ID\n    };\n  },\n  mounted() {\n    // 存储token到localStorage，供API使用\n    localStorage.setItem('de_token', this.apiToken);\n    this.loadTables();\n    this.initMemoryId();\n    this.addWelcomeMessage();\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\"帮我生成一个销售额柱状图\", \"展示近六个月的销售趋势折线图\", \"按照区域统计销售量并生成饼图\", \"帮我做一个按产品类别的销量对比图\"];\n  },\n  updated() {\n    this.scrollToBottom();\n  },\n  methods: {\n    SelectDataList() {\n      this.loadTables();\n      this.drawer = true;\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id');\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4());\n        localStorage.setItem('user_memory_id', storedMemoryId);\n      }\n      this.memoryId = storedMemoryId;\n    },\n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0;\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i];\n        number = number * 16 + (parseInt(hexValue, 16) || 0);\n      }\n      return number % 1000000;\n    },\n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      });\n    },\n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight;\n      }\n    },\n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables();\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data;\n          this.datasets = this.flattenDatasets(this.tables);\n          this.filteredDatasets = this.datasets;\n        } else {\n          this.tables = [];\n          this.datasets = [];\n          this.filteredDatasets = [];\n        }\n      } catch (e) {\n        this.tables = [];\n        this.datasets = [];\n        this.filteredDatasets = [];\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = [];\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node);\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children));\n        }\n      }\n      return result;\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase();\n      if (!keyword) {\n        this.filteredDatasets = this.datasets;\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword));\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n\n    // 测试详情请求方法\n    // async testDetailRequest() {\n    //   if (this.datasets.length > 0) {\n    //     const testDataset = this.datasets[0];\n    //     console.log('测试数据集:', testDataset);\n    //     alert(`正在请求数据集详情，ID: ${testDataset.id}`);\n    //     await this.showDatasetDetail(testDataset);\n    //   } else {\n    //     alert('没有可用的数据集');\n    //   }\n    // },\n\n    // 智能分析数据集\n    analyzeDataset() {\n      if (!this.currentDatasetDetail || !this.datasetFields.length) {\n        this.$message.warning('当前数据集没有可用字段');\n        return;\n      }\n\n      // 提取所需的字段信息\n      const fieldsInfo = this.datasetFields.map(field => {\n        // 只保留需要的字段属性\n        return {\n          id: field.id,\n          originName: field.originName || '',\n          name: field.name || '',\n          dataeaseName: field.dataeaseName || '',\n          groupType: field.groupType || '',\n          type: field.type || '',\n          datasourceId: field.datasourceId || '',\n          datasetTableId: field.datasetTableId || '',\n          datasetGroupId: field.datasetGroupId || ''\n        };\n      });\n\n      // 存储当前数据集信息，包括ID和字段\n      this.currentAnalysisDataset = {\n        id: this.currentDatasetDetail.id,\n        name: this.currentDatasetDetail.name,\n        fields: fieldsInfo\n      };\n\n      // 关闭详情抽屉，返回到聊天界面\n      this.dialogVisible = false;\n\n      // 提示用户\n      this.$message.success(`已选择数据集\"${this.currentDatasetDetail.name}\"进行智能分析，请在下方输入您的问题`);\n\n      // 自动聚焦到问题输入框\n      this.$nextTick(() => {\n        const inputEl = document.querySelector('.question-input input');\n        if (inputEl) inputEl.focus();\n      });\n    },\n    // 选择数据集进行分析（新增方法）\n    selectDatasetForAnalysis(dataset) {\n      // 首先获取数据集详情\n      this.showDatasetDetail(dataset).then(() => {\n        // 详情获取成功后，自动设置为分析数据集\n        setTimeout(() => {\n          this.analyzeDataset();\n        }, 500);\n      });\n    },\n    // 更换数据集（新增方法）\n    changeDataset() {\n      this.currentAnalysisDataset = null;\n      this.$message.info('已清除当前数据集选择，请重新选择数据集');\n    },\n    async showDatasetDetail(dataset) {\n      console.log('showDatasetDetail 被调用，参数:', dataset);\n\n      // 验证参数\n      if (!dataset || !dataset.id) {\n        console.error('无效的数据集参数:', dataset);\n        return Promise.reject(new Error('数据集参数无效'));\n      }\n\n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n      try {\n        console.log(`开始请求数据集详情，ID: ${dataset.id}`);\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n        console.log('数据集详情API响应:', res);\n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          console.log('数据集详情数据:', detail);\n          this.currentDatasetDetail = detail;\n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || detail.data && detail.data.fields || [];\n          // 数据内容\n          this.datasetData = detail.data && detail.data.data || [];\n          // 调试打印\n          console.log(`字段信息: ${this.datasetFields.length}个字段`);\n          console.log(`数据内容: ${this.datasetData.length}条记录`);\n          if (this.datasetFields.length === 0) {\n            console.warn('未找到字段信息');\n          }\n          if (this.datasetData.length === 0) {\n            console.warn('未找到数据内容');\n          }\n          this.dialogVisible = true;\n          return Promise.resolve(detail);\n        } else {\n          console.error('API返回错误:', res.data);\n          this.$message.error(`API返回错误: ${res.data.msg || '未知错误'}`);\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n          this.dialogVisible = true;\n          return Promise.reject(new Error(res.data.msg || '未知错误'));\n        }\n      } catch (e) {\n        console.error('请求数据集详情失败:', e);\n        this.$message.error(`请求失败: ${e.message || '未知错误'}`);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n        return Promise.reject(e);\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n\n      // 获取当前选中的数据集信息\n      // 构建发送给AI的完整消息\n      let aiMessage = this.question.trim();\n\n      // 如果有当前分析的数据集，添加数据集信息\n      if (this.currentAnalysisDataset) {\n        // 构建AI需要的格式\n        const datasetInfo = {\n          datasetId: this.currentAnalysisDataset.id,\n          datasetName: this.currentAnalysisDataset.name,\n          fields: this.currentAnalysisDataset.fields\n        };\n\n        // 将数据集信息添加到消息中\n        aiMessage = JSON.stringify({\n          question: this.question.trim(),\n          dataset: datasetInfo\n        });\n      }\n\n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      };\n      this.messages.push(userMsg);\n\n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      };\n      this.messages.push(botMsg);\n\n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1];\n\n      // 保存问题并清空输入框\n      const question = this.question;\n      this.question = '';\n      this.isSending = true;\n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = this.currentAnalysisDataset ? aiMessage : `${question}。当前数据集：未选择具体数据集`;\n\n        // 发送请求\n        await axios.post('http://localhost:8088/api/indicator/chat', {\n          memoryId: this.memoryId,\n          message\n        }, {\n          responseType: 'stream',\n          onDownloadProgress: e => {\n            const fullText = e.event.target.responseText; // 累积的完整文本\n            let newText = fullText.substring(lastMsg.content.length);\n            lastMsg.content += newText; // 增量更新\n            this.scrollToBottom(); // 实时滚动\n\n            // 保存原始响应\n            this.lastRawResponse = fullText;\n          }\n        });\n\n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false;\n        this.isSending = false;\n\n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error);\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。';\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      }\n    },\n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n\n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n\n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n\n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n\n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n\n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n\n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n\n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n\n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n\n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n\n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n\n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n\n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n\n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n\n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n\n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n\n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      this.messages.push(botMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n\n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n\n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [{\n            field: '类别1',\n            value: 100\n          }, {\n            field: '类别2',\n            value: 200\n          }, {\n            field: '类别3',\n            value: 150\n          }],\n          metrics: [{\n            name: '数值'\n          }]\n        }\n      };\n\n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n\n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [{\n              value: 1,\n              field: \"神朔\",\n              name: \"神朔\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"甘泉\",\n              name: \"甘泉\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"包神\",\n              name: \"包神\",\n              category: \"记录数*\"\n            }],\n            fields: [{\n              id: \"1746787308487\",\n              name: \"名称\",\n              groupType: \"d\"\n            }, {\n              id: \"-1\",\n              name: \"记录数*\",\n              groupType: \"q\"\n            }]\n          }\n        }\n      };\n\n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n\n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n\n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n\n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n\n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n\n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content.replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '').replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '').trim();\n\n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n\n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n\n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n\n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n\n        // 根据图表类型选择对应的处理函数\n        switch (chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n\n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n\n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n\n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2,\n          // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n\n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = canvas.height * imgWidth / canvas.width;\n\n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n\n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n\n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n\n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n\n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    // 导出完整对话方法\n    async exportAllConversation() {\n      try {\n        // 设置导出状态\n        this.exportingAll = true;\n\n        // 1. 提取所有图表消息\n        let chartMessages = [];\n\n        // 先找出所有包含图表的消息\n        for (let i = 0; i < this.messages.length; i++) {\n          const message = this.messages[i];\n          if (!message.isUser && message.chartConfig) {\n            chartMessages.push({\n              message: message,\n              index: i\n            });\n          }\n        }\n\n        // 如果没有图表，显示提示\n        if (chartMessages.length === 0) {\n          this.$message.warning('暂无图表数据。请先通过对话生成图表。');\n          this.exportingAll = false;\n          return;\n        }\n\n        // 2. 创建PDF\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 3. 处理每个图表，每个图表单独一页\n        for (let i = 0; i < chartMessages.length; i++) {\n          // 如果不是第一页，添加新页面\n          if (i > 0) {\n            pdf.addPage();\n          }\n          const chartMessage = chartMessages[i].message;\n          const chartConfig = chartMessage.chartConfig;\n\n          // 创建临时容器\n          const tempContainer = document.createElement('div');\n          tempContainer.style.position = 'absolute';\n          tempContainer.style.left = '-9999px';\n          tempContainer.style.width = '800px';\n          tempContainer.style.background = '#fff';\n          tempContainer.style.padding = '20px';\n          document.body.appendChild(tempContainer);\n\n          // 构建PDF标题和基本信息\n          const title = chartConfig.title || '数据分析图表';\n          const currentDate = new Date().toLocaleString();\n          const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n          let htmlContent = `\n            <div style=\"font-family: Arial, sans-serif; padding: 20px;\">\n              <h1 style=\"text-align: center; color: #333; font-size: 24px; margin-bottom: 30px;\">${title}</h1>\n              <div style=\"text-align: right; margin-bottom: 20px;\">\n                <p style=\"color: #666; margin: 5px 0;\">数据集: ${datasetName}</p>\n                <p style=\"color: #666; margin: 5px 0;\">生成时间: ${currentDate}</p>\n              </div>\n              \n              <div style=\"margin: 20px 0;\">\n                <h2 style=\"color: #333; font-size: 20px; margin-bottom: 15px;\">分析描述:</h2>\n          `;\n\n          // 添加描述\n          let description = chartMessage.content;\n\n          // 清理HTML标签，保留纯文本\n          if (description.includes('<')) {\n            const tempDiv = document.createElement('div');\n            tempDiv.innerHTML = description;\n            description = tempDiv.textContent || tempDiv.innerText || '';\n          }\n          htmlContent += `\n            <p style=\"margin: 15px 0; color: #333; line-height: 1.6;\">${description}</p>\n            \n            <div style=\"margin: 30px 0;\">\n              <div id=\"chart-container\" style=\"height: 400px; margin: 30px 0;\"></div>\n              \n              <div style=\"margin: 30px 0;\">\n                <h3 style=\"color: #333; font-size: 18px; margin-bottom: 15px;\">数据表格:</h3>\n                <div id=\"data-table\"></div>\n              </div>\n            </div>\n          `;\n          htmlContent += `</div>`;\n\n          // 设置HTML内容\n          tempContainer.innerHTML = htmlContent;\n\n          // 渲染图表\n          const chartContainer = tempContainer.querySelector('#chart-container');\n          const dataTableContainer = tempContainer.querySelector('#data-table');\n          if (chartContainer && dataTableContainer) {\n            // 初始化图表\n            const chartInstance = echarts.init(chartContainer);\n            let options;\n\n            // 复用你现有的图表处理逻辑\n            switch (chartConfig.type) {\n              case 'bar':\n                options = this.getBarChartOptions(chartConfig);\n                break;\n              case 'line':\n                options = this.getLineChartOptions(chartConfig);\n                break;\n              case 'pie':\n                options = this.getPieChartOptions(chartConfig);\n                break;\n              case 'bar-horizontal':\n                options = this.getBarHorizontalOptions(chartConfig);\n                break;\n              default:\n                options = this.getDefaultOptions(chartConfig);\n            }\n            chartInstance.setOption(options);\n\n            // 渲染数据表格\n            this.renderDataTable(dataTableContainer, chartConfig);\n\n            // 等待图表渲染完成\n            await new Promise(resolve => setTimeout(resolve, 1000));\n\n            // 使用html2canvas捕获当前页面内容\n            const canvas = await html2canvas(tempContainer, {\n              scale: 2,\n              useCORS: true,\n              allowTaint: true,\n              backgroundColor: '#ffffff'\n            });\n\n            // 将canvas转为图像\n            const imgData = canvas.toDataURL('image/png');\n\n            // 计算适当的宽度和高度以适应A4页面\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            const pageHeight = pdf.internal.pageSize.getHeight();\n            const imgWidth = pageWidth - 40; // 左右各20mm边距\n            const imgHeight = canvas.height * imgWidth / canvas.width;\n\n            // 如果图像高度超过页面高度，进行缩放\n            let finalImgHeight = imgHeight;\n            let finalImgWidth = imgWidth;\n            if (imgHeight > pageHeight - 40) {\n              // 上下各20mm边距\n              finalImgHeight = pageHeight - 40;\n              finalImgWidth = canvas.width * finalImgHeight / canvas.height;\n            }\n\n            // 添加图像到PDF，居中显示\n            const xPos = (pageWidth - finalImgWidth) / 2;\n            const yPos = 20; // 顶部边距\n\n            pdf.addImage(imgData, 'PNG', xPos, yPos, finalImgWidth, finalImgHeight);\n\n            // 清理\n            chartInstance.dispose();\n            document.body.removeChild(tempContainer);\n          }\n        }\n\n        // 4. 保存PDF\n        const fileName = `指标分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;\n        pdf.save(fileName);\n\n        // 5. 重置状态\n        this.exportingAll = false;\n        this.$message.success('指标导出成功');\n      } catch (error) {\n        console.error('指标导出失败:', error);\n        this.exportingAll = false;\n        this.$message.error('指标导出失败: ' + error.message);\n      }\n    },\n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n\n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n\n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n\n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n\n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n\n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n\n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      return options;\n    },\n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n\n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n\n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n\n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n\n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n\n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n\n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n\n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n\n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "v4", "uuidv4", "axios", "ChartDisplay", "html2canvas", "jsPDF", "echarts", "name", "components", "data", "description", "tablename", "tables", "datasets", "filteredDatasets", "searchKeyword", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "tableIndicators", "dialogVisible", "messages", "memoryId", "isSending", "messageListRef", "showRawResponsePanel", "lastRawResponse", "drawer", "direction", "currentDatasetDetail", "datasetFields", "datasetData", "apiToken", "currentAnalysisDataset", "innerDrawer", "tableList", "exportingAll", "hoveredCardId", "mounted", "localStorage", "setItem", "loadTables", "initMemoryId", "addWelcomeMessage", "updated", "scrollToBottom", "methods", "SelectDataList", "storedMemoryId", "getItem", "uuidToNumber", "uuid", "number", "i", "length", "hexValue", "parseInt", "push", "isUser", "content", "isTyping", "$refs", "scrollTop", "scrollHeight", "res", "getAllTables", "code", "flattenDatasets", "e", "tree", "result", "node", "leaf", "children", "concat", "onSearchDataset", "keyword", "trim", "toLowerCase", "filter", "ds", "includes", "analyzeDataset", "$message", "warning", "fieldsInfo", "map", "field", "id", "originName", "dataeaseName", "groupType", "type", "datasourceId", "datasetTableId", "datasetGroupId", "fields", "success", "$nextTick", "inputEl", "document", "querySelector", "focus", "selectDatasetForAnalysis", "dataset", "showDatasetDetail", "then", "setTimeout", "changeDataset", "info", "console", "log", "error", "Promise", "reject", "Error", "headers", "getDatasetDetail", "detail", "allFields", "warn", "resolve", "msg", "message", "showSuggestions", "useQuestion", "q", "getTableFields", "table", "tableCode", "submitQuestion", "aiMessage", "datasetInfo", "datasetId", "datasetName", "JSON", "stringify", "userMsg", "botMsg", "lastMsg", "post", "responseType", "onDownloadProgress", "fullText", "event", "target", "responseText", "newText", "substring", "parseChartConfig", "chartDataIdMatch", "match", "chartDataId", "fetchChartDataById", "chartConfigMatch", "chartConfig", "parse", "tableId", "replace", "jsonRegex", "jsonMatch", "jsonStr", "parseError", "defaultConfig", "Date", "now", "toString", "title", "response", "get", "status", "$forceUpdate", "test<PERSON>hart", "testChartConfig", "testApiResponse", "value", "metrics", "json<PERSON><PERSON><PERSON>", "apiResponseMsg", "testRealData", "realData", "category", "realDataMsg", "showRawResponse", "exportToPDF", "$set", "tempContainer", "createElement", "style", "position", "left", "width", "background", "padding", "body", "append<PERSON><PERSON><PERSON>", "tableName", "currentDate", "toLocaleString", "tempDiv", "innerHTML", "textContent", "innerText", "chartContainer", "chart", "init", "options", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "setOption", "dataTableContainer", "renderDataTable", "canvas", "scale", "useCORS", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "imgData", "toDataURL", "pdf", "orientation", "unit", "format", "imgWidth", "imgHeight", "height", "addImage", "save", "getTime", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "exportAllConversation", "chartMessages", "index", "addPage", "chartMessage", "htmlContent", "chartInstance", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "finalImgHeight", "finalImgWidth", "xPos", "yPos", "fileName", "toISOString", "slice", "createBasicChartOptions", "categories", "item", "text", "tooltip", "trigger", "xAxis", "yAxis", "series", "seriesMap", "for<PERSON>ach", "s", "Array", "fill", "Object", "values", "chartData", "isArray", "f", "xAxisData", "categoriesSet", "Set", "add", "from", "seriesData", "find", "axisPointer", "legend", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "container", "firstItem", "tableHTML", "header", "undefined"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <!-- 调试信息区域 -->\n    <div style=\"position: fixed; top: 10px; right: 10px; z-index: 9999; background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-width: 300px; max-height: 300px; overflow: auto;\">\n      <!-- <button @click=\"testDetailRequest()\">测试详情请求</button>\n      <div v-if=\"datasets.length > 0\">\n        <p>数据集数量: {{ datasets.length }}</p>\n        <p>第一个数据集ID: {{ datasets[0] && datasets[0].id }}</p>\n      </div> -->\n    </div>\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <div class=\"header-actions\" style=\"display: flex; align-items: center; margin-top: 10px;\">\n           \n          </div>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n        </div>\n        \n        <!-- 数据集选择区域 - 根据选择状态动态显示 -->\n        <div class=\"data-selection\" v-show=\"!currentAnalysisDataset\">\n          <h3>可用数据</h3>\n          <div class=\"data-sets\">\n            <el-row :gutter=\"16\">\n              <el-col :span=\"8\" v-for=\"(table, idx) in datasets.slice(0, 3)\" :key=\"table.id + '_' + idx\">\n                <div class=\"data-card-wrapper\" @mouseenter=\"showCardActions(table.id)\" @mouseleave=\"hideCardActions\">\n                  <el-card class=\"data-card\" :class=\"{ 'card-hover': hoveredCardId === table.id }\">\n                    <div class=\"data-header\">\n                      <span class=\"sample-tag\">样例</span>\n                      <span class=\"data-title\">{{ table.name }}</span>\n                      <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n                    </div>\n                    <div class=\"data-fields\">\n                      <el-tag\n                        v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])\"\n                        :key=\"field.id || idx\"\n                        size=\"mini\"\n                        type=\"info\"\n                        class=\"field-tag\"\n                      >\n                        {{ field.name || field }}\n                      </el-tag>\n                      <span v-if=\"table.fields && table.fields.length > 4\" class=\"more-fields\">...</span>\n                    </div>\n\n                    <!-- 悬停时显示的操作按钮 -->\n                    <div class=\"card-actions\" v-show=\"hoveredCardId === table.id\">\n                      <el-button size=\"small\" type=\"primary\" plain @click=\"showDatasetDetail(table)\">\n                        预览\n                      </el-button>\n                      <el-button size=\"small\" type=\"primary\" @click=\"selectDatasetForAnalysis(table)\">\n                        提问\n                      </el-button>\n                    </div>\n                  </el-card>\n                </div>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n\n        <!-- 数据集指示器 - 显示在消息列表上方 -->\n        <div class=\"dataset-indicator\" v-show=\"currentAnalysisDataset\">\n          <div class=\"dataset-info\">\n            <div class=\"dataset-main-info\">\n              <i class=\"el-icon-database\"></i>\n              <span class=\"dataset-name\">{{ currentAnalysisDataset?.name }}</span>\n              <span class=\"dataset-fields-count\">{{ currentAnalysisDataset?.fields?.length || 0 }}个字段</span>\n            </div>\n            <div class=\"dataset-actions\">\n              <el-button size=\"mini\" type=\"text\" @click=\"showDatasetDetail(currentAnalysisDataset)\" class=\"dataset-preview-btn\">\n                <i class=\"el-icon-view\"></i> 预览\n              </el-button>\n              <el-button size=\"mini\" type=\"text\" @click=\"changeDataset\" class=\"dataset-change-btn\">\n                <i class=\"el-icon-refresh\"></i> 更换数据集\n              </el-button>\n            </div>\n          </div>\n          <div class=\"dataset-fields-preview\">\n            <el-tag\n              v-for=\"(field, idx) in (currentAnalysisDataset?.fields || []).slice(0, 6)\"\n              :key=\"field.id || idx\"\n              size=\"mini\"\n              :type=\"field.groupType === 'd' ? 'primary' : 'success'\"\n              class=\"field-tag-preview\"\n            >\n              {{ field.name }}\n            </el-tag>\n            <span v-if=\"currentAnalysisDataset?.fields && currentAnalysisDataset.fields.length > 6\" class=\"more-fields-preview\">\n              +{{ currentAnalysisDataset.fields.length - 6 }}个字段\n            </span>\n          </div>\n        </div>\n\n        <!-- 聊天消息列表区域 -->\n        <div class=\"message-list\" ref=\"messageListRef\">\n          <div\n            v-for=\"(message, index) in messages\"\n            :key=\"index\"\n            :class=\"message.isUser ? 'message user-message' : 'message bot-message'\"\n          >\n            <!-- 聊天图标 -->\n            <div class=\"avatar-container\" v-if=\"!message.isUser\">\n              <div class=\"bot-avatar\">\n                <i class=\"el-icon-s-tools\"></i>\n              </div>\n            </div>\n            <!-- 消息内容 -->\n            <div class=\"message-content\">\n              <div v-html=\"message.content\"></div>\n              <!-- 如果消息中包含图表配置，则显示图表和导出按钮 -->\n              <div v-if=\"message.chartConfig\" class=\"chart-container\">\n                <div class=\"chart-actions\">\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-download\" \n                            @click=\"exportToPDF(message)\" :loading=\"message.exporting\">\n                    导出PDF\n                  </el-button>\n                </div>\n                <chart-display :chart-config=\"message.chartConfig\" ref=\"chartDisplay\"></chart-display>\n              </div>\n              <!-- loading动画 -->\n              <span\n                class=\"loading-dots\"\n                v-if=\"message.isTyping\"\n              >\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n              </span>\n            </div>\n            <div class=\"avatar-container\" v-if=\"message.isUser\">\n              <div class=\"user-avatar\">\n                <i class=\"el-icon-user\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 底部问题输入区域（移至message-list下方） -->\n        <div class=\"question-input-container\">\n          <span>👋直接问我问题，或在上方选择一个主题/数据开始！</span>\n          <div class=\"question-input-wrapper\">\n            <el-button type=\"text\" \n            style=\"margin-left: 10px;\" \n            size=\"small\"\n            @click=\"SelectDataList\" \n            icon=\"el-icon-upload2\">\n            选择数据\n          </el-button>\n            <el-button \n              type=\"text\"\n              size=\"small\"\n              icon=\"el-icon-bottom\" \n              @click=\"exportAllConversation\"\n              :loading=\"exportingAll\"\n              :disabled=\"messages.length <= 1\"\n              style=\"margin-left: 20px;\">\n              导出完整指标\n            </el-button>\n            <el-input \n              style=\"margin-bottom: 12px;width: 800px;\"\n              v-model=\"question\" \n              placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n              class=\"question-input\"\n              @keyup.enter.native=\"submitQuestion\"\n              :disabled=\"isSending\">\n            </el-input>\n            <div class=\"input-actions\">\n              <button class=\"action-btn\" @click=\"showSuggestions\">\n                <i class=\"el-icon-magic-stick\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testChart\" title=\"测试图表功能\">\n                <i class=\"el-icon-data-line\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testRealData\" title=\"测试实际数据\">\n                <i class=\"el-icon-s-data\"></i>\n              </button>\n              <button class=\"action-btn debug-btn\" @click=\"showRawResponse\" title=\"显示AI原始响应\">\n                <i class=\"el-icon-monitor\"></i>\n              </button>\n              <button class=\"action-btn send-btn\" @click=\"submitQuestion\" :disabled=\"isSending\">\n                <i class=\"el-icon-position\"></i>\n              </button>\n              \n            </div>\n          </div>\n          \n          <!-- 建议问题弹出层 -->\n          <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n            <div class=\"suggestions-title\">\n              <i class=\"el-icon-s-promotion\"></i> 官方推荐\n            </div>\n            <div class=\"suggestions-list\">\n              <div \n                v-for=\"(suggestion, index) in suggestedQuestions\" \n                :key=\"index\"\n                class=\"suggestion-item\"\n                @click=\"useQuestion(suggestion)\">\n                {{ suggestion }}\n              </div>\n            </div>\n          </div>\n          \n          <!-- AI原始响应弹出层 -->\n          <div v-if=\"showRawResponsePanel\" class=\"raw-response-panel\">\n            <div class=\"raw-response-title\">\n              <i class=\"el-icon-monitor\"></i> AI原始响应\n              <button class=\"close-btn\" @click=\"showRawResponsePanel = false\">\n                <i class=\"el-icon-close\"></i>\n              </button>\n            </div>\n            <pre class=\"raw-response-content\">{{ lastRawResponse }}</pre>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <el-drawer\n      title=\"数据详情\"\n      :visible.sync=\"dialogVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      \n     \n      <div style=\"padding: 20px\" v-if=\"currentDatasetDetail\">\n        <h3>{{currentDatasetDetail.name}}</h3>\n        <div style=\"margin-top: 10px; margin-bottom: 20px; text-align: right;\">\n          <el-button type=\"primary\" @click=\"analyzeDataset\" icon=\"el-icon-data-analysis\">\n            智能分析数据\n          </el-button>\n        </div>\n        <el-divider></el-divider>\n        \n        <h4>字段信息</h4>\n        <el-table :data=\"datasetFields\" style=\"width: 100%\">\n          <el-table-column prop=\"name\" label=\"字段名称\"></el-table-column>\n          <el-table-column prop=\"type\" label=\"字段类型\"></el-table-column>\n          <el-table-column prop=\"groupType\" label=\"分组类型\"></el-table-column>\n        </el-table>\n        \n        <el-divider></el-divider>\n        \n        <h4>数据预览</h4>\n        <el-table :data=\"datasetData.slice(0, 100)\" style=\"width: 100%\">\n          <el-table-column \n            v-for=\"field in datasetFields\" \n            :key=\"field.id || field.name\"\n            :prop=\"field.dataeaseName || field.name\"\n            :label=\"field.name\">\n          </el-table-column>\n        </el-table>\n        <div v-if=\"datasetData.length > 100\" class=\"data-limit-notice\">\n          显示前100条数据，共 {{ datasetData.length }} 条\n        </div>\n         <div v-if=\"datasetData.length < 100\" class=\"data-limit-notice\">\n          显示前{{ datasetData.length }}条数据，共 {{ datasetData.length }} 条\n        </div>\n      </div>\n    </el-drawer>\n    <el-drawer\n      title=\"数据集列表\"\n      :visible.sync=\"drawer\"\n      direction=\"rtl\"\n      size=\"45%\">\n      <div style=\"padding: 20px\">\n        <el-input\n          v-model=\"searchKeyword\"\n          placeholder=\"搜索数据集\"\n          @input=\"onSearchDataset\"\n          style=\"margin-bottom: 16px; width: 300px;\"\n        />\n        <el-row :gutter=\"16\">\n          <el-col :span=\"8\" v-for=\"(table, idx) in filteredDatasets\" :key=\"table.id + '_' + idx\">\n            <el-card class=\"data-card\" @click.native=\"selectDatasetForAnalysis(table)\">\n              <div class=\"data-header\">\n                <span class=\"sample-tag\">样例</span>\n                <span class=\"data-title\">{{ table.name }}</span>\n                <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n              </div>\n              <div class=\"data-fields\">\n                <el-tag\n                  v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])\"\n                  :key=\"field.id || idx\"\n                  size=\"mini\"\n                  type=\"info\"\n                  class=\"field-tag\"\n                >\n                  {{ field.name || field }}\n                </el-tag>\n                <span v-if=\"table.fields && table.fields.length > 4\" class=\"more-fields\">+{{ table.fields.length - 4 }}个字段</span>\n              </div>\n            </el-card>\n          </el-col>\n        </el-row>\n      </div>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api/index.js'\nimport { v4 as uuidv4 } from 'uuid'\nimport axios from 'axios'\nimport ChartDisplay from './components/ChartDisplay.vue'\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas'\nimport { jsPDF } from 'jspdf'\nimport * as echarts from 'echarts'\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [], // 原始树结构\n      datasets: [], // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [], // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false, // 新增：控制原始响应弹出层\n      lastRawResponse: '', // 新增：存储最后收到的原始响应\n      drawer:false,    //抽屉展示\n      direction: 'rtl', //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      apiToken: 'eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2OTEyYzNiLTQ5ZmMtNDAyMC1iMzRiLWE3YWJlNmY1MTI0MSJ9.J4QCC6qh2GZ7ENDHuZQWQ1GvGOQ1HOr8gW34KXIHV9N_73Jppx-qmEwpq0AHlXM3DUjd5EAlpIVAvzJygm_ldg', // 示例token，实际应该从登录后存储\n      currentAnalysisDataset: null, // 存储当前用于智能分析的数据集信息\n      innerDrawer:false,\n      tableList:[],\n      exportingAll: false, // 新增：控制完整对话导出状态\n      hoveredCardId: null, // 当前悬停的卡片ID\n    }\n  },\n  \n  mounted() {\n    // 存储token到localStorage，供API使用\n    localStorage.setItem('de_token', this.apiToken);\n    this.loadTables()\n    this.initMemoryId()\n    this.addWelcomeMessage()\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\n      \"帮我生成一个销售额柱状图\",\n      \"展示近六个月的销售趋势折线图\",\n      \"按照区域统计销售量并生成饼图\",\n      \"帮我做一个按产品类别的销量对比图\"\n    ]\n  },\n  updated() {\n    this.scrollToBottom()\n  },\n  methods: {\n    SelectDataList(){\n      this.loadTables()\n      this.drawer=true\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id')\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4())\n        localStorage.setItem('user_memory_id', storedMemoryId)\n      }\n      this.memoryId = storedMemoryId\n    },\n    \n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i]\n        number = number * 16 + (parseInt(hexValue, 16) || 0)\n      }\n      return number % 1000000\n    },\n    \n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      })\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight\n      }\n    },\n    \n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables()\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data\n          this.datasets = this.flattenDatasets(this.tables)\n          this.filteredDatasets = this.datasets\n        } else {\n          this.tables = []\n          this.datasets = []\n          this.filteredDatasets = []\n        }\n      } catch (e) {\n        this.tables = []\n        this.datasets = []\n        this.filteredDatasets = []\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = []\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node)\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children))\n        }\n      }\n      return result\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase()\n      if (!keyword) {\n        this.filteredDatasets = this.datasets\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword))\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n    \n    // 测试详情请求方法\n    // async testDetailRequest() {\n    //   if (this.datasets.length > 0) {\n    //     const testDataset = this.datasets[0];\n    //     console.log('测试数据集:', testDataset);\n    //     alert(`正在请求数据集详情，ID: ${testDataset.id}`);\n    //     await this.showDatasetDetail(testDataset);\n      //   } else {\n    //     alert('没有可用的数据集');\n    //   }\n    // },\n\n    // 智能分析数据集\n    analyzeDataset() {\n      if (!this.currentDatasetDetail || !this.datasetFields.length) {\n        this.$message.warning('当前数据集没有可用字段');\n        return;\n      }\n\n      // 提取所需的字段信息\n      const fieldsInfo = this.datasetFields.map(field => {\n        // 只保留需要的字段属性\n        return {\n          id: field.id,\n          originName: field.originName || '',\n          name: field.name || '',\n          dataeaseName: field.dataeaseName || '',\n          groupType: field.groupType || '',\n          type: field.type || '',\n          datasourceId: field.datasourceId || '',\n          datasetTableId: field.datasetTableId || '',\n          datasetGroupId: field.datasetGroupId || ''\n        };\n      });\n\n      // 存储当前数据集信息，包括ID和字段\n      this.currentAnalysisDataset = {\n        id: this.currentDatasetDetail.id,\n        name: this.currentDatasetDetail.name,\n        fields: fieldsInfo\n      };\n\n      // 关闭详情抽屉，返回到聊天界面\n      this.dialogVisible = false;\n\n      // 提示用户\n      this.$message.success(`已选择数据集\"${this.currentDatasetDetail.name}\"进行智能分析，请在下方输入您的问题`);\n\n      // 自动聚焦到问题输入框\n      this.$nextTick(() => {\n        const inputEl = document.querySelector('.question-input input');\n        if (inputEl) inputEl.focus();\n      });\n    },\n\n    // 选择数据集进行分析（新增方法）\n    selectDatasetForAnalysis(dataset) {\n      // 首先获取数据集详情\n      this.showDatasetDetail(dataset).then(() => {\n        // 详情获取成功后，自动设置为分析数据集\n        setTimeout(() => {\n          this.analyzeDataset();\n        }, 500);\n      });\n    },\n\n    // 更换数据集（新增方法）\n    changeDataset() {\n      this.currentAnalysisDataset = null;\n      this.$message.info('已清除当前数据集选择，请重新选择数据集');\n    },\n  \n    async showDatasetDetail(dataset) {\n      console.log('showDatasetDetail 被调用，参数:', dataset);\n\n      // 验证参数\n      if (!dataset || !dataset.id) {\n        console.error('无效的数据集参数:', dataset);\n        return Promise.reject(new Error('数据集参数无效'));\n      }\n\n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n\n      try {\n        console.log(`开始请求数据集详情，ID: ${dataset.id}`);\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n        console.log('数据集详情API响应:', res);\n\n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          console.log('数据集详情数据:', detail);\n          this.currentDatasetDetail = detail;\n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || (detail.data && detail.data.fields) || [];\n          // 数据内容\n          this.datasetData = (detail.data && detail.data.data) || [];\n          // 调试打印\n          console.log(`字段信息: ${this.datasetFields.length}个字段`);\n          console.log(`数据内容: ${this.datasetData.length}条记录`);\n\n          if (this.datasetFields.length === 0) {\n            console.warn('未找到字段信息');\n          }\n\n          if (this.datasetData.length === 0) {\n            console.warn('未找到数据内容');\n          }\n\n          this.dialogVisible = true;\n          return Promise.resolve(detail);\n        } else {\n          console.error('API返回错误:', res.data);\n          this.$message.error(`API返回错误: ${res.data.msg || '未知错误'}`);\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n          this.dialogVisible = true;\n          return Promise.reject(new Error(res.data.msg || '未知错误'));\n        }\n      } catch (e) {\n        console.error('请求数据集详情失败:', e);\n        this.$message.error(`请求失败: ${e.message || '未知错误'}`);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n        return Promise.reject(e);\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    \n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      // 获取当前选中的数据集信息\n      // 构建发送给AI的完整消息\n      let aiMessage = this.question.trim();\n      \n      // 如果有当前分析的数据集，添加数据集信息\n      if (this.currentAnalysisDataset) {\n        // 构建AI需要的格式\n        const datasetInfo = {\n          datasetId: this.currentAnalysisDataset.id,\n          datasetName: this.currentAnalysisDataset.name,\n          fields: this.currentAnalysisDataset.fields\n        };\n        \n        // 将数据集信息添加到消息中\n        aiMessage = JSON.stringify({\n          question: this.question.trim(),\n          dataset: datasetInfo\n        });\n      }\n      \n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      }\n      this.messages.push(userMsg)\n      \n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      }\n      this.messages.push(botMsg)\n      \n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1]\n      \n      // 保存问题并清空输入框\n      const question = this.question\n      this.question = ''\n      this.isSending = true\n      \n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = this.currentAnalysisDataset \n          ? aiMessage \n          : `${question}。当前数据集：未选择具体数据集`;\n        \n        // 发送请求\n        await axios.post(\n          'http://localhost:8088/api/indicator/chat',\n          { memoryId: this.memoryId, message },\n          {\n            responseType: 'stream',\n            onDownloadProgress: (e) => {\n              const fullText = e.event.target.responseText // 累积的完整文本\n              let newText = fullText.substring(lastMsg.content.length)\n              lastMsg.content += newText // 增量更新\n              this.scrollToBottom() // 实时滚动\n              \n              // 保存原始响应\n              this.lastRawResponse = fullText\n            }\n          }\n        )\n        \n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false\n        this.isSending = false\n        \n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error)\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'\n        lastMsg.isTyping = false\n        this.isSending = false\n      }\n    },\n    \n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n      \n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n        \n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n      \n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        \n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n          \n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n          \n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n            \n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, \n              '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            \n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      \n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n      \n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        \n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n          \n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          \n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            \n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n              \n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], \n                  '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n      \n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || \n          message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n        \n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        \n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    \n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        \n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n          \n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n          \n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n          \n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n          \n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    \n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n      \n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      \n      this.messages.push(botMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n      \n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n      \n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [\n            { field: '类别1', value: 100 },\n            { field: '类别2', value: 200 },\n            { field: '类别3', value: 150 }\n          ],\n          metrics: [{ name: '数值' }]\n        }\n      };\n      \n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      \n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      \n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    \n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n      \n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [\n              {value: 1, field: \"神朔\", name: \"神朔\", category: \"记录数*\"},\n              {value: 1, field: \"甘泉\", name: \"甘泉\", category: \"记录数*\"},\n              {value: 1, field: \"包神\", name: \"包神\", category: \"记录数*\"}\n            ],\n            fields: [\n              {id: \"1746787308487\", name: \"名称\", groupType: \"d\"},\n              {id: \"-1\", name: \"记录数*\", groupType: \"q\"}\n            ]\n          }\n        }\n      };\n      \n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n      \n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n      \n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      \n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      \n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n        \n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n        \n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n        \n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content\n          .replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '')\n          .replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '')\n          .trim();\n          \n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n        \n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n        \n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n        \n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n        \n        // 根据图表类型选择对应的处理函数\n        switch(chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n        \n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n        \n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        \n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n        \n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2, // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n        \n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = (canvas.height * imgWidth) / canvas.width;\n        \n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n        \n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n        \n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n        \n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n        \n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n        \n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    \n    // 导出完整对话方法\n    async exportAllConversation() {\n      try {\n        // 设置导出状态\n        this.exportingAll = true;\n        \n        // 1. 提取所有图表消息\n        let chartMessages = [];\n        \n        // 先找出所有包含图表的消息\n        for (let i = 0; i < this.messages.length; i++) {\n          const message = this.messages[i];\n          if (!message.isUser && message.chartConfig) {\n            chartMessages.push({\n              message: message,\n              index: i\n            });\n          }\n        }\n        \n        // 如果没有图表，显示提示\n        if (chartMessages.length === 0) {\n          this.$message.warning('暂无图表数据。请先通过对话生成图表。');\n          this.exportingAll = false;\n          return;\n        }\n        \n        // 2. 创建PDF\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 3. 处理每个图表，每个图表单独一页\n        for (let i = 0; i < chartMessages.length; i++) {\n          // 如果不是第一页，添加新页面\n          if (i > 0) {\n            pdf.addPage();\n          }\n          \n          const chartMessage = chartMessages[i].message;\n          const chartConfig = chartMessage.chartConfig;\n          \n          // 创建临时容器\n          const tempContainer = document.createElement('div');\n          tempContainer.style.position = 'absolute';\n          tempContainer.style.left = '-9999px';\n          tempContainer.style.width = '800px';\n          tempContainer.style.background = '#fff';\n          tempContainer.style.padding = '20px';\n          document.body.appendChild(tempContainer);\n          \n          // 构建PDF标题和基本信息\n          const title = chartConfig.title || '数据分析图表';\n          const currentDate = new Date().toLocaleString();\n          const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n          \n          let htmlContent = `\n            <div style=\"font-family: Arial, sans-serif; padding: 20px;\">\n              <h1 style=\"text-align: center; color: #333; font-size: 24px; margin-bottom: 30px;\">${title}</h1>\n              <div style=\"text-align: right; margin-bottom: 20px;\">\n                <p style=\"color: #666; margin: 5px 0;\">数据集: ${datasetName}</p>\n                <p style=\"color: #666; margin: 5px 0;\">生成时间: ${currentDate}</p>\n              </div>\n              \n              <div style=\"margin: 20px 0;\">\n                <h2 style=\"color: #333; font-size: 20px; margin-bottom: 15px;\">分析描述:</h2>\n          `;\n          \n          // 添加描述\n          let description = chartMessage.content;\n          \n          // 清理HTML标签，保留纯文本\n          if (description.includes('<')) {\n            const tempDiv = document.createElement('div');\n            tempDiv.innerHTML = description;\n            description = tempDiv.textContent || tempDiv.innerText || '';\n          }\n          \n          htmlContent += `\n            <p style=\"margin: 15px 0; color: #333; line-height: 1.6;\">${description}</p>\n            \n            <div style=\"margin: 30px 0;\">\n              <div id=\"chart-container\" style=\"height: 400px; margin: 30px 0;\"></div>\n              \n              <div style=\"margin: 30px 0;\">\n                <h3 style=\"color: #333; font-size: 18px; margin-bottom: 15px;\">数据表格:</h3>\n                <div id=\"data-table\"></div>\n              </div>\n            </div>\n          `;\n          \n          htmlContent += `</div>`;\n          \n          // 设置HTML内容\n          tempContainer.innerHTML = htmlContent;\n          \n          // 渲染图表\n          const chartContainer = tempContainer.querySelector('#chart-container');\n          const dataTableContainer = tempContainer.querySelector('#data-table');\n          \n          if (chartContainer && dataTableContainer) {\n            // 初始化图表\n            const chartInstance = echarts.init(chartContainer);\n            let options;\n            \n            // 复用你现有的图表处理逻辑\n            switch(chartConfig.type) {\n              case 'bar':\n                options = this.getBarChartOptions(chartConfig);\n                break;\n              case 'line':\n                options = this.getLineChartOptions(chartConfig);\n                break;\n              case 'pie':\n                options = this.getPieChartOptions(chartConfig);\n                break;\n              case 'bar-horizontal':\n                options = this.getBarHorizontalOptions(chartConfig);\n                break;\n              default:\n                options = this.getDefaultOptions(chartConfig);\n            }\n            \n            chartInstance.setOption(options);\n            \n            // 渲染数据表格\n            this.renderDataTable(dataTableContainer, chartConfig);\n            \n            // 等待图表渲染完成\n            await new Promise(resolve => setTimeout(resolve, 1000));\n            \n            // 使用html2canvas捕获当前页面内容\n            const canvas = await html2canvas(tempContainer, {\n              scale: 2,\n              useCORS: true,\n              allowTaint: true,\n              backgroundColor: '#ffffff'\n            });\n            \n            // 将canvas转为图像\n            const imgData = canvas.toDataURL('image/png');\n            \n            // 计算适当的宽度和高度以适应A4页面\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            const pageHeight = pdf.internal.pageSize.getHeight();\n            const imgWidth = pageWidth - 40; // 左右各20mm边距\n            const imgHeight = (canvas.height * imgWidth) / canvas.width;\n            \n            // 如果图像高度超过页面高度，进行缩放\n            let finalImgHeight = imgHeight;\n            let finalImgWidth = imgWidth;\n            \n            if (imgHeight > pageHeight - 40) { // 上下各20mm边距\n              finalImgHeight = pageHeight - 40;\n              finalImgWidth = (canvas.width * finalImgHeight) / canvas.height;\n            }\n            \n            // 添加图像到PDF，居中显示\n            const xPos = (pageWidth - finalImgWidth) / 2;\n            const yPos = 20; // 顶部边距\n            \n            pdf.addImage(imgData, 'PNG', xPos, yPos, finalImgWidth, finalImgHeight);\n            \n            // 清理\n            chartInstance.dispose();\n            document.body.removeChild(tempContainer);\n          }\n        }\n        \n        // 4. 保存PDF\n        const fileName = `指标分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;\n        pdf.save(fileName);\n        \n        // 5. 重置状态\n        this.exportingAll = false;\n        this.$message.success('指标导出成功');\n        \n      } catch (error) {\n        console.error('指标导出失败:', error);\n        this.exportingAll = false;\n        this.$message.error('指标导出失败: ' + error.message);\n      }\n    },\n    \n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n      \n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n      \n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n      \n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n          \n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n          \n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n          \n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      \n      return options;\n    },\n    \n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      \n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n      \n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        \n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n      \n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n      \n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n      \n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n        \n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n        \n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        \n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n      \n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #ffffff;\n}\n\n.app-layout {\n  display: flex;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #ffffff;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #e8eaec;\n  background-color: #fafbfc;\n  height: 100%;\n  overflow-y: auto;\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.04);\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 25px;\n  text-align: center;\n  white-space: nowrap;\n  letter-spacing: 2px;\n  font-weight: 900;\n}\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 24px 32px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n  height: 100vh;\n  box-sizing: border-box;\n  background-color: #ffffff;\n}\n\n.header {\n  margin-bottom: 32px;\n  width: 100%;\n  text-align: center;\n}\n\n.header h2 {\n  margin: 0;\n  font-size: 28px;\n  font-weight: 600;\n  color: #1f2329;\n}\n\n.highlight {\n  color: #1664ff;\n  font-weight: 700;\n}\n\n.sub-title {\n  color: #86909c;\n  font-size: 14px;\n  margin: 8px 0 0 0;\n  line-height: 1.5;\n}\n\n.data-card {\n  cursor: pointer;\n  margin-bottom: 16px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  border-radius: 12px;\n  border: 1px solid #e8eaec;\n  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n  overflow: hidden;\n  position: relative;\n}\n\n.data-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 3px;\n  background: linear-gradient(90deg, #1664ff, #00d4aa);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.data-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 24px rgba(22, 100, 255, 0.12);\n  border-color: #1664ff;\n}\n\n.data-card:hover::before {\n  opacity: 1;\n}\n\n.data-header {\n  padding: 16px 16px 12px 16px;\n  border-bottom: 1px solid #f0f1f3;\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n.sample-tag {\n  background: linear-gradient(135deg, #e8f4ff, #f0f9ff);\n  color: #1664ff;\n  padding: 4px 8px;\n  font-size: 11px;\n  border-radius: 6px;\n  font-weight: 500;\n  border: 1px solid #d4e8ff;\n}\n\n.data-title {\n  font-weight: 600;\n  font-size: 14px;\n  color: #1f2329;\n  flex: 1;\n  min-width: 0;\n}\n\n.common-tag {\n  background-color: #f7f8fa;\n  color: #86909c;\n  padding: 4px 8px;\n  font-size: 11px;\n  border-radius: 6px;\n  font-weight: 500;\n  border: 1px solid #e8eaec;\n}\n\n.data-fields {\n  padding: 12px 16px 16px 16px;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n  align-items: center;\n}\n\n/* 字段标签样式 */\n.field-tag {\n  background-color: #f7f8fa !important;\n  color: #4e5969 !important;\n  border: 1px solid #e8eaec !important;\n  border-radius: 6px !important;\n  font-size: 11px !important;\n  font-weight: 500 !important;\n  padding: 2px 6px !important;\n  margin: 0 !important;\n}\n\n.more-fields {\n  color: #86909c;\n  font-size: 11px;\n  font-weight: 500;\n  padding: 2px 6px;\n  background-color: #f0f1f3;\n  border-radius: 6px;\n}\n\n/* 数据集指示器样式 */\n.dataset-indicator {\n  width: 100%;\n  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);\n  border: 1px solid #e8f4ff;\n  border-radius: 12px;\n  padding: 16px 20px;\n  margin-bottom: 24px;\n  box-shadow: 0 2px 8px rgba(22, 100, 255, 0.06);\n  transition: all 0.3s ease;\n}\n\n.dataset-info {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.dataset-main-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.dataset-main-info .el-icon-database {\n  color: #1664ff;\n  font-size: 16px;\n}\n\n.dataset-name {\n  font-weight: 600;\n  font-size: 15px;\n  color: #1f2329;\n}\n\n.dataset-fields-count {\n  color: #86909c;\n  font-size: 12px;\n  background-color: #f0f1f3;\n  padding: 2px 8px;\n  border-radius: 6px;\n}\n\n.dataset-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.dataset-preview-btn,\n.dataset-change-btn {\n  color: #1664ff !important;\n  font-size: 12px !important;\n  padding: 4px 8px !important;\n  border-radius: 6px !important;\n  transition: all 0.2s ease !important;\n}\n\n.dataset-preview-btn:hover,\n.dataset-change-btn:hover {\n  background-color: #e8f4ff !important;\n  color: #0f4fff !important;\n}\n\n.dataset-fields-preview {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n  align-items: center;\n}\n\n.field-tag-preview {\n  font-size: 11px !important;\n  font-weight: 500 !important;\n  padding: 2px 6px !important;\n  border-radius: 6px !important;\n  margin: 0 !important;\n}\n\n.more-fields-preview {\n  color: #86909c;\n  font-size: 11px;\n  font-weight: 500;\n  padding: 2px 6px;\n  background-color: #f0f1f3;\n  border-radius: 6px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.multi-line-input .el-input__inner {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  height: auto !important;\n  line-height: inherit;\n  padding: 6px 10px;\n  min-height: 40px;\n}\n\n/* 数据选择区域样式 */\n.data-selection {\n  width: 100%;\n  margin-bottom: 32px;\n  transition: all 0.3s ease;\n}\n\n.data-selection h3 {\n  font-size: 18px;\n  font-weight: 600;\n  color: #1f2329;\n  margin: 0 0 20px 0;\n  text-align: center;\n}\n\n/* 底部问题输入区域 */\n.question-input-container {\n  width: 100%;\n  padding: 24px 16px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-top: 20px;\n}\n\n.question-input-container > span {\n  margin-bottom: 12px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.question-input-wrapper {\n  align-items: center;\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n  background-color: #f8faff;\n  border: 1px solid #e8f4ff;\n  border-radius: 24px;\n  padding: 8px 16px;\n  box-shadow: 0 2px 8px rgba(22, 100, 255, 0.06);\n  transition: all 0.3s ease;\n}\n\n.question-input-wrapper:hover {\n  border-color: #1664ff;\n  box-shadow: 0 4px 12px rgba(22, 100, 255, 0.12);\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n  float: right;\n}\n\n.action-btn {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  background-color: #ffffff;\n  border: 1px solid #e8eaec;\n  margin-left: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n  transition: all 0.2s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);\n}\n\n.action-btn:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);\n}\n\n.send-btn {\n  background: linear-gradient(135deg, #1664ff, #0f4fff);\n  color: white;\n  border: none;\n}\n\n.send-btn:hover {\n  background: linear-gradient(135deg, #0f4fff, #0a3ccc);\n}\n\n.test-btn {\n  background: linear-gradient(135deg, #00d4aa, #00b894);\n  color: white;\n  border: none;\n}\n\n.debug-btn {\n  background: linear-gradient(135deg, #86909c, #6b7280);\n  color: white;\n  border: none;\n}\n\n.send-btn:disabled {\n  background: #c0c4cc;\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);\n}\n\n/* Suggested Questions Panel */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n\n/* Message list style */\n.message-list {\n  width: 100%;\n  max-height: calc(100vh - 280px);\n  overflow-y: auto;\n  margin-top: 20px;\n  padding: 16px;\n  background-color: transparent;\n  border-radius: 12px;\n  flex: 1;\n}\n\n.message {\n  margin-bottom: 24px;\n  display: flex;\n  position: relative;\n  max-width: 85%;\n}\n\n.message-content {\n  padding: 14px 18px;\n  border-radius: 12px;\n  line-height: 1.6;\n  font-size: 14px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n  border: 1px solid transparent;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n  align-self: flex-end;\n  margin-left: auto;\n}\n\n.user-message .message-content {\n  background: linear-gradient(135deg, #1664ff, #0f4fff);\n  color: #ffffff;\n  margin-right: 12px;\n  border-color: #1664ff;\n}\n\n.bot-message {\n  align-self: flex-start;\n  margin-right: auto;\n}\n\n.bot-message .message-content {\n  background-color: #ffffff;\n  color: #1f2329;\n  margin-left: 12px;\n  border-color: #e8eaec;\n}\n\n.avatar-container {\n  display: flex;\n  align-items: flex-start;\n}\n\n.bot-avatar, .user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border: 2px solid #ffffff;\n}\n\n.bot-avatar {\n  background: linear-gradient(135deg, #00d4aa, #00b894);\n  color: white;\n}\n\n.user-avatar {\n  background: linear-gradient(135deg, #1664ff, #0f4fff);\n  color: white;\n}\n\n/* Loading animation */\n.loading-dots {\n  display: inline-block;\n}\n\n.dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  background-color: #999;\n  border-radius: 50%;\n  margin: 0 2px;\n  animation: pulse 1.2s infinite ease-in-out both;\n}\n\n.dot:nth-child(2) {\n  animation-delay: -0.4s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: -0.8s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(0.6);\n    opacity: 0.4;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* Styles related to charts */\n.chart-container {\n  position: relative;\n  margin-top: 16px;\n  border: 1px solid #e8eaec;\n  border-radius: 12px;\n  padding: 16px;\n  background-color: #ffffff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\n}\n\n/* 确保图表容器有足够高度 */\n.chart-canvas {\n  min-height: 320px;\n}\n\n.chart-notice {\n  color: #1664ff;\n  font-size: 14px;\n  margin: 12px 0;\n  font-weight: 600;\n  text-align: center;\n  padding: 8px 16px;\n  background: linear-gradient(135deg, #e8f4ff, #f0f9ff);\n  border-radius: 8px;\n  border: 1px solid #d4e8ff;\n}\n\n/* AI original response popup layer style */\n.raw-response-panel {\n  position: fixed;\n  bottom: 100px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 800px;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n  z-index: 1000;\n  max-height: 60%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.raw-response-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 16px;\n  color: #303133;\n  background-color: #f5f7fa;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.raw-response-title .el-icon-monitor {\n  margin-right: 8px;\n}\n\n.raw-response-title .close-btn {\n  background-color: #f5f7fa;\n  border: 1px solid #ebeef5;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.raw-response-title .close-btn:hover {\n  background-color: #ebeef5;\n}\n\n.raw-response-content {\n  flex: 1;\n  padding: 15px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #303133;\n  white-space: pre-wrap;\n  word-break: break-all;\n  overflow-wrap: break-word;\n}\n\n/* 添加PDF导出相关样式 */\n.chart-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-bottom: 10px;\n}\n\n/* 数据限制提示样式 */\n.data-limit-notice {\n  margin-top: 12px;\n  padding: 10px 16px;\n  background: linear-gradient(135deg, #f0f9eb, #f7fcf0);\n  color: #00b894;\n  border: 1px solid #d4e8d4;\n  border-radius: 8px;\n  font-size: 12px;\n  text-align: center;\n  font-weight: 500;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .sidebar {\n    transform: translateX(-100%);\n    position: fixed;\n    z-index: 1000;\n    transition: transform 0.3s ease;\n  }\n\n  .main-content {\n    padding: 16px 20px;\n  }\n\n  .message {\n    max-width: 95%;\n    margin-left: 0;\n    margin-right: 0;\n  }\n\n  .data-card {\n    margin-bottom: 12px;\n  }\n\n  .dataset-indicator {\n    padding: 12px 16px;\n  }\n\n  .dataset-info {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .question-input-wrapper {\n    max-width: 100%;\n    padding: 6px 12px;\n  }\n}\n\n@media (max-width: 480px) {\n  .header h2 {\n    font-size: 24px;\n  }\n\n  .data-selection h3 {\n    font-size: 16px;\n  }\n\n  .message-content {\n    padding: 10px 14px;\n    font-size: 13px;\n  }\n}\n</style>"], "mappings": ";;;;;;;;;;;;;AAkUA,SAAAA,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,YAAA;AACA;AACA,OAAAC,WAAA;AACA,SAAAC,KAAA;AACA,YAAAC,OAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MAAA;MACAC,QAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;MACAC,oBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,MAAA;MAAA;MACAC,SAAA;MAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MAAA;MACAC,sBAAA;MAAA;MACAC,WAAA;MACAC,SAAA;MACAC,YAAA;MAAA;MACAC,aAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,YAAA,CAAAC,OAAA,kBAAAR,QAAA;IACA,KAAAS,UAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;IACA;IACA,KAAA1B,kBAAA,IACA,gBACA,kBACA,kBACA,mBACA;EACA;EACA2B,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,eAAA;MACA,KAAAN,UAAA;MACA,KAAAd,MAAA;IACA;IACA;IACAe,aAAA;MACA,IAAAM,cAAA,GAAAT,YAAA,CAAAU,OAAA;MACA,KAAAD,cAAA;QACAA,cAAA,QAAAE,YAAA,CAAApD,MAAA;QACAyC,YAAA,CAAAC,OAAA,mBAAAQ,cAAA;MACA;MACA,KAAA1B,QAAA,GAAA0B,cAAA;IACA;IAEA;IACAE,aAAAC,IAAA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAG,MAAA,IAAAD,CAAA,MAAAA,CAAA;QACA,MAAAE,QAAA,GAAAJ,IAAA,CAAAE,CAAA;QACAD,MAAA,GAAAA,MAAA,SAAAI,QAAA,CAAAD,QAAA;MACA;MACA,OAAAH,MAAA;IACA;IAEA;IACAT,kBAAA;MACA,KAAAtB,QAAA,CAAAoC,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IAEA;IACAf,eAAA;MACA,SAAAgB,KAAA,CAAArC,cAAA;QACA,KAAAqC,KAAA,CAAArC,cAAA,CAAAsC,SAAA,QAAAD,KAAA,CAAArC,cAAA,CAAAuC,YAAA;MACA;IACA;IAEA,MAAAtB,WAAA;MACA;QACA,MAAAuB,GAAA,SAAApE,OAAA,CAAAqE,YAAA;QACA,IAAAD,GAAA,CAAA1D,IAAA,IAAA0D,GAAA,CAAA1D,IAAA,CAAA4D,IAAA;UACA,KAAAzD,MAAA,GAAAuD,GAAA,CAAA1D,IAAA,CAAAA,IAAA;UACA,KAAAI,QAAA,QAAAyD,eAAA,MAAA1D,MAAA;UACA,KAAAE,gBAAA,QAAAD,QAAA;QACA;UACA,KAAAD,MAAA;UACA,KAAAC,QAAA;UACA,KAAAC,gBAAA;QACA;MACA,SAAAyD,CAAA;QACA,KAAA3D,MAAA;QACA,KAAAC,QAAA;QACA,KAAAC,gBAAA;MACA;IACA;IACA;IACAwD,gBAAAE,IAAA;MACA,IAAAC,MAAA;MACA,WAAAC,IAAA,IAAAF,IAAA;QACA,IAAAE,IAAA,CAAAC,IAAA;UACAF,MAAA,CAAAb,IAAA,CAAAc,IAAA;QACA,WAAAA,IAAA,CAAAE,QAAA,IAAAF,IAAA,CAAAE,QAAA,CAAAnB,MAAA;UACAgB,MAAA,GAAAA,MAAA,CAAAI,MAAA,MAAAP,eAAA,CAAAI,IAAA,CAAAE,QAAA;QACA;MACA;MACA,OAAAH,MAAA;IACA;IACA;IACAK,gBAAA;MACA,MAAAC,OAAA,QAAAhE,aAAA,CAAAiE,IAAA,GAAAC,WAAA;MACA,KAAAF,OAAA;QACA,KAAAjE,gBAAA,QAAAD,QAAA;MACA;QACA,KAAAC,gBAAA,QAAAD,QAAA,CAAAqE,MAAA,CAAAC,EAAA,IAAAA,EAAA,CAAA5E,IAAA,IAAA4E,EAAA,CAAA5E,IAAA,CAAA0E,WAAA,GAAAG,QAAA,CAAAL,OAAA;MACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAM,eAAA;MACA,UAAArD,oBAAA,UAAAC,aAAA,CAAAwB,MAAA;QACA,KAAA6B,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAC,UAAA,QAAAvD,aAAA,CAAAwD,GAAA,CAAAC,KAAA;QACA;QACA;UACAC,EAAA,EAAAD,KAAA,CAAAC,EAAA;UACAC,UAAA,EAAAF,KAAA,CAAAE,UAAA;UACArF,IAAA,EAAAmF,KAAA,CAAAnF,IAAA;UACAsF,YAAA,EAAAH,KAAA,CAAAG,YAAA;UACAC,SAAA,EAAAJ,KAAA,CAAAI,SAAA;UACAC,IAAA,EAAAL,KAAA,CAAAK,IAAA;UACAC,YAAA,EAAAN,KAAA,CAAAM,YAAA;UACAC,cAAA,EAAAP,KAAA,CAAAO,cAAA;UACAC,cAAA,EAAAR,KAAA,CAAAQ,cAAA;QACA;MACA;;MAEA;MACA,KAAA9D,sBAAA;QACAuD,EAAA,OAAA3D,oBAAA,CAAA2D,EAAA;QACApF,IAAA,OAAAyB,oBAAA,CAAAzB,IAAA;QACA4F,MAAA,EAAAX;MACA;;MAEA;MACA,KAAAjE,aAAA;;MAEA;MACA,KAAA+D,QAAA,CAAAc,OAAA,gBAAApE,oBAAA,CAAAzB,IAAA;;MAEA;MACA,KAAA8F,SAAA;QACA,MAAAC,OAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,OAAA,EAAAA,OAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACAC,yBAAAC,OAAA;MACA;MACA,KAAAC,iBAAA,CAAAD,OAAA,EAAAE,IAAA;QACA;QACAC,UAAA;UACA,KAAAzB,cAAA;QACA;MACA;IACA;IAEA;IACA0B,cAAA;MACA,KAAA3E,sBAAA;MACA,KAAAkD,QAAA,CAAA0B,IAAA;IACA;IAEA,MAAAJ,kBAAAD,OAAA;MACAM,OAAA,CAAAC,GAAA,8BAAAP,OAAA;;MAEA;MACA,KAAAA,OAAA,KAAAA,OAAA,CAAAhB,EAAA;QACAsB,OAAA,CAAAE,KAAA,cAAAR,OAAA;QACA,OAAAS,OAAA,CAAAC,MAAA,KAAAC,KAAA;MACA;;MAEA;MACA,MAAAC,OAAA;QACA,mBAAApF,QAAA;QACA;QACA;QACA;MACA;MAEA;QACA8E,OAAA,CAAAC,GAAA,kBAAAP,OAAA,CAAAhB,EAAA;QACA,MAAAxB,GAAA,SAAApE,OAAA,CAAAyH,gBAAA,CAAAb,OAAA,CAAAhB,EAAA,SAAA4B,OAAA;QACAN,OAAA,CAAAC,GAAA,gBAAA/C,GAAA;QAEA,IAAAA,GAAA,CAAA1D,IAAA,IAAA0D,GAAA,CAAA1D,IAAA,CAAA4D,IAAA;UACA,MAAAoD,MAAA,GAAAtD,GAAA,CAAA1D,IAAA,CAAAA,IAAA;UACAwG,OAAA,CAAAC,GAAA,aAAAO,MAAA;UACA,KAAAzF,oBAAA,GAAAyF,MAAA;UACA;UACA,KAAAxF,aAAA,GAAAwF,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAhH,IAAA,IAAAgH,MAAA,CAAAhH,IAAA,CAAA0F,MAAA;UACA;UACA,KAAAjE,WAAA,GAAAuF,MAAA,CAAAhH,IAAA,IAAAgH,MAAA,CAAAhH,IAAA,CAAAA,IAAA;UACA;UACAwG,OAAA,CAAAC,GAAA,eAAAjF,aAAA,CAAAwB,MAAA;UACAwD,OAAA,CAAAC,GAAA,eAAAhF,WAAA,CAAAuB,MAAA;UAEA,SAAAxB,aAAA,CAAAwB,MAAA;YACAwD,OAAA,CAAAU,IAAA;UACA;UAEA,SAAAzF,WAAA,CAAAuB,MAAA;YACAwD,OAAA,CAAAU,IAAA;UACA;UAEA,KAAApG,aAAA;UACA,OAAA6F,OAAA,CAAAQ,OAAA,CAAAH,MAAA;QACA;UACAR,OAAA,CAAAE,KAAA,aAAAhD,GAAA,CAAA1D,IAAA;UACA,KAAA6E,QAAA,CAAA6B,KAAA,aAAAhD,GAAA,CAAA1D,IAAA,CAAAoH,GAAA;UACA,KAAA7F,oBAAA;UACA,KAAAC,aAAA;UACA,KAAAC,WAAA;UACA,KAAAX,aAAA;UACA,OAAA6F,OAAA,CAAAC,MAAA,KAAAC,KAAA,CAAAnD,GAAA,CAAA1D,IAAA,CAAAoH,GAAA;QACA;MACA,SAAAtD,CAAA;QACA0C,OAAA,CAAAE,KAAA,eAAA5C,CAAA;QACA,KAAAe,QAAA,CAAA6B,KAAA,UAAA5C,CAAA,CAAAuD,OAAA;QACA,KAAA9F,oBAAA;QACA,KAAAC,aAAA;QACA,KAAAC,WAAA;QACA,KAAAX,aAAA;QACA,OAAA6F,OAAA,CAAAC,MAAA,CAAA9C,CAAA;MACA;IACA;IACAwD,gBAAA;MACA,KAAA5G,oBAAA,SAAAA,oBAAA;IACA;IACA6G,YAAAC,CAAA;MACA,KAAAhH,QAAA,GAAAgH,CAAA;MACA,KAAA9G,oBAAA;IACA;IACA+G,eAAAC,KAAA;MACA,IAAAA,KAAA,CAAAC,SAAA;QACA;MACA;MACA;IACA;IAEA;IACA,MAAAC,eAAA;MACA,UAAApH,QAAA,CAAA+D,IAAA,WAAAtD,SAAA;QACA,KAAA4D,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA;MACA,IAAA+C,SAAA,QAAArH,QAAA,CAAA+D,IAAA;;MAEA;MACA,SAAA5C,sBAAA;QACA;QACA,MAAAmG,WAAA;UACAC,SAAA,OAAApG,sBAAA,CAAAuD,EAAA;UACA8C,WAAA,OAAArG,sBAAA,CAAA7B,IAAA;UACA4F,MAAA,OAAA/D,sBAAA,CAAA+D;QACA;;QAEA;QACAmC,SAAA,GAAAI,IAAA,CAAAC,SAAA;UACA1H,QAAA,OAAAA,QAAA,CAAA+D,IAAA;UACA2B,OAAA,EAAA4B;QACA;MACA;;MAEA;MACA,MAAAK,OAAA;QACA/E,MAAA;QACAC,OAAA,OAAA7C,QAAA,CAAA+D,IAAA;QACAjB,QAAA;MACA;MACA,KAAAvC,QAAA,CAAAoC,IAAA,CAAAgF,OAAA;;MAEA;MACA,MAAAC,MAAA;QACAhF,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAAvC,QAAA,CAAAoC,IAAA,CAAAiF,MAAA;;MAEA;MACA,MAAAC,OAAA,QAAAtH,QAAA,MAAAA,QAAA,CAAAiC,MAAA;;MAEA;MACA,MAAAxC,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA;MACA,KAAAS,SAAA;MAEA;QACA;QACA,MAAAoG,OAAA,QAAA1F,sBAAA,GACAkG,SAAA,GACA,GAAArH,QAAA;;QAEA;QACA,MAAAf,KAAA,CAAA6I,IAAA,CACA,4CACA;UAAAtH,QAAA,OAAAA,QAAA;UAAAqG;QAAA,GACA;UACAkB,YAAA;UACAC,kBAAA,EAAA1E,CAAA;YACA,MAAA2E,QAAA,GAAA3E,CAAA,CAAA4E,KAAA,CAAAC,MAAA,CAAAC,YAAA;YACA,IAAAC,OAAA,GAAAJ,QAAA,CAAAK,SAAA,CAAAT,OAAA,CAAAhF,OAAA,CAAAL,MAAA;YACAqF,OAAA,CAAAhF,OAAA,IAAAwF,OAAA;YACA,KAAAtG,cAAA;;YAEA;YACA,KAAAnB,eAAA,GAAAqH,QAAA;UACA;QACA,CACA;;QAEA;QACAJ,OAAA,CAAA/E,QAAA;QACA,KAAArC,SAAA;;QAEA;QACA,KAAA8H,gBAAA,CAAAV,OAAA;MACA,SAAA3B,KAAA;QACAF,OAAA,CAAAE,KAAA,UAAAA,KAAA;QACA2B,OAAA,CAAAhF,OAAA;QACAgF,OAAA,CAAA/E,QAAA;QACA,KAAArC,SAAA;MACA;IACA;IAEA;IACA8H,iBAAA1B,OAAA;MACAb,OAAA,CAAAC,GAAA,qBAAAY,OAAA,CAAAhE,OAAA;;MAEA;MACA,MAAA2F,gBAAA,GAAA3B,OAAA,CAAAhE,OAAA,CAAA4F,KAAA;MACA,IAAAD,gBAAA,IAAAA,gBAAA;QACA,MAAAE,WAAA,GAAAF,gBAAA;QACAxC,OAAA,CAAAC,GAAA,cAAAyC,WAAA;;QAEA;QACA,KAAAC,kBAAA,CAAAD,WAAA,EAAA7B,OAAA;QACA;MACA;;MAEA;MACA,MAAA+B,gBAAA,GAAA/B,OAAA,CAAAhE,OAAA,CAAA4F,KAAA;MACA,IAAAG,gBAAA,IAAAA,gBAAA;QACA5C,OAAA,CAAAC,GAAA,eAAA2C,gBAAA;QAEA;UACA,IAAAC,WAAA,GAAApB,IAAA,CAAAqB,KAAA,CAAAF,gBAAA;UACA5C,OAAA,CAAAC,GAAA,gBAAA4C,WAAA;;UAEA;UACA,IAAAA,WAAA,CAAAzF,IAAA,UAAAyF,WAAA,CAAArJ,IAAA;YACAwG,OAAA,CAAAC,GAAA,yBAAA4C,WAAA,CAAArJ,IAAA;YACAqJ,WAAA,GAAAA,WAAA,CAAArJ,IAAA;UACA;;UAEA;UACA,IAAAqJ,WAAA,CAAA/D,IAAA,KAAA+D,WAAA,CAAAtB,SAAA,IAAAsB,WAAA,CAAAE,OAAA,IAAAF,WAAA,CAAArJ,IAAA;YACAwG,OAAA,CAAAC,GAAA,eAAA4C,WAAA;YACAhC,OAAA,CAAAgC,WAAA,GAAAA,WAAA;;YAEA;YACAhC,OAAA,CAAAhE,OAAA,GAAAgE,OAAA,CAAAhE,OAAA,CAAAmG,OAAA,6BACA;YAEA;UACA;YACAhD,OAAA,CAAAC,GAAA,gBAAA4C,WAAA;UACA;QACA,SAAA3C,KAAA;UACAF,OAAA,CAAAE,KAAA,cAAAA,KAAA;QACA;MACA;MAEAF,OAAA,CAAAC,GAAA;;MAEA;MACA;QACA;QACA,MAAAgD,SAAA;QACA,MAAAC,SAAA,GAAArC,OAAA,CAAAhE,OAAA,CAAA4F,KAAA,CAAAQ,SAAA;QAEA,IAAAC,SAAA;UACAlD,OAAA,CAAAC,GAAA,kBAAAiD,SAAA;;UAEA;UACA;UACA,MAAAC,OAAA,GAAAD,SAAA,IAAAF,OAAA;UACAhD,OAAA,CAAAC,GAAA,iBAAAkD,OAAA;UAEA;YACA,MAAAN,WAAA,GAAApB,IAAA,CAAAqB,KAAA,CAAAK,OAAA;YACAnD,OAAA,CAAAC,GAAA,gBAAA4C,WAAA;YAEA,IAAAA,WAAA,CAAAzF,IAAA,UAAAyF,WAAA,CAAArJ,IAAA;cACA,MAAAA,IAAA,GAAAqJ,WAAA,CAAArJ,IAAA;cACAwG,OAAA,CAAAC,GAAA,aAAAzG,IAAA;;cAEA;cACA,IAAAA,IAAA,CAAAsF,IAAA,KAAAtF,IAAA,CAAAuJ,OAAA,IAAAvJ,IAAA,CAAAA,IAAA;gBACAwG,OAAA,CAAAC,GAAA,eAAAzG,IAAA;gBACAqH,OAAA,CAAAgC,WAAA,GAAArJ,IAAA;gBACAqH,OAAA,CAAAhE,OAAA,GAAAgE,OAAA,CAAAhE,OAAA,CAAAmG,OAAA,CAAAE,SAAA,KACA;gBACA;cACA;YACA;UACA,SAAAE,UAAA;YACApD,OAAA,CAAAC,GAAA,cAAAmD,UAAA;UACA;QACA;MACA,SAAAlD,KAAA;QACAF,OAAA,CAAAC,GAAA,aAAAC,KAAA;MACA;;MAEA;MACA,IAAAW,OAAA,CAAAhE,OAAA,CAAAsB,QAAA,iBACA0C,OAAA,CAAAhE,OAAA,CAAAsB,QAAA;QACA6B,OAAA,CAAAC,GAAA;;QAEA;QACA,MAAAoD,aAAA;UACA3E,EAAA,EAAA4E,IAAA,CAAAC,GAAA,GAAAC,QAAA;UACA1E,IAAA;UACA2E,KAAA;UACAV,OAAA,OAAAhJ,aAAA,EAAA2E,EAAA;QACA;QAEAsB,OAAA,CAAAC,GAAA,eAAAoD,aAAA;QACAxC,OAAA,CAAAgC,WAAA,GAAAQ,aAAA;MACA;IACA;IAEA;IACA,MAAAV,mBAAAD,WAAA,EAAA7B,OAAA;MACA;QACAb,OAAA,CAAAC,GAAA,qBAAAyC,WAAA;QACA,MAAAgB,QAAA,SAAAzK,KAAA,CAAA0K,GAAA,yCAAAjB,WAAA;QAEA,IAAAgB,QAAA,CAAAlK,IAAA,IAAAkK,QAAA,CAAAE,MAAA;UACA5D,OAAA,CAAAC,GAAA,cAAAyD,QAAA,CAAAlK,IAAA;;UAEA;UACA,IAAAqJ,WAAA,GAAAa,QAAA,CAAAlK,IAAA;UACA,IAAAqJ,WAAA,CAAAzF,IAAA,UAAAyF,WAAA,CAAArJ,IAAA;YACAqJ,WAAA,GAAAA,WAAA,CAAArJ,IAAA;UACA;;UAEA;UACAqH,OAAA,CAAAgC,WAAA,GAAAA,WAAA;;UAEA;UACA,KAAAhC,OAAA,CAAAhE,OAAA,CAAAsB,QAAA;YACA0C,OAAA,CAAAhE,OAAA;UACA;;UAEA;UACA,KAAAgH,YAAA;QACA;UACA7D,OAAA,CAAAE,KAAA,cAAAwD,QAAA;UACA7C,OAAA,CAAAhE,OAAA;QACA;MACA,SAAAqD,KAAA;QACAF,OAAA,CAAAE,KAAA,cAAAA,KAAA;QACAW,OAAA,CAAAhE,OAAA,0CAAAqD,KAAA,CAAAW,OAAA;MACA;IACA;IAEA;IACAiD,UAAA;MACA;MACA,MAAAC,eAAA;QACArF,EAAA;QACAI,IAAA;QACA2E,KAAA;QACAlC,SAAA;QACAwB,OAAA;MACA;;MAEA;MACA,MAAAnB,MAAA;QACAhF,MAAA;QACAC,OAAA;QACAC,QAAA;QACA+F,WAAA,EAAAkB;MACA;MAEA,KAAAxJ,QAAA,CAAAoC,IAAA,CAAAiF,MAAA;;MAEA;MACA,KAAAhH,eAAA,GAAA6G,IAAA,CAAAC,SAAA,CAAAqC,eAAA;;MAEA;MACA/D,OAAA,CAAAC,GAAA,iBAAA1F,QAAA;;MAEA;MACA,MAAAyJ,eAAA;QACA5G,IAAA;QACAwD,GAAA;QACApH,IAAA;UACAsF,IAAA;UACAtF,IAAA,GACA;YAAAiF,KAAA;YAAAwF,KAAA;UAAA,GACA;YAAAxF,KAAA;YAAAwF,KAAA;UAAA,GACA;YAAAxF,KAAA;YAAAwF,KAAA;UAAA,EACA;UACAC,OAAA;YAAA5K,IAAA;UAAA;QACA;MACA;;MAEA;MACA,MAAA6K,WAAA,iBAAA1C,IAAA,CAAAC,SAAA,CAAAsC,eAAA;MACA,MAAAI,cAAA;QACAxH,MAAA;QACAC,OAAA,gBAAAsH,WAAA;QACArH,QAAA;MACA;MAEA,KAAAvC,QAAA,CAAAoC,IAAA,CAAAyH,cAAA;MACA,KAAA7B,gBAAA,CAAA6B,cAAA;;MAEA;MACA,KAAAxJ,eAAA,GAAAuJ,WAAA;MAEAnE,OAAA,CAAAC,GAAA,iBAAAmE,cAAA;IACA;IAEA;IACAC,aAAA;MACArE,OAAA,CAAAC,GAAA;;MAEA;MACA,MAAAqE,QAAA;QACAlH,IAAA;QACAwD,GAAA;QACApH,IAAA;UACAkF,EAAA;UACA+E,KAAA;UACAV,OAAA;UACAjE,IAAA;UACAtF,IAAA;YACAA,IAAA,GACA;cAAAyK,KAAA;cAAAxF,KAAA;cAAAnF,IAAA;cAAAiL,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAxF,KAAA;cAAAnF,IAAA;cAAAiL,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAxF,KAAA;cAAAnF,IAAA;cAAAiL,QAAA;YAAA,EACA;YACArF,MAAA,GACA;cAAAR,EAAA;cAAApF,IAAA;cAAAuF,SAAA;YAAA,GACA;cAAAH,EAAA;cAAApF,IAAA;cAAAuF,SAAA;YAAA;UAEA;QACA;MACA;;MAEA;MACA,MAAA2F,WAAA;QACA5H,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MAEA;MACA0H,WAAA,CAAA3B,WAAA,GAAAyB,QAAA,CAAA9K,IAAA;;MAEA;MACA,KAAAe,QAAA,CAAAoC,IAAA,CAAA6H,WAAA;;MAEA;MACA,KAAA5J,eAAA,GAAA6G,IAAA,CAAAC,SAAA,CAAA4C,QAAA;MAEAtE,OAAA,CAAAC,GAAA,gBAAAuE,WAAA;IACA;IAEA;IACAC,gBAAA;MACA,KAAA9J,oBAAA;MACA,KAAAC,eAAA,QAAAL,QAAA,MAAAA,QAAA,CAAAiC,MAAA,MAAAK,OAAA;IACA;IAEA;IACA,MAAA6H,YAAA7D,OAAA;MACA,KAAAA,OAAA,CAAAgC,WAAA;MAEA;QACA;QACA,KAAA8B,IAAA,CAAA9D,OAAA;;QAEA;QACA,MAAA+D,aAAA,GAAAtF,QAAA,CAAAuF,aAAA;QACAD,aAAA,CAAAE,KAAA,CAAAC,QAAA;QACAH,aAAA,CAAAE,KAAA,CAAAE,IAAA;QACAJ,aAAA,CAAAE,KAAA,CAAAG,KAAA;QACAL,aAAA,CAAAE,KAAA,CAAAI,UAAA;QACAN,aAAA,CAAAE,KAAA,CAAAK,OAAA;QACA7F,QAAA,CAAA8F,IAAA,CAAAC,WAAA,CAAAT,aAAA;;QAEA;QACA;QACA,MAAAnB,KAAA,GAAA5C,OAAA,CAAAgC,WAAA,CAAAY,KAAA;QACA,MAAAjC,WAAA,QAAAzH,aAAA,QAAAA,aAAA,CAAAuL,SAAA;QACA,MAAAC,WAAA,OAAAjC,IAAA,GAAAkC,cAAA;;QAEA;QACA,IAAA/L,WAAA,GAAAoH,OAAA,CAAAhE,OAAA,CACAmG,OAAA,6CACAA,OAAA,8CACAjF,IAAA;;QAEA;QACA,IAAAtE,WAAA,CAAA0E,QAAA;UACA,MAAAsH,OAAA,GAAAnG,QAAA,CAAAuF,aAAA;UACAY,OAAA,CAAAC,SAAA,GAAAjM,WAAA;UACAA,WAAA,GAAAgM,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;QACA;;QAEA;QACAhB,aAAA,CAAAc,SAAA;AACA;AACA,2DAAAjC,KAAA;AACA,8DAAAjC,WAAA;AACA,+DAAA+D,WAAA;AACA;;AAEA;AACA;AACA,mBAAA9L,WAAA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;QAEA;QACA,MAAAoM,cAAA,GAAAjB,aAAA,CAAArF,aAAA;QACA,MAAAuG,KAAA,GAAAzM,OAAA,CAAA0M,IAAA,CAAAF,cAAA;;QAEA;QACA,MAAAhD,WAAA,GAAAhC,OAAA,CAAAgC,WAAA;QACA,IAAAmD,OAAA;;QAEA;QACA,QAAAnD,WAAA,CAAA/D,IAAA;UACA;YACAkH,OAAA,QAAAC,kBAAA,CAAApD,WAAA;YACA;UACA;YACAmD,OAAA,QAAAE,mBAAA,CAAArD,WAAA;YACA;UACA;YACAmD,OAAA,QAAAG,kBAAA,CAAAtD,WAAA;YACA;UACA;YACAmD,OAAA,QAAAI,uBAAA,CAAAvD,WAAA;YACA;UACA;YACAmD,OAAA,QAAAK,iBAAA,CAAAxD,WAAA;QACA;;QAEA;QACAiD,KAAA,CAAAQ,SAAA,CAAAN,OAAA;QACAhG,OAAA,CAAAC,GAAA,mBAAA+F,OAAA;;QAEA;QACA,UAAA7F,OAAA,CAAAQ,OAAA,IAAAd,UAAA,CAAAc,OAAA;;QAEA;QACA,MAAA4F,kBAAA,GAAA3B,aAAA,CAAArF,aAAA;QACA,KAAAiH,eAAA,CAAAD,kBAAA,EAAA1F,OAAA,CAAAgC,WAAA;;QAEA;QACA,MAAA4D,MAAA,SAAAtN,WAAA,CAAAyL,aAAA;UACA8B,KAAA;UAAA;UACAC,OAAA;UACAC,UAAA;UACAC,eAAA;QACA;;QAEA;QACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;QACA,MAAAC,GAAA,OAAA5N,KAAA;UACA6N,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,MAAAC,QAAA;QACA,MAAAC,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAAxB,KAAA;;QAEA;QACA+B,GAAA,CAAAO,QAAA,CAAAT,OAAA,iBAAAM,QAAA,EAAAC,SAAA;;QAEA;QACAL,GAAA,CAAAQ,IAAA,IAAA/D,KAAA,QAAAH,IAAA,GAAAmE,OAAA;;QAEA;QACAnI,QAAA,CAAA8F,IAAA,CAAAsC,WAAA,CAAA9C,aAAA;QACAkB,KAAA,CAAA6B,OAAA;;QAEA;QACA,KAAAhD,IAAA,CAAA9D,OAAA;;QAEA;QACA,KAAAxC,QAAA,CAAAc,OAAA;MAEA,SAAAe,KAAA;QACAF,OAAA,CAAAE,KAAA,aAAAA,KAAA;QACA,KAAAyE,IAAA,CAAA9D,OAAA;QACA,KAAAxC,QAAA,CAAA6B,KAAA,eAAAA,KAAA,CAAAW,OAAA;MACA;IACA;IAEA;IACA,MAAA+G,sBAAA;MACA;QACA;QACA,KAAAtM,YAAA;;QAEA;QACA,IAAAuM,aAAA;;QAEA;QACA,SAAAtL,CAAA,MAAAA,CAAA,QAAAhC,QAAA,CAAAiC,MAAA,EAAAD,CAAA;UACA,MAAAsE,OAAA,QAAAtG,QAAA,CAAAgC,CAAA;UACA,KAAAsE,OAAA,CAAAjE,MAAA,IAAAiE,OAAA,CAAAgC,WAAA;YACAgF,aAAA,CAAAlL,IAAA;cACAkE,OAAA,EAAAA,OAAA;cACAiH,KAAA,EAAAvL;YACA;UACA;QACA;;QAEA;QACA,IAAAsL,aAAA,CAAArL,MAAA;UACA,KAAA6B,QAAA,CAAAC,OAAA;UACA,KAAAhD,YAAA;UACA;QACA;;QAEA;QACA,MAAA0L,GAAA,OAAA5N,KAAA;UACA6N,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,SAAA5K,CAAA,MAAAA,CAAA,GAAAsL,aAAA,CAAArL,MAAA,EAAAD,CAAA;UACA;UACA,IAAAA,CAAA;YACAyK,GAAA,CAAAe,OAAA;UACA;UAEA,MAAAC,YAAA,GAAAH,aAAA,CAAAtL,CAAA,EAAAsE,OAAA;UACA,MAAAgC,WAAA,GAAAmF,YAAA,CAAAnF,WAAA;;UAEA;UACA,MAAA+B,aAAA,GAAAtF,QAAA,CAAAuF,aAAA;UACAD,aAAA,CAAAE,KAAA,CAAAC,QAAA;UACAH,aAAA,CAAAE,KAAA,CAAAE,IAAA;UACAJ,aAAA,CAAAE,KAAA,CAAAG,KAAA;UACAL,aAAA,CAAAE,KAAA,CAAAI,UAAA;UACAN,aAAA,CAAAE,KAAA,CAAAK,OAAA;UACA7F,QAAA,CAAA8F,IAAA,CAAAC,WAAA,CAAAT,aAAA;;UAEA;UACA,MAAAnB,KAAA,GAAAZ,WAAA,CAAAY,KAAA;UACA,MAAA8B,WAAA,OAAAjC,IAAA,GAAAkC,cAAA;UACA,MAAAhE,WAAA,QAAAzH,aAAA,QAAAA,aAAA,CAAAuL,SAAA;UAEA,IAAA2C,WAAA;AACA;AACA,mGAAAxE,KAAA;AACA;AACA,8DAAAjC,WAAA;AACA,+DAAA+D,WAAA;AACA;;AAEA;AACA;AACA;;UAEA;UACA,IAAA9L,WAAA,GAAAuO,YAAA,CAAAnL,OAAA;;UAEA;UACA,IAAApD,WAAA,CAAA0E,QAAA;YACA,MAAAsH,OAAA,GAAAnG,QAAA,CAAAuF,aAAA;YACAY,OAAA,CAAAC,SAAA,GAAAjM,WAAA;YACAA,WAAA,GAAAgM,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;UACA;UAEAqC,WAAA;AACA,wEAAAxO,WAAA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;UAEAwO,WAAA;;UAEA;UACArD,aAAA,CAAAc,SAAA,GAAAuC,WAAA;;UAEA;UACA,MAAApC,cAAA,GAAAjB,aAAA,CAAArF,aAAA;UACA,MAAAgH,kBAAA,GAAA3B,aAAA,CAAArF,aAAA;UAEA,IAAAsG,cAAA,IAAAU,kBAAA;YACA;YACA,MAAA2B,aAAA,GAAA7O,OAAA,CAAA0M,IAAA,CAAAF,cAAA;YACA,IAAAG,OAAA;;YAEA;YACA,QAAAnD,WAAA,CAAA/D,IAAA;cACA;gBACAkH,OAAA,QAAAC,kBAAA,CAAApD,WAAA;gBACA;cACA;gBACAmD,OAAA,QAAAE,mBAAA,CAAArD,WAAA;gBACA;cACA;gBACAmD,OAAA,QAAAG,kBAAA,CAAAtD,WAAA;gBACA;cACA;gBACAmD,OAAA,QAAAI,uBAAA,CAAAvD,WAAA;gBACA;cACA;gBACAmD,OAAA,QAAAK,iBAAA,CAAAxD,WAAA;YACA;YAEAqF,aAAA,CAAA5B,SAAA,CAAAN,OAAA;;YAEA;YACA,KAAAQ,eAAA,CAAAD,kBAAA,EAAA1D,WAAA;;YAEA;YACA,UAAA1C,OAAA,CAAAQ,OAAA,IAAAd,UAAA,CAAAc,OAAA;;YAEA;YACA,MAAA8F,MAAA,SAAAtN,WAAA,CAAAyL,aAAA;cACA8B,KAAA;cACAC,OAAA;cACAC,UAAA;cACAC,eAAA;YACA;;YAEA;YACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;;YAEA;YACA,MAAAoB,SAAA,GAAAnB,GAAA,CAAAoB,QAAA,CAAAC,QAAA,CAAAC,QAAA;YACA,MAAAC,UAAA,GAAAvB,GAAA,CAAAoB,QAAA,CAAAC,QAAA,CAAAG,SAAA;YACA,MAAApB,QAAA,GAAAe,SAAA;YACA,MAAAd,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAAxB,KAAA;;YAEA;YACA,IAAAwD,cAAA,GAAApB,SAAA;YACA,IAAAqB,aAAA,GAAAtB,QAAA;YAEA,IAAAC,SAAA,GAAAkB,UAAA;cAAA;cACAE,cAAA,GAAAF,UAAA;cACAG,aAAA,GAAAjC,MAAA,CAAAxB,KAAA,GAAAwD,cAAA,GAAAhC,MAAA,CAAAa,MAAA;YACA;;YAEA;YACA,MAAAqB,IAAA,IAAAR,SAAA,GAAAO,aAAA;YACA,MAAAE,IAAA;;YAEA5B,GAAA,CAAAO,QAAA,CAAAT,OAAA,SAAA6B,IAAA,EAAAC,IAAA,EAAAF,aAAA,EAAAD,cAAA;;YAEA;YACAP,aAAA,CAAAP,OAAA;YACArI,QAAA,CAAA8F,IAAA,CAAAsC,WAAA,CAAA9C,aAAA;UACA;QACA;;QAEA;QACA,MAAAiE,QAAA,iBAAAvF,IAAA,GAAAwF,WAAA,GAAAC,KAAA;QACA/B,GAAA,CAAAQ,IAAA,CAAAqB,QAAA;;QAEA;QACA,KAAAvN,YAAA;QACA,KAAA+C,QAAA,CAAAc,OAAA;MAEA,SAAAe,KAAA;QACAF,OAAA,CAAAE,KAAA,YAAAA,KAAA;QACA,KAAA5E,YAAA;QACA,KAAA+C,QAAA,CAAA6B,KAAA,cAAAA,KAAA,CAAAW,OAAA;MACA;IACA;IAEA;IACAmI,wBAAAnG,WAAA;MACA,MAAA/D,IAAA,GAAA+D,WAAA,CAAA/D,IAAA;MACA,IAAAtF,IAAA;MACA,IAAAyP,UAAA;;MAEA;MACA,IAAApG,WAAA,CAAArJ,IAAA,IAAAqJ,WAAA,CAAArJ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAqJ,WAAA,CAAArJ,IAAA,CAAAA,IAAA;QACAyP,UAAA,GAAAzP,IAAA,CAAAgF,GAAA,CAAA0K,IAAA,IAAAA,IAAA,CAAAzK,KAAA,IAAAyK,IAAA,CAAA5P,IAAA;MACA;;MAEA;MACA,MAAA0M,OAAA;QACAvC,KAAA;UACA0F,IAAA,EAAAtG,WAAA,CAAAY,KAAA;QACA;QACA2F,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAxK,IAAA;UACAtF,IAAA,EAAAyP;QACA;QACAM,KAAA;UACAzK,IAAA;QACA;QACA0K,MAAA;MACA;;MAEA;MACA,IAAAhQ,IAAA,CAAAgD,MAAA;QACA,IAAAhD,IAAA,IAAAgQ,MAAA;UACA;UACA,MAAAC,SAAA;;UAEA;UACAjQ,IAAA,CAAAkQ,OAAA,CAAAR,IAAA;YACA,IAAAA,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACA,KAAAF,SAAA,CAAAE,CAAA,CAAApF,QAAA;kBACAkF,SAAA,CAAAE,CAAA,CAAApF,QAAA;oBACAjL,IAAA,EAAAqQ,CAAA,CAAApF,QAAA;oBACAzF,IAAA,EAAAA,IAAA;oBACAtF,IAAA,EAAAoQ,KAAA,CAAAX,UAAA,CAAAzM,MAAA,EAAAqN,IAAA;kBACA;gBACA;cACA;YACA;UACA;;UAEA;UACArQ,IAAA,CAAAkQ,OAAA,EAAAR,IAAA,EAAApB,KAAA;YACA,IAAAoB,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACAF,SAAA,CAAAE,CAAA,CAAApF,QAAA,EAAA/K,IAAA,CAAAsO,KAAA,IAAA6B,CAAA,CAAA1F,KAAA;cACA;YACA;UACA;;UAEA;UACA+B,OAAA,CAAAwD,MAAA,GAAAM,MAAA,CAAAC,MAAA,CAAAN,SAAA;QACA;UACA;UACAzD,OAAA,CAAAwD,MAAA,CAAA7M,IAAA;YACArD,IAAA;YACAwF,IAAA,EAAAA,IAAA;YACAtF,IAAA,EAAAA,IAAA,CAAAgF,GAAA,CAAA0K,IAAA,IAAAA,IAAA,CAAAjF,KAAA;UACA;QACA;MACA;MAEA,OAAA+B,OAAA;IACA;IAEA;IACA;IACAC,mBAAA+D,SAAA;MACAhK,OAAA,CAAAC,GAAA,gBAAA+J,SAAA;;MAEA;MACA,IAAAxQ,IAAA;MACA,IAAA0K,OAAA;;MAEA;MACA,IAAA8F,SAAA,CAAAxQ,IAAA,IAAAoQ,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAxQ,IAAA;QACAA,IAAA,GAAAwQ,SAAA,CAAAxQ,IAAA;QACA0K,OAAA,GAAA8F,SAAA,CAAA9F,OAAA;MACA;MACA;MAAA,KACA,IAAA8F,SAAA,CAAAxQ,IAAA,IAAAwQ,SAAA,CAAAxQ,IAAA,CAAAA,IAAA,IAAAoQ,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAxQ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAwQ,SAAA,CAAAxQ,IAAA,CAAAA,IAAA;QACA0K,OAAA,GAAA8F,SAAA,CAAAxQ,IAAA,CAAA0F,MAAA,GACA8K,SAAA,CAAAxQ,IAAA,CAAA0F,MAAA,CAAAjB,MAAA,CAAAiM,CAAA,IAAAA,CAAA,CAAArL,SAAA;MACA;MAEAmB,OAAA,CAAAC,GAAA,YAAAzG,IAAA;MACAwG,OAAA,CAAAC,GAAA,QAAAiE,OAAA;;MAEA;MACA,MAAAiG,SAAA,GAAA3Q,IAAA,CAAAgF,GAAA,CAAA0K,IAAA,IAAAA,IAAA,CAAAzK,KAAA,IAAAyK,IAAA,CAAA5P,IAAA;;MAEA;MACA,MAAAkQ,MAAA;MACA,IAAAhQ,IAAA,CAAAgD,MAAA,QAAAhD,IAAA,IAAAgQ,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACA7Q,IAAA,CAAAkQ,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAApF,QAAA;UACA;QACA;QAEA,MAAA0E,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAnF,QAAA;UACA,MAAAiG,UAAA,GAAAhR,IAAA,CAAAgF,GAAA,CAAA0K,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAApF,QAAA,KAAAA,QAAA;YACA,OAAAiF,MAAA,GAAAA,MAAA,CAAAvF,KAAA;UACA;UAEAuF,MAAA,CAAA7M,IAAA;YACArD,IAAA,EAAAiL,QAAA;YACAzF,IAAA;YACAtF,IAAA,EAAAgR;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAA7M,IAAA;UACArD,IAAA,EAAA4K,OAAA,KAAA5K,IAAA;UACAwF,IAAA;UACAtF,IAAA,EAAAA,IAAA,CAAAgF,GAAA,CAAA0K,IAAA,IAAAA,IAAA,CAAAjF,KAAA;QACA;MACA;MAEA;QACAR,KAAA;UACA0F,IAAA,EAAAa,SAAA,CAAAvG,KAAA;QACA;QACA2F,OAAA;UACAC,OAAA;UACAqB,WAAA;YACA5L,IAAA;UACA;QACA;QACA6L,MAAA;UACAnR,IAAA,EAAAgQ,MAAA,CAAAhL,GAAA,CAAAmL,CAAA,IAAAA,CAAA,CAAArQ,IAAA;QACA;QACAgQ,KAAA;UACAxK,IAAA;UACAtF,IAAA,EAAA2Q;QACA;QACAZ,KAAA;UACAzK,IAAA;QACA;QACA0K,MAAA,EAAAA;MACA;IACA;IAEA;IACAtD,oBAAA8D,SAAA;MACA,IAAAxQ,IAAA;MACA,IAAA0K,OAAA;;MAEA;MACA,IAAA8F,SAAA,CAAAxQ,IAAA,IAAAoQ,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAxQ,IAAA;QACAA,IAAA,GAAAwQ,SAAA,CAAAxQ,IAAA;QACA0K,OAAA,GAAA8F,SAAA,CAAA9F,OAAA;MACA;MACA;MAAA,KACA,IAAA8F,SAAA,CAAAxQ,IAAA,IAAAwQ,SAAA,CAAAxQ,IAAA,CAAAA,IAAA,IAAAoQ,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAxQ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAwQ,SAAA,CAAAxQ,IAAA,CAAAA,IAAA;QACA0K,OAAA,GAAA8F,SAAA,CAAAxQ,IAAA,CAAA0F,MAAA,GACA8K,SAAA,CAAAxQ,IAAA,CAAA0F,MAAA,CAAAjB,MAAA,CAAAiM,CAAA,IAAAA,CAAA,CAAArL,SAAA;MACA;;MAEA;MACA,MAAAsL,SAAA,GAAA3Q,IAAA,CAAAgF,GAAA,CAAA0K,IAAA,IAAAA,IAAA,CAAAzK,KAAA,IAAAyK,IAAA,CAAA5P,IAAA;;MAEA;MACA,MAAAkQ,MAAA;MACA,IAAAhQ,IAAA,CAAAgD,MAAA,QAAAhD,IAAA,IAAAgQ,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACA7Q,IAAA,CAAAkQ,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAApF,QAAA;UACA;QACA;QAEA,MAAA0E,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAnF,QAAA;UACA,MAAAiG,UAAA,GAAAhR,IAAA,CAAAgF,GAAA,CAAA0K,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAApF,QAAA,KAAAA,QAAA;YACA,OAAAiF,MAAA,GAAAA,MAAA,CAAAvF,KAAA;UACA;UAEAuF,MAAA,CAAA7M,IAAA;YACArD,IAAA,EAAAiL,QAAA;YACAzF,IAAA;YACAtF,IAAA,EAAAgR;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAA7M,IAAA;UACArD,IAAA,EAAA4K,OAAA,KAAA5K,IAAA;UACAwF,IAAA;UACAtF,IAAA,EAAAA,IAAA,CAAAgF,GAAA,CAAA0K,IAAA,IAAAA,IAAA,CAAAjF,KAAA;QACA;MACA;MAEA;QACAR,KAAA;UACA0F,IAAA,EAAAa,SAAA,CAAAvG,KAAA;QACA;QACA2F,OAAA;UACAC,OAAA;QACA;QACAsB,MAAA;UACAnR,IAAA,EAAAgQ,MAAA,CAAAhL,GAAA,CAAAmL,CAAA,IAAAA,CAAA,CAAArQ,IAAA;QACA;QACAgQ,KAAA;UACAxK,IAAA;UACAtF,IAAA,EAAA2Q;QACA;QACAZ,KAAA;UACAzK,IAAA;QACA;QACA0K,MAAA,EAAAA;MACA;IACA;IAEA;IACArD,mBAAA6D,SAAA;MACA,IAAAxQ,IAAA;;MAEA;MACA,IAAAwQ,SAAA,CAAAxQ,IAAA,IAAAoQ,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAxQ,IAAA;QACAA,IAAA,GAAAwQ,SAAA,CAAAxQ,IAAA;MACA;MACA;MAAA,KACA,IAAAwQ,SAAA,CAAAxQ,IAAA,IAAAwQ,SAAA,CAAAxQ,IAAA,CAAAA,IAAA,IAAAoQ,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAxQ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAwQ,SAAA,CAAAxQ,IAAA,CAAAA,IAAA;MACA;MAEA,MAAAgR,UAAA,GAAAhR,IAAA,CAAAgF,GAAA,CAAA0K,IAAA;QACA5P,IAAA,EAAA4P,IAAA,CAAAzK,KAAA,IAAAyK,IAAA,CAAA5P,IAAA;QACA2K,KAAA,EAAAiF,IAAA,CAAAjF;MACA;MAEA;QACAR,KAAA;UACA0F,IAAA,EAAAa,SAAA,CAAAvG,KAAA;QACA;QACA2F,OAAA;UACAC,OAAA;UACAuB,SAAA;QACA;QACAD,MAAA;UACAE,MAAA;UACAC,KAAA;UACAC,GAAA;UACAvR,IAAA,EAAAgR,UAAA,CAAAhM,GAAA,CAAA0K,IAAA,IAAAA,IAAA,CAAA5P,IAAA;QACA;QACAkQ,MAAA;UACAlQ,IAAA;UACAwF,IAAA;UACAkM,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACApG,QAAA;UACA;UACAqG,QAAA;YACAF,KAAA;cACAC,IAAA;cACAE,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAJ,IAAA;UACA;UACA3R,IAAA,EAAAgR;QACA;MACA;IACA;IAEA;IACApE,wBAAA4D,SAAA;MACA;MACA,IAAAxQ,IAAA;MACA,IAAA0K,OAAA;;MAEA;MACA,IAAA8F,SAAA,CAAAxQ,IAAA,IAAAoQ,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAxQ,IAAA;QACAA,IAAA,GAAAwQ,SAAA,CAAAxQ,IAAA;QACA0K,OAAA,GAAA8F,SAAA,CAAA9F,OAAA;MACA;MACA;MAAA,KACA,IAAA8F,SAAA,CAAAxQ,IAAA,IAAAwQ,SAAA,CAAAxQ,IAAA,CAAAA,IAAA,IAAAoQ,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAxQ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAwQ,SAAA,CAAAxQ,IAAA,CAAAA,IAAA;QACA0K,OAAA,GAAA8F,SAAA,CAAAxQ,IAAA,CAAA0F,MAAA,GACA8K,SAAA,CAAAxQ,IAAA,CAAA0F,MAAA,CAAAjB,MAAA,CAAAiM,CAAA,IAAAA,CAAA,CAAArL,SAAA;MACA;;MAEA;MACA,MAAA2M,SAAA,GAAAhS,IAAA,CAAAgF,GAAA,CAAA0K,IAAA,IAAAA,IAAA,CAAAzK,KAAA,IAAAyK,IAAA,CAAA5P,IAAA;;MAEA;MACA,MAAAkQ,MAAA;MACA,IAAAhQ,IAAA,CAAAgD,MAAA,QAAAhD,IAAA,IAAAgQ,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACA7Q,IAAA,CAAAkQ,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAApF,QAAA;UACA;QACA;QAEA,MAAA0E,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAnF,QAAA;UACA,MAAAiG,UAAA,GAAAhR,IAAA,CAAAgF,GAAA,CAAA0K,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAApF,QAAA,KAAAA,QAAA;YACA,OAAAiF,MAAA,GAAAA,MAAA,CAAAvF,KAAA;UACA;UAEAuF,MAAA,CAAA7M,IAAA;YACArD,IAAA,EAAAiL,QAAA;YACAzF,IAAA;YAAA;YACAtF,IAAA,EAAAgR;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAA7M,IAAA;UACArD,IAAA,EAAA4K,OAAA,KAAA5K,IAAA;UACAwF,IAAA;UAAA;UACAtF,IAAA,EAAAA,IAAA,CAAAgF,GAAA,CAAA0K,IAAA,IAAAA,IAAA,CAAAjF,KAAA;QACA;MACA;MAEA;QACAR,KAAA;UACA0F,IAAA,EAAAa,SAAA,CAAAvG,KAAA;QACA;QACA2F,OAAA;UACAC,OAAA;UACAqB,WAAA;YACA5L,IAAA;UACA;QACA;QACA6L,MAAA;UACAnR,IAAA,EAAAgQ,MAAA,CAAAhL,GAAA,CAAAmL,CAAA,IAAAA,CAAA,CAAArQ,IAAA;QACA;QACA;QACAgQ,KAAA;UACAxK,IAAA;QACA;QACAyK,KAAA;UACAzK,IAAA;UAAA;UACAtF,IAAA,EAAAgS;QACA;QACAhC,MAAA,EAAAA;MACA;IACA;IAEA;IACAnD,kBAAA2D,SAAA;MACA;QACAvG,KAAA;UACA0F,IAAA,EAAAa,SAAA,CAAAvG,KAAA;QACA;QACA2F,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAxK,IAAA;UACAtF,IAAA;QACA;QACA+P,KAAA;UACAzK,IAAA;QACA;QACA0K,MAAA;UACA1K,IAAA,EAAAkL,SAAA,CAAAlL,IAAA;UACAtF,IAAA;QACA;MACA;IACA;IAEA;IACAgN,gBAAAiF,SAAA,EAAA5I,WAAA;MACA;MACA,IAAArJ,IAAA;MACA,IAAA8G,OAAA;;MAEA;MACA,IAAAuC,WAAA,CAAArJ,IAAA,IAAAqJ,WAAA,CAAArJ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAqJ,WAAA,CAAArJ,IAAA,CAAAA,IAAA;;QAEA;QACA,IAAAA,IAAA,CAAAgD,MAAA;UACA,IAAAhD,IAAA,IAAAgQ,MAAA;YACA;YACAlJ,OAAA;YACA,MAAAoL,SAAA,GAAAlS,IAAA;YACA,IAAAkS,SAAA,CAAAlC,MAAA,IAAAI,KAAA,CAAAK,OAAA,CAAAyB,SAAA,CAAAlC,MAAA;cACAkC,SAAA,CAAAlC,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACArJ,OAAA,CAAA3D,IAAA,CAAAgN,CAAA,CAAApF,QAAA;cACA;YACA;UACA;YACA;YACAjE,OAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAqL,SAAA;;MAEA;MACAA,SAAA;MACArL,OAAA,CAAAoJ,OAAA,CAAAkC,MAAA;QACAD,SAAA,sGAAAC,MAAA;MACA;MACAD,SAAA;;MAEA;MACAA,SAAA;MACAnS,IAAA,CAAAkQ,OAAA,CAAAR,IAAA;QACAyC,SAAA;;QAEA;QACAA,SAAA,yDAAAzC,IAAA,CAAAzK,KAAA,IAAAyK,IAAA,CAAA5P,IAAA;;QAEA;QACA,IAAA4P,IAAA,CAAAM,MAAA;UACA;UACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;YACAgC,SAAA,yDAAAhC,CAAA,CAAA1F,KAAA,KAAA4H,SAAA,GAAAlC,CAAA,CAAA1F,KAAA;UACA;QACA;UACA;UACA0H,SAAA,yDAAAzC,IAAA,CAAAjF,KAAA,KAAA4H,SAAA,GAAA3C,IAAA,CAAAjF,KAAA;QACA;QAEA0H,SAAA;MACA;MACAA,SAAA;;MAEA;MACAF,SAAA,CAAA/F,SAAA,GAAAiG,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}