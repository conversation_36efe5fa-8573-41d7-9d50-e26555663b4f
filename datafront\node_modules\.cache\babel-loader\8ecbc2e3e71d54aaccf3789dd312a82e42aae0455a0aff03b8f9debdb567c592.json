{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport ElementUI from 'element-ui';\nimport 'element-ui/lib/theme-chalk/index.css';\nimport axios from 'axios';\nVue.use(ElementUI);\nVue.prototype.$http = axios;\naxios.defaults.baseURL = 'http://localhost:8080/api';\nVue.config.productionTip = false;\nnew Vue({\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "ElementUI", "axios", "use", "prototype", "$http", "defaults", "baseURL", "config", "productionTip", "render", "h", "$mount"], "sources": ["D:/FastBI/datafront/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport ElementUI from 'element-ui'\nimport 'element-ui/lib/theme-chalk/index.css'\nimport axios from 'axios'\n\nVue.use(ElementUI)\nVue.prototype.$http = axios\naxios.defaults.baseURL = 'http://localhost:8080/api'\n\nVue.config.productionTip = false\n\nnew Vue({\n  render: h => h(App),\n}).$mount('#app')"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,sCAAsC;AAC7C,OAAOC,KAAK,MAAM,OAAO;AAEzBH,GAAG,CAACI,GAAG,CAACF,SAAS,CAAC;AAClBF,GAAG,CAACK,SAAS,CAACC,KAAK,GAAGH,KAAK;AAC3BA,KAAK,CAACI,QAAQ,CAACC,OAAO,GAAG,2BAA2B;AAEpDR,GAAG,CAACS,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIV,GAAG,CAAC;EACNW,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACX,GAAG;AACpB,CAAC,CAAC,CAACY,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}