{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n};\nexports.default = function (Vue) {\n  /**\n   * template\n   *\n   * @param {String} string\n   * @param {Array} ...args\n   * @return {String}\n   */\n\n  function template(string) {\n    for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    if (args.length === 1 && _typeof(args[0]) === 'object') {\n      args = args[0];\n    }\n    if (!args || !args.hasOwnProperty) {\n      args = {};\n    }\n    return string.replace(RE_NARGS, function (match, prefix, i, index) {\n      var result = void 0;\n      if (string[index - 1] === '{' && string[index + match.length] === '}') {\n        return i;\n      } else {\n        result = (0, _util.hasOwn)(args, i) ? args[i] : null;\n        if (result === null || result === undefined) {\n          return '';\n        }\n        return result;\n      }\n    });\n  }\n  return template;\n};\nvar _util = require('element-ui/lib/utils/util');\nvar RE_NARGS = /(%|)\\{([0-9a-zA-Z_]+)\\}/g;\n/**\n *  String format template\n *  - Inspired:\n *    https://github.com/Matt-Esch/string-template/index.js\n */", "map": {"version": 3, "names": ["exports", "__esModule", "_typeof", "Symbol", "iterator", "obj", "constructor", "prototype", "default", "<PERSON><PERSON>", "template", "string", "_len", "arguments", "length", "args", "Array", "_key", "hasOwnProperty", "replace", "RE_NARGS", "match", "prefix", "i", "index", "result", "_util", "hasOwn", "undefined", "require"], "sources": ["D:/FastBI/datafront/node_modules/element-ui/lib/locale/format.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.default = function (Vue) {\n\n  /**\n   * template\n   *\n   * @param {String} string\n   * @param {Array} ...args\n   * @return {String}\n   */\n\n  function template(string) {\n    for (var _len = arguments.length, args = Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    if (args.length === 1 && _typeof(args[0]) === 'object') {\n      args = args[0];\n    }\n\n    if (!args || !args.hasOwnProperty) {\n      args = {};\n    }\n\n    return string.replace(RE_NARGS, function (match, prefix, i, index) {\n      var result = void 0;\n\n      if (string[index - 1] === '{' && string[index + match.length] === '}') {\n        return i;\n      } else {\n        result = (0, _util.hasOwn)(args, i) ? args[i] : null;\n        if (result === null || result === undefined) {\n          return '';\n        }\n\n        return result;\n      }\n    });\n  }\n\n  return template;\n};\n\nvar _util = require('element-ui/lib/utils/util');\n\nvar RE_NARGS = /(%|)\\{([0-9a-zA-Z_]+)\\}/g;\n/**\n *  String format template\n *  - Inspired:\n *    https://github.com/Matt-Esch/string-template/index.js\n */"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAUC,GAAG,EAAE;EAAE,OAAO,OAAOA,GAAG;AAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAI,OAAOF,MAAM,KAAK,UAAU,IAAIE,GAAG,CAACC,WAAW,KAAKH,MAAM,IAAIE,GAAG,KAAKF,MAAM,CAACI,SAAS,GAAG,QAAQ,GAAG,OAAOF,GAAG;AAAE,CAAC;AAE5QL,OAAO,CAACQ,OAAO,GAAG,UAAUC,GAAG,EAAE;EAE/B;AACF;AACA;AACA;AACA;AACA;AACA;;EAEE,SAASC,QAAQA,CAACC,MAAM,EAAE;IACxB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAGC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACtGF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAClC;IAEA,IAAIF,IAAI,CAACD,MAAM,KAAK,CAAC,IAAIZ,OAAO,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACtDA,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC;IAChB;IAEA,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACG,cAAc,EAAE;MACjCH,IAAI,GAAG,CAAC,CAAC;IACX;IAEA,OAAOJ,MAAM,CAACQ,OAAO,CAACC,QAAQ,EAAE,UAAUC,KAAK,EAAEC,MAAM,EAAEC,CAAC,EAAEC,KAAK,EAAE;MACjE,IAAIC,MAAM,GAAG,KAAK,CAAC;MAEnB,IAAId,MAAM,CAACa,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,IAAIb,MAAM,CAACa,KAAK,GAAGH,KAAK,CAACP,MAAM,CAAC,KAAK,GAAG,EAAE;QACrE,OAAOS,CAAC;MACV,CAAC,MAAM;QACLE,MAAM,GAAG,CAAC,CAAC,EAAEC,KAAK,CAACC,MAAM,EAAEZ,IAAI,EAAEQ,CAAC,CAAC,GAAGR,IAAI,CAACQ,CAAC,CAAC,GAAG,IAAI;QACpD,IAAIE,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKG,SAAS,EAAE;UAC3C,OAAO,EAAE;QACX;QAEA,OAAOH,MAAM;MACf;IACF,CAAC,CAAC;EACJ;EAEA,OAAOf,QAAQ;AACjB,CAAC;AAED,IAAIgB,KAAK,GAAGG,OAAO,CAAC,2BAA2B,CAAC;AAEhD,IAAIT,QAAQ,GAAG,0BAA0B;AACzC;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}