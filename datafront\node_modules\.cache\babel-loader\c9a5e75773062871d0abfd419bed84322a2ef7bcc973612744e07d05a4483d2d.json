{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport createRenderPlanner from '../helper/createRenderPlanner.js';\nimport { extend } from 'zrender/lib/core/util.js';\nvar positiveBorderColorQuery = ['itemStyle', 'borderColor'];\nvar negativeBorderColorQuery = ['itemStyle', 'borderColor0'];\nvar dojiBorderColorQuery = ['itemStyle', 'borderColorDoji'];\nvar positiveColorQuery = ['itemStyle', 'color'];\nvar negativeColorQuery = ['itemStyle', 'color0'];\nexport function getColor(sign, model) {\n  return model.get(sign > 0 ? positiveColorQuery : negativeColorQuery);\n}\nexport function getBorderColor(sign, model) {\n  return model.get(sign === 0 ? dojiBorderColorQuery : sign > 0 ? positiveBorderColorQuery : negativeBorderColorQuery);\n}\nvar candlestickVisual = {\n  seriesType: 'candlestick',\n  plan: createRenderPlanner(),\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var isLargeRender = seriesModel.pipelineContext.large;\n    return !isLargeRender && {\n      progress: function (params, data) {\n        var dataIndex;\n        while ((dataIndex = params.next()) != null) {\n          var itemModel = data.getItemModel(dataIndex);\n          var sign = data.getItemLayout(dataIndex).sign;\n          var style = itemModel.getItemStyle();\n          style.fill = getColor(sign, itemModel);\n          style.stroke = getBorderColor(sign, itemModel) || style.fill;\n          var existsStyle = data.ensureUniqueItemVisual(dataIndex, 'style');\n          extend(existsStyle, style);\n        }\n      }\n    };\n  }\n};\nexport default candlestickVisual;", "map": {"version": 3, "names": ["createRenderPlanner", "extend", "positiveBorderColorQuery", "negativeBorderColorQuery", "dojiBorderColorQuery", "positiveColor<PERSON>uery", "negativeColorQuery", "getColor", "sign", "model", "get", "getBorderColor", "candlestickVisual", "seriesType", "plan", "performRawSeries", "reset", "seriesModel", "ecModel", "isSeriesFiltered", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "pipelineContext", "large", "progress", "params", "data", "dataIndex", "next", "itemModel", "getItemModel", "getItemLayout", "style", "getItemStyle", "fill", "stroke", "existsStyle", "ensureUniqueItemVisual"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/candlestick/candlestickVisual.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport createRenderPlanner from '../helper/createRenderPlanner.js';\nimport { extend } from 'zrender/lib/core/util.js';\nvar positiveBorderColorQuery = ['itemStyle', 'borderColor'];\nvar negativeBorderColorQuery = ['itemStyle', 'borderColor0'];\nvar dojiBorderColorQuery = ['itemStyle', 'borderColorDoji'];\nvar positiveColorQuery = ['itemStyle', 'color'];\nvar negativeColorQuery = ['itemStyle', 'color0'];\nexport function getColor(sign, model) {\n  return model.get(sign > 0 ? positiveColorQuery : negativeColorQuery);\n}\nexport function getBorderColor(sign, model) {\n  return model.get(sign === 0 ? dojiBorderColorQuery : sign > 0 ? positiveBorderColorQuery : negativeBorderColorQuery);\n}\nvar candlestickVisual = {\n  seriesType: 'candlestick',\n  plan: createRenderPlanner(),\n  // For legend.\n  performRawSeries: true,\n  reset: function (seriesModel, ecModel) {\n    // Only visible series has each data be visual encoded\n    if (ecModel.isSeriesFiltered(seriesModel)) {\n      return;\n    }\n    var isLargeRender = seriesModel.pipelineContext.large;\n    return !isLargeRender && {\n      progress: function (params, data) {\n        var dataIndex;\n        while ((dataIndex = params.next()) != null) {\n          var itemModel = data.getItemModel(dataIndex);\n          var sign = data.getItemLayout(dataIndex).sign;\n          var style = itemModel.getItemStyle();\n          style.fill = getColor(sign, itemModel);\n          style.stroke = getBorderColor(sign, itemModel) || style.fill;\n          var existsStyle = data.ensureUniqueItemVisual(dataIndex, 'style');\n          extend(existsStyle, style);\n        }\n      }\n    };\n  }\n};\nexport default candlestickVisual;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,mBAAmB,MAAM,kCAAkC;AAClE,SAASC,MAAM,QAAQ,0BAA0B;AACjD,IAAIC,wBAAwB,GAAG,CAAC,WAAW,EAAE,aAAa,CAAC;AAC3D,IAAIC,wBAAwB,GAAG,CAAC,WAAW,EAAE,cAAc,CAAC;AAC5D,IAAIC,oBAAoB,GAAG,CAAC,WAAW,EAAE,iBAAiB,CAAC;AAC3D,IAAIC,kBAAkB,GAAG,CAAC,WAAW,EAAE,OAAO,CAAC;AAC/C,IAAIC,kBAAkB,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC;AAChD,OAAO,SAASC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACpC,OAAOA,KAAK,CAACC,GAAG,CAACF,IAAI,GAAG,CAAC,GAAGH,kBAAkB,GAAGC,kBAAkB,CAAC;AACtE;AACA,OAAO,SAASK,cAAcA,CAACH,IAAI,EAAEC,KAAK,EAAE;EAC1C,OAAOA,KAAK,CAACC,GAAG,CAACF,IAAI,KAAK,CAAC,GAAGJ,oBAAoB,GAAGI,IAAI,GAAG,CAAC,GAAGN,wBAAwB,GAAGC,wBAAwB,CAAC;AACtH;AACA,IAAIS,iBAAiB,GAAG;EACtBC,UAAU,EAAE,aAAa;EACzBC,IAAI,EAAEd,mBAAmB,CAAC,CAAC;EAC3B;EACAe,gBAAgB,EAAE,IAAI;EACtBC,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAEC,OAAO,EAAE;IACrC;IACA,IAAIA,OAAO,CAACC,gBAAgB,CAACF,WAAW,CAAC,EAAE;MACzC;IACF;IACA,IAAIG,aAAa,GAAGH,WAAW,CAACI,eAAe,CAACC,KAAK;IACrD,OAAO,CAACF,aAAa,IAAI;MACvBG,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAEC,IAAI,EAAE;QAChC,IAAIC,SAAS;QACb,OAAO,CAACA,SAAS,GAAGF,MAAM,CAACG,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE;UAC1C,IAAIC,SAAS,GAAGH,IAAI,CAACI,YAAY,CAACH,SAAS,CAAC;UAC5C,IAAIlB,IAAI,GAAGiB,IAAI,CAACK,aAAa,CAACJ,SAAS,CAAC,CAAClB,IAAI;UAC7C,IAAIuB,KAAK,GAAGH,SAAS,CAACI,YAAY,CAAC,CAAC;UACpCD,KAAK,CAACE,IAAI,GAAG1B,QAAQ,CAACC,IAAI,EAAEoB,SAAS,CAAC;UACtCG,KAAK,CAACG,MAAM,GAAGvB,cAAc,CAACH,IAAI,EAAEoB,SAAS,CAAC,IAAIG,KAAK,CAACE,IAAI;UAC5D,IAAIE,WAAW,GAAGV,IAAI,CAACW,sBAAsB,CAACV,SAAS,EAAE,OAAO,CAAC;UACjEzB,MAAM,CAACkC,WAAW,EAAEJ,KAAK,CAAC;QAC5B;MACF;IACF,CAAC;EACH;AACF,CAAC;AACD,eAAenB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}