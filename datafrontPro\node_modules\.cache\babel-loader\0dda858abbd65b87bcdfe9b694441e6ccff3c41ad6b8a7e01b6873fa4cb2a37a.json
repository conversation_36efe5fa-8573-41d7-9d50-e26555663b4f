{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { CHART_TYPES, CHART_TYPE_CONFIG } from '@/utils/chartCompatibility';\nexport default {\n  name: 'ChartTypeSelector',\n  props: {\n    // 当前选中的图表类型\n    currentType: {\n      type: String,\n      default: CHART_TYPES.BAR\n    },\n    // 各图表类型的兼容性信息\n    compatibility: {\n      type: Object,\n      default: () => ({})\n    },\n    // 各图表类型的加载状态\n    loadingStates: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  computed: {\n    chartTypes() {\n      return Object.keys(CHART_TYPES).map(key => {\n        const type = CHART_TYPES[key];\n        const config = CHART_TYPE_CONFIG[type];\n        const compatibility = this.compatibility[type] || {\n          compatible: true,\n          reason: ''\n        };\n        const loading = this.loadingStates[type] || false;\n        return {\n          type,\n          name: config.name,\n          icon: config.icon,\n          description: config.description,\n          compatible: compatibility.compatible,\n          reason: compatibility.reason,\n          loading\n        };\n      });\n    }\n  },\n  methods: {\n    handleTypeChange(type) {\n      if (type === this.currentType) return;\n      const compatibility = this.compatibility[type];\n      if (!compatibility || !compatibility.compatible) {\n        this.$message.warning(`无法切换到${CHART_TYPE_CONFIG[type].name}：${compatibility?.reason || '数据不兼容'}`);\n        return;\n      }\n      this.$emit('type-change', type);\n    }\n  }\n};", "map": {"version": 3, "names": ["CHART_TYPES", "CHART_TYPE_CONFIG", "name", "props", "currentType", "type", "String", "default", "BAR", "compatibility", "Object", "loadingStates", "computed", "chartTypes", "keys", "map", "key", "config", "compatible", "reason", "loading", "icon", "description", "methods", "handleTypeChange", "$message", "warning", "$emit"], "sources": ["src/components/ChartTypeSelector.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-type-selector\">\n    <div class=\"selector-header\">\n      <span class=\"selector-title\">图表类型</span>\n      <el-tooltip content=\"点击切换不同的图表类型\" placement=\"top\">\n        <i class=\"el-icon-question\"></i>\n      </el-tooltip>\n    </div>\n    <div class=\"chart-type-buttons\">\n      <el-button-group>\n        <el-button\n          v-for=\"chartType in chartTypes\"\n          :key=\"chartType.type\"\n          :type=\"currentType === chartType.type ? 'primary' : 'default'\"\n          :disabled=\"!chartType.compatible\"\n          :loading=\"chartType.loading\"\n          size=\"small\"\n          @click=\"handleTypeChange(chartType.type)\"\n          class=\"chart-type-btn\"\n        >\n          <i :class=\"chartType.icon\"></i>\n          <span class=\"btn-text\">{{ chartType.name }}</span>\n          <el-tooltip\n            v-if=\"!chartType.compatible\"\n            :content=\"chartType.reason\"\n            placement=\"top\"\n          >\n            <i class=\"el-icon-warning-outline disabled-icon\"></i>\n          </el-tooltip>\n        </el-button>\n      </el-button-group>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { CHART_TYPES, CHART_TYPE_CONFIG } from '@/utils/chartCompatibility'\n\nexport default {\n  name: 'ChartTypeSelector',\n  props: {\n    // 当前选中的图表类型\n    currentType: {\n      type: String,\n      default: CHART_TYPES.BAR\n    },\n    // 各图表类型的兼容性信息\n    compatibility: {\n      type: Object,\n      default: () => ({})\n    },\n    // 各图表类型的加载状态\n    loadingStates: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  computed: {\n    chartTypes() {\n      return Object.keys(CHART_TYPES).map(key => {\n        const type = CHART_TYPES[key];\n        const config = CHART_TYPE_CONFIG[type];\n        const compatibility = this.compatibility[type] || { compatible: true, reason: '' };\n        const loading = this.loadingStates[type] || false;\n        \n        return {\n          type,\n          name: config.name,\n          icon: config.icon,\n          description: config.description,\n          compatible: compatibility.compatible,\n          reason: compatibility.reason,\n          loading\n        };\n      });\n    }\n  },\n  methods: {\n    handleTypeChange(type) {\n      if (type === this.currentType) return;\n      \n      const compatibility = this.compatibility[type];\n      if (!compatibility || !compatibility.compatible) {\n        this.$message.warning(`无法切换到${CHART_TYPE_CONFIG[type].name}：${compatibility?.reason || '数据不兼容'}`);\n        return;\n      }\n      \n      this.$emit('type-change', type);\n    }\n  }\n}\n</script>\n\n<style scoped>\n.chart-type-selector {\n  margin-bottom: 16px;\n  padding: 12px;\n  background: #fafafa;\n  border-radius: 6px;\n  border: 1px solid #e8e8e8;\n}\n\n.selector-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.selector-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #333;\n  margin-right: 6px;\n}\n\n.selector-header i {\n  color: #999;\n  cursor: help;\n}\n\n.chart-type-buttons {\n  display: flex;\n  justify-content: center;\n}\n\n.chart-type-btn {\n  position: relative;\n  min-width: 80px;\n  height: 36px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 4px 8px;\n  margin: 0 2px;\n}\n\n.chart-type-btn i {\n  font-size: 16px;\n  margin-bottom: 2px;\n}\n\n.btn-text {\n  font-size: 12px;\n  line-height: 1;\n}\n\n.disabled-icon {\n  position: absolute;\n  top: 2px;\n  right: 2px;\n  font-size: 12px;\n  color: #f56c6c;\n}\n\n/* 禁用状态样式 */\n.chart-type-btn.is-disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.chart-type-btn.is-disabled .btn-text {\n  color: #c0c4cc;\n}\n\n/* 加载状态样式 */\n.chart-type-btn.is-loading {\n  pointer-events: none;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .chart-type-btn {\n    min-width: 60px;\n    height: 32px;\n    padding: 2px 4px;\n  }\n  \n  .chart-type-btn i {\n    font-size: 14px;\n  }\n  \n  .btn-text {\n    font-size: 11px;\n  }\n}\n\n/* 过渡动画 */\n.chart-type-btn {\n  transition: all 0.3s ease;\n}\n\n.chart-type-btn:hover:not(.is-disabled) {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n</style>\n"], "mappings": ";;AAoCA,SAAAA,WAAA,EAAAC,iBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,EAAAP,WAAA,CAAAQ;IACA;IACA;IACAC,aAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA,EAAAA,CAAA;IACA;IACA;IACAI,aAAA;MACAN,IAAA,EAAAK,MAAA;MACAH,OAAA,EAAAA,CAAA;IACA;EACA;EACAK,QAAA;IACAC,WAAA;MACA,OAAAH,MAAA,CAAAI,IAAA,CAAAd,WAAA,EAAAe,GAAA,CAAAC,GAAA;QACA,MAAAX,IAAA,GAAAL,WAAA,CAAAgB,GAAA;QACA,MAAAC,MAAA,GAAAhB,iBAAA,CAAAI,IAAA;QACA,MAAAI,aAAA,QAAAA,aAAA,CAAAJ,IAAA;UAAAa,UAAA;UAAAC,MAAA;QAAA;QACA,MAAAC,OAAA,QAAAT,aAAA,CAAAN,IAAA;QAEA;UACAA,IAAA;UACAH,IAAA,EAAAe,MAAA,CAAAf,IAAA;UACAmB,IAAA,EAAAJ,MAAA,CAAAI,IAAA;UACAC,WAAA,EAAAL,MAAA,CAAAK,WAAA;UACAJ,UAAA,EAAAT,aAAA,CAAAS,UAAA;UACAC,MAAA,EAAAV,aAAA,CAAAU,MAAA;UACAC;QACA;MACA;IACA;EACA;EACAG,OAAA;IACAC,iBAAAnB,IAAA;MACA,IAAAA,IAAA,UAAAD,WAAA;MAEA,MAAAK,aAAA,QAAAA,aAAA,CAAAJ,IAAA;MACA,KAAAI,aAAA,KAAAA,aAAA,CAAAS,UAAA;QACA,KAAAO,QAAA,CAAAC,OAAA,SAAAzB,iBAAA,CAAAI,IAAA,EAAAH,IAAA,IAAAO,aAAA,EAAAU,MAAA;QACA;MACA;MAEA,KAAAQ,KAAA,gBAAAtB,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}