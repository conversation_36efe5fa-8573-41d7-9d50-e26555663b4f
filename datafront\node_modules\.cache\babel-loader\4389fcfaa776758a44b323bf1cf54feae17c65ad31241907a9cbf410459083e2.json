{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      localChartConfig: null,\n      // 本地图表配置副本\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'el-icon-s-data',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'el-icon-connection',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'el-icon-pie-chart',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'el-icon-s-operation',\n          compatible: true\n        }\n      }\n    };\n  },\n  computed: {\n    // 是否显示图表切换器\n    showChartSwitcher() {\n      return this.availableChartTypes.length > 1 && !this.loading && !this.error;\n    },\n    // 可用的图表类型列表\n    availableChartTypes() {\n      const types = [];\n\n      // 添加当前图表类型\n      if (this.currentChartType && this.chartTypeConfig[this.currentChartType]) {\n        types.push({\n          type: this.currentChartType,\n          ...this.chartTypeConfig[this.currentChartType]\n        });\n      }\n\n      // 添加推荐的图表类型\n      this.recommendedChartTypes.forEach(type => {\n        if (type !== this.currentChartType && this.chartTypeConfig[type]) {\n          types.push({\n            type: type,\n            ...this.chartTypeConfig[type],\n            compatible: this.isChartTypeCompatible(type)\n          });\n        }\n      });\n      return types;\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler(newConfig) {\n        // 更新当前图表类型\n        if (newConfig && newConfig.type) {\n          this.currentChartType = newConfig.type;\n        }\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    // 初始化当前图表类型\n    if (this.chartConfig && this.chartConfig.type) {\n      this.currentChartType = this.chartConfig.type;\n    }\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    // 切换图表类型\n    async switchChartType(newType) {\n      if (newType === this.currentChartType || this.switchingChart) {\n        return;\n      }\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n      try {\n        this.switchingChart = true;\n\n        // 检查兼容性\n        if (!this.isChartTypeCompatible(newType)) {\n          this.$message.warning(`当前数据不适合 ${this.chartTypeConfig[newType]?.name} 图表`);\n          return;\n        }\n\n        // 更新图表类型\n        this.currentChartType = newType;\n\n        // 创建新的图表配置，复用相同数据\n        const newChartConfig = {\n          ...this.chartConfig,\n          type: newType\n        };\n\n        // 触发重新渲染\n        this.$emit('chart-type-changed', newChartConfig);\n\n        // 使用本地副本进行渲染，避免直接修改 prop\n        this.localChartConfig = {\n          ...newChartConfig\n        };\n        this.renderChartWithConfig(this.localChartConfig);\n        this.$message.success(`已切换到${this.chartTypeConfig[newType]?.name}`);\n      } catch (error) {\n        console.error('图表切换失败:', error);\n        this.$message.error('图表切换失败: ' + error.message);\n      } finally {\n        this.switchingChart = false;\n      }\n    },\n    // 检查图表类型兼容性\n    isChartTypeCompatible(chartType) {\n      // 检查是否有数据\n      if (!this.chartConfig || !this.chartConfig.data) {\n        return false;\n      }\n      let data = [];\n      if (this.chartConfig.data && Array.isArray(this.chartConfig.data)) {\n        data = this.chartConfig.data;\n      } else if (this.chartConfig.data && this.chartConfig.data.data && Array.isArray(this.chartConfig.data.data)) {\n        data = this.chartConfig.data.data;\n      }\n\n      // 基本检查：是否有数据\n      if (!data || data.length === 0) {\n        return false;\n      }\n\n      // 检查数据格式是否包含必要字段\n      const firstItem = data[0];\n      if (!firstItem || typeof firstItem.value === 'undefined' || !firstItem.field) {\n        return false;\n      }\n\n      // 所有当前支持的图表类型都兼容这种数据格式\n      const supportedTypes = ['bar', 'line', 'pie', 'bar-horizontal'];\n      return supportedTypes.includes(chartType);\n    },\n    renderChart() {\n      this.renderChartWithConfig(this.chartConfig);\n    },\n    renderChartWithConfig(config) {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!config || !config.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', config);\n        return;\n      }\n      console.log('开始渲染图表，配置:', config);\n\n      // 检查chartConfig是否已包含完整数据\n      if (config.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(config);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n\n      // 如果没有数据，才调用API获取\n      getData(config).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n\n      // 使用当前的图表类型（可能是切换后的类型）\n      const chartType = chartData.type || this.currentChartType || this.chartConfig.type;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch (chartType) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n\n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n\n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n\n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        console.log('提取的数据:', extractedData);\n\n        // 根据提取的数据类型调用不同的处理函数\n        switch (extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const {\n        data = [],\n        metrics = []\n      } = chartData;\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const {\n        data = []\n      } = chartData;\n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getData", "echarts", "name", "props", "chartConfig", "type", "Object", "required", "default", "recommendedChartTypes", "Array", "data", "loading", "error", "errorMsg", "chartInstance", "<PERSON><PERSON><PERSON>", "currentChartType", "localChartConfig", "chartTypeConfig", "icon", "compatible", "computed", "showChartSwitcher", "availableChartTypes", "length", "types", "push", "for<PERSON>ach", "isChartTypeCompatible", "watch", "deep", "handler", "newConfig", "<PERSON><PERSON><PERSON>", "mounted", "window", "addEventListener", "resizeChart", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "dispose", "methods", "switchChartType", "newType", "console", "log", "$message", "warning", "newChartConfig", "$emit", "renderChartWithConfig", "success", "message", "chartType", "isArray", "firstItem", "value", "field", "supportedTypes", "includes", "config", "options", "convertToChartOptions", "<PERSON><PERSON><PERSON>", "then", "response", "code", "msg", "chartData", "catch", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "extractedData", "metrics", "fields", "filter", "f", "groupType", "xAxisData", "map", "item", "series", "categoriesSet", "Set", "s", "add", "category", "categories", "from", "seriesData", "find", "title", "text", "tooltip", "trigger", "axisPointer", "legend", "xAxis", "yAxis", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "position", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "$refs", "chartRef", "init", "setOption", "resize"], "sources": ["src/components/ChartDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <!-- 图表切换工具栏 -->\n    <div v-if=\"showChartSwitcher\" class=\"chart-switcher\">\n      <div class=\"chart-switcher-title\">图表切换</div>\n      <div class=\"chart-type-buttons\">\n        <button\n          v-for=\"chartType in availableChartTypes\"\n          :key=\"chartType.type\"\n          :class=\"['chart-type-btn', {\n            'active': currentChartType === chartType.type,\n            'disabled': !chartType.compatible\n          }]\"\n          :disabled=\"!chartType.compatible || switchingChart\"\n          @click=\"switchChartType(chartType.type)\"\n          :title=\"chartType.name\"\n        >\n          <i :class=\"chartType.icon\"></i>\n          <span>{{ chartType.name }}</span>\n          <div v-if=\"switchingChart && currentChartType === chartType.type\" class=\"switching-loader\">\n            <i class=\"el-icon-loading\"></i>\n          </div>\n        </button>\n      </div>\n    </div>\n\n    <!-- 图表显示区域 -->\n    <div v-if=\"loading\" class=\"chart-loading\">\n      <i class=\"el-icon-loading\"></i>\n      <span>加载图表中...</span>\n    </div>\n    <div v-else-if=\"error\" class=\"chart-error\">\n      <i class=\"el-icon-warning\"></i>\n      <span>{{ errorMsg }}</span>\n    </div>\n    <div v-else ref=\"chartRef\" class=\"chart-canvas\"></div>\n  </div>\n</template>\n\n<script>\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\n\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      localChartConfig: null, // 本地图表配置副本\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'el-icon-s-data',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'el-icon-connection',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'el-icon-pie-chart',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'el-icon-s-operation',\n          compatible: true\n        }\n      }\n    }\n  },\n  computed: {\n    // 是否显示图表切换器\n    showChartSwitcher() {\n      return this.availableChartTypes.length > 1 && !this.loading && !this.error;\n    },\n\n    // 可用的图表类型列表\n    availableChartTypes() {\n      const types = [];\n\n      // 添加当前图表类型\n      if (this.currentChartType && this.chartTypeConfig[this.currentChartType]) {\n        types.push({\n          type: this.currentChartType,\n          ...this.chartTypeConfig[this.currentChartType]\n        });\n      }\n\n      // 添加推荐的图表类型\n      this.recommendedChartTypes.forEach(type => {\n        if (type !== this.currentChartType && this.chartTypeConfig[type]) {\n          types.push({\n            type: type,\n            ...this.chartTypeConfig[type],\n            compatible: this.isChartTypeCompatible(type)\n          });\n        }\n      });\n\n      return types;\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler(newConfig) {\n        // 更新当前图表类型\n        if (newConfig && newConfig.type) {\n          this.currentChartType = newConfig.type;\n        }\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    // 初始化当前图表类型\n    if (this.chartConfig && this.chartConfig.type) {\n      this.currentChartType = this.chartConfig.type;\n    }\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    // 切换图表类型\n    async switchChartType(newType) {\n      if (newType === this.currentChartType || this.switchingChart) {\n        return;\n      }\n\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n\n      try {\n        this.switchingChart = true;\n\n        // 检查兼容性\n        if (!this.isChartTypeCompatible(newType)) {\n          this.$message.warning(`当前数据不适合 ${this.chartTypeConfig[newType]?.name} 图表`);\n          return;\n        }\n\n        // 更新图表类型\n        this.currentChartType = newType;\n\n        // 创建新的图表配置，复用相同数据\n        const newChartConfig = {\n          ...this.chartConfig,\n          type: newType\n        };\n\n        // 触发重新渲染\n        this.$emit('chart-type-changed', newChartConfig);\n\n        // 使用本地副本进行渲染，避免直接修改 prop\n        this.localChartConfig = { ...newChartConfig };\n        this.renderChartWithConfig(this.localChartConfig);\n\n        this.$message.success(`已切换到${this.chartTypeConfig[newType]?.name}`);\n\n      } catch (error) {\n        console.error('图表切换失败:', error);\n        this.$message.error('图表切换失败: ' + error.message);\n      } finally {\n        this.switchingChart = false;\n      }\n    },\n\n    // 检查图表类型兼容性\n    isChartTypeCompatible(chartType) {\n      // 检查是否有数据\n      if (!this.chartConfig || !this.chartConfig.data) {\n        return false;\n      }\n\n      let data = [];\n      if (this.chartConfig.data && Array.isArray(this.chartConfig.data)) {\n        data = this.chartConfig.data;\n      } else if (this.chartConfig.data && this.chartConfig.data.data && Array.isArray(this.chartConfig.data.data)) {\n        data = this.chartConfig.data.data;\n      }\n\n      // 基本检查：是否有数据\n      if (!data || data.length === 0) {\n        return false;\n      }\n\n      // 检查数据格式是否包含必要字段\n      const firstItem = data[0];\n      if (!firstItem || typeof firstItem.value === 'undefined' || !firstItem.field) {\n        return false;\n      }\n\n      // 所有当前支持的图表类型都兼容这种数据格式\n      const supportedTypes = ['bar', 'line', 'pie', 'bar-horizontal'];\n      return supportedTypes.includes(chartType);\n    },\n\n    renderChart() {\n      this.renderChartWithConfig(this.chartConfig);\n    },\n\n    renderChartWithConfig(config) {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!config || !config.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', config);\n        return;\n      }\n\n      console.log('开始渲染图表，配置:', config);\n\n      // 检查chartConfig是否已包含完整数据\n      if (config.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(config);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n\n      // 如果没有数据，才调用API获取\n      getData(config).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        \n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        \n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        \n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        \n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n          \n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    \n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n\n      // 使用当前的图表类型（可能是切换后的类型）\n      const chartType = chartData.type || this.currentChartType || this.chartConfig.type;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch(chartType) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n        \n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n        \n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n        \n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        \n        console.log('提取的数据:', extractedData);\n        \n        // 根据提取的数据类型调用不同的处理函数\n        switch(extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    \n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const { data = [], metrics = [] } = chartData;\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const { data = [] } = chartData;\n      \n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n        \n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n        \n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    \n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.chart-container {\n  width: 100%;\n  position: relative;\n  max-width: 650px;\n  margin: 0 auto;\n  z-index: 1;\n  box-sizing: border-box;\n  isolation: isolate; /* 创建新的层叠上下文 */\n}\n\n/* 图表切换工具栏样式 */\n.chart-switcher {\n  background: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 8px 8px 0 0;\n  padding: 12px 16px;\n  margin-bottom: 0;\n}\n\n.chart-switcher-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #495057;\n  margin-bottom: 8px;\n}\n\n.chart-type-buttons {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.chart-type-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 12px;\n  border: 1px solid #dee2e6;\n  border-radius: 6px;\n  background: #fff;\n  color: #495057;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n  min-width: 70px;\n  justify-content: center;\n}\n\n.chart-type-btn:hover:not(.disabled) {\n  border-color: #409eff;\n  color: #409eff;\n  background: #ecf5ff;\n}\n\n.chart-type-btn.active {\n  border-color: #409eff;\n  background: #409eff;\n  color: #fff;\n}\n\n.chart-type-btn.disabled {\n  background: #f5f5f5;\n  color: #c0c4cc;\n  cursor: not-allowed;\n  border-color: #e4e7ed;\n}\n\n.chart-type-btn i {\n  font-size: 14px;\n}\n\n.switching-loader {\n  position: absolute;\n  top: 50%;\n  right: 4px;\n  transform: translateY(-50%);\n}\n\n.switching-loader i {\n  font-size: 12px;\n  animation: rotating 1s linear infinite;\n}\n\n@keyframes rotating {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n.chart-canvas {\n  width: 100%;\n  height: 350px;\n  position: relative;\n  z-index: 1;\n  border: 1px solid #e9ecef;\n  border-top: none;\n  border-radius: 0 0 8px 8px;\n  background: #fff;\n}\n\n.chart-loading, .chart-error {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  color: #909399;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 2;\n  background: rgba(255,255,255,0.9);\n}\n\n.chart-loading i, .chart-error i {\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.chart-error {\n  color: #F56C6C;\n}\n</style> "], "mappings": ";;;;;;;;;;;;;AAwCA,SAAAA,OAAA;AACA,YAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACA;IACAC,qBAAA;MACAJ,IAAA,EAAAK,KAAA;MACAF,OAAA,EAAAA,CAAA;IACA;EACA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;MACAC,aAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,gBAAA;MAAA;MACA;MACAC,eAAA;QACA;UACAjB,IAAA;UACAkB,IAAA;UACAC,UAAA;QACA;QACA;UACAnB,IAAA;UACAkB,IAAA;UACAC,UAAA;QACA;QACA;UACAnB,IAAA;UACAkB,IAAA;UACAC,UAAA;QACA;QACA;UACAnB,IAAA;UACAkB,IAAA;UACAC,UAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,kBAAA;MACA,YAAAC,mBAAA,CAAAC,MAAA,cAAAb,OAAA,UAAAC,KAAA;IACA;IAEA;IACAW,oBAAA;MACA,MAAAE,KAAA;;MAEA;MACA,SAAAT,gBAAA,SAAAE,eAAA,MAAAF,gBAAA;QACAS,KAAA,CAAAC,IAAA;UACAtB,IAAA,OAAAY,gBAAA;UACA,QAAAE,eAAA,MAAAF,gBAAA;QACA;MACA;;MAEA;MACA,KAAAR,qBAAA,CAAAmB,OAAA,CAAAvB,IAAA;QACA,IAAAA,IAAA,UAAAY,gBAAA,SAAAE,eAAA,CAAAd,IAAA;UACAqB,KAAA,CAAAC,IAAA;YACAtB,IAAA,EAAAA,IAAA;YACA,QAAAc,eAAA,CAAAd,IAAA;YACAgB,UAAA,OAAAQ,qBAAA,CAAAxB,IAAA;UACA;QACA;MACA;MAEA,OAAAqB,KAAA;IACA;EACA;EACAI,KAAA;IACA1B,WAAA;MACA2B,IAAA;MACAC,QAAAC,SAAA;QACA;QACA,IAAAA,SAAA,IAAAA,SAAA,CAAA5B,IAAA;UACA,KAAAY,gBAAA,GAAAgB,SAAA,CAAA5B,IAAA;QACA;QACA,KAAA6B,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACA,SAAA/B,WAAA,SAAAA,WAAA,CAAAC,IAAA;MACA,KAAAY,gBAAA,QAAAb,WAAA,CAAAC,IAAA;IACA;IACA,KAAA6B,WAAA;IACA;IACAE,MAAA,CAAAC,gBAAA,gBAAAC,WAAA;EACA;EACAC,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,WAAA;IACA;IACA,SAAAvB,aAAA;MACA,KAAAA,aAAA,CAAA0B,OAAA;IACA;EACA;EACAC,OAAA;IACA;IACA,MAAAC,gBAAAC,OAAA;MACA,IAAAA,OAAA,UAAA3B,gBAAA,SAAAD,cAAA;QACA;MACA;MAEA6B,OAAA,CAAAC,GAAA,iBAAA7B,gBAAA,QAAA2B,OAAA;MAEA;QACA,KAAA5B,cAAA;;QAEA;QACA,UAAAa,qBAAA,CAAAe,OAAA;UACA,KAAAG,QAAA,CAAAC,OAAA,iBAAA7B,eAAA,CAAAyB,OAAA,GAAA1C,IAAA;UACA;QACA;;QAEA;QACA,KAAAe,gBAAA,GAAA2B,OAAA;;QAEA;QACA,MAAAK,cAAA;UACA,QAAA7C,WAAA;UACAC,IAAA,EAAAuC;QACA;;QAEA;QACA,KAAAM,KAAA,uBAAAD,cAAA;;QAEA;QACA,KAAA/B,gBAAA;UAAA,GAAA+B;QAAA;QACA,KAAAE,qBAAA,MAAAjC,gBAAA;QAEA,KAAA6B,QAAA,CAAAK,OAAA,aAAAjC,eAAA,CAAAyB,OAAA,GAAA1C,IAAA;MAEA,SAAAW,KAAA;QACAgC,OAAA,CAAAhC,KAAA,YAAAA,KAAA;QACA,KAAAkC,QAAA,CAAAlC,KAAA,cAAAA,KAAA,CAAAwC,OAAA;MACA;QACA,KAAArC,cAAA;MACA;IACA;IAEA;IACAa,sBAAAyB,SAAA;MACA;MACA,UAAAlD,WAAA,UAAAA,WAAA,CAAAO,IAAA;QACA;MACA;MAEA,IAAAA,IAAA;MACA,SAAAP,WAAA,CAAAO,IAAA,IAAAD,KAAA,CAAA6C,OAAA,MAAAnD,WAAA,CAAAO,IAAA;QACAA,IAAA,QAAAP,WAAA,CAAAO,IAAA;MACA,gBAAAP,WAAA,CAAAO,IAAA,SAAAP,WAAA,CAAAO,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAA6C,OAAA,MAAAnD,WAAA,CAAAO,IAAA,CAAAA,IAAA;QACAA,IAAA,QAAAP,WAAA,CAAAO,IAAA,CAAAA,IAAA;MACA;;MAEA;MACA,KAAAA,IAAA,IAAAA,IAAA,CAAAc,MAAA;QACA;MACA;;MAEA;MACA,MAAA+B,SAAA,GAAA7C,IAAA;MACA,KAAA6C,SAAA,WAAAA,SAAA,CAAAC,KAAA,qBAAAD,SAAA,CAAAE,KAAA;QACA;MACA;;MAEA;MACA,MAAAC,cAAA;MACA,OAAAA,cAAA,CAAAC,QAAA,CAAAN,SAAA;IACA;IAEApB,YAAA;MACA,KAAAiB,qBAAA,MAAA/C,WAAA;IACA;IAEA+C,sBAAAU,MAAA;MACA,KAAAjD,OAAA;MACA,KAAAC,KAAA;;MAEA;MACA,KAAAgD,MAAA,KAAAA,MAAA,CAAAxD,IAAA;QACA,KAAAO,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA;QACA+B,OAAA,CAAAhC,KAAA,YAAAgD,MAAA;QACA;MACA;MAEAhB,OAAA,CAAAC,GAAA,eAAAe,MAAA;;MAEA;MACA,IAAAA,MAAA,CAAAlD,IAAA;QACAkC,OAAA,CAAAC,GAAA;QACA,KAAAlC,OAAA;QAEA;UACA;UACA,MAAAkD,OAAA,QAAAC,qBAAA,CAAAF,MAAA;UACAhB,OAAA,CAAAC,GAAA,kBAAAgB,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAjD,KAAA;UACAgC,OAAA,CAAAhC,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAwC,OAAA;QACA;QACA;MACA;;MAEA;MACArD,OAAA,CAAA6D,MAAA,EAAAI,IAAA,CAAAC,QAAA;QACArB,OAAA,CAAAC,GAAA,eAAAoB,QAAA;QACA,KAAAtD,OAAA;QAEA,KAAAsD,QAAA;UACA,KAAArD,KAAA;UACA,KAAAC,QAAA;UACA+B,OAAA,CAAAhC,KAAA;UACA;QACA;QAEA,IAAAqD,QAAA,CAAAC,IAAA;UACA,KAAAtD,KAAA;UACA,KAAAC,QAAA,GAAAoD,QAAA,CAAAE,GAAA,iBAAAF,QAAA,CAAAC,IAAA;UACAtB,OAAA,CAAAhC,KAAA,cAAAqD,QAAA;UACA;QACA;QAEA,MAAAG,SAAA,GAAAH,QAAA,CAAAvD,IAAA;QACAkC,OAAA,CAAAC,GAAA,cAAAuB,SAAA;QAEA;UACA;UACA,MAAAP,OAAA,QAAAC,qBAAA,CAAAM,SAAA;UACAxB,OAAA,CAAAC,GAAA,kBAAAgB,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAjD,KAAA;UACAgC,OAAA,CAAAhC,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAwC,OAAA;QACA;MACA,GAAAiB,KAAA,CAAAzD,KAAA;QACAgC,OAAA,CAAAhC,KAAA,cAAAA,KAAA;QACA,KAAAD,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAwC,OAAA;MACA;IACA;IAEA;IACAU,sBAAAM,SAAA;MACAxB,OAAA,CAAAC,GAAA,yBAAAuB,SAAA;;MAEA;MACA,MAAAf,SAAA,GAAAe,SAAA,CAAAhE,IAAA,SAAAY,gBAAA,SAAAb,WAAA,CAAAC,IAAA;;MAEA;MACA,IAAAgE,SAAA,CAAA1D,IAAA,IAAAD,KAAA,CAAA6C,OAAA,CAAAc,SAAA,CAAA1D,IAAA;QACAkC,OAAA,CAAAC,GAAA;QACA;QACA,QAAAQ,SAAA;UACA;YACA,YAAAiB,kBAAA,CAAAF,SAAA;UACA;YACA,YAAAG,mBAAA,CAAAH,SAAA;UACA;YACA,YAAAI,kBAAA,CAAAJ,SAAA;UACA;YACA,YAAAK,uBAAA,CAAAL,SAAA;UACA;YACA,YAAAM,iBAAA;QACA;MACA;QACA9B,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAA8B,aAAA;;QAEA;QACA,IAAAP,SAAA,CAAA1D,IAAA,IAAA0D,SAAA,CAAA1D,IAAA,CAAAA,IAAA;UACAkC,OAAA,CAAAC,GAAA;UACA8B,aAAA;YACAvE,IAAA,EAAAgE,SAAA,CAAA1D,IAAA,CAAAN,IAAA,SAAAD,WAAA,CAAAC,IAAA;YACAM,IAAA,EAAA0D,SAAA,CAAA1D,IAAA,CAAAA,IAAA;YACAkE,OAAA,EAAAR,SAAA,CAAA1D,IAAA,CAAAmE,MAAA,GAAAT,SAAA,CAAA1D,IAAA,CAAAmE,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;UACA;QACA;;QAEA;QACA,KAAAL,aAAA;UACA/B,OAAA,CAAAC,GAAA;UACA,YAAA6B,iBAAA;QACA;QAEA9B,OAAA,CAAAC,GAAA,WAAA8B,aAAA;;QAEA;QACA,QAAAA,aAAA,CAAAvE,IAAA;UACA;YACA,YAAAkE,kBAAA,CAAAK,aAAA;UACA;YACA,YAAAJ,mBAAA,CAAAI,aAAA;UACA;YACA,YAAAH,kBAAA,CAAAG,aAAA;UACA;YACA,YAAAF,uBAAA,CAAAE,aAAA;UACA;YACA,YAAAD,iBAAA;QACA;MACA;IACA;IAEA;IACAJ,mBAAAF,SAAA;MACAxB,OAAA,CAAAC,GAAA,gBAAAuB,SAAA;;MAEA;MACA,IAAA1D,IAAA;MACA,IAAAkE,OAAA;;MAEA;MACA,IAAAR,SAAA,CAAA1D,IAAA,IAAAD,KAAA,CAAA6C,OAAA,CAAAc,SAAA,CAAA1D,IAAA;QACAA,IAAA,GAAA0D,SAAA,CAAA1D,IAAA;QACAkE,OAAA,GAAAR,SAAA,CAAAQ,OAAA;MACA;MACA;MAAA,KACA,IAAAR,SAAA,CAAA1D,IAAA,IAAA0D,SAAA,CAAA1D,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAA6C,OAAA,CAAAc,SAAA,CAAA1D,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA0D,SAAA,CAAA1D,IAAA,CAAAA,IAAA;QACAkE,OAAA,GAAAR,SAAA,CAAA1D,IAAA,CAAAmE,MAAA,GACAT,SAAA,CAAA1D,IAAA,CAAAmE,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEApC,OAAA,CAAAC,GAAA,YAAAnC,IAAA;MACAkC,OAAA,CAAAC,GAAA,QAAA+B,OAAA;;MAEA;MACA,MAAAK,SAAA,GAAAvE,IAAA,CAAAwE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA1B,KAAA,IAAA0B,IAAA,CAAAlF,IAAA;;MAEA;MACA,MAAAmF,MAAA;MACA,IAAA1E,IAAA,CAAAc,MAAA,QAAAd,IAAA,IAAA0E,MAAA;QACA;QACA,MAAAC,aAAA,OAAAC,GAAA;QACA5E,IAAA,CAAAiB,OAAA,CAAAwD,IAAA;UACA,IAAAA,IAAA,CAAAC,MAAA;YACAD,IAAA,CAAAC,MAAA,CAAAzD,OAAA,CAAA4D,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAjF,KAAA,CAAAkF,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAA/D,OAAA,CAAA8D,QAAA;UACA,MAAAG,UAAA,GAAAlF,IAAA,CAAAwE,GAAA,CAAAC,IAAA;YACA,MAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA,EAAAS,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAL,MAAA,GAAAA,MAAA,CAAA5B,KAAA;UACA;UAEA4B,MAAA,CAAA1D,IAAA;YACAzB,IAAA,EAAAwF,QAAA;YACArF,IAAA;YACAM,IAAA,EAAAkF;UACA;QACA;MACA;QACA;QACAR,MAAA,CAAA1D,IAAA;UACAzB,IAAA,EAAA2E,OAAA,KAAA3E,IAAA;UACAG,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAAwE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA3B,KAAA;QACA;MACA;MAEA;QACAsC,KAAA;UACAC,IAAA,OAAA5F,WAAA,CAAA2F,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA9F,IAAA;UACA;QACA;QACA+F,MAAA;UACAzF,IAAA,EAAA0E,MAAA,CAAAF,GAAA,CAAAK,CAAA,IAAAA,CAAA,CAAAtF,IAAA;QACA;QACAmG,KAAA;UACAhG,IAAA;UACAM,IAAA,EAAAuE;QACA;QACAoB,KAAA;UACAjG,IAAA;QACA;QACAgF,MAAA,EAAAA;MACA;IACA;IAEA;IACAb,oBAAAH,SAAA;MACA;QAAA1D,IAAA;QAAAkE,OAAA;MAAA,IAAAR,SAAA;;MAEA;MACA,MAAAa,SAAA,GAAAvE,IAAA,CAAAwE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA1B,KAAA;;MAEA;MACA,MAAA2B,MAAA;MACA,IAAA1E,IAAA,CAAAc,MAAA,QAAAd,IAAA,IAAA0E,MAAA;QACA;QACA,MAAAC,aAAA,OAAAC,GAAA;QACA5E,IAAA,CAAAiB,OAAA,CAAAwD,IAAA;UACA,IAAAA,IAAA,CAAAC,MAAA;YACAD,IAAA,CAAAC,MAAA,CAAAzD,OAAA,CAAA4D,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAjF,KAAA,CAAAkF,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAA/D,OAAA,CAAA8D,QAAA;UACA,MAAAG,UAAA,GAAAlF,IAAA,CAAAwE,GAAA,CAAAC,IAAA;YACA,MAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA,EAAAS,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAL,MAAA,GAAAA,MAAA,CAAA5B,KAAA;UACA;UAEA4B,MAAA,CAAA1D,IAAA;YACAzB,IAAA,EAAAwF,QAAA;YACArF,IAAA;YACAM,IAAA,EAAAkF;UACA;QACA;MACA;QACA;QACAR,MAAA,CAAA1D,IAAA;UACAzB,IAAA,EAAA2E,OAAA,KAAA3E,IAAA;UACAG,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAAwE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA3B,KAAA;QACA;MACA;MAEA;QACAsC,KAAA;UACAC,IAAA,OAAA5F,WAAA,CAAA2F,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAE,MAAA;UACAzF,IAAA,EAAA0E,MAAA,CAAAF,GAAA,CAAAK,CAAA,IAAAA,CAAA,CAAAtF,IAAA;QACA;QACAmG,KAAA;UACAhG,IAAA;UACAM,IAAA,EAAAuE;QACA;QACAoB,KAAA;UACAjG,IAAA;QACA;QACAgF,MAAA,EAAAA;MACA;IACA;IAEA;IACAZ,mBAAAJ,SAAA;MACA;QAAA1D,IAAA;MAAA,IAAA0D,SAAA;MAEA,MAAAwB,UAAA,GAAAlF,IAAA,CAAAwE,GAAA,CAAAC,IAAA;QACAlF,IAAA,EAAAkF,IAAA,CAAA1B,KAAA;QACAD,KAAA,EAAA2B,IAAA,CAAA3B;MACA;MAEA;QACAsC,KAAA;UACAC,IAAA,OAAA5F,WAAA,CAAA2F,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAK,SAAA;QACA;QACAH,MAAA;UACAI,MAAA;UACAC,KAAA;UACAC,GAAA;UACA/F,IAAA,EAAAkF,UAAA,CAAAV,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAlF,IAAA;QACA;QACAmF,MAAA;UACAnF,IAAA;UACAG,IAAA;UACAsG,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACAH,KAAA;cACAC,IAAA;cACAG,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAL,IAAA;UACA;UACAnG,IAAA,EAAAkF;QACA;MACA;IACA;IAEA;IACAnB,wBAAAL,SAAA;MACAxB,OAAA,CAAAC,GAAA,gBAAAuB,SAAA;;MAEA;MACA,IAAA1D,IAAA;MACA,IAAAkE,OAAA;;MAEA;MACA,IAAAR,SAAA,CAAA1D,IAAA,IAAAD,KAAA,CAAA6C,OAAA,CAAAc,SAAA,CAAA1D,IAAA;QACAA,IAAA,GAAA0D,SAAA,CAAA1D,IAAA;QACAkE,OAAA,GAAAR,SAAA,CAAAQ,OAAA;MACA;MACA;MAAA,KACA,IAAAR,SAAA,CAAA1D,IAAA,IAAA0D,SAAA,CAAA1D,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAA6C,OAAA,CAAAc,SAAA,CAAA1D,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA0D,SAAA,CAAA1D,IAAA,CAAAA,IAAA;QACAkE,OAAA,GAAAR,SAAA,CAAA1D,IAAA,CAAAmE,MAAA,GACAT,SAAA,CAAA1D,IAAA,CAAAmE,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEApC,OAAA,CAAAC,GAAA,YAAAnC,IAAA;MACAkC,OAAA,CAAAC,GAAA,QAAA+B,OAAA;;MAEA;MACA,MAAAuC,SAAA,GAAAzG,IAAA,CAAAwE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA1B,KAAA,IAAA0B,IAAA,CAAAlF,IAAA;;MAEA;MACA,MAAAmF,MAAA;MACA,IAAA1E,IAAA,CAAAc,MAAA,QAAAd,IAAA,IAAA0E,MAAA;QACA;QACA,MAAAC,aAAA,OAAAC,GAAA;QACA5E,IAAA,CAAAiB,OAAA,CAAAwD,IAAA;UACA,IAAAA,IAAA,CAAAC,MAAA;YACAD,IAAA,CAAAC,MAAA,CAAAzD,OAAA,CAAA4D,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAjF,KAAA,CAAAkF,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAA/D,OAAA,CAAA8D,QAAA;UACA,MAAAG,UAAA,GAAAlF,IAAA,CAAAwE,GAAA,CAAAC,IAAA;YACA,MAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA,EAAAS,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAL,MAAA,GAAAA,MAAA,CAAA5B,KAAA;UACA;UAEA4B,MAAA,CAAA1D,IAAA;YACAzB,IAAA,EAAAwF,QAAA;YACArF,IAAA;YAAA;YACAM,IAAA,EAAAkF;UACA;QACA;MACA;QACA;QACAR,MAAA,CAAA1D,IAAA;UACAzB,IAAA,EAAA2E,OAAA,KAAA3E,IAAA;UACAG,IAAA;UAAA;UACAM,IAAA,EAAAA,IAAA,CAAAwE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA3B,KAAA;QACA;MACA;MAEA;QACAsC,KAAA;UACAC,IAAA,OAAA5F,WAAA,CAAA2F,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA9F,IAAA;UACA;QACA;QACA+F,MAAA;UACAzF,IAAA,EAAA0E,MAAA,CAAAF,GAAA,CAAAK,CAAA,IAAAA,CAAA,CAAAtF,IAAA;QACA;QACA;QACAmG,KAAA;UACAhG,IAAA;QACA;QACAiG,KAAA;UACAjG,IAAA;UAAA;UACAM,IAAA,EAAAyG;QACA;QACA/B,MAAA,EAAAA;MACA;IACA;IAEA;IACAV,kBAAA;MACA9B,OAAA,CAAAC,GAAA;MACA;QACAiD,KAAA;UACAC,IAAA,OAAA5F,WAAA,CAAA2F,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAG,KAAA;UACAhG,IAAA;UACAM,IAAA;QACA;QACA2F,KAAA;UACAjG,IAAA;QACA;QACAgF,MAAA;UACAhF,IAAA,OAAAD,WAAA,CAAAC,IAAA;UACAM,IAAA;QACA;MACA;IACA;IAEA;IACAqD,UAAAF,OAAA;MACA;QACAjB,OAAA,CAAAC,GAAA;QACA,UAAAuE,KAAA,CAAAC,QAAA;UACAzE,OAAA,CAAAhC,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA;UACA;QACA;;QAEA;QACA,SAAAC,aAAA;UACA,KAAAA,aAAA,CAAA0B,OAAA;QACA;;QAEA;QACA,KAAA1B,aAAA,GAAAd,OAAA,CAAAsH,IAAA,MAAAF,KAAA,CAAAC,QAAA;QACA,KAAAvG,aAAA,CAAAyG,SAAA,CAAA1D,OAAA;QACAjB,OAAA,CAAAC,GAAA;MACA,SAAAjC,KAAA;QACAgC,OAAA,CAAAhC,KAAA,YAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,gBAAAD,KAAA,CAAAwC,OAAA;MACA;IACA;IAEA;IACAf,YAAA;MACA,SAAAvB,aAAA;QACA,KAAAA,aAAA,CAAA0G,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}