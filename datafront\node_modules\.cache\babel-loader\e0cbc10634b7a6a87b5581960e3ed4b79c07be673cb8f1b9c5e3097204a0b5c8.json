{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { error } from '../util/log.js';\n// Implementation of exported APIs. For example registerMap, getMap.\n// The implementations will be registered when installing the component.\n// Avoid these code being bundled to the core module.\nvar implsStore = {};\n// TODO Type\nexport function registerImpl(name, impl) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (implsStore[name]) {\n      error(\"Already has an implementation of \" + name + \".\");\n    }\n  }\n  implsStore[name] = impl;\n}\nexport function getImpl(name) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!implsStore[name]) {\n      error(\"Implementation of \" + name + \" doesn't exists.\");\n    }\n  }\n  return implsStore[name];\n}", "map": {"version": 3, "names": ["error", "implsStore", "registerImpl", "name", "impl", "process", "env", "NODE_ENV", "getImpl"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/core/impl.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { error } from '../util/log.js';\n// Implementation of exported APIs. For example registerMap, getMap.\n// The implementations will be registered when installing the component.\n// Avoid these code being bundled to the core module.\nvar implsStore = {};\n// TODO Type\nexport function registerImpl(name, impl) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (implsStore[name]) {\n      error(\"Already has an implementation of \" + name + \".\");\n    }\n  }\n  implsStore[name] = impl;\n}\nexport function getImpl(name) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!implsStore[name]) {\n      error(\"Implementation of \" + name + \" doesn't exists.\");\n    }\n  }\n  return implsStore[name];\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,KAAK,QAAQ,gBAAgB;AACtC;AACA;AACA;AACA,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB;AACA,OAAO,SAASC,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACvC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIN,UAAU,CAACE,IAAI,CAAC,EAAE;MACpBH,KAAK,CAAC,mCAAmC,GAAGG,IAAI,GAAG,GAAG,CAAC;IACzD;EACF;EACAF,UAAU,CAACE,IAAI,CAAC,GAAGC,IAAI;AACzB;AACA,OAAO,SAASI,OAAOA,CAACL,IAAI,EAAE;EAC5B,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACN,UAAU,CAACE,IAAI,CAAC,EAAE;MACrBH,KAAK,CAAC,oBAAoB,GAAGG,IAAI,GAAG,kBAAkB,CAAC;IACzD;EACF;EACA,OAAOF,UAAU,CAACE,IAAI,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}