{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Caution: If the mechanism should be changed some day, these cases\r\n * should be considered:\r\n *\r\n * (1) In `merge option` mode, if using the same option to call `setOption`\r\n * many times, the result should be the same (try our best to ensure that).\r\n * (2) In `merge option` mode, if a component has no id/name specified, it\r\n * will be merged by index, and the result sequence of the components is\r\n * consistent to the original sequence.\r\n * (3) In `replaceMerge` mode, keep the result sequence of the components is\r\n * consistent to the original sequence, even though there might result in \"hole\".\r\n * (4) `reset` feature (in toolbox). Find detailed info in comments about\r\n * `mergeOption` in module:echarts/model/OptionManager.\r\n */\nimport { each, filter, isArray, isObject, isString, createHashMap, assert, clone, merge, extend, mixin, isFunction } from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../util/model.js';\nimport Model from './Model.js';\nimport ComponentModel from './Component.js';\nimport globalDefault from './globalDefault.js';\nimport { resetSourceDefaulter } from '../data/helper/sourceHelper.js';\nimport { concatInternalOptions } from './internalComponentCreator.js';\nimport { PaletteMixin } from './mixin/palette.js';\nimport { error, warn } from '../util/log.js';\n// -----------------------\n// Internal method names:\n// -----------------------\nvar reCreateSeriesIndices;\nvar assertSeriesInitialized;\nvar initBase;\nvar OPTION_INNER_KEY = '\\0_ec_inner';\nvar OPTION_INNER_VALUE = 1;\nvar BUITIN_COMPONENTS_MAP = {\n  grid: 'GridComponent',\n  polar: 'PolarComponent',\n  geo: 'GeoComponent',\n  singleAxis: 'SingleAxisComponent',\n  parallel: 'ParallelComponent',\n  calendar: 'CalendarComponent',\n  graphic: 'GraphicComponent',\n  toolbox: 'ToolboxComponent',\n  tooltip: 'TooltipComponent',\n  axisPointer: 'AxisPointerComponent',\n  brush: 'BrushComponent',\n  title: 'TitleComponent',\n  timeline: 'TimelineComponent',\n  markPoint: 'MarkPointComponent',\n  markLine: 'MarkLineComponent',\n  markArea: 'MarkAreaComponent',\n  legend: 'LegendComponent',\n  dataZoom: 'DataZoomComponent',\n  visualMap: 'VisualMapComponent',\n  // aria: 'AriaComponent',\n  // dataset: 'DatasetComponent',\n  // Dependencies\n  xAxis: 'GridComponent',\n  yAxis: 'GridComponent',\n  angleAxis: 'PolarComponent',\n  radiusAxis: 'PolarComponent'\n};\nvar BUILTIN_CHARTS_MAP = {\n  line: 'LineChart',\n  bar: 'BarChart',\n  pie: 'PieChart',\n  scatter: 'ScatterChart',\n  radar: 'RadarChart',\n  map: 'MapChart',\n  tree: 'TreeChart',\n  treemap: 'TreemapChart',\n  graph: 'GraphChart',\n  gauge: 'GaugeChart',\n  funnel: 'FunnelChart',\n  parallel: 'ParallelChart',\n  sankey: 'SankeyChart',\n  boxplot: 'BoxplotChart',\n  candlestick: 'CandlestickChart',\n  effectScatter: 'EffectScatterChart',\n  lines: 'LinesChart',\n  heatmap: 'HeatmapChart',\n  pictorialBar: 'PictorialBarChart',\n  themeRiver: 'ThemeRiverChart',\n  sunburst: 'SunburstChart',\n  custom: 'CustomChart'\n};\nvar componetsMissingLogPrinted = {};\nfunction checkMissingComponents(option) {\n  each(option, function (componentOption, mainType) {\n    if (!ComponentModel.hasClass(mainType)) {\n      var componentImportName = BUITIN_COMPONENTS_MAP[mainType];\n      if (componentImportName && !componetsMissingLogPrinted[componentImportName]) {\n        error(\"Component \" + mainType + \" is used but not imported.\\nimport { \" + componentImportName + \" } from 'echarts/components';\\necharts.use([\" + componentImportName + \"]);\");\n        componetsMissingLogPrinted[componentImportName] = true;\n      }\n    }\n  });\n}\nvar GlobalModel = /** @class */function (_super) {\n  __extends(GlobalModel, _super);\n  function GlobalModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  GlobalModel.prototype.init = function (option, parentModel, ecModel, theme, locale, optionManager) {\n    theme = theme || {};\n    this.option = null; // Mark as not initialized.\n    this._theme = new Model(theme);\n    this._locale = new Model(locale);\n    this._optionManager = optionManager;\n  };\n  GlobalModel.prototype.setOption = function (option, opts, optionPreprocessorFuncs) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(option != null, 'option is null/undefined');\n      assert(option[OPTION_INNER_KEY] !== OPTION_INNER_VALUE, 'please use chart.getOption()');\n    }\n    var innerOpt = normalizeSetOptionInput(opts);\n    this._optionManager.setOption(option, optionPreprocessorFuncs, innerOpt);\n    this._resetOption(null, innerOpt);\n  };\n  /**\r\n   * @param type null/undefined: reset all.\r\n   *        'recreate': force recreate all.\r\n   *        'timeline': only reset timeline option\r\n   *        'media': only reset media query option\r\n   * @return Whether option changed.\r\n   */\n  GlobalModel.prototype.resetOption = function (type, opt) {\n    return this._resetOption(type, normalizeSetOptionInput(opt));\n  };\n  GlobalModel.prototype._resetOption = function (type, opt) {\n    var optionChanged = false;\n    var optionManager = this._optionManager;\n    if (!type || type === 'recreate') {\n      var baseOption = optionManager.mountOption(type === 'recreate');\n      if (process.env.NODE_ENV !== 'production') {\n        checkMissingComponents(baseOption);\n      }\n      if (!this.option || type === 'recreate') {\n        initBase(this, baseOption);\n      } else {\n        this.restoreData();\n        this._mergeOption(baseOption, opt);\n      }\n      optionChanged = true;\n    }\n    if (type === 'timeline' || type === 'media') {\n      this.restoreData();\n    }\n    // By design, if `setOption(option2)` at the second time, and `option2` is a `ECUnitOption`,\n    // it should better not have the same props with `MediaUnit['option']`.\n    // Because either `option2` or `MediaUnit['option']` will be always merged to \"current option\"\n    // rather than original \"baseOption\". If they both override a prop, the result might be\n    // unexpected when media state changed after `setOption` called.\n    // If we really need to modify a props in each `MediaUnit['option']`, use the full version\n    // (`{baseOption, media}`) in `setOption`.\n    // For `timeline`, the case is the same.\n    if (!type || type === 'recreate' || type === 'timeline') {\n      var timelineOption = optionManager.getTimelineOption(this);\n      if (timelineOption) {\n        optionChanged = true;\n        this._mergeOption(timelineOption, opt);\n      }\n    }\n    if (!type || type === 'recreate' || type === 'media') {\n      var mediaOptions = optionManager.getMediaOption(this);\n      if (mediaOptions.length) {\n        each(mediaOptions, function (mediaOption) {\n          optionChanged = true;\n          this._mergeOption(mediaOption, opt);\n        }, this);\n      }\n    }\n    return optionChanged;\n  };\n  GlobalModel.prototype.mergeOption = function (option) {\n    this._mergeOption(option, null);\n  };\n  GlobalModel.prototype._mergeOption = function (newOption, opt) {\n    var option = this.option;\n    var componentsMap = this._componentsMap;\n    var componentsCount = this._componentsCount;\n    var newCmptTypes = [];\n    var newCmptTypeMap = createHashMap();\n    var replaceMergeMainTypeMap = opt && opt.replaceMergeMainTypeMap;\n    resetSourceDefaulter(this);\n    // If no component class, merge directly.\n    // For example: color, animaiton options, etc.\n    each(newOption, function (componentOption, mainType) {\n      if (componentOption == null) {\n        return;\n      }\n      if (!ComponentModel.hasClass(mainType)) {\n        // globalSettingTask.dirty();\n        option[mainType] = option[mainType] == null ? clone(componentOption) : merge(option[mainType], componentOption, true);\n      } else if (mainType) {\n        newCmptTypes.push(mainType);\n        newCmptTypeMap.set(mainType, true);\n      }\n    });\n    if (replaceMergeMainTypeMap) {\n      // If there is a mainType `xxx` in `replaceMerge` but not declared in option,\n      // we trade it as it is declared in option as `{xxx: []}`. Because:\n      // (1) for normal merge, `{xxx: null/undefined}` are the same meaning as `{xxx: []}`.\n      // (2) some preprocessor may convert some of `{xxx: null/undefined}` to `{xxx: []}`.\n      replaceMergeMainTypeMap.each(function (val, mainTypeInReplaceMerge) {\n        if (ComponentModel.hasClass(mainTypeInReplaceMerge) && !newCmptTypeMap.get(mainTypeInReplaceMerge)) {\n          newCmptTypes.push(mainTypeInReplaceMerge);\n          newCmptTypeMap.set(mainTypeInReplaceMerge, true);\n        }\n      });\n    }\n    ComponentModel.topologicalTravel(newCmptTypes, ComponentModel.getAllClassMainTypes(), visitComponent, this);\n    function visitComponent(mainType) {\n      var newCmptOptionList = concatInternalOptions(this, mainType, modelUtil.normalizeToArray(newOption[mainType]));\n      var oldCmptList = componentsMap.get(mainType);\n      var mergeMode =\n      // `!oldCmptList` means init. See the comment in `mappingToExists`\n      !oldCmptList ? 'replaceAll' : replaceMergeMainTypeMap && replaceMergeMainTypeMap.get(mainType) ? 'replaceMerge' : 'normalMerge';\n      var mappingResult = modelUtil.mappingToExists(oldCmptList, newCmptOptionList, mergeMode);\n      // Set mainType and complete subType.\n      modelUtil.setComponentTypeToKeyInfo(mappingResult, mainType, ComponentModel);\n      // Empty it before the travel, in order to prevent `this._componentsMap`\n      // from being used in the `init`/`mergeOption`/`optionUpdated` of some\n      // components, which is probably incorrect logic.\n      option[mainType] = null;\n      componentsMap.set(mainType, null);\n      componentsCount.set(mainType, 0);\n      var optionsByMainType = [];\n      var cmptsByMainType = [];\n      var cmptsCountByMainType = 0;\n      var tooltipExists;\n      var tooltipWarningLogged;\n      each(mappingResult, function (resultItem, index) {\n        var componentModel = resultItem.existing;\n        var newCmptOption = resultItem.newOption;\n        if (!newCmptOption) {\n          if (componentModel) {\n            // Consider where is no new option and should be merged using {},\n            // see removeEdgeAndAdd in topologicalTravel and\n            // ComponentModel.getAllClassMainTypes.\n            componentModel.mergeOption({}, this);\n            componentModel.optionUpdated({}, false);\n          }\n          // If no both `resultItem.exist` and `resultItem.option`,\n          // either it is in `replaceMerge` and not matched by any id,\n          // or it has been removed in previous `replaceMerge` and left a \"hole\" in this component index.\n        } else {\n          var isSeriesType = mainType === 'series';\n          var ComponentModelClass = ComponentModel.getClass(mainType, resultItem.keyInfo.subType, !isSeriesType // Give a more detailed warn later if series don't exists\n          );\n          if (!ComponentModelClass) {\n            if (process.env.NODE_ENV !== 'production') {\n              var subType = resultItem.keyInfo.subType;\n              var seriesImportName = BUILTIN_CHARTS_MAP[subType];\n              if (!componetsMissingLogPrinted[subType]) {\n                componetsMissingLogPrinted[subType] = true;\n                if (seriesImportName) {\n                  error(\"Series \" + subType + \" is used but not imported.\\nimport { \" + seriesImportName + \" } from 'echarts/charts';\\necharts.use([\" + seriesImportName + \"]);\");\n                } else {\n                  error(\"Unknown series \" + subType);\n                }\n              }\n            }\n            return;\n          }\n          // TODO Before multiple tooltips get supported, we do this check to avoid unexpected exception.\n          if (mainType === 'tooltip') {\n            if (tooltipExists) {\n              if (process.env.NODE_ENV !== 'production') {\n                if (!tooltipWarningLogged) {\n                  warn('Currently only one tooltip component is allowed.');\n                  tooltipWarningLogged = true;\n                }\n              }\n              return;\n            }\n            tooltipExists = true;\n          }\n          if (componentModel && componentModel.constructor === ComponentModelClass) {\n            componentModel.name = resultItem.keyInfo.name;\n            // componentModel.settingTask && componentModel.settingTask.dirty();\n            componentModel.mergeOption(newCmptOption, this);\n            componentModel.optionUpdated(newCmptOption, false);\n          } else {\n            // PENDING Global as parent ?\n            var extraOpt = extend({\n              componentIndex: index\n            }, resultItem.keyInfo);\n            componentModel = new ComponentModelClass(newCmptOption, this, this, extraOpt);\n            // Assign `keyInfo`\n            extend(componentModel, extraOpt);\n            if (resultItem.brandNew) {\n              componentModel.__requireNewView = true;\n            }\n            componentModel.init(newCmptOption, this, this);\n            // Call optionUpdated after init.\n            // newCmptOption has been used as componentModel.option\n            // and may be merged with theme and default, so pass null\n            // to avoid confusion.\n            componentModel.optionUpdated(null, true);\n          }\n        }\n        if (componentModel) {\n          optionsByMainType.push(componentModel.option);\n          cmptsByMainType.push(componentModel);\n          cmptsCountByMainType++;\n        } else {\n          // Always do assign to avoid elided item in array.\n          optionsByMainType.push(void 0);\n          cmptsByMainType.push(void 0);\n        }\n      }, this);\n      option[mainType] = optionsByMainType;\n      componentsMap.set(mainType, cmptsByMainType);\n      componentsCount.set(mainType, cmptsCountByMainType);\n      // Backup series for filtering.\n      if (mainType === 'series') {\n        reCreateSeriesIndices(this);\n      }\n    }\n    // If no series declared, ensure `_seriesIndices` initialized.\n    if (!this._seriesIndices) {\n      reCreateSeriesIndices(this);\n    }\n  };\n  /**\r\n   * Get option for output (cloned option and inner info removed)\r\n   */\n  GlobalModel.prototype.getOption = function () {\n    var option = clone(this.option);\n    each(option, function (optInMainType, mainType) {\n      if (ComponentModel.hasClass(mainType)) {\n        var opts = modelUtil.normalizeToArray(optInMainType);\n        // Inner cmpts need to be removed.\n        // Inner cmpts might not be at last since ec5.0, but still\n        // compatible for users: if inner cmpt at last, splice the returned array.\n        var realLen = opts.length;\n        var metNonInner = false;\n        for (var i = realLen - 1; i >= 0; i--) {\n          // Remove options with inner id.\n          if (opts[i] && !modelUtil.isComponentIdInternal(opts[i])) {\n            metNonInner = true;\n          } else {\n            opts[i] = null;\n            !metNonInner && realLen--;\n          }\n        }\n        opts.length = realLen;\n        option[mainType] = opts;\n      }\n    });\n    delete option[OPTION_INNER_KEY];\n    return option;\n  };\n  GlobalModel.prototype.getTheme = function () {\n    return this._theme;\n  };\n  GlobalModel.prototype.getLocaleModel = function () {\n    return this._locale;\n  };\n  GlobalModel.prototype.setUpdatePayload = function (payload) {\n    this._payload = payload;\n  };\n  GlobalModel.prototype.getUpdatePayload = function () {\n    return this._payload;\n  };\n  /**\r\n   * @param idx If not specified, return the first one.\r\n   */\n  GlobalModel.prototype.getComponent = function (mainType, idx) {\n    var list = this._componentsMap.get(mainType);\n    if (list) {\n      var cmpt = list[idx || 0];\n      if (cmpt) {\n        return cmpt;\n      } else if (idx == null) {\n        for (var i = 0; i < list.length; i++) {\n          if (list[i]) {\n            return list[i];\n          }\n        }\n      }\n    }\n  };\n  /**\r\n   * @return Never be null/undefined.\r\n   */\n  GlobalModel.prototype.queryComponents = function (condition) {\n    var mainType = condition.mainType;\n    if (!mainType) {\n      return [];\n    }\n    var index = condition.index;\n    var id = condition.id;\n    var name = condition.name;\n    var cmpts = this._componentsMap.get(mainType);\n    if (!cmpts || !cmpts.length) {\n      return [];\n    }\n    var result;\n    if (index != null) {\n      result = [];\n      each(modelUtil.normalizeToArray(index), function (idx) {\n        cmpts[idx] && result.push(cmpts[idx]);\n      });\n    } else if (id != null) {\n      result = queryByIdOrName('id', id, cmpts);\n    } else if (name != null) {\n      result = queryByIdOrName('name', name, cmpts);\n    } else {\n      // Return all non-empty components in that mainType\n      result = filter(cmpts, function (cmpt) {\n        return !!cmpt;\n      });\n    }\n    return filterBySubType(result, condition);\n  };\n  /**\r\n   * The interface is different from queryComponents,\r\n   * which is convenient for inner usage.\r\n   *\r\n   * @usage\r\n   * let result = findComponents(\r\n   *     {mainType: 'dataZoom', query: {dataZoomId: 'abc'}}\r\n   * );\r\n   * let result = findComponents(\r\n   *     {mainType: 'series', subType: 'pie', query: {seriesName: 'uio'}}\r\n   * );\r\n   * let result = findComponents(\r\n   *     {mainType: 'series',\r\n   *     filter: function (model, index) {...}}\r\n   * );\r\n   * // result like [component0, componnet1, ...]\r\n   */\n  GlobalModel.prototype.findComponents = function (condition) {\n    var query = condition.query;\n    var mainType = condition.mainType;\n    var queryCond = getQueryCond(query);\n    var result = queryCond ? this.queryComponents(queryCond)\n    // Retrieve all non-empty components.\n    : filter(this._componentsMap.get(mainType), function (cmpt) {\n      return !!cmpt;\n    });\n    return doFilter(filterBySubType(result, condition));\n    function getQueryCond(q) {\n      var indexAttr = mainType + 'Index';\n      var idAttr = mainType + 'Id';\n      var nameAttr = mainType + 'Name';\n      return q && (q[indexAttr] != null || q[idAttr] != null || q[nameAttr] != null) ? {\n        mainType: mainType,\n        // subType will be filtered finally.\n        index: q[indexAttr],\n        id: q[idAttr],\n        name: q[nameAttr]\n      } : null;\n    }\n    function doFilter(res) {\n      return condition.filter ? filter(res, condition.filter) : res;\n    }\n  };\n  GlobalModel.prototype.eachComponent = function (mainType, cb, context) {\n    var componentsMap = this._componentsMap;\n    if (isFunction(mainType)) {\n      var ctxForAll_1 = cb;\n      var cbForAll_1 = mainType;\n      componentsMap.each(function (cmpts, componentType) {\n        for (var i = 0; cmpts && i < cmpts.length; i++) {\n          var cmpt = cmpts[i];\n          cmpt && cbForAll_1.call(ctxForAll_1, componentType, cmpt, cmpt.componentIndex);\n        }\n      });\n    } else {\n      var cmpts = isString(mainType) ? componentsMap.get(mainType) : isObject(mainType) ? this.findComponents(mainType) : null;\n      for (var i = 0; cmpts && i < cmpts.length; i++) {\n        var cmpt = cmpts[i];\n        cmpt && cb.call(context, cmpt, cmpt.componentIndex);\n      }\n    }\n  };\n  /**\r\n   * Get series list before filtered by name.\r\n   */\n  GlobalModel.prototype.getSeriesByName = function (name) {\n    var nameStr = modelUtil.convertOptionIdName(name, null);\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries && nameStr != null && oneSeries.name === nameStr;\n    });\n  };\n  /**\r\n   * Get series list before filtered by index.\r\n   */\n  GlobalModel.prototype.getSeriesByIndex = function (seriesIndex) {\n    return this._componentsMap.get('series')[seriesIndex];\n  };\n  /**\r\n   * Get series list before filtered by type.\r\n   * FIXME: rename to getRawSeriesByType?\r\n   */\n  GlobalModel.prototype.getSeriesByType = function (subType) {\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries && oneSeries.subType === subType;\n    });\n  };\n  /**\r\n   * Get all series before filtered.\r\n   */\n  GlobalModel.prototype.getSeries = function () {\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries;\n    });\n  };\n  /**\r\n   * Count series before filtered.\r\n   */\n  GlobalModel.prototype.getSeriesCount = function () {\n    return this._componentsCount.get('series');\n  };\n  /**\r\n   * After filtering, series may be different\r\n   * from raw series.\r\n   */\n  GlobalModel.prototype.eachSeries = function (cb, context) {\n    assertSeriesInitialized(this);\n    each(this._seriesIndices, function (rawSeriesIndex) {\n      var series = this._componentsMap.get('series')[rawSeriesIndex];\n      cb.call(context, series, rawSeriesIndex);\n    }, this);\n  };\n  /**\r\n   * Iterate raw series before filtered.\r\n   *\r\n   * @param {Function} cb\r\n   * @param {*} context\r\n   */\n  GlobalModel.prototype.eachRawSeries = function (cb, context) {\n    each(this._componentsMap.get('series'), function (series) {\n      series && cb.call(context, series, series.componentIndex);\n    });\n  };\n  /**\r\n   * After filtering, series may be different.\r\n   * from raw series.\r\n   */\n  GlobalModel.prototype.eachSeriesByType = function (subType, cb, context) {\n    assertSeriesInitialized(this);\n    each(this._seriesIndices, function (rawSeriesIndex) {\n      var series = this._componentsMap.get('series')[rawSeriesIndex];\n      if (series.subType === subType) {\n        cb.call(context, series, rawSeriesIndex);\n      }\n    }, this);\n  };\n  /**\r\n   * Iterate raw series before filtered of given type.\r\n   */\n  GlobalModel.prototype.eachRawSeriesByType = function (subType, cb, context) {\n    return each(this.getSeriesByType(subType), cb, context);\n  };\n  GlobalModel.prototype.isSeriesFiltered = function (seriesModel) {\n    assertSeriesInitialized(this);\n    return this._seriesIndicesMap.get(seriesModel.componentIndex) == null;\n  };\n  GlobalModel.prototype.getCurrentSeriesIndices = function () {\n    return (this._seriesIndices || []).slice();\n  };\n  GlobalModel.prototype.filterSeries = function (cb, context) {\n    assertSeriesInitialized(this);\n    var newSeriesIndices = [];\n    each(this._seriesIndices, function (seriesRawIdx) {\n      var series = this._componentsMap.get('series')[seriesRawIdx];\n      cb.call(context, series, seriesRawIdx) && newSeriesIndices.push(seriesRawIdx);\n    }, this);\n    this._seriesIndices = newSeriesIndices;\n    this._seriesIndicesMap = createHashMap(newSeriesIndices);\n  };\n  GlobalModel.prototype.restoreData = function (payload) {\n    reCreateSeriesIndices(this);\n    var componentsMap = this._componentsMap;\n    var componentTypes = [];\n    componentsMap.each(function (components, componentType) {\n      if (ComponentModel.hasClass(componentType)) {\n        componentTypes.push(componentType);\n      }\n    });\n    ComponentModel.topologicalTravel(componentTypes, ComponentModel.getAllClassMainTypes(), function (componentType) {\n      each(componentsMap.get(componentType), function (component) {\n        if (component && (componentType !== 'series' || !isNotTargetSeries(component, payload))) {\n          component.restoreData();\n        }\n      });\n    });\n  };\n  GlobalModel.internalField = function () {\n    reCreateSeriesIndices = function (ecModel) {\n      var seriesIndices = ecModel._seriesIndices = [];\n      each(ecModel._componentsMap.get('series'), function (series) {\n        // series may have been removed by `replaceMerge`.\n        series && seriesIndices.push(series.componentIndex);\n      });\n      ecModel._seriesIndicesMap = createHashMap(seriesIndices);\n    };\n    assertSeriesInitialized = function (ecModel) {\n      // Components that use _seriesIndices should depends on series component,\n      // which make sure that their initialization is after series.\n      if (process.env.NODE_ENV !== 'production') {\n        if (!ecModel._seriesIndices) {\n          throw new Error('Option should contains series.');\n        }\n      }\n    };\n    initBase = function (ecModel, baseOption) {\n      // Using OPTION_INNER_KEY to mark that this option cannot be used outside,\n      // i.e. `chart.setOption(chart.getModel().option);` is forbidden.\n      ecModel.option = {};\n      ecModel.option[OPTION_INNER_KEY] = OPTION_INNER_VALUE;\n      // Init with series: [], in case of calling findSeries method\n      // before series initialized.\n      ecModel._componentsMap = createHashMap({\n        series: []\n      });\n      ecModel._componentsCount = createHashMap();\n      // If user spefied `option.aria`, aria will be enable. This detection should be\n      // performed before theme and globalDefault merge.\n      var airaOption = baseOption.aria;\n      if (isObject(airaOption) && airaOption.enabled == null) {\n        airaOption.enabled = true;\n      }\n      mergeTheme(baseOption, ecModel._theme.option);\n      // TODO Needs clone when merging to the unexisted property\n      merge(baseOption, globalDefault, false);\n      ecModel._mergeOption(baseOption, null);\n    };\n  }();\n  return GlobalModel;\n}(Model);\nfunction isNotTargetSeries(seriesModel, payload) {\n  if (payload) {\n    var index = payload.seriesIndex;\n    var id = payload.seriesId;\n    var name_1 = payload.seriesName;\n    return index != null && seriesModel.componentIndex !== index || id != null && seriesModel.id !== id || name_1 != null && seriesModel.name !== name_1;\n  }\n}\nfunction mergeTheme(option, theme) {\n  // PENDING\n  // NOT use `colorLayer` in theme if option has `color`\n  var notMergeColorLayer = option.color && !option.colorLayer;\n  each(theme, function (themeItem, name) {\n    if (name === 'colorLayer' && notMergeColorLayer) {\n      return;\n    }\n    // If it is component model mainType, the model handles that merge later.\n    // otherwise, merge them here.\n    if (!ComponentModel.hasClass(name)) {\n      if (typeof themeItem === 'object') {\n        option[name] = !option[name] ? clone(themeItem) : merge(option[name], themeItem, false);\n      } else {\n        if (option[name] == null) {\n          option[name] = themeItem;\n        }\n      }\n    }\n  });\n}\nfunction queryByIdOrName(attr, idOrName, cmpts) {\n  // Here is a break from echarts4: string and number are\n  // treated as equal.\n  if (isArray(idOrName)) {\n    var keyMap_1 = createHashMap();\n    each(idOrName, function (idOrNameItem) {\n      if (idOrNameItem != null) {\n        var idName = modelUtil.convertOptionIdName(idOrNameItem, null);\n        idName != null && keyMap_1.set(idOrNameItem, true);\n      }\n    });\n    return filter(cmpts, function (cmpt) {\n      return cmpt && keyMap_1.get(cmpt[attr]);\n    });\n  } else {\n    var idName_1 = modelUtil.convertOptionIdName(idOrName, null);\n    return filter(cmpts, function (cmpt) {\n      return cmpt && idName_1 != null && cmpt[attr] === idName_1;\n    });\n  }\n}\nfunction filterBySubType(components, condition) {\n  // Using hasOwnProperty for restrict. Consider\n  // subType is undefined in user payload.\n  return condition.hasOwnProperty('subType') ? filter(components, function (cmpt) {\n    return cmpt && cmpt.subType === condition.subType;\n  }) : components;\n}\nfunction normalizeSetOptionInput(opts) {\n  var replaceMergeMainTypeMap = createHashMap();\n  opts && each(modelUtil.normalizeToArray(opts.replaceMerge), function (mainType) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(ComponentModel.hasClass(mainType), '\"' + mainType + '\" is not valid component main type in \"replaceMerge\"');\n    }\n    replaceMergeMainTypeMap.set(mainType, true);\n  });\n  return {\n    replaceMergeMainTypeMap: replaceMergeMainTypeMap\n  };\n}\nmixin(GlobalModel, PaletteMixin);\nexport default GlobalModel;", "map": {"version": 3, "names": ["__extends", "each", "filter", "isArray", "isObject", "isString", "createHashMap", "assert", "clone", "merge", "extend", "mixin", "isFunction", "modelUtil", "Model", "ComponentModel", "globalDefault", "resetSourceDefaulter", "concatInternalOptions", "Palette<PERSON><PERSON>in", "error", "warn", "reCreateSeriesIndices", "assertSeriesInitialized", "initBase", "OPTION_INNER_KEY", "OPTION_INNER_VALUE", "BUITIN_COMPONENTS_MAP", "grid", "polar", "geo", "singleAxis", "parallel", "calendar", "graphic", "toolbox", "tooltip", "axisPointer", "brush", "title", "timeline", "markPoint", "markLine", "<PERSON><PERSON><PERSON>", "legend", "dataZoom", "visualMap", "xAxis", "yAxis", "angleAxis", "radiusAxis", "BUILTIN_CHARTS_MAP", "line", "bar", "pie", "scatter", "radar", "map", "tree", "treemap", "graph", "gauge", "funnel", "sankey", "boxplot", "candlestick", "effectScatter", "lines", "heatmap", "pictorialBar", "themeRiver", "sunburst", "custom", "componetsMissingLogPrinted", "checkMissingComponents", "option", "componentOption", "mainType", "hasClass", "componentImportName", "GlobalModel", "_super", "apply", "arguments", "prototype", "init", "parentModel", "ecModel", "theme", "locale", "optionManager", "_theme", "_locale", "_optionManager", "setOption", "opts", "optionPreprocessorFuncs", "process", "env", "NODE_ENV", "innerOpt", "normalizeSetOptionInput", "_resetOption", "resetOption", "type", "opt", "optionChanged", "baseOption", "mountOption", "restoreData", "_mergeOption", "timelineOption", "getTimelineOption", "mediaOptions", "getMediaOption", "length", "mediaOption", "mergeOption", "newOption", "componentsMap", "_componentsMap", "componentsCount", "_componentsCount", "newCmptTypes", "newCmptTypeMap", "replaceMergeMainTypeMap", "push", "set", "val", "mainTypeInReplaceMerge", "get", "topologicalTravel", "getAllClassMainTypes", "visitComponent", "newCmptOptionList", "normalizeToArray", "oldCmptList", "mergeMode", "mappingResult", "mappingToExists", "setComponentTypeToKeyInfo", "optionsByMainType", "cmptsByMainType", "cmptsCountByMainType", "tooltipExists", "tooltipWarningLogged", "resultItem", "index", "componentModel", "existing", "newCmptOption", "optionUpdated", "isSeriesType", "ComponentModelClass", "getClass", "keyInfo", "subType", "seriesImportName", "constructor", "name", "extraOpt", "componentIndex", "brandNew", "__require<PERSON><PERSON><PERSON><PERSON><PERSON>", "_seriesIndices", "getOption", "optInMainType", "realLen", "metNonInner", "i", "isComponentIdInternal", "getTheme", "getLocaleModel", "setUpdatePayload", "payload", "_payload", "getUpdatePayload", "getComponent", "idx", "list", "cmpt", "queryComponents", "condition", "id", "cmpts", "result", "queryByIdOrName", "filterBySubType", "findComponents", "query", "queryCond", "getQueryCond", "<PERSON><PERSON><PERSON><PERSON>", "q", "indexAttr", "idAttr", "nameAttr", "res", "eachComponent", "cb", "context", "ctxForAll_1", "cbForAll_1", "componentType", "call", "getSeriesByName", "nameStr", "convertOptionIdName", "oneSeries", "getSeriesByIndex", "seriesIndex", "getSeriesByType", "getSeries", "getSeriesCount", "eachSeries", "rawSeriesIndex", "series", "eachRawSeries", "eachSeriesByType", "eachRawSeriesByType", "isSeriesFiltered", "seriesModel", "_seriesIndicesMap", "getCurrentSeriesIndices", "slice", "filterSeries", "newSeriesIndices", "seriesRawIdx", "componentTypes", "components", "component", "isNotTargetSeries", "internalField", "seriesIndices", "Error", "airaOption", "aria", "enabled", "mergeTheme", "seriesId", "name_1", "seriesName", "notMergeColorLayer", "color", "colorLayer", "themeItem", "attr", "idOrName", "keyMap_1", "idOrNameItem", "idName", "idName_1", "hasOwnProperty", "replaceMerge"], "sources": ["E:/frontCodeCode/datafront/node_modules/echarts/lib/model/Global.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Caution: If the mechanism should be changed some day, these cases\r\n * should be considered:\r\n *\r\n * (1) In `merge option` mode, if using the same option to call `setOption`\r\n * many times, the result should be the same (try our best to ensure that).\r\n * (2) In `merge option` mode, if a component has no id/name specified, it\r\n * will be merged by index, and the result sequence of the components is\r\n * consistent to the original sequence.\r\n * (3) In `replaceMerge` mode, keep the result sequence of the components is\r\n * consistent to the original sequence, even though there might result in \"hole\".\r\n * (4) `reset` feature (in toolbox). Find detailed info in comments about\r\n * `mergeOption` in module:echarts/model/OptionManager.\r\n */\nimport { each, filter, isArray, isObject, isString, createHashMap, assert, clone, merge, extend, mixin, isFunction } from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../util/model.js';\nimport Model from './Model.js';\nimport ComponentModel from './Component.js';\nimport globalDefault from './globalDefault.js';\nimport { resetSourceDefaulter } from '../data/helper/sourceHelper.js';\nimport { concatInternalOptions } from './internalComponentCreator.js';\nimport { PaletteMixin } from './mixin/palette.js';\nimport { error, warn } from '../util/log.js';\n// -----------------------\n// Internal method names:\n// -----------------------\nvar reCreateSeriesIndices;\nvar assertSeriesInitialized;\nvar initBase;\nvar OPTION_INNER_KEY = '\\0_ec_inner';\nvar OPTION_INNER_VALUE = 1;\nvar BUITIN_COMPONENTS_MAP = {\n  grid: 'GridComponent',\n  polar: 'PolarComponent',\n  geo: 'GeoComponent',\n  singleAxis: 'SingleAxisComponent',\n  parallel: 'ParallelComponent',\n  calendar: 'CalendarComponent',\n  graphic: 'GraphicComponent',\n  toolbox: 'ToolboxComponent',\n  tooltip: 'TooltipComponent',\n  axisPointer: 'AxisPointerComponent',\n  brush: 'BrushComponent',\n  title: 'TitleComponent',\n  timeline: 'TimelineComponent',\n  markPoint: 'MarkPointComponent',\n  markLine: 'MarkLineComponent',\n  markArea: 'MarkAreaComponent',\n  legend: 'LegendComponent',\n  dataZoom: 'DataZoomComponent',\n  visualMap: 'VisualMapComponent',\n  // aria: 'AriaComponent',\n  // dataset: 'DatasetComponent',\n  // Dependencies\n  xAxis: 'GridComponent',\n  yAxis: 'GridComponent',\n  angleAxis: 'PolarComponent',\n  radiusAxis: 'PolarComponent'\n};\nvar BUILTIN_CHARTS_MAP = {\n  line: 'LineChart',\n  bar: 'BarChart',\n  pie: 'PieChart',\n  scatter: 'ScatterChart',\n  radar: 'RadarChart',\n  map: 'MapChart',\n  tree: 'TreeChart',\n  treemap: 'TreemapChart',\n  graph: 'GraphChart',\n  gauge: 'GaugeChart',\n  funnel: 'FunnelChart',\n  parallel: 'ParallelChart',\n  sankey: 'SankeyChart',\n  boxplot: 'BoxplotChart',\n  candlestick: 'CandlestickChart',\n  effectScatter: 'EffectScatterChart',\n  lines: 'LinesChart',\n  heatmap: 'HeatmapChart',\n  pictorialBar: 'PictorialBarChart',\n  themeRiver: 'ThemeRiverChart',\n  sunburst: 'SunburstChart',\n  custom: 'CustomChart'\n};\nvar componetsMissingLogPrinted = {};\nfunction checkMissingComponents(option) {\n  each(option, function (componentOption, mainType) {\n    if (!ComponentModel.hasClass(mainType)) {\n      var componentImportName = BUITIN_COMPONENTS_MAP[mainType];\n      if (componentImportName && !componetsMissingLogPrinted[componentImportName]) {\n        error(\"Component \" + mainType + \" is used but not imported.\\nimport { \" + componentImportName + \" } from 'echarts/components';\\necharts.use([\" + componentImportName + \"]);\");\n        componetsMissingLogPrinted[componentImportName] = true;\n      }\n    }\n  });\n}\nvar GlobalModel = /** @class */function (_super) {\n  __extends(GlobalModel, _super);\n  function GlobalModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  GlobalModel.prototype.init = function (option, parentModel, ecModel, theme, locale, optionManager) {\n    theme = theme || {};\n    this.option = null; // Mark as not initialized.\n    this._theme = new Model(theme);\n    this._locale = new Model(locale);\n    this._optionManager = optionManager;\n  };\n  GlobalModel.prototype.setOption = function (option, opts, optionPreprocessorFuncs) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(option != null, 'option is null/undefined');\n      assert(option[OPTION_INNER_KEY] !== OPTION_INNER_VALUE, 'please use chart.getOption()');\n    }\n    var innerOpt = normalizeSetOptionInput(opts);\n    this._optionManager.setOption(option, optionPreprocessorFuncs, innerOpt);\n    this._resetOption(null, innerOpt);\n  };\n  /**\r\n   * @param type null/undefined: reset all.\r\n   *        'recreate': force recreate all.\r\n   *        'timeline': only reset timeline option\r\n   *        'media': only reset media query option\r\n   * @return Whether option changed.\r\n   */\n  GlobalModel.prototype.resetOption = function (type, opt) {\n    return this._resetOption(type, normalizeSetOptionInput(opt));\n  };\n  GlobalModel.prototype._resetOption = function (type, opt) {\n    var optionChanged = false;\n    var optionManager = this._optionManager;\n    if (!type || type === 'recreate') {\n      var baseOption = optionManager.mountOption(type === 'recreate');\n      if (process.env.NODE_ENV !== 'production') {\n        checkMissingComponents(baseOption);\n      }\n      if (!this.option || type === 'recreate') {\n        initBase(this, baseOption);\n      } else {\n        this.restoreData();\n        this._mergeOption(baseOption, opt);\n      }\n      optionChanged = true;\n    }\n    if (type === 'timeline' || type === 'media') {\n      this.restoreData();\n    }\n    // By design, if `setOption(option2)` at the second time, and `option2` is a `ECUnitOption`,\n    // it should better not have the same props with `MediaUnit['option']`.\n    // Because either `option2` or `MediaUnit['option']` will be always merged to \"current option\"\n    // rather than original \"baseOption\". If they both override a prop, the result might be\n    // unexpected when media state changed after `setOption` called.\n    // If we really need to modify a props in each `MediaUnit['option']`, use the full version\n    // (`{baseOption, media}`) in `setOption`.\n    // For `timeline`, the case is the same.\n    if (!type || type === 'recreate' || type === 'timeline') {\n      var timelineOption = optionManager.getTimelineOption(this);\n      if (timelineOption) {\n        optionChanged = true;\n        this._mergeOption(timelineOption, opt);\n      }\n    }\n    if (!type || type === 'recreate' || type === 'media') {\n      var mediaOptions = optionManager.getMediaOption(this);\n      if (mediaOptions.length) {\n        each(mediaOptions, function (mediaOption) {\n          optionChanged = true;\n          this._mergeOption(mediaOption, opt);\n        }, this);\n      }\n    }\n    return optionChanged;\n  };\n  GlobalModel.prototype.mergeOption = function (option) {\n    this._mergeOption(option, null);\n  };\n  GlobalModel.prototype._mergeOption = function (newOption, opt) {\n    var option = this.option;\n    var componentsMap = this._componentsMap;\n    var componentsCount = this._componentsCount;\n    var newCmptTypes = [];\n    var newCmptTypeMap = createHashMap();\n    var replaceMergeMainTypeMap = opt && opt.replaceMergeMainTypeMap;\n    resetSourceDefaulter(this);\n    // If no component class, merge directly.\n    // For example: color, animaiton options, etc.\n    each(newOption, function (componentOption, mainType) {\n      if (componentOption == null) {\n        return;\n      }\n      if (!ComponentModel.hasClass(mainType)) {\n        // globalSettingTask.dirty();\n        option[mainType] = option[mainType] == null ? clone(componentOption) : merge(option[mainType], componentOption, true);\n      } else if (mainType) {\n        newCmptTypes.push(mainType);\n        newCmptTypeMap.set(mainType, true);\n      }\n    });\n    if (replaceMergeMainTypeMap) {\n      // If there is a mainType `xxx` in `replaceMerge` but not declared in option,\n      // we trade it as it is declared in option as `{xxx: []}`. Because:\n      // (1) for normal merge, `{xxx: null/undefined}` are the same meaning as `{xxx: []}`.\n      // (2) some preprocessor may convert some of `{xxx: null/undefined}` to `{xxx: []}`.\n      replaceMergeMainTypeMap.each(function (val, mainTypeInReplaceMerge) {\n        if (ComponentModel.hasClass(mainTypeInReplaceMerge) && !newCmptTypeMap.get(mainTypeInReplaceMerge)) {\n          newCmptTypes.push(mainTypeInReplaceMerge);\n          newCmptTypeMap.set(mainTypeInReplaceMerge, true);\n        }\n      });\n    }\n    ComponentModel.topologicalTravel(newCmptTypes, ComponentModel.getAllClassMainTypes(), visitComponent, this);\n    function visitComponent(mainType) {\n      var newCmptOptionList = concatInternalOptions(this, mainType, modelUtil.normalizeToArray(newOption[mainType]));\n      var oldCmptList = componentsMap.get(mainType);\n      var mergeMode =\n      // `!oldCmptList` means init. See the comment in `mappingToExists`\n      !oldCmptList ? 'replaceAll' : replaceMergeMainTypeMap && replaceMergeMainTypeMap.get(mainType) ? 'replaceMerge' : 'normalMerge';\n      var mappingResult = modelUtil.mappingToExists(oldCmptList, newCmptOptionList, mergeMode);\n      // Set mainType and complete subType.\n      modelUtil.setComponentTypeToKeyInfo(mappingResult, mainType, ComponentModel);\n      // Empty it before the travel, in order to prevent `this._componentsMap`\n      // from being used in the `init`/`mergeOption`/`optionUpdated` of some\n      // components, which is probably incorrect logic.\n      option[mainType] = null;\n      componentsMap.set(mainType, null);\n      componentsCount.set(mainType, 0);\n      var optionsByMainType = [];\n      var cmptsByMainType = [];\n      var cmptsCountByMainType = 0;\n      var tooltipExists;\n      var tooltipWarningLogged;\n      each(mappingResult, function (resultItem, index) {\n        var componentModel = resultItem.existing;\n        var newCmptOption = resultItem.newOption;\n        if (!newCmptOption) {\n          if (componentModel) {\n            // Consider where is no new option and should be merged using {},\n            // see removeEdgeAndAdd in topologicalTravel and\n            // ComponentModel.getAllClassMainTypes.\n            componentModel.mergeOption({}, this);\n            componentModel.optionUpdated({}, false);\n          }\n          // If no both `resultItem.exist` and `resultItem.option`,\n          // either it is in `replaceMerge` and not matched by any id,\n          // or it has been removed in previous `replaceMerge` and left a \"hole\" in this component index.\n        } else {\n          var isSeriesType = mainType === 'series';\n          var ComponentModelClass = ComponentModel.getClass(mainType, resultItem.keyInfo.subType, !isSeriesType // Give a more detailed warn later if series don't exists\n          );\n          if (!ComponentModelClass) {\n            if (process.env.NODE_ENV !== 'production') {\n              var subType = resultItem.keyInfo.subType;\n              var seriesImportName = BUILTIN_CHARTS_MAP[subType];\n              if (!componetsMissingLogPrinted[subType]) {\n                componetsMissingLogPrinted[subType] = true;\n                if (seriesImportName) {\n                  error(\"Series \" + subType + \" is used but not imported.\\nimport { \" + seriesImportName + \" } from 'echarts/charts';\\necharts.use([\" + seriesImportName + \"]);\");\n                } else {\n                  error(\"Unknown series \" + subType);\n                }\n              }\n            }\n            return;\n          }\n          // TODO Before multiple tooltips get supported, we do this check to avoid unexpected exception.\n          if (mainType === 'tooltip') {\n            if (tooltipExists) {\n              if (process.env.NODE_ENV !== 'production') {\n                if (!tooltipWarningLogged) {\n                  warn('Currently only one tooltip component is allowed.');\n                  tooltipWarningLogged = true;\n                }\n              }\n              return;\n            }\n            tooltipExists = true;\n          }\n          if (componentModel && componentModel.constructor === ComponentModelClass) {\n            componentModel.name = resultItem.keyInfo.name;\n            // componentModel.settingTask && componentModel.settingTask.dirty();\n            componentModel.mergeOption(newCmptOption, this);\n            componentModel.optionUpdated(newCmptOption, false);\n          } else {\n            // PENDING Global as parent ?\n            var extraOpt = extend({\n              componentIndex: index\n            }, resultItem.keyInfo);\n            componentModel = new ComponentModelClass(newCmptOption, this, this, extraOpt);\n            // Assign `keyInfo`\n            extend(componentModel, extraOpt);\n            if (resultItem.brandNew) {\n              componentModel.__requireNewView = true;\n            }\n            componentModel.init(newCmptOption, this, this);\n            // Call optionUpdated after init.\n            // newCmptOption has been used as componentModel.option\n            // and may be merged with theme and default, so pass null\n            // to avoid confusion.\n            componentModel.optionUpdated(null, true);\n          }\n        }\n        if (componentModel) {\n          optionsByMainType.push(componentModel.option);\n          cmptsByMainType.push(componentModel);\n          cmptsCountByMainType++;\n        } else {\n          // Always do assign to avoid elided item in array.\n          optionsByMainType.push(void 0);\n          cmptsByMainType.push(void 0);\n        }\n      }, this);\n      option[mainType] = optionsByMainType;\n      componentsMap.set(mainType, cmptsByMainType);\n      componentsCount.set(mainType, cmptsCountByMainType);\n      // Backup series for filtering.\n      if (mainType === 'series') {\n        reCreateSeriesIndices(this);\n      }\n    }\n    // If no series declared, ensure `_seriesIndices` initialized.\n    if (!this._seriesIndices) {\n      reCreateSeriesIndices(this);\n    }\n  };\n  /**\r\n   * Get option for output (cloned option and inner info removed)\r\n   */\n  GlobalModel.prototype.getOption = function () {\n    var option = clone(this.option);\n    each(option, function (optInMainType, mainType) {\n      if (ComponentModel.hasClass(mainType)) {\n        var opts = modelUtil.normalizeToArray(optInMainType);\n        // Inner cmpts need to be removed.\n        // Inner cmpts might not be at last since ec5.0, but still\n        // compatible for users: if inner cmpt at last, splice the returned array.\n        var realLen = opts.length;\n        var metNonInner = false;\n        for (var i = realLen - 1; i >= 0; i--) {\n          // Remove options with inner id.\n          if (opts[i] && !modelUtil.isComponentIdInternal(opts[i])) {\n            metNonInner = true;\n          } else {\n            opts[i] = null;\n            !metNonInner && realLen--;\n          }\n        }\n        opts.length = realLen;\n        option[mainType] = opts;\n      }\n    });\n    delete option[OPTION_INNER_KEY];\n    return option;\n  };\n  GlobalModel.prototype.getTheme = function () {\n    return this._theme;\n  };\n  GlobalModel.prototype.getLocaleModel = function () {\n    return this._locale;\n  };\n  GlobalModel.prototype.setUpdatePayload = function (payload) {\n    this._payload = payload;\n  };\n  GlobalModel.prototype.getUpdatePayload = function () {\n    return this._payload;\n  };\n  /**\r\n   * @param idx If not specified, return the first one.\r\n   */\n  GlobalModel.prototype.getComponent = function (mainType, idx) {\n    var list = this._componentsMap.get(mainType);\n    if (list) {\n      var cmpt = list[idx || 0];\n      if (cmpt) {\n        return cmpt;\n      } else if (idx == null) {\n        for (var i = 0; i < list.length; i++) {\n          if (list[i]) {\n            return list[i];\n          }\n        }\n      }\n    }\n  };\n  /**\r\n   * @return Never be null/undefined.\r\n   */\n  GlobalModel.prototype.queryComponents = function (condition) {\n    var mainType = condition.mainType;\n    if (!mainType) {\n      return [];\n    }\n    var index = condition.index;\n    var id = condition.id;\n    var name = condition.name;\n    var cmpts = this._componentsMap.get(mainType);\n    if (!cmpts || !cmpts.length) {\n      return [];\n    }\n    var result;\n    if (index != null) {\n      result = [];\n      each(modelUtil.normalizeToArray(index), function (idx) {\n        cmpts[idx] && result.push(cmpts[idx]);\n      });\n    } else if (id != null) {\n      result = queryByIdOrName('id', id, cmpts);\n    } else if (name != null) {\n      result = queryByIdOrName('name', name, cmpts);\n    } else {\n      // Return all non-empty components in that mainType\n      result = filter(cmpts, function (cmpt) {\n        return !!cmpt;\n      });\n    }\n    return filterBySubType(result, condition);\n  };\n  /**\r\n   * The interface is different from queryComponents,\r\n   * which is convenient for inner usage.\r\n   *\r\n   * @usage\r\n   * let result = findComponents(\r\n   *     {mainType: 'dataZoom', query: {dataZoomId: 'abc'}}\r\n   * );\r\n   * let result = findComponents(\r\n   *     {mainType: 'series', subType: 'pie', query: {seriesName: 'uio'}}\r\n   * );\r\n   * let result = findComponents(\r\n   *     {mainType: 'series',\r\n   *     filter: function (model, index) {...}}\r\n   * );\r\n   * // result like [component0, componnet1, ...]\r\n   */\n  GlobalModel.prototype.findComponents = function (condition) {\n    var query = condition.query;\n    var mainType = condition.mainType;\n    var queryCond = getQueryCond(query);\n    var result = queryCond ? this.queryComponents(queryCond)\n    // Retrieve all non-empty components.\n    : filter(this._componentsMap.get(mainType), function (cmpt) {\n      return !!cmpt;\n    });\n    return doFilter(filterBySubType(result, condition));\n    function getQueryCond(q) {\n      var indexAttr = mainType + 'Index';\n      var idAttr = mainType + 'Id';\n      var nameAttr = mainType + 'Name';\n      return q && (q[indexAttr] != null || q[idAttr] != null || q[nameAttr] != null) ? {\n        mainType: mainType,\n        // subType will be filtered finally.\n        index: q[indexAttr],\n        id: q[idAttr],\n        name: q[nameAttr]\n      } : null;\n    }\n    function doFilter(res) {\n      return condition.filter ? filter(res, condition.filter) : res;\n    }\n  };\n  GlobalModel.prototype.eachComponent = function (mainType, cb, context) {\n    var componentsMap = this._componentsMap;\n    if (isFunction(mainType)) {\n      var ctxForAll_1 = cb;\n      var cbForAll_1 = mainType;\n      componentsMap.each(function (cmpts, componentType) {\n        for (var i = 0; cmpts && i < cmpts.length; i++) {\n          var cmpt = cmpts[i];\n          cmpt && cbForAll_1.call(ctxForAll_1, componentType, cmpt, cmpt.componentIndex);\n        }\n      });\n    } else {\n      var cmpts = isString(mainType) ? componentsMap.get(mainType) : isObject(mainType) ? this.findComponents(mainType) : null;\n      for (var i = 0; cmpts && i < cmpts.length; i++) {\n        var cmpt = cmpts[i];\n        cmpt && cb.call(context, cmpt, cmpt.componentIndex);\n      }\n    }\n  };\n  /**\r\n   * Get series list before filtered by name.\r\n   */\n  GlobalModel.prototype.getSeriesByName = function (name) {\n    var nameStr = modelUtil.convertOptionIdName(name, null);\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries && nameStr != null && oneSeries.name === nameStr;\n    });\n  };\n  /**\r\n   * Get series list before filtered by index.\r\n   */\n  GlobalModel.prototype.getSeriesByIndex = function (seriesIndex) {\n    return this._componentsMap.get('series')[seriesIndex];\n  };\n  /**\r\n   * Get series list before filtered by type.\r\n   * FIXME: rename to getRawSeriesByType?\r\n   */\n  GlobalModel.prototype.getSeriesByType = function (subType) {\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries && oneSeries.subType === subType;\n    });\n  };\n  /**\r\n   * Get all series before filtered.\r\n   */\n  GlobalModel.prototype.getSeries = function () {\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries;\n    });\n  };\n  /**\r\n   * Count series before filtered.\r\n   */\n  GlobalModel.prototype.getSeriesCount = function () {\n    return this._componentsCount.get('series');\n  };\n  /**\r\n   * After filtering, series may be different\r\n   * from raw series.\r\n   */\n  GlobalModel.prototype.eachSeries = function (cb, context) {\n    assertSeriesInitialized(this);\n    each(this._seriesIndices, function (rawSeriesIndex) {\n      var series = this._componentsMap.get('series')[rawSeriesIndex];\n      cb.call(context, series, rawSeriesIndex);\n    }, this);\n  };\n  /**\r\n   * Iterate raw series before filtered.\r\n   *\r\n   * @param {Function} cb\r\n   * @param {*} context\r\n   */\n  GlobalModel.prototype.eachRawSeries = function (cb, context) {\n    each(this._componentsMap.get('series'), function (series) {\n      series && cb.call(context, series, series.componentIndex);\n    });\n  };\n  /**\r\n   * After filtering, series may be different.\r\n   * from raw series.\r\n   */\n  GlobalModel.prototype.eachSeriesByType = function (subType, cb, context) {\n    assertSeriesInitialized(this);\n    each(this._seriesIndices, function (rawSeriesIndex) {\n      var series = this._componentsMap.get('series')[rawSeriesIndex];\n      if (series.subType === subType) {\n        cb.call(context, series, rawSeriesIndex);\n      }\n    }, this);\n  };\n  /**\r\n   * Iterate raw series before filtered of given type.\r\n   */\n  GlobalModel.prototype.eachRawSeriesByType = function (subType, cb, context) {\n    return each(this.getSeriesByType(subType), cb, context);\n  };\n  GlobalModel.prototype.isSeriesFiltered = function (seriesModel) {\n    assertSeriesInitialized(this);\n    return this._seriesIndicesMap.get(seriesModel.componentIndex) == null;\n  };\n  GlobalModel.prototype.getCurrentSeriesIndices = function () {\n    return (this._seriesIndices || []).slice();\n  };\n  GlobalModel.prototype.filterSeries = function (cb, context) {\n    assertSeriesInitialized(this);\n    var newSeriesIndices = [];\n    each(this._seriesIndices, function (seriesRawIdx) {\n      var series = this._componentsMap.get('series')[seriesRawIdx];\n      cb.call(context, series, seriesRawIdx) && newSeriesIndices.push(seriesRawIdx);\n    }, this);\n    this._seriesIndices = newSeriesIndices;\n    this._seriesIndicesMap = createHashMap(newSeriesIndices);\n  };\n  GlobalModel.prototype.restoreData = function (payload) {\n    reCreateSeriesIndices(this);\n    var componentsMap = this._componentsMap;\n    var componentTypes = [];\n    componentsMap.each(function (components, componentType) {\n      if (ComponentModel.hasClass(componentType)) {\n        componentTypes.push(componentType);\n      }\n    });\n    ComponentModel.topologicalTravel(componentTypes, ComponentModel.getAllClassMainTypes(), function (componentType) {\n      each(componentsMap.get(componentType), function (component) {\n        if (component && (componentType !== 'series' || !isNotTargetSeries(component, payload))) {\n          component.restoreData();\n        }\n      });\n    });\n  };\n  GlobalModel.internalField = function () {\n    reCreateSeriesIndices = function (ecModel) {\n      var seriesIndices = ecModel._seriesIndices = [];\n      each(ecModel._componentsMap.get('series'), function (series) {\n        // series may have been removed by `replaceMerge`.\n        series && seriesIndices.push(series.componentIndex);\n      });\n      ecModel._seriesIndicesMap = createHashMap(seriesIndices);\n    };\n    assertSeriesInitialized = function (ecModel) {\n      // Components that use _seriesIndices should depends on series component,\n      // which make sure that their initialization is after series.\n      if (process.env.NODE_ENV !== 'production') {\n        if (!ecModel._seriesIndices) {\n          throw new Error('Option should contains series.');\n        }\n      }\n    };\n    initBase = function (ecModel, baseOption) {\n      // Using OPTION_INNER_KEY to mark that this option cannot be used outside,\n      // i.e. `chart.setOption(chart.getModel().option);` is forbidden.\n      ecModel.option = {};\n      ecModel.option[OPTION_INNER_KEY] = OPTION_INNER_VALUE;\n      // Init with series: [], in case of calling findSeries method\n      // before series initialized.\n      ecModel._componentsMap = createHashMap({\n        series: []\n      });\n      ecModel._componentsCount = createHashMap();\n      // If user spefied `option.aria`, aria will be enable. This detection should be\n      // performed before theme and globalDefault merge.\n      var airaOption = baseOption.aria;\n      if (isObject(airaOption) && airaOption.enabled == null) {\n        airaOption.enabled = true;\n      }\n      mergeTheme(baseOption, ecModel._theme.option);\n      // TODO Needs clone when merging to the unexisted property\n      merge(baseOption, globalDefault, false);\n      ecModel._mergeOption(baseOption, null);\n    };\n  }();\n  return GlobalModel;\n}(Model);\nfunction isNotTargetSeries(seriesModel, payload) {\n  if (payload) {\n    var index = payload.seriesIndex;\n    var id = payload.seriesId;\n    var name_1 = payload.seriesName;\n    return index != null && seriesModel.componentIndex !== index || id != null && seriesModel.id !== id || name_1 != null && seriesModel.name !== name_1;\n  }\n}\nfunction mergeTheme(option, theme) {\n  // PENDING\n  // NOT use `colorLayer` in theme if option has `color`\n  var notMergeColorLayer = option.color && !option.colorLayer;\n  each(theme, function (themeItem, name) {\n    if (name === 'colorLayer' && notMergeColorLayer) {\n      return;\n    }\n    // If it is component model mainType, the model handles that merge later.\n    // otherwise, merge them here.\n    if (!ComponentModel.hasClass(name)) {\n      if (typeof themeItem === 'object') {\n        option[name] = !option[name] ? clone(themeItem) : merge(option[name], themeItem, false);\n      } else {\n        if (option[name] == null) {\n          option[name] = themeItem;\n        }\n      }\n    }\n  });\n}\nfunction queryByIdOrName(attr, idOrName, cmpts) {\n  // Here is a break from echarts4: string and number are\n  // treated as equal.\n  if (isArray(idOrName)) {\n    var keyMap_1 = createHashMap();\n    each(idOrName, function (idOrNameItem) {\n      if (idOrNameItem != null) {\n        var idName = modelUtil.convertOptionIdName(idOrNameItem, null);\n        idName != null && keyMap_1.set(idOrNameItem, true);\n      }\n    });\n    return filter(cmpts, function (cmpt) {\n      return cmpt && keyMap_1.get(cmpt[attr]);\n    });\n  } else {\n    var idName_1 = modelUtil.convertOptionIdName(idOrName, null);\n    return filter(cmpts, function (cmpt) {\n      return cmpt && idName_1 != null && cmpt[attr] === idName_1;\n    });\n  }\n}\nfunction filterBySubType(components, condition) {\n  // Using hasOwnProperty for restrict. Consider\n  // subType is undefined in user payload.\n  return condition.hasOwnProperty('subType') ? filter(components, function (cmpt) {\n    return cmpt && cmpt.subType === condition.subType;\n  }) : components;\n}\nfunction normalizeSetOptionInput(opts) {\n  var replaceMergeMainTypeMap = createHashMap();\n  opts && each(modelUtil.normalizeToArray(opts.replaceMerge), function (mainType) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(ComponentModel.hasClass(mainType), '\"' + mainType + '\" is not valid component main type in \"replaceMerge\"');\n    }\n    replaceMergeMainTypeMap.set(mainType, true);\n  });\n  return {\n    replaceMergeMainTypeMap: replaceMergeMainTypeMap\n  };\n}\nmixin(GlobalModel, PaletteMixin);\nexport default GlobalModel;"], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,QAAQ,0BAA0B;AACpJ,OAAO,KAAKC,SAAS,MAAM,kBAAkB;AAC7C,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,cAAc,MAAM,gBAAgB;AAC3C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,qBAAqB,QAAQ,+BAA+B;AACrE,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,KAAK,EAAEC,IAAI,QAAQ,gBAAgB;AAC5C;AACA;AACA;AACA,IAAIC,qBAAqB;AACzB,IAAIC,uBAAuB;AAC3B,IAAIC,QAAQ;AACZ,IAAIC,gBAAgB,GAAG,aAAa;AACpC,IAAIC,kBAAkB,GAAG,CAAC;AAC1B,IAAIC,qBAAqB,GAAG;EAC1BC,IAAI,EAAE,eAAe;EACrBC,KAAK,EAAE,gBAAgB;EACvBC,GAAG,EAAE,cAAc;EACnBC,UAAU,EAAE,qBAAqB;EACjCC,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAE,mBAAmB;EAC7BC,OAAO,EAAE,kBAAkB;EAC3BC,OAAO,EAAE,kBAAkB;EAC3BC,OAAO,EAAE,kBAAkB;EAC3BC,WAAW,EAAE,sBAAsB;EACnCC,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE,gBAAgB;EACvBC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,oBAAoB;EAC/BC,QAAQ,EAAE,mBAAmB;EAC7BC,QAAQ,EAAE,mBAAmB;EAC7BC,MAAM,EAAE,iBAAiB;EACzBC,QAAQ,EAAE,mBAAmB;EAC7BC,SAAS,EAAE,oBAAoB;EAC/B;EACA;EACA;EACAC,KAAK,EAAE,eAAe;EACtBC,KAAK,EAAE,eAAe;EACtBC,SAAS,EAAE,gBAAgB;EAC3BC,UAAU,EAAE;AACd,CAAC;AACD,IAAIC,kBAAkB,GAAG;EACvBC,IAAI,EAAE,WAAW;EACjBC,GAAG,EAAE,UAAU;EACfC,GAAG,EAAE,UAAU;EACfC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,YAAY;EACnBC,GAAG,EAAE,UAAU;EACfC,IAAI,EAAE,WAAW;EACjBC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,YAAY;EACnBC,KAAK,EAAE,YAAY;EACnBC,MAAM,EAAE,aAAa;EACrB9B,QAAQ,EAAE,eAAe;EACzB+B,MAAM,EAAE,aAAa;EACrBC,OAAO,EAAE,cAAc;EACvBC,WAAW,EAAE,kBAAkB;EAC/BC,aAAa,EAAE,oBAAoB;EACnCC,KAAK,EAAE,YAAY;EACnBC,OAAO,EAAE,cAAc;EACvBC,YAAY,EAAE,mBAAmB;EACjCC,UAAU,EAAE,iBAAiB;EAC7BC,QAAQ,EAAE,eAAe;EACzBC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,0BAA0B,GAAG,CAAC,CAAC;AACnC,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EACtC1E,IAAI,CAAC0E,MAAM,EAAE,UAAUC,eAAe,EAAEC,QAAQ,EAAE;IAChD,IAAI,CAAC9D,cAAc,CAAC+D,QAAQ,CAACD,QAAQ,CAAC,EAAE;MACtC,IAAIE,mBAAmB,GAAGpD,qBAAqB,CAACkD,QAAQ,CAAC;MACzD,IAAIE,mBAAmB,IAAI,CAACN,0BAA0B,CAACM,mBAAmB,CAAC,EAAE;QAC3E3D,KAAK,CAAC,YAAY,GAAGyD,QAAQ,GAAG,uCAAuC,GAAGE,mBAAmB,GAAG,8CAA8C,GAAGA,mBAAmB,GAAG,KAAK,CAAC;QAC7KN,0BAA0B,CAACM,mBAAmB,CAAC,GAAG,IAAI;MACxD;IACF;EACF,CAAC,CAAC;AACJ;AACA,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/CjF,SAAS,CAACgF,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACAH,WAAW,CAACI,SAAS,CAACC,IAAI,GAAG,UAAUV,MAAM,EAAEW,WAAW,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,aAAa,EAAE;IACjGF,KAAK,GAAGA,KAAK,IAAI,CAAC,CAAC;IACnB,IAAI,CAACb,MAAM,GAAG,IAAI,CAAC,CAAC;IACpB,IAAI,CAACgB,MAAM,GAAG,IAAI7E,KAAK,CAAC0E,KAAK,CAAC;IAC9B,IAAI,CAACI,OAAO,GAAG,IAAI9E,KAAK,CAAC2E,MAAM,CAAC;IAChC,IAAI,CAACI,cAAc,GAAGH,aAAa;EACrC,CAAC;EACDV,WAAW,CAACI,SAAS,CAACU,SAAS,GAAG,UAAUnB,MAAM,EAAEoB,IAAI,EAAEC,uBAAuB,EAAE;IACjF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC5F,MAAM,CAACoE,MAAM,IAAI,IAAI,EAAE,0BAA0B,CAAC;MAClDpE,MAAM,CAACoE,MAAM,CAAClD,gBAAgB,CAAC,KAAKC,kBAAkB,EAAE,8BAA8B,CAAC;IACzF;IACA,IAAI0E,QAAQ,GAAGC,uBAAuB,CAACN,IAAI,CAAC;IAC5C,IAAI,CAACF,cAAc,CAACC,SAAS,CAACnB,MAAM,EAAEqB,uBAAuB,EAAEI,QAAQ,CAAC;IACxE,IAAI,CAACE,YAAY,CAAC,IAAI,EAAEF,QAAQ,CAAC;EACnC,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACEpB,WAAW,CAACI,SAAS,CAACmB,WAAW,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;IACvD,OAAO,IAAI,CAACH,YAAY,CAACE,IAAI,EAAEH,uBAAuB,CAACI,GAAG,CAAC,CAAC;EAC9D,CAAC;EACDzB,WAAW,CAACI,SAAS,CAACkB,YAAY,GAAG,UAAUE,IAAI,EAAEC,GAAG,EAAE;IACxD,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAIhB,aAAa,GAAG,IAAI,CAACG,cAAc;IACvC,IAAI,CAACW,IAAI,IAAIA,IAAI,KAAK,UAAU,EAAE;MAChC,IAAIG,UAAU,GAAGjB,aAAa,CAACkB,WAAW,CAACJ,IAAI,KAAK,UAAU,CAAC;MAC/D,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCzB,sBAAsB,CAACiC,UAAU,CAAC;MACpC;MACA,IAAI,CAAC,IAAI,CAAChC,MAAM,IAAI6B,IAAI,KAAK,UAAU,EAAE;QACvChF,QAAQ,CAAC,IAAI,EAAEmF,UAAU,CAAC;MAC5B,CAAC,MAAM;QACL,IAAI,CAACE,WAAW,CAAC,CAAC;QAClB,IAAI,CAACC,YAAY,CAACH,UAAU,EAAEF,GAAG,CAAC;MACpC;MACAC,aAAa,GAAG,IAAI;IACtB;IACA,IAAIF,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,OAAO,EAAE;MAC3C,IAAI,CAACK,WAAW,CAAC,CAAC;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACL,IAAI,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,UAAU,EAAE;MACvD,IAAIO,cAAc,GAAGrB,aAAa,CAACsB,iBAAiB,CAAC,IAAI,CAAC;MAC1D,IAAID,cAAc,EAAE;QAClBL,aAAa,GAAG,IAAI;QACpB,IAAI,CAACI,YAAY,CAACC,cAAc,EAAEN,GAAG,CAAC;MACxC;IACF;IACA,IAAI,CAACD,IAAI,IAAIA,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpD,IAAIS,YAAY,GAAGvB,aAAa,CAACwB,cAAc,CAAC,IAAI,CAAC;MACrD,IAAID,YAAY,CAACE,MAAM,EAAE;QACvBlH,IAAI,CAACgH,YAAY,EAAE,UAAUG,WAAW,EAAE;UACxCV,aAAa,GAAG,IAAI;UACpB,IAAI,CAACI,YAAY,CAACM,WAAW,EAAEX,GAAG,CAAC;QACrC,CAAC,EAAE,IAAI,CAAC;MACV;IACF;IACA,OAAOC,aAAa;EACtB,CAAC;EACD1B,WAAW,CAACI,SAAS,CAACiC,WAAW,GAAG,UAAU1C,MAAM,EAAE;IACpD,IAAI,CAACmC,YAAY,CAACnC,MAAM,EAAE,IAAI,CAAC;EACjC,CAAC;EACDK,WAAW,CAACI,SAAS,CAAC0B,YAAY,GAAG,UAAUQ,SAAS,EAAEb,GAAG,EAAE;IAC7D,IAAI9B,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI4C,aAAa,GAAG,IAAI,CAACC,cAAc;IACvC,IAAIC,eAAe,GAAG,IAAI,CAACC,gBAAgB;IAC3C,IAAIC,YAAY,GAAG,EAAE;IACrB,IAAIC,cAAc,GAAGtH,aAAa,CAAC,CAAC;IACpC,IAAIuH,uBAAuB,GAAGpB,GAAG,IAAIA,GAAG,CAACoB,uBAAuB;IAChE5G,oBAAoB,CAAC,IAAI,CAAC;IAC1B;IACA;IACAhB,IAAI,CAACqH,SAAS,EAAE,UAAU1C,eAAe,EAAEC,QAAQ,EAAE;MACnD,IAAID,eAAe,IAAI,IAAI,EAAE;QAC3B;MACF;MACA,IAAI,CAAC7D,cAAc,CAAC+D,QAAQ,CAACD,QAAQ,CAAC,EAAE;QACtC;QACAF,MAAM,CAACE,QAAQ,CAAC,GAAGF,MAAM,CAACE,QAAQ,CAAC,IAAI,IAAI,GAAGrE,KAAK,CAACoE,eAAe,CAAC,GAAGnE,KAAK,CAACkE,MAAM,CAACE,QAAQ,CAAC,EAAED,eAAe,EAAE,IAAI,CAAC;MACvH,CAAC,MAAM,IAAIC,QAAQ,EAAE;QACnB8C,YAAY,CAACG,IAAI,CAACjD,QAAQ,CAAC;QAC3B+C,cAAc,CAACG,GAAG,CAAClD,QAAQ,EAAE,IAAI,CAAC;MACpC;IACF,CAAC,CAAC;IACF,IAAIgD,uBAAuB,EAAE;MAC3B;MACA;MACA;MACA;MACAA,uBAAuB,CAAC5H,IAAI,CAAC,UAAU+H,GAAG,EAAEC,sBAAsB,EAAE;QAClE,IAAIlH,cAAc,CAAC+D,QAAQ,CAACmD,sBAAsB,CAAC,IAAI,CAACL,cAAc,CAACM,GAAG,CAACD,sBAAsB,CAAC,EAAE;UAClGN,YAAY,CAACG,IAAI,CAACG,sBAAsB,CAAC;UACzCL,cAAc,CAACG,GAAG,CAACE,sBAAsB,EAAE,IAAI,CAAC;QAClD;MACF,CAAC,CAAC;IACJ;IACAlH,cAAc,CAACoH,iBAAiB,CAACR,YAAY,EAAE5G,cAAc,CAACqH,oBAAoB,CAAC,CAAC,EAAEC,cAAc,EAAE,IAAI,CAAC;IAC3G,SAASA,cAAcA,CAACxD,QAAQ,EAAE;MAChC,IAAIyD,iBAAiB,GAAGpH,qBAAqB,CAAC,IAAI,EAAE2D,QAAQ,EAAEhE,SAAS,CAAC0H,gBAAgB,CAACjB,SAAS,CAACzC,QAAQ,CAAC,CAAC,CAAC;MAC9G,IAAI2D,WAAW,GAAGjB,aAAa,CAACW,GAAG,CAACrD,QAAQ,CAAC;MAC7C,IAAI4D,SAAS;MACb;MACA,CAACD,WAAW,GAAG,YAAY,GAAGX,uBAAuB,IAAIA,uBAAuB,CAACK,GAAG,CAACrD,QAAQ,CAAC,GAAG,cAAc,GAAG,aAAa;MAC/H,IAAI6D,aAAa,GAAG7H,SAAS,CAAC8H,eAAe,CAACH,WAAW,EAAEF,iBAAiB,EAAEG,SAAS,CAAC;MACxF;MACA5H,SAAS,CAAC+H,yBAAyB,CAACF,aAAa,EAAE7D,QAAQ,EAAE9D,cAAc,CAAC;MAC5E;MACA;MACA;MACA4D,MAAM,CAACE,QAAQ,CAAC,GAAG,IAAI;MACvB0C,aAAa,CAACQ,GAAG,CAAClD,QAAQ,EAAE,IAAI,CAAC;MACjC4C,eAAe,CAACM,GAAG,CAAClD,QAAQ,EAAE,CAAC,CAAC;MAChC,IAAIgE,iBAAiB,GAAG,EAAE;MAC1B,IAAIC,eAAe,GAAG,EAAE;MACxB,IAAIC,oBAAoB,GAAG,CAAC;MAC5B,IAAIC,aAAa;MACjB,IAAIC,oBAAoB;MACxBhJ,IAAI,CAACyI,aAAa,EAAE,UAAUQ,UAAU,EAAEC,KAAK,EAAE;QAC/C,IAAIC,cAAc,GAAGF,UAAU,CAACG,QAAQ;QACxC,IAAIC,aAAa,GAAGJ,UAAU,CAAC5B,SAAS;QACxC,IAAI,CAACgC,aAAa,EAAE;UAClB,IAAIF,cAAc,EAAE;YAClB;YACA;YACA;YACAA,cAAc,CAAC/B,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;YACpC+B,cAAc,CAACG,aAAa,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;UACzC;UACA;UACA;UACA;QACF,CAAC,MAAM;UACL,IAAIC,YAAY,GAAG3E,QAAQ,KAAK,QAAQ;UACxC,IAAI4E,mBAAmB,GAAG1I,cAAc,CAAC2I,QAAQ,CAAC7E,QAAQ,EAAEqE,UAAU,CAACS,OAAO,CAACC,OAAO,EAAE,CAACJ,YAAY,CAAC;UACtG,CAAC;UACD,IAAI,CAACC,mBAAmB,EAAE;YACxB,IAAIxD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;cACzC,IAAIyD,OAAO,GAAGV,UAAU,CAACS,OAAO,CAACC,OAAO;cACxC,IAAIC,gBAAgB,GAAG1G,kBAAkB,CAACyG,OAAO,CAAC;cAClD,IAAI,CAACnF,0BAA0B,CAACmF,OAAO,CAAC,EAAE;gBACxCnF,0BAA0B,CAACmF,OAAO,CAAC,GAAG,IAAI;gBAC1C,IAAIC,gBAAgB,EAAE;kBACpBzI,KAAK,CAAC,SAAS,GAAGwI,OAAO,GAAG,uCAAuC,GAAGC,gBAAgB,GAAG,0CAA0C,GAAGA,gBAAgB,GAAG,KAAK,CAAC;gBACjK,CAAC,MAAM;kBACLzI,KAAK,CAAC,iBAAiB,GAAGwI,OAAO,CAAC;gBACpC;cACF;YACF;YACA;UACF;UACA;UACA,IAAI/E,QAAQ,KAAK,SAAS,EAAE;YAC1B,IAAImE,aAAa,EAAE;cACjB,IAAI/C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;gBACzC,IAAI,CAAC8C,oBAAoB,EAAE;kBACzB5H,IAAI,CAAC,kDAAkD,CAAC;kBACxD4H,oBAAoB,GAAG,IAAI;gBAC7B;cACF;cACA;YACF;YACAD,aAAa,GAAG,IAAI;UACtB;UACA,IAAII,cAAc,IAAIA,cAAc,CAACU,WAAW,KAAKL,mBAAmB,EAAE;YACxEL,cAAc,CAACW,IAAI,GAAGb,UAAU,CAACS,OAAO,CAACI,IAAI;YAC7C;YACAX,cAAc,CAAC/B,WAAW,CAACiC,aAAa,EAAE,IAAI,CAAC;YAC/CF,cAAc,CAACG,aAAa,CAACD,aAAa,EAAE,KAAK,CAAC;UACpD,CAAC,MAAM;YACL;YACA,IAAIU,QAAQ,GAAGtJ,MAAM,CAAC;cACpBuJ,cAAc,EAAEd;YAClB,CAAC,EAAED,UAAU,CAACS,OAAO,CAAC;YACtBP,cAAc,GAAG,IAAIK,mBAAmB,CAACH,aAAa,EAAE,IAAI,EAAE,IAAI,EAAEU,QAAQ,CAAC;YAC7E;YACAtJ,MAAM,CAAC0I,cAAc,EAAEY,QAAQ,CAAC;YAChC,IAAId,UAAU,CAACgB,QAAQ,EAAE;cACvBd,cAAc,CAACe,gBAAgB,GAAG,IAAI;YACxC;YACAf,cAAc,CAAC/D,IAAI,CAACiE,aAAa,EAAE,IAAI,EAAE,IAAI,CAAC;YAC9C;YACA;YACA;YACA;YACAF,cAAc,CAACG,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC;UAC1C;QACF;QACA,IAAIH,cAAc,EAAE;UAClBP,iBAAiB,CAACf,IAAI,CAACsB,cAAc,CAACzE,MAAM,CAAC;UAC7CmE,eAAe,CAAChB,IAAI,CAACsB,cAAc,CAAC;UACpCL,oBAAoB,EAAE;QACxB,CAAC,MAAM;UACL;UACAF,iBAAiB,CAACf,IAAI,CAAC,KAAK,CAAC,CAAC;UAC9BgB,eAAe,CAAChB,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B;MACF,CAAC,EAAE,IAAI,CAAC;MACRnD,MAAM,CAACE,QAAQ,CAAC,GAAGgE,iBAAiB;MACpCtB,aAAa,CAACQ,GAAG,CAAClD,QAAQ,EAAEiE,eAAe,CAAC;MAC5CrB,eAAe,CAACM,GAAG,CAAClD,QAAQ,EAAEkE,oBAAoB,CAAC;MACnD;MACA,IAAIlE,QAAQ,KAAK,QAAQ,EAAE;QACzBvD,qBAAqB,CAAC,IAAI,CAAC;MAC7B;IACF;IACA;IACA,IAAI,CAAC,IAAI,CAAC8I,cAAc,EAAE;MACxB9I,qBAAqB,CAAC,IAAI,CAAC;IAC7B;EACF,CAAC;EACD;AACF;AACA;EACE0D,WAAW,CAACI,SAAS,CAACiF,SAAS,GAAG,YAAY;IAC5C,IAAI1F,MAAM,GAAGnE,KAAK,CAAC,IAAI,CAACmE,MAAM,CAAC;IAC/B1E,IAAI,CAAC0E,MAAM,EAAE,UAAU2F,aAAa,EAAEzF,QAAQ,EAAE;MAC9C,IAAI9D,cAAc,CAAC+D,QAAQ,CAACD,QAAQ,CAAC,EAAE;QACrC,IAAIkB,IAAI,GAAGlF,SAAS,CAAC0H,gBAAgB,CAAC+B,aAAa,CAAC;QACpD;QACA;QACA;QACA,IAAIC,OAAO,GAAGxE,IAAI,CAACoB,MAAM;QACzB,IAAIqD,WAAW,GAAG,KAAK;QACvB,KAAK,IAAIC,CAAC,GAAGF,OAAO,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UACrC;UACA,IAAI1E,IAAI,CAAC0E,CAAC,CAAC,IAAI,CAAC5J,SAAS,CAAC6J,qBAAqB,CAAC3E,IAAI,CAAC0E,CAAC,CAAC,CAAC,EAAE;YACxDD,WAAW,GAAG,IAAI;UACpB,CAAC,MAAM;YACLzE,IAAI,CAAC0E,CAAC,CAAC,GAAG,IAAI;YACd,CAACD,WAAW,IAAID,OAAO,EAAE;UAC3B;QACF;QACAxE,IAAI,CAACoB,MAAM,GAAGoD,OAAO;QACrB5F,MAAM,CAACE,QAAQ,CAAC,GAAGkB,IAAI;MACzB;IACF,CAAC,CAAC;IACF,OAAOpB,MAAM,CAAClD,gBAAgB,CAAC;IAC/B,OAAOkD,MAAM;EACf,CAAC;EACDK,WAAW,CAACI,SAAS,CAACuF,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAAChF,MAAM;EACpB,CAAC;EACDX,WAAW,CAACI,SAAS,CAACwF,cAAc,GAAG,YAAY;IACjD,OAAO,IAAI,CAAChF,OAAO;EACrB,CAAC;EACDZ,WAAW,CAACI,SAAS,CAACyF,gBAAgB,GAAG,UAAUC,OAAO,EAAE;IAC1D,IAAI,CAACC,QAAQ,GAAGD,OAAO;EACzB,CAAC;EACD9F,WAAW,CAACI,SAAS,CAAC4F,gBAAgB,GAAG,YAAY;IACnD,OAAO,IAAI,CAACD,QAAQ;EACtB,CAAC;EACD;AACF;AACA;EACE/F,WAAW,CAACI,SAAS,CAAC6F,YAAY,GAAG,UAAUpG,QAAQ,EAAEqG,GAAG,EAAE;IAC5D,IAAIC,IAAI,GAAG,IAAI,CAAC3D,cAAc,CAACU,GAAG,CAACrD,QAAQ,CAAC;IAC5C,IAAIsG,IAAI,EAAE;MACR,IAAIC,IAAI,GAAGD,IAAI,CAACD,GAAG,IAAI,CAAC,CAAC;MACzB,IAAIE,IAAI,EAAE;QACR,OAAOA,IAAI;MACb,CAAC,MAAM,IAAIF,GAAG,IAAI,IAAI,EAAE;QACtB,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,IAAI,CAAChE,MAAM,EAAEsD,CAAC,EAAE,EAAE;UACpC,IAAIU,IAAI,CAACV,CAAC,CAAC,EAAE;YACX,OAAOU,IAAI,CAACV,CAAC,CAAC;UAChB;QACF;MACF;IACF;EACF,CAAC;EACD;AACF;AACA;EACEzF,WAAW,CAACI,SAAS,CAACiG,eAAe,GAAG,UAAUC,SAAS,EAAE;IAC3D,IAAIzG,QAAQ,GAAGyG,SAAS,CAACzG,QAAQ;IACjC,IAAI,CAACA,QAAQ,EAAE;MACb,OAAO,EAAE;IACX;IACA,IAAIsE,KAAK,GAAGmC,SAAS,CAACnC,KAAK;IAC3B,IAAIoC,EAAE,GAAGD,SAAS,CAACC,EAAE;IACrB,IAAIxB,IAAI,GAAGuB,SAAS,CAACvB,IAAI;IACzB,IAAIyB,KAAK,GAAG,IAAI,CAAChE,cAAc,CAACU,GAAG,CAACrD,QAAQ,CAAC;IAC7C,IAAI,CAAC2G,KAAK,IAAI,CAACA,KAAK,CAACrE,MAAM,EAAE;MAC3B,OAAO,EAAE;IACX;IACA,IAAIsE,MAAM;IACV,IAAItC,KAAK,IAAI,IAAI,EAAE;MACjBsC,MAAM,GAAG,EAAE;MACXxL,IAAI,CAACY,SAAS,CAAC0H,gBAAgB,CAACY,KAAK,CAAC,EAAE,UAAU+B,GAAG,EAAE;QACrDM,KAAK,CAACN,GAAG,CAAC,IAAIO,MAAM,CAAC3D,IAAI,CAAC0D,KAAK,CAACN,GAAG,CAAC,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIK,EAAE,IAAI,IAAI,EAAE;MACrBE,MAAM,GAAGC,eAAe,CAAC,IAAI,EAAEH,EAAE,EAAEC,KAAK,CAAC;IAC3C,CAAC,MAAM,IAAIzB,IAAI,IAAI,IAAI,EAAE;MACvB0B,MAAM,GAAGC,eAAe,CAAC,MAAM,EAAE3B,IAAI,EAAEyB,KAAK,CAAC;IAC/C,CAAC,MAAM;MACL;MACAC,MAAM,GAAGvL,MAAM,CAACsL,KAAK,EAAE,UAAUJ,IAAI,EAAE;QACrC,OAAO,CAAC,CAACA,IAAI;MACf,CAAC,CAAC;IACJ;IACA,OAAOO,eAAe,CAACF,MAAM,EAAEH,SAAS,CAAC;EAC3C,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtG,WAAW,CAACI,SAAS,CAACwG,cAAc,GAAG,UAAUN,SAAS,EAAE;IAC1D,IAAIO,KAAK,GAAGP,SAAS,CAACO,KAAK;IAC3B,IAAIhH,QAAQ,GAAGyG,SAAS,CAACzG,QAAQ;IACjC,IAAIiH,SAAS,GAAGC,YAAY,CAACF,KAAK,CAAC;IACnC,IAAIJ,MAAM,GAAGK,SAAS,GAAG,IAAI,CAACT,eAAe,CAACS,SAAS;IACvD;IAAA,EACE5L,MAAM,CAAC,IAAI,CAACsH,cAAc,CAACU,GAAG,CAACrD,QAAQ,CAAC,EAAE,UAAUuG,IAAI,EAAE;MAC1D,OAAO,CAAC,CAACA,IAAI;IACf,CAAC,CAAC;IACF,OAAOY,QAAQ,CAACL,eAAe,CAACF,MAAM,EAAEH,SAAS,CAAC,CAAC;IACnD,SAASS,YAAYA,CAACE,CAAC,EAAE;MACvB,IAAIC,SAAS,GAAGrH,QAAQ,GAAG,OAAO;MAClC,IAAIsH,MAAM,GAAGtH,QAAQ,GAAG,IAAI;MAC5B,IAAIuH,QAAQ,GAAGvH,QAAQ,GAAG,MAAM;MAChC,OAAOoH,CAAC,KAAKA,CAAC,CAACC,SAAS,CAAC,IAAI,IAAI,IAAID,CAAC,CAACE,MAAM,CAAC,IAAI,IAAI,IAAIF,CAAC,CAACG,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG;QAC/EvH,QAAQ,EAAEA,QAAQ;QAClB;QACAsE,KAAK,EAAE8C,CAAC,CAACC,SAAS,CAAC;QACnBX,EAAE,EAAEU,CAAC,CAACE,MAAM,CAAC;QACbpC,IAAI,EAAEkC,CAAC,CAACG,QAAQ;MAClB,CAAC,GAAG,IAAI;IACV;IACA,SAASJ,QAAQA,CAACK,GAAG,EAAE;MACrB,OAAOf,SAAS,CAACpL,MAAM,GAAGA,MAAM,CAACmM,GAAG,EAAEf,SAAS,CAACpL,MAAM,CAAC,GAAGmM,GAAG;IAC/D;EACF,CAAC;EACDrH,WAAW,CAACI,SAAS,CAACkH,aAAa,GAAG,UAAUzH,QAAQ,EAAE0H,EAAE,EAAEC,OAAO,EAAE;IACrE,IAAIjF,aAAa,GAAG,IAAI,CAACC,cAAc;IACvC,IAAI5G,UAAU,CAACiE,QAAQ,CAAC,EAAE;MACxB,IAAI4H,WAAW,GAAGF,EAAE;MACpB,IAAIG,UAAU,GAAG7H,QAAQ;MACzB0C,aAAa,CAACtH,IAAI,CAAC,UAAUuL,KAAK,EAAEmB,aAAa,EAAE;QACjD,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEe,KAAK,IAAIf,CAAC,GAAGe,KAAK,CAACrE,MAAM,EAAEsD,CAAC,EAAE,EAAE;UAC9C,IAAIW,IAAI,GAAGI,KAAK,CAACf,CAAC,CAAC;UACnBW,IAAI,IAAIsB,UAAU,CAACE,IAAI,CAACH,WAAW,EAAEE,aAAa,EAAEvB,IAAI,EAAEA,IAAI,CAACnB,cAAc,CAAC;QAChF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIuB,KAAK,GAAGnL,QAAQ,CAACwE,QAAQ,CAAC,GAAG0C,aAAa,CAACW,GAAG,CAACrD,QAAQ,CAAC,GAAGzE,QAAQ,CAACyE,QAAQ,CAAC,GAAG,IAAI,CAAC+G,cAAc,CAAC/G,QAAQ,CAAC,GAAG,IAAI;MACxH,KAAK,IAAI4F,CAAC,GAAG,CAAC,EAAEe,KAAK,IAAIf,CAAC,GAAGe,KAAK,CAACrE,MAAM,EAAEsD,CAAC,EAAE,EAAE;QAC9C,IAAIW,IAAI,GAAGI,KAAK,CAACf,CAAC,CAAC;QACnBW,IAAI,IAAImB,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEpB,IAAI,EAAEA,IAAI,CAACnB,cAAc,CAAC;MACrD;IACF;EACF,CAAC;EACD;AACF;AACA;EACEjF,WAAW,CAACI,SAAS,CAACyH,eAAe,GAAG,UAAU9C,IAAI,EAAE;IACtD,IAAI+C,OAAO,GAAGjM,SAAS,CAACkM,mBAAmB,CAAChD,IAAI,EAAE,IAAI,CAAC;IACvD,OAAO7J,MAAM,CAAC,IAAI,CAACsH,cAAc,CAACU,GAAG,CAAC,QAAQ,CAAC,EAAE,UAAU8E,SAAS,EAAE;MACpE,OAAO,CAAC,CAACA,SAAS,IAAIF,OAAO,IAAI,IAAI,IAAIE,SAAS,CAACjD,IAAI,KAAK+C,OAAO;IACrE,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACE9H,WAAW,CAACI,SAAS,CAAC6H,gBAAgB,GAAG,UAAUC,WAAW,EAAE;IAC9D,OAAO,IAAI,CAAC1F,cAAc,CAACU,GAAG,CAAC,QAAQ,CAAC,CAACgF,WAAW,CAAC;EACvD,CAAC;EACD;AACF;AACA;AACA;EACElI,WAAW,CAACI,SAAS,CAAC+H,eAAe,GAAG,UAAUvD,OAAO,EAAE;IACzD,OAAO1J,MAAM,CAAC,IAAI,CAACsH,cAAc,CAACU,GAAG,CAAC,QAAQ,CAAC,EAAE,UAAU8E,SAAS,EAAE;MACpE,OAAO,CAAC,CAACA,SAAS,IAAIA,SAAS,CAACpD,OAAO,KAAKA,OAAO;IACrD,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACE5E,WAAW,CAACI,SAAS,CAACgI,SAAS,GAAG,YAAY;IAC5C,OAAOlN,MAAM,CAAC,IAAI,CAACsH,cAAc,CAACU,GAAG,CAAC,QAAQ,CAAC,EAAE,UAAU8E,SAAS,EAAE;MACpE,OAAO,CAAC,CAACA,SAAS;IACpB,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACEhI,WAAW,CAACI,SAAS,CAACiI,cAAc,GAAG,YAAY;IACjD,OAAO,IAAI,CAAC3F,gBAAgB,CAACQ,GAAG,CAAC,QAAQ,CAAC;EAC5C,CAAC;EACD;AACF;AACA;AACA;EACElD,WAAW,CAACI,SAAS,CAACkI,UAAU,GAAG,UAAUf,EAAE,EAAEC,OAAO,EAAE;IACxDjL,uBAAuB,CAAC,IAAI,CAAC;IAC7BtB,IAAI,CAAC,IAAI,CAACmK,cAAc,EAAE,UAAUmD,cAAc,EAAE;MAClD,IAAIC,MAAM,GAAG,IAAI,CAAChG,cAAc,CAACU,GAAG,CAAC,QAAQ,CAAC,CAACqF,cAAc,CAAC;MAC9DhB,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEgB,MAAM,EAAED,cAAc,CAAC;IAC1C,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEvI,WAAW,CAACI,SAAS,CAACqI,aAAa,GAAG,UAAUlB,EAAE,EAAEC,OAAO,EAAE;IAC3DvM,IAAI,CAAC,IAAI,CAACuH,cAAc,CAACU,GAAG,CAAC,QAAQ,CAAC,EAAE,UAAUsF,MAAM,EAAE;MACxDA,MAAM,IAAIjB,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEgB,MAAM,EAAEA,MAAM,CAACvD,cAAc,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;EACEjF,WAAW,CAACI,SAAS,CAACsI,gBAAgB,GAAG,UAAU9D,OAAO,EAAE2C,EAAE,EAAEC,OAAO,EAAE;IACvEjL,uBAAuB,CAAC,IAAI,CAAC;IAC7BtB,IAAI,CAAC,IAAI,CAACmK,cAAc,EAAE,UAAUmD,cAAc,EAAE;MAClD,IAAIC,MAAM,GAAG,IAAI,CAAChG,cAAc,CAACU,GAAG,CAAC,QAAQ,CAAC,CAACqF,cAAc,CAAC;MAC9D,IAAIC,MAAM,CAAC5D,OAAO,KAAKA,OAAO,EAAE;QAC9B2C,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEgB,MAAM,EAAED,cAAc,CAAC;MAC1C;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;AACF;AACA;EACEvI,WAAW,CAACI,SAAS,CAACuI,mBAAmB,GAAG,UAAU/D,OAAO,EAAE2C,EAAE,EAAEC,OAAO,EAAE;IAC1E,OAAOvM,IAAI,CAAC,IAAI,CAACkN,eAAe,CAACvD,OAAO,CAAC,EAAE2C,EAAE,EAAEC,OAAO,CAAC;EACzD,CAAC;EACDxH,WAAW,CAACI,SAAS,CAACwI,gBAAgB,GAAG,UAAUC,WAAW,EAAE;IAC9DtM,uBAAuB,CAAC,IAAI,CAAC;IAC7B,OAAO,IAAI,CAACuM,iBAAiB,CAAC5F,GAAG,CAAC2F,WAAW,CAAC5D,cAAc,CAAC,IAAI,IAAI;EACvE,CAAC;EACDjF,WAAW,CAACI,SAAS,CAAC2I,uBAAuB,GAAG,YAAY;IAC1D,OAAO,CAAC,IAAI,CAAC3D,cAAc,IAAI,EAAE,EAAE4D,KAAK,CAAC,CAAC;EAC5C,CAAC;EACDhJ,WAAW,CAACI,SAAS,CAAC6I,YAAY,GAAG,UAAU1B,EAAE,EAAEC,OAAO,EAAE;IAC1DjL,uBAAuB,CAAC,IAAI,CAAC;IAC7B,IAAI2M,gBAAgB,GAAG,EAAE;IACzBjO,IAAI,CAAC,IAAI,CAACmK,cAAc,EAAE,UAAU+D,YAAY,EAAE;MAChD,IAAIX,MAAM,GAAG,IAAI,CAAChG,cAAc,CAACU,GAAG,CAAC,QAAQ,CAAC,CAACiG,YAAY,CAAC;MAC5D5B,EAAE,CAACK,IAAI,CAACJ,OAAO,EAAEgB,MAAM,EAAEW,YAAY,CAAC,IAAID,gBAAgB,CAACpG,IAAI,CAACqG,YAAY,CAAC;IAC/E,CAAC,EAAE,IAAI,CAAC;IACR,IAAI,CAAC/D,cAAc,GAAG8D,gBAAgB;IACtC,IAAI,CAACJ,iBAAiB,GAAGxN,aAAa,CAAC4N,gBAAgB,CAAC;EAC1D,CAAC;EACDlJ,WAAW,CAACI,SAAS,CAACyB,WAAW,GAAG,UAAUiE,OAAO,EAAE;IACrDxJ,qBAAqB,CAAC,IAAI,CAAC;IAC3B,IAAIiG,aAAa,GAAG,IAAI,CAACC,cAAc;IACvC,IAAI4G,cAAc,GAAG,EAAE;IACvB7G,aAAa,CAACtH,IAAI,CAAC,UAAUoO,UAAU,EAAE1B,aAAa,EAAE;MACtD,IAAI5L,cAAc,CAAC+D,QAAQ,CAAC6H,aAAa,CAAC,EAAE;QAC1CyB,cAAc,CAACtG,IAAI,CAAC6E,aAAa,CAAC;MACpC;IACF,CAAC,CAAC;IACF5L,cAAc,CAACoH,iBAAiB,CAACiG,cAAc,EAAErN,cAAc,CAACqH,oBAAoB,CAAC,CAAC,EAAE,UAAUuE,aAAa,EAAE;MAC/G1M,IAAI,CAACsH,aAAa,CAACW,GAAG,CAACyE,aAAa,CAAC,EAAE,UAAU2B,SAAS,EAAE;QAC1D,IAAIA,SAAS,KAAK3B,aAAa,KAAK,QAAQ,IAAI,CAAC4B,iBAAiB,CAACD,SAAS,EAAExD,OAAO,CAAC,CAAC,EAAE;UACvFwD,SAAS,CAACzH,WAAW,CAAC,CAAC;QACzB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD7B,WAAW,CAACwJ,aAAa,GAAG,YAAY;IACtClN,qBAAqB,GAAG,SAAAA,CAAUiE,OAAO,EAAE;MACzC,IAAIkJ,aAAa,GAAGlJ,OAAO,CAAC6E,cAAc,GAAG,EAAE;MAC/CnK,IAAI,CAACsF,OAAO,CAACiC,cAAc,CAACU,GAAG,CAAC,QAAQ,CAAC,EAAE,UAAUsF,MAAM,EAAE;QAC3D;QACAA,MAAM,IAAIiB,aAAa,CAAC3G,IAAI,CAAC0F,MAAM,CAACvD,cAAc,CAAC;MACrD,CAAC,CAAC;MACF1E,OAAO,CAACuI,iBAAiB,GAAGxN,aAAa,CAACmO,aAAa,CAAC;IAC1D,CAAC;IACDlN,uBAAuB,GAAG,SAAAA,CAAUgE,OAAO,EAAE;MAC3C;MACA;MACA,IAAIU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAI,CAACZ,OAAO,CAAC6E,cAAc,EAAE;UAC3B,MAAM,IAAIsE,KAAK,CAAC,gCAAgC,CAAC;QACnD;MACF;IACF,CAAC;IACDlN,QAAQ,GAAG,SAAAA,CAAU+D,OAAO,EAAEoB,UAAU,EAAE;MACxC;MACA;MACApB,OAAO,CAACZ,MAAM,GAAG,CAAC,CAAC;MACnBY,OAAO,CAACZ,MAAM,CAAClD,gBAAgB,CAAC,GAAGC,kBAAkB;MACrD;MACA;MACA6D,OAAO,CAACiC,cAAc,GAAGlH,aAAa,CAAC;QACrCkN,MAAM,EAAE;MACV,CAAC,CAAC;MACFjI,OAAO,CAACmC,gBAAgB,GAAGpH,aAAa,CAAC,CAAC;MAC1C;MACA;MACA,IAAIqO,UAAU,GAAGhI,UAAU,CAACiI,IAAI;MAChC,IAAIxO,QAAQ,CAACuO,UAAU,CAAC,IAAIA,UAAU,CAACE,OAAO,IAAI,IAAI,EAAE;QACtDF,UAAU,CAACE,OAAO,GAAG,IAAI;MAC3B;MACAC,UAAU,CAACnI,UAAU,EAAEpB,OAAO,CAACI,MAAM,CAAChB,MAAM,CAAC;MAC7C;MACAlE,KAAK,CAACkG,UAAU,EAAE3F,aAAa,EAAE,KAAK,CAAC;MACvCuE,OAAO,CAACuB,YAAY,CAACH,UAAU,EAAE,IAAI,CAAC;IACxC,CAAC;EACH,CAAC,CAAC,CAAC;EACH,OAAO3B,WAAW;AACpB,CAAC,CAAClE,KAAK,CAAC;AACR,SAASyN,iBAAiBA,CAACV,WAAW,EAAE/C,OAAO,EAAE;EAC/C,IAAIA,OAAO,EAAE;IACX,IAAI3B,KAAK,GAAG2B,OAAO,CAACoC,WAAW;IAC/B,IAAI3B,EAAE,GAAGT,OAAO,CAACiE,QAAQ;IACzB,IAAIC,MAAM,GAAGlE,OAAO,CAACmE,UAAU;IAC/B,OAAO9F,KAAK,IAAI,IAAI,IAAI0E,WAAW,CAAC5D,cAAc,KAAKd,KAAK,IAAIoC,EAAE,IAAI,IAAI,IAAIsC,WAAW,CAACtC,EAAE,KAAKA,EAAE,IAAIyD,MAAM,IAAI,IAAI,IAAInB,WAAW,CAAC9D,IAAI,KAAKiF,MAAM;EACtJ;AACF;AACA,SAASF,UAAUA,CAACnK,MAAM,EAAEa,KAAK,EAAE;EACjC;EACA;EACA,IAAI0J,kBAAkB,GAAGvK,MAAM,CAACwK,KAAK,IAAI,CAACxK,MAAM,CAACyK,UAAU;EAC3DnP,IAAI,CAACuF,KAAK,EAAE,UAAU6J,SAAS,EAAEtF,IAAI,EAAE;IACrC,IAAIA,IAAI,KAAK,YAAY,IAAImF,kBAAkB,EAAE;MAC/C;IACF;IACA;IACA;IACA,IAAI,CAACnO,cAAc,CAAC+D,QAAQ,CAACiF,IAAI,CAAC,EAAE;MAClC,IAAI,OAAOsF,SAAS,KAAK,QAAQ,EAAE;QACjC1K,MAAM,CAACoF,IAAI,CAAC,GAAG,CAACpF,MAAM,CAACoF,IAAI,CAAC,GAAGvJ,KAAK,CAAC6O,SAAS,CAAC,GAAG5O,KAAK,CAACkE,MAAM,CAACoF,IAAI,CAAC,EAAEsF,SAAS,EAAE,KAAK,CAAC;MACzF,CAAC,MAAM;QACL,IAAI1K,MAAM,CAACoF,IAAI,CAAC,IAAI,IAAI,EAAE;UACxBpF,MAAM,CAACoF,IAAI,CAAC,GAAGsF,SAAS;QAC1B;MACF;IACF;EACF,CAAC,CAAC;AACJ;AACA,SAAS3D,eAAeA,CAAC4D,IAAI,EAAEC,QAAQ,EAAE/D,KAAK,EAAE;EAC9C;EACA;EACA,IAAIrL,OAAO,CAACoP,QAAQ,CAAC,EAAE;IACrB,IAAIC,QAAQ,GAAGlP,aAAa,CAAC,CAAC;IAC9BL,IAAI,CAACsP,QAAQ,EAAE,UAAUE,YAAY,EAAE;MACrC,IAAIA,YAAY,IAAI,IAAI,EAAE;QACxB,IAAIC,MAAM,GAAG7O,SAAS,CAACkM,mBAAmB,CAAC0C,YAAY,EAAE,IAAI,CAAC;QAC9DC,MAAM,IAAI,IAAI,IAAIF,QAAQ,CAACzH,GAAG,CAAC0H,YAAY,EAAE,IAAI,CAAC;MACpD;IACF,CAAC,CAAC;IACF,OAAOvP,MAAM,CAACsL,KAAK,EAAE,UAAUJ,IAAI,EAAE;MACnC,OAAOA,IAAI,IAAIoE,QAAQ,CAACtH,GAAG,CAACkD,IAAI,CAACkE,IAAI,CAAC,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAIK,QAAQ,GAAG9O,SAAS,CAACkM,mBAAmB,CAACwC,QAAQ,EAAE,IAAI,CAAC;IAC5D,OAAOrP,MAAM,CAACsL,KAAK,EAAE,UAAUJ,IAAI,EAAE;MACnC,OAAOA,IAAI,IAAIuE,QAAQ,IAAI,IAAI,IAAIvE,IAAI,CAACkE,IAAI,CAAC,KAAKK,QAAQ;IAC5D,CAAC,CAAC;EACJ;AACF;AACA,SAAShE,eAAeA,CAAC0C,UAAU,EAAE/C,SAAS,EAAE;EAC9C;EACA;EACA,OAAOA,SAAS,CAACsE,cAAc,CAAC,SAAS,CAAC,GAAG1P,MAAM,CAACmO,UAAU,EAAE,UAAUjD,IAAI,EAAE;IAC9E,OAAOA,IAAI,IAAIA,IAAI,CAACxB,OAAO,KAAK0B,SAAS,CAAC1B,OAAO;EACnD,CAAC,CAAC,GAAGyE,UAAU;AACjB;AACA,SAAShI,uBAAuBA,CAACN,IAAI,EAAE;EACrC,IAAI8B,uBAAuB,GAAGvH,aAAa,CAAC,CAAC;EAC7CyF,IAAI,IAAI9F,IAAI,CAACY,SAAS,CAAC0H,gBAAgB,CAACxC,IAAI,CAAC8J,YAAY,CAAC,EAAE,UAAUhL,QAAQ,EAAE;IAC9E,IAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC5F,MAAM,CAACQ,cAAc,CAAC+D,QAAQ,CAACD,QAAQ,CAAC,EAAE,GAAG,GAAGA,QAAQ,GAAG,sDAAsD,CAAC;IACpH;IACAgD,uBAAuB,CAACE,GAAG,CAAClD,QAAQ,EAAE,IAAI,CAAC;EAC7C,CAAC,CAAC;EACF,OAAO;IACLgD,uBAAuB,EAAEA;EAC3B,CAAC;AACH;AACAlH,KAAK,CAACqE,WAAW,EAAE7D,YAAY,CAAC;AAChC,eAAe6D,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}