{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createLegacyDataSelectAction } from '../../legacy/dataSelectAction.js';\nimport pieLayout from '../pie/pieLayout.js';\nimport dataFilter from '../../processor/dataFilter.js';\nimport { curry } from 'zrender/lib/core/util.js';\nimport PieView from './PieView.js';\nimport PieSeriesModel from './PieSeries.js';\nimport negativeDataFilter from '../../processor/negativeDataFilter.js';\nexport function install(registers) {\n  registers.registerChartView(PieView);\n  registers.registerSeriesModel(PieSeriesModel);\n  createLegacyDataSelectAction('pie', registers.registerAction);\n  registers.registerLayout(curry(pieLayout, 'pie'));\n  registers.registerProcessor(dataFilter('pie'));\n  registers.registerProcessor(negativeDataFilter('pie'));\n}", "map": {"version": 3, "names": ["createLegacyDataSelectAction", "pieLayout", "dataFilter", "curry", "<PERSON><PERSON>ie<PERSON>", "PieSeriesModel", "negativeDataFilter", "install", "registers", "registerChartView", "registerSeriesModel", "registerAction", "registerLayout", "registerProcessor"], "sources": ["E:/AllProject/datafront/node_modules/echarts/lib/chart/pie/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createLegacyDataSelectAction } from '../../legacy/dataSelectAction.js';\nimport pieLayout from '../pie/pieLayout.js';\nimport dataFilter from '../../processor/dataFilter.js';\nimport { curry } from 'zrender/lib/core/util.js';\nimport PieView from './PieView.js';\nimport PieSeriesModel from './PieSeries.js';\nimport negativeDataFilter from '../../processor/negativeDataFilter.js';\nexport function install(registers) {\n  registers.registerChartView(PieView);\n  registers.registerSeriesModel(PieSeriesModel);\n  createLegacyDataSelectAction('pie', registers.registerAction);\n  registers.registerLayout(curry(pieLayout, 'pie'));\n  registers.registerProcessor(dataFilter('pie'));\n  registers.registerProcessor(negativeDataFilter('pie'));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,4BAA4B,QAAQ,kCAAkC;AAC/E,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,UAAU,MAAM,+BAA+B;AACtD,SAASC,KAAK,QAAQ,0BAA0B;AAChD,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,cAAc,MAAM,gBAAgB;AAC3C,OAAOC,kBAAkB,MAAM,uCAAuC;AACtE,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACL,OAAO,CAAC;EACpCI,SAAS,CAACE,mBAAmB,CAACL,cAAc,CAAC;EAC7CL,4BAA4B,CAAC,KAAK,EAAEQ,SAAS,CAACG,cAAc,CAAC;EAC7DH,SAAS,CAACI,cAAc,CAACT,KAAK,CAACF,SAAS,EAAE,KAAK,CAAC,CAAC;EACjDO,SAAS,CAACK,iBAAiB,CAACX,UAAU,CAAC,KAAK,CAAC,CAAC;EAC9CM,SAAS,CAACK,iBAAiB,CAACP,kBAAkB,CAAC,KAAK,CAAC,CAAC;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}