{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n};\nexports.isVNode = isVNode;\nvar _util = require('element-ui/lib/utils/util');\nfunction isVNode(node) {\n  return node !== null && (typeof node === 'undefined' ? 'undefined' : _typeof(node)) === 'object' && (0, _util.hasOwn)(node, 'componentOptions');\n}\n;", "map": {"version": 3, "names": ["exports", "__esModule", "_typeof", "Symbol", "iterator", "obj", "constructor", "prototype", "isVNode", "_util", "require", "node", "hasOwn"], "sources": ["E:/AllProject/datafront/node_modules/element-ui/lib/utils/vdom.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.isVNode = isVNode;\n\nvar _util = require('element-ui/lib/utils/util');\n\nfunction isVNode(node) {\n  return node !== null && (typeof node === 'undefined' ? 'undefined' : _typeof(node)) === 'object' && (0, _util.hasOwn)(node, 'componentOptions');\n};"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAUC,GAAG,EAAE;EAAE,OAAO,OAAOA,GAAG;AAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAI,OAAOF,MAAM,KAAK,UAAU,IAAIE,GAAG,CAACC,WAAW,KAAKH,MAAM,IAAIE,GAAG,KAAKF,MAAM,CAACI,SAAS,GAAG,QAAQ,GAAG,OAAOF,GAAG;AAAE,CAAC;AAE5QL,OAAO,CAACQ,OAAO,GAAGA,OAAO;AAEzB,IAAIC,KAAK,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAEhD,SAASF,OAAOA,CAACG,IAAI,EAAE;EACrB,OAAOA,IAAI,KAAK,IAAI,IAAI,CAAC,OAAOA,IAAI,KAAK,WAAW,GAAG,WAAW,GAAGT,OAAO,CAACS,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,CAAC,EAAEF,KAAK,CAACG,MAAM,EAAED,IAAI,EAAE,kBAAkB,CAAC;AACjJ;AAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}