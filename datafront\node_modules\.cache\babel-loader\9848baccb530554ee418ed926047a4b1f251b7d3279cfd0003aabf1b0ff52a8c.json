{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'el-icon-s-data',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'el-icon-connection',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'el-icon-pie-chart',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'el-icon-s-operation',\n          compatible: true\n        }\n      }\n    };\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler() {\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    renderChart() {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n      console.log('开始渲染图表，配置:', this.chartConfig);\n\n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartConfig);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n\n      // 如果没有数据，才调用API获取\n      getData(this.chartConfig).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch (this.chartConfig.type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n\n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n\n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n\n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        console.log('提取的数据:', extractedData);\n\n        // 根据提取的数据类型调用不同的处理函数\n        switch (extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const {\n        data = [],\n        metrics = []\n      } = chartData;\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const {\n        data = []\n      } = chartData;\n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getData", "echarts", "name", "props", "chartConfig", "type", "Object", "required", "default", "recommendedChartTypes", "Array", "data", "loading", "error", "errorMsg", "chartInstance", "<PERSON><PERSON><PERSON>", "currentChartType", "chartTypeConfig", "icon", "compatible", "watch", "deep", "handler", "<PERSON><PERSON><PERSON>", "mounted", "window", "addEventListener", "resizeChart", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "dispose", "methods", "console", "log", "options", "convertToChartOptions", "<PERSON><PERSON><PERSON>", "message", "then", "response", "code", "msg", "chartData", "catch", "isArray", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "extractedData", "metrics", "fields", "filter", "f", "groupType", "xAxisData", "map", "item", "field", "series", "length", "categoriesSet", "Set", "for<PERSON>ach", "s", "add", "category", "categories", "from", "seriesData", "find", "value", "push", "title", "text", "tooltip", "trigger", "axisPointer", "legend", "xAxis", "yAxis", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "position", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "$refs", "chartRef", "init", "setOption", "resize"], "sources": ["src/components/ChartDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <!-- 图表切换工具栏 -->\n    <div v-if=\"showChartSwitcher\" class=\"chart-switcher\">\n      <div class=\"chart-switcher-title\">图表切换</div>\n      <div class=\"chart-type-buttons\">\n        <button\n          v-for=\"chartType in availableChartTypes\"\n          :key=\"chartType.type\"\n          :class=\"['chart-type-btn', {\n            'active': currentChartType === chartType.type,\n            'disabled': !chartType.compatible\n          }]\"\n          :disabled=\"!chartType.compatible || switchingChart\"\n          @click=\"switchChartType(chartType.type)\"\n          :title=\"chartType.name\"\n        >\n          <i :class=\"chartType.icon\"></i>\n          <span>{{ chartType.name }}</span>\n          <div v-if=\"switchingChart && currentChartType === chartType.type\" class=\"switching-loader\">\n            <i class=\"el-icon-loading\"></i>\n          </div>\n        </button>\n      </div>\n    </div>\n\n    <!-- 图表显示区域 -->\n    <div v-if=\"loading\" class=\"chart-loading\">\n      <i class=\"el-icon-loading\"></i>\n      <span>加载图表中...</span>\n    </div>\n    <div v-else-if=\"error\" class=\"chart-error\">\n      <i class=\"el-icon-warning\"></i>\n      <span>{{ errorMsg }}</span>\n    </div>\n    <div v-else ref=\"chartRef\" class=\"chart-canvas\"></div>\n  </div>\n</template>\n\n<script>\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\n\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'el-icon-s-data',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'el-icon-connection',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'el-icon-pie-chart',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'el-icon-s-operation',\n          compatible: true\n        }\n      }\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler() {\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    renderChart() {\n      this.loading = true;\n      this.error = false;\n      \n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n      \n      console.log('开始渲染图表，配置:', this.chartConfig);\n      \n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n        \n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartConfig);\n          console.log('生成的echarts选项:', options);\n          \n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n      \n      // 如果没有数据，才调用API获取\n      getData(this.chartConfig).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        \n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        \n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        \n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        \n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n          \n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    \n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n      \n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch(this.chartConfig.type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n        \n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n        \n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n        \n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        \n        console.log('提取的数据:', extractedData);\n        \n        // 根据提取的数据类型调用不同的处理函数\n        switch(extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    \n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const { data = [], metrics = [] } = chartData;\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const { data = [] } = chartData;\n      \n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n        \n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n        \n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    \n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.chart-container {\n  width: 100%;\n  height: 350px;\n  position: relative;\n  max-width: 650px;\n  margin: 0 auto;\n  z-index: 1;\n  box-sizing: border-box;\n  isolation: isolate; /* 创建新的层叠上下文 */\n}\n\n.chart-canvas {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  z-index: 1;\n}\n\n.chart-loading, .chart-error {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  color: #909399;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 2;\n  background: rgba(255,255,255,0.9);\n}\n\n.chart-loading i, .chart-error i {\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.chart-error {\n  color: #F56C6C;\n}\n</style> "], "mappings": ";;;;;;;;;;;;;AAwCA,SAAAA,OAAA;AACA,YAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACA;IACAC,qBAAA;MACAJ,IAAA,EAAAK,KAAA;MACAF,OAAA,EAAAA,CAAA;IACA;EACA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;MACAC,aAAA;MACAC,cAAA;MACAC,gBAAA;MACA;MACAC,eAAA;QACA;UACAhB,IAAA;UACAiB,IAAA;UACAC,UAAA;QACA;QACA;UACAlB,IAAA;UACAiB,IAAA;UACAC,UAAA;QACA;QACA;UACAlB,IAAA;UACAiB,IAAA;UACAC,UAAA;QACA;QACA;UACAlB,IAAA;UACAiB,IAAA;UACAC,UAAA;QACA;MACA;IACA;EACA;EACAC,KAAA;IACAjB,WAAA;MACAkB,IAAA;MACAC,QAAA;QACA,KAAAC,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAD,WAAA;IACA;IACAE,MAAA,CAAAC,gBAAA,gBAAAC,WAAA;EACA;EACAC,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,WAAA;IACA;IACA,SAAAb,aAAA;MACA,KAAAA,aAAA,CAAAgB,OAAA;IACA;EACA;EACAC,OAAA;IACAR,YAAA;MACA,KAAAZ,OAAA;MACA,KAAAC,KAAA;;MAEA;MACA,UAAAT,WAAA,UAAAA,WAAA,CAAAC,IAAA;QACA,KAAAO,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA;QACAmB,OAAA,CAAApB,KAAA,iBAAAT,WAAA;QACA;MACA;MAEA6B,OAAA,CAAAC,GAAA,oBAAA9B,WAAA;;MAEA;MACA,SAAAA,WAAA,CAAAO,IAAA;QACAsB,OAAA,CAAAC,GAAA;QACA,KAAAtB,OAAA;QAEA;UACA;UACA,MAAAuB,OAAA,QAAAC,qBAAA,MAAAhC,WAAA;UACA6B,OAAA,CAAAC,GAAA,kBAAAC,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAtB,KAAA;UACAoB,OAAA,CAAApB,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAyB,OAAA;QACA;QACA;MACA;;MAEA;MACAtC,OAAA,MAAAI,WAAA,EAAAmC,IAAA,CAAAC,QAAA;QACAP,OAAA,CAAAC,GAAA,eAAAM,QAAA;QACA,KAAA5B,OAAA;QAEA,KAAA4B,QAAA;UACA,KAAA3B,KAAA;UACA,KAAAC,QAAA;UACAmB,OAAA,CAAApB,KAAA;UACA;QACA;QAEA,IAAA2B,QAAA,CAAAC,IAAA;UACA,KAAA5B,KAAA;UACA,KAAAC,QAAA,GAAA0B,QAAA,CAAAE,GAAA,iBAAAF,QAAA,CAAAC,IAAA;UACAR,OAAA,CAAApB,KAAA,cAAA2B,QAAA;UACA;QACA;QAEA,MAAAG,SAAA,GAAAH,QAAA,CAAA7B,IAAA;QACAsB,OAAA,CAAAC,GAAA,cAAAS,SAAA;QAEA;UACA;UACA,MAAAR,OAAA,QAAAC,qBAAA,CAAAO,SAAA;UACAV,OAAA,CAAAC,GAAA,kBAAAC,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAAtB,KAAA;UACAoB,OAAA,CAAApB,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAyB,OAAA;QACA;MACA,GAAAM,KAAA,CAAA/B,KAAA;QACAoB,OAAA,CAAApB,KAAA,cAAAA,KAAA;QACA,KAAAD,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAyB,OAAA;MACA;IACA;IAEA;IACAF,sBAAAO,SAAA;MACAV,OAAA,CAAAC,GAAA,yBAAAS,SAAA;;MAEA;MACA,IAAAA,SAAA,CAAAhC,IAAA,IAAAD,KAAA,CAAAmC,OAAA,CAAAF,SAAA,CAAAhC,IAAA;QACAsB,OAAA,CAAAC,GAAA;QACA;QACA,aAAA9B,WAAA,CAAAC,IAAA;UACA;YACA,YAAAyC,kBAAA,CAAAH,SAAA;UACA;YACA,YAAAI,mBAAA,CAAAJ,SAAA;UACA;YACA,YAAAK,kBAAA,CAAAL,SAAA;UACA;YACA,YAAAM,uBAAA,CAAAN,SAAA;UACA;YACA,YAAAO,iBAAA;QACA;MACA;QACAjB,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAAiB,aAAA;;QAEA;QACA,IAAAR,SAAA,CAAAhC,IAAA,IAAAgC,SAAA,CAAAhC,IAAA,CAAAA,IAAA;UACAsB,OAAA,CAAAC,GAAA;UACAiB,aAAA;YACA9C,IAAA,EAAAsC,SAAA,CAAAhC,IAAA,CAAAN,IAAA,SAAAD,WAAA,CAAAC,IAAA;YACAM,IAAA,EAAAgC,SAAA,CAAAhC,IAAA,CAAAA,IAAA;YACAyC,OAAA,EAAAT,SAAA,CAAAhC,IAAA,CAAA0C,MAAA,GAAAV,SAAA,CAAAhC,IAAA,CAAA0C,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;UACA;QACA;;QAEA;QACA,KAAAL,aAAA;UACAlB,OAAA,CAAAC,GAAA;UACA,YAAAgB,iBAAA;QACA;QAEAjB,OAAA,CAAAC,GAAA,WAAAiB,aAAA;;QAEA;QACA,QAAAA,aAAA,CAAA9C,IAAA;UACA;YACA,YAAAyC,kBAAA,CAAAK,aAAA;UACA;YACA,YAAAJ,mBAAA,CAAAI,aAAA;UACA;YACA,YAAAH,kBAAA,CAAAG,aAAA;UACA;YACA,YAAAF,uBAAA,CAAAE,aAAA;UACA;YACA,YAAAD,iBAAA;QACA;MACA;IACA;IAEA;IACAJ,mBAAAH,SAAA;MACAV,OAAA,CAAAC,GAAA,gBAAAS,SAAA;;MAEA;MACA,IAAAhC,IAAA;MACA,IAAAyC,OAAA;;MAEA;MACA,IAAAT,SAAA,CAAAhC,IAAA,IAAAD,KAAA,CAAAmC,OAAA,CAAAF,SAAA,CAAAhC,IAAA;QACAA,IAAA,GAAAgC,SAAA,CAAAhC,IAAA;QACAyC,OAAA,GAAAT,SAAA,CAAAS,OAAA;MACA;MACA;MAAA,KACA,IAAAT,SAAA,CAAAhC,IAAA,IAAAgC,SAAA,CAAAhC,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAAmC,OAAA,CAAAF,SAAA,CAAAhC,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgC,SAAA,CAAAhC,IAAA,CAAAA,IAAA;QACAyC,OAAA,GAAAT,SAAA,CAAAhC,IAAA,CAAA0C,MAAA,GACAV,SAAA,CAAAhC,IAAA,CAAA0C,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAvB,OAAA,CAAAC,GAAA,YAAAvB,IAAA;MACAsB,OAAA,CAAAC,GAAA,QAAAkB,OAAA;;MAEA;MACA,MAAAK,SAAA,GAAA9C,IAAA,CAAA+C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAzD,IAAA;;MAEA;MACA,MAAA2D,MAAA;MACA,IAAAlD,IAAA,CAAAmD,MAAA,QAAAnD,IAAA,IAAAkD,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACArD,IAAA,CAAAsD,OAAA,CAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAI,OAAA,CAAAC,CAAA,IAAAH,aAAA,CAAAI,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAA3D,KAAA,CAAA4D,IAAA,CAAAP,aAAA;QACAM,UAAA,CAAAJ,OAAA,CAAAG,QAAA;UACA,MAAAG,UAAA,GAAA5D,IAAA,CAAA+C,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAW,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAP,MAAA,GAAAA,MAAA,CAAAY,KAAA;UACA;UAEAZ,MAAA,CAAAa,IAAA;YACAxE,IAAA,EAAAkE,QAAA;YACA/D,IAAA;YACAM,IAAA,EAAA4D;UACA;QACA;MACA;QACA;QACAV,MAAA,CAAAa,IAAA;UACAxE,IAAA,EAAAkD,OAAA,KAAAlD,IAAA;UACAG,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAA+C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAc,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAxE,WAAA,CAAAuE,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA1E,IAAA;UACA;QACA;QACA2E,MAAA;UACArE,IAAA,EAAAkD,MAAA,CAAAH,GAAA,CAAAQ,CAAA,IAAAA,CAAA,CAAAhE,IAAA;QACA;QACA+E,KAAA;UACA5E,IAAA;UACAM,IAAA,EAAA8C;QACA;QACAyB,KAAA;UACA7E,IAAA;QACA;QACAwD,MAAA,EAAAA;MACA;IACA;IAEA;IACAd,oBAAAJ,SAAA;MACA;QAAAhC,IAAA;QAAAyC,OAAA;MAAA,IAAAT,SAAA;;MAEA;MACA,MAAAc,SAAA,GAAA9C,IAAA,CAAA+C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA;;MAEA;MACA,MAAAC,MAAA;MACA,IAAAlD,IAAA,CAAAmD,MAAA,QAAAnD,IAAA,IAAAkD,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACArD,IAAA,CAAAsD,OAAA,CAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAI,OAAA,CAAAC,CAAA,IAAAH,aAAA,CAAAI,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAA3D,KAAA,CAAA4D,IAAA,CAAAP,aAAA;QACAM,UAAA,CAAAJ,OAAA,CAAAG,QAAA;UACA,MAAAG,UAAA,GAAA5D,IAAA,CAAA+C,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAW,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAP,MAAA,GAAAA,MAAA,CAAAY,KAAA;UACA;UAEAZ,MAAA,CAAAa,IAAA;YACAxE,IAAA,EAAAkE,QAAA;YACA/D,IAAA;YACAM,IAAA,EAAA4D;UACA;QACA;MACA;QACA;QACAV,MAAA,CAAAa,IAAA;UACAxE,IAAA,EAAAkD,OAAA,KAAAlD,IAAA;UACAG,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAA+C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAc,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAxE,WAAA,CAAAuE,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAE,MAAA;UACArE,IAAA,EAAAkD,MAAA,CAAAH,GAAA,CAAAQ,CAAA,IAAAA,CAAA,CAAAhE,IAAA;QACA;QACA+E,KAAA;UACA5E,IAAA;UACAM,IAAA,EAAA8C;QACA;QACAyB,KAAA;UACA7E,IAAA;QACA;QACAwD,MAAA,EAAAA;MACA;IACA;IAEA;IACAb,mBAAAL,SAAA;MACA;QAAAhC,IAAA;MAAA,IAAAgC,SAAA;MAEA,MAAA4B,UAAA,GAAA5D,IAAA,CAAA+C,GAAA,CAAAC,IAAA;QACAzD,IAAA,EAAAyD,IAAA,CAAAC,KAAA;QACAa,KAAA,EAAAd,IAAA,CAAAc;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAxE,WAAA,CAAAuE,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAK,SAAA;QACA;QACAH,MAAA;UACAI,MAAA;UACAC,KAAA;UACAC,GAAA;UACA3E,IAAA,EAAA4D,UAAA,CAAAb,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAzD,IAAA;QACA;QACA2D,MAAA;UACA3D,IAAA;UACAG,IAAA;UACAkF,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACAH,KAAA;cACAC,IAAA;cACAG,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAL,IAAA;UACA;UACA/E,IAAA,EAAA4D;QACA;MACA;IACA;IAEA;IACAtB,wBAAAN,SAAA;MACAV,OAAA,CAAAC,GAAA,gBAAAS,SAAA;;MAEA;MACA,IAAAhC,IAAA;MACA,IAAAyC,OAAA;;MAEA;MACA,IAAAT,SAAA,CAAAhC,IAAA,IAAAD,KAAA,CAAAmC,OAAA,CAAAF,SAAA,CAAAhC,IAAA;QACAA,IAAA,GAAAgC,SAAA,CAAAhC,IAAA;QACAyC,OAAA,GAAAT,SAAA,CAAAS,OAAA;MACA;MACA;MAAA,KACA,IAAAT,SAAA,CAAAhC,IAAA,IAAAgC,SAAA,CAAAhC,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAAmC,OAAA,CAAAF,SAAA,CAAAhC,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgC,SAAA,CAAAhC,IAAA,CAAAA,IAAA;QACAyC,OAAA,GAAAT,SAAA,CAAAhC,IAAA,CAAA0C,MAAA,GACAV,SAAA,CAAAhC,IAAA,CAAA0C,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAvB,OAAA,CAAAC,GAAA,YAAAvB,IAAA;MACAsB,OAAA,CAAAC,GAAA,QAAAkB,OAAA;;MAEA;MACA,MAAA4C,SAAA,GAAArF,IAAA,CAAA+C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAAzD,IAAA;;MAEA;MACA,MAAA2D,MAAA;MACA,IAAAlD,IAAA,CAAAmD,MAAA,QAAAnD,IAAA,IAAAkD,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACArD,IAAA,CAAAsD,OAAA,CAAAN,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAI,OAAA,CAAAC,CAAA,IAAAH,aAAA,CAAAI,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAA3D,KAAA,CAAA4D,IAAA,CAAAP,aAAA;QACAM,UAAA,CAAAJ,OAAA,CAAAG,QAAA;UACA,MAAAG,UAAA,GAAA5D,IAAA,CAAA+C,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAW,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAP,MAAA,GAAAA,MAAA,CAAAY,KAAA;UACA;UAEAZ,MAAA,CAAAa,IAAA;YACAxE,IAAA,EAAAkE,QAAA;YACA/D,IAAA;YAAA;YACAM,IAAA,EAAA4D;UACA;QACA;MACA;QACA;QACAV,MAAA,CAAAa,IAAA;UACAxE,IAAA,EAAAkD,OAAA,KAAAlD,IAAA;UACAG,IAAA;UAAA;UACAM,IAAA,EAAAA,IAAA,CAAA+C,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAc,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAxE,WAAA,CAAAuE,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA1E,IAAA;UACA;QACA;QACA2E,MAAA;UACArE,IAAA,EAAAkD,MAAA,CAAAH,GAAA,CAAAQ,CAAA,IAAAA,CAAA,CAAAhE,IAAA;QACA;QACA;QACA+E,KAAA;UACA5E,IAAA;QACA;QACA6E,KAAA;UACA7E,IAAA;UAAA;UACAM,IAAA,EAAAqF;QACA;QACAnC,MAAA,EAAAA;MACA;IACA;IAEA;IACAX,kBAAA;MACAjB,OAAA,CAAAC,GAAA;MACA;QACAyC,KAAA;UACAC,IAAA,OAAAxE,WAAA,CAAAuE,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAG,KAAA;UACA5E,IAAA;UACAM,IAAA;QACA;QACAuE,KAAA;UACA7E,IAAA;QACA;QACAwD,MAAA;UACAxD,IAAA,OAAAD,WAAA,CAAAC,IAAA;UACAM,IAAA;QACA;MACA;IACA;IAEA;IACA0B,UAAAF,OAAA;MACA;QACAF,OAAA,CAAAC,GAAA;QACA,UAAA+D,KAAA,CAAAC,QAAA;UACAjE,OAAA,CAAApB,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA;UACA;QACA;;QAEA;QACA,SAAAC,aAAA;UACA,KAAAA,aAAA,CAAAgB,OAAA;QACA;;QAEA;QACA,KAAAhB,aAAA,GAAAd,OAAA,CAAAkG,IAAA,MAAAF,KAAA,CAAAC,QAAA;QACA,KAAAnF,aAAA,CAAAqF,SAAA,CAAAjE,OAAA;QACAF,OAAA,CAAAC,GAAA;MACA,SAAArB,KAAA;QACAoB,OAAA,CAAApB,KAAA,YAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,gBAAAD,KAAA,CAAAyB,OAAA;MACA;IACA;IAEA;IACAV,YAAA;MACA,SAAAb,aAAA;QACA,KAAAA,aAAA,CAAAsF,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}