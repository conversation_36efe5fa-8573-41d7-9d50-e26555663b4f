{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { dataApi } from './api';\nimport { v4 as uuidv4 } from 'uuid';\nimport axios from 'axios';\nimport ChartDisplay from './components/ChartDisplay.vue';\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null\n    };\n  },\n  mounted() {\n    this.loadTables();\n    this.initMemoryId();\n    this.addWelcomeMessage();\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\"帮我生成一个销售额柱状图\", \"展示近六个月的销售趋势折线图\", \"按照区域统计销售量并生成饼图\", \"帮我做一个按产品类别的销量对比图\"];\n  },\n  updated() {\n    this.scrollToBottom();\n  },\n  methods: {\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id');\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4());\n        localStorage.setItem('user_memory_id', storedMemoryId);\n      }\n      this.memoryId = storedMemoryId;\n    },\n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0;\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i];\n        number = number * 16 + (parseInt(hexValue, 16) || 0);\n      }\n      return number % 1000000;\n    },\n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      });\n    },\n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight;\n      }\n    },\n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables();\n        this.tables = response.data;\n      } catch (error) {\n        this.$message.error('加载数据表失败');\n        console.error(error);\n      }\n    },\n    async selectTable(table) {\n      try {\n        this.dialogVisible = true;\n        console.log(table);\n        const response = await dataApi.getTableIndicators(table);\n        this.tableIndicators = response.data;\n        console.log(this.tableIndicators);\n      } catch (error) {\n        this.$message.error('加载数据表失败');\n        console.error(error);\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n\n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedTable ? this.selectedTable.tableName : this.tables.length > 0 ? this.tables[0].tableName : \"未知数据集\";\n\n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      };\n      this.messages.push(userMsg);\n\n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      };\n      this.messages.push(botMsg);\n\n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1];\n\n      // 保存问题并清空输入框\n      const question = this.question;\n      this.question = '';\n      this.isSending = true;\n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`;\n\n        // 发送请求\n        await axios.post('http://localhost:8088/api/indicator/chat', {\n          memoryId: this.memoryId,\n          message\n        }, {\n          responseType: 'stream',\n          onDownloadProgress: e => {\n            const fullText = e.event.target.responseText; // 累积的完整文本\n            let newText = fullText.substring(lastMsg.content.length);\n            lastMsg.content += newText; // 增量更新\n            this.scrollToBottom(); // 实时滚动\n          }\n        });\n\n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false;\n        this.isSending = false;\n\n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error);\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。';\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      }\n    },\n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n\n      // 首先尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        let chartConfig = JSON.parse(chartConfigMatch[1]);\n        console.log('解析后的JSON对象:', chartConfig);\n\n        // 检查是否是API响应格式（包含code和data字段）\n        if (chartConfig.code === 0 && chartConfig.data) {\n          console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n          chartConfig = chartConfig.data;\n        }\n\n        // 如果包含必要的图表配置字段，则视为有效的图表配置\n        if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n          console.log('找到有效的图表配置:', chartConfig);\n          message.chartConfig = chartConfig;\n\n          // 可以选择性地从消息中移除JSON代码块\n          message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n        } else {\n          console.log('图表配置缺少必要字段:', chartConfig);\n        }\n      } else {\n        console.log('未找到JSON代码块，尝试直接解析响应内容');\n\n        // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n        try {\n          // 尝试查找可能的JSON字符串\n          const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n          const jsonMatch = message.content.match(jsonRegex);\n          if (jsonMatch) {\n            console.log('找到可能的JSON字符串:', jsonMatch[0]);\n\n            // 尝试将字符串转换为JSON对象\n            // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n            const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n            console.log('处理后的JSON字符串:', jsonStr);\n            try {\n              const chartConfig = JSON.parse(jsonStr);\n              console.log('解析后的JSON对象:', chartConfig);\n              if (chartConfig.code === 0 && chartConfig.data) {\n                const data = chartConfig.data;\n                console.log('提取的图表数据:', data);\n\n                // 检查是否包含必要的图表字段\n                if (data.type && (data.tableId || data.data)) {\n                  console.log('找到有效的图表配置:', data);\n                  message.chartConfig = data;\n                  message.content = message.content.replace(jsonMatch[0], '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                }\n              }\n            } catch (parseError) {\n              console.log('JSON解析错误:', parseError);\n            }\n          } else {\n            console.log('未找到可能的JSON字符串');\n\n            // 最后的尝试：检查消息中是否包含图表数据的关键词\n            if (message.content.includes('图表数据已成功获取') || message.content.includes('已生成图表')) {\n              console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n\n              // 构建一个默认的图表配置\n              const defaultConfig = {\n                id: Date.now().toString(),\n                type: 'bar',\n                title: '数据图表',\n                tableId: this.selectedTable?.id || '1114644377738809344'\n              };\n              console.log('构建的默认图表配置:', defaultConfig);\n              message.chartConfig = defaultConfig;\n            }\n          }\n        } catch (error) {\n          console.log('解析过程中出错:', error);\n        }\n      }\n    },\n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n\n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      this.messages.push(botMsg);\n\n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n\n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [{\n            field: '类别1',\n            value: 100\n          }, {\n            field: '类别2',\n            value: 200\n          }, {\n            field: '类别3',\n            value: 150\n          }],\n          metrics: [{\n            name: '数值'\n          }]\n        }\n      };\n\n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n\n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [{\n              value: 1,\n              field: \"神朔\",\n              name: \"神朔\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"甘泉\",\n              name: \"甘泉\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"包神\",\n              name: \"包神\",\n              category: \"记录数*\"\n            }],\n            fields: [{\n              id: \"1746787308487\",\n              name: \"名称\",\n              groupType: \"d\"\n            }, {\n              id: \"-1\",\n              name: \"记录数*\",\n              groupType: \"q\"\n            }]\n          }\n        }\n      };\n\n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n\n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n\n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n      console.log('添加了实际数据的消息:', realDataMsg);\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "v4", "uuidv4", "axios", "ChartDisplay", "name", "components", "data", "description", "tablename", "tables", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "tableIndicators", "dialogVisible", "messages", "memoryId", "isSending", "messageListRef", "mounted", "loadTables", "initMemoryId", "addWelcomeMessage", "updated", "scrollToBottom", "methods", "storedMemoryId", "localStorage", "getItem", "uuidToNumber", "setItem", "uuid", "number", "i", "length", "hexValue", "parseInt", "push", "isUser", "content", "isTyping", "$refs", "scrollTop", "scrollHeight", "response", "getAllTables", "error", "$message", "console", "selectTable", "table", "log", "getTableIndicators", "showSuggestions", "useQuestion", "q", "getTableFields", "tableCode", "submitQuestion", "trim", "warning", "currentDataset", "tableName", "userMsg", "botMsg", "lastMsg", "message", "post", "responseType", "onDownloadProgress", "e", "fullText", "event", "target", "responseText", "newText", "substring", "parseChartConfig", "chartConfigMatch", "match", "chartConfig", "JSON", "parse", "code", "type", "datasetId", "tableId", "replace", "jsonRegex", "jsonMatch", "jsonStr", "parseError", "includes", "defaultConfig", "id", "Date", "now", "toString", "title", "test<PERSON>hart", "testChartConfig", "testApiResponse", "msg", "field", "value", "metrics", "json<PERSON><PERSON><PERSON>", "stringify", "apiResponseMsg", "testRealData", "realData", "category", "fields", "groupType", "realDataMsg"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n        </div>\n        \n        <div class=\"data-selection\">\n          <h3>目前可用数据</h3>\n          <div class=\"data-sets\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\" v-for=\"table in tables\" :key=\"table.tableCode\">\n                <el-card class=\"data-card\" @click.native=\"selectTable(table.datasetId)\">\n                  <div class=\"data-header\">\n                    <span class=\"sample-tag\">{{table.tableName}}</span>\n                    <span>{{ table.description }}</span>\n                  </div>\n                  <div class=\"data-body\">\n                    <div class=\"data-fields\">\n                      <div v-for=\"(field, index) in getTableFields(table)\" :key=\"index\" class=\"field-item\">\n                        {{ field }}\n                      </div>\n                    </div>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        \n        <!-- 聊天消息列表区域 -->\n        <div class=\"message-list\" ref=\"messageListRef\">\n          <div\n            v-for=\"(message, index) in messages\"\n            :key=\"index\"\n            :class=\"message.isUser ? 'message user-message' : 'message bot-message'\"\n          >\n            <!-- 聊天图标 -->\n            <div class=\"avatar-container\" v-if=\"!message.isUser\">\n              <div class=\"bot-avatar\">\n                <i class=\"el-icon-s-tools\"></i>\n              </div>\n            </div>\n            <!-- 消息内容 -->\n            <div class=\"message-content\">\n              <div v-html=\"message.content\"></div>\n              <!-- 如果消息中包含图表配置，则显示图表 -->\n              <div v-if=\"message.chartConfig\" class=\"chart-container\">\n                <chart-display :chart-config=\"message.chartConfig\"></chart-display>\n              </div>\n              <!-- loading动画 -->\n              <span\n                class=\"loading-dots\"\n                v-if=\"message.isTyping\"\n              >\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n              </span>\n            </div>\n            <div class=\"avatar-container\" v-if=\"message.isUser\">\n              <div class=\"user-avatar\">\n                <i class=\"el-icon-user\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 底部问题输入区域 -->\n    <div class=\"question-input-container\">\n      <span>👋直接问我问题，或在上方选择一个主题/数据开始！</span>\n      <div class=\"question-input-wrapper\">\n       \n        <div class=\"input-prefix\">👋</div>\n        <el-input \n          v-model=\"question\" \n          placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n          class=\"question-input\"\n          @keyup.enter.native=\"submitQuestion\"\n          :disabled=\"isSending\">\n        </el-input>\n        <div class=\"input-actions\">\n          <button class=\"action-btn\" @click=\"showSuggestions\">\n            <i class=\"el-icon-magic-stick\"></i>\n          </button>\n          <button class=\"action-btn test-btn\" @click=\"testChart\" title=\"测试图表功能\">\n            <i class=\"el-icon-data-line\"></i>\n          </button>\n          <button class=\"action-btn test-btn\" @click=\"testRealData\" title=\"测试实际数据\">\n            <i class=\"el-icon-s-data\"></i>\n          </button>\n          <button class=\"action-btn send-btn\" @click=\"submitQuestion\" :disabled=\"isSending\">\n            <i class=\"el-icon-position\"></i>\n          </button>\n        </div>\n      </div>\n      \n      <!-- 建议问题弹出层 -->\n      <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n        <div class=\"suggestions-title\">\n          <i class=\"el-icon-s-promotion\"></i> 官方推荐\n        </div>\n        <div class=\"suggestions-list\">\n          <div \n            v-for=\"(suggestion, index) in suggestedQuestions\" \n            :key=\"index\"\n            class=\"suggestion-item\"\n            @click=\"useQuestion(suggestion)\">\n            {{ suggestion }}\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <el-drawer\n      title=\"数据详情\"\n      :visible.sync=\"dialogVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <div style=\"padding: 20px\">\n        <el-table :data=\"tableIndicators\">\n          <el-table-column property=\"id\" ></el-table-column>\n          <el-table-column property=\"datasetTag\" ></el-table-column>\n          <el-table-column property=\"transactionDate\" ></el-table-column>\n          <el-table-column property=\"salesAmount\" ></el-table-column>\n        </el-table>\n      </div>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api'\nimport { v4 as uuidv4 } from 'uuid'\nimport axios from 'axios'\nimport ChartDisplay from './components/ChartDisplay.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null\n    }\n  },\n  mounted() {\n    this.loadTables()\n    this.initMemoryId()\n    this.addWelcomeMessage()\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\n      \"帮我生成一个销售额柱状图\",\n      \"展示近六个月的销售趋势折线图\",\n      \"按照区域统计销售量并生成饼图\",\n      \"帮我做一个按产品类别的销量对比图\"\n    ]\n  },\n  updated() {\n    this.scrollToBottom()\n  },\n  methods: {\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id')\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4())\n        localStorage.setItem('user_memory_id', storedMemoryId)\n      }\n      this.memoryId = storedMemoryId\n    },\n    \n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i]\n        number = number * 16 + (parseInt(hexValue, 16) || 0)\n      }\n      return number % 1000000\n    },\n    \n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      })\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight\n      }\n    },\n    \n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables()\n        this.tables = response.data\n      } catch (error) {\n        this.$message.error('加载数据表失败')\n        console.error(error)\n      }\n    },\n    async selectTable(table) {\n      try {\n        this.dialogVisible=true\n        console.log(table)\n      const response=await dataApi.getTableIndicators(table)\n      this.tableIndicators=response.data\n      console.log(this.tableIndicators)\n      }catch(error){\n        this.$message.error('加载数据表失败')\n        console.error(error)\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    \n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedTable ? \n        this.selectedTable.tableName : \n        (this.tables.length > 0 ? this.tables[0].tableName : \"未知数据集\")\n      \n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      }\n      this.messages.push(userMsg)\n      \n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      }\n      this.messages.push(botMsg)\n      \n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1]\n      \n      // 保存问题并清空输入框\n      const question = this.question\n      this.question = ''\n      this.isSending = true\n      \n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`\n        \n        // 发送请求\n        await axios.post(\n          'http://localhost:8088/api/indicator/chat',\n          { memoryId: this.memoryId, message },\n          {\n            responseType: 'stream',\n            onDownloadProgress: (e) => {\n              const fullText = e.event.target.responseText // 累积的完整文本\n              let newText = fullText.substring(lastMsg.content.length)\n              lastMsg.content += newText // 增量更新\n              this.scrollToBottom() // 实时滚动\n            }\n          }\n        )\n        \n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false\n        this.isSending = false\n        \n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error)\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'\n        lastMsg.isTyping = false\n        this.isSending = false\n      }\n    },\n    \n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n      \n      // 首先尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        \n        let chartConfig = JSON.parse(chartConfigMatch[1]);\n        console.log('解析后的JSON对象:', chartConfig);\n        \n        // 检查是否是API响应格式（包含code和data字段）\n        if (chartConfig.code === 0 && chartConfig.data) {\n          console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n          chartConfig = chartConfig.data;\n        }\n        \n        // 如果包含必要的图表配置字段，则视为有效的图表配置\n        if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n          console.log('找到有效的图表配置:', chartConfig);\n          message.chartConfig = chartConfig;\n          \n          // 可以选择性地从消息中移除JSON代码块\n          message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, \n            '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n        } else {\n          console.log('图表配置缺少必要字段:', chartConfig);\n        }\n      } else {\n        console.log('未找到JSON代码块，尝试直接解析响应内容');\n        \n        // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n        try {\n          // 尝试查找可能的JSON字符串\n          const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n          const jsonMatch = message.content.match(jsonRegex);\n          \n          if (jsonMatch) {\n            console.log('找到可能的JSON字符串:', jsonMatch[0]);\n            \n            // 尝试将字符串转换为JSON对象\n            // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n            const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n            console.log('处理后的JSON字符串:', jsonStr);\n            \n            try {\n              const chartConfig = JSON.parse(jsonStr);\n              console.log('解析后的JSON对象:', chartConfig);\n              \n              if (chartConfig.code === 0 && chartConfig.data) {\n                const data = chartConfig.data;\n                console.log('提取的图表数据:', data);\n                \n                // 检查是否包含必要的图表字段\n                if (data.type && (data.tableId || data.data)) {\n                  console.log('找到有效的图表配置:', data);\n                  message.chartConfig = data;\n                  message.content = message.content.replace(jsonMatch[0], \n                    '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                }\n              }\n            } catch (parseError) {\n              console.log('JSON解析错误:', parseError);\n            }\n          } else {\n            console.log('未找到可能的JSON字符串');\n            \n            // 最后的尝试：检查消息中是否包含图表数据的关键词\n            if (message.content.includes('图表数据已成功获取') || \n                message.content.includes('已生成图表')) {\n              console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n              \n              // 构建一个默认的图表配置\n              const defaultConfig = {\n                id: Date.now().toString(),\n                type: 'bar',\n                title: '数据图表',\n                tableId: this.selectedTable?.id || '1114644377738809344'\n              };\n              \n              console.log('构建的默认图表配置:', defaultConfig);\n              message.chartConfig = defaultConfig;\n            }\n          }\n        } catch (error) {\n          console.log('解析过程中出错:', error);\n        }\n      }\n    },\n    \n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n      \n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      \n      this.messages.push(botMsg);\n      \n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n      \n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [\n            { field: '类别1', value: 100 },\n            { field: '类别2', value: 200 },\n            { field: '类别3', value: 150 }\n          ],\n          metrics: [{ name: '数值' }]\n        }\n      };\n      \n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      \n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n      \n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    \n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n      \n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [\n              {value: 1, field: \"神朔\", name: \"神朔\", category: \"记录数*\"},\n              {value: 1, field: \"甘泉\", name: \"甘泉\", category: \"记录数*\"},\n              {value: 1, field: \"包神\", name: \"包神\", category: \"记录数*\"}\n            ],\n            fields: [\n              {id: \"1746787308487\", name: \"名称\", groupType: \"d\"},\n              {id: \"-1\", name: \"记录数*\", groupType: \"q\"}\n            ]\n          }\n        }\n      };\n      \n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n      \n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n      \n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n      \n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.app-layout {\n  display: flex;\n  height: calc(100vh - 100px); /* 为更高的底部输入框留出空间 */\n  overflow: hidden;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  background-color: #f9f9f9;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n\n}\n\n.logo h2 {\n  margin: 0;\n    font-size: 25px;\n    text-align: center;\n    white-space: nowrap;\n    letter-spacing: 2px;\n    /* 字体变粗 */\n    font-weight: 900;\n}\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  overflow-x: auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.header {\n  margin-bottom: 20px;\n  \n}\n\n.header h2 {\n  margin: 0;\n  font-size: 30px;\n}\n\n.highlight {\n  color: #409eff;\n}\n\n.sub-title {\n  color: #606266;\n  font-size: 14px;\n  margin: 5px 0 0 0;\n}\n\n.data-card {\n  cursor: pointer;\n  margin-bottom: 15px;\n  transition: all 0.3s;\n}\n\n.data-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.data-header {\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.sample-tag {\n  background-color: #ecf5ff;\n  color: #409eff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n.data-fields {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.field-item {\n  background-color: #f5f7fa;\n  padding: 2px 8px;\n  margin: 4px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n/* 底部问题输入区域 */\n.question-input-container {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  background-color: #fff;\n  border-top: 1px solid #ebeef5;\n  padding: 25px 15px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.question-input-container > span {\n  margin-bottom: 12px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.question-input-wrapper {\n  display: flex;\n  align-items: center;\n  width: 70%;\n  max-width: 800px;\n  margin: 0 auto;\n  background-color: #f5f7fa;\n  border-radius: 20px;\n  padding: 8px 15px;\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: #fff;\n  border: 1px solid #dcdfe6;\n  margin-left: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n}\n\n.send-btn {\n  background-color: #409eff;\n  color: white;\n  border: none;\n}\n\n.test-btn {\n  background-color: #67c23a;\n  color: white;\n  border: none;\n}\n\n.send-btn:disabled {\n  background-color: #c0c4cc;\n  cursor: not-allowed;\n}\n\n/* 建议问题面板 */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n\n/* 消息列表样式 */\n.message-list {\n  width: 100%;\n  max-height: 420px;\n  overflow-y: auto;\n  margin-top: 20px;\n  padding: 10px;\n  background-color: transparent;\n  border-radius: 8px;\n}\n\n.message {\n  margin-bottom: 20px;\n  display: flex;\n  position: relative;\n  max-width: 80%;\n}\n\n.message-content {\n  padding: 12px 15px;\n  border-radius: 6px;\n  line-height: 1.5;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n  align-self: flex-end;\n  margin-left: auto;\n}\n\n.user-message .message-content {\n  background-color: #ecf5ff;\n  margin-right: 10px;\n}\n\n.bot-message {\n  align-self: flex-start;\n}\n\n.bot-message .message-content {\n  background-color: #f5f7fa;\n  margin-left: 10px;\n}\n\n.avatar-container {\n  display: flex;\n  align-items: flex-start;\n}\n\n.bot-avatar, .user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bot-avatar {\n  background-color: #4caf50;\n  color: white;\n}\n\n.user-avatar {\n  background-color: #2196f3;\n  color: white;\n}\n\n/* 加载动画 */\n.loading-dots {\n  display: inline-block;\n}\n\n.dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  background-color: #999;\n  border-radius: 50%;\n  margin: 0 2px;\n  animation: pulse 1.2s infinite ease-in-out both;\n}\n\n.dot:nth-child(2) {\n  animation-delay: -0.4s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: -0.8s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(0.6);\n    opacity: 0.4;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* 图表相关样式 */\n.chart-container {\n  margin-top: 15px;\n  margin-bottom: 15px;\n  border: 1px solid #e6e6e6;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.chart-notice {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n  font-weight: bold;\n}\n</style>"], "mappings": ";AA6JA,SAAAA,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,UAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;IACA;IACA,KAAAX,kBAAA,IACA,gBACA,kBACA,kBACA,mBACA;EACA;EACAY,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAJ,aAAA;MACA,IAAAK,cAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,KAAAF,cAAA;QACAA,cAAA,QAAAG,YAAA,CAAA/B,MAAA;QACA6B,YAAA,CAAAG,OAAA,mBAAAJ,cAAA;MACA;MACA,KAAAV,QAAA,GAAAU,cAAA;IACA;IAEA;IACAG,aAAAE,IAAA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAG,MAAA,IAAAD,CAAA,MAAAA,CAAA;QACA,MAAAE,QAAA,GAAAJ,IAAA,CAAAE,CAAA;QACAD,MAAA,GAAAA,MAAA,SAAAI,QAAA,CAAAD,QAAA;MACA;MACA,OAAAH,MAAA;IACA;IAEA;IACAV,kBAAA;MACA,KAAAP,QAAA,CAAAsB,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IAEA;IACAhB,eAAA;MACA,SAAAiB,KAAA,CAAAvB,cAAA;QACA,KAAAuB,KAAA,CAAAvB,cAAA,CAAAwB,SAAA,QAAAD,KAAA,CAAAvB,cAAA,CAAAyB,YAAA;MACA;IACA;IAEA,MAAAvB,WAAA;MACA;QACA,MAAAwB,QAAA,SAAAhD,OAAA,CAAAiD,YAAA;QACA,KAAAvC,MAAA,GAAAsC,QAAA,CAAAzC,IAAA;MACA,SAAA2C,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,CAAAA,KAAA;MACA;IACA;IACA,MAAAG,YAAAC,KAAA;MACA;QACA,KAAApC,aAAA;QACAkC,OAAA,CAAAG,GAAA,CAAAD,KAAA;QACA,MAAAN,QAAA,SAAAhD,OAAA,CAAAwD,kBAAA,CAAAF,KAAA;QACA,KAAArC,eAAA,GAAA+B,QAAA,CAAAzC,IAAA;QACA6C,OAAA,CAAAG,GAAA,MAAAtC,eAAA;MACA,SAAAiC,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,CAAAA,KAAA;MACA;IACA;IACAO,gBAAA;MACA,KAAA3C,oBAAA,SAAAA,oBAAA;IACA;IACA4C,YAAAC,CAAA;MACA,KAAA/C,QAAA,GAAA+C,CAAA;MACA,KAAA7C,oBAAA;IACA;IACA8C,eAAAN,KAAA;MACA,IAAAA,KAAA,CAAAO,SAAA;QACA;MACA;MACA;IACA;IAEA;IACA,MAAAC,eAAA;MACA,UAAAlD,QAAA,CAAAmD,IAAA,WAAA1C,SAAA;QACA,KAAA8B,QAAA,CAAAa,OAAA;QACA;MACA;;MAEA;MACA,MAAAC,cAAA,QAAAtD,aAAA,GACA,KAAAA,aAAA,CAAAuD,SAAA,GACA,KAAAxD,MAAA,CAAA4B,MAAA,YAAA5B,MAAA,IAAAwD,SAAA;;MAEA;MACA,MAAAC,OAAA;QACAzB,MAAA;QACAC,OAAA,OAAA/B,QAAA,CAAAmD,IAAA;QACAnB,QAAA;MACA;MACA,KAAAzB,QAAA,CAAAsB,IAAA,CAAA0B,OAAA;;MAEA;MACA,MAAAC,MAAA;QACA1B,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAAzB,QAAA,CAAAsB,IAAA,CAAA2B,MAAA;;MAEA;MACA,MAAAC,OAAA,QAAAlD,QAAA,MAAAA,QAAA,CAAAmB,MAAA;;MAEA;MACA,MAAA1B,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA;MACA,KAAAS,SAAA;MAEA;QACA;QACA,MAAAiD,OAAA,MAAA1D,QAAA,WAAAqD,cAAA;;QAEA;QACA,MAAA9D,KAAA,CAAAoE,IAAA,CACA,4CACA;UAAAnD,QAAA,OAAAA,QAAA;UAAAkD;QAAA,GACA;UACAE,YAAA;UACAC,kBAAA,EAAAC,CAAA;YACA,MAAAC,QAAA,GAAAD,CAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAC,YAAA;YACA,IAAAC,OAAA,GAAAJ,QAAA,CAAAK,SAAA,CAAAX,OAAA,CAAA1B,OAAA,CAAAL,MAAA;YACA+B,OAAA,CAAA1B,OAAA,IAAAoC,OAAA;YACA,KAAAnD,cAAA;UACA;QACA,CACA;;QAEA;QACAyC,OAAA,CAAAzB,QAAA;QACA,KAAAvB,SAAA;;QAEA;QACA,KAAA4D,gBAAA,CAAAZ,OAAA;MACA,SAAAnB,KAAA;QACAE,OAAA,CAAAF,KAAA,UAAAA,KAAA;QACAmB,OAAA,CAAA1B,OAAA;QACA0B,OAAA,CAAAzB,QAAA;QACA,KAAAvB,SAAA;MACA;IACA;IAEA;IACA4D,iBAAAX,OAAA;MACAlB,OAAA,CAAAG,GAAA,qBAAAe,OAAA,CAAA3B,OAAA;;MAEA;MACA,MAAAuC,gBAAA,GAAAZ,OAAA,CAAA3B,OAAA,CAAAwC,KAAA;MACA,IAAAD,gBAAA,IAAAA,gBAAA;QACA9B,OAAA,CAAAG,GAAA,eAAA2B,gBAAA;QAEA,IAAAE,WAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAJ,gBAAA;QACA9B,OAAA,CAAAG,GAAA,gBAAA6B,WAAA;;QAEA;QACA,IAAAA,WAAA,CAAAG,IAAA,UAAAH,WAAA,CAAA7E,IAAA;UACA6C,OAAA,CAAAG,GAAA,yBAAA6B,WAAA,CAAA7E,IAAA;UACA6E,WAAA,GAAAA,WAAA,CAAA7E,IAAA;QACA;;QAEA;QACA,IAAA6E,WAAA,CAAAI,IAAA,KAAAJ,WAAA,CAAAK,SAAA,IAAAL,WAAA,CAAAM,OAAA,IAAAN,WAAA,CAAA7E,IAAA;UACA6C,OAAA,CAAAG,GAAA,eAAA6B,WAAA;UACAd,OAAA,CAAAc,WAAA,GAAAA,WAAA;;UAEA;UACAd,OAAA,CAAA3B,OAAA,GAAA2B,OAAA,CAAA3B,OAAA,CAAAgD,OAAA,6BACA;QACA;UACAvC,OAAA,CAAAG,GAAA,gBAAA6B,WAAA;QACA;MACA;QACAhC,OAAA,CAAAG,GAAA;;QAEA;QACA;UACA;UACA,MAAAqC,SAAA;UACA,MAAAC,SAAA,GAAAvB,OAAA,CAAA3B,OAAA,CAAAwC,KAAA,CAAAS,SAAA;UAEA,IAAAC,SAAA;YACAzC,OAAA,CAAAG,GAAA,kBAAAsC,SAAA;;YAEA;YACA;YACA,MAAAC,OAAA,GAAAD,SAAA,IAAAF,OAAA;YACAvC,OAAA,CAAAG,GAAA,iBAAAuC,OAAA;YAEA;cACA,MAAAV,WAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAQ,OAAA;cACA1C,OAAA,CAAAG,GAAA,gBAAA6B,WAAA;cAEA,IAAAA,WAAA,CAAAG,IAAA,UAAAH,WAAA,CAAA7E,IAAA;gBACA,MAAAA,IAAA,GAAA6E,WAAA,CAAA7E,IAAA;gBACA6C,OAAA,CAAAG,GAAA,aAAAhD,IAAA;;gBAEA;gBACA,IAAAA,IAAA,CAAAiF,IAAA,KAAAjF,IAAA,CAAAmF,OAAA,IAAAnF,IAAA,CAAAA,IAAA;kBACA6C,OAAA,CAAAG,GAAA,eAAAhD,IAAA;kBACA+D,OAAA,CAAAc,WAAA,GAAA7E,IAAA;kBACA+D,OAAA,CAAA3B,OAAA,GAAA2B,OAAA,CAAA3B,OAAA,CAAAgD,OAAA,CAAAE,SAAA,KACA;gBACA;cACA;YACA,SAAAE,UAAA;cACA3C,OAAA,CAAAG,GAAA,cAAAwC,UAAA;YACA;UACA;YACA3C,OAAA,CAAAG,GAAA;;YAEA;YACA,IAAAe,OAAA,CAAA3B,OAAA,CAAAqD,QAAA,iBACA1B,OAAA,CAAA3B,OAAA,CAAAqD,QAAA;cACA5C,OAAA,CAAAG,GAAA;;cAEA;cACA,MAAA0C,aAAA;gBACAC,EAAA,EAAAC,IAAA,CAAAC,GAAA,GAAAC,QAAA;gBACAb,IAAA;gBACAc,KAAA;gBACAZ,OAAA,OAAA/E,aAAA,EAAAuF,EAAA;cACA;cAEA9C,OAAA,CAAAG,GAAA,eAAA0C,aAAA;cACA3B,OAAA,CAAAc,WAAA,GAAAa,aAAA;YACA;UACA;QACA,SAAA/C,KAAA;UACAE,OAAA,CAAAG,GAAA,aAAAL,KAAA;QACA;MACA;IACA;IAEA;IACAqD,UAAA;MACA;MACA,MAAAC,eAAA;QACAN,EAAA;QACAV,IAAA;QACAc,KAAA;QACAb,SAAA;QACAC,OAAA;MACA;;MAEA;MACA,MAAAtB,MAAA;QACA1B,MAAA;QACAC,OAAA;QACAC,QAAA;QACAwC,WAAA,EAAAoB;MACA;MAEA,KAAArF,QAAA,CAAAsB,IAAA,CAAA2B,MAAA;;MAEA;MACAhB,OAAA,CAAAG,GAAA,iBAAApC,QAAA;;MAEA;MACA,MAAAsF,eAAA;QACAlB,IAAA;QACAmB,GAAA;QACAnG,IAAA;UACAiF,IAAA;UACAjF,IAAA,GACA;YAAAoG,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,EACA;UACAC,OAAA;YAAAxG,IAAA;UAAA;QACA;MACA;;MAEA;MACA,MAAAyG,WAAA,iBAAAzB,IAAA,CAAA0B,SAAA,CAAAN,eAAA;MACA,MAAAO,cAAA;QACAtE,MAAA;QACAC,OAAA,gBAAAmE,WAAA;QACAlE,QAAA;MACA;MAEA,KAAAzB,QAAA,CAAAsB,IAAA,CAAAuE,cAAA;MACA,KAAA/B,gBAAA,CAAA+B,cAAA;MAEA5D,OAAA,CAAAG,GAAA,iBAAAyD,cAAA;IACA;IAEA;IACAC,aAAA;MACA7D,OAAA,CAAAG,GAAA;;MAEA;MACA,MAAA2D,QAAA;QACA3B,IAAA;QACAmB,GAAA;QACAnG,IAAA;UACA2F,EAAA;UACAI,KAAA;UACAZ,OAAA;UACAF,IAAA;UACAjF,IAAA;YACAA,IAAA,GACA;cAAAqG,KAAA;cAAAD,KAAA;cAAAtG,IAAA;cAAA8G,QAAA;YAAA,GACA;cAAAP,KAAA;cAAAD,KAAA;cAAAtG,IAAA;cAAA8G,QAAA;YAAA,GACA;cAAAP,KAAA;cAAAD,KAAA;cAAAtG,IAAA;cAAA8G,QAAA;YAAA,EACA;YACAC,MAAA,GACA;cAAAlB,EAAA;cAAA7F,IAAA;cAAAgH,SAAA;YAAA,GACA;cAAAnB,EAAA;cAAA7F,IAAA;cAAAgH,SAAA;YAAA;UAEA;QACA;MACA;;MAEA;MACA,MAAAC,WAAA;QACA5E,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MAEA;MACA0E,WAAA,CAAAlC,WAAA,GAAA8B,QAAA,CAAA3G,IAAA;;MAEA;MACA,KAAAY,QAAA,CAAAsB,IAAA,CAAA6E,WAAA;MAEAlE,OAAA,CAAAG,GAAA,gBAAA+D,WAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}