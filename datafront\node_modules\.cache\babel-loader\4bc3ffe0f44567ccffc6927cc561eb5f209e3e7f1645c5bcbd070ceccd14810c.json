{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as layout from '../../util/layout.js';\nimport { parsePercent, linearMap } from '../../util/number.js';\nimport { isFunction } from 'zrender/lib/core/util.js';\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nfunction getSortedIndices(data, sort) {\n  var valueDim = data.mapDimension('value');\n  var valueArr = data.mapArray(valueDim, function (val) {\n    return val;\n  });\n  var indices = [];\n  var isAscending = sort === 'ascending';\n  for (var i = 0, len = data.count(); i < len; i++) {\n    indices[i] = i;\n  }\n  // Add custom sortable function & none sortable opetion by \"options.sort\"\n  if (isFunction(sort)) {\n    indices.sort(sort);\n  } else if (sort !== 'none') {\n    indices.sort(function (a, b) {\n      return isAscending ? valueArr[a] - valueArr[b] : valueArr[b] - valueArr[a];\n    });\n  }\n  return indices;\n}\nfunction labelLayout(data) {\n  var seriesModel = data.hostModel;\n  var orient = seriesModel.get('orient');\n  data.each(function (idx) {\n    var itemModel = data.getItemModel(idx);\n    var labelModel = itemModel.getModel('label');\n    var labelPosition = labelModel.get('position');\n    var labelLineModel = itemModel.getModel('labelLine');\n    var layout = data.getItemLayout(idx);\n    var points = layout.points;\n    var isLabelInside = labelPosition === 'inner' || labelPosition === 'inside' || labelPosition === 'center' || labelPosition === 'insideLeft' || labelPosition === 'insideRight';\n    var textAlign;\n    var textX;\n    var textY;\n    var linePoints;\n    if (isLabelInside) {\n      if (labelPosition === 'insideLeft') {\n        textX = (points[0][0] + points[3][0]) / 2 + 5;\n        textY = (points[0][1] + points[3][1]) / 2;\n        textAlign = 'left';\n      } else if (labelPosition === 'insideRight') {\n        textX = (points[1][0] + points[2][0]) / 2 - 5;\n        textY = (points[1][1] + points[2][1]) / 2;\n        textAlign = 'right';\n      } else {\n        textX = (points[0][0] + points[1][0] + points[2][0] + points[3][0]) / 4;\n        textY = (points[0][1] + points[1][1] + points[2][1] + points[3][1]) / 4;\n        textAlign = 'center';\n      }\n      linePoints = [[textX, textY], [textX, textY]];\n    } else {\n      var x1 = void 0;\n      var y1 = void 0;\n      var x2 = void 0;\n      var y2 = void 0;\n      var labelLineLen = labelLineModel.get('length');\n      if (process.env.NODE_ENV !== 'production') {\n        if (orient === 'vertical' && ['top', 'bottom'].indexOf(labelPosition) > -1) {\n          labelPosition = 'left';\n          console.warn('Position error: Funnel chart on vertical orient dose not support top and bottom.');\n        }\n        if (orient === 'horizontal' && ['left', 'right'].indexOf(labelPosition) > -1) {\n          labelPosition = 'bottom';\n          console.warn('Position error: Funnel chart on horizontal orient dose not support left and right.');\n        }\n      }\n      if (labelPosition === 'left') {\n        // Left side\n        x1 = (points[3][0] + points[0][0]) / 2;\n        y1 = (points[3][1] + points[0][1]) / 2;\n        x2 = x1 - labelLineLen;\n        textX = x2 - 5;\n        textAlign = 'right';\n      } else if (labelPosition === 'right') {\n        // Right side\n        x1 = (points[1][0] + points[2][0]) / 2;\n        y1 = (points[1][1] + points[2][1]) / 2;\n        x2 = x1 + labelLineLen;\n        textX = x2 + 5;\n        textAlign = 'left';\n      } else if (labelPosition === 'top') {\n        // Top side\n        x1 = (points[3][0] + points[0][0]) / 2;\n        y1 = (points[3][1] + points[0][1]) / 2;\n        y2 = y1 - labelLineLen;\n        textY = y2 - 5;\n        textAlign = 'center';\n      } else if (labelPosition === 'bottom') {\n        // Bottom side\n        x1 = (points[1][0] + points[2][0]) / 2;\n        y1 = (points[1][1] + points[2][1]) / 2;\n        y2 = y1 + labelLineLen;\n        textY = y2 + 5;\n        textAlign = 'center';\n      } else if (labelPosition === 'rightTop') {\n        // RightTop side\n        x1 = orient === 'horizontal' ? points[3][0] : points[1][0];\n        y1 = orient === 'horizontal' ? points[3][1] : points[1][1];\n        if (orient === 'horizontal') {\n          y2 = y1 - labelLineLen;\n          textY = y2 - 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 + labelLineLen;\n          textX = x2 + 5;\n          textAlign = 'top';\n        }\n      } else if (labelPosition === 'rightBottom') {\n        // RightBottom side\n        x1 = points[2][0];\n        y1 = points[2][1];\n        if (orient === 'horizontal') {\n          y2 = y1 + labelLineLen;\n          textY = y2 + 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 + labelLineLen;\n          textX = x2 + 5;\n          textAlign = 'bottom';\n        }\n      } else if (labelPosition === 'leftTop') {\n        // LeftTop side\n        x1 = points[0][0];\n        y1 = orient === 'horizontal' ? points[0][1] : points[1][1];\n        if (orient === 'horizontal') {\n          y2 = y1 - labelLineLen;\n          textY = y2 - 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 - labelLineLen;\n          textX = x2 - 5;\n          textAlign = 'right';\n        }\n      } else if (labelPosition === 'leftBottom') {\n        // LeftBottom side\n        x1 = orient === 'horizontal' ? points[1][0] : points[3][0];\n        y1 = orient === 'horizontal' ? points[1][1] : points[2][1];\n        if (orient === 'horizontal') {\n          y2 = y1 + labelLineLen;\n          textY = y2 + 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 - labelLineLen;\n          textX = x2 - 5;\n          textAlign = 'right';\n        }\n      } else {\n        // Right side or Bottom side\n        x1 = (points[1][0] + points[2][0]) / 2;\n        y1 = (points[1][1] + points[2][1]) / 2;\n        if (orient === 'horizontal') {\n          y2 = y1 + labelLineLen;\n          textY = y2 + 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 + labelLineLen;\n          textX = x2 + 5;\n          textAlign = 'left';\n        }\n      }\n      if (orient === 'horizontal') {\n        x2 = x1;\n        textX = x2;\n      } else {\n        y2 = y1;\n        textY = y2;\n      }\n      linePoints = [[x1, y1], [x2, y2]];\n    }\n    layout.label = {\n      linePoints: linePoints,\n      x: textX,\n      y: textY,\n      verticalAlign: 'middle',\n      textAlign: textAlign,\n      inside: isLabelInside\n    };\n  });\n}\nexport default function funnelLayout(ecModel, api) {\n  ecModel.eachSeriesByType('funnel', function (seriesModel) {\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var sort = seriesModel.get('sort');\n    var viewRect = getViewRect(seriesModel, api);\n    var orient = seriesModel.get('orient');\n    var viewWidth = viewRect.width;\n    var viewHeight = viewRect.height;\n    var indices = getSortedIndices(data, sort);\n    var x = viewRect.x;\n    var y = viewRect.y;\n    var sizeExtent = orient === 'horizontal' ? [parsePercent(seriesModel.get('minSize'), viewHeight), parsePercent(seriesModel.get('maxSize'), viewHeight)] : [parsePercent(seriesModel.get('minSize'), viewWidth), parsePercent(seriesModel.get('maxSize'), viewWidth)];\n    var dataExtent = data.getDataExtent(valueDim);\n    var min = seriesModel.get('min');\n    var max = seriesModel.get('max');\n    if (min == null) {\n      min = Math.min(dataExtent[0], 0);\n    }\n    if (max == null) {\n      max = dataExtent[1];\n    }\n    var funnelAlign = seriesModel.get('funnelAlign');\n    var gap = seriesModel.get('gap');\n    var viewSize = orient === 'horizontal' ? viewWidth : viewHeight;\n    var itemSize = (viewSize - gap * (data.count() - 1)) / data.count();\n    var getLinePoints = function (idx, offset) {\n      // End point index is data.count() and we assign it 0\n      if (orient === 'horizontal') {\n        var val_1 = data.get(valueDim, idx) || 0;\n        var itemHeight = linearMap(val_1, [min, max], sizeExtent, true);\n        var y0 = void 0;\n        switch (funnelAlign) {\n          case 'top':\n            y0 = y;\n            break;\n          case 'center':\n            y0 = y + (viewHeight - itemHeight) / 2;\n            break;\n          case 'bottom':\n            y0 = y + (viewHeight - itemHeight);\n            break;\n        }\n        return [[offset, y0], [offset, y0 + itemHeight]];\n      }\n      var val = data.get(valueDim, idx) || 0;\n      var itemWidth = linearMap(val, [min, max], sizeExtent, true);\n      var x0;\n      switch (funnelAlign) {\n        case 'left':\n          x0 = x;\n          break;\n        case 'center':\n          x0 = x + (viewWidth - itemWidth) / 2;\n          break;\n        case 'right':\n          x0 = x + viewWidth - itemWidth;\n          break;\n      }\n      return [[x0, offset], [x0 + itemWidth, offset]];\n    };\n    if (sort === 'ascending') {\n      // From bottom to top\n      itemSize = -itemSize;\n      gap = -gap;\n      if (orient === 'horizontal') {\n        x += viewWidth;\n      } else {\n        y += viewHeight;\n      }\n      indices = indices.reverse();\n    }\n    for (var i = 0; i < indices.length; i++) {\n      var idx = indices[i];\n      var nextIdx = indices[i + 1];\n      var itemModel = data.getItemModel(idx);\n      if (orient === 'horizontal') {\n        var width = itemModel.get(['itemStyle', 'width']);\n        if (width == null) {\n          width = itemSize;\n        } else {\n          width = parsePercent(width, viewWidth);\n          if (sort === 'ascending') {\n            width = -width;\n          }\n        }\n        var start = getLinePoints(idx, x);\n        var end = getLinePoints(nextIdx, x + width);\n        x += width + gap;\n        data.setItemLayout(idx, {\n          points: start.concat(end.slice().reverse())\n        });\n      } else {\n        var height = itemModel.get(['itemStyle', 'height']);\n        if (height == null) {\n          height = itemSize;\n        } else {\n          height = parsePercent(height, viewHeight);\n          if (sort === 'ascending') {\n            height = -height;\n          }\n        }\n        var start = getLinePoints(idx, y);\n        var end = getLinePoints(nextIdx, y + height);\n        y += height + gap;\n        data.setItemLayout(idx, {\n          points: start.concat(end.slice().reverse())\n        });\n      }\n    }\n    labelLayout(data);\n  });\n}", "map": {"version": 3, "names": ["layout", "parsePercent", "linearMap", "isFunction", "getViewRect", "seriesModel", "api", "getLayoutRect", "getBoxLayoutParams", "width", "getWidth", "height", "getHeight", "getSortedIndices", "data", "sort", "valueDim", "mapDimension", "valueArr", "mapArray", "val", "indices", "isAscending", "i", "len", "count", "a", "b", "labelLayout", "hostModel", "orient", "get", "each", "idx", "itemModel", "getItemModel", "labelModel", "getModel", "labelPosition", "labelLineModel", "getItemLayout", "points", "isLabelInside", "textAlign", "textX", "textY", "linePoints", "x1", "y1", "x2", "y2", "labelLineLen", "process", "env", "NODE_ENV", "indexOf", "console", "warn", "label", "x", "y", "verticalAlign", "inside", "funnelLayout", "ecModel", "eachSeriesByType", "getData", "viewRect", "viewWidth", "viewHeight", "sizeExtent", "dataExtent", "getDataExtent", "min", "max", "Math", "funnelAlign", "gap", "viewSize", "itemSize", "getLinePoints", "offset", "val_1", "itemHeight", "y0", "itemWidth", "x0", "reverse", "length", "nextIdx", "start", "end", "setItemLayout", "concat", "slice"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/funnel/funnelLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as layout from '../../util/layout.js';\nimport { parsePercent, linearMap } from '../../util/number.js';\nimport { isFunction } from 'zrender/lib/core/util.js';\nfunction getViewRect(seriesModel, api) {\n  return layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  });\n}\nfunction getSortedIndices(data, sort) {\n  var valueDim = data.mapDimension('value');\n  var valueArr = data.mapArray(valueDim, function (val) {\n    return val;\n  });\n  var indices = [];\n  var isAscending = sort === 'ascending';\n  for (var i = 0, len = data.count(); i < len; i++) {\n    indices[i] = i;\n  }\n  // Add custom sortable function & none sortable opetion by \"options.sort\"\n  if (isFunction(sort)) {\n    indices.sort(sort);\n  } else if (sort !== 'none') {\n    indices.sort(function (a, b) {\n      return isAscending ? valueArr[a] - valueArr[b] : valueArr[b] - valueArr[a];\n    });\n  }\n  return indices;\n}\nfunction labelLayout(data) {\n  var seriesModel = data.hostModel;\n  var orient = seriesModel.get('orient');\n  data.each(function (idx) {\n    var itemModel = data.getItemModel(idx);\n    var labelModel = itemModel.getModel('label');\n    var labelPosition = labelModel.get('position');\n    var labelLineModel = itemModel.getModel('labelLine');\n    var layout = data.getItemLayout(idx);\n    var points = layout.points;\n    var isLabelInside = labelPosition === 'inner' || labelPosition === 'inside' || labelPosition === 'center' || labelPosition === 'insideLeft' || labelPosition === 'insideRight';\n    var textAlign;\n    var textX;\n    var textY;\n    var linePoints;\n    if (isLabelInside) {\n      if (labelPosition === 'insideLeft') {\n        textX = (points[0][0] + points[3][0]) / 2 + 5;\n        textY = (points[0][1] + points[3][1]) / 2;\n        textAlign = 'left';\n      } else if (labelPosition === 'insideRight') {\n        textX = (points[1][0] + points[2][0]) / 2 - 5;\n        textY = (points[1][1] + points[2][1]) / 2;\n        textAlign = 'right';\n      } else {\n        textX = (points[0][0] + points[1][0] + points[2][0] + points[3][0]) / 4;\n        textY = (points[0][1] + points[1][1] + points[2][1] + points[3][1]) / 4;\n        textAlign = 'center';\n      }\n      linePoints = [[textX, textY], [textX, textY]];\n    } else {\n      var x1 = void 0;\n      var y1 = void 0;\n      var x2 = void 0;\n      var y2 = void 0;\n      var labelLineLen = labelLineModel.get('length');\n      if (process.env.NODE_ENV !== 'production') {\n        if (orient === 'vertical' && ['top', 'bottom'].indexOf(labelPosition) > -1) {\n          labelPosition = 'left';\n          console.warn('Position error: Funnel chart on vertical orient dose not support top and bottom.');\n        }\n        if (orient === 'horizontal' && ['left', 'right'].indexOf(labelPosition) > -1) {\n          labelPosition = 'bottom';\n          console.warn('Position error: Funnel chart on horizontal orient dose not support left and right.');\n        }\n      }\n      if (labelPosition === 'left') {\n        // Left side\n        x1 = (points[3][0] + points[0][0]) / 2;\n        y1 = (points[3][1] + points[0][1]) / 2;\n        x2 = x1 - labelLineLen;\n        textX = x2 - 5;\n        textAlign = 'right';\n      } else if (labelPosition === 'right') {\n        // Right side\n        x1 = (points[1][0] + points[2][0]) / 2;\n        y1 = (points[1][1] + points[2][1]) / 2;\n        x2 = x1 + labelLineLen;\n        textX = x2 + 5;\n        textAlign = 'left';\n      } else if (labelPosition === 'top') {\n        // Top side\n        x1 = (points[3][0] + points[0][0]) / 2;\n        y1 = (points[3][1] + points[0][1]) / 2;\n        y2 = y1 - labelLineLen;\n        textY = y2 - 5;\n        textAlign = 'center';\n      } else if (labelPosition === 'bottom') {\n        // Bottom side\n        x1 = (points[1][0] + points[2][0]) / 2;\n        y1 = (points[1][1] + points[2][1]) / 2;\n        y2 = y1 + labelLineLen;\n        textY = y2 + 5;\n        textAlign = 'center';\n      } else if (labelPosition === 'rightTop') {\n        // RightTop side\n        x1 = orient === 'horizontal' ? points[3][0] : points[1][0];\n        y1 = orient === 'horizontal' ? points[3][1] : points[1][1];\n        if (orient === 'horizontal') {\n          y2 = y1 - labelLineLen;\n          textY = y2 - 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 + labelLineLen;\n          textX = x2 + 5;\n          textAlign = 'top';\n        }\n      } else if (labelPosition === 'rightBottom') {\n        // RightBottom side\n        x1 = points[2][0];\n        y1 = points[2][1];\n        if (orient === 'horizontal') {\n          y2 = y1 + labelLineLen;\n          textY = y2 + 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 + labelLineLen;\n          textX = x2 + 5;\n          textAlign = 'bottom';\n        }\n      } else if (labelPosition === 'leftTop') {\n        // LeftTop side\n        x1 = points[0][0];\n        y1 = orient === 'horizontal' ? points[0][1] : points[1][1];\n        if (orient === 'horizontal') {\n          y2 = y1 - labelLineLen;\n          textY = y2 - 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 - labelLineLen;\n          textX = x2 - 5;\n          textAlign = 'right';\n        }\n      } else if (labelPosition === 'leftBottom') {\n        // LeftBottom side\n        x1 = orient === 'horizontal' ? points[1][0] : points[3][0];\n        y1 = orient === 'horizontal' ? points[1][1] : points[2][1];\n        if (orient === 'horizontal') {\n          y2 = y1 + labelLineLen;\n          textY = y2 + 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 - labelLineLen;\n          textX = x2 - 5;\n          textAlign = 'right';\n        }\n      } else {\n        // Right side or Bottom side\n        x1 = (points[1][0] + points[2][0]) / 2;\n        y1 = (points[1][1] + points[2][1]) / 2;\n        if (orient === 'horizontal') {\n          y2 = y1 + labelLineLen;\n          textY = y2 + 5;\n          textAlign = 'center';\n        } else {\n          x2 = x1 + labelLineLen;\n          textX = x2 + 5;\n          textAlign = 'left';\n        }\n      }\n      if (orient === 'horizontal') {\n        x2 = x1;\n        textX = x2;\n      } else {\n        y2 = y1;\n        textY = y2;\n      }\n      linePoints = [[x1, y1], [x2, y2]];\n    }\n    layout.label = {\n      linePoints: linePoints,\n      x: textX,\n      y: textY,\n      verticalAlign: 'middle',\n      textAlign: textAlign,\n      inside: isLabelInside\n    };\n  });\n}\nexport default function funnelLayout(ecModel, api) {\n  ecModel.eachSeriesByType('funnel', function (seriesModel) {\n    var data = seriesModel.getData();\n    var valueDim = data.mapDimension('value');\n    var sort = seriesModel.get('sort');\n    var viewRect = getViewRect(seriesModel, api);\n    var orient = seriesModel.get('orient');\n    var viewWidth = viewRect.width;\n    var viewHeight = viewRect.height;\n    var indices = getSortedIndices(data, sort);\n    var x = viewRect.x;\n    var y = viewRect.y;\n    var sizeExtent = orient === 'horizontal' ? [parsePercent(seriesModel.get('minSize'), viewHeight), parsePercent(seriesModel.get('maxSize'), viewHeight)] : [parsePercent(seriesModel.get('minSize'), viewWidth), parsePercent(seriesModel.get('maxSize'), viewWidth)];\n    var dataExtent = data.getDataExtent(valueDim);\n    var min = seriesModel.get('min');\n    var max = seriesModel.get('max');\n    if (min == null) {\n      min = Math.min(dataExtent[0], 0);\n    }\n    if (max == null) {\n      max = dataExtent[1];\n    }\n    var funnelAlign = seriesModel.get('funnelAlign');\n    var gap = seriesModel.get('gap');\n    var viewSize = orient === 'horizontal' ? viewWidth : viewHeight;\n    var itemSize = (viewSize - gap * (data.count() - 1)) / data.count();\n    var getLinePoints = function (idx, offset) {\n      // End point index is data.count() and we assign it 0\n      if (orient === 'horizontal') {\n        var val_1 = data.get(valueDim, idx) || 0;\n        var itemHeight = linearMap(val_1, [min, max], sizeExtent, true);\n        var y0 = void 0;\n        switch (funnelAlign) {\n          case 'top':\n            y0 = y;\n            break;\n          case 'center':\n            y0 = y + (viewHeight - itemHeight) / 2;\n            break;\n          case 'bottom':\n            y0 = y + (viewHeight - itemHeight);\n            break;\n        }\n        return [[offset, y0], [offset, y0 + itemHeight]];\n      }\n      var val = data.get(valueDim, idx) || 0;\n      var itemWidth = linearMap(val, [min, max], sizeExtent, true);\n      var x0;\n      switch (funnelAlign) {\n        case 'left':\n          x0 = x;\n          break;\n        case 'center':\n          x0 = x + (viewWidth - itemWidth) / 2;\n          break;\n        case 'right':\n          x0 = x + viewWidth - itemWidth;\n          break;\n      }\n      return [[x0, offset], [x0 + itemWidth, offset]];\n    };\n    if (sort === 'ascending') {\n      // From bottom to top\n      itemSize = -itemSize;\n      gap = -gap;\n      if (orient === 'horizontal') {\n        x += viewWidth;\n      } else {\n        y += viewHeight;\n      }\n      indices = indices.reverse();\n    }\n    for (var i = 0; i < indices.length; i++) {\n      var idx = indices[i];\n      var nextIdx = indices[i + 1];\n      var itemModel = data.getItemModel(idx);\n      if (orient === 'horizontal') {\n        var width = itemModel.get(['itemStyle', 'width']);\n        if (width == null) {\n          width = itemSize;\n        } else {\n          width = parsePercent(width, viewWidth);\n          if (sort === 'ascending') {\n            width = -width;\n          }\n        }\n        var start = getLinePoints(idx, x);\n        var end = getLinePoints(nextIdx, x + width);\n        x += width + gap;\n        data.setItemLayout(idx, {\n          points: start.concat(end.slice().reverse())\n        });\n      } else {\n        var height = itemModel.get(['itemStyle', 'height']);\n        if (height == null) {\n          height = itemSize;\n        } else {\n          height = parsePercent(height, viewHeight);\n          if (sort === 'ascending') {\n            height = -height;\n          }\n        }\n        var start = getLinePoints(idx, y);\n        var end = getLinePoints(nextIdx, y + height);\n        y += height + gap;\n        data.setItemLayout(idx, {\n          points: start.concat(end.slice().reverse())\n        });\n      }\n    }\n    labelLayout(data);\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,sBAAsB;AAC9C,SAASC,YAAY,EAAEC,SAAS,QAAQ,sBAAsB;AAC9D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,WAAWA,CAACC,WAAW,EAAEC,GAAG,EAAE;EACrC,OAAON,MAAM,CAACO,aAAa,CAACF,WAAW,CAACG,kBAAkB,CAAC,CAAC,EAAE;IAC5DC,KAAK,EAAEH,GAAG,CAACI,QAAQ,CAAC,CAAC;IACrBC,MAAM,EAAEL,GAAG,CAACM,SAAS,CAAC;EACxB,CAAC,CAAC;AACJ;AACA,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACpC,IAAIC,QAAQ,GAAGF,IAAI,CAACG,YAAY,CAAC,OAAO,CAAC;EACzC,IAAIC,QAAQ,GAAGJ,IAAI,CAACK,QAAQ,CAACH,QAAQ,EAAE,UAAUI,GAAG,EAAE;IACpD,OAAOA,GAAG;EACZ,CAAC,CAAC;EACF,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,WAAW,GAAGP,IAAI,KAAK,WAAW;EACtC,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGV,IAAI,CAACW,KAAK,CAAC,CAAC,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAChDF,OAAO,CAACE,CAAC,CAAC,GAAGA,CAAC;EAChB;EACA;EACA,IAAIpB,UAAU,CAACY,IAAI,CAAC,EAAE;IACpBM,OAAO,CAACN,IAAI,CAACA,IAAI,CAAC;EACpB,CAAC,MAAM,IAAIA,IAAI,KAAK,MAAM,EAAE;IAC1BM,OAAO,CAACN,IAAI,CAAC,UAAUW,CAAC,EAAEC,CAAC,EAAE;MAC3B,OAAOL,WAAW,GAAGJ,QAAQ,CAACQ,CAAC,CAAC,GAAGR,QAAQ,CAACS,CAAC,CAAC,GAAGT,QAAQ,CAACS,CAAC,CAAC,GAAGT,QAAQ,CAACQ,CAAC,CAAC;IAC5E,CAAC,CAAC;EACJ;EACA,OAAOL,OAAO;AAChB;AACA,SAASO,WAAWA,CAACd,IAAI,EAAE;EACzB,IAAIT,WAAW,GAAGS,IAAI,CAACe,SAAS;EAChC,IAAIC,MAAM,GAAGzB,WAAW,CAAC0B,GAAG,CAAC,QAAQ,CAAC;EACtCjB,IAAI,CAACkB,IAAI,CAAC,UAAUC,GAAG,EAAE;IACvB,IAAIC,SAAS,GAAGpB,IAAI,CAACqB,YAAY,CAACF,GAAG,CAAC;IACtC,IAAIG,UAAU,GAAGF,SAAS,CAACG,QAAQ,CAAC,OAAO,CAAC;IAC5C,IAAIC,aAAa,GAAGF,UAAU,CAACL,GAAG,CAAC,UAAU,CAAC;IAC9C,IAAIQ,cAAc,GAAGL,SAAS,CAACG,QAAQ,CAAC,WAAW,CAAC;IACpD,IAAIrC,MAAM,GAAGc,IAAI,CAAC0B,aAAa,CAACP,GAAG,CAAC;IACpC,IAAIQ,MAAM,GAAGzC,MAAM,CAACyC,MAAM;IAC1B,IAAIC,aAAa,GAAGJ,aAAa,KAAK,OAAO,IAAIA,aAAa,KAAK,QAAQ,IAAIA,aAAa,KAAK,QAAQ,IAAIA,aAAa,KAAK,YAAY,IAAIA,aAAa,KAAK,aAAa;IAC9K,IAAIK,SAAS;IACb,IAAIC,KAAK;IACT,IAAIC,KAAK;IACT,IAAIC,UAAU;IACd,IAAIJ,aAAa,EAAE;MACjB,IAAIJ,aAAa,KAAK,YAAY,EAAE;QAClCM,KAAK,GAAG,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QAC7CI,KAAK,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzCE,SAAS,GAAG,MAAM;MACpB,CAAC,MAAM,IAAIL,aAAa,KAAK,aAAa,EAAE;QAC1CM,KAAK,GAAG,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;QAC7CI,KAAK,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACzCE,SAAS,GAAG,OAAO;MACrB,CAAC,MAAM;QACLC,KAAK,GAAG,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACvEI,KAAK,GAAG,CAACJ,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACvEE,SAAS,GAAG,QAAQ;MACtB;MACAG,UAAU,GAAG,CAAC,CAACF,KAAK,EAAEC,KAAK,CAAC,EAAE,CAACD,KAAK,EAAEC,KAAK,CAAC,CAAC;IAC/C,CAAC,MAAM;MACL,IAAIE,EAAE,GAAG,KAAK,CAAC;MACf,IAAIC,EAAE,GAAG,KAAK,CAAC;MACf,IAAIC,EAAE,GAAG,KAAK,CAAC;MACf,IAAIC,EAAE,GAAG,KAAK,CAAC;MACf,IAAIC,YAAY,GAAGZ,cAAc,CAACR,GAAG,CAAC,QAAQ,CAAC;MAC/C,IAAIqB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIxB,MAAM,KAAK,UAAU,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACyB,OAAO,CAACjB,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;UAC1EA,aAAa,GAAG,MAAM;UACtBkB,OAAO,CAACC,IAAI,CAAC,kFAAkF,CAAC;QAClG;QACA,IAAI3B,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAACyB,OAAO,CAACjB,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;UAC5EA,aAAa,GAAG,QAAQ;UACxBkB,OAAO,CAACC,IAAI,CAAC,oFAAoF,CAAC;QACpG;MACF;MACA,IAAInB,aAAa,KAAK,MAAM,EAAE;QAC5B;QACAS,EAAE,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtCO,EAAE,GAAG,CAACP,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtCQ,EAAE,GAAGF,EAAE,GAAGI,YAAY;QACtBP,KAAK,GAAGK,EAAE,GAAG,CAAC;QACdN,SAAS,GAAG,OAAO;MACrB,CAAC,MAAM,IAAIL,aAAa,KAAK,OAAO,EAAE;QACpC;QACAS,EAAE,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtCO,EAAE,GAAG,CAACP,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtCQ,EAAE,GAAGF,EAAE,GAAGI,YAAY;QACtBP,KAAK,GAAGK,EAAE,GAAG,CAAC;QACdN,SAAS,GAAG,MAAM;MACpB,CAAC,MAAM,IAAIL,aAAa,KAAK,KAAK,EAAE;QAClC;QACAS,EAAE,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtCO,EAAE,GAAG,CAACP,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtCS,EAAE,GAAGF,EAAE,GAAGG,YAAY;QACtBN,KAAK,GAAGK,EAAE,GAAG,CAAC;QACdP,SAAS,GAAG,QAAQ;MACtB,CAAC,MAAM,IAAIL,aAAa,KAAK,QAAQ,EAAE;QACrC;QACAS,EAAE,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtCO,EAAE,GAAG,CAACP,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtCS,EAAE,GAAGF,EAAE,GAAGG,YAAY;QACtBN,KAAK,GAAGK,EAAE,GAAG,CAAC;QACdP,SAAS,GAAG,QAAQ;MACtB,CAAC,MAAM,IAAIL,aAAa,KAAK,UAAU,EAAE;QACvC;QACAS,EAAE,GAAGjB,MAAM,KAAK,YAAY,GAAGW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1DO,EAAE,GAAGlB,MAAM,KAAK,YAAY,GAAGW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAIX,MAAM,KAAK,YAAY,EAAE;UAC3BoB,EAAE,GAAGF,EAAE,GAAGG,YAAY;UACtBN,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdP,SAAS,GAAG,QAAQ;QACtB,CAAC,MAAM;UACLM,EAAE,GAAGF,EAAE,GAAGI,YAAY;UACtBP,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdN,SAAS,GAAG,KAAK;QACnB;MACF,CAAC,MAAM,IAAIL,aAAa,KAAK,aAAa,EAAE;QAC1C;QACAS,EAAE,GAAGN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjBO,EAAE,GAAGP,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,IAAIX,MAAM,KAAK,YAAY,EAAE;UAC3BoB,EAAE,GAAGF,EAAE,GAAGG,YAAY;UACtBN,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdP,SAAS,GAAG,QAAQ;QACtB,CAAC,MAAM;UACLM,EAAE,GAAGF,EAAE,GAAGI,YAAY;UACtBP,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdN,SAAS,GAAG,QAAQ;QACtB;MACF,CAAC,MAAM,IAAIL,aAAa,KAAK,SAAS,EAAE;QACtC;QACAS,EAAE,GAAGN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjBO,EAAE,GAAGlB,MAAM,KAAK,YAAY,GAAGW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAIX,MAAM,KAAK,YAAY,EAAE;UAC3BoB,EAAE,GAAGF,EAAE,GAAGG,YAAY;UACtBN,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdP,SAAS,GAAG,QAAQ;QACtB,CAAC,MAAM;UACLM,EAAE,GAAGF,EAAE,GAAGI,YAAY;UACtBP,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdN,SAAS,GAAG,OAAO;QACrB;MACF,CAAC,MAAM,IAAIL,aAAa,KAAK,YAAY,EAAE;QACzC;QACAS,EAAE,GAAGjB,MAAM,KAAK,YAAY,GAAGW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1DO,EAAE,GAAGlB,MAAM,KAAK,YAAY,GAAGW,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAIX,MAAM,KAAK,YAAY,EAAE;UAC3BoB,EAAE,GAAGF,EAAE,GAAGG,YAAY;UACtBN,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdP,SAAS,GAAG,QAAQ;QACtB,CAAC,MAAM;UACLM,EAAE,GAAGF,EAAE,GAAGI,YAAY;UACtBP,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdN,SAAS,GAAG,OAAO;QACrB;MACF,CAAC,MAAM;QACL;QACAI,EAAE,GAAG,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtCO,EAAE,GAAG,CAACP,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACtC,IAAIX,MAAM,KAAK,YAAY,EAAE;UAC3BoB,EAAE,GAAGF,EAAE,GAAGG,YAAY;UACtBN,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdP,SAAS,GAAG,QAAQ;QACtB,CAAC,MAAM;UACLM,EAAE,GAAGF,EAAE,GAAGI,YAAY;UACtBP,KAAK,GAAGK,EAAE,GAAG,CAAC;UACdN,SAAS,GAAG,MAAM;QACpB;MACF;MACA,IAAIb,MAAM,KAAK,YAAY,EAAE;QAC3BmB,EAAE,GAAGF,EAAE;QACPH,KAAK,GAAGK,EAAE;MACZ,CAAC,MAAM;QACLC,EAAE,GAAGF,EAAE;QACPH,KAAK,GAAGK,EAAE;MACZ;MACAJ,UAAU,GAAG,CAAC,CAACC,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC;IACnC;IACAlD,MAAM,CAAC0D,KAAK,GAAG;MACbZ,UAAU,EAAEA,UAAU;MACtBa,CAAC,EAAEf,KAAK;MACRgB,CAAC,EAAEf,KAAK;MACRgB,aAAa,EAAE,QAAQ;MACvBlB,SAAS,EAAEA,SAAS;MACpBmB,MAAM,EAAEpB;IACV,CAAC;EACH,CAAC,CAAC;AACJ;AACA,eAAe,SAASqB,YAAYA,CAACC,OAAO,EAAE1D,GAAG,EAAE;EACjD0D,OAAO,CAACC,gBAAgB,CAAC,QAAQ,EAAE,UAAU5D,WAAW,EAAE;IACxD,IAAIS,IAAI,GAAGT,WAAW,CAAC6D,OAAO,CAAC,CAAC;IAChC,IAAIlD,QAAQ,GAAGF,IAAI,CAACG,YAAY,CAAC,OAAO,CAAC;IACzC,IAAIF,IAAI,GAAGV,WAAW,CAAC0B,GAAG,CAAC,MAAM,CAAC;IAClC,IAAIoC,QAAQ,GAAG/D,WAAW,CAACC,WAAW,EAAEC,GAAG,CAAC;IAC5C,IAAIwB,MAAM,GAAGzB,WAAW,CAAC0B,GAAG,CAAC,QAAQ,CAAC;IACtC,IAAIqC,SAAS,GAAGD,QAAQ,CAAC1D,KAAK;IAC9B,IAAI4D,UAAU,GAAGF,QAAQ,CAACxD,MAAM;IAChC,IAAIU,OAAO,GAAGR,gBAAgB,CAACC,IAAI,EAAEC,IAAI,CAAC;IAC1C,IAAI4C,CAAC,GAAGQ,QAAQ,CAACR,CAAC;IAClB,IAAIC,CAAC,GAAGO,QAAQ,CAACP,CAAC;IAClB,IAAIU,UAAU,GAAGxC,MAAM,KAAK,YAAY,GAAG,CAAC7B,YAAY,CAACI,WAAW,CAAC0B,GAAG,CAAC,SAAS,CAAC,EAAEsC,UAAU,CAAC,EAAEpE,YAAY,CAACI,WAAW,CAAC0B,GAAG,CAAC,SAAS,CAAC,EAAEsC,UAAU,CAAC,CAAC,GAAG,CAACpE,YAAY,CAACI,WAAW,CAAC0B,GAAG,CAAC,SAAS,CAAC,EAAEqC,SAAS,CAAC,EAAEnE,YAAY,CAACI,WAAW,CAAC0B,GAAG,CAAC,SAAS,CAAC,EAAEqC,SAAS,CAAC,CAAC;IACpQ,IAAIG,UAAU,GAAGzD,IAAI,CAAC0D,aAAa,CAACxD,QAAQ,CAAC;IAC7C,IAAIyD,GAAG,GAAGpE,WAAW,CAAC0B,GAAG,CAAC,KAAK,CAAC;IAChC,IAAI2C,GAAG,GAAGrE,WAAW,CAAC0B,GAAG,CAAC,KAAK,CAAC;IAChC,IAAI0C,GAAG,IAAI,IAAI,EAAE;MACfA,GAAG,GAAGE,IAAI,CAACF,GAAG,CAACF,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAClC;IACA,IAAIG,GAAG,IAAI,IAAI,EAAE;MACfA,GAAG,GAAGH,UAAU,CAAC,CAAC,CAAC;IACrB;IACA,IAAIK,WAAW,GAAGvE,WAAW,CAAC0B,GAAG,CAAC,aAAa,CAAC;IAChD,IAAI8C,GAAG,GAAGxE,WAAW,CAAC0B,GAAG,CAAC,KAAK,CAAC;IAChC,IAAI+C,QAAQ,GAAGhD,MAAM,KAAK,YAAY,GAAGsC,SAAS,GAAGC,UAAU;IAC/D,IAAIU,QAAQ,GAAG,CAACD,QAAQ,GAAGD,GAAG,IAAI/D,IAAI,CAACW,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAIX,IAAI,CAACW,KAAK,CAAC,CAAC;IACnE,IAAIuD,aAAa,GAAG,SAAAA,CAAU/C,GAAG,EAAEgD,MAAM,EAAE;MACzC;MACA,IAAInD,MAAM,KAAK,YAAY,EAAE;QAC3B,IAAIoD,KAAK,GAAGpE,IAAI,CAACiB,GAAG,CAACf,QAAQ,EAAEiB,GAAG,CAAC,IAAI,CAAC;QACxC,IAAIkD,UAAU,GAAGjF,SAAS,CAACgF,KAAK,EAAE,CAACT,GAAG,EAAEC,GAAG,CAAC,EAAEJ,UAAU,EAAE,IAAI,CAAC;QAC/D,IAAIc,EAAE,GAAG,KAAK,CAAC;QACf,QAAQR,WAAW;UACjB,KAAK,KAAK;YACRQ,EAAE,GAAGxB,CAAC;YACN;UACF,KAAK,QAAQ;YACXwB,EAAE,GAAGxB,CAAC,GAAG,CAACS,UAAU,GAAGc,UAAU,IAAI,CAAC;YACtC;UACF,KAAK,QAAQ;YACXC,EAAE,GAAGxB,CAAC,IAAIS,UAAU,GAAGc,UAAU,CAAC;YAClC;QACJ;QACA,OAAO,CAAC,CAACF,MAAM,EAAEG,EAAE,CAAC,EAAE,CAACH,MAAM,EAAEG,EAAE,GAAGD,UAAU,CAAC,CAAC;MAClD;MACA,IAAI/D,GAAG,GAAGN,IAAI,CAACiB,GAAG,CAACf,QAAQ,EAAEiB,GAAG,CAAC,IAAI,CAAC;MACtC,IAAIoD,SAAS,GAAGnF,SAAS,CAACkB,GAAG,EAAE,CAACqD,GAAG,EAAEC,GAAG,CAAC,EAAEJ,UAAU,EAAE,IAAI,CAAC;MAC5D,IAAIgB,EAAE;MACN,QAAQV,WAAW;QACjB,KAAK,MAAM;UACTU,EAAE,GAAG3B,CAAC;UACN;QACF,KAAK,QAAQ;UACX2B,EAAE,GAAG3B,CAAC,GAAG,CAACS,SAAS,GAAGiB,SAAS,IAAI,CAAC;UACpC;QACF,KAAK,OAAO;UACVC,EAAE,GAAG3B,CAAC,GAAGS,SAAS,GAAGiB,SAAS;UAC9B;MACJ;MACA,OAAO,CAAC,CAACC,EAAE,EAAEL,MAAM,CAAC,EAAE,CAACK,EAAE,GAAGD,SAAS,EAAEJ,MAAM,CAAC,CAAC;IACjD,CAAC;IACD,IAAIlE,IAAI,KAAK,WAAW,EAAE;MACxB;MACAgE,QAAQ,GAAG,CAACA,QAAQ;MACpBF,GAAG,GAAG,CAACA,GAAG;MACV,IAAI/C,MAAM,KAAK,YAAY,EAAE;QAC3B6B,CAAC,IAAIS,SAAS;MAChB,CAAC,MAAM;QACLR,CAAC,IAAIS,UAAU;MACjB;MACAhD,OAAO,GAAGA,OAAO,CAACkE,OAAO,CAAC,CAAC;IAC7B;IACA,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACmE,MAAM,EAAEjE,CAAC,EAAE,EAAE;MACvC,IAAIU,GAAG,GAAGZ,OAAO,CAACE,CAAC,CAAC;MACpB,IAAIkE,OAAO,GAAGpE,OAAO,CAACE,CAAC,GAAG,CAAC,CAAC;MAC5B,IAAIW,SAAS,GAAGpB,IAAI,CAACqB,YAAY,CAACF,GAAG,CAAC;MACtC,IAAIH,MAAM,KAAK,YAAY,EAAE;QAC3B,IAAIrB,KAAK,GAAGyB,SAAS,CAACH,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACjD,IAAItB,KAAK,IAAI,IAAI,EAAE;UACjBA,KAAK,GAAGsE,QAAQ;QAClB,CAAC,MAAM;UACLtE,KAAK,GAAGR,YAAY,CAACQ,KAAK,EAAE2D,SAAS,CAAC;UACtC,IAAIrD,IAAI,KAAK,WAAW,EAAE;YACxBN,KAAK,GAAG,CAACA,KAAK;UAChB;QACF;QACA,IAAIiF,KAAK,GAAGV,aAAa,CAAC/C,GAAG,EAAE0B,CAAC,CAAC;QACjC,IAAIgC,GAAG,GAAGX,aAAa,CAACS,OAAO,EAAE9B,CAAC,GAAGlD,KAAK,CAAC;QAC3CkD,CAAC,IAAIlD,KAAK,GAAGoE,GAAG;QAChB/D,IAAI,CAAC8E,aAAa,CAAC3D,GAAG,EAAE;UACtBQ,MAAM,EAAEiD,KAAK,CAACG,MAAM,CAACF,GAAG,CAACG,KAAK,CAAC,CAAC,CAACP,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI5E,MAAM,GAAGuB,SAAS,CAACH,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QACnD,IAAIpB,MAAM,IAAI,IAAI,EAAE;UAClBA,MAAM,GAAGoE,QAAQ;QACnB,CAAC,MAAM;UACLpE,MAAM,GAAGV,YAAY,CAACU,MAAM,EAAE0D,UAAU,CAAC;UACzC,IAAItD,IAAI,KAAK,WAAW,EAAE;YACxBJ,MAAM,GAAG,CAACA,MAAM;UAClB;QACF;QACA,IAAI+E,KAAK,GAAGV,aAAa,CAAC/C,GAAG,EAAE2B,CAAC,CAAC;QACjC,IAAI+B,GAAG,GAAGX,aAAa,CAACS,OAAO,EAAE7B,CAAC,GAAGjD,MAAM,CAAC;QAC5CiD,CAAC,IAAIjD,MAAM,GAAGkE,GAAG;QACjB/D,IAAI,CAAC8E,aAAa,CAAC3D,GAAG,EAAE;UACtBQ,MAAM,EAAEiD,KAAK,CAACG,MAAM,CAACF,GAAG,CAACG,KAAK,CAAC,CAAC,CAACP,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC;MACJ;IACF;IACA3D,WAAW,CAACd,IAAI,CAAC;EACnB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}