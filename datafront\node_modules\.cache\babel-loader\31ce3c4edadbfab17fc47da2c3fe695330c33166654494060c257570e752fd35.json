{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { quantile, asc } from '../../util/number.js';\nimport { isFunction, isString } from 'zrender/lib/core/util.js';\n/**\r\n * See:\r\n *  <https://en.wikipedia.org/wiki/Box_plot#cite_note-frigge_hoaglin_iglewicz-2>\r\n *  <http://stat.ethz.ch/R-manual/R-devel/library/grDevices/html/boxplot.stats.html>\r\n *\r\n * Helper method for preparing data.\r\n *\r\n * @param rawData like\r\n *        [\r\n *            [12,232,443], (raw data set for the first box)\r\n *            [3843,5545,1232], (raw data set for the second box)\r\n *            ...\r\n *        ]\r\n * @param opt.boundIQR=1.5 Data less than min bound is outlier.\r\n *      default 1.5, means Q1 - 1.5 * (Q3 - Q1).\r\n *      If 'none'/0 passed, min bound will not be used.\r\n */\nexport default function prepareBoxplotData(rawData, opt) {\n  opt = opt || {};\n  var boxData = [];\n  var outliers = [];\n  var boundIQR = opt.boundIQR;\n  var useExtreme = boundIQR === 'none' || boundIQR === 0;\n  for (var i = 0; i < rawData.length; i++) {\n    var ascList = asc(rawData[i].slice());\n    var Q1 = quantile(ascList, 0.25);\n    var Q2 = quantile(ascList, 0.5);\n    var Q3 = quantile(ascList, 0.75);\n    var min = ascList[0];\n    var max = ascList[ascList.length - 1];\n    var bound = (boundIQR == null ? 1.5 : boundIQR) * (Q3 - Q1);\n    var low = useExtreme ? min : Math.max(min, Q1 - bound);\n    var high = useExtreme ? max : Math.min(max, Q3 + bound);\n    var itemNameFormatter = opt.itemNameFormatter;\n    var itemName = isFunction(itemNameFormatter) ? itemNameFormatter({\n      value: i\n    }) : isString(itemNameFormatter) ? itemNameFormatter.replace('{value}', i + '') : i + '';\n    boxData.push([itemName, low, Q1, Q2, Q3, high]);\n    for (var j = 0; j < ascList.length; j++) {\n      var dataItem = ascList[j];\n      if (dataItem < low || dataItem > high) {\n        var outlier = [itemName, dataItem];\n        outliers.push(outlier);\n      }\n    }\n  }\n  return {\n    boxData: boxData,\n    outliers: outliers\n  };\n}", "map": {"version": 3, "names": ["quantile", "asc", "isFunction", "isString", "prepareBoxplotData", "rawData", "opt", "boxData", "outliers", "boundIQR", "useExtreme", "i", "length", "ascList", "slice", "Q1", "Q2", "Q3", "min", "max", "bound", "low", "Math", "high", "itemNameFormatter", "itemName", "value", "replace", "push", "j", "dataItem", "outlier"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/boxplot/prepareBoxplotData.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { quantile, asc } from '../../util/number.js';\nimport { isFunction, isString } from 'zrender/lib/core/util.js';\n/**\r\n * See:\r\n *  <https://en.wikipedia.org/wiki/Box_plot#cite_note-frigge_hoaglin_iglewicz-2>\r\n *  <http://stat.ethz.ch/R-manual/R-devel/library/grDevices/html/boxplot.stats.html>\r\n *\r\n * Helper method for preparing data.\r\n *\r\n * @param rawData like\r\n *        [\r\n *            [12,232,443], (raw data set for the first box)\r\n *            [3843,5545,1232], (raw data set for the second box)\r\n *            ...\r\n *        ]\r\n * @param opt.boundIQR=1.5 Data less than min bound is outlier.\r\n *      default 1.5, means Q1 - 1.5 * (Q3 - Q1).\r\n *      If 'none'/0 passed, min bound will not be used.\r\n */\nexport default function prepareBoxplotData(rawData, opt) {\n  opt = opt || {};\n  var boxData = [];\n  var outliers = [];\n  var boundIQR = opt.boundIQR;\n  var useExtreme = boundIQR === 'none' || boundIQR === 0;\n  for (var i = 0; i < rawData.length; i++) {\n    var ascList = asc(rawData[i].slice());\n    var Q1 = quantile(ascList, 0.25);\n    var Q2 = quantile(ascList, 0.5);\n    var Q3 = quantile(ascList, 0.75);\n    var min = ascList[0];\n    var max = ascList[ascList.length - 1];\n    var bound = (boundIQR == null ? 1.5 : boundIQR) * (Q3 - Q1);\n    var low = useExtreme ? min : Math.max(min, Q1 - bound);\n    var high = useExtreme ? max : Math.min(max, Q3 + bound);\n    var itemNameFormatter = opt.itemNameFormatter;\n    var itemName = isFunction(itemNameFormatter) ? itemNameFormatter({\n      value: i\n    }) : isString(itemNameFormatter) ? itemNameFormatter.replace('{value}', i + '') : i + '';\n    boxData.push([itemName, low, Q1, Q2, Q3, high]);\n    for (var j = 0; j < ascList.length; j++) {\n      var dataItem = ascList[j];\n      if (dataItem < low || dataItem > high) {\n        var outlier = [itemName, dataItem];\n        outliers.push(outlier);\n      }\n    }\n  }\n  return {\n    boxData: boxData,\n    outliers: outliers\n  };\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQ,EAAEC,GAAG,QAAQ,sBAAsB;AACpD,SAASC,UAAU,EAAEC,QAAQ,QAAQ,0BAA0B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,GAAG,EAAE;EACvDA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,QAAQ,GAAGH,GAAG,CAACG,QAAQ;EAC3B,IAAIC,UAAU,GAAGD,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,CAAC;EACtD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,OAAO,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIE,OAAO,GAAGZ,GAAG,CAACI,OAAO,CAACM,CAAC,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;IACrC,IAAIC,EAAE,GAAGf,QAAQ,CAACa,OAAO,EAAE,IAAI,CAAC;IAChC,IAAIG,EAAE,GAAGhB,QAAQ,CAACa,OAAO,EAAE,GAAG,CAAC;IAC/B,IAAII,EAAE,GAAGjB,QAAQ,CAACa,OAAO,EAAE,IAAI,CAAC;IAChC,IAAIK,GAAG,GAAGL,OAAO,CAAC,CAAC,CAAC;IACpB,IAAIM,GAAG,GAAGN,OAAO,CAACA,OAAO,CAACD,MAAM,GAAG,CAAC,CAAC;IACrC,IAAIQ,KAAK,GAAG,CAACX,QAAQ,IAAI,IAAI,GAAG,GAAG,GAAGA,QAAQ,KAAKQ,EAAE,GAAGF,EAAE,CAAC;IAC3D,IAAIM,GAAG,GAAGX,UAAU,GAAGQ,GAAG,GAAGI,IAAI,CAACH,GAAG,CAACD,GAAG,EAAEH,EAAE,GAAGK,KAAK,CAAC;IACtD,IAAIG,IAAI,GAAGb,UAAU,GAAGS,GAAG,GAAGG,IAAI,CAACJ,GAAG,CAACC,GAAG,EAAEF,EAAE,GAAGG,KAAK,CAAC;IACvD,IAAII,iBAAiB,GAAGlB,GAAG,CAACkB,iBAAiB;IAC7C,IAAIC,QAAQ,GAAGvB,UAAU,CAACsB,iBAAiB,CAAC,GAAGA,iBAAiB,CAAC;MAC/DE,KAAK,EAAEf;IACT,CAAC,CAAC,GAAGR,QAAQ,CAACqB,iBAAiB,CAAC,GAAGA,iBAAiB,CAACG,OAAO,CAAC,SAAS,EAAEhB,CAAC,GAAG,EAAE,CAAC,GAAGA,CAAC,GAAG,EAAE;IACxFJ,OAAO,CAACqB,IAAI,CAAC,CAACH,QAAQ,EAAEJ,GAAG,EAAEN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEM,IAAI,CAAC,CAAC;IAC/C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,OAAO,CAACD,MAAM,EAAEiB,CAAC,EAAE,EAAE;MACvC,IAAIC,QAAQ,GAAGjB,OAAO,CAACgB,CAAC,CAAC;MACzB,IAAIC,QAAQ,GAAGT,GAAG,IAAIS,QAAQ,GAAGP,IAAI,EAAE;QACrC,IAAIQ,OAAO,GAAG,CAACN,QAAQ,EAAEK,QAAQ,CAAC;QAClCtB,QAAQ,CAACoB,IAAI,CAACG,OAAO,CAAC;MACxB;IACF;EACF;EACA,OAAO;IACLxB,OAAO,EAAEA,OAAO;IAChBC,QAAQ,EAAEA;EACZ,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}