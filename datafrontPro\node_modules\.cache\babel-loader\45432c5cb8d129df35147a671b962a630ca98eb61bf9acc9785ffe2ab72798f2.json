{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.typed-array.with.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/* global Int32Array */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from '../model/Model.js';\nimport DataDiffer from './DataDiffer.js';\nimport { DefaultDataProvider } from './helper/dataProvider.js';\nimport { summarizeDimensions } from './helper/dimensionHelper.js';\nimport SeriesDimensionDefine from './SeriesDimensionDefine.js';\nimport { SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ORIGINAL } from '../util/types.js';\nimport { convertOptionIdName, isDataItemOption } from '../util/model.js';\nimport { setCommonECData } from '../util/innerStore.js';\nimport { isSourceInstance } from './Source.js';\nimport DataStore from './DataStore.js';\nimport { isSeriesDataSchema } from './helper/SeriesDataSchema.js';\nvar isObject = zrUtil.isObject;\nvar map = zrUtil.map;\nvar CtorInt32Array = typeof Int32Array === 'undefined' ? Array : Int32Array;\n// Use prefix to avoid index to be the same as otherIdList[idx],\n// which will cause weird update animation.\nvar ID_PREFIX = 'e\\0\\0';\nvar INDEX_NOT_FOUND = -1;\n// type SeriesDimensionIndex = DimensionIndex;\nvar TRANSFERABLE_PROPERTIES = ['hasItemOption', '_nameList', '_idList', '_invertedIndicesMap', '_dimSummary', 'userOutput', '_rawData', '_dimValueGetter', '_nameDimIdx', '_idDimIdx', '_nameRepeatCount'];\nvar CLONE_PROPERTIES = ['_approximateExtent'];\n// -----------------------------\n// Internal method declarations:\n// -----------------------------\nvar prepareInvertedIndex;\nvar getId;\nvar getIdNameFromStore;\nvar normalizeDimensions;\nvar transferProperties;\nvar cloneListForMapAndSample;\nvar makeIdFromName;\nvar SeriesData = /** @class */function () {\n  /**\r\n   * @param dimensionsInput.dimensions\r\n   *        For example, ['someDimName', {name: 'someDimName', type: 'someDimType'}, ...].\r\n   *        Dimensions should be concrete names like x, y, z, lng, lat, angle, radius\r\n   */\n  function SeriesData(dimensionsInput, hostModel) {\n    this.type = 'list';\n    this._dimOmitted = false;\n    this._nameList = [];\n    this._idList = [];\n    // Models of data option is stored sparse for optimizing memory cost\n    // Never used yet (not used yet).\n    // private _optionModels: Model[] = [];\n    // Global visual properties after visual coding\n    this._visual = {};\n    // Global layout properties.\n    this._layout = {};\n    // Item visual properties after visual coding\n    this._itemVisuals = [];\n    // Item layout properties after layout\n    this._itemLayouts = [];\n    // Graphic elements\n    this._graphicEls = [];\n    // key: dim, value: extent\n    this._approximateExtent = {};\n    this._calculationInfo = {};\n    // Having detected that there is data item is non primitive type\n    // (in type `OptionDataItemObject`).\n    // Like `data: [ { value: xx, itemStyle: {...} }, ...]`\n    // At present it only happen in `SOURCE_FORMAT_ORIGINAL`.\n    this.hasItemOption = false;\n    // Methods that create a new list based on this list should be listed here.\n    // Notice that those method should `RETURN` the new list.\n    this.TRANSFERABLE_METHODS = ['cloneShallow', 'downSample', 'minmaxDownSample', 'lttbDownSample', 'map'];\n    // Methods that change indices of this list should be listed here.\n    this.CHANGABLE_METHODS = ['filterSelf', 'selectRange'];\n    this.DOWNSAMPLE_METHODS = ['downSample', 'minmaxDownSample', 'lttbDownSample'];\n    var dimensions;\n    var assignStoreDimIdx = false;\n    if (isSeriesDataSchema(dimensionsInput)) {\n      dimensions = dimensionsInput.dimensions;\n      this._dimOmitted = dimensionsInput.isDimensionOmitted();\n      this._schema = dimensionsInput;\n    } else {\n      assignStoreDimIdx = true;\n      dimensions = dimensionsInput;\n    }\n    dimensions = dimensions || ['x', 'y'];\n    var dimensionInfos = {};\n    var dimensionNames = [];\n    var invertedIndicesMap = {};\n    var needsHasOwn = false;\n    var emptyObj = {};\n    for (var i = 0; i < dimensions.length; i++) {\n      // Use the original dimensions[i], where other flag props may exists.\n      var dimInfoInput = dimensions[i];\n      var dimensionInfo = zrUtil.isString(dimInfoInput) ? new SeriesDimensionDefine({\n        name: dimInfoInput\n      }) : !(dimInfoInput instanceof SeriesDimensionDefine) ? new SeriesDimensionDefine(dimInfoInput) : dimInfoInput;\n      var dimensionName = dimensionInfo.name;\n      dimensionInfo.type = dimensionInfo.type || 'float';\n      if (!dimensionInfo.coordDim) {\n        dimensionInfo.coordDim = dimensionName;\n        dimensionInfo.coordDimIndex = 0;\n      }\n      var otherDims = dimensionInfo.otherDims = dimensionInfo.otherDims || {};\n      dimensionNames.push(dimensionName);\n      dimensionInfos[dimensionName] = dimensionInfo;\n      if (emptyObj[dimensionName] != null) {\n        needsHasOwn = true;\n      }\n      if (dimensionInfo.createInvertedIndices) {\n        invertedIndicesMap[dimensionName] = [];\n      }\n      if (otherDims.itemName === 0) {\n        this._nameDimIdx = i;\n      }\n      if (otherDims.itemId === 0) {\n        this._idDimIdx = i;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.assert(assignStoreDimIdx || dimensionInfo.storeDimIndex >= 0);\n      }\n      if (assignStoreDimIdx) {\n        dimensionInfo.storeDimIndex = i;\n      }\n    }\n    this.dimensions = dimensionNames;\n    this._dimInfos = dimensionInfos;\n    this._initGetDimensionInfo(needsHasOwn);\n    this.hostModel = hostModel;\n    this._invertedIndicesMap = invertedIndicesMap;\n    if (this._dimOmitted) {\n      var dimIdxToName_1 = this._dimIdxToName = zrUtil.createHashMap();\n      zrUtil.each(dimensionNames, function (dimName) {\n        dimIdxToName_1.set(dimensionInfos[dimName].storeDimIndex, dimName);\n      });\n    }\n  }\n  /**\r\n   *\r\n   * Get concrete dimension name by dimension name or dimension index.\r\n   * If input a dimension name, do not validate whether the dimension name exits.\r\n   *\r\n   * @caution\r\n   * @param dim Must make sure the dimension is `SeriesDimensionLoose`.\r\n   * Because only those dimensions will have auto-generated dimension names if not\r\n   * have a user-specified name, and other dimensions will get a return of null/undefined.\r\n   *\r\n   * @notice Because of this reason, should better use `getDimensionIndex` instead, for examples:\r\n   * ```js\r\n   * const val = data.getStore().get(data.getDimensionIndex(dim), dataIdx);\r\n   * ```\r\n   *\r\n   * @return Concrete dim name.\r\n   */\n  SeriesData.prototype.getDimension = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx == null) {\n      return dim;\n    }\n    dimIdx = dim;\n    if (!this._dimOmitted) {\n      return this.dimensions[dimIdx];\n    }\n    // Retrieve from series dimension definition because it probably contains\n    // generated dimension name (like 'x', 'y').\n    var dimName = this._dimIdxToName.get(dimIdx);\n    if (dimName != null) {\n      return dimName;\n    }\n    var sourceDimDef = this._schema.getSourceDimension(dimIdx);\n    if (sourceDimDef) {\n      return sourceDimDef.name;\n    }\n  };\n  /**\r\n   * Get dimension index in data store. Return -1 if not found.\r\n   * Can be used to index value from getRawValue.\r\n   */\n  SeriesData.prototype.getDimensionIndex = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx != null) {\n      return dimIdx;\n    }\n    if (dim == null) {\n      return -1;\n    }\n    var dimInfo = this._getDimInfo(dim);\n    return dimInfo ? dimInfo.storeDimIndex : this._dimOmitted ? this._schema.getSourceDimensionIndex(dim) : -1;\n  };\n  /**\r\n   * The meanings of the input parameter `dim`:\r\n   *\r\n   * + If dim is a number (e.g., `1`), it means the index of the dimension.\r\n   *   For example, `getDimension(0)` will return 'x' or 'lng' or 'radius'.\r\n   * + If dim is a number-like string (e.g., `\"1\"`):\r\n   *     + If there is the same concrete dim name defined in `series.dimensions` or `dataset.dimensions`,\r\n   *        it means that concrete name.\r\n   *     + If not, it will be converted to a number, which means the index of the dimension.\r\n   *        (why? because of the backward compatibility. We have been tolerating number-like string in\r\n   *        dimension setting, although now it seems that it is not a good idea.)\r\n   *     For example, `visualMap[i].dimension: \"1\"` is the same meaning as `visualMap[i].dimension: 1`,\r\n   *     if no dimension name is defined as `\"1\"`.\r\n   * + If dim is a not-number-like string, it means the concrete dim name.\r\n   *   For example, it can be be default name `\"x\"`, `\"y\"`, `\"z\"`, `\"lng\"`, `\"lat\"`, `\"angle\"`, `\"radius\"`,\r\n   *   or customized in `dimensions` property of option like `\"age\"`.\r\n   *\r\n   * @return recognized `DimensionIndex`. Otherwise return null/undefined (means that dim is `DimensionName`).\r\n   */\n  SeriesData.prototype._recognizeDimIndex = function (dim) {\n    if (zrUtil.isNumber(dim)\n    // If being a number-like string but not being defined as a dimension name.\n    || dim != null && !isNaN(dim) && !this._getDimInfo(dim) && (!this._dimOmitted || this._schema.getSourceDimensionIndex(dim) < 0)) {\n      return +dim;\n    }\n  };\n  SeriesData.prototype._getStoreDimIndex = function (dim) {\n    var dimIdx = this.getDimensionIndex(dim);\n    if (process.env.NODE_ENV !== 'production') {\n      if (dimIdx == null) {\n        throw new Error('Unknown dimension ' + dim);\n      }\n    }\n    return dimIdx;\n  };\n  /**\r\n   * Get type and calculation info of particular dimension\r\n   * @param dim\r\n   *        Dimension can be concrete names like x, y, z, lng, lat, angle, radius\r\n   *        Or a ordinal number. For example getDimensionInfo(0) will return 'x' or 'lng' or 'radius'\r\n   */\n  SeriesData.prototype.getDimensionInfo = function (dim) {\n    // Do not clone, because there may be categories in dimInfo.\n    return this._getDimInfo(this.getDimension(dim));\n  };\n  SeriesData.prototype._initGetDimensionInfo = function (needsHasOwn) {\n    var dimensionInfos = this._dimInfos;\n    this._getDimInfo = needsHasOwn ? function (dimName) {\n      return dimensionInfos.hasOwnProperty(dimName) ? dimensionInfos[dimName] : undefined;\n    } : function (dimName) {\n      return dimensionInfos[dimName];\n    };\n  };\n  /**\r\n   * concrete dimension name list on coord.\r\n   */\n  SeriesData.prototype.getDimensionsOnCoord = function () {\n    return this._dimSummary.dataDimsOnCoord.slice();\n  };\n  SeriesData.prototype.mapDimension = function (coordDim, idx) {\n    var dimensionsSummary = this._dimSummary;\n    if (idx == null) {\n      return dimensionsSummary.encodeFirstDimNotExtra[coordDim];\n    }\n    var dims = dimensionsSummary.encode[coordDim];\n    return dims ? dims[idx] : null;\n  };\n  SeriesData.prototype.mapDimensionsAll = function (coordDim) {\n    var dimensionsSummary = this._dimSummary;\n    var dims = dimensionsSummary.encode[coordDim];\n    return (dims || []).slice();\n  };\n  SeriesData.prototype.getStore = function () {\n    return this._store;\n  };\n  /**\r\n   * Initialize from data\r\n   * @param data source or data or data store.\r\n   * @param nameList The name of a datum is used on data diff and\r\n   *        default label/tooltip.\r\n   *        A name can be specified in encode.itemName,\r\n   *        or dataItem.name (only for series option data),\r\n   *        or provided in nameList from outside.\r\n   */\n  SeriesData.prototype.initData = function (data, nameList, dimValueGetter) {\n    var _this = this;\n    var store;\n    if (data instanceof DataStore) {\n      store = data;\n    }\n    if (!store) {\n      var dimensions = this.dimensions;\n      var provider = isSourceInstance(data) || zrUtil.isArrayLike(data) ? new DefaultDataProvider(data, dimensions.length) : data;\n      store = new DataStore();\n      var dimensionInfos = map(dimensions, function (dimName) {\n        return {\n          type: _this._dimInfos[dimName].type,\n          property: dimName\n        };\n      });\n      store.initData(provider, dimensionInfos, dimValueGetter);\n    }\n    this._store = store;\n    // Reset\n    this._nameList = (nameList || []).slice();\n    this._idList = [];\n    this._nameRepeatCount = {};\n    this._doInit(0, store.count());\n    // Cache summary info for fast visit. See \"dimensionHelper\".\n    // Needs to be initialized after store is prepared.\n    this._dimSummary = summarizeDimensions(this, this._schema);\n    this.userOutput = this._dimSummary.userOutput;\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   */\n  SeriesData.prototype.appendData = function (data) {\n    var range = this._store.appendData(data);\n    this._doInit(range[0], range[1]);\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   * This method does not modify `rawData` (`dataProvider`), but only\r\n   * add values to store.\r\n   *\r\n   * The final count will be increased by `Math.max(values.length, names.length)`.\r\n   *\r\n   * @param values That is the SourceType: 'arrayRows', like\r\n   *        [\r\n   *            [12, 33, 44],\r\n   *            [NaN, 43, 1],\r\n   *            ['-', 'asdf', 0]\r\n   *        ]\r\n   *        Each item is exactly corresponding to a dimension.\r\n   */\n  SeriesData.prototype.appendValues = function (values, names) {\n    var _a = this._store.appendValues(values, names && names.length),\n      start = _a.start,\n      end = _a.end;\n    var shouldMakeIdFromName = this._shouldMakeIdFromName();\n    this._updateOrdinalMeta();\n    if (names) {\n      for (var idx = start; idx < end; idx++) {\n        var sourceIdx = idx - start;\n        this._nameList[idx] = names[sourceIdx];\n        if (shouldMakeIdFromName) {\n          makeIdFromName(this, idx);\n        }\n      }\n    }\n  };\n  SeriesData.prototype._updateOrdinalMeta = function () {\n    var store = this._store;\n    var dimensions = this.dimensions;\n    for (var i = 0; i < dimensions.length; i++) {\n      var dimInfo = this._dimInfos[dimensions[i]];\n      if (dimInfo.ordinalMeta) {\n        store.collectOrdinalMeta(dimInfo.storeDimIndex, dimInfo.ordinalMeta);\n      }\n    }\n  };\n  SeriesData.prototype._shouldMakeIdFromName = function () {\n    var provider = this._store.getProvider();\n    return this._idDimIdx == null && provider.getSource().sourceFormat !== SOURCE_FORMAT_TYPED_ARRAY && !provider.fillStorage;\n  };\n  SeriesData.prototype._doInit = function (start, end) {\n    if (start >= end) {\n      return;\n    }\n    var store = this._store;\n    var provider = store.getProvider();\n    this._updateOrdinalMeta();\n    var nameList = this._nameList;\n    var idList = this._idList;\n    var sourceFormat = provider.getSource().sourceFormat;\n    var isFormatOriginal = sourceFormat === SOURCE_FORMAT_ORIGINAL;\n    // Each data item is value\n    // [1, 2]\n    // 2\n    // Bar chart, line chart which uses category axis\n    // only gives the 'y' value. 'x' value is the indices of category\n    // Use a tempValue to normalize the value to be a (x, y) value\n    // If dataItem is {name: ...} or {id: ...}, it has highest priority.\n    // This kind of ids and names are always stored `_nameList` and `_idList`.\n    if (isFormatOriginal && !provider.pure) {\n      var sharedDataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        var dataItem = provider.getItem(idx, sharedDataItem);\n        if (!this.hasItemOption && isDataItemOption(dataItem)) {\n          this.hasItemOption = true;\n        }\n        if (dataItem) {\n          var itemName = dataItem.name;\n          if (nameList[idx] == null && itemName != null) {\n            nameList[idx] = convertOptionIdName(itemName, null);\n          }\n          var itemId = dataItem.id;\n          if (idList[idx] == null && itemId != null) {\n            idList[idx] = convertOptionIdName(itemId, null);\n          }\n        }\n      }\n    }\n    if (this._shouldMakeIdFromName()) {\n      for (var idx = start; idx < end; idx++) {\n        makeIdFromName(this, idx);\n      }\n    }\n    prepareInvertedIndex(this);\n  };\n  /**\r\n   * PENDING: In fact currently this function is only used to short-circuit\r\n   * the calling of `scale.unionExtentFromData` when data have been filtered by modules\r\n   * like \"dataZoom\". `scale.unionExtentFromData` is used to calculate data extent for series on\r\n   * an axis, but if a \"axis related data filter module\" is used, the extent of the axis have\r\n   * been fixed and no need to calling `scale.unionExtentFromData` actually.\r\n   * But if we add \"custom data filter\" in future, which is not \"axis related\", this method may\r\n   * be still needed.\r\n   *\r\n   * Optimize for the scenario that data is filtered by a given extent.\r\n   * Consider that if data amount is more than hundreds of thousand,\r\n   * extent calculation will cost more than 10ms and the cache will\r\n   * be erased because of the filtering.\r\n   */\n  SeriesData.prototype.getApproximateExtent = function (dim) {\n    return this._approximateExtent[dim] || this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  /**\r\n   * Calculate extent on a filtered data might be time consuming.\r\n   * Approximate extent is only used for: calculate extent of filtered data outside.\r\n   */\n  SeriesData.prototype.setApproximateExtent = function (extent, dim) {\n    dim = this.getDimension(dim);\n    this._approximateExtent[dim] = extent.slice();\n  };\n  SeriesData.prototype.getCalculationInfo = function (key) {\n    return this._calculationInfo[key];\n  };\n  SeriesData.prototype.setCalculationInfo = function (key, value) {\n    isObject(key) ? zrUtil.extend(this._calculationInfo, key) : this._calculationInfo[key] = value;\n  };\n  /**\r\n   * @return Never be null/undefined. `number` will be converted to string. Because:\r\n   * In most cases, name is used in display, where returning a string is more convenient.\r\n   * In other cases, name is used in query (see `indexOfName`), where we can keep the\r\n   * rule that name `2` equals to name `'2'`.\r\n   */\n  SeriesData.prototype.getName = function (idx) {\n    var rawIndex = this.getRawIndex(idx);\n    var name = this._nameList[rawIndex];\n    if (name == null && this._nameDimIdx != null) {\n      name = getIdNameFromStore(this, this._nameDimIdx, rawIndex);\n    }\n    if (name == null) {\n      name = '';\n    }\n    return name;\n  };\n  SeriesData.prototype._getCategory = function (dimIdx, idx) {\n    var ordinal = this._store.get(dimIdx, idx);\n    var ordinalMeta = this._store.getOrdinalMeta(dimIdx);\n    if (ordinalMeta) {\n      return ordinalMeta.categories[ordinal];\n    }\n    return ordinal;\n  };\n  /**\r\n   * @return Never null/undefined. `number` will be converted to string. Because:\r\n   * In all cases having encountered at present, id is used in making diff comparison, which\r\n   * are usually based on hash map. We can keep the rule that the internal id are always string\r\n   * (treat `2` is the same as `'2'`) to make the related logic simple.\r\n   */\n  SeriesData.prototype.getId = function (idx) {\n    return getId(this, this.getRawIndex(idx));\n  };\n  SeriesData.prototype.count = function () {\n    return this._store.count();\n  };\n  /**\r\n   * Get value. Return NaN if idx is out of range.\r\n   *\r\n   * @notice Should better to use `data.getStore().get(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.get = function (dim, idx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.get(dimInfo.storeDimIndex, idx);\n    }\n  };\n  /**\r\n   * @notice Should better to use `data.getStore().getByRawIndex(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.getByRawIndex = function (dim, rawIdx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.getByRawIndex(dimInfo.storeDimIndex, rawIdx);\n    }\n  };\n  SeriesData.prototype.getIndices = function () {\n    return this._store.getIndices();\n  };\n  SeriesData.prototype.getDataExtent = function (dim) {\n    return this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getSum = function (dim) {\n    return this._store.getSum(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getMedian = function (dim) {\n    return this._store.getMedian(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getValues = function (dimensions, idx) {\n    var _this = this;\n    var store = this._store;\n    return zrUtil.isArray(dimensions) ? store.getValues(map(dimensions, function (dim) {\n      return _this._getStoreDimIndex(dim);\n    }), idx) : store.getValues(dimensions);\n  };\n  /**\r\n   * If value is NaN. Including '-'\r\n   * Only check the coord dimensions.\r\n   */\n  SeriesData.prototype.hasValue = function (idx) {\n    var dataDimIndicesOnCoord = this._dimSummary.dataDimIndicesOnCoord;\n    for (var i = 0, len = dataDimIndicesOnCoord.length; i < len; i++) {\n      // Ordinal type originally can be string or number.\n      // But when an ordinal type is used on coord, it can\n      // not be string but only number. So we can also use isNaN.\n      if (isNaN(this._store.get(dataDimIndicesOnCoord[i], idx))) {\n        return false;\n      }\n    }\n    return true;\n  };\n  /**\r\n   * Retrieve the index with given name\r\n   */\n  SeriesData.prototype.indexOfName = function (name) {\n    for (var i = 0, len = this._store.count(); i < len; i++) {\n      if (this.getName(i) === name) {\n        return i;\n      }\n    }\n    return -1;\n  };\n  SeriesData.prototype.getRawIndex = function (idx) {\n    return this._store.getRawIndex(idx);\n  };\n  SeriesData.prototype.indexOfRawIndex = function (rawIndex) {\n    return this._store.indexOfRawIndex(rawIndex);\n  };\n  /**\r\n   * Only support the dimension which inverted index created.\r\n   * Do not support other cases until required.\r\n   * @param dim concrete dim\r\n   * @param value ordinal index\r\n   * @return rawIndex\r\n   */\n  SeriesData.prototype.rawIndexOf = function (dim, value) {\n    var invertedIndices = dim && this._invertedIndicesMap[dim];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!invertedIndices) {\n        throw new Error('Do not supported yet');\n      }\n    }\n    var rawIndex = invertedIndices && invertedIndices[value];\n    if (rawIndex == null || isNaN(rawIndex)) {\n      return INDEX_NOT_FOUND;\n    }\n    return rawIndex;\n  };\n  /**\r\n   * Retrieve the index of nearest value\r\n   * @param dim\r\n   * @param value\r\n   * @param [maxDistance=Infinity]\r\n   * @return If and only if multiple indices has\r\n   *         the same value, they are put to the result.\r\n   */\n  SeriesData.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    return this._store.indicesOfNearest(this._getStoreDimIndex(dim), value, maxDistance);\n  };\n  SeriesData.prototype.each = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store.each(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  SeriesData.prototype.filterSelf = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store = this._store.filter(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return this;\n  };\n  /**\r\n   * Select data in range. (For optimization of filter)\r\n   * (Manually inline code, support 5 million data filtering in data zoom.)\r\n   */\n  SeriesData.prototype.selectRange = function (range) {\n    'use strict';\n\n    var _this = this;\n    var innerRange = {};\n    var dims = zrUtil.keys(range);\n    var dimIndices = [];\n    zrUtil.each(dims, function (dim) {\n      var dimIdx = _this._getStoreDimIndex(dim);\n      innerRange[dimIdx] = range[dim];\n      dimIndices.push(dimIdx);\n    });\n    this._store = this._store.selectRange(innerRange);\n    return this;\n  };\n  /* eslint-enable max-len */\n  SeriesData.prototype.mapArray = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    ctx = ctx || this;\n    var result = [];\n    this.each(dims, function () {\n      result.push(cb && cb.apply(this, arguments));\n    }, ctx);\n    return result;\n  };\n  SeriesData.prototype.map = function (dims, cb, ctx, ctxCompat) {\n    'use strict';\n\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.map(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return list;\n  };\n  SeriesData.prototype.modify = function (dims, cb, ctx, ctxCompat) {\n    var _this = this;\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.each(normalizeDimensions(dims), function (dim) {\n        var dimInfo = _this.getDimensionInfo(dim);\n        if (!dimInfo.isCalculationCoord) {\n          console.error('Danger: only stack dimension can be modified');\n        }\n      });\n    }\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    // If do shallow clone here, if there are too many stacked series,\n    // it still cost lots of memory, because `_store.dimensions` are not shared.\n    // We should consider there probably be shallow clone happen in each series\n    // in consequent filter/map.\n    this._store.modify(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  /**\r\n   * Large data down sampling on given dimension\r\n   * @param sampleIndex Sample index for name and id\r\n   */\n  SeriesData.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.downSample(this._getStoreDimIndex(dimension), rate, sampleValue, sampleIndex);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using min-max\r\n   * @param {string} valueDimension\r\n   * @param {number} rate\r\n   */\n  SeriesData.prototype.minmaxDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.minmaxDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using largest-triangle-three-buckets\r\n   * @param {string} valueDimension\r\n   * @param {number} targetCount\r\n   */\n  SeriesData.prototype.lttbDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.lttbDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  SeriesData.prototype.getRawDataItem = function (idx) {\n    return this._store.getRawDataItem(idx);\n  };\n  /**\r\n   * Get model of one data item.\r\n   */\n  // TODO: Type of data item\n  SeriesData.prototype.getItemModel = function (idx) {\n    var hostModel = this.hostModel;\n    var dataItem = this.getRawDataItem(idx);\n    return new Model(dataItem, hostModel, hostModel && hostModel.ecModel);\n  };\n  /**\r\n   * Create a data differ\r\n   */\n  SeriesData.prototype.diff = function (otherList) {\n    var thisList = this;\n    return new DataDiffer(otherList ? otherList.getStore().getIndices() : [], this.getStore().getIndices(), function (idx) {\n      return getId(otherList, idx);\n    }, function (idx) {\n      return getId(thisList, idx);\n    });\n  };\n  /**\r\n   * Get visual property.\r\n   */\n  SeriesData.prototype.getVisual = function (key) {\n    var visual = this._visual;\n    return visual && visual[key];\n  };\n  SeriesData.prototype.setVisual = function (kvObj, val) {\n    this._visual = this._visual || {};\n    if (isObject(kvObj)) {\n      zrUtil.extend(this._visual, kvObj);\n    } else {\n      this._visual[kvObj] = val;\n    }\n  };\n  /**\r\n   * Get visual property of single data item\r\n   */\n  // eslint-disable-next-line\n  SeriesData.prototype.getItemVisual = function (idx, key) {\n    var itemVisual = this._itemVisuals[idx];\n    var val = itemVisual && itemVisual[key];\n    if (val == null) {\n      // Use global visual property\n      return this.getVisual(key);\n    }\n    return val;\n  };\n  /**\r\n   * If exists visual property of single data item\r\n   */\n  SeriesData.prototype.hasItemVisual = function () {\n    return this._itemVisuals.length > 0;\n  };\n  /**\r\n   * Make sure itemVisual property is unique\r\n   */\n  // TODO: use key to save visual to reduce memory.\n  SeriesData.prototype.ensureUniqueItemVisual = function (idx, key) {\n    var itemVisuals = this._itemVisuals;\n    var itemVisual = itemVisuals[idx];\n    if (!itemVisual) {\n      itemVisual = itemVisuals[idx] = {};\n    }\n    var val = itemVisual[key];\n    if (val == null) {\n      val = this.getVisual(key);\n      // TODO Performance?\n      if (zrUtil.isArray(val)) {\n        val = val.slice();\n      } else if (isObject(val)) {\n        val = zrUtil.extend({}, val);\n      }\n      itemVisual[key] = val;\n    }\n    return val;\n  };\n  // eslint-disable-next-line\n  SeriesData.prototype.setItemVisual = function (idx, key, value) {\n    var itemVisual = this._itemVisuals[idx] || {};\n    this._itemVisuals[idx] = itemVisual;\n    if (isObject(key)) {\n      zrUtil.extend(itemVisual, key);\n    } else {\n      itemVisual[key] = value;\n    }\n  };\n  /**\r\n   * Clear itemVisuals and list visual.\r\n   */\n  SeriesData.prototype.clearAllVisual = function () {\n    this._visual = {};\n    this._itemVisuals = [];\n  };\n  SeriesData.prototype.setLayout = function (key, val) {\n    isObject(key) ? zrUtil.extend(this._layout, key) : this._layout[key] = val;\n  };\n  /**\r\n   * Get layout property.\r\n   */\n  SeriesData.prototype.getLayout = function (key) {\n    return this._layout[key];\n  };\n  /**\r\n   * Get layout of single data item\r\n   */\n  SeriesData.prototype.getItemLayout = function (idx) {\n    return this._itemLayouts[idx];\n  };\n  /**\r\n   * Set layout of single data item\r\n   */\n  SeriesData.prototype.setItemLayout = function (idx, layout, merge) {\n    this._itemLayouts[idx] = merge ? zrUtil.extend(this._itemLayouts[idx] || {}, layout) : layout;\n  };\n  /**\r\n   * Clear all layout of single data item\r\n   */\n  SeriesData.prototype.clearItemLayouts = function () {\n    this._itemLayouts.length = 0;\n  };\n  /**\r\n   * Set graphic element relative to data. It can be set as null\r\n   */\n  SeriesData.prototype.setItemGraphicEl = function (idx, el) {\n    var seriesIndex = this.hostModel && this.hostModel.seriesIndex;\n    setCommonECData(seriesIndex, this.dataType, idx, el);\n    this._graphicEls[idx] = el;\n  };\n  SeriesData.prototype.getItemGraphicEl = function (idx) {\n    return this._graphicEls[idx];\n  };\n  SeriesData.prototype.eachItemGraphicEl = function (cb, context) {\n    zrUtil.each(this._graphicEls, function (el, idx) {\n      if (el) {\n        cb && cb.call(context, el, idx);\n      }\n    });\n  };\n  /**\r\n   * Shallow clone a new list except visual and layout properties, and graph elements.\r\n   * New list only change the indices.\r\n   */\n  SeriesData.prototype.cloneShallow = function (list) {\n    if (!list) {\n      list = new SeriesData(this._schema ? this._schema : map(this.dimensions, this._getDimInfo, this), this.hostModel);\n    }\n    transferProperties(list, this);\n    list._store = this._store;\n    return list;\n  };\n  /**\r\n   * Wrap some method to add more feature\r\n   */\n  SeriesData.prototype.wrapMethod = function (methodName, injectFunction) {\n    var originalMethod = this[methodName];\n    if (!zrUtil.isFunction(originalMethod)) {\n      return;\n    }\n    this.__wrappedMethods = this.__wrappedMethods || [];\n    this.__wrappedMethods.push(methodName);\n    this[methodName] = function () {\n      var res = originalMethod.apply(this, arguments);\n      return injectFunction.apply(this, [res].concat(zrUtil.slice(arguments)));\n    };\n  };\n  // ----------------------------------------------------------\n  // A work around for internal method visiting private member.\n  // ----------------------------------------------------------\n  SeriesData.internalField = function () {\n    prepareInvertedIndex = function (data) {\n      var invertedIndicesMap = data._invertedIndicesMap;\n      zrUtil.each(invertedIndicesMap, function (invertedIndices, dim) {\n        var dimInfo = data._dimInfos[dim];\n        // Currently, only dimensions that has ordinalMeta can create inverted indices.\n        var ordinalMeta = dimInfo.ordinalMeta;\n        var store = data._store;\n        if (ordinalMeta) {\n          invertedIndices = invertedIndicesMap[dim] = new CtorInt32Array(ordinalMeta.categories.length);\n          // The default value of TypedArray is 0. To avoid miss\n          // mapping to 0, we should set it as INDEX_NOT_FOUND.\n          for (var i = 0; i < invertedIndices.length; i++) {\n            invertedIndices[i] = INDEX_NOT_FOUND;\n          }\n          for (var i = 0; i < store.count(); i++) {\n            // Only support the case that all values are distinct.\n            invertedIndices[store.get(dimInfo.storeDimIndex, i)] = i;\n          }\n        }\n      });\n    };\n    getIdNameFromStore = function (data, dimIdx, idx) {\n      return convertOptionIdName(data._getCategory(dimIdx, idx), null);\n    };\n    /**\r\n     * @see the comment of `List['getId']`.\r\n     */\n    getId = function (data, rawIndex) {\n      var id = data._idList[rawIndex];\n      if (id == null && data._idDimIdx != null) {\n        id = getIdNameFromStore(data, data._idDimIdx, rawIndex);\n      }\n      if (id == null) {\n        id = ID_PREFIX + rawIndex;\n      }\n      return id;\n    };\n    normalizeDimensions = function (dimensions) {\n      if (!zrUtil.isArray(dimensions)) {\n        dimensions = dimensions != null ? [dimensions] : [];\n      }\n      return dimensions;\n    };\n    /**\r\n     * Data in excludeDimensions is copied, otherwise transferred.\r\n     */\n    cloneListForMapAndSample = function (original) {\n      var list = new SeriesData(original._schema ? original._schema : map(original.dimensions, original._getDimInfo, original), original.hostModel);\n      // FIXME If needs stackedOn, value may already been stacked\n      transferProperties(list, original);\n      return list;\n    };\n    transferProperties = function (target, source) {\n      zrUtil.each(TRANSFERABLE_PROPERTIES.concat(source.__wrappedMethods || []), function (propName) {\n        if (source.hasOwnProperty(propName)) {\n          target[propName] = source[propName];\n        }\n      });\n      target.__wrappedMethods = source.__wrappedMethods;\n      zrUtil.each(CLONE_PROPERTIES, function (propName) {\n        target[propName] = zrUtil.clone(source[propName]);\n      });\n      target._calculationInfo = zrUtil.extend({}, source._calculationInfo);\n    };\n    makeIdFromName = function (data, idx) {\n      var nameList = data._nameList;\n      var idList = data._idList;\n      var nameDimIdx = data._nameDimIdx;\n      var idDimIdx = data._idDimIdx;\n      var name = nameList[idx];\n      var id = idList[idx];\n      if (name == null && nameDimIdx != null) {\n        nameList[idx] = name = getIdNameFromStore(data, nameDimIdx, idx);\n      }\n      if (id == null && idDimIdx != null) {\n        idList[idx] = id = getIdNameFromStore(data, idDimIdx, idx);\n      }\n      if (id == null && name != null) {\n        var nameRepeatCount = data._nameRepeatCount;\n        var nmCnt = nameRepeatCount[name] = (nameRepeatCount[name] || 0) + 1;\n        id = name;\n        if (nmCnt > 1) {\n          id += '__ec__' + nmCnt;\n        }\n        idList[idx] = id;\n      }\n    };\n  }();\n  return SeriesData;\n}();\nexport default SeriesData;", "map": {"version": 3, "names": ["zrUtil", "Model", "<PERSON><PERSON><PERSON><PERSON>", "DefaultDataProvider", "summarizeDimensions", "SeriesDimensionDefine", "SOURCE_FORMAT_TYPED_ARRAY", "SOURCE_FORMAT_ORIGINAL", "convertOptionIdName", "isDataItemOption", "setCommonECData", "isSourceInstance", "DataStore", "isSeriesDataSchema", "isObject", "map", "CtorInt32Array", "Int32Array", "Array", "ID_PREFIX", "INDEX_NOT_FOUND", "TRANSFERABLE_PROPERTIES", "CLONE_PROPERTIES", "prepareInvertedIndex", "getId", "getIdNameFromStore", "normalizeDimensions", "transferProperties", "cloneListForMapAndSample", "makeIdFromName", "SeriesData", "dimensionsInput", "hostModel", "type", "_dimOmitted", "_nameList", "_idList", "_visual", "_layout", "_itemVisuals", "_itemLayouts", "_graphicEls", "_approximateExtent", "_calculationInfo", "hasItemOption", "TRANSFERABLE_METHODS", "CHANGABLE_METHODS", "DOWNSAMPLE_METHODS", "dimensions", "assignStoreDimIdx", "isDimensionOmitted", "_schema", "dimensionInfos", "dimensionNames", "invertedIndicesMap", "needsHasOwn", "emptyObj", "i", "length", "dimInfoInput", "dimensionInfo", "isString", "name", "dimensionName", "coordDim", "coordDimIndex", "otherDims", "push", "createInvertedIndices", "itemName", "_nameDimIdx", "itemId", "_idDimIdx", "process", "env", "NODE_ENV", "assert", "storeDimIndex", "_dimInfos", "_initGetDimensionInfo", "_invertedIndicesMap", "dimIdxToName_1", "_dimIdxToName", "createHashMap", "each", "dimName", "set", "prototype", "getDimension", "dim", "dimIdx", "_recognizeDimIndex", "get", "sourceDimDef", "getSourceDimension", "getDimensionIndex", "dimInfo", "_getDimInfo", "getSourceDimensionIndex", "isNumber", "isNaN", "_getStoreDimIndex", "Error", "getDimensionInfo", "hasOwnProperty", "undefined", "getDimensionsOnCoord", "_dimS<PERSON>mary", "dataDimsOnCoord", "slice", "mapDimension", "idx", "dimensionsSummary", "encodeFirstDimNotExtra", "dims", "encode", "mapDimensionsAll", "getStore", "_store", "initData", "data", "nameList", "dimValueGetter", "_this", "store", "provider", "isArrayLike", "property", "_nameRepeatCount", "_doInit", "count", "userOutput", "appendData", "range", "append<PERSON>al<PERSON>", "values", "names", "_a", "start", "end", "shouldMakeIdFromName", "_shouldMakeIdFromName", "_updateOrdinalMeta", "sourceIdx", "ordinalMeta", "collectOrdinalMeta", "get<PERSON><PERSON><PERSON>", "getSource", "sourceFormat", "fillStorage", "idList", "isFormatOriginal", "pure", "sharedDataItem", "dataItem", "getItem", "id", "getApproximateExtent", "getDataExtent", "setApproximateExtent", "extent", "getCalculationInfo", "key", "setCalculationInfo", "value", "extend", "getName", "rawIndex", "getRawIndex", "_getCategory", "ordinal", "getOrdinalMeta", "categories", "getByRawIndex", "rawIdx", "getIndices", "getSum", "getMedian", "getV<PERSON>ues", "isArray", "hasValue", "dataDimIndicesOnCoord", "len", "indexOfName", "indexOfRawIndex", "rawIndexOf", "invertedIndices", "indicesOfNearest", "maxDistance", "cb", "ctx", "isFunction", "fCtx", "dimIndices", "bind", "filterSelf", "filter", "selectRange", "innerRange", "keys", "mapArray", "result", "apply", "arguments", "ctxCompat", "list", "modify", "isCalculationCoord", "console", "error", "downSample", "dimension", "rate", "sampleValue", "sampleIndex", "minmaxDownSample", "valueDimension", "lttbDownSample", "getRawDataItem", "getItemModel", "ecModel", "diff", "otherList", "thisList", "getVisual", "visual", "setVisual", "kvObj", "val", "getItemVisual", "itemVisual", "hasItemVisual", "ensureUniqueItemVisual", "itemVisuals", "setItemVisual", "clearAllVisual", "setLayout", "getLayout", "getItemLayout", "setItemLayout", "layout", "merge", "clearItemLayouts", "setItemGraphicEl", "el", "seriesIndex", "dataType", "getItemGraphicEl", "eachItemGraphicEl", "context", "call", "cloneShallow", "wrapMethod", "methodName", "injectFunction", "originalMethod", "__wrapped<PERSON>eth<PERSON>", "res", "concat", "internalField", "original", "target", "source", "propName", "clone", "nameDimIdx", "idDimIdx", "nameRepeatCount", "nmCnt"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/data/SeriesData.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/* global Int32Array */\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from '../model/Model.js';\nimport DataDiffer from './DataDiffer.js';\nimport { DefaultDataProvider } from './helper/dataProvider.js';\nimport { summarizeDimensions } from './helper/dimensionHelper.js';\nimport SeriesDimensionDefine from './SeriesDimensionDefine.js';\nimport { SOURCE_FORMAT_TYPED_ARRAY, SOURCE_FORMAT_ORIGINAL } from '../util/types.js';\nimport { convertOptionIdName, isDataItemOption } from '../util/model.js';\nimport { setCommonECData } from '../util/innerStore.js';\nimport { isSourceInstance } from './Source.js';\nimport DataStore from './DataStore.js';\nimport { isSeriesDataSchema } from './helper/SeriesDataSchema.js';\nvar isObject = zrUtil.isObject;\nvar map = zrUtil.map;\nvar CtorInt32Array = typeof Int32Array === 'undefined' ? Array : Int32Array;\n// Use prefix to avoid index to be the same as otherIdList[idx],\n// which will cause weird update animation.\nvar ID_PREFIX = 'e\\0\\0';\nvar INDEX_NOT_FOUND = -1;\n// type SeriesDimensionIndex = DimensionIndex;\nvar TRANSFERABLE_PROPERTIES = ['hasItemOption', '_nameList', '_idList', '_invertedIndicesMap', '_dimSummary', 'userOutput', '_rawData', '_dimValueGetter', '_nameDimIdx', '_idDimIdx', '_nameRepeatCount'];\nvar CLONE_PROPERTIES = ['_approximateExtent'];\n// -----------------------------\n// Internal method declarations:\n// -----------------------------\nvar prepareInvertedIndex;\nvar getId;\nvar getIdNameFromStore;\nvar normalizeDimensions;\nvar transferProperties;\nvar cloneListForMapAndSample;\nvar makeIdFromName;\nvar SeriesData = /** @class */function () {\n  /**\r\n   * @param dimensionsInput.dimensions\r\n   *        For example, ['someDimName', {name: 'someDimName', type: 'someDimType'}, ...].\r\n   *        Dimensions should be concrete names like x, y, z, lng, lat, angle, radius\r\n   */\n  function SeriesData(dimensionsInput, hostModel) {\n    this.type = 'list';\n    this._dimOmitted = false;\n    this._nameList = [];\n    this._idList = [];\n    // Models of data option is stored sparse for optimizing memory cost\n    // Never used yet (not used yet).\n    // private _optionModels: Model[] = [];\n    // Global visual properties after visual coding\n    this._visual = {};\n    // Global layout properties.\n    this._layout = {};\n    // Item visual properties after visual coding\n    this._itemVisuals = [];\n    // Item layout properties after layout\n    this._itemLayouts = [];\n    // Graphic elements\n    this._graphicEls = [];\n    // key: dim, value: extent\n    this._approximateExtent = {};\n    this._calculationInfo = {};\n    // Having detected that there is data item is non primitive type\n    // (in type `OptionDataItemObject`).\n    // Like `data: [ { value: xx, itemStyle: {...} }, ...]`\n    // At present it only happen in `SOURCE_FORMAT_ORIGINAL`.\n    this.hasItemOption = false;\n    // Methods that create a new list based on this list should be listed here.\n    // Notice that those method should `RETURN` the new list.\n    this.TRANSFERABLE_METHODS = ['cloneShallow', 'downSample', 'minmaxDownSample', 'lttbDownSample', 'map'];\n    // Methods that change indices of this list should be listed here.\n    this.CHANGABLE_METHODS = ['filterSelf', 'selectRange'];\n    this.DOWNSAMPLE_METHODS = ['downSample', 'minmaxDownSample', 'lttbDownSample'];\n    var dimensions;\n    var assignStoreDimIdx = false;\n    if (isSeriesDataSchema(dimensionsInput)) {\n      dimensions = dimensionsInput.dimensions;\n      this._dimOmitted = dimensionsInput.isDimensionOmitted();\n      this._schema = dimensionsInput;\n    } else {\n      assignStoreDimIdx = true;\n      dimensions = dimensionsInput;\n    }\n    dimensions = dimensions || ['x', 'y'];\n    var dimensionInfos = {};\n    var dimensionNames = [];\n    var invertedIndicesMap = {};\n    var needsHasOwn = false;\n    var emptyObj = {};\n    for (var i = 0; i < dimensions.length; i++) {\n      // Use the original dimensions[i], where other flag props may exists.\n      var dimInfoInput = dimensions[i];\n      var dimensionInfo = zrUtil.isString(dimInfoInput) ? new SeriesDimensionDefine({\n        name: dimInfoInput\n      }) : !(dimInfoInput instanceof SeriesDimensionDefine) ? new SeriesDimensionDefine(dimInfoInput) : dimInfoInput;\n      var dimensionName = dimensionInfo.name;\n      dimensionInfo.type = dimensionInfo.type || 'float';\n      if (!dimensionInfo.coordDim) {\n        dimensionInfo.coordDim = dimensionName;\n        dimensionInfo.coordDimIndex = 0;\n      }\n      var otherDims = dimensionInfo.otherDims = dimensionInfo.otherDims || {};\n      dimensionNames.push(dimensionName);\n      dimensionInfos[dimensionName] = dimensionInfo;\n      if (emptyObj[dimensionName] != null) {\n        needsHasOwn = true;\n      }\n      if (dimensionInfo.createInvertedIndices) {\n        invertedIndicesMap[dimensionName] = [];\n      }\n      if (otherDims.itemName === 0) {\n        this._nameDimIdx = i;\n      }\n      if (otherDims.itemId === 0) {\n        this._idDimIdx = i;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        zrUtil.assert(assignStoreDimIdx || dimensionInfo.storeDimIndex >= 0);\n      }\n      if (assignStoreDimIdx) {\n        dimensionInfo.storeDimIndex = i;\n      }\n    }\n    this.dimensions = dimensionNames;\n    this._dimInfos = dimensionInfos;\n    this._initGetDimensionInfo(needsHasOwn);\n    this.hostModel = hostModel;\n    this._invertedIndicesMap = invertedIndicesMap;\n    if (this._dimOmitted) {\n      var dimIdxToName_1 = this._dimIdxToName = zrUtil.createHashMap();\n      zrUtil.each(dimensionNames, function (dimName) {\n        dimIdxToName_1.set(dimensionInfos[dimName].storeDimIndex, dimName);\n      });\n    }\n  }\n  /**\r\n   *\r\n   * Get concrete dimension name by dimension name or dimension index.\r\n   * If input a dimension name, do not validate whether the dimension name exits.\r\n   *\r\n   * @caution\r\n   * @param dim Must make sure the dimension is `SeriesDimensionLoose`.\r\n   * Because only those dimensions will have auto-generated dimension names if not\r\n   * have a user-specified name, and other dimensions will get a return of null/undefined.\r\n   *\r\n   * @notice Because of this reason, should better use `getDimensionIndex` instead, for examples:\r\n   * ```js\r\n   * const val = data.getStore().get(data.getDimensionIndex(dim), dataIdx);\r\n   * ```\r\n   *\r\n   * @return Concrete dim name.\r\n   */\n  SeriesData.prototype.getDimension = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx == null) {\n      return dim;\n    }\n    dimIdx = dim;\n    if (!this._dimOmitted) {\n      return this.dimensions[dimIdx];\n    }\n    // Retrieve from series dimension definition because it probably contains\n    // generated dimension name (like 'x', 'y').\n    var dimName = this._dimIdxToName.get(dimIdx);\n    if (dimName != null) {\n      return dimName;\n    }\n    var sourceDimDef = this._schema.getSourceDimension(dimIdx);\n    if (sourceDimDef) {\n      return sourceDimDef.name;\n    }\n  };\n  /**\r\n   * Get dimension index in data store. Return -1 if not found.\r\n   * Can be used to index value from getRawValue.\r\n   */\n  SeriesData.prototype.getDimensionIndex = function (dim) {\n    var dimIdx = this._recognizeDimIndex(dim);\n    if (dimIdx != null) {\n      return dimIdx;\n    }\n    if (dim == null) {\n      return -1;\n    }\n    var dimInfo = this._getDimInfo(dim);\n    return dimInfo ? dimInfo.storeDimIndex : this._dimOmitted ? this._schema.getSourceDimensionIndex(dim) : -1;\n  };\n  /**\r\n   * The meanings of the input parameter `dim`:\r\n   *\r\n   * + If dim is a number (e.g., `1`), it means the index of the dimension.\r\n   *   For example, `getDimension(0)` will return 'x' or 'lng' or 'radius'.\r\n   * + If dim is a number-like string (e.g., `\"1\"`):\r\n   *     + If there is the same concrete dim name defined in `series.dimensions` or `dataset.dimensions`,\r\n   *        it means that concrete name.\r\n   *     + If not, it will be converted to a number, which means the index of the dimension.\r\n   *        (why? because of the backward compatibility. We have been tolerating number-like string in\r\n   *        dimension setting, although now it seems that it is not a good idea.)\r\n   *     For example, `visualMap[i].dimension: \"1\"` is the same meaning as `visualMap[i].dimension: 1`,\r\n   *     if no dimension name is defined as `\"1\"`.\r\n   * + If dim is a not-number-like string, it means the concrete dim name.\r\n   *   For example, it can be be default name `\"x\"`, `\"y\"`, `\"z\"`, `\"lng\"`, `\"lat\"`, `\"angle\"`, `\"radius\"`,\r\n   *   or customized in `dimensions` property of option like `\"age\"`.\r\n   *\r\n   * @return recognized `DimensionIndex`. Otherwise return null/undefined (means that dim is `DimensionName`).\r\n   */\n  SeriesData.prototype._recognizeDimIndex = function (dim) {\n    if (zrUtil.isNumber(dim)\n    // If being a number-like string but not being defined as a dimension name.\n    || dim != null && !isNaN(dim) && !this._getDimInfo(dim) && (!this._dimOmitted || this._schema.getSourceDimensionIndex(dim) < 0)) {\n      return +dim;\n    }\n  };\n  SeriesData.prototype._getStoreDimIndex = function (dim) {\n    var dimIdx = this.getDimensionIndex(dim);\n    if (process.env.NODE_ENV !== 'production') {\n      if (dimIdx == null) {\n        throw new Error('Unknown dimension ' + dim);\n      }\n    }\n    return dimIdx;\n  };\n  /**\r\n   * Get type and calculation info of particular dimension\r\n   * @param dim\r\n   *        Dimension can be concrete names like x, y, z, lng, lat, angle, radius\r\n   *        Or a ordinal number. For example getDimensionInfo(0) will return 'x' or 'lng' or 'radius'\r\n   */\n  SeriesData.prototype.getDimensionInfo = function (dim) {\n    // Do not clone, because there may be categories in dimInfo.\n    return this._getDimInfo(this.getDimension(dim));\n  };\n  SeriesData.prototype._initGetDimensionInfo = function (needsHasOwn) {\n    var dimensionInfos = this._dimInfos;\n    this._getDimInfo = needsHasOwn ? function (dimName) {\n      return dimensionInfos.hasOwnProperty(dimName) ? dimensionInfos[dimName] : undefined;\n    } : function (dimName) {\n      return dimensionInfos[dimName];\n    };\n  };\n  /**\r\n   * concrete dimension name list on coord.\r\n   */\n  SeriesData.prototype.getDimensionsOnCoord = function () {\n    return this._dimSummary.dataDimsOnCoord.slice();\n  };\n  SeriesData.prototype.mapDimension = function (coordDim, idx) {\n    var dimensionsSummary = this._dimSummary;\n    if (idx == null) {\n      return dimensionsSummary.encodeFirstDimNotExtra[coordDim];\n    }\n    var dims = dimensionsSummary.encode[coordDim];\n    return dims ? dims[idx] : null;\n  };\n  SeriesData.prototype.mapDimensionsAll = function (coordDim) {\n    var dimensionsSummary = this._dimSummary;\n    var dims = dimensionsSummary.encode[coordDim];\n    return (dims || []).slice();\n  };\n  SeriesData.prototype.getStore = function () {\n    return this._store;\n  };\n  /**\r\n   * Initialize from data\r\n   * @param data source or data or data store.\r\n   * @param nameList The name of a datum is used on data diff and\r\n   *        default label/tooltip.\r\n   *        A name can be specified in encode.itemName,\r\n   *        or dataItem.name (only for series option data),\r\n   *        or provided in nameList from outside.\r\n   */\n  SeriesData.prototype.initData = function (data, nameList, dimValueGetter) {\n    var _this = this;\n    var store;\n    if (data instanceof DataStore) {\n      store = data;\n    }\n    if (!store) {\n      var dimensions = this.dimensions;\n      var provider = isSourceInstance(data) || zrUtil.isArrayLike(data) ? new DefaultDataProvider(data, dimensions.length) : data;\n      store = new DataStore();\n      var dimensionInfos = map(dimensions, function (dimName) {\n        return {\n          type: _this._dimInfos[dimName].type,\n          property: dimName\n        };\n      });\n      store.initData(provider, dimensionInfos, dimValueGetter);\n    }\n    this._store = store;\n    // Reset\n    this._nameList = (nameList || []).slice();\n    this._idList = [];\n    this._nameRepeatCount = {};\n    this._doInit(0, store.count());\n    // Cache summary info for fast visit. See \"dimensionHelper\".\n    // Needs to be initialized after store is prepared.\n    this._dimSummary = summarizeDimensions(this, this._schema);\n    this.userOutput = this._dimSummary.userOutput;\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   */\n  SeriesData.prototype.appendData = function (data) {\n    var range = this._store.appendData(data);\n    this._doInit(range[0], range[1]);\n  };\n  /**\r\n   * Caution: Can be only called on raw data (before `this._indices` created).\r\n   * This method does not modify `rawData` (`dataProvider`), but only\r\n   * add values to store.\r\n   *\r\n   * The final count will be increased by `Math.max(values.length, names.length)`.\r\n   *\r\n   * @param values That is the SourceType: 'arrayRows', like\r\n   *        [\r\n   *            [12, 33, 44],\r\n   *            [NaN, 43, 1],\r\n   *            ['-', 'asdf', 0]\r\n   *        ]\r\n   *        Each item is exactly corresponding to a dimension.\r\n   */\n  SeriesData.prototype.appendValues = function (values, names) {\n    var _a = this._store.appendValues(values, names && names.length),\n      start = _a.start,\n      end = _a.end;\n    var shouldMakeIdFromName = this._shouldMakeIdFromName();\n    this._updateOrdinalMeta();\n    if (names) {\n      for (var idx = start; idx < end; idx++) {\n        var sourceIdx = idx - start;\n        this._nameList[idx] = names[sourceIdx];\n        if (shouldMakeIdFromName) {\n          makeIdFromName(this, idx);\n        }\n      }\n    }\n  };\n  SeriesData.prototype._updateOrdinalMeta = function () {\n    var store = this._store;\n    var dimensions = this.dimensions;\n    for (var i = 0; i < dimensions.length; i++) {\n      var dimInfo = this._dimInfos[dimensions[i]];\n      if (dimInfo.ordinalMeta) {\n        store.collectOrdinalMeta(dimInfo.storeDimIndex, dimInfo.ordinalMeta);\n      }\n    }\n  };\n  SeriesData.prototype._shouldMakeIdFromName = function () {\n    var provider = this._store.getProvider();\n    return this._idDimIdx == null && provider.getSource().sourceFormat !== SOURCE_FORMAT_TYPED_ARRAY && !provider.fillStorage;\n  };\n  SeriesData.prototype._doInit = function (start, end) {\n    if (start >= end) {\n      return;\n    }\n    var store = this._store;\n    var provider = store.getProvider();\n    this._updateOrdinalMeta();\n    var nameList = this._nameList;\n    var idList = this._idList;\n    var sourceFormat = provider.getSource().sourceFormat;\n    var isFormatOriginal = sourceFormat === SOURCE_FORMAT_ORIGINAL;\n    // Each data item is value\n    // [1, 2]\n    // 2\n    // Bar chart, line chart which uses category axis\n    // only gives the 'y' value. 'x' value is the indices of category\n    // Use a tempValue to normalize the value to be a (x, y) value\n    // If dataItem is {name: ...} or {id: ...}, it has highest priority.\n    // This kind of ids and names are always stored `_nameList` and `_idList`.\n    if (isFormatOriginal && !provider.pure) {\n      var sharedDataItem = [];\n      for (var idx = start; idx < end; idx++) {\n        // NOTICE: Try not to write things into dataItem\n        var dataItem = provider.getItem(idx, sharedDataItem);\n        if (!this.hasItemOption && isDataItemOption(dataItem)) {\n          this.hasItemOption = true;\n        }\n        if (dataItem) {\n          var itemName = dataItem.name;\n          if (nameList[idx] == null && itemName != null) {\n            nameList[idx] = convertOptionIdName(itemName, null);\n          }\n          var itemId = dataItem.id;\n          if (idList[idx] == null && itemId != null) {\n            idList[idx] = convertOptionIdName(itemId, null);\n          }\n        }\n      }\n    }\n    if (this._shouldMakeIdFromName()) {\n      for (var idx = start; idx < end; idx++) {\n        makeIdFromName(this, idx);\n      }\n    }\n    prepareInvertedIndex(this);\n  };\n  /**\r\n   * PENDING: In fact currently this function is only used to short-circuit\r\n   * the calling of `scale.unionExtentFromData` when data have been filtered by modules\r\n   * like \"dataZoom\". `scale.unionExtentFromData` is used to calculate data extent for series on\r\n   * an axis, but if a \"axis related data filter module\" is used, the extent of the axis have\r\n   * been fixed and no need to calling `scale.unionExtentFromData` actually.\r\n   * But if we add \"custom data filter\" in future, which is not \"axis related\", this method may\r\n   * be still needed.\r\n   *\r\n   * Optimize for the scenario that data is filtered by a given extent.\r\n   * Consider that if data amount is more than hundreds of thousand,\r\n   * extent calculation will cost more than 10ms and the cache will\r\n   * be erased because of the filtering.\r\n   */\n  SeriesData.prototype.getApproximateExtent = function (dim) {\n    return this._approximateExtent[dim] || this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  /**\r\n   * Calculate extent on a filtered data might be time consuming.\r\n   * Approximate extent is only used for: calculate extent of filtered data outside.\r\n   */\n  SeriesData.prototype.setApproximateExtent = function (extent, dim) {\n    dim = this.getDimension(dim);\n    this._approximateExtent[dim] = extent.slice();\n  };\n  SeriesData.prototype.getCalculationInfo = function (key) {\n    return this._calculationInfo[key];\n  };\n  SeriesData.prototype.setCalculationInfo = function (key, value) {\n    isObject(key) ? zrUtil.extend(this._calculationInfo, key) : this._calculationInfo[key] = value;\n  };\n  /**\r\n   * @return Never be null/undefined. `number` will be converted to string. Because:\r\n   * In most cases, name is used in display, where returning a string is more convenient.\r\n   * In other cases, name is used in query (see `indexOfName`), where we can keep the\r\n   * rule that name `2` equals to name `'2'`.\r\n   */\n  SeriesData.prototype.getName = function (idx) {\n    var rawIndex = this.getRawIndex(idx);\n    var name = this._nameList[rawIndex];\n    if (name == null && this._nameDimIdx != null) {\n      name = getIdNameFromStore(this, this._nameDimIdx, rawIndex);\n    }\n    if (name == null) {\n      name = '';\n    }\n    return name;\n  };\n  SeriesData.prototype._getCategory = function (dimIdx, idx) {\n    var ordinal = this._store.get(dimIdx, idx);\n    var ordinalMeta = this._store.getOrdinalMeta(dimIdx);\n    if (ordinalMeta) {\n      return ordinalMeta.categories[ordinal];\n    }\n    return ordinal;\n  };\n  /**\r\n   * @return Never null/undefined. `number` will be converted to string. Because:\r\n   * In all cases having encountered at present, id is used in making diff comparison, which\r\n   * are usually based on hash map. We can keep the rule that the internal id are always string\r\n   * (treat `2` is the same as `'2'`) to make the related logic simple.\r\n   */\n  SeriesData.prototype.getId = function (idx) {\n    return getId(this, this.getRawIndex(idx));\n  };\n  SeriesData.prototype.count = function () {\n    return this._store.count();\n  };\n  /**\r\n   * Get value. Return NaN if idx is out of range.\r\n   *\r\n   * @notice Should better to use `data.getStore().get(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.get = function (dim, idx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.get(dimInfo.storeDimIndex, idx);\n    }\n  };\n  /**\r\n   * @notice Should better to use `data.getStore().getByRawIndex(dimIndex, dataIdx)` instead.\r\n   */\n  SeriesData.prototype.getByRawIndex = function (dim, rawIdx) {\n    var store = this._store;\n    var dimInfo = this._dimInfos[dim];\n    if (dimInfo) {\n      return store.getByRawIndex(dimInfo.storeDimIndex, rawIdx);\n    }\n  };\n  SeriesData.prototype.getIndices = function () {\n    return this._store.getIndices();\n  };\n  SeriesData.prototype.getDataExtent = function (dim) {\n    return this._store.getDataExtent(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getSum = function (dim) {\n    return this._store.getSum(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getMedian = function (dim) {\n    return this._store.getMedian(this._getStoreDimIndex(dim));\n  };\n  SeriesData.prototype.getValues = function (dimensions, idx) {\n    var _this = this;\n    var store = this._store;\n    return zrUtil.isArray(dimensions) ? store.getValues(map(dimensions, function (dim) {\n      return _this._getStoreDimIndex(dim);\n    }), idx) : store.getValues(dimensions);\n  };\n  /**\r\n   * If value is NaN. Including '-'\r\n   * Only check the coord dimensions.\r\n   */\n  SeriesData.prototype.hasValue = function (idx) {\n    var dataDimIndicesOnCoord = this._dimSummary.dataDimIndicesOnCoord;\n    for (var i = 0, len = dataDimIndicesOnCoord.length; i < len; i++) {\n      // Ordinal type originally can be string or number.\n      // But when an ordinal type is used on coord, it can\n      // not be string but only number. So we can also use isNaN.\n      if (isNaN(this._store.get(dataDimIndicesOnCoord[i], idx))) {\n        return false;\n      }\n    }\n    return true;\n  };\n  /**\r\n   * Retrieve the index with given name\r\n   */\n  SeriesData.prototype.indexOfName = function (name) {\n    for (var i = 0, len = this._store.count(); i < len; i++) {\n      if (this.getName(i) === name) {\n        return i;\n      }\n    }\n    return -1;\n  };\n  SeriesData.prototype.getRawIndex = function (idx) {\n    return this._store.getRawIndex(idx);\n  };\n  SeriesData.prototype.indexOfRawIndex = function (rawIndex) {\n    return this._store.indexOfRawIndex(rawIndex);\n  };\n  /**\r\n   * Only support the dimension which inverted index created.\r\n   * Do not support other cases until required.\r\n   * @param dim concrete dim\r\n   * @param value ordinal index\r\n   * @return rawIndex\r\n   */\n  SeriesData.prototype.rawIndexOf = function (dim, value) {\n    var invertedIndices = dim && this._invertedIndicesMap[dim];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!invertedIndices) {\n        throw new Error('Do not supported yet');\n      }\n    }\n    var rawIndex = invertedIndices && invertedIndices[value];\n    if (rawIndex == null || isNaN(rawIndex)) {\n      return INDEX_NOT_FOUND;\n    }\n    return rawIndex;\n  };\n  /**\r\n   * Retrieve the index of nearest value\r\n   * @param dim\r\n   * @param value\r\n   * @param [maxDistance=Infinity]\r\n   * @return If and only if multiple indices has\r\n   *         the same value, they are put to the result.\r\n   */\n  SeriesData.prototype.indicesOfNearest = function (dim, value, maxDistance) {\n    return this._store.indicesOfNearest(this._getStoreDimIndex(dim), value, maxDistance);\n  };\n  SeriesData.prototype.each = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store.each(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  SeriesData.prototype.filterSelf = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    this._store = this._store.filter(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return this;\n  };\n  /**\r\n   * Select data in range. (For optimization of filter)\r\n   * (Manually inline code, support 5 million data filtering in data zoom.)\r\n   */\n  SeriesData.prototype.selectRange = function (range) {\n    'use strict';\n\n    var _this = this;\n    var innerRange = {};\n    var dims = zrUtil.keys(range);\n    var dimIndices = [];\n    zrUtil.each(dims, function (dim) {\n      var dimIdx = _this._getStoreDimIndex(dim);\n      innerRange[dimIdx] = range[dim];\n      dimIndices.push(dimIdx);\n    });\n    this._store = this._store.selectRange(innerRange);\n    return this;\n  };\n  /* eslint-enable max-len */\n  SeriesData.prototype.mapArray = function (dims, cb, ctx) {\n    'use strict';\n\n    if (zrUtil.isFunction(dims)) {\n      ctx = cb;\n      cb = dims;\n      dims = [];\n    }\n    // ctxCompat just for compat echarts3\n    ctx = ctx || this;\n    var result = [];\n    this.each(dims, function () {\n      result.push(cb && cb.apply(this, arguments));\n    }, ctx);\n    return result;\n  };\n  SeriesData.prototype.map = function (dims, cb, ctx, ctxCompat) {\n    'use strict';\n\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.map(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n    return list;\n  };\n  SeriesData.prototype.modify = function (dims, cb, ctx, ctxCompat) {\n    var _this = this;\n    // ctxCompat just for compat echarts3\n    var fCtx = ctx || ctxCompat || this;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.each(normalizeDimensions(dims), function (dim) {\n        var dimInfo = _this.getDimensionInfo(dim);\n        if (!dimInfo.isCalculationCoord) {\n          console.error('Danger: only stack dimension can be modified');\n        }\n      });\n    }\n    var dimIndices = map(normalizeDimensions(dims), this._getStoreDimIndex, this);\n    // If do shallow clone here, if there are too many stacked series,\n    // it still cost lots of memory, because `_store.dimensions` are not shared.\n    // We should consider there probably be shallow clone happen in each series\n    // in consequent filter/map.\n    this._store.modify(dimIndices, fCtx ? zrUtil.bind(cb, fCtx) : cb);\n  };\n  /**\r\n   * Large data down sampling on given dimension\r\n   * @param sampleIndex Sample index for name and id\r\n   */\n  SeriesData.prototype.downSample = function (dimension, rate, sampleValue, sampleIndex) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.downSample(this._getStoreDimIndex(dimension), rate, sampleValue, sampleIndex);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using min-max\r\n   * @param {string} valueDimension\r\n   * @param {number} rate\r\n   */\n  SeriesData.prototype.minmaxDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.minmaxDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  /**\r\n   * Large data down sampling using largest-triangle-three-buckets\r\n   * @param {string} valueDimension\r\n   * @param {number} targetCount\r\n   */\n  SeriesData.prototype.lttbDownSample = function (valueDimension, rate) {\n    var list = cloneListForMapAndSample(this);\n    list._store = this._store.lttbDownSample(this._getStoreDimIndex(valueDimension), rate);\n    return list;\n  };\n  SeriesData.prototype.getRawDataItem = function (idx) {\n    return this._store.getRawDataItem(idx);\n  };\n  /**\r\n   * Get model of one data item.\r\n   */\n  // TODO: Type of data item\n  SeriesData.prototype.getItemModel = function (idx) {\n    var hostModel = this.hostModel;\n    var dataItem = this.getRawDataItem(idx);\n    return new Model(dataItem, hostModel, hostModel && hostModel.ecModel);\n  };\n  /**\r\n   * Create a data differ\r\n   */\n  SeriesData.prototype.diff = function (otherList) {\n    var thisList = this;\n    return new DataDiffer(otherList ? otherList.getStore().getIndices() : [], this.getStore().getIndices(), function (idx) {\n      return getId(otherList, idx);\n    }, function (idx) {\n      return getId(thisList, idx);\n    });\n  };\n  /**\r\n   * Get visual property.\r\n   */\n  SeriesData.prototype.getVisual = function (key) {\n    var visual = this._visual;\n    return visual && visual[key];\n  };\n  SeriesData.prototype.setVisual = function (kvObj, val) {\n    this._visual = this._visual || {};\n    if (isObject(kvObj)) {\n      zrUtil.extend(this._visual, kvObj);\n    } else {\n      this._visual[kvObj] = val;\n    }\n  };\n  /**\r\n   * Get visual property of single data item\r\n   */\n  // eslint-disable-next-line\n  SeriesData.prototype.getItemVisual = function (idx, key) {\n    var itemVisual = this._itemVisuals[idx];\n    var val = itemVisual && itemVisual[key];\n    if (val == null) {\n      // Use global visual property\n      return this.getVisual(key);\n    }\n    return val;\n  };\n  /**\r\n   * If exists visual property of single data item\r\n   */\n  SeriesData.prototype.hasItemVisual = function () {\n    return this._itemVisuals.length > 0;\n  };\n  /**\r\n   * Make sure itemVisual property is unique\r\n   */\n  // TODO: use key to save visual to reduce memory.\n  SeriesData.prototype.ensureUniqueItemVisual = function (idx, key) {\n    var itemVisuals = this._itemVisuals;\n    var itemVisual = itemVisuals[idx];\n    if (!itemVisual) {\n      itemVisual = itemVisuals[idx] = {};\n    }\n    var val = itemVisual[key];\n    if (val == null) {\n      val = this.getVisual(key);\n      // TODO Performance?\n      if (zrUtil.isArray(val)) {\n        val = val.slice();\n      } else if (isObject(val)) {\n        val = zrUtil.extend({}, val);\n      }\n      itemVisual[key] = val;\n    }\n    return val;\n  };\n  // eslint-disable-next-line\n  SeriesData.prototype.setItemVisual = function (idx, key, value) {\n    var itemVisual = this._itemVisuals[idx] || {};\n    this._itemVisuals[idx] = itemVisual;\n    if (isObject(key)) {\n      zrUtil.extend(itemVisual, key);\n    } else {\n      itemVisual[key] = value;\n    }\n  };\n  /**\r\n   * Clear itemVisuals and list visual.\r\n   */\n  SeriesData.prototype.clearAllVisual = function () {\n    this._visual = {};\n    this._itemVisuals = [];\n  };\n  SeriesData.prototype.setLayout = function (key, val) {\n    isObject(key) ? zrUtil.extend(this._layout, key) : this._layout[key] = val;\n  };\n  /**\r\n   * Get layout property.\r\n   */\n  SeriesData.prototype.getLayout = function (key) {\n    return this._layout[key];\n  };\n  /**\r\n   * Get layout of single data item\r\n   */\n  SeriesData.prototype.getItemLayout = function (idx) {\n    return this._itemLayouts[idx];\n  };\n  /**\r\n   * Set layout of single data item\r\n   */\n  SeriesData.prototype.setItemLayout = function (idx, layout, merge) {\n    this._itemLayouts[idx] = merge ? zrUtil.extend(this._itemLayouts[idx] || {}, layout) : layout;\n  };\n  /**\r\n   * Clear all layout of single data item\r\n   */\n  SeriesData.prototype.clearItemLayouts = function () {\n    this._itemLayouts.length = 0;\n  };\n  /**\r\n   * Set graphic element relative to data. It can be set as null\r\n   */\n  SeriesData.prototype.setItemGraphicEl = function (idx, el) {\n    var seriesIndex = this.hostModel && this.hostModel.seriesIndex;\n    setCommonECData(seriesIndex, this.dataType, idx, el);\n    this._graphicEls[idx] = el;\n  };\n  SeriesData.prototype.getItemGraphicEl = function (idx) {\n    return this._graphicEls[idx];\n  };\n  SeriesData.prototype.eachItemGraphicEl = function (cb, context) {\n    zrUtil.each(this._graphicEls, function (el, idx) {\n      if (el) {\n        cb && cb.call(context, el, idx);\n      }\n    });\n  };\n  /**\r\n   * Shallow clone a new list except visual and layout properties, and graph elements.\r\n   * New list only change the indices.\r\n   */\n  SeriesData.prototype.cloneShallow = function (list) {\n    if (!list) {\n      list = new SeriesData(this._schema ? this._schema : map(this.dimensions, this._getDimInfo, this), this.hostModel);\n    }\n    transferProperties(list, this);\n    list._store = this._store;\n    return list;\n  };\n  /**\r\n   * Wrap some method to add more feature\r\n   */\n  SeriesData.prototype.wrapMethod = function (methodName, injectFunction) {\n    var originalMethod = this[methodName];\n    if (!zrUtil.isFunction(originalMethod)) {\n      return;\n    }\n    this.__wrappedMethods = this.__wrappedMethods || [];\n    this.__wrappedMethods.push(methodName);\n    this[methodName] = function () {\n      var res = originalMethod.apply(this, arguments);\n      return injectFunction.apply(this, [res].concat(zrUtil.slice(arguments)));\n    };\n  };\n  // ----------------------------------------------------------\n  // A work around for internal method visiting private member.\n  // ----------------------------------------------------------\n  SeriesData.internalField = function () {\n    prepareInvertedIndex = function (data) {\n      var invertedIndicesMap = data._invertedIndicesMap;\n      zrUtil.each(invertedIndicesMap, function (invertedIndices, dim) {\n        var dimInfo = data._dimInfos[dim];\n        // Currently, only dimensions that has ordinalMeta can create inverted indices.\n        var ordinalMeta = dimInfo.ordinalMeta;\n        var store = data._store;\n        if (ordinalMeta) {\n          invertedIndices = invertedIndicesMap[dim] = new CtorInt32Array(ordinalMeta.categories.length);\n          // The default value of TypedArray is 0. To avoid miss\n          // mapping to 0, we should set it as INDEX_NOT_FOUND.\n          for (var i = 0; i < invertedIndices.length; i++) {\n            invertedIndices[i] = INDEX_NOT_FOUND;\n          }\n          for (var i = 0; i < store.count(); i++) {\n            // Only support the case that all values are distinct.\n            invertedIndices[store.get(dimInfo.storeDimIndex, i)] = i;\n          }\n        }\n      });\n    };\n    getIdNameFromStore = function (data, dimIdx, idx) {\n      return convertOptionIdName(data._getCategory(dimIdx, idx), null);\n    };\n    /**\r\n     * @see the comment of `List['getId']`.\r\n     */\n    getId = function (data, rawIndex) {\n      var id = data._idList[rawIndex];\n      if (id == null && data._idDimIdx != null) {\n        id = getIdNameFromStore(data, data._idDimIdx, rawIndex);\n      }\n      if (id == null) {\n        id = ID_PREFIX + rawIndex;\n      }\n      return id;\n    };\n    normalizeDimensions = function (dimensions) {\n      if (!zrUtil.isArray(dimensions)) {\n        dimensions = dimensions != null ? [dimensions] : [];\n      }\n      return dimensions;\n    };\n    /**\r\n     * Data in excludeDimensions is copied, otherwise transferred.\r\n     */\n    cloneListForMapAndSample = function (original) {\n      var list = new SeriesData(original._schema ? original._schema : map(original.dimensions, original._getDimInfo, original), original.hostModel);\n      // FIXME If needs stackedOn, value may already been stacked\n      transferProperties(list, original);\n      return list;\n    };\n    transferProperties = function (target, source) {\n      zrUtil.each(TRANSFERABLE_PROPERTIES.concat(source.__wrappedMethods || []), function (propName) {\n        if (source.hasOwnProperty(propName)) {\n          target[propName] = source[propName];\n        }\n      });\n      target.__wrappedMethods = source.__wrappedMethods;\n      zrUtil.each(CLONE_PROPERTIES, function (propName) {\n        target[propName] = zrUtil.clone(source[propName]);\n      });\n      target._calculationInfo = zrUtil.extend({}, source._calculationInfo);\n    };\n    makeIdFromName = function (data, idx) {\n      var nameList = data._nameList;\n      var idList = data._idList;\n      var nameDimIdx = data._nameDimIdx;\n      var idDimIdx = data._idDimIdx;\n      var name = nameList[idx];\n      var id = idList[idx];\n      if (name == null && nameDimIdx != null) {\n        nameList[idx] = name = getIdNameFromStore(data, nameDimIdx, idx);\n      }\n      if (id == null && idDimIdx != null) {\n        idList[idx] = id = getIdNameFromStore(data, idDimIdx, idx);\n      }\n      if (id == null && name != null) {\n        var nameRepeatCount = data._nameRepeatCount;\n        var nmCnt = nameRepeatCount[name] = (nameRepeatCount[name] || 0) + 1;\n        id = name;\n        if (nmCnt > 1) {\n          id += '__ec__' + nmCnt;\n        }\n        idList[idx] = id;\n      }\n    };\n  }();\n  return SeriesData;\n}();\nexport default SeriesData;"], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,SAASC,yBAAyB,EAAEC,sBAAsB,QAAQ,kBAAkB;AACpF,SAASC,mBAAmB,EAAEC,gBAAgB,QAAQ,kBAAkB;AACxE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,OAAOC,SAAS,MAAM,gBAAgB;AACtC,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,IAAIC,QAAQ,GAAGd,MAAM,CAACc,QAAQ;AAC9B,IAAIC,GAAG,GAAGf,MAAM,CAACe,GAAG;AACpB,IAAIC,cAAc,GAAG,OAAOC,UAAU,KAAK,WAAW,GAAGC,KAAK,GAAGD,UAAU;AAC3E;AACA;AACA,IAAIE,SAAS,GAAG,OAAO;AACvB,IAAIC,eAAe,GAAG,CAAC,CAAC;AACxB;AACA,IAAIC,uBAAuB,GAAG,CAAC,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,qBAAqB,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW,EAAE,kBAAkB,CAAC;AAC1M,IAAIC,gBAAgB,GAAG,CAAC,oBAAoB,CAAC;AAC7C;AACA;AACA;AACA,IAAIC,oBAAoB;AACxB,IAAIC,KAAK;AACT,IAAIC,kBAAkB;AACtB,IAAIC,mBAAmB;AACvB,IAAIC,kBAAkB;AACtB,IAAIC,wBAAwB;AAC5B,IAAIC,cAAc;AAClB,IAAIC,UAAU,GAAG,aAAa,YAAY;EACxC;AACF;AACA;AACA;AACA;EACE,SAASA,UAAUA,CAACC,eAAe,EAAEC,SAAS,EAAE;IAC9C,IAAI,CAACC,IAAI,GAAG,MAAM;IAClB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,EAAE;IACjB;IACA;IACA;IACA;IACA,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB;IACA,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB;IACA,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB;IACA,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB;IACA,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB;IACA,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B;IACA;IACA;IACA;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;IACA;IACA,IAAI,CAACC,oBAAoB,GAAG,CAAC,cAAc,EAAE,YAAY,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,KAAK,CAAC;IACvG;IACA,IAAI,CAACC,iBAAiB,GAAG,CAAC,YAAY,EAAE,aAAa,CAAC;IACtD,IAAI,CAACC,kBAAkB,GAAG,CAAC,YAAY,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;IAC9E,IAAIC,UAAU;IACd,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIpC,kBAAkB,CAACkB,eAAe,CAAC,EAAE;MACvCiB,UAAU,GAAGjB,eAAe,CAACiB,UAAU;MACvC,IAAI,CAACd,WAAW,GAAGH,eAAe,CAACmB,kBAAkB,CAAC,CAAC;MACvD,IAAI,CAACC,OAAO,GAAGpB,eAAe;IAChC,CAAC,MAAM;MACLkB,iBAAiB,GAAG,IAAI;MACxBD,UAAU,GAAGjB,eAAe;IAC9B;IACAiB,UAAU,GAAGA,UAAU,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;IACrC,IAAII,cAAc,GAAG,CAAC,CAAC;IACvB,IAAIC,cAAc,GAAG,EAAE;IACvB,IAAIC,kBAAkB,GAAG,CAAC,CAAC;IAC3B,IAAIC,WAAW,GAAG,KAAK;IACvB,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,UAAU,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1C;MACA,IAAIE,YAAY,GAAGX,UAAU,CAACS,CAAC,CAAC;MAChC,IAAIG,aAAa,GAAG5D,MAAM,CAAC6D,QAAQ,CAACF,YAAY,CAAC,GAAG,IAAItD,qBAAqB,CAAC;QAC5EyD,IAAI,EAAEH;MACR,CAAC,CAAC,GAAG,EAAEA,YAAY,YAAYtD,qBAAqB,CAAC,GAAG,IAAIA,qBAAqB,CAACsD,YAAY,CAAC,GAAGA,YAAY;MAC9G,IAAII,aAAa,GAAGH,aAAa,CAACE,IAAI;MACtCF,aAAa,CAAC3B,IAAI,GAAG2B,aAAa,CAAC3B,IAAI,IAAI,OAAO;MAClD,IAAI,CAAC2B,aAAa,CAACI,QAAQ,EAAE;QAC3BJ,aAAa,CAACI,QAAQ,GAAGD,aAAa;QACtCH,aAAa,CAACK,aAAa,GAAG,CAAC;MACjC;MACA,IAAIC,SAAS,GAAGN,aAAa,CAACM,SAAS,GAAGN,aAAa,CAACM,SAAS,IAAI,CAAC,CAAC;MACvEb,cAAc,CAACc,IAAI,CAACJ,aAAa,CAAC;MAClCX,cAAc,CAACW,aAAa,CAAC,GAAGH,aAAa;MAC7C,IAAIJ,QAAQ,CAACO,aAAa,CAAC,IAAI,IAAI,EAAE;QACnCR,WAAW,GAAG,IAAI;MACpB;MACA,IAAIK,aAAa,CAACQ,qBAAqB,EAAE;QACvCd,kBAAkB,CAACS,aAAa,CAAC,GAAG,EAAE;MACxC;MACA,IAAIG,SAAS,CAACG,QAAQ,KAAK,CAAC,EAAE;QAC5B,IAAI,CAACC,WAAW,GAAGb,CAAC;MACtB;MACA,IAAIS,SAAS,CAACK,MAAM,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACC,SAAS,GAAGf,CAAC;MACpB;MACA,IAAIgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC3E,MAAM,CAAC4E,MAAM,CAAC3B,iBAAiB,IAAIW,aAAa,CAACiB,aAAa,IAAI,CAAC,CAAC;MACtE;MACA,IAAI5B,iBAAiB,EAAE;QACrBW,aAAa,CAACiB,aAAa,GAAGpB,CAAC;MACjC;IACF;IACA,IAAI,CAACT,UAAU,GAAGK,cAAc;IAChC,IAAI,CAACyB,SAAS,GAAG1B,cAAc;IAC/B,IAAI,CAAC2B,qBAAqB,CAACxB,WAAW,CAAC;IACvC,IAAI,CAACvB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACgD,mBAAmB,GAAG1B,kBAAkB;IAC7C,IAAI,IAAI,CAACpB,WAAW,EAAE;MACpB,IAAI+C,cAAc,GAAG,IAAI,CAACC,aAAa,GAAGlF,MAAM,CAACmF,aAAa,CAAC,CAAC;MAChEnF,MAAM,CAACoF,IAAI,CAAC/B,cAAc,EAAE,UAAUgC,OAAO,EAAE;QAC7CJ,cAAc,CAACK,GAAG,CAAClC,cAAc,CAACiC,OAAO,CAAC,CAACR,aAAa,EAAEQ,OAAO,CAAC;MACpE,CAAC,CAAC;IACJ;EACF;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvD,UAAU,CAACyD,SAAS,CAACC,YAAY,GAAG,UAAUC,GAAG,EAAE;IACjD,IAAIC,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAACF,GAAG,CAAC;IACzC,IAAIC,MAAM,IAAI,IAAI,EAAE;MAClB,OAAOD,GAAG;IACZ;IACAC,MAAM,GAAGD,GAAG;IACZ,IAAI,CAAC,IAAI,CAACvD,WAAW,EAAE;MACrB,OAAO,IAAI,CAACc,UAAU,CAAC0C,MAAM,CAAC;IAChC;IACA;IACA;IACA,IAAIL,OAAO,GAAG,IAAI,CAACH,aAAa,CAACU,GAAG,CAACF,MAAM,CAAC;IAC5C,IAAIL,OAAO,IAAI,IAAI,EAAE;MACnB,OAAOA,OAAO;IAChB;IACA,IAAIQ,YAAY,GAAG,IAAI,CAAC1C,OAAO,CAAC2C,kBAAkB,CAACJ,MAAM,CAAC;IAC1D,IAAIG,YAAY,EAAE;MAChB,OAAOA,YAAY,CAAC/B,IAAI;IAC1B;EACF,CAAC;EACD;AACF;AACA;AACA;EACEhC,UAAU,CAACyD,SAAS,CAACQ,iBAAiB,GAAG,UAAUN,GAAG,EAAE;IACtD,IAAIC,MAAM,GAAG,IAAI,CAACC,kBAAkB,CAACF,GAAG,CAAC;IACzC,IAAIC,MAAM,IAAI,IAAI,EAAE;MAClB,OAAOA,MAAM;IACf;IACA,IAAID,GAAG,IAAI,IAAI,EAAE;MACf,OAAO,CAAC,CAAC;IACX;IACA,IAAIO,OAAO,GAAG,IAAI,CAACC,WAAW,CAACR,GAAG,CAAC;IACnC,OAAOO,OAAO,GAAGA,OAAO,CAACnB,aAAa,GAAG,IAAI,CAAC3C,WAAW,GAAG,IAAI,CAACiB,OAAO,CAAC+C,uBAAuB,CAACT,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5G,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3D,UAAU,CAACyD,SAAS,CAACI,kBAAkB,GAAG,UAAUF,GAAG,EAAE;IACvD,IAAIzF,MAAM,CAACmG,QAAQ,CAACV,GAAG;IACvB;IAAA,GACGA,GAAG,IAAI,IAAI,IAAI,CAACW,KAAK,CAACX,GAAG,CAAC,IAAI,CAAC,IAAI,CAACQ,WAAW,CAACR,GAAG,CAAC,KAAK,CAAC,IAAI,CAACvD,WAAW,IAAI,IAAI,CAACiB,OAAO,CAAC+C,uBAAuB,CAACT,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/H,OAAO,CAACA,GAAG;IACb;EACF,CAAC;EACD3D,UAAU,CAACyD,SAAS,CAACc,iBAAiB,GAAG,UAAUZ,GAAG,EAAE;IACtD,IAAIC,MAAM,GAAG,IAAI,CAACK,iBAAiB,CAACN,GAAG,CAAC;IACxC,IAAIhB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIe,MAAM,IAAI,IAAI,EAAE;QAClB,MAAM,IAAIY,KAAK,CAAC,oBAAoB,GAAGb,GAAG,CAAC;MAC7C;IACF;IACA,OAAOC,MAAM;EACf,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE5D,UAAU,CAACyD,SAAS,CAACgB,gBAAgB,GAAG,UAAUd,GAAG,EAAE;IACrD;IACA,OAAO,IAAI,CAACQ,WAAW,CAAC,IAAI,CAACT,YAAY,CAACC,GAAG,CAAC,CAAC;EACjD,CAAC;EACD3D,UAAU,CAACyD,SAAS,CAACR,qBAAqB,GAAG,UAAUxB,WAAW,EAAE;IAClE,IAAIH,cAAc,GAAG,IAAI,CAAC0B,SAAS;IACnC,IAAI,CAACmB,WAAW,GAAG1C,WAAW,GAAG,UAAU8B,OAAO,EAAE;MAClD,OAAOjC,cAAc,CAACoD,cAAc,CAACnB,OAAO,CAAC,GAAGjC,cAAc,CAACiC,OAAO,CAAC,GAAGoB,SAAS;IACrF,CAAC,GAAG,UAAUpB,OAAO,EAAE;MACrB,OAAOjC,cAAc,CAACiC,OAAO,CAAC;IAChC,CAAC;EACH,CAAC;EACD;AACF;AACA;EACEvD,UAAU,CAACyD,SAAS,CAACmB,oBAAoB,GAAG,YAAY;IACtD,OAAO,IAAI,CAACC,WAAW,CAACC,eAAe,CAACC,KAAK,CAAC,CAAC;EACjD,CAAC;EACD/E,UAAU,CAACyD,SAAS,CAACuB,YAAY,GAAG,UAAU9C,QAAQ,EAAE+C,GAAG,EAAE;IAC3D,IAAIC,iBAAiB,GAAG,IAAI,CAACL,WAAW;IACxC,IAAII,GAAG,IAAI,IAAI,EAAE;MACf,OAAOC,iBAAiB,CAACC,sBAAsB,CAACjD,QAAQ,CAAC;IAC3D;IACA,IAAIkD,IAAI,GAAGF,iBAAiB,CAACG,MAAM,CAACnD,QAAQ,CAAC;IAC7C,OAAOkD,IAAI,GAAGA,IAAI,CAACH,GAAG,CAAC,GAAG,IAAI;EAChC,CAAC;EACDjF,UAAU,CAACyD,SAAS,CAAC6B,gBAAgB,GAAG,UAAUpD,QAAQ,EAAE;IAC1D,IAAIgD,iBAAiB,GAAG,IAAI,CAACL,WAAW;IACxC,IAAIO,IAAI,GAAGF,iBAAiB,CAACG,MAAM,CAACnD,QAAQ,CAAC;IAC7C,OAAO,CAACkD,IAAI,IAAI,EAAE,EAAEL,KAAK,CAAC,CAAC;EAC7B,CAAC;EACD/E,UAAU,CAACyD,SAAS,CAAC8B,QAAQ,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACC,MAAM;EACpB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExF,UAAU,CAACyD,SAAS,CAACgC,QAAQ,GAAG,UAAUC,IAAI,EAAEC,QAAQ,EAAEC,cAAc,EAAE;IACxE,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,KAAK;IACT,IAAIJ,IAAI,YAAY5G,SAAS,EAAE;MAC7BgH,KAAK,GAAGJ,IAAI;IACd;IACA,IAAI,CAACI,KAAK,EAAE;MACV,IAAI5E,UAAU,GAAG,IAAI,CAACA,UAAU;MAChC,IAAI6E,QAAQ,GAAGlH,gBAAgB,CAAC6G,IAAI,CAAC,IAAIxH,MAAM,CAAC8H,WAAW,CAACN,IAAI,CAAC,GAAG,IAAIrH,mBAAmB,CAACqH,IAAI,EAAExE,UAAU,CAACU,MAAM,CAAC,GAAG8D,IAAI;MAC3HI,KAAK,GAAG,IAAIhH,SAAS,CAAC,CAAC;MACvB,IAAIwC,cAAc,GAAGrC,GAAG,CAACiC,UAAU,EAAE,UAAUqC,OAAO,EAAE;QACtD,OAAO;UACLpD,IAAI,EAAE0F,KAAK,CAAC7C,SAAS,CAACO,OAAO,CAAC,CAACpD,IAAI;UACnC8F,QAAQ,EAAE1C;QACZ,CAAC;MACH,CAAC,CAAC;MACFuC,KAAK,CAACL,QAAQ,CAACM,QAAQ,EAAEzE,cAAc,EAAEsE,cAAc,CAAC;IAC1D;IACA,IAAI,CAACJ,MAAM,GAAGM,KAAK;IACnB;IACA,IAAI,CAACzF,SAAS,GAAG,CAACsF,QAAQ,IAAI,EAAE,EAAEZ,KAAK,CAAC,CAAC;IACzC,IAAI,CAACzE,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC4F,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,OAAO,CAAC,CAAC,EAAEL,KAAK,CAACM,KAAK,CAAC,CAAC,CAAC;IAC9B;IACA;IACA,IAAI,CAACvB,WAAW,GAAGvG,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC+C,OAAO,CAAC;IAC1D,IAAI,CAACgF,UAAU,GAAG,IAAI,CAACxB,WAAW,CAACwB,UAAU;EAC/C,CAAC;EACD;AACF;AACA;EACErG,UAAU,CAACyD,SAAS,CAAC6C,UAAU,GAAG,UAAUZ,IAAI,EAAE;IAChD,IAAIa,KAAK,GAAG,IAAI,CAACf,MAAM,CAACc,UAAU,CAACZ,IAAI,CAAC;IACxC,IAAI,CAACS,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAClC,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvG,UAAU,CAACyD,SAAS,CAAC+C,YAAY,GAAG,UAAUC,MAAM,EAAEC,KAAK,EAAE;IAC3D,IAAIC,EAAE,GAAG,IAAI,CAACnB,MAAM,CAACgB,YAAY,CAACC,MAAM,EAAEC,KAAK,IAAIA,KAAK,CAAC9E,MAAM,CAAC;MAC9DgF,KAAK,GAAGD,EAAE,CAACC,KAAK;MAChBC,GAAG,GAAGF,EAAE,CAACE,GAAG;IACd,IAAIC,oBAAoB,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACvD,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAIN,KAAK,EAAE;MACT,KAAK,IAAIzB,GAAG,GAAG2B,KAAK,EAAE3B,GAAG,GAAG4B,GAAG,EAAE5B,GAAG,EAAE,EAAE;QACtC,IAAIgC,SAAS,GAAGhC,GAAG,GAAG2B,KAAK;QAC3B,IAAI,CAACvG,SAAS,CAAC4E,GAAG,CAAC,GAAGyB,KAAK,CAACO,SAAS,CAAC;QACtC,IAAIH,oBAAoB,EAAE;UACxB/G,cAAc,CAAC,IAAI,EAAEkF,GAAG,CAAC;QAC3B;MACF;IACF;EACF,CAAC;EACDjF,UAAU,CAACyD,SAAS,CAACuD,kBAAkB,GAAG,YAAY;IACpD,IAAIlB,KAAK,GAAG,IAAI,CAACN,MAAM;IACvB,IAAItE,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,UAAU,CAACU,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1C,IAAIuC,OAAO,GAAG,IAAI,CAAClB,SAAS,CAAC9B,UAAU,CAACS,CAAC,CAAC,CAAC;MAC3C,IAAIuC,OAAO,CAACgD,WAAW,EAAE;QACvBpB,KAAK,CAACqB,kBAAkB,CAACjD,OAAO,CAACnB,aAAa,EAAEmB,OAAO,CAACgD,WAAW,CAAC;MACtE;IACF;EACF,CAAC;EACDlH,UAAU,CAACyD,SAAS,CAACsD,qBAAqB,GAAG,YAAY;IACvD,IAAIhB,QAAQ,GAAG,IAAI,CAACP,MAAM,CAAC4B,WAAW,CAAC,CAAC;IACxC,OAAO,IAAI,CAAC1E,SAAS,IAAI,IAAI,IAAIqD,QAAQ,CAACsB,SAAS,CAAC,CAAC,CAACC,YAAY,KAAK9I,yBAAyB,IAAI,CAACuH,QAAQ,CAACwB,WAAW;EAC3H,CAAC;EACDvH,UAAU,CAACyD,SAAS,CAAC0C,OAAO,GAAG,UAAUS,KAAK,EAAEC,GAAG,EAAE;IACnD,IAAID,KAAK,IAAIC,GAAG,EAAE;MAChB;IACF;IACA,IAAIf,KAAK,GAAG,IAAI,CAACN,MAAM;IACvB,IAAIO,QAAQ,GAAGD,KAAK,CAACsB,WAAW,CAAC,CAAC;IAClC,IAAI,CAACJ,kBAAkB,CAAC,CAAC;IACzB,IAAIrB,QAAQ,GAAG,IAAI,CAACtF,SAAS;IAC7B,IAAImH,MAAM,GAAG,IAAI,CAAClH,OAAO;IACzB,IAAIgH,YAAY,GAAGvB,QAAQ,CAACsB,SAAS,CAAC,CAAC,CAACC,YAAY;IACpD,IAAIG,gBAAgB,GAAGH,YAAY,KAAK7I,sBAAsB;IAC9D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIgJ,gBAAgB,IAAI,CAAC1B,QAAQ,CAAC2B,IAAI,EAAE;MACtC,IAAIC,cAAc,GAAG,EAAE;MACvB,KAAK,IAAI1C,GAAG,GAAG2B,KAAK,EAAE3B,GAAG,GAAG4B,GAAG,EAAE5B,GAAG,EAAE,EAAE;QACtC;QACA,IAAI2C,QAAQ,GAAG7B,QAAQ,CAAC8B,OAAO,CAAC5C,GAAG,EAAE0C,cAAc,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC7G,aAAa,IAAInC,gBAAgB,CAACiJ,QAAQ,CAAC,EAAE;UACrD,IAAI,CAAC9G,aAAa,GAAG,IAAI;QAC3B;QACA,IAAI8G,QAAQ,EAAE;UACZ,IAAIrF,QAAQ,GAAGqF,QAAQ,CAAC5F,IAAI;UAC5B,IAAI2D,QAAQ,CAACV,GAAG,CAAC,IAAI,IAAI,IAAI1C,QAAQ,IAAI,IAAI,EAAE;YAC7CoD,QAAQ,CAACV,GAAG,CAAC,GAAGvG,mBAAmB,CAAC6D,QAAQ,EAAE,IAAI,CAAC;UACrD;UACA,IAAIE,MAAM,GAAGmF,QAAQ,CAACE,EAAE;UACxB,IAAIN,MAAM,CAACvC,GAAG,CAAC,IAAI,IAAI,IAAIxC,MAAM,IAAI,IAAI,EAAE;YACzC+E,MAAM,CAACvC,GAAG,CAAC,GAAGvG,mBAAmB,CAAC+D,MAAM,EAAE,IAAI,CAAC;UACjD;QACF;MACF;IACF;IACA,IAAI,IAAI,CAACsE,qBAAqB,CAAC,CAAC,EAAE;MAChC,KAAK,IAAI9B,GAAG,GAAG2B,KAAK,EAAE3B,GAAG,GAAG4B,GAAG,EAAE5B,GAAG,EAAE,EAAE;QACtClF,cAAc,CAAC,IAAI,EAAEkF,GAAG,CAAC;MAC3B;IACF;IACAxF,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEO,UAAU,CAACyD,SAAS,CAACsE,oBAAoB,GAAG,UAAUpE,GAAG,EAAE;IACzD,OAAO,IAAI,CAAC/C,kBAAkB,CAAC+C,GAAG,CAAC,IAAI,IAAI,CAAC6B,MAAM,CAACwC,aAAa,CAAC,IAAI,CAACzD,iBAAiB,CAACZ,GAAG,CAAC,CAAC;EAC/F,CAAC;EACD;AACF;AACA;AACA;EACE3D,UAAU,CAACyD,SAAS,CAACwE,oBAAoB,GAAG,UAAUC,MAAM,EAAEvE,GAAG,EAAE;IACjEA,GAAG,GAAG,IAAI,CAACD,YAAY,CAACC,GAAG,CAAC;IAC5B,IAAI,CAAC/C,kBAAkB,CAAC+C,GAAG,CAAC,GAAGuE,MAAM,CAACnD,KAAK,CAAC,CAAC;EAC/C,CAAC;EACD/E,UAAU,CAACyD,SAAS,CAAC0E,kBAAkB,GAAG,UAAUC,GAAG,EAAE;IACvD,OAAO,IAAI,CAACvH,gBAAgB,CAACuH,GAAG,CAAC;EACnC,CAAC;EACDpI,UAAU,CAACyD,SAAS,CAAC4E,kBAAkB,GAAG,UAAUD,GAAG,EAAEE,KAAK,EAAE;IAC9DtJ,QAAQ,CAACoJ,GAAG,CAAC,GAAGlK,MAAM,CAACqK,MAAM,CAAC,IAAI,CAAC1H,gBAAgB,EAAEuH,GAAG,CAAC,GAAG,IAAI,CAACvH,gBAAgB,CAACuH,GAAG,CAAC,GAAGE,KAAK;EAChG,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEtI,UAAU,CAACyD,SAAS,CAAC+E,OAAO,GAAG,UAAUvD,GAAG,EAAE;IAC5C,IAAIwD,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACzD,GAAG,CAAC;IACpC,IAAIjD,IAAI,GAAG,IAAI,CAAC3B,SAAS,CAACoI,QAAQ,CAAC;IACnC,IAAIzG,IAAI,IAAI,IAAI,IAAI,IAAI,CAACQ,WAAW,IAAI,IAAI,EAAE;MAC5CR,IAAI,GAAGrC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC6C,WAAW,EAAEiG,QAAQ,CAAC;IAC7D;IACA,IAAIzG,IAAI,IAAI,IAAI,EAAE;MAChBA,IAAI,GAAG,EAAE;IACX;IACA,OAAOA,IAAI;EACb,CAAC;EACDhC,UAAU,CAACyD,SAAS,CAACkF,YAAY,GAAG,UAAU/E,MAAM,EAAEqB,GAAG,EAAE;IACzD,IAAI2D,OAAO,GAAG,IAAI,CAACpD,MAAM,CAAC1B,GAAG,CAACF,MAAM,EAAEqB,GAAG,CAAC;IAC1C,IAAIiC,WAAW,GAAG,IAAI,CAAC1B,MAAM,CAACqD,cAAc,CAACjF,MAAM,CAAC;IACpD,IAAIsD,WAAW,EAAE;MACf,OAAOA,WAAW,CAAC4B,UAAU,CAACF,OAAO,CAAC;IACxC;IACA,OAAOA,OAAO;EAChB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE5I,UAAU,CAACyD,SAAS,CAAC/D,KAAK,GAAG,UAAUuF,GAAG,EAAE;IAC1C,OAAOvF,KAAK,CAAC,IAAI,EAAE,IAAI,CAACgJ,WAAW,CAACzD,GAAG,CAAC,CAAC;EAC3C,CAAC;EACDjF,UAAU,CAACyD,SAAS,CAAC2C,KAAK,GAAG,YAAY;IACvC,OAAO,IAAI,CAACZ,MAAM,CAACY,KAAK,CAAC,CAAC;EAC5B,CAAC;EACD;AACF;AACA;AACA;AACA;EACEpG,UAAU,CAACyD,SAAS,CAACK,GAAG,GAAG,UAAUH,GAAG,EAAEsB,GAAG,EAAE;IAC7C,IAAIa,KAAK,GAAG,IAAI,CAACN,MAAM;IACvB,IAAItB,OAAO,GAAG,IAAI,CAAClB,SAAS,CAACW,GAAG,CAAC;IACjC,IAAIO,OAAO,EAAE;MACX,OAAO4B,KAAK,CAAChC,GAAG,CAACI,OAAO,CAACnB,aAAa,EAAEkC,GAAG,CAAC;IAC9C;EACF,CAAC;EACD;AACF;AACA;EACEjF,UAAU,CAACyD,SAAS,CAACsF,aAAa,GAAG,UAAUpF,GAAG,EAAEqF,MAAM,EAAE;IAC1D,IAAIlD,KAAK,GAAG,IAAI,CAACN,MAAM;IACvB,IAAItB,OAAO,GAAG,IAAI,CAAClB,SAAS,CAACW,GAAG,CAAC;IACjC,IAAIO,OAAO,EAAE;MACX,OAAO4B,KAAK,CAACiD,aAAa,CAAC7E,OAAO,CAACnB,aAAa,EAAEiG,MAAM,CAAC;IAC3D;EACF,CAAC;EACDhJ,UAAU,CAACyD,SAAS,CAACwF,UAAU,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACzD,MAAM,CAACyD,UAAU,CAAC,CAAC;EACjC,CAAC;EACDjJ,UAAU,CAACyD,SAAS,CAACuE,aAAa,GAAG,UAAUrE,GAAG,EAAE;IAClD,OAAO,IAAI,CAAC6B,MAAM,CAACwC,aAAa,CAAC,IAAI,CAACzD,iBAAiB,CAACZ,GAAG,CAAC,CAAC;EAC/D,CAAC;EACD3D,UAAU,CAACyD,SAAS,CAACyF,MAAM,GAAG,UAAUvF,GAAG,EAAE;IAC3C,OAAO,IAAI,CAAC6B,MAAM,CAAC0D,MAAM,CAAC,IAAI,CAAC3E,iBAAiB,CAACZ,GAAG,CAAC,CAAC;EACxD,CAAC;EACD3D,UAAU,CAACyD,SAAS,CAAC0F,SAAS,GAAG,UAAUxF,GAAG,EAAE;IAC9C,OAAO,IAAI,CAAC6B,MAAM,CAAC2D,SAAS,CAAC,IAAI,CAAC5E,iBAAiB,CAACZ,GAAG,CAAC,CAAC;EAC3D,CAAC;EACD3D,UAAU,CAACyD,SAAS,CAAC2F,SAAS,GAAG,UAAUlI,UAAU,EAAE+D,GAAG,EAAE;IAC1D,IAAIY,KAAK,GAAG,IAAI;IAChB,IAAIC,KAAK,GAAG,IAAI,CAACN,MAAM;IACvB,OAAOtH,MAAM,CAACmL,OAAO,CAACnI,UAAU,CAAC,GAAG4E,KAAK,CAACsD,SAAS,CAACnK,GAAG,CAACiC,UAAU,EAAE,UAAUyC,GAAG,EAAE;MACjF,OAAOkC,KAAK,CAACtB,iBAAiB,CAACZ,GAAG,CAAC;IACrC,CAAC,CAAC,EAAEsB,GAAG,CAAC,GAAGa,KAAK,CAACsD,SAAS,CAAClI,UAAU,CAAC;EACxC,CAAC;EACD;AACF;AACA;AACA;EACElB,UAAU,CAACyD,SAAS,CAAC6F,QAAQ,GAAG,UAAUrE,GAAG,EAAE;IAC7C,IAAIsE,qBAAqB,GAAG,IAAI,CAAC1E,WAAW,CAAC0E,qBAAqB;IAClE,KAAK,IAAI5H,CAAC,GAAG,CAAC,EAAE6H,GAAG,GAAGD,qBAAqB,CAAC3H,MAAM,EAAED,CAAC,GAAG6H,GAAG,EAAE7H,CAAC,EAAE,EAAE;MAChE;MACA;MACA;MACA,IAAI2C,KAAK,CAAC,IAAI,CAACkB,MAAM,CAAC1B,GAAG,CAACyF,qBAAqB,CAAC5H,CAAC,CAAC,EAAEsD,GAAG,CAAC,CAAC,EAAE;QACzD,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;EACEjF,UAAU,CAACyD,SAAS,CAACgG,WAAW,GAAG,UAAUzH,IAAI,EAAE;IACjD,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAE6H,GAAG,GAAG,IAAI,CAAChE,MAAM,CAACY,KAAK,CAAC,CAAC,EAAEzE,CAAC,GAAG6H,GAAG,EAAE7H,CAAC,EAAE,EAAE;MACvD,IAAI,IAAI,CAAC6G,OAAO,CAAC7G,CAAC,CAAC,KAAKK,IAAI,EAAE;QAC5B,OAAOL,CAAC;MACV;IACF;IACA,OAAO,CAAC,CAAC;EACX,CAAC;EACD3B,UAAU,CAACyD,SAAS,CAACiF,WAAW,GAAG,UAAUzD,GAAG,EAAE;IAChD,OAAO,IAAI,CAACO,MAAM,CAACkD,WAAW,CAACzD,GAAG,CAAC;EACrC,CAAC;EACDjF,UAAU,CAACyD,SAAS,CAACiG,eAAe,GAAG,UAAUjB,QAAQ,EAAE;IACzD,OAAO,IAAI,CAACjD,MAAM,CAACkE,eAAe,CAACjB,QAAQ,CAAC;EAC9C,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACEzI,UAAU,CAACyD,SAAS,CAACkG,UAAU,GAAG,UAAUhG,GAAG,EAAE2E,KAAK,EAAE;IACtD,IAAIsB,eAAe,GAAGjG,GAAG,IAAI,IAAI,CAACT,mBAAmB,CAACS,GAAG,CAAC;IAC1D,IAAIhB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAAC+G,eAAe,EAAE;QACpB,MAAM,IAAIpF,KAAK,CAAC,sBAAsB,CAAC;MACzC;IACF;IACA,IAAIiE,QAAQ,GAAGmB,eAAe,IAAIA,eAAe,CAACtB,KAAK,CAAC;IACxD,IAAIG,QAAQ,IAAI,IAAI,IAAInE,KAAK,CAACmE,QAAQ,CAAC,EAAE;MACvC,OAAOnJ,eAAe;IACxB;IACA,OAAOmJ,QAAQ;EACjB,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEzI,UAAU,CAACyD,SAAS,CAACoG,gBAAgB,GAAG,UAAUlG,GAAG,EAAE2E,KAAK,EAAEwB,WAAW,EAAE;IACzE,OAAO,IAAI,CAACtE,MAAM,CAACqE,gBAAgB,CAAC,IAAI,CAACtF,iBAAiB,CAACZ,GAAG,CAAC,EAAE2E,KAAK,EAAEwB,WAAW,CAAC;EACtF,CAAC;EACD9J,UAAU,CAACyD,SAAS,CAACH,IAAI,GAAG,UAAU8B,IAAI,EAAE2E,EAAE,EAAEC,GAAG,EAAE;IACnD,YAAY;;IAEZ,IAAI9L,MAAM,CAAC+L,UAAU,CAAC7E,IAAI,CAAC,EAAE;MAC3B4E,GAAG,GAAGD,EAAE;MACRA,EAAE,GAAG3E,IAAI;MACTA,IAAI,GAAG,EAAE;IACX;IACA;IACA,IAAI8E,IAAI,GAAGF,GAAG,IAAI,IAAI;IACtB,IAAIG,UAAU,GAAGlL,GAAG,CAACW,mBAAmB,CAACwF,IAAI,CAAC,EAAE,IAAI,CAACb,iBAAiB,EAAE,IAAI,CAAC;IAC7E,IAAI,CAACiB,MAAM,CAAClC,IAAI,CAAC6G,UAAU,EAAED,IAAI,GAAGhM,MAAM,CAACkM,IAAI,CAACL,EAAE,EAAEG,IAAI,CAAC,GAAGH,EAAE,CAAC;EACjE,CAAC;EACD/J,UAAU,CAACyD,SAAS,CAAC4G,UAAU,GAAG,UAAUjF,IAAI,EAAE2E,EAAE,EAAEC,GAAG,EAAE;IACzD,YAAY;;IAEZ,IAAI9L,MAAM,CAAC+L,UAAU,CAAC7E,IAAI,CAAC,EAAE;MAC3B4E,GAAG,GAAGD,EAAE;MACRA,EAAE,GAAG3E,IAAI;MACTA,IAAI,GAAG,EAAE;IACX;IACA;IACA,IAAI8E,IAAI,GAAGF,GAAG,IAAI,IAAI;IACtB,IAAIG,UAAU,GAAGlL,GAAG,CAACW,mBAAmB,CAACwF,IAAI,CAAC,EAAE,IAAI,CAACb,iBAAiB,EAAE,IAAI,CAAC;IAC7E,IAAI,CAACiB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC8E,MAAM,CAACH,UAAU,EAAED,IAAI,GAAGhM,MAAM,CAACkM,IAAI,CAACL,EAAE,EAAEG,IAAI,CAAC,GAAGH,EAAE,CAAC;IAC/E,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;EACE/J,UAAU,CAACyD,SAAS,CAAC8G,WAAW,GAAG,UAAUhE,KAAK,EAAE;IAClD,YAAY;;IAEZ,IAAIV,KAAK,GAAG,IAAI;IAChB,IAAI2E,UAAU,GAAG,CAAC,CAAC;IACnB,IAAIpF,IAAI,GAAGlH,MAAM,CAACuM,IAAI,CAAClE,KAAK,CAAC;IAC7B,IAAI4D,UAAU,GAAG,EAAE;IACnBjM,MAAM,CAACoF,IAAI,CAAC8B,IAAI,EAAE,UAAUzB,GAAG,EAAE;MAC/B,IAAIC,MAAM,GAAGiC,KAAK,CAACtB,iBAAiB,CAACZ,GAAG,CAAC;MACzC6G,UAAU,CAAC5G,MAAM,CAAC,GAAG2C,KAAK,CAAC5C,GAAG,CAAC;MAC/BwG,UAAU,CAAC9H,IAAI,CAACuB,MAAM,CAAC;IACzB,CAAC,CAAC;IACF,IAAI,CAAC4B,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC+E,WAAW,CAACC,UAAU,CAAC;IACjD,OAAO,IAAI;EACb,CAAC;EACD;EACAxK,UAAU,CAACyD,SAAS,CAACiH,QAAQ,GAAG,UAAUtF,IAAI,EAAE2E,EAAE,EAAEC,GAAG,EAAE;IACvD,YAAY;;IAEZ,IAAI9L,MAAM,CAAC+L,UAAU,CAAC7E,IAAI,CAAC,EAAE;MAC3B4E,GAAG,GAAGD,EAAE;MACRA,EAAE,GAAG3E,IAAI;MACTA,IAAI,GAAG,EAAE;IACX;IACA;IACA4E,GAAG,GAAGA,GAAG,IAAI,IAAI;IACjB,IAAIW,MAAM,GAAG,EAAE;IACf,IAAI,CAACrH,IAAI,CAAC8B,IAAI,EAAE,YAAY;MAC1BuF,MAAM,CAACtI,IAAI,CAAC0H,EAAE,IAAIA,EAAE,CAACa,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;IAC9C,CAAC,EAAEb,GAAG,CAAC;IACP,OAAOW,MAAM;EACf,CAAC;EACD3K,UAAU,CAACyD,SAAS,CAACxE,GAAG,GAAG,UAAUmG,IAAI,EAAE2E,EAAE,EAAEC,GAAG,EAAEc,SAAS,EAAE;IAC7D,YAAY;;IAEZ;IACA,IAAIZ,IAAI,GAAGF,GAAG,IAAIc,SAAS,IAAI,IAAI;IACnC,IAAIX,UAAU,GAAGlL,GAAG,CAACW,mBAAmB,CAACwF,IAAI,CAAC,EAAE,IAAI,CAACb,iBAAiB,EAAE,IAAI,CAAC;IAC7E,IAAIwG,IAAI,GAAGjL,wBAAwB,CAAC,IAAI,CAAC;IACzCiL,IAAI,CAACvF,MAAM,GAAG,IAAI,CAACA,MAAM,CAACvG,GAAG,CAACkL,UAAU,EAAED,IAAI,GAAGhM,MAAM,CAACkM,IAAI,CAACL,EAAE,EAAEG,IAAI,CAAC,GAAGH,EAAE,CAAC;IAC5E,OAAOgB,IAAI;EACb,CAAC;EACD/K,UAAU,CAACyD,SAAS,CAACuH,MAAM,GAAG,UAAU5F,IAAI,EAAE2E,EAAE,EAAEC,GAAG,EAAEc,SAAS,EAAE;IAChE,IAAIjF,KAAK,GAAG,IAAI;IAChB;IACA,IAAIqE,IAAI,GAAGF,GAAG,IAAIc,SAAS,IAAI,IAAI;IACnC,IAAInI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC3E,MAAM,CAACoF,IAAI,CAAC1D,mBAAmB,CAACwF,IAAI,CAAC,EAAE,UAAUzB,GAAG,EAAE;QACpD,IAAIO,OAAO,GAAG2B,KAAK,CAACpB,gBAAgB,CAACd,GAAG,CAAC;QACzC,IAAI,CAACO,OAAO,CAAC+G,kBAAkB,EAAE;UAC/BC,OAAO,CAACC,KAAK,CAAC,8CAA8C,CAAC;QAC/D;MACF,CAAC,CAAC;IACJ;IACA,IAAIhB,UAAU,GAAGlL,GAAG,CAACW,mBAAmB,CAACwF,IAAI,CAAC,EAAE,IAAI,CAACb,iBAAiB,EAAE,IAAI,CAAC;IAC7E;IACA;IACA;IACA;IACA,IAAI,CAACiB,MAAM,CAACwF,MAAM,CAACb,UAAU,EAAED,IAAI,GAAGhM,MAAM,CAACkM,IAAI,CAACL,EAAE,EAAEG,IAAI,CAAC,GAAGH,EAAE,CAAC;EACnE,CAAC;EACD;AACF;AACA;AACA;EACE/J,UAAU,CAACyD,SAAS,CAAC2H,UAAU,GAAG,UAAUC,SAAS,EAAEC,IAAI,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACrF,IAAIT,IAAI,GAAGjL,wBAAwB,CAAC,IAAI,CAAC;IACzCiL,IAAI,CAACvF,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC4F,UAAU,CAAC,IAAI,CAAC7G,iBAAiB,CAAC8G,SAAS,CAAC,EAAEC,IAAI,EAAEC,WAAW,EAAEC,WAAW,CAAC;IACvG,OAAOT,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACE/K,UAAU,CAACyD,SAAS,CAACgI,gBAAgB,GAAG,UAAUC,cAAc,EAAEJ,IAAI,EAAE;IACtE,IAAIP,IAAI,GAAGjL,wBAAwB,CAAC,IAAI,CAAC;IACzCiL,IAAI,CAACvF,MAAM,GAAG,IAAI,CAACA,MAAM,CAACiG,gBAAgB,CAAC,IAAI,CAAClH,iBAAiB,CAACmH,cAAc,CAAC,EAAEJ,IAAI,CAAC;IACxF,OAAOP,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;AACA;EACE/K,UAAU,CAACyD,SAAS,CAACkI,cAAc,GAAG,UAAUD,cAAc,EAAEJ,IAAI,EAAE;IACpE,IAAIP,IAAI,GAAGjL,wBAAwB,CAAC,IAAI,CAAC;IACzCiL,IAAI,CAACvF,MAAM,GAAG,IAAI,CAACA,MAAM,CAACmG,cAAc,CAAC,IAAI,CAACpH,iBAAiB,CAACmH,cAAc,CAAC,EAAEJ,IAAI,CAAC;IACtF,OAAOP,IAAI;EACb,CAAC;EACD/K,UAAU,CAACyD,SAAS,CAACmI,cAAc,GAAG,UAAU3G,GAAG,EAAE;IACnD,OAAO,IAAI,CAACO,MAAM,CAACoG,cAAc,CAAC3G,GAAG,CAAC;EACxC,CAAC;EACD;AACF;AACA;EACE;EACAjF,UAAU,CAACyD,SAAS,CAACoI,YAAY,GAAG,UAAU5G,GAAG,EAAE;IACjD,IAAI/E,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAI0H,QAAQ,GAAG,IAAI,CAACgE,cAAc,CAAC3G,GAAG,CAAC;IACvC,OAAO,IAAI9G,KAAK,CAACyJ,QAAQ,EAAE1H,SAAS,EAAEA,SAAS,IAAIA,SAAS,CAAC4L,OAAO,CAAC;EACvE,CAAC;EACD;AACF;AACA;EACE9L,UAAU,CAACyD,SAAS,CAACsI,IAAI,GAAG,UAAUC,SAAS,EAAE;IAC/C,IAAIC,QAAQ,GAAG,IAAI;IACnB,OAAO,IAAI7N,UAAU,CAAC4N,SAAS,GAAGA,SAAS,CAACzG,QAAQ,CAAC,CAAC,CAAC0D,UAAU,CAAC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAAC,CAAC0D,UAAU,CAAC,CAAC,EAAE,UAAUhE,GAAG,EAAE;MACrH,OAAOvF,KAAK,CAACsM,SAAS,EAAE/G,GAAG,CAAC;IAC9B,CAAC,EAAE,UAAUA,GAAG,EAAE;MAChB,OAAOvF,KAAK,CAACuM,QAAQ,EAAEhH,GAAG,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACEjF,UAAU,CAACyD,SAAS,CAACyI,SAAS,GAAG,UAAU9D,GAAG,EAAE;IAC9C,IAAI+D,MAAM,GAAG,IAAI,CAAC5L,OAAO;IACzB,OAAO4L,MAAM,IAAIA,MAAM,CAAC/D,GAAG,CAAC;EAC9B,CAAC;EACDpI,UAAU,CAACyD,SAAS,CAAC2I,SAAS,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAE;IACrD,IAAI,CAAC/L,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,CAAC,CAAC;IACjC,IAAIvB,QAAQ,CAACqN,KAAK,CAAC,EAAE;MACnBnO,MAAM,CAACqK,MAAM,CAAC,IAAI,CAAChI,OAAO,EAAE8L,KAAK,CAAC;IACpC,CAAC,MAAM;MACL,IAAI,CAAC9L,OAAO,CAAC8L,KAAK,CAAC,GAAGC,GAAG;IAC3B;EACF,CAAC;EACD;AACF;AACA;EACE;EACAtM,UAAU,CAACyD,SAAS,CAAC8I,aAAa,GAAG,UAAUtH,GAAG,EAAEmD,GAAG,EAAE;IACvD,IAAIoE,UAAU,GAAG,IAAI,CAAC/L,YAAY,CAACwE,GAAG,CAAC;IACvC,IAAIqH,GAAG,GAAGE,UAAU,IAAIA,UAAU,CAACpE,GAAG,CAAC;IACvC,IAAIkE,GAAG,IAAI,IAAI,EAAE;MACf;MACA,OAAO,IAAI,CAACJ,SAAS,CAAC9D,GAAG,CAAC;IAC5B;IACA,OAAOkE,GAAG;EACZ,CAAC;EACD;AACF;AACA;EACEtM,UAAU,CAACyD,SAAS,CAACgJ,aAAa,GAAG,YAAY;IAC/C,OAAO,IAAI,CAAChM,YAAY,CAACmB,MAAM,GAAG,CAAC;EACrC,CAAC;EACD;AACF;AACA;EACE;EACA5B,UAAU,CAACyD,SAAS,CAACiJ,sBAAsB,GAAG,UAAUzH,GAAG,EAAEmD,GAAG,EAAE;IAChE,IAAIuE,WAAW,GAAG,IAAI,CAAClM,YAAY;IACnC,IAAI+L,UAAU,GAAGG,WAAW,CAAC1H,GAAG,CAAC;IACjC,IAAI,CAACuH,UAAU,EAAE;MACfA,UAAU,GAAGG,WAAW,CAAC1H,GAAG,CAAC,GAAG,CAAC,CAAC;IACpC;IACA,IAAIqH,GAAG,GAAGE,UAAU,CAACpE,GAAG,CAAC;IACzB,IAAIkE,GAAG,IAAI,IAAI,EAAE;MACfA,GAAG,GAAG,IAAI,CAACJ,SAAS,CAAC9D,GAAG,CAAC;MACzB;MACA,IAAIlK,MAAM,CAACmL,OAAO,CAACiD,GAAG,CAAC,EAAE;QACvBA,GAAG,GAAGA,GAAG,CAACvH,KAAK,CAAC,CAAC;MACnB,CAAC,MAAM,IAAI/F,QAAQ,CAACsN,GAAG,CAAC,EAAE;QACxBA,GAAG,GAAGpO,MAAM,CAACqK,MAAM,CAAC,CAAC,CAAC,EAAE+D,GAAG,CAAC;MAC9B;MACAE,UAAU,CAACpE,GAAG,CAAC,GAAGkE,GAAG;IACvB;IACA,OAAOA,GAAG;EACZ,CAAC;EACD;EACAtM,UAAU,CAACyD,SAAS,CAACmJ,aAAa,GAAG,UAAU3H,GAAG,EAAEmD,GAAG,EAAEE,KAAK,EAAE;IAC9D,IAAIkE,UAAU,GAAG,IAAI,CAAC/L,YAAY,CAACwE,GAAG,CAAC,IAAI,CAAC,CAAC;IAC7C,IAAI,CAACxE,YAAY,CAACwE,GAAG,CAAC,GAAGuH,UAAU;IACnC,IAAIxN,QAAQ,CAACoJ,GAAG,CAAC,EAAE;MACjBlK,MAAM,CAACqK,MAAM,CAACiE,UAAU,EAAEpE,GAAG,CAAC;IAChC,CAAC,MAAM;MACLoE,UAAU,CAACpE,GAAG,CAAC,GAAGE,KAAK;IACzB;EACF,CAAC;EACD;AACF;AACA;EACEtI,UAAU,CAACyD,SAAS,CAACoJ,cAAc,GAAG,YAAY;IAChD,IAAI,CAACtM,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACE,YAAY,GAAG,EAAE;EACxB,CAAC;EACDT,UAAU,CAACyD,SAAS,CAACqJ,SAAS,GAAG,UAAU1E,GAAG,EAAEkE,GAAG,EAAE;IACnDtN,QAAQ,CAACoJ,GAAG,CAAC,GAAGlK,MAAM,CAACqK,MAAM,CAAC,IAAI,CAAC/H,OAAO,EAAE4H,GAAG,CAAC,GAAG,IAAI,CAAC5H,OAAO,CAAC4H,GAAG,CAAC,GAAGkE,GAAG;EAC5E,CAAC;EACD;AACF;AACA;EACEtM,UAAU,CAACyD,SAAS,CAACsJ,SAAS,GAAG,UAAU3E,GAAG,EAAE;IAC9C,OAAO,IAAI,CAAC5H,OAAO,CAAC4H,GAAG,CAAC;EAC1B,CAAC;EACD;AACF;AACA;EACEpI,UAAU,CAACyD,SAAS,CAACuJ,aAAa,GAAG,UAAU/H,GAAG,EAAE;IAClD,OAAO,IAAI,CAACvE,YAAY,CAACuE,GAAG,CAAC;EAC/B,CAAC;EACD;AACF;AACA;EACEjF,UAAU,CAACyD,SAAS,CAACwJ,aAAa,GAAG,UAAUhI,GAAG,EAAEiI,MAAM,EAAEC,KAAK,EAAE;IACjE,IAAI,CAACzM,YAAY,CAACuE,GAAG,CAAC,GAAGkI,KAAK,GAAGjP,MAAM,CAACqK,MAAM,CAAC,IAAI,CAAC7H,YAAY,CAACuE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAEiI,MAAM,CAAC,GAAGA,MAAM;EAC/F,CAAC;EACD;AACF;AACA;EACElN,UAAU,CAACyD,SAAS,CAAC2J,gBAAgB,GAAG,YAAY;IAClD,IAAI,CAAC1M,YAAY,CAACkB,MAAM,GAAG,CAAC;EAC9B,CAAC;EACD;AACF;AACA;EACE5B,UAAU,CAACyD,SAAS,CAAC4J,gBAAgB,GAAG,UAAUpI,GAAG,EAAEqI,EAAE,EAAE;IACzD,IAAIC,WAAW,GAAG,IAAI,CAACrN,SAAS,IAAI,IAAI,CAACA,SAAS,CAACqN,WAAW;IAC9D3O,eAAe,CAAC2O,WAAW,EAAE,IAAI,CAACC,QAAQ,EAAEvI,GAAG,EAAEqI,EAAE,CAAC;IACpD,IAAI,CAAC3M,WAAW,CAACsE,GAAG,CAAC,GAAGqI,EAAE;EAC5B,CAAC;EACDtN,UAAU,CAACyD,SAAS,CAACgK,gBAAgB,GAAG,UAAUxI,GAAG,EAAE;IACrD,OAAO,IAAI,CAACtE,WAAW,CAACsE,GAAG,CAAC;EAC9B,CAAC;EACDjF,UAAU,CAACyD,SAAS,CAACiK,iBAAiB,GAAG,UAAU3D,EAAE,EAAE4D,OAAO,EAAE;IAC9DzP,MAAM,CAACoF,IAAI,CAAC,IAAI,CAAC3C,WAAW,EAAE,UAAU2M,EAAE,EAAErI,GAAG,EAAE;MAC/C,IAAIqI,EAAE,EAAE;QACNvD,EAAE,IAAIA,EAAE,CAAC6D,IAAI,CAACD,OAAO,EAAEL,EAAE,EAAErI,GAAG,CAAC;MACjC;IACF,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;AACA;EACEjF,UAAU,CAACyD,SAAS,CAACoK,YAAY,GAAG,UAAU9C,IAAI,EAAE;IAClD,IAAI,CAACA,IAAI,EAAE;MACTA,IAAI,GAAG,IAAI/K,UAAU,CAAC,IAAI,CAACqB,OAAO,GAAG,IAAI,CAACA,OAAO,GAAGpC,GAAG,CAAC,IAAI,CAACiC,UAAU,EAAE,IAAI,CAACiD,WAAW,EAAE,IAAI,CAAC,EAAE,IAAI,CAACjE,SAAS,CAAC;IACnH;IACAL,kBAAkB,CAACkL,IAAI,EAAE,IAAI,CAAC;IAC9BA,IAAI,CAACvF,MAAM,GAAG,IAAI,CAACA,MAAM;IACzB,OAAOuF,IAAI;EACb,CAAC;EACD;AACF;AACA;EACE/K,UAAU,CAACyD,SAAS,CAACqK,UAAU,GAAG,UAAUC,UAAU,EAAEC,cAAc,EAAE;IACtE,IAAIC,cAAc,GAAG,IAAI,CAACF,UAAU,CAAC;IACrC,IAAI,CAAC7P,MAAM,CAAC+L,UAAU,CAACgE,cAAc,CAAC,EAAE;MACtC;IACF;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,IAAI,EAAE;IACnD,IAAI,CAACA,gBAAgB,CAAC7L,IAAI,CAAC0L,UAAU,CAAC;IACtC,IAAI,CAACA,UAAU,CAAC,GAAG,YAAY;MAC7B,IAAII,GAAG,GAAGF,cAAc,CAACrD,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAC/C,OAAOmD,cAAc,CAACpD,KAAK,CAAC,IAAI,EAAE,CAACuD,GAAG,CAAC,CAACC,MAAM,CAAClQ,MAAM,CAAC6G,KAAK,CAAC8F,SAAS,CAAC,CAAC,CAAC;IAC1E,CAAC;EACH,CAAC;EACD;EACA;EACA;EACA7K,UAAU,CAACqO,aAAa,GAAG,YAAY;IACrC5O,oBAAoB,GAAG,SAAAA,CAAUiG,IAAI,EAAE;MACrC,IAAIlE,kBAAkB,GAAGkE,IAAI,CAACxC,mBAAmB;MACjDhF,MAAM,CAACoF,IAAI,CAAC9B,kBAAkB,EAAE,UAAUoI,eAAe,EAAEjG,GAAG,EAAE;QAC9D,IAAIO,OAAO,GAAGwB,IAAI,CAAC1C,SAAS,CAACW,GAAG,CAAC;QACjC;QACA,IAAIuD,WAAW,GAAGhD,OAAO,CAACgD,WAAW;QACrC,IAAIpB,KAAK,GAAGJ,IAAI,CAACF,MAAM;QACvB,IAAI0B,WAAW,EAAE;UACf0C,eAAe,GAAGpI,kBAAkB,CAACmC,GAAG,CAAC,GAAG,IAAIzE,cAAc,CAACgI,WAAW,CAAC4B,UAAU,CAAClH,MAAM,CAAC;UAC7F;UACA;UACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiI,eAAe,CAAChI,MAAM,EAAED,CAAC,EAAE,EAAE;YAC/CiI,eAAe,CAACjI,CAAC,CAAC,GAAGrC,eAAe;UACtC;UACA,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmE,KAAK,CAACM,KAAK,CAAC,CAAC,EAAEzE,CAAC,EAAE,EAAE;YACtC;YACAiI,eAAe,CAAC9D,KAAK,CAAChC,GAAG,CAACI,OAAO,CAACnB,aAAa,EAAEpB,CAAC,CAAC,CAAC,GAAGA,CAAC;UAC1D;QACF;MACF,CAAC,CAAC;IACJ,CAAC;IACDhC,kBAAkB,GAAG,SAAAA,CAAU+F,IAAI,EAAE9B,MAAM,EAAEqB,GAAG,EAAE;MAChD,OAAOvG,mBAAmB,CAACgH,IAAI,CAACiD,YAAY,CAAC/E,MAAM,EAAEqB,GAAG,CAAC,EAAE,IAAI,CAAC;IAClE,CAAC;IACD;AACJ;AACA;IACIvF,KAAK,GAAG,SAAAA,CAAUgG,IAAI,EAAE+C,QAAQ,EAAE;MAChC,IAAIX,EAAE,GAAGpC,IAAI,CAACpF,OAAO,CAACmI,QAAQ,CAAC;MAC/B,IAAIX,EAAE,IAAI,IAAI,IAAIpC,IAAI,CAAChD,SAAS,IAAI,IAAI,EAAE;QACxCoF,EAAE,GAAGnI,kBAAkB,CAAC+F,IAAI,EAAEA,IAAI,CAAChD,SAAS,EAAE+F,QAAQ,CAAC;MACzD;MACA,IAAIX,EAAE,IAAI,IAAI,EAAE;QACdA,EAAE,GAAGzI,SAAS,GAAGoJ,QAAQ;MAC3B;MACA,OAAOX,EAAE;IACX,CAAC;IACDlI,mBAAmB,GAAG,SAAAA,CAAUsB,UAAU,EAAE;MAC1C,IAAI,CAAChD,MAAM,CAACmL,OAAO,CAACnI,UAAU,CAAC,EAAE;QAC/BA,UAAU,GAAGA,UAAU,IAAI,IAAI,GAAG,CAACA,UAAU,CAAC,GAAG,EAAE;MACrD;MACA,OAAOA,UAAU;IACnB,CAAC;IACD;AACJ;AACA;IACIpB,wBAAwB,GAAG,SAAAA,CAAUwO,QAAQ,EAAE;MAC7C,IAAIvD,IAAI,GAAG,IAAI/K,UAAU,CAACsO,QAAQ,CAACjN,OAAO,GAAGiN,QAAQ,CAACjN,OAAO,GAAGpC,GAAG,CAACqP,QAAQ,CAACpN,UAAU,EAAEoN,QAAQ,CAACnK,WAAW,EAAEmK,QAAQ,CAAC,EAAEA,QAAQ,CAACpO,SAAS,CAAC;MAC7I;MACAL,kBAAkB,CAACkL,IAAI,EAAEuD,QAAQ,CAAC;MAClC,OAAOvD,IAAI;IACb,CAAC;IACDlL,kBAAkB,GAAG,SAAAA,CAAU0O,MAAM,EAAEC,MAAM,EAAE;MAC7CtQ,MAAM,CAACoF,IAAI,CAAC/D,uBAAuB,CAAC6O,MAAM,CAACI,MAAM,CAACN,gBAAgB,IAAI,EAAE,CAAC,EAAE,UAAUO,QAAQ,EAAE;QAC7F,IAAID,MAAM,CAAC9J,cAAc,CAAC+J,QAAQ,CAAC,EAAE;UACnCF,MAAM,CAACE,QAAQ,CAAC,GAAGD,MAAM,CAACC,QAAQ,CAAC;QACrC;MACF,CAAC,CAAC;MACFF,MAAM,CAACL,gBAAgB,GAAGM,MAAM,CAACN,gBAAgB;MACjDhQ,MAAM,CAACoF,IAAI,CAAC9D,gBAAgB,EAAE,UAAUiP,QAAQ,EAAE;QAChDF,MAAM,CAACE,QAAQ,CAAC,GAAGvQ,MAAM,CAACwQ,KAAK,CAACF,MAAM,CAACC,QAAQ,CAAC,CAAC;MACnD,CAAC,CAAC;MACFF,MAAM,CAAC1N,gBAAgB,GAAG3C,MAAM,CAACqK,MAAM,CAAC,CAAC,CAAC,EAAEiG,MAAM,CAAC3N,gBAAgB,CAAC;IACtE,CAAC;IACDd,cAAc,GAAG,SAAAA,CAAU2F,IAAI,EAAET,GAAG,EAAE;MACpC,IAAIU,QAAQ,GAAGD,IAAI,CAACrF,SAAS;MAC7B,IAAImH,MAAM,GAAG9B,IAAI,CAACpF,OAAO;MACzB,IAAIqO,UAAU,GAAGjJ,IAAI,CAAClD,WAAW;MACjC,IAAIoM,QAAQ,GAAGlJ,IAAI,CAAChD,SAAS;MAC7B,IAAIV,IAAI,GAAG2D,QAAQ,CAACV,GAAG,CAAC;MACxB,IAAI6C,EAAE,GAAGN,MAAM,CAACvC,GAAG,CAAC;MACpB,IAAIjD,IAAI,IAAI,IAAI,IAAI2M,UAAU,IAAI,IAAI,EAAE;QACtChJ,QAAQ,CAACV,GAAG,CAAC,GAAGjD,IAAI,GAAGrC,kBAAkB,CAAC+F,IAAI,EAAEiJ,UAAU,EAAE1J,GAAG,CAAC;MAClE;MACA,IAAI6C,EAAE,IAAI,IAAI,IAAI8G,QAAQ,IAAI,IAAI,EAAE;QAClCpH,MAAM,CAACvC,GAAG,CAAC,GAAG6C,EAAE,GAAGnI,kBAAkB,CAAC+F,IAAI,EAAEkJ,QAAQ,EAAE3J,GAAG,CAAC;MAC5D;MACA,IAAI6C,EAAE,IAAI,IAAI,IAAI9F,IAAI,IAAI,IAAI,EAAE;QAC9B,IAAI6M,eAAe,GAAGnJ,IAAI,CAACQ,gBAAgB;QAC3C,IAAI4I,KAAK,GAAGD,eAAe,CAAC7M,IAAI,CAAC,GAAG,CAAC6M,eAAe,CAAC7M,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QACpE8F,EAAE,GAAG9F,IAAI;QACT,IAAI8M,KAAK,GAAG,CAAC,EAAE;UACbhH,EAAE,IAAI,QAAQ,GAAGgH,KAAK;QACxB;QACAtH,MAAM,CAACvC,GAAG,CAAC,GAAG6C,EAAE;MAClB;IACF,CAAC;EACH,CAAC,CAAC,CAAC;EACH,OAAO9H,UAAU;AACnB,CAAC,CAAC,CAAC;AACH,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}