{"ast": null, "code": "var throttle = require('./throttle');\nvar debounce = require('./debounce');\nmodule.exports = {\n  throttle: throttle,\n  debounce: debounce\n};", "map": {"version": 3, "names": ["throttle", "require", "debounce", "module", "exports"], "sources": ["D:/FastBI/datafront/node_modules/throttle-debounce/index.js"], "sourcesContent": ["var throttle = require('./throttle');\nvar debounce = require('./debounce');\n\nmodule.exports = {\n\tthrottle: throttle,\n\tdebounce: debounce\n};\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;AACpC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,YAAY,CAAC;AAEpCE,MAAM,CAACC,OAAO,GAAG;EAChBJ,QAAQ,EAAEA,QAAQ;EAClBE,QAAQ,EAAEA;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}