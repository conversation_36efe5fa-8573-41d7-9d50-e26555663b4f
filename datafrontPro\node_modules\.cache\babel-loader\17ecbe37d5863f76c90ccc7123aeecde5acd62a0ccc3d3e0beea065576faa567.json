{"ast": null, "code": "import axios from 'axios';\nexport const dataApi = {\n  getAllTables() {\n    return axios.get('/data/tables');\n  },\n  getTableIndicators(tableCode) {\n    return axios.get(`/api/data/tables/${tableCode}/indicators`);\n  },\n  getTableSample(tableCode) {\n    return axios.get(`/api/data/tables/${tableCode}/sample`);\n  },\n  queryByQuestion(tableCode, question) {\n    return axios.get('/api/analytics/query', {\n      params: {\n        tableCode,\n        question\n      }\n    });\n  }\n};", "map": {"version": 3, "names": ["axios", "dataApi", "getAllTables", "get", "getTableIndicators", "tableCode", "getTableSample", "queryByQuestion", "question", "params"], "sources": ["E:/AllProject/datafront/src/api/index.js"], "sourcesContent": ["import axios from 'axios'\r\n\r\nexport const dataApi = {\r\n  getAllTables() {\r\n    return axios.get('/data/tables')\r\n  },\r\n  getTableIndicators(tableCode) {\r\n    return axios.get(`/api/data/tables/${tableCode}/indicators`)\r\n  },\r\n  getTableSample(tableCode) {\r\n    return axios.get(`/api/data/tables/${tableCode}/sample`)\r\n  },\r\n  queryByQuestion(tableCode, question) {\r\n    return axios.get('/api/analytics/query', {\r\n      params: {\r\n        tableCode,\r\n        question\r\n      }\r\n    })\r\n  }\r\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,MAAMC,OAAO,GAAG;EACrBC,YAAYA,CAAA,EAAG;IACb,OAAOF,KAAK,CAACG,GAAG,CAAC,cAAc,CAAC;EAClC,CAAC;EACDC,kBAAkBA,CAACC,SAAS,EAAE;IAC5B,OAAOL,KAAK,CAACG,GAAG,CAAC,oBAAoBE,SAAS,aAAa,CAAC;EAC9D,CAAC;EACDC,cAAcA,CAACD,SAAS,EAAE;IACxB,OAAOL,KAAK,CAACG,GAAG,CAAC,oBAAoBE,SAAS,SAAS,CAAC;EAC1D,CAAC;EACDE,eAAeA,CAACF,SAAS,EAAEG,QAAQ,EAAE;IACnC,OAAOR,KAAK,CAACG,GAAG,CAAC,sBAAsB,EAAE;MACvCM,MAAM,EAAE;QACNJ,SAAS;QACTG;MACF;IACF,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}