{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { dataApi } from './api/index.js';\nimport { v4 as uuidv4 } from 'uuid';\nimport axios from 'axios';\nimport ChartDisplay from './components/ChartDisplay.vue';\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas';\nimport { jsPDF } from 'jspdf';\nimport * as echarts from 'echarts';\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      // 原始树结构\n      datasets: [],\n      // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [],\n      // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false,\n      // 新增：控制原始响应弹出层\n      lastRawResponse: '',\n      // 新增：存储最后收到的原始响应\n      drawer: false,\n      //抽屉展示\n      direction: 'rtl',\n      //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      apiToken: 'eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2OTEyYzNiLTQ5ZmMtNDAyMC1iMzRiLWE3YWJlNmY1MTI0MSJ9.J4QCC6qh2GZ7ENDHuZQWQ1GvGOQ1HOr8gW34KXIHV9N_73Jppx-qmEwpq0AHlXM3DUjd5EAlpIVAvzJygm_ldg',\n      // 示例token，实际应该从登录后存储\n      currentAnalysisDataset: null,\n      // 存储当前用于智能分析的数据集信息\n      innerDrawer: false,\n      tableList: [],\n      exportingAll: false // 新增：控制完整对话导出状态\n    };\n  },\n  mounted() {\n    // 存储token到localStorage，供API使用\n    localStorage.setItem('de_token', this.apiToken);\n    this.loadTables();\n    this.initMemoryId();\n    this.addWelcomeMessage();\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\"帮我生成一个销售额柱状图\", \"展示近六个月的销售趋势折线图\", \"按照区域统计销售量并生成饼图\", \"帮我做一个按产品类别的销量对比图\"];\n  },\n  updated() {\n    this.scrollToBottom();\n  },\n  methods: {\n    SelectDataList() {\n      this.loadTables();\n      this.drawer = true;\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id');\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4());\n        localStorage.setItem('user_memory_id', storedMemoryId);\n      }\n      this.memoryId = storedMemoryId;\n    },\n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0;\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i];\n        number = number * 16 + (parseInt(hexValue, 16) || 0);\n      }\n      return number % 1000000;\n    },\n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      });\n    },\n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight;\n      }\n    },\n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables();\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data;\n          this.datasets = this.flattenDatasets(this.tables);\n          this.filteredDatasets = this.datasets;\n        } else {\n          this.tables = [];\n          this.datasets = [];\n          this.filteredDatasets = [];\n        }\n      } catch (e) {\n        this.tables = [];\n        this.datasets = [];\n        this.filteredDatasets = [];\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = [];\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node);\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children));\n        }\n      }\n      return result;\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase();\n      if (!keyword) {\n        this.filteredDatasets = this.datasets;\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword));\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n\n    // 测试详情请求方法\n    // async testDetailRequest() {\n    //   if (this.datasets.length > 0) {\n    //     const testDataset = this.datasets[0];\n    //     console.log('测试数据集:', testDataset);\n    //     alert(`正在请求数据集详情，ID: ${testDataset.id}`);\n    //     await this.showDatasetDetail(testDataset);\n    //   } else {\n    //     alert('没有可用的数据集');\n    //   }\n    // },\n\n    // 智能分析数据集\n    analyzeDataset() {\n      if (!this.currentDatasetDetail || !this.datasetFields.length) {\n        this.$message.warning('当前数据集没有可用字段');\n        return;\n      }\n\n      // 提取所需的字段信息\n      const fieldsInfo = this.datasetFields.map(field => {\n        // 只保留需要的字段属性\n        return {\n          id: field.id,\n          originName: field.originName || '',\n          name: field.name || '',\n          dataeaseName: field.dataeaseName || '',\n          groupType: field.groupType || '',\n          type: field.type || '',\n          datasourceId: field.datasourceId || '',\n          datasetTableId: field.datasetTableId || '',\n          datasetGroupId: field.datasetGroupId || ''\n        };\n      });\n\n      // 存储当前数据集信息，包括ID和字段\n      this.currentAnalysisDataset = {\n        id: this.currentDatasetDetail.id,\n        name: this.currentDatasetDetail.name,\n        fields: fieldsInfo\n      };\n\n      // 关闭详情抽屉，返回到聊天界面\n      this.dialogVisible = false;\n\n      // 提示用户\n      this.$message.success(`已选择数据集\"${this.currentDatasetDetail.name}\"进行智能分析，请在下方输入您的问题`);\n\n      // 自动聚焦到问题输入框\n      this.$nextTick(() => {\n        const inputEl = document.querySelector('.question-input input');\n        if (inputEl) inputEl.focus();\n      });\n    },\n    async showDatasetDetail(dataset) {\n      // alert(`showDatasetDetail 被调用，数据集ID: ${dataset?.id || '未知'}`);\n      console.log('showDatasetDetail 被调用，参数:', dataset);\n\n      // 验证参数\n      if (!dataset || !dataset.id) {\n        console.error('无效的数据集参数:', dataset);\n        // alert('数据集参数无效，缺少ID');\n        return;\n      }\n\n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n      try {\n        console.log(`开始请求数据集详情，ID: ${dataset.id}`);\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n        console.log('数据集详情API响应:', res);\n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          console.log('数据集详情数据:', detail);\n          this.currentDatasetDetail = detail;\n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || detail.data && detail.data.fields || [];\n          // 数据内容\n          this.datasetData = detail.data && detail.data.data || [];\n          // 调试打印\n          console.log(`字段信息: ${this.datasetFields.length}个字段`);\n          console.log(`数据内容: ${this.datasetData.length}条记录`);\n          if (this.datasetFields.length === 0) {\n            console.warn('未找到字段信息');\n          }\n          if (this.datasetData.length === 0) {\n            console.warn('未找到数据内容');\n          }\n        } else {\n          console.error('API返回错误:', res.data);\n          alert(`API返回错误: ${res.data.msg || '未知错误'}`);\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n        }\n        this.dialogVisible = true;\n      } catch (e) {\n        console.error('请求数据集详情失败:', e);\n        alert(`请求失败: ${e.message || '未知错误'}`);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n\n      // 获取当前选中的数据集信息\n      // 构建发送给AI的完整消息\n      let aiMessage = this.question.trim();\n\n      // 如果有当前分析的数据集，添加数据集信息\n      if (this.currentAnalysisDataset) {\n        // 构建AI需要的格式\n        const datasetInfo = {\n          datasetId: this.currentAnalysisDataset.id,\n          datasetName: this.currentAnalysisDataset.name,\n          fields: this.currentAnalysisDataset.fields\n        };\n\n        // 将数据集信息添加到消息中\n        aiMessage = JSON.stringify({\n          question: this.question.trim(),\n          dataset: datasetInfo\n        });\n      }\n\n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      };\n      this.messages.push(userMsg);\n\n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      };\n      this.messages.push(botMsg);\n\n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1];\n\n      // 保存问题并清空输入框\n      const question = this.question;\n      this.question = '';\n      this.isSending = true;\n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = this.currentAnalysisDataset ? aiMessage : `${question}。当前数据集：未选择具体数据集`;\n\n        // 发送请求\n        await axios.post('http://localhost:8088/api/indicator/chat', {\n          memoryId: this.memoryId,\n          message\n        }, {\n          responseType: 'stream',\n          onDownloadProgress: e => {\n            const fullText = e.event.target.responseText; // 累积的完整文本\n            let newText = fullText.substring(lastMsg.content.length);\n            lastMsg.content += newText; // 增量更新\n            this.scrollToBottom(); // 实时滚动\n\n            // 保存原始响应\n            this.lastRawResponse = fullText;\n          }\n        });\n\n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false;\n        this.isSending = false;\n\n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error);\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。';\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      }\n    },\n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n\n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n\n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n\n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n\n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n\n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n\n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n\n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n\n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n\n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n\n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n\n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n\n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n\n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n\n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n\n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n\n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      this.messages.push(botMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n\n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n\n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [{\n            field: '类别1',\n            value: 100\n          }, {\n            field: '类别2',\n            value: 200\n          }, {\n            field: '类别3',\n            value: 150\n          }],\n          metrics: [{\n            name: '数值'\n          }]\n        }\n      };\n\n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n\n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [{\n              value: 1,\n              field: \"神朔\",\n              name: \"神朔\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"甘泉\",\n              name: \"甘泉\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"包神\",\n              name: \"包神\",\n              category: \"记录数*\"\n            }],\n            fields: [{\n              id: \"1746787308487\",\n              name: \"名称\",\n              groupType: \"d\"\n            }, {\n              id: \"-1\",\n              name: \"记录数*\",\n              groupType: \"q\"\n            }]\n          }\n        }\n      };\n\n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n\n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n\n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n\n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n\n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n\n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content.replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '').replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '').trim();\n\n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n\n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n\n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n\n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n\n        // 根据图表类型选择对应的处理函数\n        switch (chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n\n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n\n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n\n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2,\n          // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n\n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = canvas.height * imgWidth / canvas.width;\n\n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n\n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n\n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n\n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n\n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    // 导出完整对话方法\n    async exportAllConversation() {\n      try {\n        // 设置导出状态\n        this.exportingAll = true;\n\n        // 1. 提取所有图表消息\n        let chartMessages = [];\n\n        // 先找出所有包含图表的消息\n        for (let i = 0; i < this.messages.length; i++) {\n          const message = this.messages[i];\n          if (!message.isUser && message.chartConfig) {\n            chartMessages.push({\n              message: message,\n              index: i\n            });\n          }\n        }\n\n        // 如果没有图表，显示提示\n        if (chartMessages.length === 0) {\n          this.$message.warning('暂无图表数据。请先通过对话生成图表。');\n          this.exportingAll = false;\n          return;\n        }\n\n        // 2. 创建PDF\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 3. 处理每个图表，每个图表单独一页\n        for (let i = 0; i < chartMessages.length; i++) {\n          // 如果不是第一页，添加新页面\n          if (i > 0) {\n            pdf.addPage();\n          }\n          const chartMessage = chartMessages[i].message;\n          const chartConfig = chartMessage.chartConfig;\n\n          // 创建临时容器\n          const tempContainer = document.createElement('div');\n          tempContainer.style.position = 'absolute';\n          tempContainer.style.left = '-9999px';\n          tempContainer.style.width = '800px';\n          tempContainer.style.background = '#fff';\n          tempContainer.style.padding = '20px';\n          document.body.appendChild(tempContainer);\n\n          // 构建PDF标题和基本信息\n          const title = chartConfig.title || '数据分析图表';\n          const currentDate = new Date().toLocaleString();\n          const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n          let htmlContent = `\n            <div style=\"font-family: Arial, sans-serif; padding: 20px;\">\n              <h1 style=\"text-align: center; color: #333; font-size: 24px; margin-bottom: 30px;\">${title}</h1>\n              <div style=\"text-align: right; margin-bottom: 20px;\">\n                <p style=\"color: #666; margin: 5px 0;\">数据集: ${datasetName}</p>\n                <p style=\"color: #666; margin: 5px 0;\">生成时间: ${currentDate}</p>\n              </div>\n              \n              <div style=\"margin: 20px 0;\">\n                <h2 style=\"color: #333; font-size: 20px; margin-bottom: 15px;\">分析描述:</h2>\n          `;\n\n          // 添加描述\n          let description = chartMessage.content;\n\n          // 清理HTML标签，保留纯文本\n          if (description.includes('<')) {\n            const tempDiv = document.createElement('div');\n            tempDiv.innerHTML = description;\n            description = tempDiv.textContent || tempDiv.innerText || '';\n          }\n          htmlContent += `\n            <p style=\"margin: 15px 0; color: #333; line-height: 1.6;\">${description}</p>\n            \n            <div style=\"margin: 30px 0;\">\n              <div id=\"chart-container\" style=\"height: 400px; margin: 30px 0;\"></div>\n              \n              <div style=\"margin: 30px 0;\">\n                <h3 style=\"color: #333; font-size: 18px; margin-bottom: 15px;\">数据表格:</h3>\n                <div id=\"data-table\"></div>\n              </div>\n            </div>\n          `;\n          htmlContent += `</div>`;\n\n          // 设置HTML内容\n          tempContainer.innerHTML = htmlContent;\n\n          // 渲染图表\n          const chartContainer = tempContainer.querySelector('#chart-container');\n          const dataTableContainer = tempContainer.querySelector('#data-table');\n          if (chartContainer && dataTableContainer) {\n            // 初始化图表\n            const chartInstance = echarts.init(chartContainer);\n            let options;\n\n            // 复用你现有的图表处理逻辑\n            switch (chartConfig.type) {\n              case 'bar':\n                options = this.getBarChartOptions(chartConfig);\n                break;\n              case 'line':\n                options = this.getLineChartOptions(chartConfig);\n                break;\n              case 'pie':\n                options = this.getPieChartOptions(chartConfig);\n                break;\n              case 'bar-horizontal':\n                options = this.getBarHorizontalOptions(chartConfig);\n                break;\n              default:\n                options = this.getDefaultOptions(chartConfig);\n            }\n            chartInstance.setOption(options);\n\n            // 渲染数据表格\n            this.renderDataTable(dataTableContainer, chartConfig);\n\n            // 等待图表渲染完成\n            await new Promise(resolve => setTimeout(resolve, 1000));\n\n            // 使用html2canvas捕获当前页面内容\n            const canvas = await html2canvas(tempContainer, {\n              scale: 2,\n              useCORS: true,\n              allowTaint: true,\n              backgroundColor: '#ffffff'\n            });\n\n            // 将canvas转为图像\n            const imgData = canvas.toDataURL('image/png');\n\n            // 计算适当的宽度和高度以适应A4页面\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            const pageHeight = pdf.internal.pageSize.getHeight();\n            const imgWidth = pageWidth - 40; // 左右各20mm边距\n            const imgHeight = canvas.height * imgWidth / canvas.width;\n\n            // 如果图像高度超过页面高度，进行缩放\n            let finalImgHeight = imgHeight;\n            let finalImgWidth = imgWidth;\n            if (imgHeight > pageHeight - 40) {\n              // 上下各20mm边距\n              finalImgHeight = pageHeight - 40;\n              finalImgWidth = canvas.width * finalImgHeight / canvas.height;\n            }\n\n            // 添加图像到PDF，居中显示\n            const xPos = (pageWidth - finalImgWidth) / 2;\n            const yPos = 20; // 顶部边距\n\n            pdf.addImage(imgData, 'PNG', xPos, yPos, finalImgWidth, finalImgHeight);\n\n            // 清理\n            chartInstance.dispose();\n            document.body.removeChild(tempContainer);\n          }\n        }\n\n        // 4. 保存PDF\n        const fileName = `指标分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;\n        pdf.save(fileName);\n\n        // 5. 重置状态\n        this.exportingAll = false;\n        this.$message.success('指标导出成功');\n      } catch (error) {\n        console.error('指标导出失败:', error);\n        this.exportingAll = false;\n        this.$message.error('指标导出失败: ' + error.message);\n      }\n    },\n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n\n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n\n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n\n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n\n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n\n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n\n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      return options;\n    },\n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n\n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n\n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n\n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n\n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n\n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n\n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n\n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n\n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "v4", "uuidv4", "axios", "ChartDisplay", "html2canvas", "jsPDF", "echarts", "name", "components", "data", "description", "tablename", "tables", "datasets", "filteredDatasets", "searchKeyword", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "tableIndicators", "dialogVisible", "messages", "memoryId", "isSending", "messageListRef", "showRawResponsePanel", "lastRawResponse", "drawer", "direction", "currentDatasetDetail", "datasetFields", "datasetData", "apiToken", "currentAnalysisDataset", "innerDrawer", "tableList", "exportingAll", "mounted", "localStorage", "setItem", "loadTables", "initMemoryId", "addWelcomeMessage", "updated", "scrollToBottom", "methods", "SelectDataList", "storedMemoryId", "getItem", "uuidToNumber", "uuid", "number", "i", "length", "hexValue", "parseInt", "push", "isUser", "content", "isTyping", "$refs", "scrollTop", "scrollHeight", "res", "getAllTables", "code", "flattenDatasets", "e", "tree", "result", "node", "leaf", "children", "concat", "onSearchDataset", "keyword", "trim", "toLowerCase", "filter", "ds", "includes", "analyzeDataset", "$message", "warning", "fieldsInfo", "map", "field", "id", "originName", "dataeaseName", "groupType", "type", "datasourceId", "datasetTableId", "datasetGroupId", "fields", "success", "$nextTick", "inputEl", "document", "querySelector", "focus", "showDatasetDetail", "dataset", "console", "log", "error", "headers", "getDatasetDetail", "detail", "allFields", "warn", "alert", "msg", "message", "showSuggestions", "useQuestion", "q", "getTableFields", "table", "tableCode", "submitQuestion", "aiMessage", "datasetInfo", "datasetId", "datasetName", "JSON", "stringify", "userMsg", "botMsg", "lastMsg", "post", "responseType", "onDownloadProgress", "fullText", "event", "target", "responseText", "newText", "substring", "parseChartConfig", "chartDataIdMatch", "match", "chartDataId", "fetchChartDataById", "chartConfigMatch", "chartConfig", "parse", "tableId", "replace", "jsonRegex", "jsonMatch", "jsonStr", "parseError", "defaultConfig", "Date", "now", "toString", "title", "response", "get", "status", "$forceUpdate", "test<PERSON>hart", "testChartConfig", "testApiResponse", "value", "metrics", "json<PERSON><PERSON><PERSON>", "apiResponseMsg", "testRealData", "realData", "category", "realDataMsg", "showRawResponse", "exportToPDF", "$set", "tempContainer", "createElement", "style", "position", "left", "width", "background", "padding", "body", "append<PERSON><PERSON><PERSON>", "tableName", "currentDate", "toLocaleString", "tempDiv", "innerHTML", "textContent", "innerText", "chartContainer", "chart", "init", "options", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "setOption", "Promise", "resolve", "setTimeout", "dataTableContainer", "renderDataTable", "canvas", "scale", "useCORS", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "imgData", "toDataURL", "pdf", "orientation", "unit", "format", "imgWidth", "imgHeight", "height", "addImage", "save", "getTime", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "exportAllConversation", "chartMessages", "index", "addPage", "chartMessage", "htmlContent", "chartInstance", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "finalImgHeight", "finalImgWidth", "xPos", "yPos", "fileName", "toISOString", "slice", "createBasicChartOptions", "categories", "item", "text", "tooltip", "trigger", "xAxis", "yAxis", "series", "seriesMap", "for<PERSON>ach", "s", "Array", "fill", "Object", "values", "chartData", "isArray", "f", "xAxisData", "categoriesSet", "Set", "add", "from", "seriesData", "find", "axisPointer", "legend", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "container", "firstItem", "tableHTML", "header", "undefined"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <!-- 调试信息区域 -->\n    <div style=\"position: fixed; top: 10px; right: 10px; z-index: 9999; background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-width: 300px; max-height: 300px; overflow: auto;\">\n      <!-- <button @click=\"testDetailRequest()\">测试详情请求</button>\n      <div v-if=\"datasets.length > 0\">\n        <p>数据集数量: {{ datasets.length }}</p>\n        <p>第一个数据集ID: {{ datasets[0] && datasets[0].id }}</p>\n      </div> -->\n    </div>\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <div class=\"header-actions\" style=\"display: flex; align-items: center; margin-top: 10px;\">\n           \n          </div>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n        </div>\n        \n        <div class=\"data-selection\">\n          <h3>目前可用数据</h3>\n          <div class=\"data-sets\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\" v-for=\"(table, idx) in datasets.slice(0, 3)\" :key=\"table.id + '_' + idx\">\n                <el-card class=\"data-card\" @click.native=\"showDatasetDetail(table)\" style=\"background-color: #f9f9f9;\">\n                  \n                  <div class=\"data-header\">\n                    <span class=\"sample-tag\">样例</span>\n                    <span class=\"data-title\">{{ table.name }}</span>\n                    \n                    <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n                  </div>\n                  <div class=\"data-fields\">\n                    <el-tag\n                      v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])\"\n                      :key=\"field.id || idx\"\n                      size=\"mini\"\n                      type=\"info\"\n                      style=\"margin-right: 4px; margin-bottom: 4px;\"\n                    >\n                      {{ field.name || field }}\n                    </el-tag>\n                    <span v-if=\"table.fields && table.fields.length > 4\">...</span>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        \n        <!-- 聊天消息列表区域 -->\n        <div class=\"message-list\" ref=\"messageListRef\">\n          <div\n            v-for=\"(message, index) in messages\"\n            :key=\"index\"\n            :class=\"message.isUser ? 'message user-message' : 'message bot-message'\"\n          >\n            <!-- 聊天图标 -->\n            <div class=\"avatar-container\" v-if=\"!message.isUser\">\n              <div class=\"bot-avatar\">\n                <i class=\"el-icon-s-tools\"></i>\n              </div>\n            </div>\n            <!-- 消息内容 -->\n            <div class=\"message-content\">\n              <div v-html=\"message.content\"></div>\n              <!-- 如果消息中包含图表配置，则显示图表和导出按钮 -->\n              <div v-if=\"message.chartConfig\" class=\"chart-container\">\n                <div class=\"chart-actions\">\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-download\" \n                            @click=\"exportToPDF(message)\" :loading=\"message.exporting\">\n                    导出PDF\n                  </el-button>\n                </div>\n                <chart-display :chart-config=\"message.chartConfig\" ref=\"chartDisplay\"></chart-display>\n              </div>\n              <!-- loading动画 -->\n              <span\n                class=\"loading-dots\"\n                v-if=\"message.isTyping\"\n              >\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n              </span>\n            </div>\n            <div class=\"avatar-container\" v-if=\"message.isUser\">\n              <div class=\"user-avatar\">\n                <i class=\"el-icon-user\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 底部问题输入区域（移至message-list下方） -->\n        <div class=\"question-input-container\">\n          <span>👋直接问我问题，或在上方选择一个主题/数据开始！</span>\n          <div class=\"question-input-wrapper\">\n            <el-button type=\"text\" \n            style=\"margin-left: 10px;\" \n            size=\"small\"\n            @click=\"SelectDataList\" \n            icon=\"el-icon-upload2\">\n            选择数据\n          </el-button>\n            <el-button \n              type=\"text\"\n              size=\"small\"\n              icon=\"el-icon-bottom\" \n              @click=\"exportAllConversation\"\n              :loading=\"exportingAll\"\n              :disabled=\"messages.length <= 1\"\n              style=\"margin-left: 20px;\">\n              导出完整指标\n            </el-button>\n            <el-input \n              style=\"margin-bottom: 12px;width: 800px;\"\n              v-model=\"question\" \n              placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n              class=\"question-input\"\n              @keyup.enter.native=\"submitQuestion\"\n              :disabled=\"isSending\">\n            </el-input>\n            <div class=\"input-actions\">\n              <button class=\"action-btn\" @click=\"showSuggestions\">\n                <i class=\"el-icon-magic-stick\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testChart\" title=\"测试图表功能\">\n                <i class=\"el-icon-data-line\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testRealData\" title=\"测试实际数据\">\n                <i class=\"el-icon-s-data\"></i>\n              </button>\n              <button class=\"action-btn debug-btn\" @click=\"showRawResponse\" title=\"显示AI原始响应\">\n                <i class=\"el-icon-monitor\"></i>\n              </button>\n              <button class=\"action-btn send-btn\" @click=\"submitQuestion\" :disabled=\"isSending\">\n                <i class=\"el-icon-position\"></i>\n              </button>\n              \n            </div>\n          </div>\n          \n          <!-- 建议问题弹出层 -->\n          <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n            <div class=\"suggestions-title\">\n              <i class=\"el-icon-s-promotion\"></i> 官方推荐\n            </div>\n            <div class=\"suggestions-list\">\n              <div \n                v-for=\"(suggestion, index) in suggestedQuestions\" \n                :key=\"index\"\n                class=\"suggestion-item\"\n                @click=\"useQuestion(suggestion)\">\n                {{ suggestion }}\n              </div>\n            </div>\n          </div>\n          \n          <!-- AI原始响应弹出层 -->\n          <div v-if=\"showRawResponsePanel\" class=\"raw-response-panel\">\n            <div class=\"raw-response-title\">\n              <i class=\"el-icon-monitor\"></i> AI原始响应\n              <button class=\"close-btn\" @click=\"showRawResponsePanel = false\">\n                <i class=\"el-icon-close\"></i>\n              </button>\n            </div>\n            <pre class=\"raw-response-content\">{{ lastRawResponse }}</pre>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <el-drawer\n      title=\"数据详情\"\n      :visible.sync=\"dialogVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      \n     \n      <div style=\"padding: 20px\" v-if=\"currentDatasetDetail\">\n        <h3>{{currentDatasetDetail.name}}</h3>\n        <div style=\"margin-top: 10px; margin-bottom: 20px; text-align: right;\">\n          <el-button type=\"primary\" @click=\"analyzeDataset\" icon=\"el-icon-data-analysis\">\n            智能分析数据\n          </el-button>\n        </div>\n        <el-divider></el-divider>\n        \n        <h4>字段信息</h4>\n        <el-table :data=\"datasetFields\" style=\"width: 100%\">\n          <el-table-column prop=\"name\" label=\"字段名称\"></el-table-column>\n          <el-table-column prop=\"type\" label=\"字段类型\"></el-table-column>\n          <el-table-column prop=\"groupType\" label=\"分组类型\"></el-table-column>\n        </el-table>\n        \n        <el-divider></el-divider>\n        \n        <h4>数据预览</h4>\n        <el-table :data=\"datasetData.slice(0, 100)\" style=\"width: 100%\">\n          <el-table-column \n            v-for=\"field in datasetFields\" \n            :key=\"field.id || field.name\"\n            :prop=\"field.dataeaseName || field.name\"\n            :label=\"field.name\">\n          </el-table-column>\n        </el-table>\n        <div v-if=\"datasetData.length > 100\" class=\"data-limit-notice\">\n          显示前100条数据，共 {{ datasetData.length }} 条\n        </div>\n         <div v-if=\"datasetData.length < 100\" class=\"data-limit-notice\">\n          显示前{{ datasetData.length }}条数据，共 {{ datasetData.length }} 条\n        </div>\n      </div>\n    </el-drawer>\n    <el-drawer\n      title=\"数据集列表\"\n      :visible.sync=\"drawer\"\n      direction=\"rtl\"\n      size=\"45%\">\n      <div style=\"padding: 20px\">\n        <el-input\n          v-model=\"searchKeyword\"\n          placeholder=\"搜索数据集\"\n          @input=\"onSearchDataset\"\n          style=\"margin-bottom: 16px; width: 300px;\"\n        />\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\" v-for=\"(table, idx) in filteredDatasets\" :key=\"table.id + '_' + idx\">\n            <el-card class=\"data-card\" @click.native=\"showDatasetDetail(table)\" style=\"background-color: #f9f9f9;\">\n              <div class=\"data-header\">\n                <span class=\"sample-tag\">样例</span>\n                <span class=\"data-title\">{{ table.name }}</span>\n                <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n          </div>\n              <div class=\"data-fields\">\n                <el-tag\n                  v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])\"\n                  :key=\"field.id || idx\"\n                  size=\"mini\"\n                  type=\"info\"\n                  style=\"margin-right: 4px; margin-bottom: 4px;\"\n                >\n                  {{ field.name || field }}\n                </el-tag>\n                <span v-if=\"table.fields && table.fields.length > 4\">...</span>\n          </div>\n        </el-card>\n          </el-col>\n        </el-row>\n      </div>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api/index.js'\nimport { v4 as uuidv4 } from 'uuid'\nimport axios from 'axios'\nimport ChartDisplay from './components/ChartDisplay.vue'\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas'\nimport { jsPDF } from 'jspdf'\nimport * as echarts from 'echarts'\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [], // 原始树结构\n      datasets: [], // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [], // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false, // 新增：控制原始响应弹出层\n      lastRawResponse: '', // 新增：存储最后收到的原始响应\n      drawer:false,    //抽屉展示\n      direction: 'rtl', //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      apiToken: 'eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2OTEyYzNiLTQ5ZmMtNDAyMC1iMzRiLWE3YWJlNmY1MTI0MSJ9.J4QCC6qh2GZ7ENDHuZQWQ1GvGOQ1HOr8gW34KXIHV9N_73Jppx-qmEwpq0AHlXM3DUjd5EAlpIVAvzJygm_ldg', // 示例token，实际应该从登录后存储\n      currentAnalysisDataset: null, // 存储当前用于智能分析的数据集信息\n      innerDrawer:false,\n      tableList:[],\n      exportingAll: false, // 新增：控制完整对话导出状态\n    }\n  },\n  \n  mounted() {\n    // 存储token到localStorage，供API使用\n    localStorage.setItem('de_token', this.apiToken);\n    this.loadTables()\n    this.initMemoryId()\n    this.addWelcomeMessage()\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\n      \"帮我生成一个销售额柱状图\",\n      \"展示近六个月的销售趋势折线图\",\n      \"按照区域统计销售量并生成饼图\",\n      \"帮我做一个按产品类别的销量对比图\"\n    ]\n  },\n  updated() {\n    this.scrollToBottom()\n  },\n  methods: {\n    SelectDataList(){\n      this.loadTables()\n      this.drawer=true\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id')\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4())\n        localStorage.setItem('user_memory_id', storedMemoryId)\n      }\n      this.memoryId = storedMemoryId\n    },\n    \n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i]\n        number = number * 16 + (parseInt(hexValue, 16) || 0)\n      }\n      return number % 1000000\n    },\n    \n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      })\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight\n      }\n    },\n    \n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables()\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data\n          this.datasets = this.flattenDatasets(this.tables)\n          this.filteredDatasets = this.datasets\n        } else {\n          this.tables = []\n          this.datasets = []\n          this.filteredDatasets = []\n        }\n      } catch (e) {\n        this.tables = []\n        this.datasets = []\n        this.filteredDatasets = []\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = []\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node)\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children))\n        }\n      }\n      return result\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase()\n      if (!keyword) {\n        this.filteredDatasets = this.datasets\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword))\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n    \n    // 测试详情请求方法\n    // async testDetailRequest() {\n    //   if (this.datasets.length > 0) {\n    //     const testDataset = this.datasets[0];\n    //     console.log('测试数据集:', testDataset);\n    //     alert(`正在请求数据集详情，ID: ${testDataset.id}`);\n    //     await this.showDatasetDetail(testDataset);\n      //   } else {\n    //     alert('没有可用的数据集');\n    //   }\n    // },\n\n    // 智能分析数据集\n    analyzeDataset() {\n      if (!this.currentDatasetDetail || !this.datasetFields.length) {\n        this.$message.warning('当前数据集没有可用字段');\n        return;\n      }\n      \n      // 提取所需的字段信息\n      const fieldsInfo = this.datasetFields.map(field => {\n        // 只保留需要的字段属性\n        return {\n          id: field.id,\n          originName: field.originName || '',\n          name: field.name || '',\n          dataeaseName: field.dataeaseName || '',\n          groupType: field.groupType || '',\n          type: field.type || '',\n          datasourceId: field.datasourceId || '',\n          datasetTableId: field.datasetTableId || '',\n          datasetGroupId: field.datasetGroupId || ''\n        };\n      });\n      \n      // 存储当前数据集信息，包括ID和字段\n      this.currentAnalysisDataset = {\n        id: this.currentDatasetDetail.id,\n        name: this.currentDatasetDetail.name,\n        fields: fieldsInfo\n      };\n      \n      // 关闭详情抽屉，返回到聊天界面\n      this.dialogVisible = false;\n      \n      // 提示用户\n      this.$message.success(`已选择数据集\"${this.currentDatasetDetail.name}\"进行智能分析，请在下方输入您的问题`);\n      \n      // 自动聚焦到问题输入框\n      this.$nextTick(() => {\n        const inputEl = document.querySelector('.question-input input');\n        if (inputEl) inputEl.focus();\n      });\n    },\n  \n    async showDatasetDetail(dataset) {\n      // alert(`showDatasetDetail 被调用，数据集ID: ${dataset?.id || '未知'}`);\n      console.log('showDatasetDetail 被调用，参数:', dataset);\n      \n      // 验证参数\n      if (!dataset || !dataset.id) {\n        console.error('无效的数据集参数:', dataset);\n        // alert('数据集参数无效，缺少ID');\n        return;\n      }\n      \n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n      \n      try {\n        console.log(`开始请求数据集详情，ID: ${dataset.id}`);\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n        console.log('数据集详情API响应:', res);\n        \n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          console.log('数据集详情数据:', detail);\n          this.currentDatasetDetail = detail;\n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || (detail.data && detail.data.fields) || [];\n          // 数据内容\n          this.datasetData = (detail.data && detail.data.data) || [];\n          // 调试打印\n          console.log(`字段信息: ${this.datasetFields.length}个字段`);\n          console.log(`数据内容: ${this.datasetData.length}条记录`);\n          \n          if (this.datasetFields.length === 0) {\n            console.warn('未找到字段信息');\n          }\n          \n          if (this.datasetData.length === 0) {\n            console.warn('未找到数据内容');\n          }\n        } else {\n          console.error('API返回错误:', res.data);\n          alert(`API返回错误: ${res.data.msg || '未知错误'}`);\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n        }\n        this.dialogVisible = true;\n      } catch (e) {\n        console.error('请求数据集详情失败:', e);\n        alert(`请求失败: ${e.message || '未知错误'}`);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    \n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      // 获取当前选中的数据集信息\n      // 构建发送给AI的完整消息\n      let aiMessage = this.question.trim();\n      \n      // 如果有当前分析的数据集，添加数据集信息\n      if (this.currentAnalysisDataset) {\n        // 构建AI需要的格式\n        const datasetInfo = {\n          datasetId: this.currentAnalysisDataset.id,\n          datasetName: this.currentAnalysisDataset.name,\n          fields: this.currentAnalysisDataset.fields\n        };\n        \n        // 将数据集信息添加到消息中\n        aiMessage = JSON.stringify({\n          question: this.question.trim(),\n          dataset: datasetInfo\n        });\n      }\n      \n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      }\n      this.messages.push(userMsg)\n      \n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      }\n      this.messages.push(botMsg)\n      \n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1]\n      \n      // 保存问题并清空输入框\n      const question = this.question\n      this.question = ''\n      this.isSending = true\n      \n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = this.currentAnalysisDataset \n          ? aiMessage \n          : `${question}。当前数据集：未选择具体数据集`;\n        \n        // 发送请求\n        await axios.post(\n          'http://localhost:8088/api/indicator/chat',\n          { memoryId: this.memoryId, message },\n          {\n            responseType: 'stream',\n            onDownloadProgress: (e) => {\n              const fullText = e.event.target.responseText // 累积的完整文本\n              let newText = fullText.substring(lastMsg.content.length)\n              lastMsg.content += newText // 增量更新\n              this.scrollToBottom() // 实时滚动\n              \n              // 保存原始响应\n              this.lastRawResponse = fullText\n            }\n          }\n        )\n        \n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false\n        this.isSending = false\n        \n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error)\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'\n        lastMsg.isTyping = false\n        this.isSending = false\n      }\n    },\n    \n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n      \n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n        \n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n      \n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        \n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n          \n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n          \n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n            \n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, \n              '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            \n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      \n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n      \n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        \n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n          \n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          \n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            \n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n              \n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], \n                  '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n      \n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || \n          message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n        \n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        \n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    \n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        \n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n          \n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n          \n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n          \n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n          \n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    \n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n      \n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      \n      this.messages.push(botMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n      \n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n      \n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [\n            { field: '类别1', value: 100 },\n            { field: '类别2', value: 200 },\n            { field: '类别3', value: 150 }\n          ],\n          metrics: [{ name: '数值' }]\n        }\n      };\n      \n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      \n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      \n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    \n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n      \n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [\n              {value: 1, field: \"神朔\", name: \"神朔\", category: \"记录数*\"},\n              {value: 1, field: \"甘泉\", name: \"甘泉\", category: \"记录数*\"},\n              {value: 1, field: \"包神\", name: \"包神\", category: \"记录数*\"}\n            ],\n            fields: [\n              {id: \"1746787308487\", name: \"名称\", groupType: \"d\"},\n              {id: \"-1\", name: \"记录数*\", groupType: \"q\"}\n            ]\n          }\n        }\n      };\n      \n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n      \n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n      \n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      \n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      \n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n        \n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n        \n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n        \n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content\n          .replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '')\n          .replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '')\n          .trim();\n          \n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n        \n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n        \n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n        \n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n        \n        // 根据图表类型选择对应的处理函数\n        switch(chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n        \n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n        \n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        \n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n        \n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2, // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n        \n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = (canvas.height * imgWidth) / canvas.width;\n        \n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n        \n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n        \n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n        \n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n        \n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n        \n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    \n    // 导出完整对话方法\n    async exportAllConversation() {\n      try {\n        // 设置导出状态\n        this.exportingAll = true;\n        \n        // 1. 提取所有图表消息\n        let chartMessages = [];\n        \n        // 先找出所有包含图表的消息\n        for (let i = 0; i < this.messages.length; i++) {\n          const message = this.messages[i];\n          if (!message.isUser && message.chartConfig) {\n            chartMessages.push({\n              message: message,\n              index: i\n            });\n          }\n        }\n        \n        // 如果没有图表，显示提示\n        if (chartMessages.length === 0) {\n          this.$message.warning('暂无图表数据。请先通过对话生成图表。');\n          this.exportingAll = false;\n          return;\n        }\n        \n        // 2. 创建PDF\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 3. 处理每个图表，每个图表单独一页\n        for (let i = 0; i < chartMessages.length; i++) {\n          // 如果不是第一页，添加新页面\n          if (i > 0) {\n            pdf.addPage();\n          }\n          \n          const chartMessage = chartMessages[i].message;\n          const chartConfig = chartMessage.chartConfig;\n          \n          // 创建临时容器\n          const tempContainer = document.createElement('div');\n          tempContainer.style.position = 'absolute';\n          tempContainer.style.left = '-9999px';\n          tempContainer.style.width = '800px';\n          tempContainer.style.background = '#fff';\n          tempContainer.style.padding = '20px';\n          document.body.appendChild(tempContainer);\n          \n          // 构建PDF标题和基本信息\n          const title = chartConfig.title || '数据分析图表';\n          const currentDate = new Date().toLocaleString();\n          const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n          \n          let htmlContent = `\n            <div style=\"font-family: Arial, sans-serif; padding: 20px;\">\n              <h1 style=\"text-align: center; color: #333; font-size: 24px; margin-bottom: 30px;\">${title}</h1>\n              <div style=\"text-align: right; margin-bottom: 20px;\">\n                <p style=\"color: #666; margin: 5px 0;\">数据集: ${datasetName}</p>\n                <p style=\"color: #666; margin: 5px 0;\">生成时间: ${currentDate}</p>\n              </div>\n              \n              <div style=\"margin: 20px 0;\">\n                <h2 style=\"color: #333; font-size: 20px; margin-bottom: 15px;\">分析描述:</h2>\n          `;\n          \n          // 添加描述\n          let description = chartMessage.content;\n          \n          // 清理HTML标签，保留纯文本\n          if (description.includes('<')) {\n            const tempDiv = document.createElement('div');\n            tempDiv.innerHTML = description;\n            description = tempDiv.textContent || tempDiv.innerText || '';\n          }\n          \n          htmlContent += `\n            <p style=\"margin: 15px 0; color: #333; line-height: 1.6;\">${description}</p>\n            \n            <div style=\"margin: 30px 0;\">\n              <div id=\"chart-container\" style=\"height: 400px; margin: 30px 0;\"></div>\n              \n              <div style=\"margin: 30px 0;\">\n                <h3 style=\"color: #333; font-size: 18px; margin-bottom: 15px;\">数据表格:</h3>\n                <div id=\"data-table\"></div>\n              </div>\n            </div>\n          `;\n          \n          htmlContent += `</div>`;\n          \n          // 设置HTML内容\n          tempContainer.innerHTML = htmlContent;\n          \n          // 渲染图表\n          const chartContainer = tempContainer.querySelector('#chart-container');\n          const dataTableContainer = tempContainer.querySelector('#data-table');\n          \n          if (chartContainer && dataTableContainer) {\n            // 初始化图表\n            const chartInstance = echarts.init(chartContainer);\n            let options;\n            \n            // 复用你现有的图表处理逻辑\n            switch(chartConfig.type) {\n              case 'bar':\n                options = this.getBarChartOptions(chartConfig);\n                break;\n              case 'line':\n                options = this.getLineChartOptions(chartConfig);\n                break;\n              case 'pie':\n                options = this.getPieChartOptions(chartConfig);\n                break;\n              case 'bar-horizontal':\n                options = this.getBarHorizontalOptions(chartConfig);\n                break;\n              default:\n                options = this.getDefaultOptions(chartConfig);\n            }\n            \n            chartInstance.setOption(options);\n            \n            // 渲染数据表格\n            this.renderDataTable(dataTableContainer, chartConfig);\n            \n            // 等待图表渲染完成\n            await new Promise(resolve => setTimeout(resolve, 1000));\n            \n            // 使用html2canvas捕获当前页面内容\n            const canvas = await html2canvas(tempContainer, {\n              scale: 2,\n              useCORS: true,\n              allowTaint: true,\n              backgroundColor: '#ffffff'\n            });\n            \n            // 将canvas转为图像\n            const imgData = canvas.toDataURL('image/png');\n            \n            // 计算适当的宽度和高度以适应A4页面\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            const pageHeight = pdf.internal.pageSize.getHeight();\n            const imgWidth = pageWidth - 40; // 左右各20mm边距\n            const imgHeight = (canvas.height * imgWidth) / canvas.width;\n            \n            // 如果图像高度超过页面高度，进行缩放\n            let finalImgHeight = imgHeight;\n            let finalImgWidth = imgWidth;\n            \n            if (imgHeight > pageHeight - 40) { // 上下各20mm边距\n              finalImgHeight = pageHeight - 40;\n              finalImgWidth = (canvas.width * finalImgHeight) / canvas.height;\n            }\n            \n            // 添加图像到PDF，居中显示\n            const xPos = (pageWidth - finalImgWidth) / 2;\n            const yPos = 20; // 顶部边距\n            \n            pdf.addImage(imgData, 'PNG', xPos, yPos, finalImgWidth, finalImgHeight);\n            \n            // 清理\n            chartInstance.dispose();\n            document.body.removeChild(tempContainer);\n          }\n        }\n        \n        // 4. 保存PDF\n        const fileName = `指标分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;\n        pdf.save(fileName);\n        \n        // 5. 重置状态\n        this.exportingAll = false;\n        this.$message.success('指标导出成功');\n        \n      } catch (error) {\n        console.error('指标导出失败:', error);\n        this.exportingAll = false;\n        this.$message.error('指标导出失败: ' + error.message);\n      }\n    },\n    \n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n      \n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n      \n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n      \n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n          \n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n          \n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n          \n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      \n      return options;\n    },\n    \n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      \n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n      \n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        \n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n      \n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n      \n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n      \n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n        \n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n        \n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        \n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n      \n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.app-layout {\n  display: flex;\n  height: 100vh; /* 占满整个视口高度 */\n  overflow: hidden;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  background-color: #f9f9f9;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 25px;\n  text-align: center;\n  white-space: nowrap;\n  letter-spacing: 2px;\n  font-weight: 900;\n}\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  /* overflow-x: auto; */\n  display: flex;\n  flex-direction: column; /* 垂直排列子元素 */\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n  height: 100vh; /* 占满整个视口高度 */\n  box-sizing: border-box; /* 包含padding和border */\n}\n\n.header {\n  margin-bottom: 30px;\n  width: 100%;\n  text-align: center;\n  padding: 20px 0;\n}\n\n/* 欢迎容器 */\n.welcome-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 25px;\n  gap: 20px;\n}\n\n.welcome-icon {\n  font-size: 48px;\n  animation: float 3s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.welcome-content {\n  text-align: left;\n}\n\n.welcome-title {\n  margin: 0;\n  font-size: 32px;\n  font-weight: 700;\n  color: #1a1a1a;\n  line-height: 1.2;\n  margin-bottom: 8px;\n}\n\n.highlight-gradient {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 800;\n  position: relative;\n}\n\n.highlight-gradient::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  width: 100%;\n  height: 3px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 2px;\n  opacity: 0.6;\n}\n\n.welcome-subtitle {\n  color: #666;\n  font-size: 16px;\n  margin: 0;\n  line-height: 1.5;\n  max-width: 500px;\n}\n\n/* 功能徽章 */\n.feature-badges {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.badge {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 14px;\n  font-weight: 500;\n  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);\n  transition: all 0.3s ease;\n  cursor: default;\n}\n\n.badge:nth-child(2) {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.badge:nth-child(3) {\n  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);\n  color: #8b4513;\n  box-shadow: 0 4px 12px rgba(252, 182, 159, 0.3);\n}\n\n.badge:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);\n}\n\n.badge:nth-child(2):hover {\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n}\n\n.badge:nth-child(3):hover {\n  box-shadow: 0 6px 20px rgba(252, 182, 159, 0.4);\n}\n\n.badge-icon {\n  font-size: 16px;\n}\n\n.badge-text {\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.data-card {\n  cursor: pointer;\n  margin-bottom: 15px;\n  transition: all 0.3s;\n  width:200px;\n  height:100px;\n}\n\n.data-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.data-header {\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.sample-tag {\n  background-color: #ecf5ff;\n  color: #409eff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n.data-title {\n  margin-left: 5px;\n  margin-right: 5px;\n  font-weight: bold;\n}\n\n.common-tag {\n  background-color: #f5f7fa;\n  color: #909399;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-left: 5px;\n}\n\n.data-fields {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.field-item {\n  background-color: #f5f7fa;\n  padding: 2px 8px;\n  margin: 4px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.multi-line-input .el-input__inner {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  height: auto !important;\n  line-height: inherit;\n  padding: 6px 10px;\n  min-height: 40px;\n}\n\n/* 底部问题输入区域（关键修改） */\n.question-input-container {\n  /* 移除固定定位 */\n  /* position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%; */\n  \n  width: 100%;\n    /* background-color: #fff; */\n    /* border-top: 1px solid #ebeef5; */\n    padding: 25px 15px;\n    /* box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05); */\n    display: flex\n;\n    flex-direction: column;\n    align-items: center;\n    margin-top: 20px;\n}\n\n.question-input-container > span {\n  margin-bottom: 12px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.question-input-wrapper {\n  align-items: center;\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n  background-color: #f5f7fa;\n  border-radius: 20px;\n  padding: 8px 15px;\n  icon:\"el-icon-upload2\";\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n  float: right;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: #fff;\n  border: 1px solid #dcdfe6;\n  margin-left: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n}\n\n.send-btn {\n  background-color: #409eff;\n  color: white;\n  border: none;\n}\n\n.test-btn {\n  background-color: #67c23a;\n  color: white;\n  border: none;\n}\n\n.debug-btn {\n  background-color: #909399;\n  color: white;\n  border: none;\n}\n\n.send-btn:disabled {\n  background-color: #c0c4cc;\n  cursor: not-allowed;\n}\n\n/* Suggested Questions Panel */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n\n/* Message list style */\n.message-list {\n  width: 100%;\n  max-height: calc(100vh - 250px); /* 修改为响应式高度，减去头部和底部的高度 */\n  overflow-y: auto;\n  margin-top: 20px;\n  padding: 10px;\n  background-color: transparent;\n  border-radius: 8px;\n  flex: 1; /* 让消息列表占据剩余空间 */\n}\n\n.message {\n  margin-bottom: 20px;\n  display: flex;\n  position: relative;\n  max-width: 80%;\n  margin-left: 150px;\n}\n\n.message-content {\n  padding: 12px 15px;\n  border-radius: 6px;\n  line-height: 1.5;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n  align-self: flex-end;\n  margin-left: auto;\n}\n\n.user-message .message-content {\n  background-color: #ecf5ff;\n  margin-right: 10px;\n}\n\n.bot-message {\n  align-self: flex-start;\n}\n\n.bot-message .message-content {\n  background-color: #f5f7fa;\n  margin-left: 10px;\n}\n\n.avatar-container {\n  display: flex;\n  align-items: flex-start;\n}\n\n.bot-avatar, .user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bot-avatar {\n  background-color: #4caf50;\n  color: white;\n}\n\n.user-avatar {\n  background-color: #2196f3;\n  color: white;\n}\n\n/* Loading animation */\n.loading-dots {\n  display: inline-block;\n}\n\n.dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  background-color: #999;\n  border-radius: 50%;\n  margin: 0 2px;\n  animation: pulse 1.2s infinite ease-in-out both;\n}\n\n.dot:nth-child(2) {\n  animation-delay: -0.4s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: -0.8s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(0.6);\n    opacity: 0.4;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* Styles related to charts */\n.chart-container {\n  position: relative;\n  margin-top: 15px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 10px;\n  background-color: #fff;\n}\n\n/* 确保图表容器有足够高度 */\n.chart-canvas {\n  min-height: 300px;\n}\n\n.chart-notice {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n  font-weight: bold;\n}\n\n/* AI original response popup layer style */\n.raw-response-panel {\n  position: fixed;\n  bottom: 100px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 800px;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n  z-index: 1000;\n  max-height: 60%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.raw-response-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 16px;\n  color: #303133;\n  background-color: #f5f7fa;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.raw-response-title .el-icon-monitor {\n  margin-right: 8px;\n}\n\n.raw-response-title .close-btn {\n  background-color: #f5f7fa;\n  border: 1px solid #ebeef5;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.raw-response-title .close-btn:hover {\n  background-color: #ebeef5;\n}\n\n.raw-response-content {\n  flex: 1;\n  padding: 15px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #303133;\n  white-space: pre-wrap;\n  word-break: break-all;\n  overflow-wrap: break-word;\n}\n\n/* 添加PDF导出相关样式 */\n.chart-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-bottom: 10px;\n}\n\n/* 数据限制提示样式 */\n.data-limit-notice {\n  margin-top: 10px;\n  padding: 8px;\n  background-color: #f0f9eb;\n  color: #67c23a;\n  border-radius: 4px;\n  font-size: 13px;\n  text-align: center;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;AAsRA,SAAAA,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,YAAA;AACA;AACA,OAAAC,WAAA;AACA,SAAAC,KAAA;AACA,YAAAC,OAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MAAA;MACAC,QAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;MACAC,oBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,MAAA;MAAA;MACAC,SAAA;MAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MAAA;MACAC,sBAAA;MAAA;MACAC,WAAA;MACAC,SAAA;MACAC,YAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,YAAA,CAAAC,OAAA,kBAAAP,QAAA;IACA,KAAAQ,UAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;IACA;IACA,KAAAzB,kBAAA,IACA,gBACA,kBACA,kBACA,mBACA;EACA;EACA0B,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,eAAA;MACA,KAAAN,UAAA;MACA,KAAAb,MAAA;IACA;IACA;IACAc,aAAA;MACA,IAAAM,cAAA,GAAAT,YAAA,CAAAU,OAAA;MACA,KAAAD,cAAA;QACAA,cAAA,QAAAE,YAAA,CAAAnD,MAAA;QACAwC,YAAA,CAAAC,OAAA,mBAAAQ,cAAA;MACA;MACA,KAAAzB,QAAA,GAAAyB,cAAA;IACA;IAEA;IACAE,aAAAC,IAAA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAG,MAAA,IAAAD,CAAA,MAAAA,CAAA;QACA,MAAAE,QAAA,GAAAJ,IAAA,CAAAE,CAAA;QACAD,MAAA,GAAAA,MAAA,SAAAI,QAAA,CAAAD,QAAA;MACA;MACA,OAAAH,MAAA;IACA;IAEA;IACAT,kBAAA;MACA,KAAArB,QAAA,CAAAmC,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IAEA;IACAf,eAAA;MACA,SAAAgB,KAAA,CAAApC,cAAA;QACA,KAAAoC,KAAA,CAAApC,cAAA,CAAAqC,SAAA,QAAAD,KAAA,CAAApC,cAAA,CAAAsC,YAAA;MACA;IACA;IAEA,MAAAtB,WAAA;MACA;QACA,MAAAuB,GAAA,SAAAnE,OAAA,CAAAoE,YAAA;QACA,IAAAD,GAAA,CAAAzD,IAAA,IAAAyD,GAAA,CAAAzD,IAAA,CAAA2D,IAAA;UACA,KAAAxD,MAAA,GAAAsD,GAAA,CAAAzD,IAAA,CAAAA,IAAA;UACA,KAAAI,QAAA,QAAAwD,eAAA,MAAAzD,MAAA;UACA,KAAAE,gBAAA,QAAAD,QAAA;QACA;UACA,KAAAD,MAAA;UACA,KAAAC,QAAA;UACA,KAAAC,gBAAA;QACA;MACA,SAAAwD,CAAA;QACA,KAAA1D,MAAA;QACA,KAAAC,QAAA;QACA,KAAAC,gBAAA;MACA;IACA;IACA;IACAuD,gBAAAE,IAAA;MACA,IAAAC,MAAA;MACA,WAAAC,IAAA,IAAAF,IAAA;QACA,IAAAE,IAAA,CAAAC,IAAA;UACAF,MAAA,CAAAb,IAAA,CAAAc,IAAA;QACA,WAAAA,IAAA,CAAAE,QAAA,IAAAF,IAAA,CAAAE,QAAA,CAAAnB,MAAA;UACAgB,MAAA,GAAAA,MAAA,CAAAI,MAAA,MAAAP,eAAA,CAAAI,IAAA,CAAAE,QAAA;QACA;MACA;MACA,OAAAH,MAAA;IACA;IACA;IACAK,gBAAA;MACA,MAAAC,OAAA,QAAA/D,aAAA,CAAAgE,IAAA,GAAAC,WAAA;MACA,KAAAF,OAAA;QACA,KAAAhE,gBAAA,QAAAD,QAAA;MACA;QACA,KAAAC,gBAAA,QAAAD,QAAA,CAAAoE,MAAA,CAAAC,EAAA,IAAAA,EAAA,CAAA3E,IAAA,IAAA2E,EAAA,CAAA3E,IAAA,CAAAyE,WAAA,GAAAG,QAAA,CAAAL,OAAA;MACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAM,eAAA;MACA,UAAApD,oBAAA,UAAAC,aAAA,CAAAuB,MAAA;QACA,KAAA6B,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAC,UAAA,QAAAtD,aAAA,CAAAuD,GAAA,CAAAC,KAAA;QACA;QACA;UACAC,EAAA,EAAAD,KAAA,CAAAC,EAAA;UACAC,UAAA,EAAAF,KAAA,CAAAE,UAAA;UACApF,IAAA,EAAAkF,KAAA,CAAAlF,IAAA;UACAqF,YAAA,EAAAH,KAAA,CAAAG,YAAA;UACAC,SAAA,EAAAJ,KAAA,CAAAI,SAAA;UACAC,IAAA,EAAAL,KAAA,CAAAK,IAAA;UACAC,YAAA,EAAAN,KAAA,CAAAM,YAAA;UACAC,cAAA,EAAAP,KAAA,CAAAO,cAAA;UACAC,cAAA,EAAAR,KAAA,CAAAQ,cAAA;QACA;MACA;;MAEA;MACA,KAAA7D,sBAAA;QACAsD,EAAA,OAAA1D,oBAAA,CAAA0D,EAAA;QACAnF,IAAA,OAAAyB,oBAAA,CAAAzB,IAAA;QACA2F,MAAA,EAAAX;MACA;;MAEA;MACA,KAAAhE,aAAA;;MAEA;MACA,KAAA8D,QAAA,CAAAc,OAAA,gBAAAnE,oBAAA,CAAAzB,IAAA;;MAEA;MACA,KAAA6F,SAAA;QACA,MAAAC,OAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,OAAA,EAAAA,OAAA,CAAAG,KAAA;MACA;IACA;IAEA,MAAAC,kBAAAC,OAAA;MACA;MACAC,OAAA,CAAAC,GAAA,8BAAAF,OAAA;;MAEA;MACA,KAAAA,OAAA,KAAAA,OAAA,CAAAhB,EAAA;QACAiB,OAAA,CAAAE,KAAA,cAAAH,OAAA;QACA;QACA;MACA;;MAEA;MACA,MAAAI,OAAA;QACA,mBAAA3E,QAAA;QACA;QACA;QACA;MACA;MAEA;QACAwE,OAAA,CAAAC,GAAA,kBAAAF,OAAA,CAAAhB,EAAA;QACA,MAAAxB,GAAA,SAAAnE,OAAA,CAAAgH,gBAAA,CAAAL,OAAA,CAAAhB,EAAA,SAAAoB,OAAA;QACAH,OAAA,CAAAC,GAAA,gBAAA1C,GAAA;QAEA,IAAAA,GAAA,CAAAzD,IAAA,IAAAyD,GAAA,CAAAzD,IAAA,CAAA2D,IAAA;UACA,MAAA4C,MAAA,GAAA9C,GAAA,CAAAzD,IAAA,CAAAA,IAAA;UACAkG,OAAA,CAAAC,GAAA,aAAAI,MAAA;UACA,KAAAhF,oBAAA,GAAAgF,MAAA;UACA;UACA,KAAA/E,aAAA,GAAA+E,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAvG,IAAA,IAAAuG,MAAA,CAAAvG,IAAA,CAAAyF,MAAA;UACA;UACA,KAAAhE,WAAA,GAAA8E,MAAA,CAAAvG,IAAA,IAAAuG,MAAA,CAAAvG,IAAA,CAAAA,IAAA;UACA;UACAkG,OAAA,CAAAC,GAAA,eAAA3E,aAAA,CAAAuB,MAAA;UACAmD,OAAA,CAAAC,GAAA,eAAA1E,WAAA,CAAAsB,MAAA;UAEA,SAAAvB,aAAA,CAAAuB,MAAA;YACAmD,OAAA,CAAAO,IAAA;UACA;UAEA,SAAAhF,WAAA,CAAAsB,MAAA;YACAmD,OAAA,CAAAO,IAAA;UACA;QACA;UACAP,OAAA,CAAAE,KAAA,aAAA3C,GAAA,CAAAzD,IAAA;UACA0G,KAAA,aAAAjD,GAAA,CAAAzD,IAAA,CAAA2G,GAAA;UACA,KAAApF,oBAAA;UACA,KAAAC,aAAA;UACA,KAAAC,WAAA;QACA;QACA,KAAAX,aAAA;MACA,SAAA+C,CAAA;QACAqC,OAAA,CAAAE,KAAA,eAAAvC,CAAA;QACA6C,KAAA,UAAA7C,CAAA,CAAA+C,OAAA;QACA,KAAArF,oBAAA;QACA,KAAAC,aAAA;QACA,KAAAC,WAAA;QACA,KAAAX,aAAA;MACA;IACA;IACA+F,gBAAA;MACA,KAAAnG,oBAAA,SAAAA,oBAAA;IACA;IACAoG,YAAAC,CAAA;MACA,KAAAvG,QAAA,GAAAuG,CAAA;MACA,KAAArG,oBAAA;IACA;IACAsG,eAAAC,KAAA;MACA,IAAAA,KAAA,CAAAC,SAAA;QACA;MACA;MACA;IACA;IAEA;IACA,MAAAC,eAAA;MACA,UAAA3G,QAAA,CAAA8D,IAAA,WAAArD,SAAA;QACA,KAAA2D,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA;MACA,IAAAuC,SAAA,QAAA5G,QAAA,CAAA8D,IAAA;;MAEA;MACA,SAAA3C,sBAAA;QACA;QACA,MAAA0F,WAAA;UACAC,SAAA,OAAA3F,sBAAA,CAAAsD,EAAA;UACAsC,WAAA,OAAA5F,sBAAA,CAAA7B,IAAA;UACA2F,MAAA,OAAA9D,sBAAA,CAAA8D;QACA;;QAEA;QACA2B,SAAA,GAAAI,IAAA,CAAAC,SAAA;UACAjH,QAAA,OAAAA,QAAA,CAAA8D,IAAA;UACA2B,OAAA,EAAAoB;QACA;MACA;;MAEA;MACA,MAAAK,OAAA;QACAvE,MAAA;QACAC,OAAA,OAAA5C,QAAA,CAAA8D,IAAA;QACAjB,QAAA;MACA;MACA,KAAAtC,QAAA,CAAAmC,IAAA,CAAAwE,OAAA;;MAEA;MACA,MAAAC,MAAA;QACAxE,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAAtC,QAAA,CAAAmC,IAAA,CAAAyE,MAAA;;MAEA;MACA,MAAAC,OAAA,QAAA7G,QAAA,MAAAA,QAAA,CAAAgC,MAAA;;MAEA;MACA,MAAAvC,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA;MACA,KAAAS,SAAA;MAEA;QACA;QACA,MAAA2F,OAAA,QAAAjF,sBAAA,GACAyF,SAAA,GACA,GAAA5G,QAAA;;QAEA;QACA,MAAAf,KAAA,CAAAoI,IAAA,CACA,4CACA;UAAA7G,QAAA,OAAAA,QAAA;UAAA4F;QAAA,GACA;UACAkB,YAAA;UACAC,kBAAA,EAAAlE,CAAA;YACA,MAAAmE,QAAA,GAAAnE,CAAA,CAAAoE,KAAA,CAAAC,MAAA,CAAAC,YAAA;YACA,IAAAC,OAAA,GAAAJ,QAAA,CAAAK,SAAA,CAAAT,OAAA,CAAAxE,OAAA,CAAAL,MAAA;YACA6E,OAAA,CAAAxE,OAAA,IAAAgF,OAAA;YACA,KAAA9F,cAAA;;YAEA;YACA,KAAAlB,eAAA,GAAA4G,QAAA;UACA;QACA,CACA;;QAEA;QACAJ,OAAA,CAAAvE,QAAA;QACA,KAAApC,SAAA;;QAEA;QACA,KAAAqH,gBAAA,CAAAV,OAAA;MACA,SAAAxB,KAAA;QACAF,OAAA,CAAAE,KAAA,UAAAA,KAAA;QACAwB,OAAA,CAAAxE,OAAA;QACAwE,OAAA,CAAAvE,QAAA;QACA,KAAApC,SAAA;MACA;IACA;IAEA;IACAqH,iBAAA1B,OAAA;MACAV,OAAA,CAAAC,GAAA,qBAAAS,OAAA,CAAAxD,OAAA;;MAEA;MACA,MAAAmF,gBAAA,GAAA3B,OAAA,CAAAxD,OAAA,CAAAoF,KAAA;MACA,IAAAD,gBAAA,IAAAA,gBAAA;QACA,MAAAE,WAAA,GAAAF,gBAAA;QACArC,OAAA,CAAAC,GAAA,cAAAsC,WAAA;;QAEA;QACA,KAAAC,kBAAA,CAAAD,WAAA,EAAA7B,OAAA;QACA;MACA;;MAEA;MACA,MAAA+B,gBAAA,GAAA/B,OAAA,CAAAxD,OAAA,CAAAoF,KAAA;MACA,IAAAG,gBAAA,IAAAA,gBAAA;QACAzC,OAAA,CAAAC,GAAA,eAAAwC,gBAAA;QAEA;UACA,IAAAC,WAAA,GAAApB,IAAA,CAAAqB,KAAA,CAAAF,gBAAA;UACAzC,OAAA,CAAAC,GAAA,gBAAAyC,WAAA;;UAEA;UACA,IAAAA,WAAA,CAAAjF,IAAA,UAAAiF,WAAA,CAAA5I,IAAA;YACAkG,OAAA,CAAAC,GAAA,yBAAAyC,WAAA,CAAA5I,IAAA;YACA4I,WAAA,GAAAA,WAAA,CAAA5I,IAAA;UACA;;UAEA;UACA,IAAA4I,WAAA,CAAAvD,IAAA,KAAAuD,WAAA,CAAAtB,SAAA,IAAAsB,WAAA,CAAAE,OAAA,IAAAF,WAAA,CAAA5I,IAAA;YACAkG,OAAA,CAAAC,GAAA,eAAAyC,WAAA;YACAhC,OAAA,CAAAgC,WAAA,GAAAA,WAAA;;YAEA;YACAhC,OAAA,CAAAxD,OAAA,GAAAwD,OAAA,CAAAxD,OAAA,CAAA2F,OAAA,6BACA;YAEA;UACA;YACA7C,OAAA,CAAAC,GAAA,gBAAAyC,WAAA;UACA;QACA,SAAAxC,KAAA;UACAF,OAAA,CAAAE,KAAA,cAAAA,KAAA;QACA;MACA;MAEAF,OAAA,CAAAC,GAAA;;MAEA;MACA;QACA;QACA,MAAA6C,SAAA;QACA,MAAAC,SAAA,GAAArC,OAAA,CAAAxD,OAAA,CAAAoF,KAAA,CAAAQ,SAAA;QAEA,IAAAC,SAAA;UACA/C,OAAA,CAAAC,GAAA,kBAAA8C,SAAA;;UAEA;UACA;UACA,MAAAC,OAAA,GAAAD,SAAA,IAAAF,OAAA;UACA7C,OAAA,CAAAC,GAAA,iBAAA+C,OAAA;UAEA;YACA,MAAAN,WAAA,GAAApB,IAAA,CAAAqB,KAAA,CAAAK,OAAA;YACAhD,OAAA,CAAAC,GAAA,gBAAAyC,WAAA;YAEA,IAAAA,WAAA,CAAAjF,IAAA,UAAAiF,WAAA,CAAA5I,IAAA;cACA,MAAAA,IAAA,GAAA4I,WAAA,CAAA5I,IAAA;cACAkG,OAAA,CAAAC,GAAA,aAAAnG,IAAA;;cAEA;cACA,IAAAA,IAAA,CAAAqF,IAAA,KAAArF,IAAA,CAAA8I,OAAA,IAAA9I,IAAA,CAAAA,IAAA;gBACAkG,OAAA,CAAAC,GAAA,eAAAnG,IAAA;gBACA4G,OAAA,CAAAgC,WAAA,GAAA5I,IAAA;gBACA4G,OAAA,CAAAxD,OAAA,GAAAwD,OAAA,CAAAxD,OAAA,CAAA2F,OAAA,CAAAE,SAAA,KACA;gBACA;cACA;YACA;UACA,SAAAE,UAAA;YACAjD,OAAA,CAAAC,GAAA,cAAAgD,UAAA;UACA;QACA;MACA,SAAA/C,KAAA;QACAF,OAAA,CAAAC,GAAA,aAAAC,KAAA;MACA;;MAEA;MACA,IAAAQ,OAAA,CAAAxD,OAAA,CAAAsB,QAAA,iBACAkC,OAAA,CAAAxD,OAAA,CAAAsB,QAAA;QACAwB,OAAA,CAAAC,GAAA;;QAEA;QACA,MAAAiD,aAAA;UACAnE,EAAA,EAAAoE,IAAA,CAAAC,GAAA,GAAAC,QAAA;UACAlE,IAAA;UACAmE,KAAA;UACAV,OAAA,OAAAvI,aAAA,EAAA0E,EAAA;QACA;QAEAiB,OAAA,CAAAC,GAAA,eAAAiD,aAAA;QACAxC,OAAA,CAAAgC,WAAA,GAAAQ,aAAA;MACA;IACA;IAEA;IACA,MAAAV,mBAAAD,WAAA,EAAA7B,OAAA;MACA;QACAV,OAAA,CAAAC,GAAA,qBAAAsC,WAAA;QACA,MAAAgB,QAAA,SAAAhK,KAAA,CAAAiK,GAAA,yCAAAjB,WAAA;QAEA,IAAAgB,QAAA,CAAAzJ,IAAA,IAAAyJ,QAAA,CAAAE,MAAA;UACAzD,OAAA,CAAAC,GAAA,cAAAsD,QAAA,CAAAzJ,IAAA;;UAEA;UACA,IAAA4I,WAAA,GAAAa,QAAA,CAAAzJ,IAAA;UACA,IAAA4I,WAAA,CAAAjF,IAAA,UAAAiF,WAAA,CAAA5I,IAAA;YACA4I,WAAA,GAAAA,WAAA,CAAA5I,IAAA;UACA;;UAEA;UACA4G,OAAA,CAAAgC,WAAA,GAAAA,WAAA;;UAEA;UACA,KAAAhC,OAAA,CAAAxD,OAAA,CAAAsB,QAAA;YACAkC,OAAA,CAAAxD,OAAA;UACA;;UAEA;UACA,KAAAwG,YAAA;QACA;UACA1D,OAAA,CAAAE,KAAA,cAAAqD,QAAA;UACA7C,OAAA,CAAAxD,OAAA;QACA;MACA,SAAAgD,KAAA;QACAF,OAAA,CAAAE,KAAA,cAAAA,KAAA;QACAQ,OAAA,CAAAxD,OAAA,0CAAAgD,KAAA,CAAAQ,OAAA;MACA;IACA;IAEA;IACAiD,UAAA;MACA;MACA,MAAAC,eAAA;QACA7E,EAAA;QACAI,IAAA;QACAmE,KAAA;QACAlC,SAAA;QACAwB,OAAA;MACA;;MAEA;MACA,MAAAnB,MAAA;QACAxE,MAAA;QACAC,OAAA;QACAC,QAAA;QACAuF,WAAA,EAAAkB;MACA;MAEA,KAAA/I,QAAA,CAAAmC,IAAA,CAAAyE,MAAA;;MAEA;MACA,KAAAvG,eAAA,GAAAoG,IAAA,CAAAC,SAAA,CAAAqC,eAAA;;MAEA;MACA5D,OAAA,CAAAC,GAAA,iBAAApF,QAAA;;MAEA;MACA,MAAAgJ,eAAA;QACApG,IAAA;QACAgD,GAAA;QACA3G,IAAA;UACAqF,IAAA;UACArF,IAAA,GACA;YAAAgF,KAAA;YAAAgF,KAAA;UAAA,GACA;YAAAhF,KAAA;YAAAgF,KAAA;UAAA,GACA;YAAAhF,KAAA;YAAAgF,KAAA;UAAA,EACA;UACAC,OAAA;YAAAnK,IAAA;UAAA;QACA;MACA;;MAEA;MACA,MAAAoK,WAAA,iBAAA1C,IAAA,CAAAC,SAAA,CAAAsC,eAAA;MACA,MAAAI,cAAA;QACAhH,MAAA;QACAC,OAAA,gBAAA8G,WAAA;QACA7G,QAAA;MACA;MAEA,KAAAtC,QAAA,CAAAmC,IAAA,CAAAiH,cAAA;MACA,KAAA7B,gBAAA,CAAA6B,cAAA;;MAEA;MACA,KAAA/I,eAAA,GAAA8I,WAAA;MAEAhE,OAAA,CAAAC,GAAA,iBAAAgE,cAAA;IACA;IAEA;IACAC,aAAA;MACAlE,OAAA,CAAAC,GAAA;;MAEA;MACA,MAAAkE,QAAA;QACA1G,IAAA;QACAgD,GAAA;QACA3G,IAAA;UACAiF,EAAA;UACAuE,KAAA;UACAV,OAAA;UACAzD,IAAA;UACArF,IAAA;YACAA,IAAA,GACA;cAAAgK,KAAA;cAAAhF,KAAA;cAAAlF,IAAA;cAAAwK,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAhF,KAAA;cAAAlF,IAAA;cAAAwK,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAhF,KAAA;cAAAlF,IAAA;cAAAwK,QAAA;YAAA,EACA;YACA7E,MAAA,GACA;cAAAR,EAAA;cAAAnF,IAAA;cAAAsF,SAAA;YAAA,GACA;cAAAH,EAAA;cAAAnF,IAAA;cAAAsF,SAAA;YAAA;UAEA;QACA;MACA;;MAEA;MACA,MAAAmF,WAAA;QACApH,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MAEA;MACAkH,WAAA,CAAA3B,WAAA,GAAAyB,QAAA,CAAArK,IAAA;;MAEA;MACA,KAAAe,QAAA,CAAAmC,IAAA,CAAAqH,WAAA;;MAEA;MACA,KAAAnJ,eAAA,GAAAoG,IAAA,CAAAC,SAAA,CAAA4C,QAAA;MAEAnE,OAAA,CAAAC,GAAA,gBAAAoE,WAAA;IACA;IAEA;IACAC,gBAAA;MACA,KAAArJ,oBAAA;MACA,KAAAC,eAAA,QAAAL,QAAA,MAAAA,QAAA,CAAAgC,MAAA,MAAAK,OAAA;IACA;IAEA;IACA,MAAAqH,YAAA7D,OAAA;MACA,KAAAA,OAAA,CAAAgC,WAAA;MAEA;QACA;QACA,KAAA8B,IAAA,CAAA9D,OAAA;;QAEA;QACA,MAAA+D,aAAA,GAAA9E,QAAA,CAAA+E,aAAA;QACAD,aAAA,CAAAE,KAAA,CAAAC,QAAA;QACAH,aAAA,CAAAE,KAAA,CAAAE,IAAA;QACAJ,aAAA,CAAAE,KAAA,CAAAG,KAAA;QACAL,aAAA,CAAAE,KAAA,CAAAI,UAAA;QACAN,aAAA,CAAAE,KAAA,CAAAK,OAAA;QACArF,QAAA,CAAAsF,IAAA,CAAAC,WAAA,CAAAT,aAAA;;QAEA;QACA;QACA,MAAAnB,KAAA,GAAA5C,OAAA,CAAAgC,WAAA,CAAAY,KAAA;QACA,MAAAjC,WAAA,QAAAhH,aAAA,QAAAA,aAAA,CAAA8K,SAAA;QACA,MAAAC,WAAA,OAAAjC,IAAA,GAAAkC,cAAA;;QAEA;QACA,IAAAtL,WAAA,GAAA2G,OAAA,CAAAxD,OAAA,CACA2F,OAAA,6CACAA,OAAA,8CACAzE,IAAA;;QAEA;QACA,IAAArE,WAAA,CAAAyE,QAAA;UACA,MAAA8G,OAAA,GAAA3F,QAAA,CAAA+E,aAAA;UACAY,OAAA,CAAAC,SAAA,GAAAxL,WAAA;UACAA,WAAA,GAAAuL,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;QACA;;QAEA;QACAhB,aAAA,CAAAc,SAAA;AACA;AACA,2DAAAjC,KAAA;AACA,8DAAAjC,WAAA;AACA,+DAAA+D,WAAA;AACA;;AAEA;AACA;AACA,mBAAArL,WAAA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;QAEA;QACA,MAAA2L,cAAA,GAAAjB,aAAA,CAAA7E,aAAA;QACA,MAAA+F,KAAA,GAAAhM,OAAA,CAAAiM,IAAA,CAAAF,cAAA;;QAEA;QACA,MAAAhD,WAAA,GAAAhC,OAAA,CAAAgC,WAAA;QACA,IAAAmD,OAAA;;QAEA;QACA,QAAAnD,WAAA,CAAAvD,IAAA;UACA;YACA0G,OAAA,QAAAC,kBAAA,CAAApD,WAAA;YACA;UACA;YACAmD,OAAA,QAAAE,mBAAA,CAAArD,WAAA;YACA;UACA;YACAmD,OAAA,QAAAG,kBAAA,CAAAtD,WAAA;YACA;UACA;YACAmD,OAAA,QAAAI,uBAAA,CAAAvD,WAAA;YACA;UACA;YACAmD,OAAA,QAAAK,iBAAA,CAAAxD,WAAA;QACA;;QAEA;QACAiD,KAAA,CAAAQ,SAAA,CAAAN,OAAA;QACA7F,OAAA,CAAAC,GAAA,mBAAA4F,OAAA;;QAEA;QACA,UAAAO,OAAA,CAAAC,OAAA,IAAAC,UAAA,CAAAD,OAAA;;QAEA;QACA,MAAAE,kBAAA,GAAA9B,aAAA,CAAA7E,aAAA;QACA,KAAA4G,eAAA,CAAAD,kBAAA,EAAA7F,OAAA,CAAAgC,WAAA;;QAEA;QACA,MAAA+D,MAAA,SAAAhN,WAAA,CAAAgL,aAAA;UACAiC,KAAA;UAAA;UACAC,OAAA;UACAC,UAAA;UACAC,eAAA;QACA;;QAEA;QACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;QACA,MAAAC,GAAA,OAAAtN,KAAA;UACAuN,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,MAAAC,QAAA;QACA,MAAAC,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAA3B,KAAA;;QAEA;QACAkC,GAAA,CAAAO,QAAA,CAAAT,OAAA,iBAAAM,QAAA,EAAAC,SAAA;;QAEA;QACAL,GAAA,CAAAQ,IAAA,IAAAlE,KAAA,QAAAH,IAAA,GAAAsE,OAAA;;QAEA;QACA9H,QAAA,CAAAsF,IAAA,CAAAyC,WAAA,CAAAjD,aAAA;QACAkB,KAAA,CAAAgC,OAAA;;QAEA;QACA,KAAAnD,IAAA,CAAA9D,OAAA;;QAEA;QACA,KAAAhC,QAAA,CAAAc,OAAA;MAEA,SAAAU,KAAA;QACAF,OAAA,CAAAE,KAAA,aAAAA,KAAA;QACA,KAAAsE,IAAA,CAAA9D,OAAA;QACA,KAAAhC,QAAA,CAAAwB,KAAA,eAAAA,KAAA,CAAAQ,OAAA;MACA;IACA;IAEA;IACA,MAAAkH,sBAAA;MACA;QACA;QACA,KAAAhM,YAAA;;QAEA;QACA,IAAAiM,aAAA;;QAEA;QACA,SAAAjL,CAAA,MAAAA,CAAA,QAAA/B,QAAA,CAAAgC,MAAA,EAAAD,CAAA;UACA,MAAA8D,OAAA,QAAA7F,QAAA,CAAA+B,CAAA;UACA,KAAA8D,OAAA,CAAAzD,MAAA,IAAAyD,OAAA,CAAAgC,WAAA;YACAmF,aAAA,CAAA7K,IAAA;cACA0D,OAAA,EAAAA,OAAA;cACAoH,KAAA,EAAAlL;YACA;UACA;QACA;;QAEA;QACA,IAAAiL,aAAA,CAAAhL,MAAA;UACA,KAAA6B,QAAA,CAAAC,OAAA;UACA,KAAA/C,YAAA;UACA;QACA;;QAEA;QACA,MAAAoL,GAAA,OAAAtN,KAAA;UACAuN,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,SAAAvK,CAAA,MAAAA,CAAA,GAAAiL,aAAA,CAAAhL,MAAA,EAAAD,CAAA;UACA;UACA,IAAAA,CAAA;YACAoK,GAAA,CAAAe,OAAA;UACA;UAEA,MAAAC,YAAA,GAAAH,aAAA,CAAAjL,CAAA,EAAA8D,OAAA;UACA,MAAAgC,WAAA,GAAAsF,YAAA,CAAAtF,WAAA;;UAEA;UACA,MAAA+B,aAAA,GAAA9E,QAAA,CAAA+E,aAAA;UACAD,aAAA,CAAAE,KAAA,CAAAC,QAAA;UACAH,aAAA,CAAAE,KAAA,CAAAE,IAAA;UACAJ,aAAA,CAAAE,KAAA,CAAAG,KAAA;UACAL,aAAA,CAAAE,KAAA,CAAAI,UAAA;UACAN,aAAA,CAAAE,KAAA,CAAAK,OAAA;UACArF,QAAA,CAAAsF,IAAA,CAAAC,WAAA,CAAAT,aAAA;;UAEA;UACA,MAAAnB,KAAA,GAAAZ,WAAA,CAAAY,KAAA;UACA,MAAA8B,WAAA,OAAAjC,IAAA,GAAAkC,cAAA;UACA,MAAAhE,WAAA,QAAAhH,aAAA,QAAAA,aAAA,CAAA8K,SAAA;UAEA,IAAA8C,WAAA;AACA;AACA,mGAAA3E,KAAA;AACA;AACA,8DAAAjC,WAAA;AACA,+DAAA+D,WAAA;AACA;;AAEA;AACA;AACA;;UAEA;UACA,IAAArL,WAAA,GAAAiO,YAAA,CAAA9K,OAAA;;UAEA;UACA,IAAAnD,WAAA,CAAAyE,QAAA;YACA,MAAA8G,OAAA,GAAA3F,QAAA,CAAA+E,aAAA;YACAY,OAAA,CAAAC,SAAA,GAAAxL,WAAA;YACAA,WAAA,GAAAuL,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;UACA;UAEAwC,WAAA;AACA,wEAAAlO,WAAA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;UAEAkO,WAAA;;UAEA;UACAxD,aAAA,CAAAc,SAAA,GAAA0C,WAAA;;UAEA;UACA,MAAAvC,cAAA,GAAAjB,aAAA,CAAA7E,aAAA;UACA,MAAA2G,kBAAA,GAAA9B,aAAA,CAAA7E,aAAA;UAEA,IAAA8F,cAAA,IAAAa,kBAAA;YACA;YACA,MAAA2B,aAAA,GAAAvO,OAAA,CAAAiM,IAAA,CAAAF,cAAA;YACA,IAAAG,OAAA;;YAEA;YACA,QAAAnD,WAAA,CAAAvD,IAAA;cACA;gBACA0G,OAAA,QAAAC,kBAAA,CAAApD,WAAA;gBACA;cACA;gBACAmD,OAAA,QAAAE,mBAAA,CAAArD,WAAA;gBACA;cACA;gBACAmD,OAAA,QAAAG,kBAAA,CAAAtD,WAAA;gBACA;cACA;gBACAmD,OAAA,QAAAI,uBAAA,CAAAvD,WAAA;gBACA;cACA;gBACAmD,OAAA,QAAAK,iBAAA,CAAAxD,WAAA;YACA;YAEAwF,aAAA,CAAA/B,SAAA,CAAAN,OAAA;;YAEA;YACA,KAAAW,eAAA,CAAAD,kBAAA,EAAA7D,WAAA;;YAEA;YACA,UAAA0D,OAAA,CAAAC,OAAA,IAAAC,UAAA,CAAAD,OAAA;;YAEA;YACA,MAAAI,MAAA,SAAAhN,WAAA,CAAAgL,aAAA;cACAiC,KAAA;cACAC,OAAA;cACAC,UAAA;cACAC,eAAA;YACA;;YAEA;YACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;;YAEA;YACA,MAAAoB,SAAA,GAAAnB,GAAA,CAAAoB,QAAA,CAAAC,QAAA,CAAAC,QAAA;YACA,MAAAC,UAAA,GAAAvB,GAAA,CAAAoB,QAAA,CAAAC,QAAA,CAAAG,SAAA;YACA,MAAApB,QAAA,GAAAe,SAAA;YACA,MAAAd,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAA3B,KAAA;;YAEA;YACA,IAAA2D,cAAA,GAAApB,SAAA;YACA,IAAAqB,aAAA,GAAAtB,QAAA;YAEA,IAAAC,SAAA,GAAAkB,UAAA;cAAA;cACAE,cAAA,GAAAF,UAAA;cACAG,aAAA,GAAAjC,MAAA,CAAA3B,KAAA,GAAA2D,cAAA,GAAAhC,MAAA,CAAAa,MAAA;YACA;;YAEA;YACA,MAAAqB,IAAA,IAAAR,SAAA,GAAAO,aAAA;YACA,MAAAE,IAAA;;YAEA5B,GAAA,CAAAO,QAAA,CAAAT,OAAA,SAAA6B,IAAA,EAAAC,IAAA,EAAAF,aAAA,EAAAD,cAAA;;YAEA;YACAP,aAAA,CAAAP,OAAA;YACAhI,QAAA,CAAAsF,IAAA,CAAAyC,WAAA,CAAAjD,aAAA;UACA;QACA;;QAEA;QACA,MAAAoE,QAAA,iBAAA1F,IAAA,GAAA2F,WAAA,GAAAC,KAAA;QACA/B,GAAA,CAAAQ,IAAA,CAAAqB,QAAA;;QAEA;QACA,KAAAjN,YAAA;QACA,KAAA8C,QAAA,CAAAc,OAAA;MAEA,SAAAU,KAAA;QACAF,OAAA,CAAAE,KAAA,YAAAA,KAAA;QACA,KAAAtE,YAAA;QACA,KAAA8C,QAAA,CAAAwB,KAAA,cAAAA,KAAA,CAAAQ,OAAA;MACA;IACA;IAEA;IACAsI,wBAAAtG,WAAA;MACA,MAAAvD,IAAA,GAAAuD,WAAA,CAAAvD,IAAA;MACA,IAAArF,IAAA;MACA,IAAAmP,UAAA;;MAEA;MACA,IAAAvG,WAAA,CAAA5I,IAAA,IAAA4I,WAAA,CAAA5I,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA4I,WAAA,CAAA5I,IAAA,CAAAA,IAAA;QACAmP,UAAA,GAAAnP,IAAA,CAAA+E,GAAA,CAAAqK,IAAA,IAAAA,IAAA,CAAApK,KAAA,IAAAoK,IAAA,CAAAtP,IAAA;MACA;;MAEA;MACA,MAAAiM,OAAA;QACAvC,KAAA;UACA6F,IAAA,EAAAzG,WAAA,CAAAY,KAAA;QACA;QACA8F,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAnK,IAAA;UACArF,IAAA,EAAAmP;QACA;QACAM,KAAA;UACApK,IAAA;QACA;QACAqK,MAAA;MACA;;MAEA;MACA,IAAA1P,IAAA,CAAA+C,MAAA;QACA,IAAA/C,IAAA,IAAA0P,MAAA;UACA;UACA,MAAAC,SAAA;;UAEA;UACA3P,IAAA,CAAA4P,OAAA,CAAAR,IAAA;YACA,IAAAA,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACA,KAAAF,SAAA,CAAAE,CAAA,CAAAvF,QAAA;kBACAqF,SAAA,CAAAE,CAAA,CAAAvF,QAAA;oBACAxK,IAAA,EAAA+P,CAAA,CAAAvF,QAAA;oBACAjF,IAAA,EAAAA,IAAA;oBACArF,IAAA,EAAA8P,KAAA,CAAAX,UAAA,CAAApM,MAAA,EAAAgN,IAAA;kBACA;gBACA;cACA;YACA;UACA;;UAEA;UACA/P,IAAA,CAAA4P,OAAA,EAAAR,IAAA,EAAApB,KAAA;YACA,IAAAoB,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACAF,SAAA,CAAAE,CAAA,CAAAvF,QAAA,EAAAtK,IAAA,CAAAgO,KAAA,IAAA6B,CAAA,CAAA7F,KAAA;cACA;YACA;UACA;;UAEA;UACA+B,OAAA,CAAA2D,MAAA,GAAAM,MAAA,CAAAC,MAAA,CAAAN,SAAA;QACA;UACA;UACA5D,OAAA,CAAA2D,MAAA,CAAAxM,IAAA;YACApD,IAAA;YACAuF,IAAA,EAAAA,IAAA;YACArF,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAqK,IAAA,IAAAA,IAAA,CAAApF,KAAA;UACA;QACA;MACA;MAEA,OAAA+B,OAAA;IACA;IAEA;IACA;IACAC,mBAAAkE,SAAA;MACAhK,OAAA,CAAAC,GAAA,gBAAA+J,SAAA;;MAEA;MACA,IAAAlQ,IAAA;MACA,IAAAiK,OAAA;;MAEA;MACA,IAAAiG,SAAA,CAAAlQ,IAAA,IAAA8P,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAlQ,IAAA;QACAA,IAAA,GAAAkQ,SAAA,CAAAlQ,IAAA;QACAiK,OAAA,GAAAiG,SAAA,CAAAjG,OAAA;MACA;MACA;MAAA,KACA,IAAAiG,SAAA,CAAAlQ,IAAA,IAAAkQ,SAAA,CAAAlQ,IAAA,CAAAA,IAAA,IAAA8P,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAlQ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAkQ,SAAA,CAAAlQ,IAAA,CAAAA,IAAA;QACAiK,OAAA,GAAAiG,SAAA,CAAAlQ,IAAA,CAAAyF,MAAA,GACAyK,SAAA,CAAAlQ,IAAA,CAAAyF,MAAA,CAAAjB,MAAA,CAAA4L,CAAA,IAAAA,CAAA,CAAAhL,SAAA;MACA;MAEAc,OAAA,CAAAC,GAAA,YAAAnG,IAAA;MACAkG,OAAA,CAAAC,GAAA,QAAA8D,OAAA;;MAEA;MACA,MAAAoG,SAAA,GAAArQ,IAAA,CAAA+E,GAAA,CAAAqK,IAAA,IAAAA,IAAA,CAAApK,KAAA,IAAAoK,IAAA,CAAAtP,IAAA;;MAEA;MACA,MAAA4P,MAAA;MACA,IAAA1P,IAAA,CAAA+C,MAAA,QAAA/C,IAAA,IAAA0P,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACAvQ,IAAA,CAAA4P,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAAvF,QAAA;UACA;QACA;QAEA,MAAA6E,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAtF,QAAA;UACA,MAAAoG,UAAA,GAAA1Q,IAAA,CAAA+E,GAAA,CAAAqK,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAAvF,QAAA,KAAAA,QAAA;YACA,OAAAoF,MAAA,GAAAA,MAAA,CAAA1F,KAAA;UACA;UAEA0F,MAAA,CAAAxM,IAAA;YACApD,IAAA,EAAAwK,QAAA;YACAjF,IAAA;YACArF,IAAA,EAAA0Q;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAAxM,IAAA;UACApD,IAAA,EAAAmK,OAAA,KAAAnK,IAAA;UACAuF,IAAA;UACArF,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAqK,IAAA,IAAAA,IAAA,CAAApF,KAAA;QACA;MACA;MAEA;QACAR,KAAA;UACA6F,IAAA,EAAAa,SAAA,CAAA1G,KAAA;QACA;QACA8F,OAAA;UACAC,OAAA;UACAqB,WAAA;YACAvL,IAAA;UACA;QACA;QACAwL,MAAA;UACA7Q,IAAA,EAAA0P,MAAA,CAAA3K,GAAA,CAAA8K,CAAA,IAAAA,CAAA,CAAA/P,IAAA;QACA;QACA0P,KAAA;UACAnK,IAAA;UACArF,IAAA,EAAAqQ;QACA;QACAZ,KAAA;UACApK,IAAA;QACA;QACAqK,MAAA,EAAAA;MACA;IACA;IAEA;IACAzD,oBAAAiE,SAAA;MACA,IAAAlQ,IAAA;MACA,IAAAiK,OAAA;;MAEA;MACA,IAAAiG,SAAA,CAAAlQ,IAAA,IAAA8P,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAlQ,IAAA;QACAA,IAAA,GAAAkQ,SAAA,CAAAlQ,IAAA;QACAiK,OAAA,GAAAiG,SAAA,CAAAjG,OAAA;MACA;MACA;MAAA,KACA,IAAAiG,SAAA,CAAAlQ,IAAA,IAAAkQ,SAAA,CAAAlQ,IAAA,CAAAA,IAAA,IAAA8P,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAlQ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAkQ,SAAA,CAAAlQ,IAAA,CAAAA,IAAA;QACAiK,OAAA,GAAAiG,SAAA,CAAAlQ,IAAA,CAAAyF,MAAA,GACAyK,SAAA,CAAAlQ,IAAA,CAAAyF,MAAA,CAAAjB,MAAA,CAAA4L,CAAA,IAAAA,CAAA,CAAAhL,SAAA;MACA;;MAEA;MACA,MAAAiL,SAAA,GAAArQ,IAAA,CAAA+E,GAAA,CAAAqK,IAAA,IAAAA,IAAA,CAAApK,KAAA,IAAAoK,IAAA,CAAAtP,IAAA;;MAEA;MACA,MAAA4P,MAAA;MACA,IAAA1P,IAAA,CAAA+C,MAAA,QAAA/C,IAAA,IAAA0P,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACAvQ,IAAA,CAAA4P,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAAvF,QAAA;UACA;QACA;QAEA,MAAA6E,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAtF,QAAA;UACA,MAAAoG,UAAA,GAAA1Q,IAAA,CAAA+E,GAAA,CAAAqK,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAAvF,QAAA,KAAAA,QAAA;YACA,OAAAoF,MAAA,GAAAA,MAAA,CAAA1F,KAAA;UACA;UAEA0F,MAAA,CAAAxM,IAAA;YACApD,IAAA,EAAAwK,QAAA;YACAjF,IAAA;YACArF,IAAA,EAAA0Q;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAAxM,IAAA;UACApD,IAAA,EAAAmK,OAAA,KAAAnK,IAAA;UACAuF,IAAA;UACArF,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAqK,IAAA,IAAAA,IAAA,CAAApF,KAAA;QACA;MACA;MAEA;QACAR,KAAA;UACA6F,IAAA,EAAAa,SAAA,CAAA1G,KAAA;QACA;QACA8F,OAAA;UACAC,OAAA;QACA;QACAsB,MAAA;UACA7Q,IAAA,EAAA0P,MAAA,CAAA3K,GAAA,CAAA8K,CAAA,IAAAA,CAAA,CAAA/P,IAAA;QACA;QACA0P,KAAA;UACAnK,IAAA;UACArF,IAAA,EAAAqQ;QACA;QACAZ,KAAA;UACApK,IAAA;QACA;QACAqK,MAAA,EAAAA;MACA;IACA;IAEA;IACAxD,mBAAAgE,SAAA;MACA,IAAAlQ,IAAA;;MAEA;MACA,IAAAkQ,SAAA,CAAAlQ,IAAA,IAAA8P,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAlQ,IAAA;QACAA,IAAA,GAAAkQ,SAAA,CAAAlQ,IAAA;MACA;MACA;MAAA,KACA,IAAAkQ,SAAA,CAAAlQ,IAAA,IAAAkQ,SAAA,CAAAlQ,IAAA,CAAAA,IAAA,IAAA8P,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAlQ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAkQ,SAAA,CAAAlQ,IAAA,CAAAA,IAAA;MACA;MAEA,MAAA0Q,UAAA,GAAA1Q,IAAA,CAAA+E,GAAA,CAAAqK,IAAA;QACAtP,IAAA,EAAAsP,IAAA,CAAApK,KAAA,IAAAoK,IAAA,CAAAtP,IAAA;QACAkK,KAAA,EAAAoF,IAAA,CAAApF;MACA;MAEA;QACAR,KAAA;UACA6F,IAAA,EAAAa,SAAA,CAAA1G,KAAA;QACA;QACA8F,OAAA;UACAC,OAAA;UACAuB,SAAA;QACA;QACAD,MAAA;UACAE,MAAA;UACAC,KAAA;UACAC,GAAA;UACAjR,IAAA,EAAA0Q,UAAA,CAAA3L,GAAA,CAAAqK,IAAA,IAAAA,IAAA,CAAAtP,IAAA;QACA;QACA4P,MAAA;UACA5P,IAAA;UACAuF,IAAA;UACA6L,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAvG,QAAA;UACA;UACAwG,QAAA;YACAF,KAAA;cACAC,IAAA;cACAE,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAJ,IAAA;UACA;UACArR,IAAA,EAAA0Q;QACA;MACA;IACA;IAEA;IACAvE,wBAAA+D,SAAA;MACA;MACA,IAAAlQ,IAAA;MACA,IAAAiK,OAAA;;MAEA;MACA,IAAAiG,SAAA,CAAAlQ,IAAA,IAAA8P,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAlQ,IAAA;QACAA,IAAA,GAAAkQ,SAAA,CAAAlQ,IAAA;QACAiK,OAAA,GAAAiG,SAAA,CAAAjG,OAAA;MACA;MACA;MAAA,KACA,IAAAiG,SAAA,CAAAlQ,IAAA,IAAAkQ,SAAA,CAAAlQ,IAAA,CAAAA,IAAA,IAAA8P,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAlQ,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAkQ,SAAA,CAAAlQ,IAAA,CAAAA,IAAA;QACAiK,OAAA,GAAAiG,SAAA,CAAAlQ,IAAA,CAAAyF,MAAA,GACAyK,SAAA,CAAAlQ,IAAA,CAAAyF,MAAA,CAAAjB,MAAA,CAAA4L,CAAA,IAAAA,CAAA,CAAAhL,SAAA;MACA;;MAEA;MACA,MAAAsM,SAAA,GAAA1R,IAAA,CAAA+E,GAAA,CAAAqK,IAAA,IAAAA,IAAA,CAAApK,KAAA,IAAAoK,IAAA,CAAAtP,IAAA;;MAEA;MACA,MAAA4P,MAAA;MACA,IAAA1P,IAAA,CAAA+C,MAAA,QAAA/C,IAAA,IAAA0P,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACAvQ,IAAA,CAAA4P,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAAvF,QAAA;UACA;QACA;QAEA,MAAA6E,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAtF,QAAA;UACA,MAAAoG,UAAA,GAAA1Q,IAAA,CAAA+E,GAAA,CAAAqK,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAAvF,QAAA,KAAAA,QAAA;YACA,OAAAoF,MAAA,GAAAA,MAAA,CAAA1F,KAAA;UACA;UAEA0F,MAAA,CAAAxM,IAAA;YACApD,IAAA,EAAAwK,QAAA;YACAjF,IAAA;YAAA;YACArF,IAAA,EAAA0Q;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAAxM,IAAA;UACApD,IAAA,EAAAmK,OAAA,KAAAnK,IAAA;UACAuF,IAAA;UAAA;UACArF,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAqK,IAAA,IAAAA,IAAA,CAAApF,KAAA;QACA;MACA;MAEA;QACAR,KAAA;UACA6F,IAAA,EAAAa,SAAA,CAAA1G,KAAA;QACA;QACA8F,OAAA;UACAC,OAAA;UACAqB,WAAA;YACAvL,IAAA;UACA;QACA;QACAwL,MAAA;UACA7Q,IAAA,EAAA0P,MAAA,CAAA3K,GAAA,CAAA8K,CAAA,IAAAA,CAAA,CAAA/P,IAAA;QACA;QACA;QACA0P,KAAA;UACAnK,IAAA;QACA;QACAoK,KAAA;UACApK,IAAA;UAAA;UACArF,IAAA,EAAA0R;QACA;QACAhC,MAAA,EAAAA;MACA;IACA;IAEA;IACAtD,kBAAA8D,SAAA;MACA;QACA1G,KAAA;UACA6F,IAAA,EAAAa,SAAA,CAAA1G,KAAA;QACA;QACA8F,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAnK,IAAA;UACArF,IAAA;QACA;QACAyP,KAAA;UACApK,IAAA;QACA;QACAqK,MAAA;UACArK,IAAA,EAAA6K,SAAA,CAAA7K,IAAA;UACArF,IAAA;QACA;MACA;IACA;IAEA;IACA0M,gBAAAiF,SAAA,EAAA/I,WAAA;MACA;MACA,IAAA5I,IAAA;MACA,IAAAqG,OAAA;;MAEA;MACA,IAAAuC,WAAA,CAAA5I,IAAA,IAAA4I,WAAA,CAAA5I,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA4I,WAAA,CAAA5I,IAAA,CAAAA,IAAA;;QAEA;QACA,IAAAA,IAAA,CAAA+C,MAAA;UACA,IAAA/C,IAAA,IAAA0P,MAAA;YACA;YACArJ,OAAA;YACA,MAAAuL,SAAA,GAAA5R,IAAA;YACA,IAAA4R,SAAA,CAAAlC,MAAA,IAAAI,KAAA,CAAAK,OAAA,CAAAyB,SAAA,CAAAlC,MAAA;cACAkC,SAAA,CAAAlC,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACAxJ,OAAA,CAAAnD,IAAA,CAAA2M,CAAA,CAAAvF,QAAA;cACA;YACA;UACA;YACA;YACAjE,OAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAwL,SAAA;;MAEA;MACAA,SAAA;MACAxL,OAAA,CAAAuJ,OAAA,CAAAkC,MAAA;QACAD,SAAA,sGAAAC,MAAA;MACA;MACAD,SAAA;;MAEA;MACAA,SAAA;MACA7R,IAAA,CAAA4P,OAAA,CAAAR,IAAA;QACAyC,SAAA;;QAEA;QACAA,SAAA,yDAAAzC,IAAA,CAAApK,KAAA,IAAAoK,IAAA,CAAAtP,IAAA;;QAEA;QACA,IAAAsP,IAAA,CAAAM,MAAA;UACA;UACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;YACAgC,SAAA,yDAAAhC,CAAA,CAAA7F,KAAA,KAAA+H,SAAA,GAAAlC,CAAA,CAAA7F,KAAA;UACA;QACA;UACA;UACA6H,SAAA,yDAAAzC,IAAA,CAAApF,KAAA,KAAA+H,SAAA,GAAA3C,IAAA,CAAApF,KAAA;QACA;QAEA6H,SAAA;MACA;MACAA,SAAA;;MAEA;MACAF,SAAA,CAAAlG,SAAA,GAAAoG,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}