{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\n/**\n * 图表类型兼容性判断工具\n * 根据数据结构判断支持哪些图表类型\n */\n\n// 支持的图表类型\nexport const CHART_TYPES = {\n  BAR: 'bar',\n  LINE: 'line',\n  PIE: 'pie',\n  BAR_HORIZONTAL: 'bar-horizontal'\n};\n\n// 图表类型配置\nexport const CHART_TYPE_CONFIG = {\n  [CHART_TYPES.BAR]: {\n    name: '柱状图',\n    icon: 'el-icon-s-data',\n    description: '适用于分类数据对比'\n  },\n  [CHART_TYPES.LINE]: {\n    name: '折线图',\n    icon: 'el-icon-connection',\n    description: '适用于趋势分析'\n  },\n  [CHART_TYPES.PIE]: {\n    name: '饼图',\n    icon: 'el-icon-pie-chart',\n    description: '适用于占比分析'\n  },\n  [CHART_TYPES.BAR_HORIZONTAL]: {\n    name: '水平柱状图',\n    icon: 'el-icon-s-marketing',\n    description: '适用于分类名称较长的数据对比'\n  }\n};\n\n/**\n * 判断数据字段类型\n * @param {Array} data - 数据数组\n * @param {string} field - 字段名\n * @returns {string} 'dimension'(维度) | 'measure'(度量) | 'time'(时间)\n */\nfunction getFieldType(data, field) {\n  if (!data || !data.length || !field) return 'unknown';\n  const values = data.map(item => item[field]).filter(v => v != null);\n  if (values.length === 0) return 'unknown';\n\n  // 检查是否为数值类型（度量）\n  const numericValues = values.filter(v => typeof v === 'number' || !isNaN(Number(v)));\n  if (numericValues.length / values.length > 0.8) {\n    return 'measure';\n  }\n\n  // 检查是否为时间类型\n  const timeValues = values.filter(v => {\n    const date = new Date(v);\n    return !isNaN(date.getTime()) && (/^\\d{4}-\\d{2}-\\d{2}/.test(v) || /^\\d{4}\\/\\d{2}\\/\\d{2}/.test(v) || /^\\d{4}-\\d{2}/.test(v) || /^\\d{4}年/.test(v) || /第\\d+季度/.test(v));\n  });\n  if (timeValues.length / values.length > 0.5) {\n    return 'time';\n  }\n\n  // 默认为维度\n  return 'dimension';\n}\n\n/**\n * 分析数据结构\n * @param {Array} data - 图表数据\n * @returns {Object} 数据结构分析结果\n */\nexport function analyzeDataStructure(data) {\n  if (!data || !Array.isArray(data) || data.length === 0) {\n    return {\n      dimensions: [],\n      measures: [],\n      timeFields: [],\n      totalFields: 0,\n      categoryCount: 0\n    };\n  }\n  const firstRow = data[0];\n  const fields = Object.keys(firstRow);\n  const dimensions = [];\n  const measures = [];\n  const timeFields = [];\n  fields.forEach(field => {\n    const type = getFieldType(data, field);\n    switch (type) {\n      case 'dimension':\n        dimensions.push(field);\n        break;\n      case 'measure':\n        measures.push(field);\n        break;\n      case 'time':\n        timeFields.push(field);\n        break;\n    }\n  });\n\n  // 计算分类数量（基于第一个维度字段）\n  let categoryCount = 0;\n  if (dimensions.length > 0) {\n    const uniqueValues = new Set(data.map(item => item[dimensions[0]]));\n    categoryCount = uniqueValues.size;\n  }\n  return {\n    dimensions,\n    measures,\n    timeFields,\n    totalFields: fields.length,\n    categoryCount\n  };\n}\n\n/**\n * 判断柱状图兼容性\n * 需要：至少1个维度字段 + 至少1个度量字段\n */\nfunction isBarChartCompatible(structure) {\n  return structure.dimensions.length >= 1 && structure.measures.length >= 1;\n}\n\n/**\n * 判断折线图兼容性  \n * 需要：至少1个时间/序列维度 + 至少1个度量字段\n */\nfunction isLineChartCompatible(structure) {\n  const hasTimeOrSequence = structure.timeFields.length >= 1 || structure.dimensions.length >= 1 && structure.categoryCount >= 3;\n  return hasTimeOrSequence && structure.measures.length >= 1;\n}\n\n/**\n * 判断饼图兼容性\n * 需要：1个维度字段 + 1个度量字段，且分类不宜过多（≤10个）\n */\nfunction isPieChartCompatible(structure) {\n  return structure.dimensions.length >= 1 && structure.measures.length >= 1 && structure.categoryCount <= 10 && structure.categoryCount >= 2;\n}\n\n/**\n * 判断水平柱状图兼容性\n * 需要：至少1个维度字段 + 至少1个度量字段\n */\nfunction isHorizontalBarChartCompatible(structure) {\n  return structure.dimensions.length >= 1 && structure.measures.length >= 1;\n}\n\n/**\n * 获取数据支持的图表类型\n * @param {Array} data - 图表数据\n * @returns {Object} 各图表类型的兼容性结果\n */\nexport function getCompatibleChartTypes(data) {\n  const structure = analyzeDataStructure(data);\n  return {\n    [CHART_TYPES.BAR]: {\n      compatible: isBarChartCompatible(structure),\n      reason: isBarChartCompatible(structure) ? '' : '需要至少1个维度字段和1个度量字段'\n    },\n    [CHART_TYPES.LINE]: {\n      compatible: isLineChartCompatible(structure),\n      reason: isLineChartCompatible(structure) ? '' : '需要时间序列数据或足够的数据点用于趋势分析'\n    },\n    [CHART_TYPES.PIE]: {\n      compatible: isPieChartCompatible(structure),\n      reason: isPieChartCompatible(structure) ? '' : structure.categoryCount > 10 ? '分类过多，不适合饼图显示' : structure.categoryCount < 2 ? '分类过少，无法形成有效的占比分析' : '需要1个维度字段和1个度量字段'\n    },\n    [CHART_TYPES.BAR_HORIZONTAL]: {\n      compatible: isHorizontalBarChartCompatible(structure),\n      reason: isHorizontalBarChartCompatible(structure) ? '' : '需要至少1个维度字段和1个度量字段'\n    }\n  };\n}\n\n/**\n * 获取推荐的默认图表类型\n * @param {Array} data - 图表数据\n * @returns {string} 推荐的图表类型\n */\nexport function getRecommendedChartType(data) {\n  const compatibility = getCompatibleChartTypes(data);\n  const structure = analyzeDataStructure(data);\n\n  // 优先级：时间序列数据推荐折线图 > 分类较少推荐饼图 > 默认柱状图\n  if (compatibility[CHART_TYPES.LINE].compatible && structure.timeFields.length > 0) {\n    return CHART_TYPES.LINE;\n  }\n  if (compatibility[CHART_TYPES.PIE].compatible && structure.categoryCount <= 6) {\n    return CHART_TYPES.PIE;\n  }\n  if (compatibility[CHART_TYPES.BAR].compatible) {\n    return CHART_TYPES.BAR;\n  }\n  if (compatibility[CHART_TYPES.BAR_HORIZONTAL].compatible) {\n    return CHART_TYPES.BAR_HORIZONTAL;\n  }\n\n  // 默认返回柱状图\n  return CHART_TYPES.BAR;\n}", "map": {"version": 3, "names": ["CHART_TYPES", "BAR", "LINE", "PIE", "BAR_HORIZONTAL", "CHART_TYPE_CONFIG", "name", "icon", "description", "getFieldType", "data", "field", "length", "values", "map", "item", "filter", "v", "numericValues", "isNaN", "Number", "timeValues", "date", "Date", "getTime", "test", "analyzeDataStructure", "Array", "isArray", "dimensions", "measures", "timeFields", "totalFields", "categoryCount", "firstRow", "fields", "Object", "keys", "for<PERSON>ach", "type", "push", "uniqueValues", "Set", "size", "isBarChartCompatible", "structure", "isLineChartCompatible", "hasTimeOrSequence", "isPieChartCompatible", "isHorizontalBarChartCompatible", "getCompatibleChartTypes", "compatible", "reason", "getRecommendedChartType", "compatibility"], "sources": ["E:/frontCodeCode/datafront/src/utils/chartCompatibility.js"], "sourcesContent": ["/**\n * 图表类型兼容性判断工具\n * 根据数据结构判断支持哪些图表类型\n */\n\n// 支持的图表类型\nexport const CHART_TYPES = {\n  BAR: 'bar',\n  LINE: 'line', \n  PIE: 'pie',\n  BAR_HORIZONTAL: 'bar-horizontal'\n};\n\n// 图表类型配置\nexport const CHART_TYPE_CONFIG = {\n  [CHART_TYPES.BAR]: {\n    name: '柱状图',\n    icon: 'el-icon-s-data',\n    description: '适用于分类数据对比'\n  },\n  [CHART_TYPES.LINE]: {\n    name: '折线图', \n    icon: 'el-icon-connection',\n    description: '适用于趋势分析'\n  },\n  [CHART_TYPES.PIE]: {\n    name: '饼图',\n    icon: 'el-icon-pie-chart', \n    description: '适用于占比分析'\n  },\n  [CHART_TYPES.BAR_HORIZONTAL]: {\n    name: '水平柱状图',\n    icon: 'el-icon-s-marketing',\n    description: '适用于分类名称较长的数据对比'\n  }\n};\n\n/**\n * 判断数据字段类型\n * @param {Array} data - 数据数组\n * @param {string} field - 字段名\n * @returns {string} 'dimension'(维度) | 'measure'(度量) | 'time'(时间)\n */\nfunction getFieldType(data, field) {\n  if (!data || !data.length || !field) return 'unknown';\n  \n  const values = data.map(item => item[field]).filter(v => v != null);\n  if (values.length === 0) return 'unknown';\n  \n  // 检查是否为数值类型（度量）\n  const numericValues = values.filter(v => typeof v === 'number' || !isNaN(Number(v)));\n  if (numericValues.length / values.length > 0.8) {\n    return 'measure';\n  }\n  \n  // 检查是否为时间类型\n  const timeValues = values.filter(v => {\n    const date = new Date(v);\n    return !isNaN(date.getTime()) && (\n      /^\\d{4}-\\d{2}-\\d{2}/.test(v) || \n      /^\\d{4}\\/\\d{2}\\/\\d{2}/.test(v) ||\n      /^\\d{4}-\\d{2}/.test(v) ||\n      /^\\d{4}年/.test(v) ||\n      /第\\d+季度/.test(v)\n    );\n  });\n  \n  if (timeValues.length / values.length > 0.5) {\n    return 'time';\n  }\n  \n  // 默认为维度\n  return 'dimension';\n}\n\n/**\n * 分析数据结构\n * @param {Array} data - 图表数据\n * @returns {Object} 数据结构分析结果\n */\nexport function analyzeDataStructure(data) {\n  if (!data || !Array.isArray(data) || data.length === 0) {\n    return {\n      dimensions: [],\n      measures: [],\n      timeFields: [],\n      totalFields: 0,\n      categoryCount: 0\n    };\n  }\n  \n  const firstRow = data[0];\n  const fields = Object.keys(firstRow);\n  \n  const dimensions = [];\n  const measures = [];\n  const timeFields = [];\n  \n  fields.forEach(field => {\n    const type = getFieldType(data, field);\n    switch (type) {\n      case 'dimension':\n        dimensions.push(field);\n        break;\n      case 'measure':\n        measures.push(field);\n        break;\n      case 'time':\n        timeFields.push(field);\n        break;\n    }\n  });\n  \n  // 计算分类数量（基于第一个维度字段）\n  let categoryCount = 0;\n  if (dimensions.length > 0) {\n    const uniqueValues = new Set(data.map(item => item[dimensions[0]]));\n    categoryCount = uniqueValues.size;\n  }\n  \n  return {\n    dimensions,\n    measures, \n    timeFields,\n    totalFields: fields.length,\n    categoryCount\n  };\n}\n\n/**\n * 判断柱状图兼容性\n * 需要：至少1个维度字段 + 至少1个度量字段\n */\nfunction isBarChartCompatible(structure) {\n  return structure.dimensions.length >= 1 && structure.measures.length >= 1;\n}\n\n/**\n * 判断折线图兼容性  \n * 需要：至少1个时间/序列维度 + 至少1个度量字段\n */\nfunction isLineChartCompatible(structure) {\n  const hasTimeOrSequence = structure.timeFields.length >= 1 || \n    (structure.dimensions.length >= 1 && structure.categoryCount >= 3);\n  return hasTimeOrSequence && structure.measures.length >= 1;\n}\n\n/**\n * 判断饼图兼容性\n * 需要：1个维度字段 + 1个度量字段，且分类不宜过多（≤10个）\n */\nfunction isPieChartCompatible(structure) {\n  return structure.dimensions.length >= 1 && \n         structure.measures.length >= 1 && \n         structure.categoryCount <= 10 &&\n         structure.categoryCount >= 2;\n}\n\n/**\n * 判断水平柱状图兼容性\n * 需要：至少1个维度字段 + 至少1个度量字段\n */\nfunction isHorizontalBarChartCompatible(structure) {\n  return structure.dimensions.length >= 1 && structure.measures.length >= 1;\n}\n\n/**\n * 获取数据支持的图表类型\n * @param {Array} data - 图表数据\n * @returns {Object} 各图表类型的兼容性结果\n */\nexport function getCompatibleChartTypes(data) {\n  const structure = analyzeDataStructure(data);\n  \n  return {\n    [CHART_TYPES.BAR]: {\n      compatible: isBarChartCompatible(structure),\n      reason: isBarChartCompatible(structure) ? '' : '需要至少1个维度字段和1个度量字段'\n    },\n    [CHART_TYPES.LINE]: {\n      compatible: isLineChartCompatible(structure),\n      reason: isLineChartCompatible(structure) ? '' : '需要时间序列数据或足够的数据点用于趋势分析'\n    },\n    [CHART_TYPES.PIE]: {\n      compatible: isPieChartCompatible(structure),\n      reason: isPieChartCompatible(structure) ? '' : \n        structure.categoryCount > 10 ? '分类过多，不适合饼图显示' : \n        structure.categoryCount < 2 ? '分类过少，无法形成有效的占比分析' :\n        '需要1个维度字段和1个度量字段'\n    },\n    [CHART_TYPES.BAR_HORIZONTAL]: {\n      compatible: isHorizontalBarChartCompatible(structure),\n      reason: isHorizontalBarChartCompatible(structure) ? '' : '需要至少1个维度字段和1个度量字段'\n    }\n  };\n}\n\n/**\n * 获取推荐的默认图表类型\n * @param {Array} data - 图表数据\n * @returns {string} 推荐的图表类型\n */\nexport function getRecommendedChartType(data) {\n  const compatibility = getCompatibleChartTypes(data);\n  const structure = analyzeDataStructure(data);\n  \n  // 优先级：时间序列数据推荐折线图 > 分类较少推荐饼图 > 默认柱状图\n  if (compatibility[CHART_TYPES.LINE].compatible && structure.timeFields.length > 0) {\n    return CHART_TYPES.LINE;\n  }\n  \n  if (compatibility[CHART_TYPES.PIE].compatible && structure.categoryCount <= 6) {\n    return CHART_TYPES.PIE;\n  }\n  \n  if (compatibility[CHART_TYPES.BAR].compatible) {\n    return CHART_TYPES.BAR;\n  }\n  \n  if (compatibility[CHART_TYPES.BAR_HORIZONTAL].compatible) {\n    return CHART_TYPES.BAR_HORIZONTAL;\n  }\n  \n  // 默认返回柱状图\n  return CHART_TYPES.BAR;\n}\n"], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,WAAW,GAAG;EACzBC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,KAAK;EACVC,cAAc,EAAE;AAClB,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAG;EAC/B,CAACL,WAAW,CAACC,GAAG,GAAG;IACjBK,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EAAE;EACf,CAAC;EACD,CAACR,WAAW,CAACE,IAAI,GAAG;IAClBI,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,oBAAoB;IAC1BC,WAAW,EAAE;EACf,CAAC;EACD,CAACR,WAAW,CAACG,GAAG,GAAG;IACjBG,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EAAE;EACf,CAAC;EACD,CAACR,WAAW,CAACI,cAAc,GAAG;IAC5BE,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EAAE;EACf;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACjC,IAAI,CAACD,IAAI,IAAI,CAACA,IAAI,CAACE,MAAM,IAAI,CAACD,KAAK,EAAE,OAAO,SAAS;EAErD,MAAME,MAAM,GAAGH,IAAI,CAACI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACJ,KAAK,CAAC,CAAC,CAACK,MAAM,CAACC,CAAC,IAAIA,CAAC,IAAI,IAAI,CAAC;EACnE,IAAIJ,MAAM,CAACD,MAAM,KAAK,CAAC,EAAE,OAAO,SAAS;;EAEzC;EACA,MAAMM,aAAa,GAAGL,MAAM,CAACG,MAAM,CAACC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAI,CAACE,KAAK,CAACC,MAAM,CAACH,CAAC,CAAC,CAAC,CAAC;EACpF,IAAIC,aAAa,CAACN,MAAM,GAAGC,MAAM,CAACD,MAAM,GAAG,GAAG,EAAE;IAC9C,OAAO,SAAS;EAClB;;EAEA;EACA,MAAMS,UAAU,GAAGR,MAAM,CAACG,MAAM,CAACC,CAAC,IAAI;IACpC,MAAMK,IAAI,GAAG,IAAIC,IAAI,CAACN,CAAC,CAAC;IACxB,OAAO,CAACE,KAAK,CAACG,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,KAC3B,oBAAoB,CAACC,IAAI,CAACR,CAAC,CAAC,IAC5B,sBAAsB,CAACQ,IAAI,CAACR,CAAC,CAAC,IAC9B,cAAc,CAACQ,IAAI,CAACR,CAAC,CAAC,IACtB,SAAS,CAACQ,IAAI,CAACR,CAAC,CAAC,IACjB,QAAQ,CAACQ,IAAI,CAACR,CAAC,CAAC,CACjB;EACH,CAAC,CAAC;EAEF,IAAII,UAAU,CAACT,MAAM,GAAGC,MAAM,CAACD,MAAM,GAAG,GAAG,EAAE;IAC3C,OAAO,MAAM;EACf;;EAEA;EACA,OAAO,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASc,oBAAoBA,CAAChB,IAAI,EAAE;EACzC,IAAI,CAACA,IAAI,IAAI,CAACiB,KAAK,CAACC,OAAO,CAAClB,IAAI,CAAC,IAAIA,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;IACtD,OAAO;MACLiB,UAAU,EAAE,EAAE;MACdC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE;IACjB,CAAC;EACH;EAEA,MAAMC,QAAQ,GAAGxB,IAAI,CAAC,CAAC,CAAC;EACxB,MAAMyB,MAAM,GAAGC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC;EAEpC,MAAML,UAAU,GAAG,EAAE;EACrB,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,UAAU,GAAG,EAAE;EAErBI,MAAM,CAACG,OAAO,CAAC3B,KAAK,IAAI;IACtB,MAAM4B,IAAI,GAAG9B,YAAY,CAACC,IAAI,EAAEC,KAAK,CAAC;IACtC,QAAQ4B,IAAI;MACV,KAAK,WAAW;QACdV,UAAU,CAACW,IAAI,CAAC7B,KAAK,CAAC;QACtB;MACF,KAAK,SAAS;QACZmB,QAAQ,CAACU,IAAI,CAAC7B,KAAK,CAAC;QACpB;MACF,KAAK,MAAM;QACToB,UAAU,CAACS,IAAI,CAAC7B,KAAK,CAAC;QACtB;IACJ;EACF,CAAC,CAAC;;EAEF;EACA,IAAIsB,aAAa,GAAG,CAAC;EACrB,IAAIJ,UAAU,CAACjB,MAAM,GAAG,CAAC,EAAE;IACzB,MAAM6B,YAAY,GAAG,IAAIC,GAAG,CAAChC,IAAI,CAACI,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACc,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnEI,aAAa,GAAGQ,YAAY,CAACE,IAAI;EACnC;EAEA,OAAO;IACLd,UAAU;IACVC,QAAQ;IACRC,UAAU;IACVC,WAAW,EAAEG,MAAM,CAACvB,MAAM;IAC1BqB;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASW,oBAAoBA,CAACC,SAAS,EAAE;EACvC,OAAOA,SAAS,CAAChB,UAAU,CAACjB,MAAM,IAAI,CAAC,IAAIiC,SAAS,CAACf,QAAQ,CAAClB,MAAM,IAAI,CAAC;AAC3E;;AAEA;AACA;AACA;AACA;AACA,SAASkC,qBAAqBA,CAACD,SAAS,EAAE;EACxC,MAAME,iBAAiB,GAAGF,SAAS,CAACd,UAAU,CAACnB,MAAM,IAAI,CAAC,IACvDiC,SAAS,CAAChB,UAAU,CAACjB,MAAM,IAAI,CAAC,IAAIiC,SAAS,CAACZ,aAAa,IAAI,CAAE;EACpE,OAAOc,iBAAiB,IAAIF,SAAS,CAACf,QAAQ,CAAClB,MAAM,IAAI,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA,SAASoC,oBAAoBA,CAACH,SAAS,EAAE;EACvC,OAAOA,SAAS,CAAChB,UAAU,CAACjB,MAAM,IAAI,CAAC,IAChCiC,SAAS,CAACf,QAAQ,CAAClB,MAAM,IAAI,CAAC,IAC9BiC,SAAS,CAACZ,aAAa,IAAI,EAAE,IAC7BY,SAAS,CAACZ,aAAa,IAAI,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACA,SAASgB,8BAA8BA,CAACJ,SAAS,EAAE;EACjD,OAAOA,SAAS,CAAChB,UAAU,CAACjB,MAAM,IAAI,CAAC,IAAIiC,SAAS,CAACf,QAAQ,CAAClB,MAAM,IAAI,CAAC;AAC3E;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASsC,uBAAuBA,CAACxC,IAAI,EAAE;EAC5C,MAAMmC,SAAS,GAAGnB,oBAAoB,CAAChB,IAAI,CAAC;EAE5C,OAAO;IACL,CAACV,WAAW,CAACC,GAAG,GAAG;MACjBkD,UAAU,EAAEP,oBAAoB,CAACC,SAAS,CAAC;MAC3CO,MAAM,EAAER,oBAAoB,CAACC,SAAS,CAAC,GAAG,EAAE,GAAG;IACjD,CAAC;IACD,CAAC7C,WAAW,CAACE,IAAI,GAAG;MAClBiD,UAAU,EAAEL,qBAAqB,CAACD,SAAS,CAAC;MAC5CO,MAAM,EAAEN,qBAAqB,CAACD,SAAS,CAAC,GAAG,EAAE,GAAG;IAClD,CAAC;IACD,CAAC7C,WAAW,CAACG,GAAG,GAAG;MACjBgD,UAAU,EAAEH,oBAAoB,CAACH,SAAS,CAAC;MAC3CO,MAAM,EAAEJ,oBAAoB,CAACH,SAAS,CAAC,GAAG,EAAE,GAC1CA,SAAS,CAACZ,aAAa,GAAG,EAAE,GAAG,cAAc,GAC7CY,SAAS,CAACZ,aAAa,GAAG,CAAC,GAAG,kBAAkB,GAChD;IACJ,CAAC;IACD,CAACjC,WAAW,CAACI,cAAc,GAAG;MAC5B+C,UAAU,EAAEF,8BAA8B,CAACJ,SAAS,CAAC;MACrDO,MAAM,EAAEH,8BAA8B,CAACJ,SAAS,CAAC,GAAG,EAAE,GAAG;IAC3D;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,uBAAuBA,CAAC3C,IAAI,EAAE;EAC5C,MAAM4C,aAAa,GAAGJ,uBAAuB,CAACxC,IAAI,CAAC;EACnD,MAAMmC,SAAS,GAAGnB,oBAAoB,CAAChB,IAAI,CAAC;;EAE5C;EACA,IAAI4C,aAAa,CAACtD,WAAW,CAACE,IAAI,CAAC,CAACiD,UAAU,IAAIN,SAAS,CAACd,UAAU,CAACnB,MAAM,GAAG,CAAC,EAAE;IACjF,OAAOZ,WAAW,CAACE,IAAI;EACzB;EAEA,IAAIoD,aAAa,CAACtD,WAAW,CAACG,GAAG,CAAC,CAACgD,UAAU,IAAIN,SAAS,CAACZ,aAAa,IAAI,CAAC,EAAE;IAC7E,OAAOjC,WAAW,CAACG,GAAG;EACxB;EAEA,IAAImD,aAAa,CAACtD,WAAW,CAACC,GAAG,CAAC,CAACkD,UAAU,EAAE;IAC7C,OAAOnD,WAAW,CAACC,GAAG;EACxB;EAEA,IAAIqD,aAAa,CAACtD,WAAW,CAACI,cAAc,CAAC,CAAC+C,UAAU,EAAE;IACxD,OAAOnD,WAAW,CAACI,cAAc;EACnC;;EAEA;EACA,OAAOJ,WAAW,CAACC,GAAG;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}