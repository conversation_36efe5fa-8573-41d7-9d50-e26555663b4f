{"ast": null, "code": "/*!\n* ZRender, a high performance 2d drawing library.\n*\n* Copyright (c) 2013, Baidu Inc.\n* All rights reserved.\n*\n* LICENSE\n* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt\n*/\nimport env from './core/env.js';\nimport * as zrUtil from './core/util.js';\nimport Handler from './Handler.js';\nimport Storage from './Storage.js';\nimport Animation, { getTime } from './animation/Animation.js';\nimport HandlerProxy from './dom/HandlerProxy.js';\nimport { lum } from './tool/color.js';\nimport { DARK_MODE_THRESHOLD } from './config.js';\nimport Group from './graphic/Group.js';\nvar painterCtors = {};\nvar instances = {};\nfunction delInstance(id) {\n  delete instances[id];\n}\nfunction isDarkMode(backgroundColor) {\n  if (!backgroundColor) {\n    return false;\n  }\n  if (typeof backgroundColor === 'string') {\n    return lum(backgroundColor, 1) < DARK_MODE_THRESHOLD;\n  } else if (backgroundColor.colorStops) {\n    var colorStops = backgroundColor.colorStops;\n    var totalLum = 0;\n    var len = colorStops.length;\n    for (var i = 0; i < len; i++) {\n      totalLum += lum(colorStops[i].color, 1);\n    }\n    totalLum /= len;\n    return totalLum < DARK_MODE_THRESHOLD;\n  }\n  return false;\n}\nvar ZRender = function () {\n  function ZRender(id, dom, opts) {\n    var _this = this;\n    this._sleepAfterStill = 10;\n    this._stillFrameAccum = 0;\n    this._needsRefresh = true;\n    this._needsRefreshHover = true;\n    this._darkMode = false;\n    opts = opts || {};\n    this.dom = dom;\n    this.id = id;\n    var storage = new Storage();\n    var rendererType = opts.renderer || 'canvas';\n    if (!painterCtors[rendererType]) {\n      rendererType = zrUtil.keys(painterCtors)[0];\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (!painterCtors[rendererType]) {\n        throw new Error(\"Renderer '\" + rendererType + \"' is not imported. Please import it first.\");\n      }\n    }\n    opts.useDirtyRect = opts.useDirtyRect == null ? false : opts.useDirtyRect;\n    var painter = new painterCtors[rendererType](dom, storage, opts, id);\n    var ssrMode = opts.ssr || painter.ssrOnly;\n    this.storage = storage;\n    this.painter = painter;\n    var handlerProxy = !env.node && !env.worker && !ssrMode ? new HandlerProxy(painter.getViewportRoot(), painter.root) : null;\n    var useCoarsePointer = opts.useCoarsePointer;\n    var usePointerSize = useCoarsePointer == null || useCoarsePointer === 'auto' ? env.touchEventsSupported : !!useCoarsePointer;\n    var defaultPointerSize = 44;\n    var pointerSize;\n    if (usePointerSize) {\n      pointerSize = zrUtil.retrieve2(opts.pointerSize, defaultPointerSize);\n    }\n    this.handler = new Handler(storage, painter, handlerProxy, painter.root, pointerSize);\n    this.animation = new Animation({\n      stage: {\n        update: ssrMode ? null : function () {\n          return _this._flush(true);\n        }\n      }\n    });\n    if (!ssrMode) {\n      this.animation.start();\n    }\n  }\n  ZRender.prototype.add = function (el) {\n    if (this._disposed || !el) {\n      return;\n    }\n    this.storage.addRoot(el);\n    el.addSelfToZr(this);\n    this.refresh();\n  };\n  ZRender.prototype.remove = function (el) {\n    if (this._disposed || !el) {\n      return;\n    }\n    this.storage.delRoot(el);\n    el.removeSelfFromZr(this);\n    this.refresh();\n  };\n  ZRender.prototype.configLayer = function (zLevel, config) {\n    if (this._disposed) {\n      return;\n    }\n    if (this.painter.configLayer) {\n      this.painter.configLayer(zLevel, config);\n    }\n    this.refresh();\n  };\n  ZRender.prototype.setBackgroundColor = function (backgroundColor) {\n    if (this._disposed) {\n      return;\n    }\n    if (this.painter.setBackgroundColor) {\n      this.painter.setBackgroundColor(backgroundColor);\n    }\n    this.refresh();\n    this._backgroundColor = backgroundColor;\n    this._darkMode = isDarkMode(backgroundColor);\n  };\n  ZRender.prototype.getBackgroundColor = function () {\n    return this._backgroundColor;\n  };\n  ZRender.prototype.setDarkMode = function (darkMode) {\n    this._darkMode = darkMode;\n  };\n  ZRender.prototype.isDarkMode = function () {\n    return this._darkMode;\n  };\n  ZRender.prototype.refreshImmediately = function (fromInside) {\n    if (this._disposed) {\n      return;\n    }\n    if (!fromInside) {\n      this.animation.update(true);\n    }\n    this._needsRefresh = false;\n    this.painter.refresh();\n    this._needsRefresh = false;\n  };\n  ZRender.prototype.refresh = function () {\n    if (this._disposed) {\n      return;\n    }\n    this._needsRefresh = true;\n    this.animation.start();\n  };\n  ZRender.prototype.flush = function () {\n    if (this._disposed) {\n      return;\n    }\n    this._flush(false);\n  };\n  ZRender.prototype._flush = function (fromInside) {\n    var triggerRendered;\n    var start = getTime();\n    if (this._needsRefresh) {\n      triggerRendered = true;\n      this.refreshImmediately(fromInside);\n    }\n    if (this._needsRefreshHover) {\n      triggerRendered = true;\n      this.refreshHoverImmediately();\n    }\n    var end = getTime();\n    if (triggerRendered) {\n      this._stillFrameAccum = 0;\n      this.trigger('rendered', {\n        elapsedTime: end - start\n      });\n    } else if (this._sleepAfterStill > 0) {\n      this._stillFrameAccum++;\n      if (this._stillFrameAccum > this._sleepAfterStill) {\n        this.animation.stop();\n      }\n    }\n  };\n  ZRender.prototype.setSleepAfterStill = function (stillFramesCount) {\n    this._sleepAfterStill = stillFramesCount;\n  };\n  ZRender.prototype.wakeUp = function () {\n    if (this._disposed) {\n      return;\n    }\n    this.animation.start();\n    this._stillFrameAccum = 0;\n  };\n  ZRender.prototype.refreshHover = function () {\n    this._needsRefreshHover = true;\n  };\n  ZRender.prototype.refreshHoverImmediately = function () {\n    if (this._disposed) {\n      return;\n    }\n    this._needsRefreshHover = false;\n    if (this.painter.refreshHover && this.painter.getType() === 'canvas') {\n      this.painter.refreshHover();\n    }\n  };\n  ZRender.prototype.resize = function (opts) {\n    if (this._disposed) {\n      return;\n    }\n    opts = opts || {};\n    this.painter.resize(opts.width, opts.height);\n    this.handler.resize();\n  };\n  ZRender.prototype.clearAnimation = function () {\n    if (this._disposed) {\n      return;\n    }\n    this.animation.clear();\n  };\n  ZRender.prototype.getWidth = function () {\n    if (this._disposed) {\n      return;\n    }\n    return this.painter.getWidth();\n  };\n  ZRender.prototype.getHeight = function () {\n    if (this._disposed) {\n      return;\n    }\n    return this.painter.getHeight();\n  };\n  ZRender.prototype.setCursorStyle = function (cursorStyle) {\n    if (this._disposed) {\n      return;\n    }\n    this.handler.setCursorStyle(cursorStyle);\n  };\n  ZRender.prototype.findHover = function (x, y) {\n    if (this._disposed) {\n      return;\n    }\n    return this.handler.findHover(x, y);\n  };\n  ZRender.prototype.on = function (eventName, eventHandler, context) {\n    if (!this._disposed) {\n      this.handler.on(eventName, eventHandler, context);\n    }\n    return this;\n  };\n  ZRender.prototype.off = function (eventName, eventHandler) {\n    if (this._disposed) {\n      return;\n    }\n    this.handler.off(eventName, eventHandler);\n  };\n  ZRender.prototype.trigger = function (eventName, event) {\n    if (this._disposed) {\n      return;\n    }\n    this.handler.trigger(eventName, event);\n  };\n  ZRender.prototype.clear = function () {\n    if (this._disposed) {\n      return;\n    }\n    var roots = this.storage.getRoots();\n    for (var i = 0; i < roots.length; i++) {\n      if (roots[i] instanceof Group) {\n        roots[i].removeSelfFromZr(this);\n      }\n    }\n    this.storage.delAllRoots();\n    this.painter.clear();\n  };\n  ZRender.prototype.dispose = function () {\n    if (this._disposed) {\n      return;\n    }\n    this.animation.stop();\n    this.clear();\n    this.storage.dispose();\n    this.painter.dispose();\n    this.handler.dispose();\n    this.animation = this.storage = this.painter = this.handler = null;\n    this._disposed = true;\n    delInstance(this.id);\n  };\n  return ZRender;\n}();\nexport function init(dom, opts) {\n  var zr = new ZRender(zrUtil.guid(), dom, opts);\n  instances[zr.id] = zr;\n  return zr;\n}\nexport function dispose(zr) {\n  zr.dispose();\n}\nexport function disposeAll() {\n  for (var key in instances) {\n    if (instances.hasOwnProperty(key)) {\n      instances[key].dispose();\n    }\n  }\n  instances = {};\n}\nexport function getInstance(id) {\n  return instances[id];\n}\nexport function registerPainter(name, Ctor) {\n  painterCtors[name] = Ctor;\n}\nvar ssrDataGetter;\nexport function getElementSSRData(el) {\n  if (typeof ssrDataGetter === 'function') {\n    return ssrDataGetter(el);\n  }\n}\nexport function registerSSRDataGetter(getter) {\n  ssrDataGetter = getter;\n}\nexport var version = '5.6.1';\n;", "map": {"version": 3, "names": ["env", "zrUtil", "Handler", "Storage", "Animation", "getTime", "HandlerProxy", "lum", "DARK_MODE_THRESHOLD", "Group", "painter<PERSON>tors", "instances", "delInstance", "id", "isDarkMode", "backgroundColor", "colorStops", "totalLum", "len", "length", "i", "color", "ZRender", "dom", "opts", "_this", "_sleepAfterStill", "_stillFrameAccum", "_needsRefresh", "_needsRefreshHover", "_darkMode", "storage", "rendererType", "renderer", "keys", "process", "NODE_ENV", "Error", "useDirtyRect", "painter", "ssrMode", "ssr", "ssrOnly", "handlerProxy", "node", "worker", "getViewportRoot", "root", "useCoarsePointer", "usePointerSize", "touchEventsSupported", "defaultPointerSize", "pointerSize", "retrieve2", "handler", "animation", "stage", "update", "_flush", "start", "prototype", "add", "el", "_disposed", "addRoot", "addSelfToZr", "refresh", "remove", "delRoot", "removeSelfFromZr", "config<PERSON><PERSON>er", "zLevel", "config", "setBackgroundColor", "_backgroundColor", "getBackgroundColor", "setDarkMode", "darkMode", "refreshImmediately", "fromInside", "flush", "triggerRendered", "refreshHoverImmediately", "end", "trigger", "elapsedTime", "stop", "setSleepAfterStill", "stillFramesCount", "wakeUp", "refreshHover", "getType", "resize", "width", "height", "clearAnimation", "clear", "getWidth", "getHeight", "setCursorStyle", "cursorStyle", "findHover", "x", "y", "on", "eventName", "<PERSON><PERSON><PERSON><PERSON>", "context", "off", "event", "roots", "getRoots", "delAllRoots", "dispose", "init", "zr", "guid", "disposeAll", "key", "hasOwnProperty", "getInstance", "registerPainter", "name", "Ctor", "ssrDataGetter", "getElementSSRData", "registerSSRDataGetter", "getter", "version"], "sources": ["D:/FastBI/datafront/node_modules/zrender/lib/zrender.js"], "sourcesContent": ["/*!\n* ZRender, a high performance 2d drawing library.\n*\n* Copyright (c) 2013, Baidu Inc.\n* All rights reserved.\n*\n* LICENSE\n* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt\n*/\nimport env from './core/env.js';\nimport * as zrUtil from './core/util.js';\nimport Handler from './Handler.js';\nimport Storage from './Storage.js';\nimport Animation, { getTime } from './animation/Animation.js';\nimport HandlerProxy from './dom/HandlerProxy.js';\nimport { lum } from './tool/color.js';\nimport { DARK_MODE_THRESHOLD } from './config.js';\nimport Group from './graphic/Group.js';\nvar painterCtors = {};\nvar instances = {};\nfunction delInstance(id) {\n    delete instances[id];\n}\nfunction isDarkMode(backgroundColor) {\n    if (!backgroundColor) {\n        return false;\n    }\n    if (typeof backgroundColor === 'string') {\n        return lum(backgroundColor, 1) < DARK_MODE_THRESHOLD;\n    }\n    else if (backgroundColor.colorStops) {\n        var colorStops = backgroundColor.colorStops;\n        var totalLum = 0;\n        var len = colorStops.length;\n        for (var i = 0; i < len; i++) {\n            totalLum += lum(colorStops[i].color, 1);\n        }\n        totalLum /= len;\n        return totalLum < DARK_MODE_THRESHOLD;\n    }\n    return false;\n}\nvar ZRender = (function () {\n    function ZRender(id, dom, opts) {\n        var _this = this;\n        this._sleepAfterStill = 10;\n        this._stillFrameAccum = 0;\n        this._needsRefresh = true;\n        this._needsRefreshHover = true;\n        this._darkMode = false;\n        opts = opts || {};\n        this.dom = dom;\n        this.id = id;\n        var storage = new Storage();\n        var rendererType = opts.renderer || 'canvas';\n        if (!painterCtors[rendererType]) {\n            rendererType = zrUtil.keys(painterCtors)[0];\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (!painterCtors[rendererType]) {\n                throw new Error(\"Renderer '\" + rendererType + \"' is not imported. Please import it first.\");\n            }\n        }\n        opts.useDirtyRect = opts.useDirtyRect == null\n            ? false\n            : opts.useDirtyRect;\n        var painter = new painterCtors[rendererType](dom, storage, opts, id);\n        var ssrMode = opts.ssr || painter.ssrOnly;\n        this.storage = storage;\n        this.painter = painter;\n        var handlerProxy = (!env.node && !env.worker && !ssrMode)\n            ? new HandlerProxy(painter.getViewportRoot(), painter.root)\n            : null;\n        var useCoarsePointer = opts.useCoarsePointer;\n        var usePointerSize = (useCoarsePointer == null || useCoarsePointer === 'auto')\n            ? env.touchEventsSupported\n            : !!useCoarsePointer;\n        var defaultPointerSize = 44;\n        var pointerSize;\n        if (usePointerSize) {\n            pointerSize = zrUtil.retrieve2(opts.pointerSize, defaultPointerSize);\n        }\n        this.handler = new Handler(storage, painter, handlerProxy, painter.root, pointerSize);\n        this.animation = new Animation({\n            stage: {\n                update: ssrMode ? null : function () { return _this._flush(true); }\n            }\n        });\n        if (!ssrMode) {\n            this.animation.start();\n        }\n    }\n    ZRender.prototype.add = function (el) {\n        if (this._disposed || !el) {\n            return;\n        }\n        this.storage.addRoot(el);\n        el.addSelfToZr(this);\n        this.refresh();\n    };\n    ZRender.prototype.remove = function (el) {\n        if (this._disposed || !el) {\n            return;\n        }\n        this.storage.delRoot(el);\n        el.removeSelfFromZr(this);\n        this.refresh();\n    };\n    ZRender.prototype.configLayer = function (zLevel, config) {\n        if (this._disposed) {\n            return;\n        }\n        if (this.painter.configLayer) {\n            this.painter.configLayer(zLevel, config);\n        }\n        this.refresh();\n    };\n    ZRender.prototype.setBackgroundColor = function (backgroundColor) {\n        if (this._disposed) {\n            return;\n        }\n        if (this.painter.setBackgroundColor) {\n            this.painter.setBackgroundColor(backgroundColor);\n        }\n        this.refresh();\n        this._backgroundColor = backgroundColor;\n        this._darkMode = isDarkMode(backgroundColor);\n    };\n    ZRender.prototype.getBackgroundColor = function () {\n        return this._backgroundColor;\n    };\n    ZRender.prototype.setDarkMode = function (darkMode) {\n        this._darkMode = darkMode;\n    };\n    ZRender.prototype.isDarkMode = function () {\n        return this._darkMode;\n    };\n    ZRender.prototype.refreshImmediately = function (fromInside) {\n        if (this._disposed) {\n            return;\n        }\n        if (!fromInside) {\n            this.animation.update(true);\n        }\n        this._needsRefresh = false;\n        this.painter.refresh();\n        this._needsRefresh = false;\n    };\n    ZRender.prototype.refresh = function () {\n        if (this._disposed) {\n            return;\n        }\n        this._needsRefresh = true;\n        this.animation.start();\n    };\n    ZRender.prototype.flush = function () {\n        if (this._disposed) {\n            return;\n        }\n        this._flush(false);\n    };\n    ZRender.prototype._flush = function (fromInside) {\n        var triggerRendered;\n        var start = getTime();\n        if (this._needsRefresh) {\n            triggerRendered = true;\n            this.refreshImmediately(fromInside);\n        }\n        if (this._needsRefreshHover) {\n            triggerRendered = true;\n            this.refreshHoverImmediately();\n        }\n        var end = getTime();\n        if (triggerRendered) {\n            this._stillFrameAccum = 0;\n            this.trigger('rendered', {\n                elapsedTime: end - start\n            });\n        }\n        else if (this._sleepAfterStill > 0) {\n            this._stillFrameAccum++;\n            if (this._stillFrameAccum > this._sleepAfterStill) {\n                this.animation.stop();\n            }\n        }\n    };\n    ZRender.prototype.setSleepAfterStill = function (stillFramesCount) {\n        this._sleepAfterStill = stillFramesCount;\n    };\n    ZRender.prototype.wakeUp = function () {\n        if (this._disposed) {\n            return;\n        }\n        this.animation.start();\n        this._stillFrameAccum = 0;\n    };\n    ZRender.prototype.refreshHover = function () {\n        this._needsRefreshHover = true;\n    };\n    ZRender.prototype.refreshHoverImmediately = function () {\n        if (this._disposed) {\n            return;\n        }\n        this._needsRefreshHover = false;\n        if (this.painter.refreshHover && this.painter.getType() === 'canvas') {\n            this.painter.refreshHover();\n        }\n    };\n    ZRender.prototype.resize = function (opts) {\n        if (this._disposed) {\n            return;\n        }\n        opts = opts || {};\n        this.painter.resize(opts.width, opts.height);\n        this.handler.resize();\n    };\n    ZRender.prototype.clearAnimation = function () {\n        if (this._disposed) {\n            return;\n        }\n        this.animation.clear();\n    };\n    ZRender.prototype.getWidth = function () {\n        if (this._disposed) {\n            return;\n        }\n        return this.painter.getWidth();\n    };\n    ZRender.prototype.getHeight = function () {\n        if (this._disposed) {\n            return;\n        }\n        return this.painter.getHeight();\n    };\n    ZRender.prototype.setCursorStyle = function (cursorStyle) {\n        if (this._disposed) {\n            return;\n        }\n        this.handler.setCursorStyle(cursorStyle);\n    };\n    ZRender.prototype.findHover = function (x, y) {\n        if (this._disposed) {\n            return;\n        }\n        return this.handler.findHover(x, y);\n    };\n    ZRender.prototype.on = function (eventName, eventHandler, context) {\n        if (!this._disposed) {\n            this.handler.on(eventName, eventHandler, context);\n        }\n        return this;\n    };\n    ZRender.prototype.off = function (eventName, eventHandler) {\n        if (this._disposed) {\n            return;\n        }\n        this.handler.off(eventName, eventHandler);\n    };\n    ZRender.prototype.trigger = function (eventName, event) {\n        if (this._disposed) {\n            return;\n        }\n        this.handler.trigger(eventName, event);\n    };\n    ZRender.prototype.clear = function () {\n        if (this._disposed) {\n            return;\n        }\n        var roots = this.storage.getRoots();\n        for (var i = 0; i < roots.length; i++) {\n            if (roots[i] instanceof Group) {\n                roots[i].removeSelfFromZr(this);\n            }\n        }\n        this.storage.delAllRoots();\n        this.painter.clear();\n    };\n    ZRender.prototype.dispose = function () {\n        if (this._disposed) {\n            return;\n        }\n        this.animation.stop();\n        this.clear();\n        this.storage.dispose();\n        this.painter.dispose();\n        this.handler.dispose();\n        this.animation =\n            this.storage =\n                this.painter =\n                    this.handler = null;\n        this._disposed = true;\n        delInstance(this.id);\n    };\n    return ZRender;\n}());\nexport function init(dom, opts) {\n    var zr = new ZRender(zrUtil.guid(), dom, opts);\n    instances[zr.id] = zr;\n    return zr;\n}\nexport function dispose(zr) {\n    zr.dispose();\n}\nexport function disposeAll() {\n    for (var key in instances) {\n        if (instances.hasOwnProperty(key)) {\n            instances[key].dispose();\n        }\n    }\n    instances = {};\n}\nexport function getInstance(id) {\n    return instances[id];\n}\nexport function registerPainter(name, Ctor) {\n    painterCtors[name] = Ctor;\n}\nvar ssrDataGetter;\nexport function getElementSSRData(el) {\n    if (typeof ssrDataGetter === 'function') {\n        return ssrDataGetter(el);\n    }\n}\nexport function registerSSRDataGetter(getter) {\n    ssrDataGetter = getter;\n}\nexport var version = '5.6.1';\n;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,GAAG,MAAM,eAAe;AAC/B,OAAO,KAAKC,MAAM,MAAM,gBAAgB;AACxC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,SAAS,IAAIC,OAAO,QAAQ,0BAA0B;AAC7D,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SAASC,mBAAmB,QAAQ,aAAa;AACjD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,IAAIC,YAAY,GAAG,CAAC,CAAC;AACrB,IAAIC,SAAS,GAAG,CAAC,CAAC;AAClB,SAASC,WAAWA,CAACC,EAAE,EAAE;EACrB,OAAOF,SAAS,CAACE,EAAE,CAAC;AACxB;AACA,SAASC,UAAUA,CAACC,eAAe,EAAE;EACjC,IAAI,CAACA,eAAe,EAAE;IAClB,OAAO,KAAK;EAChB;EACA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;IACrC,OAAOR,GAAG,CAACQ,eAAe,EAAE,CAAC,CAAC,GAAGP,mBAAmB;EACxD,CAAC,MACI,IAAIO,eAAe,CAACC,UAAU,EAAE;IACjC,IAAIA,UAAU,GAAGD,eAAe,CAACC,UAAU;IAC3C,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,GAAG,GAAGF,UAAU,CAACG,MAAM;IAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;MAC1BH,QAAQ,IAAIV,GAAG,CAACS,UAAU,CAACI,CAAC,CAAC,CAACC,KAAK,EAAE,CAAC,CAAC;IAC3C;IACAJ,QAAQ,IAAIC,GAAG;IACf,OAAOD,QAAQ,GAAGT,mBAAmB;EACzC;EACA,OAAO,KAAK;AAChB;AACA,IAAIc,OAAO,GAAI,YAAY;EACvB,SAASA,OAAOA,CAACT,EAAE,EAAEU,GAAG,EAAEC,IAAI,EAAE;IAC5B,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtBN,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACV,EAAE,GAAGA,EAAE;IACZ,IAAIkB,OAAO,GAAG,IAAI5B,OAAO,CAAC,CAAC;IAC3B,IAAI6B,YAAY,GAAGR,IAAI,CAACS,QAAQ,IAAI,QAAQ;IAC5C,IAAI,CAACvB,YAAY,CAACsB,YAAY,CAAC,EAAE;MAC7BA,YAAY,GAAG/B,MAAM,CAACiC,IAAI,CAACxB,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/C;IACA,IAAIyB,OAAO,CAACnC,GAAG,CAACoC,QAAQ,KAAK,YAAY,EAAE;MACvC,IAAI,CAAC1B,YAAY,CAACsB,YAAY,CAAC,EAAE;QAC7B,MAAM,IAAIK,KAAK,CAAC,YAAY,GAAGL,YAAY,GAAG,4CAA4C,CAAC;MAC/F;IACJ;IACAR,IAAI,CAACc,YAAY,GAAGd,IAAI,CAACc,YAAY,IAAI,IAAI,GACvC,KAAK,GACLd,IAAI,CAACc,YAAY;IACvB,IAAIC,OAAO,GAAG,IAAI7B,YAAY,CAACsB,YAAY,CAAC,CAACT,GAAG,EAAEQ,OAAO,EAAEP,IAAI,EAAEX,EAAE,CAAC;IACpE,IAAI2B,OAAO,GAAGhB,IAAI,CAACiB,GAAG,IAAIF,OAAO,CAACG,OAAO;IACzC,IAAI,CAACX,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACQ,OAAO,GAAGA,OAAO;IACtB,IAAII,YAAY,GAAI,CAAC3C,GAAG,CAAC4C,IAAI,IAAI,CAAC5C,GAAG,CAAC6C,MAAM,IAAI,CAACL,OAAO,GAClD,IAAIlC,YAAY,CAACiC,OAAO,CAACO,eAAe,CAAC,CAAC,EAAEP,OAAO,CAACQ,IAAI,CAAC,GACzD,IAAI;IACV,IAAIC,gBAAgB,GAAGxB,IAAI,CAACwB,gBAAgB;IAC5C,IAAIC,cAAc,GAAID,gBAAgB,IAAI,IAAI,IAAIA,gBAAgB,KAAK,MAAM,GACvEhD,GAAG,CAACkD,oBAAoB,GACxB,CAAC,CAACF,gBAAgB;IACxB,IAAIG,kBAAkB,GAAG,EAAE;IAC3B,IAAIC,WAAW;IACf,IAAIH,cAAc,EAAE;MAChBG,WAAW,GAAGnD,MAAM,CAACoD,SAAS,CAAC7B,IAAI,CAAC4B,WAAW,EAAED,kBAAkB,CAAC;IACxE;IACA,IAAI,CAACG,OAAO,GAAG,IAAIpD,OAAO,CAAC6B,OAAO,EAAEQ,OAAO,EAAEI,YAAY,EAAEJ,OAAO,CAACQ,IAAI,EAAEK,WAAW,CAAC;IACrF,IAAI,CAACG,SAAS,GAAG,IAAInD,SAAS,CAAC;MAC3BoD,KAAK,EAAE;QACHC,MAAM,EAAEjB,OAAO,GAAG,IAAI,GAAG,YAAY;UAAE,OAAOf,KAAK,CAACiC,MAAM,CAAC,IAAI,CAAC;QAAE;MACtE;IACJ,CAAC,CAAC;IACF,IAAI,CAAClB,OAAO,EAAE;MACV,IAAI,CAACe,SAAS,CAACI,KAAK,CAAC,CAAC;IAC1B;EACJ;EACArC,OAAO,CAACsC,SAAS,CAACC,GAAG,GAAG,UAAUC,EAAE,EAAE;IAClC,IAAI,IAAI,CAACC,SAAS,IAAI,CAACD,EAAE,EAAE;MACvB;IACJ;IACA,IAAI,CAAC/B,OAAO,CAACiC,OAAO,CAACF,EAAE,CAAC;IACxBA,EAAE,CAACG,WAAW,CAAC,IAAI,CAAC;IACpB,IAAI,CAACC,OAAO,CAAC,CAAC;EAClB,CAAC;EACD5C,OAAO,CAACsC,SAAS,CAACO,MAAM,GAAG,UAAUL,EAAE,EAAE;IACrC,IAAI,IAAI,CAACC,SAAS,IAAI,CAACD,EAAE,EAAE;MACvB;IACJ;IACA,IAAI,CAAC/B,OAAO,CAACqC,OAAO,CAACN,EAAE,CAAC;IACxBA,EAAE,CAACO,gBAAgB,CAAC,IAAI,CAAC;IACzB,IAAI,CAACH,OAAO,CAAC,CAAC;EAClB,CAAC;EACD5C,OAAO,CAACsC,SAAS,CAACU,WAAW,GAAG,UAAUC,MAAM,EAAEC,MAAM,EAAE;IACtD,IAAI,IAAI,CAACT,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,IAAI,CAACxB,OAAO,CAAC+B,WAAW,EAAE;MAC1B,IAAI,CAAC/B,OAAO,CAAC+B,WAAW,CAACC,MAAM,EAAEC,MAAM,CAAC;IAC5C;IACA,IAAI,CAACN,OAAO,CAAC,CAAC;EAClB,CAAC;EACD5C,OAAO,CAACsC,SAAS,CAACa,kBAAkB,GAAG,UAAU1D,eAAe,EAAE;IAC9D,IAAI,IAAI,CAACgD,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,IAAI,CAACxB,OAAO,CAACkC,kBAAkB,EAAE;MACjC,IAAI,CAAClC,OAAO,CAACkC,kBAAkB,CAAC1D,eAAe,CAAC;IACpD;IACA,IAAI,CAACmD,OAAO,CAAC,CAAC;IACd,IAAI,CAACQ,gBAAgB,GAAG3D,eAAe;IACvC,IAAI,CAACe,SAAS,GAAGhB,UAAU,CAACC,eAAe,CAAC;EAChD,CAAC;EACDO,OAAO,CAACsC,SAAS,CAACe,kBAAkB,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACD,gBAAgB;EAChC,CAAC;EACDpD,OAAO,CAACsC,SAAS,CAACgB,WAAW,GAAG,UAAUC,QAAQ,EAAE;IAChD,IAAI,CAAC/C,SAAS,GAAG+C,QAAQ;EAC7B,CAAC;EACDvD,OAAO,CAACsC,SAAS,CAAC9C,UAAU,GAAG,YAAY;IACvC,OAAO,IAAI,CAACgB,SAAS;EACzB,CAAC;EACDR,OAAO,CAACsC,SAAS,CAACkB,kBAAkB,GAAG,UAAUC,UAAU,EAAE;IACzD,IAAI,IAAI,CAAChB,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACgB,UAAU,EAAE;MACb,IAAI,CAACxB,SAAS,CAACE,MAAM,CAAC,IAAI,CAAC;IAC/B;IACA,IAAI,CAAC7B,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACW,OAAO,CAAC2B,OAAO,CAAC,CAAC;IACtB,IAAI,CAACtC,aAAa,GAAG,KAAK;EAC9B,CAAC;EACDN,OAAO,CAACsC,SAAS,CAACM,OAAO,GAAG,YAAY;IACpC,IAAI,IAAI,CAACH,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACnC,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC2B,SAAS,CAACI,KAAK,CAAC,CAAC;EAC1B,CAAC;EACDrC,OAAO,CAACsC,SAAS,CAACoB,KAAK,GAAG,YAAY;IAClC,IAAI,IAAI,CAACjB,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACL,MAAM,CAAC,KAAK,CAAC;EACtB,CAAC;EACDpC,OAAO,CAACsC,SAAS,CAACF,MAAM,GAAG,UAAUqB,UAAU,EAAE;IAC7C,IAAIE,eAAe;IACnB,IAAItB,KAAK,GAAGtD,OAAO,CAAC,CAAC;IACrB,IAAI,IAAI,CAACuB,aAAa,EAAE;MACpBqD,eAAe,GAAG,IAAI;MACtB,IAAI,CAACH,kBAAkB,CAACC,UAAU,CAAC;IACvC;IACA,IAAI,IAAI,CAAClD,kBAAkB,EAAE;MACzBoD,eAAe,GAAG,IAAI;MACtB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAIC,GAAG,GAAG9E,OAAO,CAAC,CAAC;IACnB,IAAI4E,eAAe,EAAE;MACjB,IAAI,CAACtD,gBAAgB,GAAG,CAAC;MACzB,IAAI,CAACyD,OAAO,CAAC,UAAU,EAAE;QACrBC,WAAW,EAAEF,GAAG,GAAGxB;MACvB,CAAC,CAAC;IACN,CAAC,MACI,IAAI,IAAI,CAACjC,gBAAgB,GAAG,CAAC,EAAE;MAChC,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACD,gBAAgB,EAAE;QAC/C,IAAI,CAAC6B,SAAS,CAAC+B,IAAI,CAAC,CAAC;MACzB;IACJ;EACJ,CAAC;EACDhE,OAAO,CAACsC,SAAS,CAAC2B,kBAAkB,GAAG,UAAUC,gBAAgB,EAAE;IAC/D,IAAI,CAAC9D,gBAAgB,GAAG8D,gBAAgB;EAC5C,CAAC;EACDlE,OAAO,CAACsC,SAAS,CAAC6B,MAAM,GAAG,YAAY;IACnC,IAAI,IAAI,CAAC1B,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACR,SAAS,CAACI,KAAK,CAAC,CAAC;IACtB,IAAI,CAAChC,gBAAgB,GAAG,CAAC;EAC7B,CAAC;EACDL,OAAO,CAACsC,SAAS,CAAC8B,YAAY,GAAG,YAAY;IACzC,IAAI,CAAC7D,kBAAkB,GAAG,IAAI;EAClC,CAAC;EACDP,OAAO,CAACsC,SAAS,CAACsB,uBAAuB,GAAG,YAAY;IACpD,IAAI,IAAI,CAACnB,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAAClC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,IAAI,CAACU,OAAO,CAACmD,YAAY,IAAI,IAAI,CAACnD,OAAO,CAACoD,OAAO,CAAC,CAAC,KAAK,QAAQ,EAAE;MAClE,IAAI,CAACpD,OAAO,CAACmD,YAAY,CAAC,CAAC;IAC/B;EACJ,CAAC;EACDpE,OAAO,CAACsC,SAAS,CAACgC,MAAM,GAAG,UAAUpE,IAAI,EAAE;IACvC,IAAI,IAAI,CAACuC,SAAS,EAAE;MAChB;IACJ;IACAvC,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,IAAI,CAACe,OAAO,CAACqD,MAAM,CAACpE,IAAI,CAACqE,KAAK,EAAErE,IAAI,CAACsE,MAAM,CAAC;IAC5C,IAAI,CAACxC,OAAO,CAACsC,MAAM,CAAC,CAAC;EACzB,CAAC;EACDtE,OAAO,CAACsC,SAAS,CAACmC,cAAc,GAAG,YAAY;IAC3C,IAAI,IAAI,CAAChC,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACR,SAAS,CAACyC,KAAK,CAAC,CAAC;EAC1B,CAAC;EACD1E,OAAO,CAACsC,SAAS,CAACqC,QAAQ,GAAG,YAAY;IACrC,IAAI,IAAI,CAAClC,SAAS,EAAE;MAChB;IACJ;IACA,OAAO,IAAI,CAACxB,OAAO,CAAC0D,QAAQ,CAAC,CAAC;EAClC,CAAC;EACD3E,OAAO,CAACsC,SAAS,CAACsC,SAAS,GAAG,YAAY;IACtC,IAAI,IAAI,CAACnC,SAAS,EAAE;MAChB;IACJ;IACA,OAAO,IAAI,CAACxB,OAAO,CAAC2D,SAAS,CAAC,CAAC;EACnC,CAAC;EACD5E,OAAO,CAACsC,SAAS,CAACuC,cAAc,GAAG,UAAUC,WAAW,EAAE;IACtD,IAAI,IAAI,CAACrC,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACT,OAAO,CAAC6C,cAAc,CAACC,WAAW,CAAC;EAC5C,CAAC;EACD9E,OAAO,CAACsC,SAAS,CAACyC,SAAS,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC1C,IAAI,IAAI,CAACxC,SAAS,EAAE;MAChB;IACJ;IACA,OAAO,IAAI,CAACT,OAAO,CAAC+C,SAAS,CAACC,CAAC,EAAEC,CAAC,CAAC;EACvC,CAAC;EACDjF,OAAO,CAACsC,SAAS,CAAC4C,EAAE,GAAG,UAAUC,SAAS,EAAEC,YAAY,EAAEC,OAAO,EAAE;IAC/D,IAAI,CAAC,IAAI,CAAC5C,SAAS,EAAE;MACjB,IAAI,CAACT,OAAO,CAACkD,EAAE,CAACC,SAAS,EAAEC,YAAY,EAAEC,OAAO,CAAC;IACrD;IACA,OAAO,IAAI;EACf,CAAC;EACDrF,OAAO,CAACsC,SAAS,CAACgD,GAAG,GAAG,UAAUH,SAAS,EAAEC,YAAY,EAAE;IACvD,IAAI,IAAI,CAAC3C,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACT,OAAO,CAACsD,GAAG,CAACH,SAAS,EAAEC,YAAY,CAAC;EAC7C,CAAC;EACDpF,OAAO,CAACsC,SAAS,CAACwB,OAAO,GAAG,UAAUqB,SAAS,EAAEI,KAAK,EAAE;IACpD,IAAI,IAAI,CAAC9C,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACT,OAAO,CAAC8B,OAAO,CAACqB,SAAS,EAAEI,KAAK,CAAC;EAC1C,CAAC;EACDvF,OAAO,CAACsC,SAAS,CAACoC,KAAK,GAAG,YAAY;IAClC,IAAI,IAAI,CAACjC,SAAS,EAAE;MAChB;IACJ;IACA,IAAI+C,KAAK,GAAG,IAAI,CAAC/E,OAAO,CAACgF,QAAQ,CAAC,CAAC;IACnC,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,KAAK,CAAC3F,MAAM,EAAEC,CAAC,EAAE,EAAE;MACnC,IAAI0F,KAAK,CAAC1F,CAAC,CAAC,YAAYX,KAAK,EAAE;QAC3BqG,KAAK,CAAC1F,CAAC,CAAC,CAACiD,gBAAgB,CAAC,IAAI,CAAC;MACnC;IACJ;IACA,IAAI,CAACtC,OAAO,CAACiF,WAAW,CAAC,CAAC;IAC1B,IAAI,CAACzE,OAAO,CAACyD,KAAK,CAAC,CAAC;EACxB,CAAC;EACD1E,OAAO,CAACsC,SAAS,CAACqD,OAAO,GAAG,YAAY;IACpC,IAAI,IAAI,CAAClD,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAACR,SAAS,CAAC+B,IAAI,CAAC,CAAC;IACrB,IAAI,CAACU,KAAK,CAAC,CAAC;IACZ,IAAI,CAACjE,OAAO,CAACkF,OAAO,CAAC,CAAC;IACtB,IAAI,CAAC1E,OAAO,CAAC0E,OAAO,CAAC,CAAC;IACtB,IAAI,CAAC3D,OAAO,CAAC2D,OAAO,CAAC,CAAC;IACtB,IAAI,CAAC1D,SAAS,GACV,IAAI,CAACxB,OAAO,GACR,IAAI,CAACQ,OAAO,GACR,IAAI,CAACe,OAAO,GAAG,IAAI;IAC/B,IAAI,CAACS,SAAS,GAAG,IAAI;IACrBnD,WAAW,CAAC,IAAI,CAACC,EAAE,CAAC;EACxB,CAAC;EACD,OAAOS,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,OAAO,SAAS4F,IAAIA,CAAC3F,GAAG,EAAEC,IAAI,EAAE;EAC5B,IAAI2F,EAAE,GAAG,IAAI7F,OAAO,CAACrB,MAAM,CAACmH,IAAI,CAAC,CAAC,EAAE7F,GAAG,EAAEC,IAAI,CAAC;EAC9Cb,SAAS,CAACwG,EAAE,CAACtG,EAAE,CAAC,GAAGsG,EAAE;EACrB,OAAOA,EAAE;AACb;AACA,OAAO,SAASF,OAAOA,CAACE,EAAE,EAAE;EACxBA,EAAE,CAACF,OAAO,CAAC,CAAC;AAChB;AACA,OAAO,SAASI,UAAUA,CAAA,EAAG;EACzB,KAAK,IAAIC,GAAG,IAAI3G,SAAS,EAAE;IACvB,IAAIA,SAAS,CAAC4G,cAAc,CAACD,GAAG,CAAC,EAAE;MAC/B3G,SAAS,CAAC2G,GAAG,CAAC,CAACL,OAAO,CAAC,CAAC;IAC5B;EACJ;EACAtG,SAAS,GAAG,CAAC,CAAC;AAClB;AACA,OAAO,SAAS6G,WAAWA,CAAC3G,EAAE,EAAE;EAC5B,OAAOF,SAAS,CAACE,EAAE,CAAC;AACxB;AACA,OAAO,SAAS4G,eAAeA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACxCjH,YAAY,CAACgH,IAAI,CAAC,GAAGC,IAAI;AAC7B;AACA,IAAIC,aAAa;AACjB,OAAO,SAASC,iBAAiBA,CAAC/D,EAAE,EAAE;EAClC,IAAI,OAAO8D,aAAa,KAAK,UAAU,EAAE;IACrC,OAAOA,aAAa,CAAC9D,EAAE,CAAC;EAC5B;AACJ;AACA,OAAO,SAASgE,qBAAqBA,CAACC,MAAM,EAAE;EAC1CH,aAAa,GAAGG,MAAM;AAC1B;AACA,OAAO,IAAIC,OAAO,GAAG,OAAO;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}