{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\n// import { dataApi } from './api/index.js'\nimport { v4 as uuidv4 } from 'uuid';\nimport axios from 'axios';\nimport ChartDisplay from './components/ChartDisplay.vue';\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas';\nimport { jsPDF } from 'jspdf';\nimport * as echarts from 'echarts';\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false,\n      // 新增：控制原始响应弹出层\n      lastRawResponse: '',\n      // 新增：存储最后收到的原始响应\n      drawer: false,\n      //抽屉展示\n      direction: 'rtl',\n      //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      innerDrawer: false,\n      tableList: []\n    };\n  },\n  mounted() {\n    this.loadTables();\n    this.initMemoryId();\n    this.addWelcomeMessage();\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\"帮我生成一个销售额柱状图\", \"展示近六个月的销售趋势折线图\", \"按照区域统计销售量并生成饼图\", \"帮我做一个按产品类别的销量对比图\"];\n  },\n  updated() {\n    this.scrollToBottom();\n  },\n  methods: {\n    SelectDataList() {\n      this.loadTables();\n      this.drawer = true;\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id');\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4());\n        localStorage.setItem('user_memory_id', storedMemoryId);\n      }\n      this.memoryId = storedMemoryId;\n    },\n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0;\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i];\n        number = number * 16 + (parseInt(hexValue, 16) || 0);\n      }\n      return number % 1000000;\n    },\n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      });\n    },\n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight;\n      }\n    },\n    async loadTables() {\n      let res = {\n        \"code\": 0,\n        \"msg\": null,\n        \"data\": [{\n          \"id\": \"0\",\n          \"name\": \"root\",\n          \"leaf\": false,\n          \"weight\": 7,\n          \"extraFlag\": 0,\n          \"type\": null,\n          \"children\": [{\n            \"id\": \"1139987253200818176\",\n            \"name\": \"ioio\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1122506616361586688\",\n            \"name\": \"一测KingBaseDataSet\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119354708838977536\",\n            \"name\": \"未命名数据EXCEl\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119347979212427264\",\n            \"name\": \"测试EXCEL数据集\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119242779856343040\",\n            \"name\": \"121213\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119240262607048704\",\n            \"name\": \"12.89\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119235024886042624\",\n            \"name\": \"1231\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119234081960366080\",\n            \"name\": \"14\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1118282918146478080\",\n            \"name\": \"测试45\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1118275224819863552\",\n            \"name\": \"15\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1118263178069938176\",\n            \"name\": \"测试5\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1118260462249709568\",\n            \"name\": \"测试data为空\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117533961606795264\",\n            \"name\": \"测试\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117529367518515200\",\n            \"name\": \"354\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117520088862429184\",\n            \"name\": \"test01\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117452714880667648\",\n            \"name\": \"未命名数据集111111\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117129362492231680\",\n            \"name\": \"未命名数据集1\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117068655105019904\",\n            \"name\": \"333\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114644377738809344\",\n            \"name\": \"排空数--修改版\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114627384671342592\",\n            \"name\": \"23\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114626600391020544\",\n            \"name\": \"35\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114622812997423104\",\n            \"name\": \"测试222221\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114617616477065216\",\n            \"name\": \"166611\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114612956722761728\",\n            \"name\": \"1213\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114612359227379712\",\n            \"name\": \"123\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114610791497207808\",\n            \"name\": \"1212\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114301988100771840\",\n            \"name\": \"铁路运费（不含税）\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114299647981129728\",\n            \"name\": \"通信设备障碍总延时\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114299149395824640\",\n            \"name\": \"大修行走公里数\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114295314858250240\",\n            \"name\": \"剩余牌数\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114284027080216576\",\n            \"name\": \"测试1\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114280404409520128\",\n            \"name\": \"666\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114238649047846912\",\n            \"name\": \"未命名数据集\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114237385908031488\",\n            \"name\": \"112\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114226839552921600\",\n            \"name\": \"测\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114226293651673088\",\n            \"name\": \"ce1\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114225881611636736\",\n            \"name\": \"11\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1113938332037025792\",\n            \"name\": \"数据资产-战略决策\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1113185924126216192\",\n            \"name\": \"未命名数据集111\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1113185517429723136\",\n            \"name\": \"未命名数据集aa\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1111656017395060736\",\n            \"name\": \"^数^据^集\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1077661410659536896\",\n            \"name\": \"包神生产运营指标\",\n            \"leaf\": false,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": [{\n              \"id\": \"1104500774345510912\",\n              \"name\": \"指标\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104499040399265792\",\n              \"name\": \"BSPI环渤海动力煤指数\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104483680820269056\",\n              \"name\": \"CECI价格指数\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104478833836429312\",\n              \"name\": \"环渤海港口动态\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104478365903097856\",\n              \"name\": \"未命名数据集\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104460252918714368\",\n              \"name\": \"大物流运量完成情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1078683560736591872\",\n              \"name\": \"陕煤集团煤矿成交价\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077711277742231552\",\n              \"name\": \"当日分界口列车出入情况对比\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077670569941536768\",\n              \"name\": \"分界口货车出入情况合计\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077669947737509888\",\n              \"name\": \"分界口货车出入情况（当日）\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077668791040741376\",\n              \"name\": \"月度运输收入\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077668450664583168\",\n              \"name\": \"运输效益指标\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077668292577071104\",\n              \"name\": \"专运车卸车情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077667982454427648\",\n              \"name\": \"当日运输情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077667199050715136\",\n              \"name\": \"分界口接入重车违流情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077666214211358720\",\n              \"name\": \"卸车情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077661887262494720\",\n              \"name\": \"装车情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }]\n          }, {\n            \"id\": \"985189269226262528\",\n            \"name\": \"示例\",\n            \"leaf\": false,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": [{\n              \"id\": \"985189053949415424\",\n              \"name\": \"茶饮订单明细\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }]\n          }]\n        }, {\n          \"id\": \"0\",\n          \"name\": \"root\",\n          \"leaf\": false,\n          \"weight\": 7,\n          \"extraFlag\": 0,\n          \"type\": null,\n          \"children\": [{\n            \"id\": \"1139987253200818176\",\n            \"name\": \"ioio\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1122506616361586688\",\n            \"name\": \"一测KingBaseDataSet\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119354708838977536\",\n            \"name\": \"未命名数据EXCEl\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119347979212427264\",\n            \"name\": \"测试EXCEL数据集\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119242779856343040\",\n            \"name\": \"121213\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119240262607048704\",\n            \"name\": \"12.89\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119235024886042624\",\n            \"name\": \"1231\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1119234081960366080\",\n            \"name\": \"14\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1118282918146478080\",\n            \"name\": \"测试45\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1118275224819863552\",\n            \"name\": \"15\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1118263178069938176\",\n            \"name\": \"测试5\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1118260462249709568\",\n            \"name\": \"测试data为空\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117533961606795264\",\n            \"name\": \"测试\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117529367518515200\",\n            \"name\": \"354\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117520088862429184\",\n            \"name\": \"test01\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117452714880667648\",\n            \"name\": \"未命名数据集111111\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117129362492231680\",\n            \"name\": \"未命名数据集1\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1117068655105019904\",\n            \"name\": \"333\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114644377738809344\",\n            \"name\": \"排空数--修改版\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114627384671342592\",\n            \"name\": \"23\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114626600391020544\",\n            \"name\": \"35\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114622812997423104\",\n            \"name\": \"测试222221\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114617616477065216\",\n            \"name\": \"166611\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114612956722761728\",\n            \"name\": \"1213\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114612359227379712\",\n            \"name\": \"123\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114610791497207808\",\n            \"name\": \"1212\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114301988100771840\",\n            \"name\": \"铁路运费（不含税）\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114299647981129728\",\n            \"name\": \"通信设备障碍总延时\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114299149395824640\",\n            \"name\": \"大修行走公里数\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114295314858250240\",\n            \"name\": \"剩余牌数\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114284027080216576\",\n            \"name\": \"测试1\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114280404409520128\",\n            \"name\": \"666\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114238649047846912\",\n            \"name\": \"未命名数据集\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114237385908031488\",\n            \"name\": \"112\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114226839552921600\",\n            \"name\": \"测\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114226293651673088\",\n            \"name\": \"ce1\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1114225881611636736\",\n            \"name\": \"11\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1113938332037025792\",\n            \"name\": \"数据资产-战略决策\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1113185924126216192\",\n            \"name\": \"未命名数据集111\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1113185517429723136\",\n            \"name\": \"未命名数据集aa\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1111656017395060736\",\n            \"name\": \"^数^据^集\",\n            \"leaf\": true,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": null\n          }, {\n            \"id\": \"1077661410659536896\",\n            \"name\": \"包神生产运营指标\",\n            \"leaf\": false,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": [{\n              \"id\": \"1104500774345510912\",\n              \"name\": \"指标\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104499040399265792\",\n              \"name\": \"BSPI环渤海动力煤指数\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104483680820269056\",\n              \"name\": \"CECI价格指数\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104478833836429312\",\n              \"name\": \"环渤海港口动态\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104478365903097856\",\n              \"name\": \"未命名数据集\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1104460252918714368\",\n              \"name\": \"大物流运量完成情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1078683560736591872\",\n              \"name\": \"陕煤集团煤矿成交价\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077711277742231552\",\n              \"name\": \"当日分界口列车出入情况对比\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077670569941536768\",\n              \"name\": \"分界口货车出入情况合计\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077669947737509888\",\n              \"name\": \"分界口货车出入情况（当日）\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077668791040741376\",\n              \"name\": \"月度运输收入\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077668450664583168\",\n              \"name\": \"运输效益指标\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077668292577071104\",\n              \"name\": \"专运车卸车情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077667982454427648\",\n              \"name\": \"当日运输情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077667199050715136\",\n              \"name\": \"分界口接入重车违流情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077666214211358720\",\n              \"name\": \"卸车情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }, {\n              \"id\": \"1077661887262494720\",\n              \"name\": \"装车情况\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }]\n          }, {\n            \"id\": \"985189269226262528\",\n            \"name\": \"示例\",\n            \"leaf\": false,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": [{\n              \"id\": \"985189053949415424\",\n              \"name\": \"茶饮订单明细\",\n              \"leaf\": true,\n              \"weight\": 7,\n              \"extraFlag\": 0,\n              \"type\": null,\n              \"children\": null\n            }]\n          }]\n        }]\n      };\n      this.tables = this.flattenDatasets(res.data);\n      console.log(this.tables, \"=====================1438\");\n\n      // try {\n      //   const response = await dataApi.getAllTables()\n      //   if (response.code === 0 && response.data) {\n      //     console.log(response.data)\n      //     this.tables = this.flattenDatasets(response.data);\n      //     console.log(this.table)\n      //   } else {\n      //     this.$message.error('加载数据表失败');\n      //   }\n      // } catch (error) {\n      //   this.$message.error('加载数据表失败')\n      //   console.error(error)\n      // }\n    },\n    // 将树形结构扁平化为列表\n    flattenDatasets(datasets) {\n      let result = [];\n      const traverse = items => {\n        if (!items || !items.length) return;\n        items.forEach(item => {\n          result.push(item);\n          if (item.children && item.children.length) {\n            traverse(item.children);\n          }\n        });\n      };\n      traverse(datasets);\n      return result;\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n\n    // 显示数据集详情\n    showDatasetDetail() {\n      this.innerDrawer = true;\n      let res = [{\n        \"id\": \"1737102130516\",\n        \"datasourceId\": \"1077661171546460160\",\n        \"datasetTableId\": \"7285934957984223232\",\n        \"datasetGroupId\": \"1077667199050715136\",\n        \"chartId\": null,\n        \"originName\": \"ds\",\n        \"name\": \"吨数\",\n        \"dbFieldName\": null,\n        \"description\": \"吨数\",\n        \"dataeaseName\": \"f_fc20d94b24c93ace\",\n        \"groupType\": \"q\",\n        \"type\": \"VARCHAR\",\n        \"precision\": null,\n        \"scale\": null,\n        \"deType\": 2,\n        \"deExtractType\": 0,\n        \"extField\": 0,\n        \"checked\": true,\n        \"columnIndex\": null,\n        \"lastSyncTime\": null,\n        \"dateFormat\": \"\",\n        \"dateFormatType\": null,\n        \"fieldShortName\": \"f_fc20d94b24c93ace\",\n        \"desensitized\": null,\n        \"params\": null\n      }, {\n        \"id\": \"1737102130517\",\n        \"datasourceId\": \"1077661171546460160\",\n        \"datasetTableId\": \"7285934957984223232\",\n        \"datasetGroupId\": \"1077667199050715136\",\n        \"chartId\": null,\n        \"originName\": \"fjkmc\",\n        \"name\": \"分界口名称\",\n        \"dbFieldName\": null,\n        \"description\": \"分界口名称\",\n        \"dataeaseName\": \"f_891517b6c5e5d7e8\",\n        \"groupType\": \"d\",\n        \"type\": \"VARCHAR\",\n        \"precision\": null,\n        \"scale\": null,\n        \"deType\": 0,\n        \"deExtractType\": 0,\n        \"extField\": 0,\n        \"checked\": true,\n        \"columnIndex\": null,\n        \"lastSyncTime\": null,\n        \"dateFormat\": null,\n        \"dateFormatType\": null,\n        \"fieldShortName\": \"f_891517b6c5e5d7e8\",\n        \"desensitized\": null,\n        \"params\": null\n      }, {\n        \"id\": \"1737102130518\",\n        \"datasourceId\": \"1077661171546460160\",\n        \"datasetTableId\": \"7285934957984223232\",\n        \"datasetGroupId\": \"1077667199050715136\",\n        \"chartId\": null,\n        \"originName\": \"id\",\n        \"name\": \"id\",\n        \"dbFieldName\": null,\n        \"description\": \"id\",\n        \"dataeaseName\": \"f_0a1541124bfd6804\",\n        \"groupType\": \"d\",\n        \"type\": \"INT\",\n        \"precision\": null,\n        \"scale\": null,\n        \"deType\": 2,\n        \"deExtractType\": 2,\n        \"extField\": 0,\n        \"checked\": true,\n        \"columnIndex\": null,\n        \"lastSyncTime\": null,\n        \"dateFormat\": null,\n        \"dateFormatType\": null,\n        \"fieldShortName\": \"f_0a1541124bfd6804\",\n        \"desensitized\": null,\n        \"params\": null\n      }, {\n        \"id\": \"1737102130519\",\n        \"datasourceId\": \"1077661171546460160\",\n        \"datasetTableId\": \"7285934957984223232\",\n        \"datasetGroupId\": \"1077667199050715136\",\n        \"chartId\": null,\n        \"originName\": \"wlcs\",\n        \"name\": \"违流车数\",\n        \"dbFieldName\": null,\n        \"description\": \"违流车数\",\n        \"dataeaseName\": \"f_229c8ebf1eb8c51c\",\n        \"groupType\": \"q\",\n        \"type\": \"VARCHAR\",\n        \"precision\": null,\n        \"scale\": null,\n        \"deType\": 2,\n        \"deExtractType\": 0,\n        \"extField\": 0,\n        \"checked\": true,\n        \"columnIndex\": null,\n        \"lastSyncTime\": null,\n        \"dateFormat\": \"\",\n        \"dateFormatType\": null,\n        \"fieldShortName\": \"f_229c8ebf1eb8c51c\",\n        \"desensitized\": null,\n        \"params\": null\n      }, {\n        \"id\": \"1737102130520\",\n        \"datasourceId\": \"1077661171546460160\",\n        \"datasetTableId\": \"7285934957984223232\",\n        \"datasetGroupId\": \"1077667199050715136\",\n        \"chartId\": null,\n        \"originName\": \"wlls\",\n        \"name\": \"违流列数\",\n        \"dbFieldName\": null,\n        \"description\": \"违流列数\",\n        \"dataeaseName\": \"f_e5f343591786fe02\",\n        \"groupType\": \"q\",\n        \"type\": \"VARCHAR\",\n        \"precision\": null,\n        \"scale\": null,\n        \"deType\": 2,\n        \"deExtractType\": 0,\n        \"extField\": 0,\n        \"checked\": true,\n        \"columnIndex\": null,\n        \"lastSyncTime\": null,\n        \"dateFormat\": \"\",\n        \"dateFormatType\": null,\n        \"fieldShortName\": \"f_e5f343591786fe02\",\n        \"desensitized\": null,\n        \"params\": null\n      }];\n      this.tableList = res;\n      // try {\n      //   // 只有叶子节点才是具体数据集\n      //   if (dataset.leaf) {\n      //     const response = await dataApi.getDatasetDetail(dataset.id);\n      //     this.currentDatasetDetail = response.data;\n\n      //     // 提取字段信息\n      //     if (this.currentDatasetDetail && this.currentDatasetDetail.info && this.currentDatasetDetail.info.union) {\n      //       this.datasetFields = this.currentDatasetDetail.info.union;\n      //     } else if (this.currentDatasetDetail && this.currentDatasetDetail.union) {\n      //       this.datasetFields = this.currentDatasetDetail.union;\n      //     } else {\n      //       this.datasetFields = [];\n      //     }\n\n      //     // 提取数据\n      //     if (this.currentDatasetDetail && this.currentDatasetDetail.data) {\n      //       this.datasetData = this.currentDatasetDetail.data;\n      //     } else {\n      //       this.datasetData = [];\n      //     }\n\n      //     // 关闭数据集列表抽屉，打开数据详情抽屉\n      //     this.drawer = false;\n      //     this.dialogVisible = true;\n      //   } else {\n      //     this.$message.info('这是一个目录，请选择具体的数据集');\n      //   }\n      // } catch (error) {\n      //   console.error('获取数据集详情失败:', error);\n      //   this.$message.error('获取数据集详情失败');\n      // }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n\n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedTable ? this.selectedTable.tableName : this.tables.length > 0 ? this.tables[0].tableName : \"未知数据集\";\n\n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      };\n      this.messages.push(userMsg);\n\n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      };\n      this.messages.push(botMsg);\n\n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1];\n\n      // 保存问题并清空输入框\n      const question = this.question;\n      this.question = '';\n      this.isSending = true;\n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`;\n\n        // 发送请求\n        await axios.post('http://localhost:8088/api/indicator/chat', {\n          memoryId: this.memoryId,\n          message\n        }, {\n          responseType: 'stream',\n          onDownloadProgress: e => {\n            const fullText = e.event.target.responseText; // 累积的完整文本\n            let newText = fullText.substring(lastMsg.content.length);\n            lastMsg.content += newText; // 增量更新\n            this.scrollToBottom(); // 实时滚动\n\n            // 保存原始响应\n            this.lastRawResponse = fullText;\n          }\n        });\n\n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false;\n        this.isSending = false;\n\n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error);\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。';\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      }\n    },\n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n\n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n\n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n\n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n\n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n\n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n\n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n\n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n\n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n\n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n\n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n\n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n\n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n\n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n\n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n\n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n\n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      this.messages.push(botMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n\n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n\n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [{\n            field: '类别1',\n            value: 100\n          }, {\n            field: '类别2',\n            value: 200\n          }, {\n            field: '类别3',\n            value: 150\n          }],\n          metrics: [{\n            name: '数值'\n          }]\n        }\n      };\n\n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n\n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [{\n              value: 1,\n              field: \"神朔\",\n              name: \"神朔\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"甘泉\",\n              name: \"甘泉\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"包神\",\n              name: \"包神\",\n              category: \"记录数*\"\n            }],\n            fields: [{\n              id: \"1746787308487\",\n              name: \"名称\",\n              groupType: \"d\"\n            }, {\n              id: \"-1\",\n              name: \"记录数*\",\n              groupType: \"q\"\n            }]\n          }\n        }\n      };\n\n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n\n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n\n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n\n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n\n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n\n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content.replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '').replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '').trim();\n\n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n\n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n\n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n\n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n\n        // 根据图表类型选择对应的处理函数\n        switch (chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n\n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n\n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n\n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2,\n          // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n\n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = canvas.height * imgWidth / canvas.width;\n\n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n\n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n\n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n\n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n\n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n\n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n\n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n\n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n\n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n\n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n\n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      return options;\n    },\n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n\n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n\n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n\n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n\n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n\n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n\n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n\n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n\n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n};", "map": {"version": 3, "names": ["v4", "uuidv4", "axios", "ChartDisplay", "html2canvas", "jsPDF", "echarts", "name", "components", "data", "description", "tablename", "tables", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "tableIndicators", "dialogVisible", "messages", "memoryId", "isSending", "messageListRef", "showRawResponsePanel", "lastRawResponse", "drawer", "direction", "currentDatasetDetail", "datasetFields", "datasetData", "innerDrawer", "tableList", "mounted", "loadTables", "initMemoryId", "addWelcomeMessage", "updated", "scrollToBottom", "methods", "SelectDataList", "storedMemoryId", "localStorage", "getItem", "uuidToNumber", "setItem", "uuid", "number", "i", "length", "hexValue", "parseInt", "push", "isUser", "content", "isTyping", "$refs", "scrollTop", "scrollHeight", "res", "flattenDatasets", "console", "log", "datasets", "result", "traverse", "items", "for<PERSON>ach", "item", "children", "showDatasetDetail", "showSuggestions", "useQuestion", "q", "getTableFields", "table", "tableCode", "submitQuestion", "trim", "$message", "warning", "currentDataset", "tableName", "userMsg", "botMsg", "lastMsg", "message", "post", "responseType", "onDownloadProgress", "e", "fullText", "event", "target", "responseText", "newText", "substring", "parseChartConfig", "error", "chartDataIdMatch", "match", "chartDataId", "fetchChartDataById", "chartConfigMatch", "chartConfig", "JSON", "parse", "code", "type", "datasetId", "tableId", "replace", "jsonRegex", "jsonMatch", "jsonStr", "parseError", "includes", "defaultConfig", "id", "Date", "now", "toString", "title", "response", "get", "status", "$forceUpdate", "test<PERSON>hart", "testChartConfig", "stringify", "testApiResponse", "msg", "field", "value", "metrics", "json<PERSON><PERSON><PERSON>", "apiResponseMsg", "testRealData", "realData", "category", "fields", "groupType", "realDataMsg", "showRawResponse", "exportToPDF", "$set", "tempContainer", "document", "createElement", "style", "position", "left", "width", "background", "padding", "body", "append<PERSON><PERSON><PERSON>", "datasetName", "currentDate", "toLocaleString", "tempDiv", "innerHTML", "textContent", "innerText", "chartContainer", "querySelector", "chart", "init", "options", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "setOption", "Promise", "resolve", "setTimeout", "dataTableContainer", "renderDataTable", "canvas", "scale", "useCORS", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "imgData", "toDataURL", "pdf", "orientation", "unit", "format", "imgWidth", "imgHeight", "height", "addImage", "save", "getTime", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "success", "createBasicChartOptions", "categories", "map", "text", "tooltip", "trigger", "xAxis", "yAxis", "series", "seriesMap", "s", "Array", "fill", "index", "Object", "values", "chartData", "isArray", "filter", "f", "xAxisData", "categoriesSet", "Set", "add", "from", "seriesData", "find", "axisPointer", "legend", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "container", "headers", "firstItem", "tableHTML", "header", "undefined"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n        </div>\n        \n        <div class=\"data-selection\">\n          <h3>目前可用数据</h3>\n          <!-- <div class=\"data-sets\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\" v-for=\"table in tables\" :key=\"table.id\">\n                <el-card class=\"data-card\" @click=\"selectDataset(table)\">\n                  <div class=\"data-header\">\n                    <span class=\"sample-tag\">{{table.name}}</span>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n          </div> -->\n        </div>\n        \n        <!-- 聊天消息列表区域 -->\n        <div class=\"message-list\" ref=\"messageListRef\">\n          <div\n            v-for=\"(message, index) in messages\"\n            :key=\"index\"\n            :class=\"message.isUser ? 'message user-message' : 'message bot-message'\"\n          >\n            <!-- 聊天图标 -->\n            <div class=\"avatar-container\" v-if=\"!message.isUser\">\n              <div class=\"bot-avatar\">\n                <i class=\"el-icon-s-tools\"></i>\n              </div>\n            </div>\n            <!-- 消息内容 -->\n            <div class=\"message-content\">\n              <div v-html=\"message.content\"></div>\n              <!-- 如果消息中包含图表配置，则显示图表和导出按钮 -->\n              <div v-if=\"message.chartConfig\" class=\"chart-container\">\n                <div class=\"chart-actions\">\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-download\" \n                            @click=\"exportToPDF(message)\" :loading=\"message.exporting\">\n                    导出PDF\n                  </el-button>\n                </div>\n                <chart-display :chart-config=\"message.chartConfig\" ref=\"chartDisplay\"></chart-display>\n              </div>\n              <!-- loading动画 -->\n              <span\n                class=\"loading-dots\"\n                v-if=\"message.isTyping\"\n              >\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n              </span>\n            </div>\n            <div class=\"avatar-container\" v-if=\"message.isUser\">\n              <div class=\"user-avatar\">\n                <i class=\"el-icon-user\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 底部问题输入区域（移至message-list下方） -->\n        <div class=\"question-input-container\">\n          <span>👋直接问我问题，或在上方选择一个主题/数据开始！</span>\n          <div class=\"question-input-wrapper\">\n            <el-button type=\"text\" style=\"margin-left: 10px;\"  @click=\"SelectDataList\">选择数据</el-button>\n            <el-input \n              style=\"margin-bottom: 12px;width: 800px;\"\n              v-model=\"question\" \n              placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n              class=\"question-input\"\n              @keyup.enter.native=\"submitQuestion\"\n              :disabled=\"isSending\">\n            </el-input>\n            <div class=\"input-actions\">\n              <button class=\"action-btn\" @click=\"showSuggestions\">\n                <i class=\"el-icon-magic-stick\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testChart\" title=\"测试图表功能\">\n                <i class=\"el-icon-data-line\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testRealData\" title=\"测试实际数据\">\n                <i class=\"el-icon-s-data\"></i>\n              </button>\n              <button class=\"action-btn debug-btn\" @click=\"showRawResponse\" title=\"显示AI原始响应\">\n                <i class=\"el-icon-monitor\"></i>\n              </button>\n              <button class=\"action-btn send-btn\" @click=\"submitQuestion\" :disabled=\"isSending\">\n                <i class=\"el-icon-position\"></i>\n              </button>\n            </div>\n          </div>\n          \n          <!-- 建议问题弹出层 -->\n          <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n            <div class=\"suggestions-title\">\n              <i class=\"el-icon-s-promotion\"></i> 官方推荐\n            </div>\n            <div class=\"suggestions-list\">\n              <div \n                v-for=\"(suggestion, index) in suggestedQuestions\" \n                :key=\"index\"\n                class=\"suggestion-item\"\n                @click=\"useQuestion(suggestion)\">\n                {{ suggestion }}\n              </div>\n            </div>\n          </div>\n          \n          <!-- AI原始响应弹出层 -->\n          <div v-if=\"showRawResponsePanel\" class=\"raw-response-panel\">\n            <div class=\"raw-response-title\">\n              <i class=\"el-icon-monitor\"></i> AI原始响应\n              <button class=\"close-btn\" @click=\"showRawResponsePanel = false\">\n                <i class=\"el-icon-close\"></i>\n              </button>\n            </div>\n            <pre class=\"raw-response-content\">{{ lastRawResponse }}</pre>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <el-drawer\n      title=\"数据详情\"\n      :visible.sync=\"dialogVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <div style=\"padding: 20px\" v-if=\"currentDatasetDetail\">\n        <h3>{{currentDatasetDetail.name}}</h3>\n        <el-divider></el-divider>\n        \n        <h4>字段信息</h4>\n        <el-table :data=\"datasetFields\" style=\"width: 100%\">\n          <el-table-column prop=\"name\" label=\"字段名称\"></el-table-column>\n          <el-table-column prop=\"type\" label=\"字段类型\"></el-table-column>\n          <el-table-column prop=\"groupType\" label=\"分组类型\"></el-table-column>\n        </el-table>\n        \n        <el-divider></el-divider>\n        \n        <h4>数据预览</h4>\n        <el-table :data=\"datasetData\" style=\"width: 100%\">\n          <el-table-column \n            v-for=\"field in datasetFields\" \n            :key=\"field.id\"\n            :prop=\"field.dataeaseName\"\n            :label=\"field.name\">\n          </el-table-column>\n        </el-table>\n      </div>\n    </el-drawer>\n    <el-drawer\n      title=\"数据集列表\"\n      :visible.sync=\"drawer\"\n      direction=\"rtl\"\n      size=\"45%\">\n      <div style=\"padding: 20px\">\n        <el-popover\n          placement=\"right\"\n          width=\"400\"\n          trigger=\"click\">\n        </el-popover>\n        <el-card \n          v-for=\"dataset in tables\" \n          :key=\"dataset.id\"\n          class=\"dataset-card\"\n          @click=\"showDatasetDetail(dataset)\"\n          style=\"margin-bottom: 15px; cursor: pointer;\">\n          <div @click=\"showDatasetDetail(dataset)\" slot=\"header\">\n            <span>{{dataset.name}}</span>\n          </div>\n          <div @click=\"showDatasetDetail(dataset)\" v-if=\"dataset.leaf === false\">\n            <span class=\"dataset-type\">类型: 目录</span>\n          </div>\n          <div @click=\"showDatasetDetail(dataset)\" v-else>\n            <span class=\"dataset-type\">类型: 数据集</span>\n          </div>\n        </el-card>\n        <el-drawer\n          title=\"我是里面的\"\n          :append-to-body=\"true\"\n          :before-close=\"handleClose\"\n          :visible.sync=\"innerDrawer\">\n           <el-table\n            :data=\"tableList\"\n            border\n            style=\"width: 100%\">\n            <el-table-column\n              prop=\"date\"\n              label=\"日期\"\n              width=\"180\">\n            </el-table-column>\n            <el-table-column\n              prop=\"name\"\n              label=\"姓名\"\n              width=\"180\">\n            </el-table-column>\n            <el-table-column\n              prop=\"address\"\n              label=\"地址\">\n            </el-table-column>\n          </el-table>\n        </el-drawer>\n\n      </div>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\n// import { dataApi } from './api/index.js'\nimport { v4 as uuidv4 } from 'uuid'\nimport axios from 'axios'\nimport ChartDisplay from './components/ChartDisplay.vue'\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas'\nimport { jsPDF } from 'jspdf'\nimport * as echarts from 'echarts'\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [\n        \n      ],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false, // 新增：控制原始响应弹出层\n      lastRawResponse: '', // 新增：存储最后收到的原始响应\n      drawer:false,    //抽屉展示\n      direction: 'rtl', //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      innerDrawer:false,\n      tableList:[]\n    }\n  },\n  \n  mounted() {\n    this.loadTables()\n    this.initMemoryId()\n    this.addWelcomeMessage()\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\n      \"帮我生成一个销售额柱状图\",\n      \"展示近六个月的销售趋势折线图\",\n      \"按照区域统计销售量并生成饼图\",\n      \"帮我做一个按产品类别的销量对比图\"\n    ]\n  },\n  updated() {\n    this.scrollToBottom()\n  },\n  methods: {\n    SelectDataList(){\n      this.loadTables()\n      this.drawer=true\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id')\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4())\n        localStorage.setItem('user_memory_id', storedMemoryId)\n      }\n      this.memoryId = storedMemoryId\n    },\n    \n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i]\n        number = number * 16 + (parseInt(hexValue, 16) || 0)\n      }\n      return number % 1000000\n    },\n    \n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      })\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight\n      }\n    },\n    \n    async loadTables() {\n       let res ={\n    \"code\": 0,\n    \"msg\": null,\n    \"data\": [\n        {\n            \"id\": \"0\",\n            \"name\": \"root\",\n            \"leaf\": false,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": [\n                {\n                    \"id\": \"1139987253200818176\",\n                    \"name\": \"ioio\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1122506616361586688\",\n                    \"name\": \"一测KingBaseDataSet\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119354708838977536\",\n                    \"name\": \"未命名数据EXCEl\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119347979212427264\",\n                    \"name\": \"测试EXCEL数据集\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119242779856343040\",\n                    \"name\": \"121213\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119240262607048704\",\n                    \"name\": \"12.89\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119235024886042624\",\n                    \"name\": \"1231\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119234081960366080\",\n                    \"name\": \"14\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1118282918146478080\",\n                    \"name\": \"测试45\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1118275224819863552\",\n                    \"name\": \"15\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1118263178069938176\",\n                    \"name\": \"测试5\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1118260462249709568\",\n                    \"name\": \"测试data为空\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117533961606795264\",\n                    \"name\": \"测试\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117529367518515200\",\n                    \"name\": \"354\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117520088862429184\",\n                    \"name\": \"test01\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117452714880667648\",\n                    \"name\": \"未命名数据集111111\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117129362492231680\",\n                    \"name\": \"未命名数据集1\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117068655105019904\",\n                    \"name\": \"333\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114644377738809344\",\n                    \"name\": \"排空数--修改版\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114627384671342592\",\n                    \"name\": \"23\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114626600391020544\",\n                    \"name\": \"35\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114622812997423104\",\n                    \"name\": \"测试222221\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114617616477065216\",\n                    \"name\": \"166611\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114612956722761728\",\n                    \"name\": \"1213\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114612359227379712\",\n                    \"name\": \"123\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114610791497207808\",\n                    \"name\": \"1212\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114301988100771840\",\n                    \"name\": \"铁路运费（不含税）\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114299647981129728\",\n                    \"name\": \"通信设备障碍总延时\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114299149395824640\",\n                    \"name\": \"大修行走公里数\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114295314858250240\",\n                    \"name\": \"剩余牌数\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114284027080216576\",\n                    \"name\": \"测试1\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114280404409520128\",\n                    \"name\": \"666\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114238649047846912\",\n                    \"name\": \"未命名数据集\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114237385908031488\",\n                    \"name\": \"112\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114226839552921600\",\n                    \"name\": \"测\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114226293651673088\",\n                    \"name\": \"ce1\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114225881611636736\",\n                    \"name\": \"11\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1113938332037025792\",\n                    \"name\": \"数据资产-战略决策\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1113185924126216192\",\n                    \"name\": \"未命名数据集111\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1113185517429723136\",\n                    \"name\": \"未命名数据集aa\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1111656017395060736\",\n                    \"name\": \"^数^据^集\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1077661410659536896\",\n                    \"name\": \"包神生产运营指标\",\n                    \"leaf\": false,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": [\n                        {\n                            \"id\": \"1104500774345510912\",\n                            \"name\": \"指标\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104499040399265792\",\n                            \"name\": \"BSPI环渤海动力煤指数\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104483680820269056\",\n                            \"name\": \"CECI价格指数\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104478833836429312\",\n                            \"name\": \"环渤海港口动态\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104478365903097856\",\n                            \"name\": \"未命名数据集\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104460252918714368\",\n                            \"name\": \"大物流运量完成情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1078683560736591872\",\n                            \"name\": \"陕煤集团煤矿成交价\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077711277742231552\",\n                            \"name\": \"当日分界口列车出入情况对比\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077670569941536768\",\n                            \"name\": \"分界口货车出入情况合计\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077669947737509888\",\n                            \"name\": \"分界口货车出入情况（当日）\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077668791040741376\",\n                            \"name\": \"月度运输收入\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077668450664583168\",\n                            \"name\": \"运输效益指标\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077668292577071104\",\n                            \"name\": \"专运车卸车情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077667982454427648\",\n                            \"name\": \"当日运输情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077667199050715136\",\n                            \"name\": \"分界口接入重车违流情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077666214211358720\",\n                            \"name\": \"卸车情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077661887262494720\",\n                            \"name\": \"装车情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        }\n                    ]\n                },\n                {\n                    \"id\": \"985189269226262528\",\n                    \"name\": \"示例\",\n                    \"leaf\": false,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": [\n                        {\n                            \"id\": \"985189053949415424\",\n                            \"name\": \"茶饮订单明细\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        }\n                    ]\n                }\n            ]\n        },\n        {\n            \"id\": \"0\",\n            \"name\": \"root\",\n            \"leaf\": false,\n            \"weight\": 7,\n            \"extraFlag\": 0,\n            \"type\": null,\n            \"children\": [\n                {\n                    \"id\": \"1139987253200818176\",\n                    \"name\": \"ioio\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1122506616361586688\",\n                    \"name\": \"一测KingBaseDataSet\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119354708838977536\",\n                    \"name\": \"未命名数据EXCEl\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119347979212427264\",\n                    \"name\": \"测试EXCEL数据集\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119242779856343040\",\n                    \"name\": \"121213\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119240262607048704\",\n                    \"name\": \"12.89\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119235024886042624\",\n                    \"name\": \"1231\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1119234081960366080\",\n                    \"name\": \"14\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1118282918146478080\",\n                    \"name\": \"测试45\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1118275224819863552\",\n                    \"name\": \"15\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1118263178069938176\",\n                    \"name\": \"测试5\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1118260462249709568\",\n                    \"name\": \"测试data为空\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117533961606795264\",\n                    \"name\": \"测试\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117529367518515200\",\n                    \"name\": \"354\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117520088862429184\",\n                    \"name\": \"test01\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117452714880667648\",\n                    \"name\": \"未命名数据集111111\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117129362492231680\",\n                    \"name\": \"未命名数据集1\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1117068655105019904\",\n                    \"name\": \"333\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114644377738809344\",\n                    \"name\": \"排空数--修改版\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114627384671342592\",\n                    \"name\": \"23\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114626600391020544\",\n                    \"name\": \"35\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114622812997423104\",\n                    \"name\": \"测试222221\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114617616477065216\",\n                    \"name\": \"166611\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114612956722761728\",\n                    \"name\": \"1213\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114612359227379712\",\n                    \"name\": \"123\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114610791497207808\",\n                    \"name\": \"1212\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114301988100771840\",\n                    \"name\": \"铁路运费（不含税）\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114299647981129728\",\n                    \"name\": \"通信设备障碍总延时\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114299149395824640\",\n                    \"name\": \"大修行走公里数\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114295314858250240\",\n                    \"name\": \"剩余牌数\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114284027080216576\",\n                    \"name\": \"测试1\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114280404409520128\",\n                    \"name\": \"666\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114238649047846912\",\n                    \"name\": \"未命名数据集\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114237385908031488\",\n                    \"name\": \"112\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114226839552921600\",\n                    \"name\": \"测\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114226293651673088\",\n                    \"name\": \"ce1\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1114225881611636736\",\n                    \"name\": \"11\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1113938332037025792\",\n                    \"name\": \"数据资产-战略决策\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1113185924126216192\",\n                    \"name\": \"未命名数据集111\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1113185517429723136\",\n                    \"name\": \"未命名数据集aa\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1111656017395060736\",\n                    \"name\": \"^数^据^集\",\n                    \"leaf\": true,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": null\n                },\n                {\n                    \"id\": \"1077661410659536896\",\n                    \"name\": \"包神生产运营指标\",\n                    \"leaf\": false,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": [\n                        {\n                            \"id\": \"1104500774345510912\",\n                            \"name\": \"指标\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104499040399265792\",\n                            \"name\": \"BSPI环渤海动力煤指数\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104483680820269056\",\n                            \"name\": \"CECI价格指数\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104478833836429312\",\n                            \"name\": \"环渤海港口动态\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104478365903097856\",\n                            \"name\": \"未命名数据集\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1104460252918714368\",\n                            \"name\": \"大物流运量完成情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1078683560736591872\",\n                            \"name\": \"陕煤集团煤矿成交价\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077711277742231552\",\n                            \"name\": \"当日分界口列车出入情况对比\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077670569941536768\",\n                            \"name\": \"分界口货车出入情况合计\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077669947737509888\",\n                            \"name\": \"分界口货车出入情况（当日）\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077668791040741376\",\n                            \"name\": \"月度运输收入\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077668450664583168\",\n                            \"name\": \"运输效益指标\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077668292577071104\",\n                            \"name\": \"专运车卸车情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077667982454427648\",\n                            \"name\": \"当日运输情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077667199050715136\",\n                            \"name\": \"分界口接入重车违流情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077666214211358720\",\n                            \"name\": \"卸车情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        },\n                        {\n                            \"id\": \"1077661887262494720\",\n                            \"name\": \"装车情况\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        }\n                    ]\n                },\n                {\n                    \"id\": \"985189269226262528\",\n                    \"name\": \"示例\",\n                    \"leaf\": false,\n                    \"weight\": 7,\n                    \"extraFlag\": 0,\n                    \"type\": null,\n                    \"children\": [\n                        {\n                            \"id\": \"985189053949415424\",\n                            \"name\": \"茶饮订单明细\",\n                            \"leaf\": true,\n                            \"weight\": 7,\n                            \"extraFlag\": 0,\n                            \"type\": null,\n                            \"children\": null\n                        }\n                    ]\n                }\n            ]\n        }\n    ]\n}\n      this.tables = this.flattenDatasets(res.data);\n      console.log(this.tables,\"=====================1438\");\n      \n\n      // try {\n      //   const response = await dataApi.getAllTables()\n      //   if (response.code === 0 && response.data) {\n      //     console.log(response.data)\n      //     this.tables = this.flattenDatasets(response.data);\n      //     console.log(this.table)\n      //   } else {\n      //     this.$message.error('加载数据表失败');\n      //   }\n      // } catch (error) {\n      //   this.$message.error('加载数据表失败')\n      //   console.error(error)\n      // }\n    },\n    \n    // 将树形结构扁平化为列表\n    flattenDatasets(datasets) {\n      let result = [];\n      \n      const traverse = (items) => {\n        if (!items || !items.length) return;\n        \n        items.forEach(item => {\n          result.push(item);\n          if (item.children && item.children.length) {\n            traverse(item.children);\n          }\n        });\n      };\n      \n      traverse(datasets);\n      return result;\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n    \n    // 显示数据集详情\n     showDatasetDetail() {\n      \n      this.innerDrawer=true\n      let res= [\n                {\n                    \"id\": \"1737102130516\",\n                    \"datasourceId\": \"1077661171546460160\",\n                    \"datasetTableId\": \"7285934957984223232\",\n                    \"datasetGroupId\": \"1077667199050715136\",\n                    \"chartId\": null,\n                    \"originName\": \"ds\",\n                    \"name\": \"吨数\",\n                    \"dbFieldName\": null,\n                    \"description\": \"吨数\",\n                    \"dataeaseName\": \"f_fc20d94b24c93ace\",\n                    \"groupType\": \"q\",\n                    \"type\": \"VARCHAR\",\n                    \"precision\": null,\n                    \"scale\": null,\n                    \"deType\": 2,\n                    \"deExtractType\": 0,\n                    \"extField\": 0,\n                    \"checked\": true,\n                    \"columnIndex\": null,\n                    \"lastSyncTime\": null,\n                    \"dateFormat\": \"\",\n                    \"dateFormatType\": null,\n                    \"fieldShortName\": \"f_fc20d94b24c93ace\",\n                    \"desensitized\": null,\n                    \"params\": null\n                },\n                {\n                    \"id\": \"1737102130517\",\n                    \"datasourceId\": \"1077661171546460160\",\n                    \"datasetTableId\": \"7285934957984223232\",\n                    \"datasetGroupId\": \"1077667199050715136\",\n                    \"chartId\": null,\n                    \"originName\": \"fjkmc\",\n                    \"name\": \"分界口名称\",\n                    \"dbFieldName\": null,\n                    \"description\": \"分界口名称\",\n                    \"dataeaseName\": \"f_891517b6c5e5d7e8\",\n                    \"groupType\": \"d\",\n                    \"type\": \"VARCHAR\",\n                    \"precision\": null,\n                    \"scale\": null,\n                    \"deType\": 0,\n                    \"deExtractType\": 0,\n                    \"extField\": 0,\n                    \"checked\": true,\n                    \"columnIndex\": null,\n                    \"lastSyncTime\": null,\n                    \"dateFormat\": null,\n                    \"dateFormatType\": null,\n                    \"fieldShortName\": \"f_891517b6c5e5d7e8\",\n                    \"desensitized\": null,\n                    \"params\": null\n                },\n                {\n                    \"id\": \"1737102130518\",\n                    \"datasourceId\": \"1077661171546460160\",\n                    \"datasetTableId\": \"7285934957984223232\",\n                    \"datasetGroupId\": \"1077667199050715136\",\n                    \"chartId\": null,\n                    \"originName\": \"id\",\n                    \"name\": \"id\",\n                    \"dbFieldName\": null,\n                    \"description\": \"id\",\n                    \"dataeaseName\": \"f_0a1541124bfd6804\",\n                    \"groupType\": \"d\",\n                    \"type\": \"INT\",\n                    \"precision\": null,\n                    \"scale\": null,\n                    \"deType\": 2,\n                    \"deExtractType\": 2,\n                    \"extField\": 0,\n                    \"checked\": true,\n                    \"columnIndex\": null,\n                    \"lastSyncTime\": null,\n                    \"dateFormat\": null,\n                    \"dateFormatType\": null,\n                    \"fieldShortName\": \"f_0a1541124bfd6804\",\n                    \"desensitized\": null,\n                    \"params\": null\n                },\n                {\n                    \"id\": \"1737102130519\",\n                    \"datasourceId\": \"1077661171546460160\",\n                    \"datasetTableId\": \"7285934957984223232\",\n                    \"datasetGroupId\": \"1077667199050715136\",\n                    \"chartId\": null,\n                    \"originName\": \"wlcs\",\n                    \"name\": \"违流车数\",\n                    \"dbFieldName\": null,\n                    \"description\": \"违流车数\",\n                    \"dataeaseName\": \"f_229c8ebf1eb8c51c\",\n                    \"groupType\": \"q\",\n                    \"type\": \"VARCHAR\",\n                    \"precision\": null,\n                    \"scale\": null,\n                    \"deType\": 2,\n                    \"deExtractType\": 0,\n                    \"extField\": 0,\n                    \"checked\": true,\n                    \"columnIndex\": null,\n                    \"lastSyncTime\": null,\n                    \"dateFormat\": \"\",\n                    \"dateFormatType\": null,\n                    \"fieldShortName\": \"f_229c8ebf1eb8c51c\",\n                    \"desensitized\": null,\n                    \"params\": null\n                },\n                {\n                    \"id\": \"1737102130520\",\n                    \"datasourceId\": \"1077661171546460160\",\n                    \"datasetTableId\": \"7285934957984223232\",\n                    \"datasetGroupId\": \"1077667199050715136\",\n                    \"chartId\": null,\n                    \"originName\": \"wlls\",\n                    \"name\": \"违流列数\",\n                    \"dbFieldName\": null,\n                    \"description\": \"违流列数\",\n                    \"dataeaseName\": \"f_e5f343591786fe02\",\n                    \"groupType\": \"q\",\n                    \"type\": \"VARCHAR\",\n                    \"precision\": null,\n                    \"scale\": null,\n                    \"deType\": 2,\n                    \"deExtractType\": 0,\n                    \"extField\": 0,\n                    \"checked\": true,\n                    \"columnIndex\": null,\n                    \"lastSyncTime\": null,\n                    \"dateFormat\": \"\",\n                    \"dateFormatType\": null,\n                    \"fieldShortName\": \"f_e5f343591786fe02\",\n                    \"desensitized\": null,\n                    \"params\": null\n                }\n            ]\n      this.tableList=res\n      // try {\n      //   // 只有叶子节点才是具体数据集\n      //   if (dataset.leaf) {\n      //     const response = await dataApi.getDatasetDetail(dataset.id);\n      //     this.currentDatasetDetail = response.data;\n          \n      //     // 提取字段信息\n      //     if (this.currentDatasetDetail && this.currentDatasetDetail.info && this.currentDatasetDetail.info.union) {\n      //       this.datasetFields = this.currentDatasetDetail.info.union;\n      //     } else if (this.currentDatasetDetail && this.currentDatasetDetail.union) {\n      //       this.datasetFields = this.currentDatasetDetail.union;\n      //     } else {\n      //       this.datasetFields = [];\n      //     }\n          \n      //     // 提取数据\n      //     if (this.currentDatasetDetail && this.currentDatasetDetail.data) {\n      //       this.datasetData = this.currentDatasetDetail.data;\n      //     } else {\n      //       this.datasetData = [];\n      //     }\n          \n      //     // 关闭数据集列表抽屉，打开数据详情抽屉\n      //     this.drawer = false;\n      //     this.dialogVisible = true;\n      //   } else {\n      //     this.$message.info('这是一个目录，请选择具体的数据集');\n      //   }\n      // } catch (error) {\n      //   console.error('获取数据集详情失败:', error);\n      //   this.$message.error('获取数据集详情失败');\n      // }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    \n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedTable ? \n        this.selectedTable.tableName : \n        (this.tables.length > 0 ? this.tables[0].tableName : \"未知数据集\")\n      \n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      }\n      this.messages.push(userMsg)\n      \n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      }\n      this.messages.push(botMsg)\n      \n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1]\n      \n      // 保存问题并清空输入框\n      const question = this.question\n      this.question = ''\n      this.isSending = true\n      \n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`\n        \n        // 发送请求\n        await axios.post(\n          'http://localhost:8088/api/indicator/chat',\n          { memoryId: this.memoryId, message },\n          {\n            responseType: 'stream',\n            onDownloadProgress: (e) => {\n              const fullText = e.event.target.responseText // 累积的完整文本\n              let newText = fullText.substring(lastMsg.content.length)\n              lastMsg.content += newText // 增量更新\n              this.scrollToBottom() // 实时滚动\n              \n              // 保存原始响应\n              this.lastRawResponse = fullText\n            }\n          }\n        )\n        \n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false\n        this.isSending = false\n        \n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error)\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'\n        lastMsg.isTyping = false\n        this.isSending = false\n      }\n    },\n    \n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n      \n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n        \n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n      \n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        \n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n          \n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n          \n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n            \n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, \n              '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            \n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      \n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n      \n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        \n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n          \n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          \n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            \n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n              \n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], \n                  '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n      \n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || \n          message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n        \n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        \n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    \n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        \n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n          \n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n          \n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n          \n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n          \n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    \n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n      \n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      \n      this.messages.push(botMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n      \n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n      \n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [\n            { field: '类别1', value: 100 },\n            { field: '类别2', value: 200 },\n            { field: '类别3', value: 150 }\n          ],\n          metrics: [{ name: '数值' }]\n        }\n      };\n      \n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      \n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      \n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    \n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n      \n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [\n              {value: 1, field: \"神朔\", name: \"神朔\", category: \"记录数*\"},\n              {value: 1, field: \"甘泉\", name: \"甘泉\", category: \"记录数*\"},\n              {value: 1, field: \"包神\", name: \"包神\", category: \"记录数*\"}\n            ],\n            fields: [\n              {id: \"1746787308487\", name: \"名称\", groupType: \"d\"},\n              {id: \"-1\", name: \"记录数*\", groupType: \"q\"}\n            ]\n          }\n        }\n      };\n      \n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n      \n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n      \n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      \n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      \n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n        \n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n        \n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n        \n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content\n          .replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '')\n          .replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '')\n          .trim();\n          \n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n        \n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n        \n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n        \n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n        \n        // 根据图表类型选择对应的处理函数\n        switch(chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n        \n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n        \n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        \n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n        \n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2, // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n        \n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = (canvas.height * imgWidth) / canvas.width;\n        \n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n        \n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n        \n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n        \n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n        \n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n        \n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    \n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n      \n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n      \n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n      \n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n          \n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n          \n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n          \n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      \n      return options;\n    },\n    \n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      \n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n      \n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        \n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n      \n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n      \n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n      \n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n        \n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n        \n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        \n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n      \n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.app-layout {\n  display: flex;\n  height: 100vh; /* 占满整个视口高度 */\n  overflow: hidden;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  background-color: #f9f9f9;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 25px;\n  text-align: center;\n  white-space: nowrap;\n  letter-spacing: 2px;\n  font-weight: 900;\n}\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  /* overflow-x: auto; */\n  display: flex;\n  flex-direction: column; /* 垂直排列子元素 */\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n  height: 100vh; /* 占满整个视口高度 */\n  box-sizing: border-box; /* 包含padding和border */\n}\n\n.header {\n  margin-bottom: 20px;\n  width: 100%; /* 撑满父容器宽度 */\n   text-align: center; /* 让文本内容居中 */\n}\n\n.header h2 {\n  margin: 0;\n  font-size: 30px;\n}\n\n.highlight {\n  color: #409eff;\n}\n\n.sub-title {\n  color: #606266;\n  font-size: 14px;\n  margin: 5px 0 0 0;\n}\n\n.data-card {\n  cursor: pointer;\n  margin-bottom: 15px;\n  transition: all 0.3s;\n}\n\n.data-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.data-header {\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.sample-tag {\n  background-color: #ecf5ff;\n  color: #409eff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n.data-fields {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.field-item {\n  background-color: #f5f7fa;\n  padding: 2px 8px;\n  margin: 4px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.multi-line-input .el-input__inner {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  height: auto !important;\n  line-height: inherit;\n  padding: 6px 10px;\n  min-height: 40px;\n}\n\n/* 底部问题输入区域（关键修改） */\n.question-input-container {\n  /* 移除固定定位 */\n  /* position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%; */\n  \n  width: 100%;\n    /* background-color: #fff; */\n    /* border-top: 1px solid #ebeef5; */\n    padding: 25px 15px;\n    /* box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05); */\n    display: flex\n;\n    flex-direction: column;\n    align-items: center;\n    margin-top: 20px;\n}\n\n.question-input-container > span {\n  margin-bottom: 12px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.question-input-wrapper {\n  align-items: center;\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n  background-color: #f5f7fa;\n  border-radius: 20px;\n  padding: 8px 15px;\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n  float: right;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: #fff;\n  border: 1px solid #dcdfe6;\n  margin-left: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n}\n\n.send-btn {\n  background-color: #409eff;\n  color: white;\n  border: none;\n}\n\n.test-btn {\n  background-color: #67c23a;\n  color: white;\n  border: none;\n}\n\n.debug-btn {\n  background-color: #909399;\n  color: white;\n  border: none;\n}\n\n.send-btn:disabled {\n  background-color: #c0c4cc;\n  cursor: not-allowed;\n}\n\n/* Suggested Questions Panel */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n\n/* Message list style */\n.message-list {\n  width: 100%;\n  max-height: calc(100vh - 250px); /* 修改为响应式高度，减去头部和底部的高度 */\n  overflow-y: auto;\n  margin-top: 20px;\n  padding: 10px;\n  background-color: transparent;\n  border-radius: 8px;\n  flex: 1; /* 让消息列表占据剩余空间 */\n}\n\n.message {\n  margin-bottom: 20px;\n  display: flex;\n  position: relative;\n  max-width: 80%;\n  margin-left: 150px;\n}\n\n.message-content {\n  padding: 12px 15px;\n  border-radius: 6px;\n  line-height: 1.5;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n  align-self: flex-end;\n  margin-left: auto;\n}\n\n.user-message .message-content {\n  background-color: #ecf5ff;\n  margin-right: 10px;\n}\n\n.bot-message {\n  align-self: flex-start;\n}\n\n.bot-message .message-content {\n  background-color: #f5f7fa;\n  margin-left: 10px;\n}\n\n.avatar-container {\n  display: flex;\n  align-items: flex-start;\n}\n\n.bot-avatar, .user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bot-avatar {\n  background-color: #4caf50;\n  color: white;\n}\n\n.user-avatar {\n  background-color: #2196f3;\n  color: white;\n}\n\n/* Loading animation */\n.loading-dots {\n  display: inline-block;\n}\n\n.dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  background-color: #999;\n  border-radius: 50%;\n  margin: 0 2px;\n  animation: pulse 1.2s infinite ease-in-out both;\n}\n\n.dot:nth-child(2) {\n  animation-delay: -0.4s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: -0.8s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(0.6);\n    opacity: 0.4;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* Styles related to charts */\n.chart-container {\n  position: relative;\n  margin-top: 15px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 10px;\n  background-color: #fff;\n}\n\n/* 确保图表容器有足够高度 */\n.chart-canvas {\n  min-height: 300px;\n}\n\n.chart-notice {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n  font-weight: bold;\n}\n\n/* AI original response popup layer style */\n.raw-response-panel {\n  position: fixed;\n  bottom: 100px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 800px;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n  z-index: 1000;\n  max-height: 60%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.raw-response-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 16px;\n  color: #303133;\n  background-color: #f5f7fa;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.raw-response-title .el-icon-monitor {\n  margin-right: 8px;\n}\n\n.raw-response-title .close-btn {\n  background-color: #f5f7fa;\n  border: 1px solid #ebeef5;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.raw-response-title .close-btn:hover {\n  background-color: #ebeef5;\n}\n\n.raw-response-content {\n  flex: 1;\n  padding: 15px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #303133;\n  white-space: pre-wrap;\n  word-break: break-all;\n  overflow-wrap: break-word;\n}\n\n/* 添加PDF导出相关样式 */\n.chart-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-bottom: 10px;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;AA+OA;AACA,SAAAA,EAAA,IAAAC,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,YAAA;AACA;AACA,OAAAC,WAAA;AACA,SAAAC,KAAA;AACA,YAAAC,OAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA,IAEA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;MACAC,oBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,MAAA;MAAA;MACAC,SAAA;MAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,WAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,UAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;IACA;IACA,KAAApB,kBAAA,IACA,gBACA,kBACA,kBACA,mBACA;EACA;EACAqB,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,eAAA;MACA,KAAAN,UAAA;MACA,KAAAR,MAAA;IACA;IACA;IACAS,aAAA;MACA,IAAAM,cAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,KAAAF,cAAA;QACAA,cAAA,QAAAG,YAAA,CAAA5C,MAAA;QACA0C,YAAA,CAAAG,OAAA,mBAAAJ,cAAA;MACA;MACA,KAAApB,QAAA,GAAAoB,cAAA;IACA;IAEA;IACAG,aAAAE,IAAA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAG,MAAA,IAAAD,CAAA,MAAAA,CAAA;QACA,MAAAE,QAAA,GAAAJ,IAAA,CAAAE,CAAA;QACAD,MAAA,GAAAA,MAAA,SAAAI,QAAA,CAAAD,QAAA;MACA;MACA,OAAAH,MAAA;IACA;IAEA;IACAX,kBAAA;MACA,KAAAhB,QAAA,CAAAgC,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IAEA;IACAjB,eAAA;MACA,SAAAkB,KAAA,CAAAjC,cAAA;QACA,KAAAiC,KAAA,CAAAjC,cAAA,CAAAkC,SAAA,QAAAD,KAAA,CAAAjC,cAAA,CAAAmC,YAAA;MACA;IACA;IAEA,MAAAxB,WAAA;MACA,IAAAyB,GAAA;QACA;QACA;QACA,SACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,aACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,aACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;UAEA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,aACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;UAEA;QAEA,GACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,aACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,aACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA,GACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;UAEA,GACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,aACA;cACA;cACA;cACA;cACA;cACA;cACA;cACA;YACA;UAEA;QAEA;MAEA;MACA,KAAAhD,MAAA,QAAAiD,eAAA,CAAAD,GAAA,CAAAnD,IAAA;MACAqD,OAAA,CAAAC,GAAA,MAAAnD,MAAA;;MAGA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAiD,gBAAAG,QAAA;MACA,IAAAC,MAAA;MAEA,MAAAC,QAAA,GAAAC,KAAA;QACA,KAAAA,KAAA,KAAAA,KAAA,CAAAjB,MAAA;QAEAiB,KAAA,CAAAC,OAAA,CAAAC,IAAA;UACAJ,MAAA,CAAAZ,IAAA,CAAAgB,IAAA;UACA,IAAAA,IAAA,CAAAC,QAAA,IAAAD,IAAA,CAAAC,QAAA,CAAApB,MAAA;YACAgB,QAAA,CAAAG,IAAA,CAAAC,QAAA;UACA;QACA;MACA;MAEAJ,QAAA,CAAAF,QAAA;MACA,OAAAC,MAAA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAM,kBAAA;MAEA,KAAAvC,WAAA;MACA,IAAA4B,GAAA,IACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,GACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA,EACA;MACA,KAAA3B,SAAA,GAAA2B,GAAA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAY,gBAAA;MACA,KAAAxD,oBAAA,SAAAA,oBAAA;IACA;IACAyD,YAAAC,CAAA;MACA,KAAA5D,QAAA,GAAA4D,CAAA;MACA,KAAA1D,oBAAA;IACA;IACA2D,eAAAC,KAAA;MACA,IAAAA,KAAA,CAAAC,SAAA;QACA;MACA;MACA;IACA;IAEA;IACA,MAAAC,eAAA;MACA,UAAAhE,QAAA,CAAAiE,IAAA,WAAAxD,SAAA;QACA,KAAAyD,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAC,cAAA,QAAArE,aAAA,GACA,KAAAA,aAAA,CAAAsE,SAAA,GACA,KAAAvE,MAAA,CAAAsC,MAAA,YAAAtC,MAAA,IAAAuE,SAAA;;MAEA;MACA,MAAAC,OAAA;QACA9B,MAAA;QACAC,OAAA,OAAAzC,QAAA,CAAAiE,IAAA;QACAvB,QAAA;MACA;MACA,KAAAnC,QAAA,CAAAgC,IAAA,CAAA+B,OAAA;;MAEA;MACA,MAAAC,MAAA;QACA/B,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAAnC,QAAA,CAAAgC,IAAA,CAAAgC,MAAA;;MAEA;MACA,MAAAC,OAAA,QAAAjE,QAAA,MAAAA,QAAA,CAAA6B,MAAA;;MAEA;MACA,MAAApC,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA;MACA,KAAAS,SAAA;MAEA;QACA;QACA,MAAAgE,OAAA,MAAAzE,QAAA,WAAAoE,cAAA;;QAEA;QACA,MAAAhF,KAAA,CAAAsF,IAAA,CACA,4CACA;UAAAlE,QAAA,OAAAA,QAAA;UAAAiE;QAAA,GACA;UACAE,YAAA;UACAC,kBAAA,EAAAC,CAAA;YACA,MAAAC,QAAA,GAAAD,CAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAC,YAAA;YACA,IAAAC,OAAA,GAAAJ,QAAA,CAAAK,SAAA,CAAAX,OAAA,CAAA/B,OAAA,CAAAL,MAAA;YACAoC,OAAA,CAAA/B,OAAA,IAAAyC,OAAA;YACA,KAAAzD,cAAA;;YAEA;YACA,KAAAb,eAAA,GAAAkE,QAAA;UACA;QACA,CACA;;QAEA;QACAN,OAAA,CAAA9B,QAAA;QACA,KAAAjC,SAAA;;QAEA;QACA,KAAA2E,gBAAA,CAAAZ,OAAA;MACA,SAAAa,KAAA;QACArC,OAAA,CAAAqC,KAAA,UAAAA,KAAA;QACAb,OAAA,CAAA/B,OAAA;QACA+B,OAAA,CAAA9B,QAAA;QACA,KAAAjC,SAAA;MACA;IACA;IAEA;IACA2E,iBAAAX,OAAA;MACAzB,OAAA,CAAAC,GAAA,qBAAAwB,OAAA,CAAAhC,OAAA;;MAEA;MACA,MAAA6C,gBAAA,GAAAb,OAAA,CAAAhC,OAAA,CAAA8C,KAAA;MACA,IAAAD,gBAAA,IAAAA,gBAAA;QACA,MAAAE,WAAA,GAAAF,gBAAA;QACAtC,OAAA,CAAAC,GAAA,cAAAuC,WAAA;;QAEA;QACA,KAAAC,kBAAA,CAAAD,WAAA,EAAAf,OAAA;QACA;MACA;;MAEA;MACA,MAAAiB,gBAAA,GAAAjB,OAAA,CAAAhC,OAAA,CAAA8C,KAAA;MACA,IAAAG,gBAAA,IAAAA,gBAAA;QACA1C,OAAA,CAAAC,GAAA,eAAAyC,gBAAA;QAEA;UACA,IAAAC,WAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,gBAAA;UACA1C,OAAA,CAAAC,GAAA,gBAAA0C,WAAA;;UAEA;UACA,IAAAA,WAAA,CAAAG,IAAA,UAAAH,WAAA,CAAAhG,IAAA;YACAqD,OAAA,CAAAC,GAAA,yBAAA0C,WAAA,CAAAhG,IAAA;YACAgG,WAAA,GAAAA,WAAA,CAAAhG,IAAA;UACA;;UAEA;UACA,IAAAgG,WAAA,CAAAI,IAAA,KAAAJ,WAAA,CAAAK,SAAA,IAAAL,WAAA,CAAAM,OAAA,IAAAN,WAAA,CAAAhG,IAAA;YACAqD,OAAA,CAAAC,GAAA,eAAA0C,WAAA;YACAlB,OAAA,CAAAkB,WAAA,GAAAA,WAAA;;YAEA;YACAlB,OAAA,CAAAhC,OAAA,GAAAgC,OAAA,CAAAhC,OAAA,CAAAyD,OAAA,6BACA;YAEA;UACA;YACAlD,OAAA,CAAAC,GAAA,gBAAA0C,WAAA;UACA;QACA,SAAAN,KAAA;UACArC,OAAA,CAAAqC,KAAA,cAAAA,KAAA;QACA;MACA;MAEArC,OAAA,CAAAC,GAAA;;MAEA;MACA;QACA;QACA,MAAAkD,SAAA;QACA,MAAAC,SAAA,GAAA3B,OAAA,CAAAhC,OAAA,CAAA8C,KAAA,CAAAY,SAAA;QAEA,IAAAC,SAAA;UACApD,OAAA,CAAAC,GAAA,kBAAAmD,SAAA;;UAEA;UACA;UACA,MAAAC,OAAA,GAAAD,SAAA,IAAAF,OAAA;UACAlD,OAAA,CAAAC,GAAA,iBAAAoD,OAAA;UAEA;YACA,MAAAV,WAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAQ,OAAA;YACArD,OAAA,CAAAC,GAAA,gBAAA0C,WAAA;YAEA,IAAAA,WAAA,CAAAG,IAAA,UAAAH,WAAA,CAAAhG,IAAA;cACA,MAAAA,IAAA,GAAAgG,WAAA,CAAAhG,IAAA;cACAqD,OAAA,CAAAC,GAAA,aAAAtD,IAAA;;cAEA;cACA,IAAAA,IAAA,CAAAoG,IAAA,KAAApG,IAAA,CAAAsG,OAAA,IAAAtG,IAAA,CAAAA,IAAA;gBACAqD,OAAA,CAAAC,GAAA,eAAAtD,IAAA;gBACA8E,OAAA,CAAAkB,WAAA,GAAAhG,IAAA;gBACA8E,OAAA,CAAAhC,OAAA,GAAAgC,OAAA,CAAAhC,OAAA,CAAAyD,OAAA,CAAAE,SAAA,KACA;gBACA;cACA;YACA;UACA,SAAAE,UAAA;YACAtD,OAAA,CAAAC,GAAA,cAAAqD,UAAA;UACA;QACA;MACA,SAAAjB,KAAA;QACArC,OAAA,CAAAC,GAAA,aAAAoC,KAAA;MACA;;MAEA;MACA,IAAAZ,OAAA,CAAAhC,OAAA,CAAA8D,QAAA,iBACA9B,OAAA,CAAAhC,OAAA,CAAA8D,QAAA;QACAvD,OAAA,CAAAC,GAAA;;QAEA;QACA,MAAAuD,aAAA;UACAC,EAAA,EAAAC,IAAA,CAAAC,GAAA,GAAAC,QAAA;UACAb,IAAA;UACAc,KAAA;UACAZ,OAAA,OAAAlG,aAAA,EAAA0G,EAAA;QACA;QAEAzD,OAAA,CAAAC,GAAA,eAAAuD,aAAA;QACA/B,OAAA,CAAAkB,WAAA,GAAAa,aAAA;MACA;IACA;IAEA;IACA,MAAAf,mBAAAD,WAAA,EAAAf,OAAA;MACA;QACAzB,OAAA,CAAAC,GAAA,qBAAAuC,WAAA;QACA,MAAAsB,QAAA,SAAA1H,KAAA,CAAA2H,GAAA,yCAAAvB,WAAA;QAEA,IAAAsB,QAAA,CAAAnH,IAAA,IAAAmH,QAAA,CAAAE,MAAA;UACAhE,OAAA,CAAAC,GAAA,cAAA6D,QAAA,CAAAnH,IAAA;;UAEA;UACA,IAAAgG,WAAA,GAAAmB,QAAA,CAAAnH,IAAA;UACA,IAAAgG,WAAA,CAAAG,IAAA,UAAAH,WAAA,CAAAhG,IAAA;YACAgG,WAAA,GAAAA,WAAA,CAAAhG,IAAA;UACA;;UAEA;UACA8E,OAAA,CAAAkB,WAAA,GAAAA,WAAA;;UAEA;UACA,KAAAlB,OAAA,CAAAhC,OAAA,CAAA8D,QAAA;YACA9B,OAAA,CAAAhC,OAAA;UACA;;UAEA;UACA,KAAAwE,YAAA;QACA;UACAjE,OAAA,CAAAqC,KAAA,cAAAyB,QAAA;UACArC,OAAA,CAAAhC,OAAA;QACA;MACA,SAAA4C,KAAA;QACArC,OAAA,CAAAqC,KAAA,cAAAA,KAAA;QACAZ,OAAA,CAAAhC,OAAA,0CAAA4C,KAAA,CAAAZ,OAAA;MACA;IACA;IAEA;IACAyC,UAAA;MACA;MACA,MAAAC,eAAA;QACAV,EAAA;QACAV,IAAA;QACAc,KAAA;QACAb,SAAA;QACAC,OAAA;MACA;;MAEA;MACA,MAAA1B,MAAA;QACA/B,MAAA;QACAC,OAAA;QACAC,QAAA;QACAiD,WAAA,EAAAwB;MACA;MAEA,KAAA5G,QAAA,CAAAgC,IAAA,CAAAgC,MAAA;;MAEA;MACA,KAAA3D,eAAA,GAAAgF,IAAA,CAAAwB,SAAA,CAAAD,eAAA;;MAEA;MACAnE,OAAA,CAAAC,GAAA,iBAAA1C,QAAA;;MAEA;MACA,MAAA8G,eAAA;QACAvB,IAAA;QACAwB,GAAA;QACA3H,IAAA;UACAoG,IAAA;UACApG,IAAA,GACA;YAAA4H,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,EACA;UACAC,OAAA;YAAAhI,IAAA;UAAA;QACA;MACA;;MAEA;MACA,MAAAiI,WAAA,iBAAA9B,IAAA,CAAAwB,SAAA,CAAAC,eAAA;MACA,MAAAM,cAAA;QACAnF,MAAA;QACAC,OAAA,gBAAAiF,WAAA;QACAhF,QAAA;MACA;MAEA,KAAAnC,QAAA,CAAAgC,IAAA,CAAAoF,cAAA;MACA,KAAAvC,gBAAA,CAAAuC,cAAA;;MAEA;MACA,KAAA/G,eAAA,GAAA8G,WAAA;MAEA1E,OAAA,CAAAC,GAAA,iBAAA0E,cAAA;IACA;IAEA;IACAC,aAAA;MACA5E,OAAA,CAAAC,GAAA;;MAEA;MACA,MAAA4E,QAAA;QACA/B,IAAA;QACAwB,GAAA;QACA3H,IAAA;UACA8G,EAAA;UACAI,KAAA;UACAZ,OAAA;UACAF,IAAA;UACApG,IAAA;YACAA,IAAA,GACA;cAAA6H,KAAA;cAAAD,KAAA;cAAA9H,IAAA;cAAAqI,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAD,KAAA;cAAA9H,IAAA;cAAAqI,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAD,KAAA;cAAA9H,IAAA;cAAAqI,QAAA;YAAA,EACA;YACAC,MAAA,GACA;cAAAtB,EAAA;cAAAhH,IAAA;cAAAuI,SAAA;YAAA,GACA;cAAAvB,EAAA;cAAAhH,IAAA;cAAAuI,SAAA;YAAA;UAEA;QACA;MACA;;MAEA;MACA,MAAAC,WAAA;QACAzF,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MAEA;MACAuF,WAAA,CAAAtC,WAAA,GAAAkC,QAAA,CAAAlI,IAAA;;MAEA;MACA,KAAAY,QAAA,CAAAgC,IAAA,CAAA0F,WAAA;;MAEA;MACA,KAAArH,eAAA,GAAAgF,IAAA,CAAAwB,SAAA,CAAAS,QAAA;MAEA7E,OAAA,CAAAC,GAAA,gBAAAgF,WAAA;IACA;IAEA;IACAC,gBAAA;MACA,KAAAvH,oBAAA;MACA,KAAAC,eAAA,QAAAL,QAAA,MAAAA,QAAA,CAAA6B,MAAA,MAAAK,OAAA;IACA;IAEA;IACA,MAAA0F,YAAA1D,OAAA;MACA,KAAAA,OAAA,CAAAkB,WAAA;MAEA;QACA;QACA,KAAAyC,IAAA,CAAA3D,OAAA;;QAEA;QACA,MAAA4D,aAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,aAAA,CAAAG,KAAA,CAAAC,QAAA;QACAJ,aAAA,CAAAG,KAAA,CAAAE,IAAA;QACAL,aAAA,CAAAG,KAAA,CAAAG,KAAA;QACAN,aAAA,CAAAG,KAAA,CAAAI,UAAA;QACAP,aAAA,CAAAG,KAAA,CAAAK,OAAA;QACAP,QAAA,CAAAQ,IAAA,CAAAC,WAAA,CAAAV,aAAA;;QAEA;QACA;QACA,MAAAxB,KAAA,GAAApC,OAAA,CAAAkB,WAAA,CAAAkB,KAAA;QACA,MAAAmC,WAAA,QAAAjJ,aAAA,QAAAA,aAAA,CAAAsE,SAAA;QACA,MAAA4E,WAAA,OAAAvC,IAAA,GAAAwC,cAAA;;QAEA;QACA,IAAAtJ,WAAA,GAAA6E,OAAA,CAAAhC,OAAA,CACAyD,OAAA,6CACAA,OAAA,8CACAjC,IAAA;;QAEA;QACA,IAAArE,WAAA,CAAA2G,QAAA;UACA,MAAA4C,OAAA,GAAAb,QAAA,CAAAC,aAAA;UACAY,OAAA,CAAAC,SAAA,GAAAxJ,WAAA;UACAA,WAAA,GAAAuJ,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;QACA;;QAEA;QACAjB,aAAA,CAAAe,SAAA;AACA;AACA,2DAAAvC,KAAA;AACA,8DAAAmC,WAAA;AACA,+DAAAC,WAAA;AACA;;AAEA;AACA;AACA,mBAAArJ,WAAA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;QAEA;QACA,MAAA2J,cAAA,GAAAlB,aAAA,CAAAmB,aAAA;QACA,MAAAC,KAAA,GAAAjK,OAAA,CAAAkK,IAAA,CAAAH,cAAA;;QAEA;QACA,MAAA5D,WAAA,GAAAlB,OAAA,CAAAkB,WAAA;QACA,IAAAgE,OAAA;;QAEA;QACA,QAAAhE,WAAA,CAAAI,IAAA;UACA;YACA4D,OAAA,QAAAC,kBAAA,CAAAjE,WAAA;YACA;UACA;YACAgE,OAAA,QAAAE,mBAAA,CAAAlE,WAAA;YACA;UACA;YACAgE,OAAA,QAAAG,kBAAA,CAAAnE,WAAA;YACA;UACA;YACAgE,OAAA,QAAAI,uBAAA,CAAApE,WAAA;YACA;UACA;YACAgE,OAAA,QAAAK,iBAAA,CAAArE,WAAA;QACA;;QAEA;QACA8D,KAAA,CAAAQ,SAAA,CAAAN,OAAA;QACA3G,OAAA,CAAAC,GAAA,mBAAA0G,OAAA;;QAEA;QACA,UAAAO,OAAA,CAAAC,OAAA,IAAAC,UAAA,CAAAD,OAAA;;QAEA;QACA,MAAAE,kBAAA,GAAAhC,aAAA,CAAAmB,aAAA;QACA,KAAAc,eAAA,CAAAD,kBAAA,EAAA5F,OAAA,CAAAkB,WAAA;;QAEA;QACA,MAAA4E,MAAA,SAAAjL,WAAA,CAAA+I,aAAA;UACAmC,KAAA;UAAA;UACAC,OAAA;UACAC,UAAA;UACAC,eAAA;QACA;;QAEA;QACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;QACA,MAAAC,GAAA,OAAAvL,KAAA;UACAwL,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,MAAAC,QAAA;QACA,MAAAC,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAA5B,KAAA;;QAEA;QACAmC,GAAA,CAAAO,QAAA,CAAAT,OAAA,iBAAAM,QAAA,EAAAC,SAAA;;QAEA;QACAL,GAAA,CAAAQ,IAAA,IAAAzE,KAAA,QAAAH,IAAA,GAAA6E,OAAA;;QAEA;QACAjD,QAAA,CAAAQ,IAAA,CAAA0C,WAAA,CAAAnD,aAAA;QACAoB,KAAA,CAAAgC,OAAA;;QAEA;QACA,KAAArD,IAAA,CAAA3D,OAAA;;QAEA;QACA,KAAAP,QAAA,CAAAwH,OAAA;MAEA,SAAArG,KAAA;QACArC,OAAA,CAAAqC,KAAA,aAAAA,KAAA;QACA,KAAA+C,IAAA,CAAA3D,OAAA;QACA,KAAAP,QAAA,CAAAmB,KAAA,eAAAA,KAAA,CAAAZ,OAAA;MACA;IACA;IAEA;IACAkH,wBAAAhG,WAAA;MACA,MAAAI,IAAA,GAAAJ,WAAA,CAAAI,IAAA;MACA,IAAApG,IAAA;MACA,IAAAiM,UAAA;;MAEA;MACA,IAAAjG,WAAA,CAAAhG,IAAA,IAAAgG,WAAA,CAAAhG,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgG,WAAA,CAAAhG,IAAA,CAAAA,IAAA;QACAiM,UAAA,GAAAjM,IAAA,CAAAkM,GAAA,CAAAtI,IAAA,IAAAA,IAAA,CAAAgE,KAAA,IAAAhE,IAAA,CAAA9D,IAAA;MACA;;MAEA;MACA,MAAAkK,OAAA;QACA9C,KAAA;UACAiF,IAAA,EAAAnG,WAAA,CAAAkB,KAAA;QACA;QACAkF,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAlG,IAAA;UACApG,IAAA,EAAAiM;QACA;QACAM,KAAA;UACAnG,IAAA;QACA;QACAoG,MAAA;MACA;;MAEA;MACA,IAAAxM,IAAA,CAAAyC,MAAA;QACA,IAAAzC,IAAA,IAAAwM,MAAA;UACA;UACA,MAAAC,SAAA;;UAEA;UACAzM,IAAA,CAAA2D,OAAA,CAAAC,IAAA;YACA,IAAAA,IAAA,CAAA4I,MAAA;cACA5I,IAAA,CAAA4I,MAAA,CAAA7I,OAAA,CAAA+I,CAAA;gBACA,KAAAD,SAAA,CAAAC,CAAA,CAAAvE,QAAA;kBACAsE,SAAA,CAAAC,CAAA,CAAAvE,QAAA;oBACArI,IAAA,EAAA4M,CAAA,CAAAvE,QAAA;oBACA/B,IAAA,EAAAA,IAAA;oBACApG,IAAA,EAAA2M,KAAA,CAAAV,UAAA,CAAAxJ,MAAA,EAAAmK,IAAA;kBACA;gBACA;cACA;YACA;UACA;;UAEA;UACA5M,IAAA,CAAA2D,OAAA,EAAAC,IAAA,EAAAiJ,KAAA;YACA,IAAAjJ,IAAA,CAAA4I,MAAA;cACA5I,IAAA,CAAA4I,MAAA,CAAA7I,OAAA,CAAA+I,CAAA;gBACAD,SAAA,CAAAC,CAAA,CAAAvE,QAAA,EAAAnI,IAAA,CAAA6M,KAAA,IAAAH,CAAA,CAAA7E,KAAA;cACA;YACA;UACA;;UAEA;UACAmC,OAAA,CAAAwC,MAAA,GAAAM,MAAA,CAAAC,MAAA,CAAAN,SAAA;QACA;UACA;UACAzC,OAAA,CAAAwC,MAAA,CAAA5J,IAAA;YACA9C,IAAA;YACAsG,IAAA,EAAAA,IAAA;YACApG,IAAA,EAAAA,IAAA,CAAAkM,GAAA,CAAAtI,IAAA,IAAAA,IAAA,CAAAiE,KAAA;UACA;QACA;MACA;MAEA,OAAAmC,OAAA;IACA;IAEA;IACA;IACAC,mBAAA+C,SAAA;MACA3J,OAAA,CAAAC,GAAA,gBAAA0J,SAAA;;MAEA;MACA,IAAAhN,IAAA;MACA,IAAA8H,OAAA;;MAEA;MACA,IAAAkF,SAAA,CAAAhN,IAAA,IAAA2M,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAAhN,IAAA;QACAA,IAAA,GAAAgN,SAAA,CAAAhN,IAAA;QACA8H,OAAA,GAAAkF,SAAA,CAAAlF,OAAA;MACA;MACA;MAAA,KACA,IAAAkF,SAAA,CAAAhN,IAAA,IAAAgN,SAAA,CAAAhN,IAAA,CAAAA,IAAA,IAAA2M,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAAhN,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgN,SAAA,CAAAhN,IAAA,CAAAA,IAAA;QACA8H,OAAA,GAAAkF,SAAA,CAAAhN,IAAA,CAAAoI,MAAA,GACA4E,SAAA,CAAAhN,IAAA,CAAAoI,MAAA,CAAA8E,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA9E,SAAA;MACA;MAEAhF,OAAA,CAAAC,GAAA,YAAAtD,IAAA;MACAqD,OAAA,CAAAC,GAAA,QAAAwE,OAAA;;MAEA;MACA,MAAAsF,SAAA,GAAApN,IAAA,CAAAkM,GAAA,CAAAtI,IAAA,IAAAA,IAAA,CAAAgE,KAAA,IAAAhE,IAAA,CAAA9D,IAAA;;MAEA;MACA,MAAA0M,MAAA;MACA,IAAAxM,IAAA,CAAAyC,MAAA,QAAAzC,IAAA,IAAAwM,MAAA;QACA;QACA,MAAAa,aAAA,OAAAC,GAAA;QACAtN,IAAA,CAAA2D,OAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAA4I,MAAA;YACA5I,IAAA,CAAA4I,MAAA,CAAA7I,OAAA,CAAA+I,CAAA,IAAAW,aAAA,CAAAE,GAAA,CAAAb,CAAA,CAAAvE,QAAA;UACA;QACA;QAEA,MAAA8D,UAAA,GAAAU,KAAA,CAAAa,IAAA,CAAAH,aAAA;QACApB,UAAA,CAAAtI,OAAA,CAAAwE,QAAA;UACA,MAAAsF,UAAA,GAAAzN,IAAA,CAAAkM,GAAA,CAAAtI,IAAA;YACA,MAAA4I,MAAA,GAAA5I,IAAA,CAAA4I,MAAA,EAAAkB,IAAA,CAAAhB,CAAA,IAAAA,CAAA,CAAAvE,QAAA,KAAAA,QAAA;YACA,OAAAqE,MAAA,GAAAA,MAAA,CAAA3E,KAAA;UACA;UAEA2E,MAAA,CAAA5J,IAAA;YACA9C,IAAA,EAAAqI,QAAA;YACA/B,IAAA;YACApG,IAAA,EAAAyN;UACA;QACA;MACA;QACA;QACAjB,MAAA,CAAA5J,IAAA;UACA9C,IAAA,EAAAgI,OAAA,KAAAhI,IAAA;UACAsG,IAAA;UACApG,IAAA,EAAAA,IAAA,CAAAkM,GAAA,CAAAtI,IAAA,IAAAA,IAAA,CAAAiE,KAAA;QACA;MACA;MAEA;QACAX,KAAA;UACAiF,IAAA,EAAAa,SAAA,CAAA9F,KAAA;QACA;QACAkF,OAAA;UACAC,OAAA;UACAsB,WAAA;YACAvH,IAAA;UACA;QACA;QACAwH,MAAA;UACA5N,IAAA,EAAAwM,MAAA,CAAAN,GAAA,CAAAQ,CAAA,IAAAA,CAAA,CAAA5M,IAAA;QACA;QACAwM,KAAA;UACAlG,IAAA;UACApG,IAAA,EAAAoN;QACA;QACAb,KAAA;UACAnG,IAAA;QACA;QACAoG,MAAA,EAAAA;MACA;IACA;IAEA;IACAtC,oBAAA8C,SAAA;MACA,IAAAhN,IAAA;MACA,IAAA8H,OAAA;;MAEA;MACA,IAAAkF,SAAA,CAAAhN,IAAA,IAAA2M,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAAhN,IAAA;QACAA,IAAA,GAAAgN,SAAA,CAAAhN,IAAA;QACA8H,OAAA,GAAAkF,SAAA,CAAAlF,OAAA;MACA;MACA;MAAA,KACA,IAAAkF,SAAA,CAAAhN,IAAA,IAAAgN,SAAA,CAAAhN,IAAA,CAAAA,IAAA,IAAA2M,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAAhN,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgN,SAAA,CAAAhN,IAAA,CAAAA,IAAA;QACA8H,OAAA,GAAAkF,SAAA,CAAAhN,IAAA,CAAAoI,MAAA,GACA4E,SAAA,CAAAhN,IAAA,CAAAoI,MAAA,CAAA8E,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA9E,SAAA;MACA;;MAEA;MACA,MAAA+E,SAAA,GAAApN,IAAA,CAAAkM,GAAA,CAAAtI,IAAA,IAAAA,IAAA,CAAAgE,KAAA,IAAAhE,IAAA,CAAA9D,IAAA;;MAEA;MACA,MAAA0M,MAAA;MACA,IAAAxM,IAAA,CAAAyC,MAAA,QAAAzC,IAAA,IAAAwM,MAAA;QACA;QACA,MAAAa,aAAA,OAAAC,GAAA;QACAtN,IAAA,CAAA2D,OAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAA4I,MAAA;YACA5I,IAAA,CAAA4I,MAAA,CAAA7I,OAAA,CAAA+I,CAAA,IAAAW,aAAA,CAAAE,GAAA,CAAAb,CAAA,CAAAvE,QAAA;UACA;QACA;QAEA,MAAA8D,UAAA,GAAAU,KAAA,CAAAa,IAAA,CAAAH,aAAA;QACApB,UAAA,CAAAtI,OAAA,CAAAwE,QAAA;UACA,MAAAsF,UAAA,GAAAzN,IAAA,CAAAkM,GAAA,CAAAtI,IAAA;YACA,MAAA4I,MAAA,GAAA5I,IAAA,CAAA4I,MAAA,EAAAkB,IAAA,CAAAhB,CAAA,IAAAA,CAAA,CAAAvE,QAAA,KAAAA,QAAA;YACA,OAAAqE,MAAA,GAAAA,MAAA,CAAA3E,KAAA;UACA;UAEA2E,MAAA,CAAA5J,IAAA;YACA9C,IAAA,EAAAqI,QAAA;YACA/B,IAAA;YACApG,IAAA,EAAAyN;UACA;QACA;MACA;QACA;QACAjB,MAAA,CAAA5J,IAAA;UACA9C,IAAA,EAAAgI,OAAA,KAAAhI,IAAA;UACAsG,IAAA;UACApG,IAAA,EAAAA,IAAA,CAAAkM,GAAA,CAAAtI,IAAA,IAAAA,IAAA,CAAAiE,KAAA;QACA;MACA;MAEA;QACAX,KAAA;UACAiF,IAAA,EAAAa,SAAA,CAAA9F,KAAA;QACA;QACAkF,OAAA;UACAC,OAAA;QACA;QACAuB,MAAA;UACA5N,IAAA,EAAAwM,MAAA,CAAAN,GAAA,CAAAQ,CAAA,IAAAA,CAAA,CAAA5M,IAAA;QACA;QACAwM,KAAA;UACAlG,IAAA;UACApG,IAAA,EAAAoN;QACA;QACAb,KAAA;UACAnG,IAAA;QACA;QACAoG,MAAA,EAAAA;MACA;IACA;IAEA;IACArC,mBAAA6C,SAAA;MACA,IAAAhN,IAAA;;MAEA;MACA,IAAAgN,SAAA,CAAAhN,IAAA,IAAA2M,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAAhN,IAAA;QACAA,IAAA,GAAAgN,SAAA,CAAAhN,IAAA;MACA;MACA;MAAA,KACA,IAAAgN,SAAA,CAAAhN,IAAA,IAAAgN,SAAA,CAAAhN,IAAA,CAAAA,IAAA,IAAA2M,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAAhN,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgN,SAAA,CAAAhN,IAAA,CAAAA,IAAA;MACA;MAEA,MAAAyN,UAAA,GAAAzN,IAAA,CAAAkM,GAAA,CAAAtI,IAAA;QACA9D,IAAA,EAAA8D,IAAA,CAAAgE,KAAA,IAAAhE,IAAA,CAAA9D,IAAA;QACA+H,KAAA,EAAAjE,IAAA,CAAAiE;MACA;MAEA;QACAX,KAAA;UACAiF,IAAA,EAAAa,SAAA,CAAA9F,KAAA;QACA;QACAkF,OAAA;UACAC,OAAA;UACAwB,SAAA;QACA;QACAD,MAAA;UACAE,MAAA;UACAC,KAAA;UACAC,GAAA;UACAhO,IAAA,EAAAyN,UAAA,CAAAvB,GAAA,CAAAtI,IAAA,IAAAA,IAAA,CAAA9D,IAAA;QACA;QACA0M,MAAA;UACA1M,IAAA;UACAsG,IAAA;UACA6H,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAtF,QAAA;UACA;UACAuF,QAAA;YACAF,KAAA;cACAC,IAAA;cACAE,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAJ,IAAA;UACA;UACApO,IAAA,EAAAyN;QACA;MACA;IACA;IAEA;IACArD,wBAAA4C,SAAA;MACA;MACA,IAAAhN,IAAA;MACA,IAAA8H,OAAA;;MAEA;MACA,IAAAkF,SAAA,CAAAhN,IAAA,IAAA2M,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAAhN,IAAA;QACAA,IAAA,GAAAgN,SAAA,CAAAhN,IAAA;QACA8H,OAAA,GAAAkF,SAAA,CAAAlF,OAAA;MACA;MACA;MAAA,KACA,IAAAkF,SAAA,CAAAhN,IAAA,IAAAgN,SAAA,CAAAhN,IAAA,CAAAA,IAAA,IAAA2M,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAAhN,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgN,SAAA,CAAAhN,IAAA,CAAAA,IAAA;QACA8H,OAAA,GAAAkF,SAAA,CAAAhN,IAAA,CAAAoI,MAAA,GACA4E,SAAA,CAAAhN,IAAA,CAAAoI,MAAA,CAAA8E,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAA9E,SAAA;MACA;;MAEA;MACA,MAAAoG,SAAA,GAAAzO,IAAA,CAAAkM,GAAA,CAAAtI,IAAA,IAAAA,IAAA,CAAAgE,KAAA,IAAAhE,IAAA,CAAA9D,IAAA;;MAEA;MACA,MAAA0M,MAAA;MACA,IAAAxM,IAAA,CAAAyC,MAAA,QAAAzC,IAAA,IAAAwM,MAAA;QACA;QACA,MAAAa,aAAA,OAAAC,GAAA;QACAtN,IAAA,CAAA2D,OAAA,CAAAC,IAAA;UACA,IAAAA,IAAA,CAAA4I,MAAA;YACA5I,IAAA,CAAA4I,MAAA,CAAA7I,OAAA,CAAA+I,CAAA,IAAAW,aAAA,CAAAE,GAAA,CAAAb,CAAA,CAAAvE,QAAA;UACA;QACA;QAEA,MAAA8D,UAAA,GAAAU,KAAA,CAAAa,IAAA,CAAAH,aAAA;QACApB,UAAA,CAAAtI,OAAA,CAAAwE,QAAA;UACA,MAAAsF,UAAA,GAAAzN,IAAA,CAAAkM,GAAA,CAAAtI,IAAA;YACA,MAAA4I,MAAA,GAAA5I,IAAA,CAAA4I,MAAA,EAAAkB,IAAA,CAAAhB,CAAA,IAAAA,CAAA,CAAAvE,QAAA,KAAAA,QAAA;YACA,OAAAqE,MAAA,GAAAA,MAAA,CAAA3E,KAAA;UACA;UAEA2E,MAAA,CAAA5J,IAAA;YACA9C,IAAA,EAAAqI,QAAA;YACA/B,IAAA;YAAA;YACApG,IAAA,EAAAyN;UACA;QACA;MACA;QACA;QACAjB,MAAA,CAAA5J,IAAA;UACA9C,IAAA,EAAAgI,OAAA,KAAAhI,IAAA;UACAsG,IAAA;UAAA;UACApG,IAAA,EAAAA,IAAA,CAAAkM,GAAA,CAAAtI,IAAA,IAAAA,IAAA,CAAAiE,KAAA;QACA;MACA;MAEA;QACAX,KAAA;UACAiF,IAAA,EAAAa,SAAA,CAAA9F,KAAA;QACA;QACAkF,OAAA;UACAC,OAAA;UACAsB,WAAA;YACAvH,IAAA;UACA;QACA;QACAwH,MAAA;UACA5N,IAAA,EAAAwM,MAAA,CAAAN,GAAA,CAAAQ,CAAA,IAAAA,CAAA,CAAA5M,IAAA;QACA;QACA;QACAwM,KAAA;UACAlG,IAAA;QACA;QACAmG,KAAA;UACAnG,IAAA;UAAA;UACApG,IAAA,EAAAyO;QACA;QACAjC,MAAA,EAAAA;MACA;IACA;IAEA;IACAnC,kBAAA2C,SAAA;MACA;QACA9F,KAAA;UACAiF,IAAA,EAAAa,SAAA,CAAA9F,KAAA;QACA;QACAkF,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAlG,IAAA;UACApG,IAAA;QACA;QACAuM,KAAA;UACAnG,IAAA;QACA;QACAoG,MAAA;UACApG,IAAA,EAAA4G,SAAA,CAAA5G,IAAA;UACApG,IAAA;QACA;MACA;IACA;IAEA;IACA2K,gBAAA+D,SAAA,EAAA1I,WAAA;MACA;MACA,IAAAhG,IAAA;MACA,IAAA2O,OAAA;;MAEA;MACA,IAAA3I,WAAA,CAAAhG,IAAA,IAAAgG,WAAA,CAAAhG,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgG,WAAA,CAAAhG,IAAA,CAAAA,IAAA;;QAEA;QACA,IAAAA,IAAA,CAAAyC,MAAA;UACA,IAAAzC,IAAA,IAAAwM,MAAA;YACA;YACAmC,OAAA;YACA,MAAAC,SAAA,GAAA5O,IAAA;YACA,IAAA4O,SAAA,CAAApC,MAAA,IAAAG,KAAA,CAAAM,OAAA,CAAA2B,SAAA,CAAApC,MAAA;cACAoC,SAAA,CAAApC,MAAA,CAAA7I,OAAA,CAAA+I,CAAA;gBACAiC,OAAA,CAAA/L,IAAA,CAAA8J,CAAA,CAAAvE,QAAA;cACA;YACA;UACA;YACA;YACAwG,OAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAE,SAAA;;MAEA;MACAA,SAAA;MACAF,OAAA,CAAAhL,OAAA,CAAAmL,MAAA;QACAD,SAAA,sGAAAC,MAAA;MACA;MACAD,SAAA;;MAEA;MACAA,SAAA;MACA7O,IAAA,CAAA2D,OAAA,CAAAC,IAAA;QACAiL,SAAA;;QAEA;QACAA,SAAA,yDAAAjL,IAAA,CAAAgE,KAAA,IAAAhE,IAAA,CAAA9D,IAAA;;QAEA;QACA,IAAA8D,IAAA,CAAA4I,MAAA;UACA;UACA5I,IAAA,CAAA4I,MAAA,CAAA7I,OAAA,CAAA+I,CAAA;YACAmC,SAAA,yDAAAnC,CAAA,CAAA7E,KAAA,KAAAkH,SAAA,GAAArC,CAAA,CAAA7E,KAAA;UACA;QACA;UACA;UACAgH,SAAA,yDAAAjL,IAAA,CAAAiE,KAAA,KAAAkH,SAAA,GAAAnL,IAAA,CAAAiE,KAAA;QACA;QAEAgH,SAAA;MACA;MACAA,SAAA;;MAEA;MACAH,SAAA,CAAAjF,SAAA,GAAAoF,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}