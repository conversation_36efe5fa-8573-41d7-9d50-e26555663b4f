<!-- Generated by SpecStory -->

# 理解左边项目的指示 (2025-07-12 05:05Z)

_**User**_

读取左边的项目。告诉我你的理解！

---

_**Assistant**_

<details>
            <summary>Searched codebase "项目结构和主要功能" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `dataease-2.10.2githubCode/README.md` | L1-28 |
| `dataease-2.10.2/README.md` | L1-28 |
| `dataease-2.10.2githubCode/core/pom.xml` | L1-20 |
| `datafront/src/App.vue` | L1-54 |
| `dataease-2.10.2githubCode/core/core-frontend/src/router/index.ts` | L1-87 |
| `dataease-2.10.2/方案.md` | L1-56 |
| `dataease-2.10.2githubCode/README.md` | L72-95 |
| `src/main/resources/DataEase核心概念与字段类型.md` | L1-33 |
| `dataease-2.10.2/README.md` | L72-95 |
| `dataease-2.10.2githubCode/core/core-frontend/src/pages/index/main.ts` | L1-27 |
| `src/main/resources/用户意图与参数映射策略.md` | L1-32 |
| `dataease-2.10.2/core/core-backend/src/main/java/io/dataease/CoreApplication.java` | L1-19 |
| `dataease-2.10.2githubCode/core/core-frontend/src/locales/zh-CN.ts` | L2349-2419 |
| `datafront/xiaozhi-ui/src/components/ChatWindow.vue` | L1-48 |
| `dataease-2.10.2/core/pom.xml` | L1-18 |
| `dataease-2.10.2githubCode/core/core-frontend/package.json` | L1-58 |
| `src/main/resources/a.md` | L1-37 |
| `datafront/README.md` | L1-25 |
| `dataease-2.10.2/core/core-backend/src/main/resources/application.yml` | L1-70 |
| `dataease-2.10.2githubCode/sdk/pom.xml` | L1-22 |
| `dataease-2.10.2/core/core-backend/src/main/resources/i18n/core_zh_CN.properties` | L1-43 |
| `datafront/src/App.vue` | L177-219 |
| `dataease-2.10.2githubCode/README.md` | L28-72 |
| `dataease-2.10.2/core/core-backend/src/main/java/io/dataease/home/<USER>
| `src/main/java/com/dataease/indicatorqa/IndicatorQaServiceApplication.java` | L1-13 |

</details>

---

Read file: src/main/java/com/dataease/indicatorqa/assistant/IndicatorQaAgent.java

---

Read file: src/main/java/com/dataease/indicatorqa/Tools/IndicatorTools.java

---

<no message>

---

Read file: src/main/java/com/dataease/indicatorqa/controller/IndicatorQaController.java

---

<no message>

---

Read file: src/main/java/com/dataease/indicatorqa/controller/ChartDataController.java

---

<no message>

---

Read file: src/main/java/com/dataease/indicatorqa/model/DataEaseChartDataRequest.java

---

<no message>

---

Read file: src/main/java/com/dataease/indicatorqa/model/IndicatorResult.java

---

<no message>

---

Read file: src/main/java/com/dataease/indicatorqa/util/ThinkingProcessParser.java

---

<no message>

---

Read file: src/main/resources/indicator-prompt-template.txt

---

<no message>

---

Read file: pom.xml

---

<no message>

---

Read file: src/main/resources/API参数详解.md

---

<no message>

---

Read file: src/main/java/com/dataease/indicatorqa/controller/DataEaseController.java

---

<no message>

---

Read file: src/main/java/com/dataease/indicatorqa/util/DataEaseApiClient.java

---

<no message>

---

Read file: src/main/java/com/dataease/indicatorqa/config/IndicatorAgentConfig.java

---

<no message>

---

Read file: src/main/java/com/dataease/indicatorqa/config/EmbeddingStoreConfig.java

---

这是一个基于DataEase开源BI工具的指标问答系统项目。项目的主要功能是通过AI助手将用户的自然语言查询转换为DataEase图表配置，实现数据可视化分析。以下是我对项目的理解：

## 项目概述

1. **核心功能**：这是一个名为"indicator-qa-service"的服务，使用LangChain4j框架构建的AI助手，能够将用户的自然语言问题转换为DataEase图表配置，并生成可视化图表。

2. **技术栈**：
   - 后端：Spring Boot 3.2.6
   - AI框架：LangChain4j 1.0.0-beta3
   - 大模型集成：支持OpenAI、阿里云百炼(Qwen)等
   - 数据存储：Redis、MongoDB
   - 流式输出：使用WebFlux和Reactor

## 主要组件

1. **IndicatorQaAgent**：核心AI服务接口，通过LangChain4j的@AiService注解实现，提供了以下功能：
   - 普通问答功能
   - 带思考过程的流式问答功能
   - 指标查询功能

2. **IndicatorTools**：AI助手使用的工具集，主要包含：
   - `getChartData`：根据用户需求调用DataEase API获取图表数据
   - 支持多种图表类型：柱状图、折线图、饼图等
   - 将图表数据缓存到Redis中

3. **控制器**：
   - `IndicatorQaController`：提供指标问答、流式问答API
   - `ChartDataController`：提供从Redis获取图表数据的API
   - `DataEaseController`：提供与DataEase交互的API

4. **数据模型**：
   - `IndicatorResult`：包含回答内容、图表数据ID和思考过程
   - `DataEaseChartDataRequest`：DataEase图表数据请求模型
   - `ThinkingStep`：思考步骤模型，用于记录AI的推理过程

5. **工具类**：
   - `ThinkingProcessParser`：解析AI思考过程的工具类
   - `DataEaseApiClient`：与DataEase API交互的客户端

## 工作流程

1. 用户发送自然语言问题到`/api/indicator/chat`或`/api/indicator/chat-with-thinking`端点
2. IndicatorQaAgent处理问题，使用IndicatorTools工具调用DataEase API
3. AI助手根据prompt模板将用户问题转换为图表配置参数
4. 系统调用DataEase API获取图表数据，并将数据缓存到Redis
5. 返回包含图表数据ID的响应给前端
6. 前端通过`/api/chart/data/{chartDataId}`获取完整图表数据并渲染

## 特色功能

1. **思考过程可视化**：通过`chatWithThinking`方法提供AI推理过程的实时输出
2. **多种图表支持**：柱状图、折线图、饼图等多种可视化方式
3. **数据缓存机制**：使用Redis缓存图表数据，提高响应速度
4. **流式输出**：使用Reactor框架实现流式输出，提供更好的用户体验

这个项目是将AI与BI工具结合的典型应用，通过自然语言交互简化了数据分析和可视化的过程，使非技术用户也能轻松进行数据分析。

---

