{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as polygonContain from 'zrender/lib/contain/polygon.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { linePolygonIntersect } from '../../util/graphic.js';\nexport function makeBrushCommonSelectorForSeries(area) {\n  var brushType = area.brushType;\n  // Do not use function binding or curry for performance.\n  var selectors = {\n    point: function (itemLayout) {\n      return selector[brushType].point(itemLayout, selectors, area);\n    },\n    rect: function (itemLayout) {\n      return selector[brushType].rect(itemLayout, selectors, area);\n    }\n  };\n  return selectors;\n}\nvar selector = {\n  lineX: getLineSelectors(0),\n  lineY: getLineSelectors(1),\n  rect: {\n    point: function (itemLayout, selectors, area) {\n      return itemLayout && area.boundingRect.contain(itemLayout[0], itemLayout[1]);\n    },\n    rect: function (itemLayout, selectors, area) {\n      return itemLayout && area.boundingRect.intersect(itemLayout);\n    }\n  },\n  polygon: {\n    point: function (itemLayout, selectors, area) {\n      return itemLayout && area.boundingRect.contain(itemLayout[0], itemLayout[1]) && polygonContain.contain(area.range, itemLayout[0], itemLayout[1]);\n    },\n    rect: function (itemLayout, selectors, area) {\n      var points = area.range;\n      if (!itemLayout || points.length <= 1) {\n        return false;\n      }\n      var x = itemLayout.x;\n      var y = itemLayout.y;\n      var width = itemLayout.width;\n      var height = itemLayout.height;\n      var p = points[0];\n      if (polygonContain.contain(points, x, y) || polygonContain.contain(points, x + width, y) || polygonContain.contain(points, x, y + height) || polygonContain.contain(points, x + width, y + height) || BoundingRect.create(itemLayout).contain(p[0], p[1]) || linePolygonIntersect(x, y, x + width, y, points) || linePolygonIntersect(x, y, x, y + height, points) || linePolygonIntersect(x + width, y, x + width, y + height, points) || linePolygonIntersect(x, y + height, x + width, y + height, points)) {\n        return true;\n      }\n    }\n  }\n};\nfunction getLineSelectors(xyIndex) {\n  var xy = ['x', 'y'];\n  var wh = ['width', 'height'];\n  return {\n    point: function (itemLayout, selectors, area) {\n      if (itemLayout) {\n        var range = area.range;\n        var p = itemLayout[xyIndex];\n        return inLineRange(p, range);\n      }\n    },\n    rect: function (itemLayout, selectors, area) {\n      if (itemLayout) {\n        var range = area.range;\n        var layoutRange = [itemLayout[xy[xyIndex]], itemLayout[xy[xyIndex]] + itemLayout[wh[xyIndex]]];\n        layoutRange[1] < layoutRange[0] && layoutRange.reverse();\n        return inLineRange(layoutRange[0], range) || inLineRange(layoutRange[1], range) || inLineRange(range[0], layoutRange) || inLineRange(range[1], layoutRange);\n      }\n    }\n  };\n}\nfunction inLineRange(p, range) {\n  return range[0] <= p && p <= range[1];\n}\nexport default selector;", "map": {"version": 3, "names": ["polygonContain", "BoundingRect", "linePolygonIntersect", "makeBrushCommonSelectorForSeries", "area", "brushType", "selectors", "point", "itemLayout", "selector", "rect", "lineX", "getLineSelectors", "lineY", "boundingRect", "contain", "intersect", "polygon", "range", "points", "length", "x", "y", "width", "height", "p", "create", "xyIndex", "xy", "wh", "inLineRange", "layoutRange", "reverse"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/brush/selector.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as polygonContain from 'zrender/lib/contain/polygon.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { linePolygonIntersect } from '../../util/graphic.js';\nexport function makeBrushCommonSelectorForSeries(area) {\n  var brushType = area.brushType;\n  // Do not use function binding or curry for performance.\n  var selectors = {\n    point: function (itemLayout) {\n      return selector[brushType].point(itemLayout, selectors, area);\n    },\n    rect: function (itemLayout) {\n      return selector[brushType].rect(itemLayout, selectors, area);\n    }\n  };\n  return selectors;\n}\nvar selector = {\n  lineX: getLineSelectors(0),\n  lineY: getLineSelectors(1),\n  rect: {\n    point: function (itemLayout, selectors, area) {\n      return itemLayout && area.boundingRect.contain(itemLayout[0], itemLayout[1]);\n    },\n    rect: function (itemLayout, selectors, area) {\n      return itemLayout && area.boundingRect.intersect(itemLayout);\n    }\n  },\n  polygon: {\n    point: function (itemLayout, selectors, area) {\n      return itemLayout && area.boundingRect.contain(itemLayout[0], itemLayout[1]) && polygonContain.contain(area.range, itemLayout[0], itemLayout[1]);\n    },\n    rect: function (itemLayout, selectors, area) {\n      var points = area.range;\n      if (!itemLayout || points.length <= 1) {\n        return false;\n      }\n      var x = itemLayout.x;\n      var y = itemLayout.y;\n      var width = itemLayout.width;\n      var height = itemLayout.height;\n      var p = points[0];\n      if (polygonContain.contain(points, x, y) || polygonContain.contain(points, x + width, y) || polygonContain.contain(points, x, y + height) || polygonContain.contain(points, x + width, y + height) || BoundingRect.create(itemLayout).contain(p[0], p[1]) || linePolygonIntersect(x, y, x + width, y, points) || linePolygonIntersect(x, y, x, y + height, points) || linePolygonIntersect(x + width, y, x + width, y + height, points) || linePolygonIntersect(x, y + height, x + width, y + height, points)) {\n        return true;\n      }\n    }\n  }\n};\nfunction getLineSelectors(xyIndex) {\n  var xy = ['x', 'y'];\n  var wh = ['width', 'height'];\n  return {\n    point: function (itemLayout, selectors, area) {\n      if (itemLayout) {\n        var range = area.range;\n        var p = itemLayout[xyIndex];\n        return inLineRange(p, range);\n      }\n    },\n    rect: function (itemLayout, selectors, area) {\n      if (itemLayout) {\n        var range = area.range;\n        var layoutRange = [itemLayout[xy[xyIndex]], itemLayout[xy[xyIndex]] + itemLayout[wh[xyIndex]]];\n        layoutRange[1] < layoutRange[0] && layoutRange.reverse();\n        return inLineRange(layoutRange[0], range) || inLineRange(layoutRange[1], range) || inLineRange(range[0], layoutRange) || inLineRange(range[1], layoutRange);\n      }\n    }\n  };\n}\nfunction inLineRange(p, range) {\n  return range[0] <= p && p <= range[1];\n}\nexport default selector;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,cAAc,MAAM,gCAAgC;AAChE,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAO,SAASC,gCAAgCA,CAACC,IAAI,EAAE;EACrD,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;EAC9B;EACA,IAAIC,SAAS,GAAG;IACdC,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAE;MAC3B,OAAOC,QAAQ,CAACJ,SAAS,CAAC,CAACE,KAAK,CAACC,UAAU,EAAEF,SAAS,EAAEF,IAAI,CAAC;IAC/D,CAAC;IACDM,IAAI,EAAE,SAAAA,CAAUF,UAAU,EAAE;MAC1B,OAAOC,QAAQ,CAACJ,SAAS,CAAC,CAACK,IAAI,CAACF,UAAU,EAAEF,SAAS,EAAEF,IAAI,CAAC;IAC9D;EACF,CAAC;EACD,OAAOE,SAAS;AAClB;AACA,IAAIG,QAAQ,GAAG;EACbE,KAAK,EAAEC,gBAAgB,CAAC,CAAC,CAAC;EAC1BC,KAAK,EAAED,gBAAgB,CAAC,CAAC,CAAC;EAC1BF,IAAI,EAAE;IACJH,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAEF,SAAS,EAAEF,IAAI,EAAE;MAC5C,OAAOI,UAAU,IAAIJ,IAAI,CAACU,YAAY,CAACC,OAAO,CAACP,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9E,CAAC;IACDE,IAAI,EAAE,SAAAA,CAAUF,UAAU,EAAEF,SAAS,EAAEF,IAAI,EAAE;MAC3C,OAAOI,UAAU,IAAIJ,IAAI,CAACU,YAAY,CAACE,SAAS,CAACR,UAAU,CAAC;IAC9D;EACF,CAAC;EACDS,OAAO,EAAE;IACPV,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAEF,SAAS,EAAEF,IAAI,EAAE;MAC5C,OAAOI,UAAU,IAAIJ,IAAI,CAACU,YAAY,CAACC,OAAO,CAACP,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC,IAAIR,cAAc,CAACe,OAAO,CAACX,IAAI,CAACc,KAAK,EAAEV,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC;IAClJ,CAAC;IACDE,IAAI,EAAE,SAAAA,CAAUF,UAAU,EAAEF,SAAS,EAAEF,IAAI,EAAE;MAC3C,IAAIe,MAAM,GAAGf,IAAI,CAACc,KAAK;MACvB,IAAI,CAACV,UAAU,IAAIW,MAAM,CAACC,MAAM,IAAI,CAAC,EAAE;QACrC,OAAO,KAAK;MACd;MACA,IAAIC,CAAC,GAAGb,UAAU,CAACa,CAAC;MACpB,IAAIC,CAAC,GAAGd,UAAU,CAACc,CAAC;MACpB,IAAIC,KAAK,GAAGf,UAAU,CAACe,KAAK;MAC5B,IAAIC,MAAM,GAAGhB,UAAU,CAACgB,MAAM;MAC9B,IAAIC,CAAC,GAAGN,MAAM,CAAC,CAAC,CAAC;MACjB,IAAInB,cAAc,CAACe,OAAO,CAACI,MAAM,EAAEE,CAAC,EAAEC,CAAC,CAAC,IAAItB,cAAc,CAACe,OAAO,CAACI,MAAM,EAAEE,CAAC,GAAGE,KAAK,EAAED,CAAC,CAAC,IAAItB,cAAc,CAACe,OAAO,CAACI,MAAM,EAAEE,CAAC,EAAEC,CAAC,GAAGE,MAAM,CAAC,IAAIxB,cAAc,CAACe,OAAO,CAACI,MAAM,EAAEE,CAAC,GAAGE,KAAK,EAAED,CAAC,GAAGE,MAAM,CAAC,IAAIvB,YAAY,CAACyB,MAAM,CAAClB,UAAU,CAAC,CAACO,OAAO,CAACU,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIvB,oBAAoB,CAACmB,CAAC,EAAEC,CAAC,EAAED,CAAC,GAAGE,KAAK,EAAED,CAAC,EAAEH,MAAM,CAAC,IAAIjB,oBAAoB,CAACmB,CAAC,EAAEC,CAAC,EAAED,CAAC,EAAEC,CAAC,GAAGE,MAAM,EAAEL,MAAM,CAAC,IAAIjB,oBAAoB,CAACmB,CAAC,GAAGE,KAAK,EAAED,CAAC,EAAED,CAAC,GAAGE,KAAK,EAAED,CAAC,GAAGE,MAAM,EAAEL,MAAM,CAAC,IAAIjB,oBAAoB,CAACmB,CAAC,EAAEC,CAAC,GAAGE,MAAM,EAAEH,CAAC,GAAGE,KAAK,EAAED,CAAC,GAAGE,MAAM,EAAEL,MAAM,CAAC,EAAE;QAC7e,OAAO,IAAI;MACb;IACF;EACF;AACF,CAAC;AACD,SAASP,gBAAgBA,CAACe,OAAO,EAAE;EACjC,IAAIC,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EACnB,IAAIC,EAAE,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;EAC5B,OAAO;IACLtB,KAAK,EAAE,SAAAA,CAAUC,UAAU,EAAEF,SAAS,EAAEF,IAAI,EAAE;MAC5C,IAAII,UAAU,EAAE;QACd,IAAIU,KAAK,GAAGd,IAAI,CAACc,KAAK;QACtB,IAAIO,CAAC,GAAGjB,UAAU,CAACmB,OAAO,CAAC;QAC3B,OAAOG,WAAW,CAACL,CAAC,EAAEP,KAAK,CAAC;MAC9B;IACF,CAAC;IACDR,IAAI,EAAE,SAAAA,CAAUF,UAAU,EAAEF,SAAS,EAAEF,IAAI,EAAE;MAC3C,IAAII,UAAU,EAAE;QACd,IAAIU,KAAK,GAAGd,IAAI,CAACc,KAAK;QACtB,IAAIa,WAAW,GAAG,CAACvB,UAAU,CAACoB,EAAE,CAACD,OAAO,CAAC,CAAC,EAAEnB,UAAU,CAACoB,EAAE,CAACD,OAAO,CAAC,CAAC,GAAGnB,UAAU,CAACqB,EAAE,CAACF,OAAO,CAAC,CAAC,CAAC;QAC9FI,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,IAAIA,WAAW,CAACC,OAAO,CAAC,CAAC;QACxD,OAAOF,WAAW,CAACC,WAAW,CAAC,CAAC,CAAC,EAAEb,KAAK,CAAC,IAAIY,WAAW,CAACC,WAAW,CAAC,CAAC,CAAC,EAAEb,KAAK,CAAC,IAAIY,WAAW,CAACZ,KAAK,CAAC,CAAC,CAAC,EAAEa,WAAW,CAAC,IAAID,WAAW,CAACZ,KAAK,CAAC,CAAC,CAAC,EAAEa,WAAW,CAAC;MAC7J;IACF;EACF,CAAC;AACH;AACA,SAASD,WAAWA,CAACL,CAAC,EAAEP,KAAK,EAAE;EAC7B,OAAOA,KAAK,CAAC,CAAC,CAAC,IAAIO,CAAC,IAAIA,CAAC,IAAIP,KAAK,CAAC,CAAC,CAAC;AACvC;AACA,eAAeT,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}