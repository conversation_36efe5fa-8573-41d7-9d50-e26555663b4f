/**
 * nnfx dark - a theme inspired by Netscape Navigator/Firefox
 *
 * @version 1.0.0
 * <AUTHOR> 2020 <PERSON> <<EMAIL>>
 * @license https://creativecommons.org/licenses/by-sa/4.0  CC BY-SA 4.0
 */

.hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  background: #333;
  color: #fff;
}

.xml .hljs-meta {
  font-weight: bold;
  font-style: italic;
  color: #69f;
}

.hljs-comment,
.hljs-quote {
  font-style: italic;
  color: #9c6;
}

.hljs-name,
.hljs-keyword {
  color: #a7a;
}

.hljs-name,
.hljs-attr {
  font-weight: bold;
}

.hljs-string {
  font-weight: normal;
}

.hljs-variable,
.hljs-template-variable {
  color: #588;
}

.hljs-code,
.hljs-string,
.hljs-meta-string,
.hljs-number,
.hljs-regexp,
.hljs-link {
  color: #bce;
}

.hljs-title,
.hljs-symbol,
.hljs-bullet,
.hljs-built_in,
.hljs-builtin-name {
  color: #d40;
}

.hljs-section,
.hljs-meta {
  color: #a85;
}

.hljs-class .hljs-title,
.hljs-type {
  color: #96c;
}

.hljs-function .hljs-title,
.hljs-attr,
.hljs-subst {
  color: #fff;
}

.hljs-formula {
  background-color: #eee;
  font-style: italic;
}

.hljs-addition {
  background-color: #797;
}

.hljs-deletion {
  background-color: #c99;
}

.hljs-selector-id,
.hljs-selector-class {
  color: #964;
}

.hljs-doctag,
.hljs-strong {
  font-weight: bold;
}

.hljs-emphasis {
  font-style: italic;
}
