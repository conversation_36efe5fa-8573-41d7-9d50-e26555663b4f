{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentView from '../../view/Component.js';\nimport GridModel from '../../coord/cartesian/GridModel.js';\nimport { Rect } from '../../util/graphic.js';\nimport { defaults } from 'zrender/lib/core/util.js';\nimport { CartesianAxisModel } from '../../coord/cartesian/AxisModel.js';\nimport axisModelCreator from '../../coord/axisModelCreator.js';\nimport Grid from '../../coord/cartesian/Grid.js';\nimport { CartesianXAxisView, CartesianYAxisView } from '../axis/CartesianAxisView.js';\n// Grid view\nvar GridView = /** @class */function (_super) {\n  __extends(GridView, _super);\n  function GridView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'grid';\n    return _this;\n  }\n  GridView.prototype.render = function (gridModel, ecModel) {\n    this.group.removeAll();\n    if (gridModel.get('show')) {\n      this.group.add(new Rect({\n        shape: gridModel.coordinateSystem.getRect(),\n        style: defaults({\n          fill: gridModel.get('backgroundColor')\n        }, gridModel.getItemStyle()),\n        silent: true,\n        z2: -1\n      }));\n    }\n  };\n  GridView.type = 'grid';\n  return GridView;\n}(ComponentView);\nvar extraOption = {\n  // gridIndex: 0,\n  // gridId: '',\n  offset: 0\n};\nexport function install(registers) {\n  registers.registerComponentView(GridView);\n  registers.registerComponentModel(GridModel);\n  registers.registerCoordinateSystem('cartesian2d', Grid);\n  axisModelCreator(registers, 'x', CartesianAxisModel, extraOption);\n  axisModelCreator(registers, 'y', CartesianAxisModel, extraOption);\n  registers.registerComponentView(CartesianXAxisView);\n  registers.registerComponentView(CartesianYAxisView);\n  registers.registerPreprocessor(function (option) {\n    // Only create grid when need\n    if (option.xAxis && option.yAxis && !option.grid) {\n      option.grid = {};\n    }\n  });\n}", "map": {"version": 3, "names": ["__extends", "ComponentView", "GridModel", "Rect", "defaults", "CartesianAxisModel", "axisModelCreator", "Grid", "CartesianXAxisView", "CartesianYAxisView", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "gridModel", "ecModel", "group", "removeAll", "get", "add", "shape", "coordinateSystem", "getRect", "style", "fill", "getItemStyle", "silent", "z2", "extraOption", "offset", "install", "registers", "registerComponentView", "registerComponentModel", "registerCoordinateSystem", "registerPreprocessor", "option", "xAxis", "yAxis", "grid"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/grid/installSimple.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentView from '../../view/Component.js';\nimport GridModel from '../../coord/cartesian/GridModel.js';\nimport { Rect } from '../../util/graphic.js';\nimport { defaults } from 'zrender/lib/core/util.js';\nimport { CartesianAxisModel } from '../../coord/cartesian/AxisModel.js';\nimport axisModelCreator from '../../coord/axisModelCreator.js';\nimport Grid from '../../coord/cartesian/Grid.js';\nimport { CartesianXAxisView, CartesianYAxisView } from '../axis/CartesianAxisView.js';\n// Grid view\nvar GridView = /** @class */function (_super) {\n  __extends(GridView, _super);\n  function GridView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'grid';\n    return _this;\n  }\n  GridView.prototype.render = function (gridModel, ecModel) {\n    this.group.removeAll();\n    if (gridModel.get('show')) {\n      this.group.add(new Rect({\n        shape: gridModel.coordinateSystem.getRect(),\n        style: defaults({\n          fill: gridModel.get('backgroundColor')\n        }, gridModel.getItemStyle()),\n        silent: true,\n        z2: -1\n      }));\n    }\n  };\n  GridView.type = 'grid';\n  return GridView;\n}(ComponentView);\nvar extraOption = {\n  // gridIndex: 0,\n  // gridId: '',\n  offset: 0\n};\nexport function install(registers) {\n  registers.registerComponentView(GridView);\n  registers.registerComponentModel(GridModel);\n  registers.registerCoordinateSystem('cartesian2d', Grid);\n  axisModelCreator(registers, 'x', CartesianAxisModel, extraOption);\n  axisModelCreator(registers, 'y', CartesianAxisModel, extraOption);\n  registers.registerComponentView(CartesianXAxisView);\n  registers.registerComponentView(CartesianYAxisView);\n  registers.registerPreprocessor(function (option) {\n    // Only create grid when need\n    if (option.xAxis && option.yAxis && !option.grid) {\n      option.grid = {};\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,SAASC,IAAI,QAAQ,uBAAuB;AAC5C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,kBAAkB,QAAQ,oCAAoC;AACvE,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,IAAI,MAAM,+BAA+B;AAChD,SAASC,kBAAkB,EAAEC,kBAAkB,QAAQ,8BAA8B;AACrF;AACA,IAAIC,QAAQ,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC5CX,SAAS,CAACU,QAAQ,EAAEC,MAAM,CAAC;EAC3B,SAASD,QAAQA,CAAA,EAAG;IAClB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAG,MAAM;IACnB,OAAOH,KAAK;EACd;EACAF,QAAQ,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,SAAS,EAAEC,OAAO,EAAE;IACxD,IAAI,CAACC,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAIH,SAAS,CAACI,GAAG,CAAC,MAAM,CAAC,EAAE;MACzB,IAAI,CAACF,KAAK,CAACG,GAAG,CAAC,IAAIpB,IAAI,CAAC;QACtBqB,KAAK,EAAEN,SAAS,CAACO,gBAAgB,CAACC,OAAO,CAAC,CAAC;QAC3CC,KAAK,EAAEvB,QAAQ,CAAC;UACdwB,IAAI,EAAEV,SAAS,CAACI,GAAG,CAAC,iBAAiB;QACvC,CAAC,EAAEJ,SAAS,CAACW,YAAY,CAAC,CAAC,CAAC;QAC5BC,MAAM,EAAE,IAAI;QACZC,EAAE,EAAE,CAAC;MACP,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACDrB,QAAQ,CAACK,IAAI,GAAG,MAAM;EACtB,OAAOL,QAAQ;AACjB,CAAC,CAACT,aAAa,CAAC;AAChB,IAAI+B,WAAW,GAAG;EAChB;EACA;EACAC,MAAM,EAAE;AACV,CAAC;AACD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,qBAAqB,CAAC1B,QAAQ,CAAC;EACzCyB,SAAS,CAACE,sBAAsB,CAACnC,SAAS,CAAC;EAC3CiC,SAAS,CAACG,wBAAwB,CAAC,aAAa,EAAE/B,IAAI,CAAC;EACvDD,gBAAgB,CAAC6B,SAAS,EAAE,GAAG,EAAE9B,kBAAkB,EAAE2B,WAAW,CAAC;EACjE1B,gBAAgB,CAAC6B,SAAS,EAAE,GAAG,EAAE9B,kBAAkB,EAAE2B,WAAW,CAAC;EACjEG,SAAS,CAACC,qBAAqB,CAAC5B,kBAAkB,CAAC;EACnD2B,SAAS,CAACC,qBAAqB,CAAC3B,kBAAkB,CAAC;EACnD0B,SAAS,CAACI,oBAAoB,CAAC,UAAUC,MAAM,EAAE;IAC/C;IACA,IAAIA,MAAM,CAACC,KAAK,IAAID,MAAM,CAACE,KAAK,IAAI,CAACF,MAAM,CAACG,IAAI,EAAE;MAChDH,MAAM,CAACG,IAAI,GAAG,CAAC,CAAC;IAClB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}