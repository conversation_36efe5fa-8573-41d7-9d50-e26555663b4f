{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport * as layout from '../../util/layout.js';\nimport TimelineView from './TimelineView.js';\nimport TimelineAxis from './TimelineAxis.js';\nimport { createSymbol, normalizeSymbolOffset, normalizeSymbolSize } from '../../util/symbol.js';\nimport * as numberUtil from '../../util/number.js';\nimport { merge, each, extend, isString, bind, defaults, retrieve2 } from 'zrender/lib/core/util.js';\nimport OrdinalScale from '../../scale/Ordinal.js';\nimport TimeScale from '../../scale/Time.js';\nimport IntervalScale from '../../scale/Interval.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nimport { makeInner } from '../../util/model.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { createTooltipMarkup } from '../tooltip/tooltipMarkup.js';\nvar PI = Math.PI;\nvar labelDataIndexStore = makeInner();\nvar SliderTimelineView = /** @class */function (_super) {\n  __extends(SliderTimelineView, _super);\n  function SliderTimelineView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SliderTimelineView.type;\n    return _this;\n  }\n  SliderTimelineView.prototype.init = function (ecModel, api) {\n    this.api = api;\n  };\n  /**\r\n   * @override\r\n   */\n  SliderTimelineView.prototype.render = function (timelineModel, ecModel, api) {\n    this.model = timelineModel;\n    this.api = api;\n    this.ecModel = ecModel;\n    this.group.removeAll();\n    if (timelineModel.get('show', true)) {\n      var layoutInfo_1 = this._layout(timelineModel, api);\n      var mainGroup_1 = this._createGroup('_mainGroup');\n      var labelGroup = this._createGroup('_labelGroup');\n      var axis_1 = this._axis = this._createAxis(layoutInfo_1, timelineModel);\n      timelineModel.formatTooltip = function (dataIndex) {\n        var name = axis_1.scale.getLabel({\n          value: dataIndex\n        });\n        return createTooltipMarkup('nameValue', {\n          noName: true,\n          value: name\n        });\n      };\n      each(['AxisLine', 'AxisTick', 'Control', 'CurrentPointer'], function (name) {\n        this['_render' + name](layoutInfo_1, mainGroup_1, axis_1, timelineModel);\n      }, this);\n      this._renderAxisLabel(layoutInfo_1, labelGroup, axis_1, timelineModel);\n      this._position(layoutInfo_1, timelineModel);\n    }\n    this._doPlayStop();\n    this._updateTicksStatus();\n  };\n  /**\r\n   * @override\r\n   */\n  SliderTimelineView.prototype.remove = function () {\n    this._clearTimer();\n    this.group.removeAll();\n  };\n  /**\r\n   * @override\r\n   */\n  SliderTimelineView.prototype.dispose = function () {\n    this._clearTimer();\n  };\n  SliderTimelineView.prototype._layout = function (timelineModel, api) {\n    var labelPosOpt = timelineModel.get(['label', 'position']);\n    var orient = timelineModel.get('orient');\n    var viewRect = getViewRect(timelineModel, api);\n    var parsedLabelPos;\n    // Auto label offset.\n    if (labelPosOpt == null || labelPosOpt === 'auto') {\n      parsedLabelPos = orient === 'horizontal' ? viewRect.y + viewRect.height / 2 < api.getHeight() / 2 ? '-' : '+' : viewRect.x + viewRect.width / 2 < api.getWidth() / 2 ? '+' : '-';\n    } else if (isString(labelPosOpt)) {\n      parsedLabelPos = {\n        horizontal: {\n          top: '-',\n          bottom: '+'\n        },\n        vertical: {\n          left: '-',\n          right: '+'\n        }\n      }[orient][labelPosOpt];\n    } else {\n      // is number\n      parsedLabelPos = labelPosOpt;\n    }\n    var labelAlignMap = {\n      horizontal: 'center',\n      vertical: parsedLabelPos >= 0 || parsedLabelPos === '+' ? 'left' : 'right'\n    };\n    var labelBaselineMap = {\n      horizontal: parsedLabelPos >= 0 || parsedLabelPos === '+' ? 'top' : 'bottom',\n      vertical: 'middle'\n    };\n    var rotationMap = {\n      horizontal: 0,\n      vertical: PI / 2\n    };\n    // Position\n    var mainLength = orient === 'vertical' ? viewRect.height : viewRect.width;\n    var controlModel = timelineModel.getModel('controlStyle');\n    var showControl = controlModel.get('show', true);\n    var controlSize = showControl ? controlModel.get('itemSize') : 0;\n    var controlGap = showControl ? controlModel.get('itemGap') : 0;\n    var sizePlusGap = controlSize + controlGap;\n    // Special label rotate.\n    var labelRotation = timelineModel.get(['label', 'rotate']) || 0;\n    labelRotation = labelRotation * PI / 180; // To radian.\n    var playPosition;\n    var prevBtnPosition;\n    var nextBtnPosition;\n    var controlPosition = controlModel.get('position', true);\n    var showPlayBtn = showControl && controlModel.get('showPlayBtn', true);\n    var showPrevBtn = showControl && controlModel.get('showPrevBtn', true);\n    var showNextBtn = showControl && controlModel.get('showNextBtn', true);\n    var xLeft = 0;\n    var xRight = mainLength;\n    // position[0] means left, position[1] means middle.\n    if (controlPosition === 'left' || controlPosition === 'bottom') {\n      showPlayBtn && (playPosition = [0, 0], xLeft += sizePlusGap);\n      showPrevBtn && (prevBtnPosition = [xLeft, 0], xLeft += sizePlusGap);\n      showNextBtn && (nextBtnPosition = [xRight - controlSize, 0], xRight -= sizePlusGap);\n    } else {\n      // 'top' 'right'\n      showPlayBtn && (playPosition = [xRight - controlSize, 0], xRight -= sizePlusGap);\n      showPrevBtn && (prevBtnPosition = [0, 0], xLeft += sizePlusGap);\n      showNextBtn && (nextBtnPosition = [xRight - controlSize, 0], xRight -= sizePlusGap);\n    }\n    var axisExtent = [xLeft, xRight];\n    if (timelineModel.get('inverse')) {\n      axisExtent.reverse();\n    }\n    return {\n      viewRect: viewRect,\n      mainLength: mainLength,\n      orient: orient,\n      rotation: rotationMap[orient],\n      labelRotation: labelRotation,\n      labelPosOpt: parsedLabelPos,\n      labelAlign: timelineModel.get(['label', 'align']) || labelAlignMap[orient],\n      labelBaseline: timelineModel.get(['label', 'verticalAlign']) || timelineModel.get(['label', 'baseline']) || labelBaselineMap[orient],\n      // Based on mainGroup.\n      playPosition: playPosition,\n      prevBtnPosition: prevBtnPosition,\n      nextBtnPosition: nextBtnPosition,\n      axisExtent: axisExtent,\n      controlSize: controlSize,\n      controlGap: controlGap\n    };\n  };\n  SliderTimelineView.prototype._position = function (layoutInfo, timelineModel) {\n    // Position is be called finally, because bounding rect is needed for\n    // adapt content to fill viewRect (auto adapt offset).\n    // Timeline may be not all in the viewRect when 'offset' is specified\n    // as a number, because it is more appropriate that label aligns at\n    // 'offset' but not the other edge defined by viewRect.\n    var mainGroup = this._mainGroup;\n    var labelGroup = this._labelGroup;\n    var viewRect = layoutInfo.viewRect;\n    if (layoutInfo.orient === 'vertical') {\n      // transform to horizontal, inverse rotate by left-top point.\n      var m = matrix.create();\n      var rotateOriginX = viewRect.x;\n      var rotateOriginY = viewRect.y + viewRect.height;\n      matrix.translate(m, m, [-rotateOriginX, -rotateOriginY]);\n      matrix.rotate(m, m, -PI / 2);\n      matrix.translate(m, m, [rotateOriginX, rotateOriginY]);\n      viewRect = viewRect.clone();\n      viewRect.applyTransform(m);\n    }\n    var viewBound = getBound(viewRect);\n    var mainBound = getBound(mainGroup.getBoundingRect());\n    var labelBound = getBound(labelGroup.getBoundingRect());\n    var mainPosition = [mainGroup.x, mainGroup.y];\n    var labelsPosition = [labelGroup.x, labelGroup.y];\n    labelsPosition[0] = mainPosition[0] = viewBound[0][0];\n    var labelPosOpt = layoutInfo.labelPosOpt;\n    if (labelPosOpt == null || isString(labelPosOpt)) {\n      // '+' or '-'\n      var mainBoundIdx = labelPosOpt === '+' ? 0 : 1;\n      toBound(mainPosition, mainBound, viewBound, 1, mainBoundIdx);\n      toBound(labelsPosition, labelBound, viewBound, 1, 1 - mainBoundIdx);\n    } else {\n      var mainBoundIdx = labelPosOpt >= 0 ? 0 : 1;\n      toBound(mainPosition, mainBound, viewBound, 1, mainBoundIdx);\n      labelsPosition[1] = mainPosition[1] + labelPosOpt;\n    }\n    mainGroup.setPosition(mainPosition);\n    labelGroup.setPosition(labelsPosition);\n    mainGroup.rotation = labelGroup.rotation = layoutInfo.rotation;\n    setOrigin(mainGroup);\n    setOrigin(labelGroup);\n    function setOrigin(targetGroup) {\n      targetGroup.originX = viewBound[0][0] - targetGroup.x;\n      targetGroup.originY = viewBound[1][0] - targetGroup.y;\n    }\n    function getBound(rect) {\n      // [[xmin, xmax], [ymin, ymax]]\n      return [[rect.x, rect.x + rect.width], [rect.y, rect.y + rect.height]];\n    }\n    function toBound(fromPos, from, to, dimIdx, boundIdx) {\n      fromPos[dimIdx] += to[dimIdx][boundIdx] - from[dimIdx][boundIdx];\n    }\n  };\n  SliderTimelineView.prototype._createAxis = function (layoutInfo, timelineModel) {\n    var data = timelineModel.getData();\n    var axisType = timelineModel.get('axisType');\n    var scale = createScaleByModel(timelineModel, axisType);\n    // Customize scale. The `tickValue` is `dataIndex`.\n    scale.getTicks = function () {\n      return data.mapArray(['value'], function (value) {\n        return {\n          value: value\n        };\n      });\n    };\n    var dataExtent = data.getDataExtent('value');\n    scale.setExtent(dataExtent[0], dataExtent[1]);\n    scale.calcNiceTicks();\n    var axis = new TimelineAxis('value', scale, layoutInfo.axisExtent, axisType);\n    axis.model = timelineModel;\n    return axis;\n  };\n  SliderTimelineView.prototype._createGroup = function (key) {\n    var newGroup = this[key] = new graphic.Group();\n    this.group.add(newGroup);\n    return newGroup;\n  };\n  SliderTimelineView.prototype._renderAxisLine = function (layoutInfo, group, axis, timelineModel) {\n    var axisExtent = axis.getExtent();\n    if (!timelineModel.get(['lineStyle', 'show'])) {\n      return;\n    }\n    var line = new graphic.Line({\n      shape: {\n        x1: axisExtent[0],\n        y1: 0,\n        x2: axisExtent[1],\n        y2: 0\n      },\n      style: extend({\n        lineCap: 'round'\n      }, timelineModel.getModel('lineStyle').getLineStyle()),\n      silent: true,\n      z2: 1\n    });\n    group.add(line);\n    var progressLine = this._progressLine = new graphic.Line({\n      shape: {\n        x1: axisExtent[0],\n        x2: this._currentPointer ? this._currentPointer.x : axisExtent[0],\n        y1: 0,\n        y2: 0\n      },\n      style: defaults({\n        lineCap: 'round',\n        lineWidth: line.style.lineWidth\n      }, timelineModel.getModel(['progress', 'lineStyle']).getLineStyle()),\n      silent: true,\n      z2: 1\n    });\n    group.add(progressLine);\n  };\n  SliderTimelineView.prototype._renderAxisTick = function (layoutInfo, group, axis, timelineModel) {\n    var _this = this;\n    var data = timelineModel.getData();\n    // Show all ticks, despite ignoring strategy.\n    var ticks = axis.scale.getTicks();\n    this._tickSymbols = [];\n    // The value is dataIndex, see the customized scale.\n    each(ticks, function (tick) {\n      var tickCoord = axis.dataToCoord(tick.value);\n      var itemModel = data.getItemModel(tick.value);\n      var itemStyleModel = itemModel.getModel('itemStyle');\n      var hoverStyleModel = itemModel.getModel(['emphasis', 'itemStyle']);\n      var progressStyleModel = itemModel.getModel(['progress', 'itemStyle']);\n      var symbolOpt = {\n        x: tickCoord,\n        y: 0,\n        onclick: bind(_this._changeTimeline, _this, tick.value)\n      };\n      var el = giveSymbol(itemModel, itemStyleModel, group, symbolOpt);\n      el.ensureState('emphasis').style = hoverStyleModel.getItemStyle();\n      el.ensureState('progress').style = progressStyleModel.getItemStyle();\n      enableHoverEmphasis(el);\n      var ecData = getECData(el);\n      if (itemModel.get('tooltip')) {\n        ecData.dataIndex = tick.value;\n        ecData.dataModel = timelineModel;\n      } else {\n        ecData.dataIndex = ecData.dataModel = null;\n      }\n      _this._tickSymbols.push(el);\n    });\n  };\n  SliderTimelineView.prototype._renderAxisLabel = function (layoutInfo, group, axis, timelineModel) {\n    var _this = this;\n    var labelModel = axis.getLabelModel();\n    if (!labelModel.get('show')) {\n      return;\n    }\n    var data = timelineModel.getData();\n    var labels = axis.getViewLabels();\n    this._tickLabels = [];\n    each(labels, function (labelItem) {\n      // The tickValue is dataIndex, see the customized scale.\n      var dataIndex = labelItem.tickValue;\n      var itemModel = data.getItemModel(dataIndex);\n      var normalLabelModel = itemModel.getModel('label');\n      var hoverLabelModel = itemModel.getModel(['emphasis', 'label']);\n      var progressLabelModel = itemModel.getModel(['progress', 'label']);\n      var tickCoord = axis.dataToCoord(labelItem.tickValue);\n      var textEl = new graphic.Text({\n        x: tickCoord,\n        y: 0,\n        rotation: layoutInfo.labelRotation - layoutInfo.rotation,\n        onclick: bind(_this._changeTimeline, _this, dataIndex),\n        silent: false,\n        style: createTextStyle(normalLabelModel, {\n          text: labelItem.formattedLabel,\n          align: layoutInfo.labelAlign,\n          verticalAlign: layoutInfo.labelBaseline\n        })\n      });\n      textEl.ensureState('emphasis').style = createTextStyle(hoverLabelModel);\n      textEl.ensureState('progress').style = createTextStyle(progressLabelModel);\n      group.add(textEl);\n      enableHoverEmphasis(textEl);\n      labelDataIndexStore(textEl).dataIndex = dataIndex;\n      _this._tickLabels.push(textEl);\n    });\n  };\n  SliderTimelineView.prototype._renderControl = function (layoutInfo, group, axis, timelineModel) {\n    var controlSize = layoutInfo.controlSize;\n    var rotation = layoutInfo.rotation;\n    var itemStyle = timelineModel.getModel('controlStyle').getItemStyle();\n    var hoverStyle = timelineModel.getModel(['emphasis', 'controlStyle']).getItemStyle();\n    var playState = timelineModel.getPlayState();\n    var inverse = timelineModel.get('inverse', true);\n    makeBtn(layoutInfo.nextBtnPosition, 'next', bind(this._changeTimeline, this, inverse ? '-' : '+'));\n    makeBtn(layoutInfo.prevBtnPosition, 'prev', bind(this._changeTimeline, this, inverse ? '+' : '-'));\n    makeBtn(layoutInfo.playPosition, playState ? 'stop' : 'play', bind(this._handlePlayClick, this, !playState), true);\n    function makeBtn(position, iconName, onclick, willRotate) {\n      if (!position) {\n        return;\n      }\n      var iconSize = parsePercent(retrieve2(timelineModel.get(['controlStyle', iconName + 'BtnSize']), controlSize), controlSize);\n      var rect = [0, -iconSize / 2, iconSize, iconSize];\n      var btn = makeControlIcon(timelineModel, iconName + 'Icon', rect, {\n        x: position[0],\n        y: position[1],\n        originX: controlSize / 2,\n        originY: 0,\n        rotation: willRotate ? -rotation : 0,\n        rectHover: true,\n        style: itemStyle,\n        onclick: onclick\n      });\n      btn.ensureState('emphasis').style = hoverStyle;\n      group.add(btn);\n      enableHoverEmphasis(btn);\n    }\n  };\n  SliderTimelineView.prototype._renderCurrentPointer = function (layoutInfo, group, axis, timelineModel) {\n    var data = timelineModel.getData();\n    var currentIndex = timelineModel.getCurrentIndex();\n    var pointerModel = data.getItemModel(currentIndex).getModel('checkpointStyle');\n    var me = this;\n    var callback = {\n      onCreate: function (pointer) {\n        pointer.draggable = true;\n        pointer.drift = bind(me._handlePointerDrag, me);\n        pointer.ondragend = bind(me._handlePointerDragend, me);\n        pointerMoveTo(pointer, me._progressLine, currentIndex, axis, timelineModel, true);\n      },\n      onUpdate: function (pointer) {\n        pointerMoveTo(pointer, me._progressLine, currentIndex, axis, timelineModel);\n      }\n    };\n    // Reuse when exists, for animation and drag.\n    this._currentPointer = giveSymbol(pointerModel, pointerModel, this._mainGroup, {}, this._currentPointer, callback);\n  };\n  SliderTimelineView.prototype._handlePlayClick = function (nextState) {\n    this._clearTimer();\n    this.api.dispatchAction({\n      type: 'timelinePlayChange',\n      playState: nextState,\n      from: this.uid\n    });\n  };\n  SliderTimelineView.prototype._handlePointerDrag = function (dx, dy, e) {\n    this._clearTimer();\n    this._pointerChangeTimeline([e.offsetX, e.offsetY]);\n  };\n  SliderTimelineView.prototype._handlePointerDragend = function (e) {\n    this._pointerChangeTimeline([e.offsetX, e.offsetY], true);\n  };\n  SliderTimelineView.prototype._pointerChangeTimeline = function (mousePos, trigger) {\n    var toCoord = this._toAxisCoord(mousePos)[0];\n    var axis = this._axis;\n    var axisExtent = numberUtil.asc(axis.getExtent().slice());\n    toCoord > axisExtent[1] && (toCoord = axisExtent[1]);\n    toCoord < axisExtent[0] && (toCoord = axisExtent[0]);\n    this._currentPointer.x = toCoord;\n    this._currentPointer.markRedraw();\n    var progressLine = this._progressLine;\n    if (progressLine) {\n      progressLine.shape.x2 = toCoord;\n      progressLine.dirty();\n    }\n    var targetDataIndex = this._findNearestTick(toCoord);\n    var timelineModel = this.model;\n    if (trigger || targetDataIndex !== timelineModel.getCurrentIndex() && timelineModel.get('realtime')) {\n      this._changeTimeline(targetDataIndex);\n    }\n  };\n  SliderTimelineView.prototype._doPlayStop = function () {\n    var _this = this;\n    this._clearTimer();\n    if (this.model.getPlayState()) {\n      this._timer = setTimeout(function () {\n        // Do not cache\n        var timelineModel = _this.model;\n        _this._changeTimeline(timelineModel.getCurrentIndex() + (timelineModel.get('rewind', true) ? -1 : 1));\n      }, this.model.get('playInterval'));\n    }\n  };\n  SliderTimelineView.prototype._toAxisCoord = function (vertex) {\n    var trans = this._mainGroup.getLocalTransform();\n    return graphic.applyTransform(vertex, trans, true);\n  };\n  SliderTimelineView.prototype._findNearestTick = function (axisCoord) {\n    var data = this.model.getData();\n    var dist = Infinity;\n    var targetDataIndex;\n    var axis = this._axis;\n    data.each(['value'], function (value, dataIndex) {\n      var coord = axis.dataToCoord(value);\n      var d = Math.abs(coord - axisCoord);\n      if (d < dist) {\n        dist = d;\n        targetDataIndex = dataIndex;\n      }\n    });\n    return targetDataIndex;\n  };\n  SliderTimelineView.prototype._clearTimer = function () {\n    if (this._timer) {\n      clearTimeout(this._timer);\n      this._timer = null;\n    }\n  };\n  SliderTimelineView.prototype._changeTimeline = function (nextIndex) {\n    var currentIndex = this.model.getCurrentIndex();\n    if (nextIndex === '+') {\n      nextIndex = currentIndex + 1;\n    } else if (nextIndex === '-') {\n      nextIndex = currentIndex - 1;\n    }\n    this.api.dispatchAction({\n      type: 'timelineChange',\n      currentIndex: nextIndex,\n      from: this.uid\n    });\n  };\n  SliderTimelineView.prototype._updateTicksStatus = function () {\n    var currentIndex = this.model.getCurrentIndex();\n    var tickSymbols = this._tickSymbols;\n    var tickLabels = this._tickLabels;\n    if (tickSymbols) {\n      for (var i = 0; i < tickSymbols.length; i++) {\n        tickSymbols && tickSymbols[i] && tickSymbols[i].toggleState('progress', i < currentIndex);\n      }\n    }\n    if (tickLabels) {\n      for (var i = 0; i < tickLabels.length; i++) {\n        tickLabels && tickLabels[i] && tickLabels[i].toggleState('progress', labelDataIndexStore(tickLabels[i]).dataIndex <= currentIndex);\n      }\n    }\n  };\n  SliderTimelineView.type = 'timeline.slider';\n  return SliderTimelineView;\n}(TimelineView);\nfunction createScaleByModel(model, axisType) {\n  axisType = axisType || model.get('type');\n  if (axisType) {\n    switch (axisType) {\n      // Buildin scale\n      case 'category':\n        return new OrdinalScale({\n          ordinalMeta: model.getCategories(),\n          extent: [Infinity, -Infinity]\n        });\n      case 'time':\n        return new TimeScale({\n          locale: model.ecModel.getLocaleModel(),\n          useUTC: model.ecModel.get('useUTC')\n        });\n      default:\n        // default to be value\n        return new IntervalScale();\n    }\n  }\n}\nfunction getViewRect(model, api) {\n  return layout.getLayoutRect(model.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  }, model.get('padding'));\n}\nfunction makeControlIcon(timelineModel, objPath, rect, opts) {\n  var style = opts.style;\n  var icon = graphic.createIcon(timelineModel.get(['controlStyle', objPath]), opts || {}, new BoundingRect(rect[0], rect[1], rect[2], rect[3]));\n  // TODO createIcon won't use style in opt.\n  if (style) {\n    icon.setStyle(style);\n  }\n  return icon;\n}\n/**\r\n * Create symbol or update symbol\r\n * opt: basic position and event handlers\r\n */\nfunction giveSymbol(hostModel, itemStyleModel, group, opt, symbol, callback) {\n  var color = itemStyleModel.get('color');\n  if (!symbol) {\n    var symbolType = hostModel.get('symbol');\n    symbol = createSymbol(symbolType, -1, -1, 2, 2, color);\n    symbol.setStyle('strokeNoScale', true);\n    group.add(symbol);\n    callback && callback.onCreate(symbol);\n  } else {\n    symbol.setColor(color);\n    group.add(symbol); // Group may be new, also need to add.\n    callback && callback.onUpdate(symbol);\n  }\n  // Style\n  var itemStyle = itemStyleModel.getItemStyle(['color']);\n  symbol.setStyle(itemStyle);\n  // Transform and events.\n  opt = merge({\n    rectHover: true,\n    z2: 100\n  }, opt, true);\n  var symbolSize = normalizeSymbolSize(hostModel.get('symbolSize'));\n  opt.scaleX = symbolSize[0] / 2;\n  opt.scaleY = symbolSize[1] / 2;\n  var symbolOffset = normalizeSymbolOffset(hostModel.get('symbolOffset'), symbolSize);\n  if (symbolOffset) {\n    opt.x = (opt.x || 0) + symbolOffset[0];\n    opt.y = (opt.y || 0) + symbolOffset[1];\n  }\n  var symbolRotate = hostModel.get('symbolRotate');\n  opt.rotation = (symbolRotate || 0) * Math.PI / 180 || 0;\n  symbol.attr(opt);\n  // FIXME\n  // (1) When symbol.style.strokeNoScale is true and updateTransform is not performed,\n  // getBoundingRect will return wrong result.\n  // (This is supposed to be resolved in zrender, but it is a little difficult to\n  // leverage performance and auto updateTransform)\n  // (2) All of ancesters of symbol do not scale, so we can just updateTransform symbol.\n  symbol.updateTransform();\n  return symbol;\n}\nfunction pointerMoveTo(pointer, progressLine, dataIndex, axis, timelineModel, noAnimation) {\n  if (pointer.dragging) {\n    return;\n  }\n  var pointerModel = timelineModel.getModel('checkpointStyle');\n  var toCoord = axis.dataToCoord(timelineModel.getData().get('value', dataIndex));\n  if (noAnimation || !pointerModel.get('animation', true)) {\n    pointer.attr({\n      x: toCoord,\n      y: 0\n    });\n    progressLine && progressLine.attr({\n      shape: {\n        x2: toCoord\n      }\n    });\n  } else {\n    var animationCfg = {\n      duration: pointerModel.get('animationDuration', true),\n      easing: pointerModel.get('animationEasing', true)\n    };\n    pointer.stopAnimation(null, true);\n    pointer.animateTo({\n      x: toCoord,\n      y: 0\n    }, animationCfg);\n    progressLine && progressLine.animateTo({\n      shape: {\n        x2: toCoord\n      }\n    }, animationCfg);\n  }\n}\nexport default SliderTimelineView;", "map": {"version": 3, "names": ["__extends", "BoundingRect", "matrix", "graphic", "createTextStyle", "layout", "TimelineView", "TimelineAxis", "createSymbol", "normalizeSymbolOffset", "normalizeSymbolSize", "numberUtil", "merge", "each", "extend", "isString", "bind", "defaults", "retrieve2", "OrdinalScale", "TimeScale", "IntervalScale", "parsePercent", "makeInner", "getECData", "enableHoverEmphasis", "createTooltipMarkup", "PI", "Math", "labelDataIndexStore", "SliderTimelineView", "_super", "_this", "apply", "arguments", "type", "prototype", "init", "ecModel", "api", "render", "timelineModel", "model", "group", "removeAll", "get", "layoutInfo_1", "_layout", "mainGroup_1", "_createGroup", "labelGroup", "axis_1", "_axis", "_createAxis", "formatTooltip", "dataIndex", "name", "scale", "get<PERSON><PERSON><PERSON>", "value", "noName", "_renderAxisLabel", "_position", "_doPlayStop", "_updateTicksStatus", "remove", "_clearTimer", "dispose", "labelPosOpt", "orient", "viewRect", "getViewRect", "parsedLabelPos", "y", "height", "getHeight", "x", "width", "getWidth", "horizontal", "top", "bottom", "vertical", "left", "right", "labelAlignMap", "labelBaselineMap", "rotationMap", "main<PERSON>ength", "controlModel", "getModel", "showControl", "controlSize", "controlGap", "sizePlusGap", "labelRotation", "playPosition", "prevBtnPosition", "nextBtnPosition", "controlPosition", "showPlayBtn", "showPrevBtn", "showNextBtn", "xLeft", "xRight", "axisExtent", "reverse", "rotation", "labelAlign", "labelBaseline", "layoutInfo", "mainGroup", "_mainGroup", "_labelGroup", "m", "create", "rotateOriginX", "rotateOriginY", "translate", "rotate", "clone", "applyTransform", "viewBound", "getBound", "mainBound", "getBoundingRect", "labelBound", "mainPosition", "labelsPosition", "mainBoundIdx", "toBound", "setPosition", "<PERSON><PERSON><PERSON><PERSON>", "targetGroup", "originX", "originY", "rect", "fromPos", "from", "to", "dimIdx", "boundIdx", "data", "getData", "axisType", "createScaleByModel", "getTicks", "mapArray", "dataExtent", "getDataExtent", "setExtent", "calcNiceTicks", "axis", "key", "newGroup", "Group", "add", "_renderAxisLine", "getExtent", "line", "Line", "shape", "x1", "y1", "x2", "y2", "style", "lineCap", "getLineStyle", "silent", "z2", "progressLine", "_progressLine", "_currentPointer", "lineWidth", "_renderAxisTick", "ticks", "_tickSymbols", "tick", "tickCoord", "dataToCoord", "itemModel", "getItemModel", "itemStyleModel", "hoverStyleModel", "progressStyleModel", "symbolOpt", "onclick", "_changeTimeline", "el", "giveSymbol", "ensureState", "getItemStyle", "ecData", "dataModel", "push", "labelModel", "getLabelModel", "labels", "getViewLabels", "_tick<PERSON><PERSON>ls", "labelItem", "tickValue", "normalLabelModel", "hoverLabelModel", "progressLabelModel", "textEl", "Text", "text", "formattedLabel", "align", "verticalAlign", "_renderControl", "itemStyle", "hoverStyle", "playState", "getPlayState", "inverse", "makeBtn", "_handlePlayClick", "position", "iconName", "willRotate", "iconSize", "btn", "makeControlIcon", "rectHover", "_renderCurrentPointer", "currentIndex", "getCurrentIndex", "pointer<PERSON>odel", "me", "callback", "onCreate", "pointer", "draggable", "drift", "_handlePointerDrag", "ondragend", "_handlePointerDragend", "pointerMoveTo", "onUpdate", "nextState", "dispatchAction", "uid", "dx", "dy", "e", "_pointerChangeTimeline", "offsetX", "offsetY", "mousePos", "trigger", "toCoord", "_toAxisCoord", "asc", "slice", "mark<PERSON><PERSON><PERSON>", "dirty", "targetDataIndex", "_findNearestTick", "_timer", "setTimeout", "vertex", "trans", "getLocalTransform", "axisCoord", "dist", "Infinity", "coord", "d", "abs", "clearTimeout", "nextIndex", "tickSymbols", "tick<PERSON><PERSON><PERSON>", "i", "length", "toggleState", "ordinalMeta", "getCategories", "extent", "locale", "getLocaleModel", "useUTC", "getLayoutRect", "getBoxLayoutParams", "obj<PERSON><PERSON>", "opts", "icon", "createIcon", "setStyle", "hostModel", "opt", "symbol", "color", "symbolType", "setColor", "symbolSize", "scaleX", "scaleY", "symbolOffset", "symbolRotate", "attr", "updateTransform", "noAnimation", "dragging", "animationCfg", "duration", "easing", "stopAnimation", "animateTo"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/component/timeline/SliderTimelineView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport * as layout from '../../util/layout.js';\nimport TimelineView from './TimelineView.js';\nimport TimelineAxis from './TimelineAxis.js';\nimport { createSymbol, normalizeSymbolOffset, normalizeSymbolSize } from '../../util/symbol.js';\nimport * as numberUtil from '../../util/number.js';\nimport { merge, each, extend, isString, bind, defaults, retrieve2 } from 'zrender/lib/core/util.js';\nimport OrdinalScale from '../../scale/Ordinal.js';\nimport TimeScale from '../../scale/Time.js';\nimport IntervalScale from '../../scale/Interval.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nimport { makeInner } from '../../util/model.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { enableHoverEmphasis } from '../../util/states.js';\nimport { createTooltipMarkup } from '../tooltip/tooltipMarkup.js';\nvar PI = Math.PI;\nvar labelDataIndexStore = makeInner();\nvar SliderTimelineView = /** @class */function (_super) {\n  __extends(SliderTimelineView, _super);\n  function SliderTimelineView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SliderTimelineView.type;\n    return _this;\n  }\n  SliderTimelineView.prototype.init = function (ecModel, api) {\n    this.api = api;\n  };\n  /**\r\n   * @override\r\n   */\n  SliderTimelineView.prototype.render = function (timelineModel, ecModel, api) {\n    this.model = timelineModel;\n    this.api = api;\n    this.ecModel = ecModel;\n    this.group.removeAll();\n    if (timelineModel.get('show', true)) {\n      var layoutInfo_1 = this._layout(timelineModel, api);\n      var mainGroup_1 = this._createGroup('_mainGroup');\n      var labelGroup = this._createGroup('_labelGroup');\n      var axis_1 = this._axis = this._createAxis(layoutInfo_1, timelineModel);\n      timelineModel.formatTooltip = function (dataIndex) {\n        var name = axis_1.scale.getLabel({\n          value: dataIndex\n        });\n        return createTooltipMarkup('nameValue', {\n          noName: true,\n          value: name\n        });\n      };\n      each(['AxisLine', 'AxisTick', 'Control', 'CurrentPointer'], function (name) {\n        this['_render' + name](layoutInfo_1, mainGroup_1, axis_1, timelineModel);\n      }, this);\n      this._renderAxisLabel(layoutInfo_1, labelGroup, axis_1, timelineModel);\n      this._position(layoutInfo_1, timelineModel);\n    }\n    this._doPlayStop();\n    this._updateTicksStatus();\n  };\n  /**\r\n   * @override\r\n   */\n  SliderTimelineView.prototype.remove = function () {\n    this._clearTimer();\n    this.group.removeAll();\n  };\n  /**\r\n   * @override\r\n   */\n  SliderTimelineView.prototype.dispose = function () {\n    this._clearTimer();\n  };\n  SliderTimelineView.prototype._layout = function (timelineModel, api) {\n    var labelPosOpt = timelineModel.get(['label', 'position']);\n    var orient = timelineModel.get('orient');\n    var viewRect = getViewRect(timelineModel, api);\n    var parsedLabelPos;\n    // Auto label offset.\n    if (labelPosOpt == null || labelPosOpt === 'auto') {\n      parsedLabelPos = orient === 'horizontal' ? viewRect.y + viewRect.height / 2 < api.getHeight() / 2 ? '-' : '+' : viewRect.x + viewRect.width / 2 < api.getWidth() / 2 ? '+' : '-';\n    } else if (isString(labelPosOpt)) {\n      parsedLabelPos = {\n        horizontal: {\n          top: '-',\n          bottom: '+'\n        },\n        vertical: {\n          left: '-',\n          right: '+'\n        }\n      }[orient][labelPosOpt];\n    } else {\n      // is number\n      parsedLabelPos = labelPosOpt;\n    }\n    var labelAlignMap = {\n      horizontal: 'center',\n      vertical: parsedLabelPos >= 0 || parsedLabelPos === '+' ? 'left' : 'right'\n    };\n    var labelBaselineMap = {\n      horizontal: parsedLabelPos >= 0 || parsedLabelPos === '+' ? 'top' : 'bottom',\n      vertical: 'middle'\n    };\n    var rotationMap = {\n      horizontal: 0,\n      vertical: PI / 2\n    };\n    // Position\n    var mainLength = orient === 'vertical' ? viewRect.height : viewRect.width;\n    var controlModel = timelineModel.getModel('controlStyle');\n    var showControl = controlModel.get('show', true);\n    var controlSize = showControl ? controlModel.get('itemSize') : 0;\n    var controlGap = showControl ? controlModel.get('itemGap') : 0;\n    var sizePlusGap = controlSize + controlGap;\n    // Special label rotate.\n    var labelRotation = timelineModel.get(['label', 'rotate']) || 0;\n    labelRotation = labelRotation * PI / 180; // To radian.\n    var playPosition;\n    var prevBtnPosition;\n    var nextBtnPosition;\n    var controlPosition = controlModel.get('position', true);\n    var showPlayBtn = showControl && controlModel.get('showPlayBtn', true);\n    var showPrevBtn = showControl && controlModel.get('showPrevBtn', true);\n    var showNextBtn = showControl && controlModel.get('showNextBtn', true);\n    var xLeft = 0;\n    var xRight = mainLength;\n    // position[0] means left, position[1] means middle.\n    if (controlPosition === 'left' || controlPosition === 'bottom') {\n      showPlayBtn && (playPosition = [0, 0], xLeft += sizePlusGap);\n      showPrevBtn && (prevBtnPosition = [xLeft, 0], xLeft += sizePlusGap);\n      showNextBtn && (nextBtnPosition = [xRight - controlSize, 0], xRight -= sizePlusGap);\n    } else {\n      // 'top' 'right'\n      showPlayBtn && (playPosition = [xRight - controlSize, 0], xRight -= sizePlusGap);\n      showPrevBtn && (prevBtnPosition = [0, 0], xLeft += sizePlusGap);\n      showNextBtn && (nextBtnPosition = [xRight - controlSize, 0], xRight -= sizePlusGap);\n    }\n    var axisExtent = [xLeft, xRight];\n    if (timelineModel.get('inverse')) {\n      axisExtent.reverse();\n    }\n    return {\n      viewRect: viewRect,\n      mainLength: mainLength,\n      orient: orient,\n      rotation: rotationMap[orient],\n      labelRotation: labelRotation,\n      labelPosOpt: parsedLabelPos,\n      labelAlign: timelineModel.get(['label', 'align']) || labelAlignMap[orient],\n      labelBaseline: timelineModel.get(['label', 'verticalAlign']) || timelineModel.get(['label', 'baseline']) || labelBaselineMap[orient],\n      // Based on mainGroup.\n      playPosition: playPosition,\n      prevBtnPosition: prevBtnPosition,\n      nextBtnPosition: nextBtnPosition,\n      axisExtent: axisExtent,\n      controlSize: controlSize,\n      controlGap: controlGap\n    };\n  };\n  SliderTimelineView.prototype._position = function (layoutInfo, timelineModel) {\n    // Position is be called finally, because bounding rect is needed for\n    // adapt content to fill viewRect (auto adapt offset).\n    // Timeline may be not all in the viewRect when 'offset' is specified\n    // as a number, because it is more appropriate that label aligns at\n    // 'offset' but not the other edge defined by viewRect.\n    var mainGroup = this._mainGroup;\n    var labelGroup = this._labelGroup;\n    var viewRect = layoutInfo.viewRect;\n    if (layoutInfo.orient === 'vertical') {\n      // transform to horizontal, inverse rotate by left-top point.\n      var m = matrix.create();\n      var rotateOriginX = viewRect.x;\n      var rotateOriginY = viewRect.y + viewRect.height;\n      matrix.translate(m, m, [-rotateOriginX, -rotateOriginY]);\n      matrix.rotate(m, m, -PI / 2);\n      matrix.translate(m, m, [rotateOriginX, rotateOriginY]);\n      viewRect = viewRect.clone();\n      viewRect.applyTransform(m);\n    }\n    var viewBound = getBound(viewRect);\n    var mainBound = getBound(mainGroup.getBoundingRect());\n    var labelBound = getBound(labelGroup.getBoundingRect());\n    var mainPosition = [mainGroup.x, mainGroup.y];\n    var labelsPosition = [labelGroup.x, labelGroup.y];\n    labelsPosition[0] = mainPosition[0] = viewBound[0][0];\n    var labelPosOpt = layoutInfo.labelPosOpt;\n    if (labelPosOpt == null || isString(labelPosOpt)) {\n      // '+' or '-'\n      var mainBoundIdx = labelPosOpt === '+' ? 0 : 1;\n      toBound(mainPosition, mainBound, viewBound, 1, mainBoundIdx);\n      toBound(labelsPosition, labelBound, viewBound, 1, 1 - mainBoundIdx);\n    } else {\n      var mainBoundIdx = labelPosOpt >= 0 ? 0 : 1;\n      toBound(mainPosition, mainBound, viewBound, 1, mainBoundIdx);\n      labelsPosition[1] = mainPosition[1] + labelPosOpt;\n    }\n    mainGroup.setPosition(mainPosition);\n    labelGroup.setPosition(labelsPosition);\n    mainGroup.rotation = labelGroup.rotation = layoutInfo.rotation;\n    setOrigin(mainGroup);\n    setOrigin(labelGroup);\n    function setOrigin(targetGroup) {\n      targetGroup.originX = viewBound[0][0] - targetGroup.x;\n      targetGroup.originY = viewBound[1][0] - targetGroup.y;\n    }\n    function getBound(rect) {\n      // [[xmin, xmax], [ymin, ymax]]\n      return [[rect.x, rect.x + rect.width], [rect.y, rect.y + rect.height]];\n    }\n    function toBound(fromPos, from, to, dimIdx, boundIdx) {\n      fromPos[dimIdx] += to[dimIdx][boundIdx] - from[dimIdx][boundIdx];\n    }\n  };\n  SliderTimelineView.prototype._createAxis = function (layoutInfo, timelineModel) {\n    var data = timelineModel.getData();\n    var axisType = timelineModel.get('axisType');\n    var scale = createScaleByModel(timelineModel, axisType);\n    // Customize scale. The `tickValue` is `dataIndex`.\n    scale.getTicks = function () {\n      return data.mapArray(['value'], function (value) {\n        return {\n          value: value\n        };\n      });\n    };\n    var dataExtent = data.getDataExtent('value');\n    scale.setExtent(dataExtent[0], dataExtent[1]);\n    scale.calcNiceTicks();\n    var axis = new TimelineAxis('value', scale, layoutInfo.axisExtent, axisType);\n    axis.model = timelineModel;\n    return axis;\n  };\n  SliderTimelineView.prototype._createGroup = function (key) {\n    var newGroup = this[key] = new graphic.Group();\n    this.group.add(newGroup);\n    return newGroup;\n  };\n  SliderTimelineView.prototype._renderAxisLine = function (layoutInfo, group, axis, timelineModel) {\n    var axisExtent = axis.getExtent();\n    if (!timelineModel.get(['lineStyle', 'show'])) {\n      return;\n    }\n    var line = new graphic.Line({\n      shape: {\n        x1: axisExtent[0],\n        y1: 0,\n        x2: axisExtent[1],\n        y2: 0\n      },\n      style: extend({\n        lineCap: 'round'\n      }, timelineModel.getModel('lineStyle').getLineStyle()),\n      silent: true,\n      z2: 1\n    });\n    group.add(line);\n    var progressLine = this._progressLine = new graphic.Line({\n      shape: {\n        x1: axisExtent[0],\n        x2: this._currentPointer ? this._currentPointer.x : axisExtent[0],\n        y1: 0,\n        y2: 0\n      },\n      style: defaults({\n        lineCap: 'round',\n        lineWidth: line.style.lineWidth\n      }, timelineModel.getModel(['progress', 'lineStyle']).getLineStyle()),\n      silent: true,\n      z2: 1\n    });\n    group.add(progressLine);\n  };\n  SliderTimelineView.prototype._renderAxisTick = function (layoutInfo, group, axis, timelineModel) {\n    var _this = this;\n    var data = timelineModel.getData();\n    // Show all ticks, despite ignoring strategy.\n    var ticks = axis.scale.getTicks();\n    this._tickSymbols = [];\n    // The value is dataIndex, see the customized scale.\n    each(ticks, function (tick) {\n      var tickCoord = axis.dataToCoord(tick.value);\n      var itemModel = data.getItemModel(tick.value);\n      var itemStyleModel = itemModel.getModel('itemStyle');\n      var hoverStyleModel = itemModel.getModel(['emphasis', 'itemStyle']);\n      var progressStyleModel = itemModel.getModel(['progress', 'itemStyle']);\n      var symbolOpt = {\n        x: tickCoord,\n        y: 0,\n        onclick: bind(_this._changeTimeline, _this, tick.value)\n      };\n      var el = giveSymbol(itemModel, itemStyleModel, group, symbolOpt);\n      el.ensureState('emphasis').style = hoverStyleModel.getItemStyle();\n      el.ensureState('progress').style = progressStyleModel.getItemStyle();\n      enableHoverEmphasis(el);\n      var ecData = getECData(el);\n      if (itemModel.get('tooltip')) {\n        ecData.dataIndex = tick.value;\n        ecData.dataModel = timelineModel;\n      } else {\n        ecData.dataIndex = ecData.dataModel = null;\n      }\n      _this._tickSymbols.push(el);\n    });\n  };\n  SliderTimelineView.prototype._renderAxisLabel = function (layoutInfo, group, axis, timelineModel) {\n    var _this = this;\n    var labelModel = axis.getLabelModel();\n    if (!labelModel.get('show')) {\n      return;\n    }\n    var data = timelineModel.getData();\n    var labels = axis.getViewLabels();\n    this._tickLabels = [];\n    each(labels, function (labelItem) {\n      // The tickValue is dataIndex, see the customized scale.\n      var dataIndex = labelItem.tickValue;\n      var itemModel = data.getItemModel(dataIndex);\n      var normalLabelModel = itemModel.getModel('label');\n      var hoverLabelModel = itemModel.getModel(['emphasis', 'label']);\n      var progressLabelModel = itemModel.getModel(['progress', 'label']);\n      var tickCoord = axis.dataToCoord(labelItem.tickValue);\n      var textEl = new graphic.Text({\n        x: tickCoord,\n        y: 0,\n        rotation: layoutInfo.labelRotation - layoutInfo.rotation,\n        onclick: bind(_this._changeTimeline, _this, dataIndex),\n        silent: false,\n        style: createTextStyle(normalLabelModel, {\n          text: labelItem.formattedLabel,\n          align: layoutInfo.labelAlign,\n          verticalAlign: layoutInfo.labelBaseline\n        })\n      });\n      textEl.ensureState('emphasis').style = createTextStyle(hoverLabelModel);\n      textEl.ensureState('progress').style = createTextStyle(progressLabelModel);\n      group.add(textEl);\n      enableHoverEmphasis(textEl);\n      labelDataIndexStore(textEl).dataIndex = dataIndex;\n      _this._tickLabels.push(textEl);\n    });\n  };\n  SliderTimelineView.prototype._renderControl = function (layoutInfo, group, axis, timelineModel) {\n    var controlSize = layoutInfo.controlSize;\n    var rotation = layoutInfo.rotation;\n    var itemStyle = timelineModel.getModel('controlStyle').getItemStyle();\n    var hoverStyle = timelineModel.getModel(['emphasis', 'controlStyle']).getItemStyle();\n    var playState = timelineModel.getPlayState();\n    var inverse = timelineModel.get('inverse', true);\n    makeBtn(layoutInfo.nextBtnPosition, 'next', bind(this._changeTimeline, this, inverse ? '-' : '+'));\n    makeBtn(layoutInfo.prevBtnPosition, 'prev', bind(this._changeTimeline, this, inverse ? '+' : '-'));\n    makeBtn(layoutInfo.playPosition, playState ? 'stop' : 'play', bind(this._handlePlayClick, this, !playState), true);\n    function makeBtn(position, iconName, onclick, willRotate) {\n      if (!position) {\n        return;\n      }\n      var iconSize = parsePercent(retrieve2(timelineModel.get(['controlStyle', iconName + 'BtnSize']), controlSize), controlSize);\n      var rect = [0, -iconSize / 2, iconSize, iconSize];\n      var btn = makeControlIcon(timelineModel, iconName + 'Icon', rect, {\n        x: position[0],\n        y: position[1],\n        originX: controlSize / 2,\n        originY: 0,\n        rotation: willRotate ? -rotation : 0,\n        rectHover: true,\n        style: itemStyle,\n        onclick: onclick\n      });\n      btn.ensureState('emphasis').style = hoverStyle;\n      group.add(btn);\n      enableHoverEmphasis(btn);\n    }\n  };\n  SliderTimelineView.prototype._renderCurrentPointer = function (layoutInfo, group, axis, timelineModel) {\n    var data = timelineModel.getData();\n    var currentIndex = timelineModel.getCurrentIndex();\n    var pointerModel = data.getItemModel(currentIndex).getModel('checkpointStyle');\n    var me = this;\n    var callback = {\n      onCreate: function (pointer) {\n        pointer.draggable = true;\n        pointer.drift = bind(me._handlePointerDrag, me);\n        pointer.ondragend = bind(me._handlePointerDragend, me);\n        pointerMoveTo(pointer, me._progressLine, currentIndex, axis, timelineModel, true);\n      },\n      onUpdate: function (pointer) {\n        pointerMoveTo(pointer, me._progressLine, currentIndex, axis, timelineModel);\n      }\n    };\n    // Reuse when exists, for animation and drag.\n    this._currentPointer = giveSymbol(pointerModel, pointerModel, this._mainGroup, {}, this._currentPointer, callback);\n  };\n  SliderTimelineView.prototype._handlePlayClick = function (nextState) {\n    this._clearTimer();\n    this.api.dispatchAction({\n      type: 'timelinePlayChange',\n      playState: nextState,\n      from: this.uid\n    });\n  };\n  SliderTimelineView.prototype._handlePointerDrag = function (dx, dy, e) {\n    this._clearTimer();\n    this._pointerChangeTimeline([e.offsetX, e.offsetY]);\n  };\n  SliderTimelineView.prototype._handlePointerDragend = function (e) {\n    this._pointerChangeTimeline([e.offsetX, e.offsetY], true);\n  };\n  SliderTimelineView.prototype._pointerChangeTimeline = function (mousePos, trigger) {\n    var toCoord = this._toAxisCoord(mousePos)[0];\n    var axis = this._axis;\n    var axisExtent = numberUtil.asc(axis.getExtent().slice());\n    toCoord > axisExtent[1] && (toCoord = axisExtent[1]);\n    toCoord < axisExtent[0] && (toCoord = axisExtent[0]);\n    this._currentPointer.x = toCoord;\n    this._currentPointer.markRedraw();\n    var progressLine = this._progressLine;\n    if (progressLine) {\n      progressLine.shape.x2 = toCoord;\n      progressLine.dirty();\n    }\n    var targetDataIndex = this._findNearestTick(toCoord);\n    var timelineModel = this.model;\n    if (trigger || targetDataIndex !== timelineModel.getCurrentIndex() && timelineModel.get('realtime')) {\n      this._changeTimeline(targetDataIndex);\n    }\n  };\n  SliderTimelineView.prototype._doPlayStop = function () {\n    var _this = this;\n    this._clearTimer();\n    if (this.model.getPlayState()) {\n      this._timer = setTimeout(function () {\n        // Do not cache\n        var timelineModel = _this.model;\n        _this._changeTimeline(timelineModel.getCurrentIndex() + (timelineModel.get('rewind', true) ? -1 : 1));\n      }, this.model.get('playInterval'));\n    }\n  };\n  SliderTimelineView.prototype._toAxisCoord = function (vertex) {\n    var trans = this._mainGroup.getLocalTransform();\n    return graphic.applyTransform(vertex, trans, true);\n  };\n  SliderTimelineView.prototype._findNearestTick = function (axisCoord) {\n    var data = this.model.getData();\n    var dist = Infinity;\n    var targetDataIndex;\n    var axis = this._axis;\n    data.each(['value'], function (value, dataIndex) {\n      var coord = axis.dataToCoord(value);\n      var d = Math.abs(coord - axisCoord);\n      if (d < dist) {\n        dist = d;\n        targetDataIndex = dataIndex;\n      }\n    });\n    return targetDataIndex;\n  };\n  SliderTimelineView.prototype._clearTimer = function () {\n    if (this._timer) {\n      clearTimeout(this._timer);\n      this._timer = null;\n    }\n  };\n  SliderTimelineView.prototype._changeTimeline = function (nextIndex) {\n    var currentIndex = this.model.getCurrentIndex();\n    if (nextIndex === '+') {\n      nextIndex = currentIndex + 1;\n    } else if (nextIndex === '-') {\n      nextIndex = currentIndex - 1;\n    }\n    this.api.dispatchAction({\n      type: 'timelineChange',\n      currentIndex: nextIndex,\n      from: this.uid\n    });\n  };\n  SliderTimelineView.prototype._updateTicksStatus = function () {\n    var currentIndex = this.model.getCurrentIndex();\n    var tickSymbols = this._tickSymbols;\n    var tickLabels = this._tickLabels;\n    if (tickSymbols) {\n      for (var i = 0; i < tickSymbols.length; i++) {\n        tickSymbols && tickSymbols[i] && tickSymbols[i].toggleState('progress', i < currentIndex);\n      }\n    }\n    if (tickLabels) {\n      for (var i = 0; i < tickLabels.length; i++) {\n        tickLabels && tickLabels[i] && tickLabels[i].toggleState('progress', labelDataIndexStore(tickLabels[i]).dataIndex <= currentIndex);\n      }\n    }\n  };\n  SliderTimelineView.type = 'timeline.slider';\n  return SliderTimelineView;\n}(TimelineView);\nfunction createScaleByModel(model, axisType) {\n  axisType = axisType || model.get('type');\n  if (axisType) {\n    switch (axisType) {\n      // Buildin scale\n      case 'category':\n        return new OrdinalScale({\n          ordinalMeta: model.getCategories(),\n          extent: [Infinity, -Infinity]\n        });\n      case 'time':\n        return new TimeScale({\n          locale: model.ecModel.getLocaleModel(),\n          useUTC: model.ecModel.get('useUTC')\n        });\n      default:\n        // default to be value\n        return new IntervalScale();\n    }\n  }\n}\nfunction getViewRect(model, api) {\n  return layout.getLayoutRect(model.getBoxLayoutParams(), {\n    width: api.getWidth(),\n    height: api.getHeight()\n  }, model.get('padding'));\n}\nfunction makeControlIcon(timelineModel, objPath, rect, opts) {\n  var style = opts.style;\n  var icon = graphic.createIcon(timelineModel.get(['controlStyle', objPath]), opts || {}, new BoundingRect(rect[0], rect[1], rect[2], rect[3]));\n  // TODO createIcon won't use style in opt.\n  if (style) {\n    icon.setStyle(style);\n  }\n  return icon;\n}\n/**\r\n * Create symbol or update symbol\r\n * opt: basic position and event handlers\r\n */\nfunction giveSymbol(hostModel, itemStyleModel, group, opt, symbol, callback) {\n  var color = itemStyleModel.get('color');\n  if (!symbol) {\n    var symbolType = hostModel.get('symbol');\n    symbol = createSymbol(symbolType, -1, -1, 2, 2, color);\n    symbol.setStyle('strokeNoScale', true);\n    group.add(symbol);\n    callback && callback.onCreate(symbol);\n  } else {\n    symbol.setColor(color);\n    group.add(symbol); // Group may be new, also need to add.\n    callback && callback.onUpdate(symbol);\n  }\n  // Style\n  var itemStyle = itemStyleModel.getItemStyle(['color']);\n  symbol.setStyle(itemStyle);\n  // Transform and events.\n  opt = merge({\n    rectHover: true,\n    z2: 100\n  }, opt, true);\n  var symbolSize = normalizeSymbolSize(hostModel.get('symbolSize'));\n  opt.scaleX = symbolSize[0] / 2;\n  opt.scaleY = symbolSize[1] / 2;\n  var symbolOffset = normalizeSymbolOffset(hostModel.get('symbolOffset'), symbolSize);\n  if (symbolOffset) {\n    opt.x = (opt.x || 0) + symbolOffset[0];\n    opt.y = (opt.y || 0) + symbolOffset[1];\n  }\n  var symbolRotate = hostModel.get('symbolRotate');\n  opt.rotation = (symbolRotate || 0) * Math.PI / 180 || 0;\n  symbol.attr(opt);\n  // FIXME\n  // (1) When symbol.style.strokeNoScale is true and updateTransform is not performed,\n  // getBoundingRect will return wrong result.\n  // (This is supposed to be resolved in zrender, but it is a little difficult to\n  // leverage performance and auto updateTransform)\n  // (2) All of ancesters of symbol do not scale, so we can just updateTransform symbol.\n  symbol.updateTransform();\n  return symbol;\n}\nfunction pointerMoveTo(pointer, progressLine, dataIndex, axis, timelineModel, noAnimation) {\n  if (pointer.dragging) {\n    return;\n  }\n  var pointerModel = timelineModel.getModel('checkpointStyle');\n  var toCoord = axis.dataToCoord(timelineModel.getData().get('value', dataIndex));\n  if (noAnimation || !pointerModel.get('animation', true)) {\n    pointer.attr({\n      x: toCoord,\n      y: 0\n    });\n    progressLine && progressLine.attr({\n      shape: {\n        x2: toCoord\n      }\n    });\n  } else {\n    var animationCfg = {\n      duration: pointerModel.get('animationDuration', true),\n      easing: pointerModel.get('animationEasing', true)\n    };\n    pointer.stopAnimation(null, true);\n    pointer.animateTo({\n      x: toCoord,\n      y: 0\n    }, animationCfg);\n    progressLine && progressLine.animateTo({\n      shape: {\n        x2: toCoord\n      }\n    }, animationCfg);\n  }\n}\nexport default SliderTimelineView;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,OAAO,KAAKC,MAAM,MAAM,4BAA4B;AACpD,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAO,KAAKC,MAAM,MAAM,sBAAsB;AAC9C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,SAASC,YAAY,EAAEC,qBAAqB,EAAEC,mBAAmB,QAAQ,sBAAsB;AAC/F,OAAO,KAAKC,UAAU,MAAM,sBAAsB;AAClD,SAASC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,0BAA0B;AACnG,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,IAAIC,EAAE,GAAGC,IAAI,CAACD,EAAE;AAChB,IAAIE,mBAAmB,GAAGN,SAAS,CAAC,CAAC;AACrC,IAAIO,kBAAkB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACtD/B,SAAS,CAAC8B,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,kBAAkB,CAACK,IAAI;IACpC,OAAOH,KAAK;EACd;EACAF,kBAAkB,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,OAAO,EAAEC,GAAG,EAAE;IAC1D,IAAI,CAACA,GAAG,GAAGA,GAAG;EAChB,CAAC;EACD;AACF;AACA;EACET,kBAAkB,CAACM,SAAS,CAACI,MAAM,GAAG,UAAUC,aAAa,EAAEH,OAAO,EAAEC,GAAG,EAAE;IAC3E,IAAI,CAACG,KAAK,GAAGD,aAAa;IAC1B,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACK,KAAK,CAACC,SAAS,CAAC,CAAC;IACtB,IAAIH,aAAa,CAACI,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;MACnC,IAAIC,YAAY,GAAG,IAAI,CAACC,OAAO,CAACN,aAAa,EAAEF,GAAG,CAAC;MACnD,IAAIS,WAAW,GAAG,IAAI,CAACC,YAAY,CAAC,YAAY,CAAC;MACjD,IAAIC,UAAU,GAAG,IAAI,CAACD,YAAY,CAAC,aAAa,CAAC;MACjD,IAAIE,MAAM,GAAG,IAAI,CAACC,KAAK,GAAG,IAAI,CAACC,WAAW,CAACP,YAAY,EAAEL,aAAa,CAAC;MACvEA,aAAa,CAACa,aAAa,GAAG,UAAUC,SAAS,EAAE;QACjD,IAAIC,IAAI,GAAGL,MAAM,CAACM,KAAK,CAACC,QAAQ,CAAC;UAC/BC,KAAK,EAAEJ;QACT,CAAC,CAAC;QACF,OAAO7B,mBAAmB,CAAC,WAAW,EAAE;UACtCkC,MAAM,EAAE,IAAI;UACZD,KAAK,EAAEH;QACT,CAAC,CAAC;MACJ,CAAC;MACD3C,IAAI,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,CAAC,EAAE,UAAU2C,IAAI,EAAE;QAC1E,IAAI,CAAC,SAAS,GAAGA,IAAI,CAAC,CAACV,YAAY,EAAEE,WAAW,EAAEG,MAAM,EAAEV,aAAa,CAAC;MAC1E,CAAC,EAAE,IAAI,CAAC;MACR,IAAI,CAACoB,gBAAgB,CAACf,YAAY,EAAEI,UAAU,EAAEC,MAAM,EAAEV,aAAa,CAAC;MACtE,IAAI,CAACqB,SAAS,CAAChB,YAAY,EAAEL,aAAa,CAAC;IAC7C;IACA,IAAI,CAACsB,WAAW,CAAC,CAAC;IAClB,IAAI,CAACC,kBAAkB,CAAC,CAAC;EAC3B,CAAC;EACD;AACF;AACA;EACElC,kBAAkB,CAACM,SAAS,CAAC6B,MAAM,GAAG,YAAY;IAChD,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACvB,KAAK,CAACC,SAAS,CAAC,CAAC;EACxB,CAAC;EACD;AACF;AACA;EACEd,kBAAkB,CAACM,SAAS,CAAC+B,OAAO,GAAG,YAAY;IACjD,IAAI,CAACD,WAAW,CAAC,CAAC;EACpB,CAAC;EACDpC,kBAAkB,CAACM,SAAS,CAACW,OAAO,GAAG,UAAUN,aAAa,EAAEF,GAAG,EAAE;IACnE,IAAI6B,WAAW,GAAG3B,aAAa,CAACI,GAAG,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IAC1D,IAAIwB,MAAM,GAAG5B,aAAa,CAACI,GAAG,CAAC,QAAQ,CAAC;IACxC,IAAIyB,QAAQ,GAAGC,WAAW,CAAC9B,aAAa,EAAEF,GAAG,CAAC;IAC9C,IAAIiC,cAAc;IAClB;IACA,IAAIJ,WAAW,IAAI,IAAI,IAAIA,WAAW,KAAK,MAAM,EAAE;MACjDI,cAAc,GAAGH,MAAM,KAAK,YAAY,GAAGC,QAAQ,CAACG,CAAC,GAAGH,QAAQ,CAACI,MAAM,GAAG,CAAC,GAAGnC,GAAG,CAACoC,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGL,QAAQ,CAACM,CAAC,GAAGN,QAAQ,CAACO,KAAK,GAAG,CAAC,GAAGtC,GAAG,CAACuC,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;IAClL,CAAC,MAAM,IAAI/D,QAAQ,CAACqD,WAAW,CAAC,EAAE;MAChCI,cAAc,GAAG;QACfO,UAAU,EAAE;UACVC,GAAG,EAAE,GAAG;UACRC,MAAM,EAAE;QACV,CAAC;QACDC,QAAQ,EAAE;UACRC,IAAI,EAAE,GAAG;UACTC,KAAK,EAAE;QACT;MACF,CAAC,CAACf,MAAM,CAAC,CAACD,WAAW,CAAC;IACxB,CAAC,MAAM;MACL;MACAI,cAAc,GAAGJ,WAAW;IAC9B;IACA,IAAIiB,aAAa,GAAG;MAClBN,UAAU,EAAE,QAAQ;MACpBG,QAAQ,EAAEV,cAAc,IAAI,CAAC,IAAIA,cAAc,KAAK,GAAG,GAAG,MAAM,GAAG;IACrE,CAAC;IACD,IAAIc,gBAAgB,GAAG;MACrBP,UAAU,EAAEP,cAAc,IAAI,CAAC,IAAIA,cAAc,KAAK,GAAG,GAAG,KAAK,GAAG,QAAQ;MAC5EU,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIK,WAAW,GAAG;MAChBR,UAAU,EAAE,CAAC;MACbG,QAAQ,EAAEvD,EAAE,GAAG;IACjB,CAAC;IACD;IACA,IAAI6D,UAAU,GAAGnB,MAAM,KAAK,UAAU,GAAGC,QAAQ,CAACI,MAAM,GAAGJ,QAAQ,CAACO,KAAK;IACzE,IAAIY,YAAY,GAAGhD,aAAa,CAACiD,QAAQ,CAAC,cAAc,CAAC;IACzD,IAAIC,WAAW,GAAGF,YAAY,CAAC5C,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC;IAChD,IAAI+C,WAAW,GAAGD,WAAW,GAAGF,YAAY,CAAC5C,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC;IAChE,IAAIgD,UAAU,GAAGF,WAAW,GAAGF,YAAY,CAAC5C,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC;IAC9D,IAAIiD,WAAW,GAAGF,WAAW,GAAGC,UAAU;IAC1C;IACA,IAAIE,aAAa,GAAGtD,aAAa,CAACI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC;IAC/DkD,aAAa,GAAGA,aAAa,GAAGpE,EAAE,GAAG,GAAG,CAAC,CAAC;IAC1C,IAAIqE,YAAY;IAChB,IAAIC,eAAe;IACnB,IAAIC,eAAe;IACnB,IAAIC,eAAe,GAAGV,YAAY,CAAC5C,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC;IACxD,IAAIuD,WAAW,GAAGT,WAAW,IAAIF,YAAY,CAAC5C,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC;IACtE,IAAIwD,WAAW,GAAGV,WAAW,IAAIF,YAAY,CAAC5C,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC;IACtE,IAAIyD,WAAW,GAAGX,WAAW,IAAIF,YAAY,CAAC5C,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC;IACtE,IAAI0D,KAAK,GAAG,CAAC;IACb,IAAIC,MAAM,GAAGhB,UAAU;IACvB;IACA,IAAIW,eAAe,KAAK,MAAM,IAAIA,eAAe,KAAK,QAAQ,EAAE;MAC9DC,WAAW,KAAKJ,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEO,KAAK,IAAIT,WAAW,CAAC;MAC5DO,WAAW,KAAKJ,eAAe,GAAG,CAACM,KAAK,EAAE,CAAC,CAAC,EAAEA,KAAK,IAAIT,WAAW,CAAC;MACnEQ,WAAW,KAAKJ,eAAe,GAAG,CAACM,MAAM,GAAGZ,WAAW,EAAE,CAAC,CAAC,EAAEY,MAAM,IAAIV,WAAW,CAAC;IACrF,CAAC,MAAM;MACL;MACAM,WAAW,KAAKJ,YAAY,GAAG,CAACQ,MAAM,GAAGZ,WAAW,EAAE,CAAC,CAAC,EAAEY,MAAM,IAAIV,WAAW,CAAC;MAChFO,WAAW,KAAKJ,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEM,KAAK,IAAIT,WAAW,CAAC;MAC/DQ,WAAW,KAAKJ,eAAe,GAAG,CAACM,MAAM,GAAGZ,WAAW,EAAE,CAAC,CAAC,EAAEY,MAAM,IAAIV,WAAW,CAAC;IACrF;IACA,IAAIW,UAAU,GAAG,CAACF,KAAK,EAAEC,MAAM,CAAC;IAChC,IAAI/D,aAAa,CAACI,GAAG,CAAC,SAAS,CAAC,EAAE;MAChC4D,UAAU,CAACC,OAAO,CAAC,CAAC;IACtB;IACA,OAAO;MACLpC,QAAQ,EAAEA,QAAQ;MAClBkB,UAAU,EAAEA,UAAU;MACtBnB,MAAM,EAAEA,MAAM;MACdsC,QAAQ,EAAEpB,WAAW,CAAClB,MAAM,CAAC;MAC7B0B,aAAa,EAAEA,aAAa;MAC5B3B,WAAW,EAAEI,cAAc;MAC3BoC,UAAU,EAAEnE,aAAa,CAACI,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,IAAIwC,aAAa,CAAChB,MAAM,CAAC;MAC1EwC,aAAa,EAAEpE,aAAa,CAACI,GAAG,CAAC,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,IAAIJ,aAAa,CAACI,GAAG,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,IAAIyC,gBAAgB,CAACjB,MAAM,CAAC;MACpI;MACA2B,YAAY,EAAEA,YAAY;MAC1BC,eAAe,EAAEA,eAAe;MAChCC,eAAe,EAAEA,eAAe;MAChCO,UAAU,EAAEA,UAAU;MACtBb,WAAW,EAAEA,WAAW;MACxBC,UAAU,EAAEA;IACd,CAAC;EACH,CAAC;EACD/D,kBAAkB,CAACM,SAAS,CAAC0B,SAAS,GAAG,UAAUgD,UAAU,EAAErE,aAAa,EAAE;IAC5E;IACA;IACA;IACA;IACA;IACA,IAAIsE,SAAS,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAI9D,UAAU,GAAG,IAAI,CAAC+D,WAAW;IACjC,IAAI3C,QAAQ,GAAGwC,UAAU,CAACxC,QAAQ;IAClC,IAAIwC,UAAU,CAACzC,MAAM,KAAK,UAAU,EAAE;MACpC;MACA,IAAI6C,CAAC,GAAGhH,MAAM,CAACiH,MAAM,CAAC,CAAC;MACvB,IAAIC,aAAa,GAAG9C,QAAQ,CAACM,CAAC;MAC9B,IAAIyC,aAAa,GAAG/C,QAAQ,CAACG,CAAC,GAAGH,QAAQ,CAACI,MAAM;MAChDxE,MAAM,CAACoH,SAAS,CAACJ,CAAC,EAAEA,CAAC,EAAE,CAAC,CAACE,aAAa,EAAE,CAACC,aAAa,CAAC,CAAC;MACxDnH,MAAM,CAACqH,MAAM,CAACL,CAAC,EAAEA,CAAC,EAAE,CAACvF,EAAE,GAAG,CAAC,CAAC;MAC5BzB,MAAM,CAACoH,SAAS,CAACJ,CAAC,EAAEA,CAAC,EAAE,CAACE,aAAa,EAAEC,aAAa,CAAC,CAAC;MACtD/C,QAAQ,GAAGA,QAAQ,CAACkD,KAAK,CAAC,CAAC;MAC3BlD,QAAQ,CAACmD,cAAc,CAACP,CAAC,CAAC;IAC5B;IACA,IAAIQ,SAAS,GAAGC,QAAQ,CAACrD,QAAQ,CAAC;IAClC,IAAIsD,SAAS,GAAGD,QAAQ,CAACZ,SAAS,CAACc,eAAe,CAAC,CAAC,CAAC;IACrD,IAAIC,UAAU,GAAGH,QAAQ,CAACzE,UAAU,CAAC2E,eAAe,CAAC,CAAC,CAAC;IACvD,IAAIE,YAAY,GAAG,CAAChB,SAAS,CAACnC,CAAC,EAAEmC,SAAS,CAACtC,CAAC,CAAC;IAC7C,IAAIuD,cAAc,GAAG,CAAC9E,UAAU,CAAC0B,CAAC,EAAE1B,UAAU,CAACuB,CAAC,CAAC;IACjDuD,cAAc,CAAC,CAAC,CAAC,GAAGD,YAAY,CAAC,CAAC,CAAC,GAAGL,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,IAAItD,WAAW,GAAG0C,UAAU,CAAC1C,WAAW;IACxC,IAAIA,WAAW,IAAI,IAAI,IAAIrD,QAAQ,CAACqD,WAAW,CAAC,EAAE;MAChD;MACA,IAAI6D,YAAY,GAAG7D,WAAW,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;MAC9C8D,OAAO,CAACH,YAAY,EAAEH,SAAS,EAAEF,SAAS,EAAE,CAAC,EAAEO,YAAY,CAAC;MAC5DC,OAAO,CAACF,cAAc,EAAEF,UAAU,EAAEJ,SAAS,EAAE,CAAC,EAAE,CAAC,GAAGO,YAAY,CAAC;IACrE,CAAC,MAAM;MACL,IAAIA,YAAY,GAAG7D,WAAW,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;MAC3C8D,OAAO,CAACH,YAAY,EAAEH,SAAS,EAAEF,SAAS,EAAE,CAAC,EAAEO,YAAY,CAAC;MAC5DD,cAAc,CAAC,CAAC,CAAC,GAAGD,YAAY,CAAC,CAAC,CAAC,GAAG3D,WAAW;IACnD;IACA2C,SAAS,CAACoB,WAAW,CAACJ,YAAY,CAAC;IACnC7E,UAAU,CAACiF,WAAW,CAACH,cAAc,CAAC;IACtCjB,SAAS,CAACJ,QAAQ,GAAGzD,UAAU,CAACyD,QAAQ,GAAGG,UAAU,CAACH,QAAQ;IAC9DyB,SAAS,CAACrB,SAAS,CAAC;IACpBqB,SAAS,CAAClF,UAAU,CAAC;IACrB,SAASkF,SAASA,CAACC,WAAW,EAAE;MAC9BA,WAAW,CAACC,OAAO,GAAGZ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGW,WAAW,CAACzD,CAAC;MACrDyD,WAAW,CAACE,OAAO,GAAGb,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGW,WAAW,CAAC5D,CAAC;IACvD;IACA,SAASkD,QAAQA,CAACa,IAAI,EAAE;MACtB;MACA,OAAO,CAAC,CAACA,IAAI,CAAC5D,CAAC,EAAE4D,IAAI,CAAC5D,CAAC,GAAG4D,IAAI,CAAC3D,KAAK,CAAC,EAAE,CAAC2D,IAAI,CAAC/D,CAAC,EAAE+D,IAAI,CAAC/D,CAAC,GAAG+D,IAAI,CAAC9D,MAAM,CAAC,CAAC;IACxE;IACA,SAASwD,OAAOA,CAACO,OAAO,EAAEC,IAAI,EAAEC,EAAE,EAAEC,MAAM,EAAEC,QAAQ,EAAE;MACpDJ,OAAO,CAACG,MAAM,CAAC,IAAID,EAAE,CAACC,MAAM,CAAC,CAACC,QAAQ,CAAC,GAAGH,IAAI,CAACE,MAAM,CAAC,CAACC,QAAQ,CAAC;IAClE;EACF,CAAC;EACD/G,kBAAkB,CAACM,SAAS,CAACiB,WAAW,GAAG,UAAUyD,UAAU,EAAErE,aAAa,EAAE;IAC9E,IAAIqG,IAAI,GAAGrG,aAAa,CAACsG,OAAO,CAAC,CAAC;IAClC,IAAIC,QAAQ,GAAGvG,aAAa,CAACI,GAAG,CAAC,UAAU,CAAC;IAC5C,IAAIY,KAAK,GAAGwF,kBAAkB,CAACxG,aAAa,EAAEuG,QAAQ,CAAC;IACvD;IACAvF,KAAK,CAACyF,QAAQ,GAAG,YAAY;MAC3B,OAAOJ,IAAI,CAACK,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE,UAAUxF,KAAK,EAAE;QAC/C,OAAO;UACLA,KAAK,EAAEA;QACT,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IACD,IAAIyF,UAAU,GAAGN,IAAI,CAACO,aAAa,CAAC,OAAO,CAAC;IAC5C5F,KAAK,CAAC6F,SAAS,CAACF,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC7C3F,KAAK,CAAC8F,aAAa,CAAC,CAAC;IACrB,IAAIC,IAAI,GAAG,IAAIjJ,YAAY,CAAC,OAAO,EAAEkD,KAAK,EAAEqD,UAAU,CAACL,UAAU,EAAEuC,QAAQ,CAAC;IAC5EQ,IAAI,CAAC9G,KAAK,GAAGD,aAAa;IAC1B,OAAO+G,IAAI;EACb,CAAC;EACD1H,kBAAkB,CAACM,SAAS,CAACa,YAAY,GAAG,UAAUwG,GAAG,EAAE;IACzD,IAAIC,QAAQ,GAAG,IAAI,CAACD,GAAG,CAAC,GAAG,IAAItJ,OAAO,CAACwJ,KAAK,CAAC,CAAC;IAC9C,IAAI,CAAChH,KAAK,CAACiH,GAAG,CAACF,QAAQ,CAAC;IACxB,OAAOA,QAAQ;EACjB,CAAC;EACD5H,kBAAkB,CAACM,SAAS,CAACyH,eAAe,GAAG,UAAU/C,UAAU,EAAEnE,KAAK,EAAE6G,IAAI,EAAE/G,aAAa,EAAE;IAC/F,IAAIgE,UAAU,GAAG+C,IAAI,CAACM,SAAS,CAAC,CAAC;IACjC,IAAI,CAACrH,aAAa,CAACI,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,EAAE;MAC7C;IACF;IACA,IAAIkH,IAAI,GAAG,IAAI5J,OAAO,CAAC6J,IAAI,CAAC;MAC1BC,KAAK,EAAE;QACLC,EAAE,EAAEzD,UAAU,CAAC,CAAC,CAAC;QACjB0D,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE3D,UAAU,CAAC,CAAC,CAAC;QACjB4D,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAExJ,MAAM,CAAC;QACZyJ,OAAO,EAAE;MACX,CAAC,EAAE9H,aAAa,CAACiD,QAAQ,CAAC,WAAW,CAAC,CAAC8E,YAAY,CAAC,CAAC,CAAC;MACtDC,MAAM,EAAE,IAAI;MACZC,EAAE,EAAE;IACN,CAAC,CAAC;IACF/H,KAAK,CAACiH,GAAG,CAACG,IAAI,CAAC;IACf,IAAIY,YAAY,GAAG,IAAI,CAACC,aAAa,GAAG,IAAIzK,OAAO,CAAC6J,IAAI,CAAC;MACvDC,KAAK,EAAE;QACLC,EAAE,EAAEzD,UAAU,CAAC,CAAC,CAAC;QACjB2D,EAAE,EAAE,IAAI,CAACS,eAAe,GAAG,IAAI,CAACA,eAAe,CAACjG,CAAC,GAAG6B,UAAU,CAAC,CAAC,CAAC;QACjE0D,EAAE,EAAE,CAAC;QACLE,EAAE,EAAE;MACN,CAAC;MACDC,KAAK,EAAErJ,QAAQ,CAAC;QACdsJ,OAAO,EAAE,OAAO;QAChBO,SAAS,EAAEf,IAAI,CAACO,KAAK,CAACQ;MACxB,CAAC,EAAErI,aAAa,CAACiD,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC8E,YAAY,CAAC,CAAC,CAAC;MACpEC,MAAM,EAAE,IAAI;MACZC,EAAE,EAAE;IACN,CAAC,CAAC;IACF/H,KAAK,CAACiH,GAAG,CAACe,YAAY,CAAC;EACzB,CAAC;EACD7I,kBAAkB,CAACM,SAAS,CAAC2I,eAAe,GAAG,UAAUjE,UAAU,EAAEnE,KAAK,EAAE6G,IAAI,EAAE/G,aAAa,EAAE;IAC/F,IAAIT,KAAK,GAAG,IAAI;IAChB,IAAI8G,IAAI,GAAGrG,aAAa,CAACsG,OAAO,CAAC,CAAC;IAClC;IACA,IAAIiC,KAAK,GAAGxB,IAAI,CAAC/F,KAAK,CAACyF,QAAQ,CAAC,CAAC;IACjC,IAAI,CAAC+B,YAAY,GAAG,EAAE;IACtB;IACApK,IAAI,CAACmK,KAAK,EAAE,UAAUE,IAAI,EAAE;MAC1B,IAAIC,SAAS,GAAG3B,IAAI,CAAC4B,WAAW,CAACF,IAAI,CAACvH,KAAK,CAAC;MAC5C,IAAI0H,SAAS,GAAGvC,IAAI,CAACwC,YAAY,CAACJ,IAAI,CAACvH,KAAK,CAAC;MAC7C,IAAI4H,cAAc,GAAGF,SAAS,CAAC3F,QAAQ,CAAC,WAAW,CAAC;MACpD,IAAI8F,eAAe,GAAGH,SAAS,CAAC3F,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;MACnE,IAAI+F,kBAAkB,GAAGJ,SAAS,CAAC3F,QAAQ,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;MACtE,IAAIgG,SAAS,GAAG;QACd9G,CAAC,EAAEuG,SAAS;QACZ1G,CAAC,EAAE,CAAC;QACJkH,OAAO,EAAE3K,IAAI,CAACgB,KAAK,CAAC4J,eAAe,EAAE5J,KAAK,EAAEkJ,IAAI,CAACvH,KAAK;MACxD,CAAC;MACD,IAAIkI,EAAE,GAAGC,UAAU,CAACT,SAAS,EAAEE,cAAc,EAAE5I,KAAK,EAAE+I,SAAS,CAAC;MAChEG,EAAE,CAACE,WAAW,CAAC,UAAU,CAAC,CAACzB,KAAK,GAAGkB,eAAe,CAACQ,YAAY,CAAC,CAAC;MACjEH,EAAE,CAACE,WAAW,CAAC,UAAU,CAAC,CAACzB,KAAK,GAAGmB,kBAAkB,CAACO,YAAY,CAAC,CAAC;MACpEvK,mBAAmB,CAACoK,EAAE,CAAC;MACvB,IAAII,MAAM,GAAGzK,SAAS,CAACqK,EAAE,CAAC;MAC1B,IAAIR,SAAS,CAACxI,GAAG,CAAC,SAAS,CAAC,EAAE;QAC5BoJ,MAAM,CAAC1I,SAAS,GAAG2H,IAAI,CAACvH,KAAK;QAC7BsI,MAAM,CAACC,SAAS,GAAGzJ,aAAa;MAClC,CAAC,MAAM;QACLwJ,MAAM,CAAC1I,SAAS,GAAG0I,MAAM,CAACC,SAAS,GAAG,IAAI;MAC5C;MACAlK,KAAK,CAACiJ,YAAY,CAACkB,IAAI,CAACN,EAAE,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC;EACD/J,kBAAkB,CAACM,SAAS,CAACyB,gBAAgB,GAAG,UAAUiD,UAAU,EAAEnE,KAAK,EAAE6G,IAAI,EAAE/G,aAAa,EAAE;IAChG,IAAIT,KAAK,GAAG,IAAI;IAChB,IAAIoK,UAAU,GAAG5C,IAAI,CAAC6C,aAAa,CAAC,CAAC;IACrC,IAAI,CAACD,UAAU,CAACvJ,GAAG,CAAC,MAAM,CAAC,EAAE;MAC3B;IACF;IACA,IAAIiG,IAAI,GAAGrG,aAAa,CAACsG,OAAO,CAAC,CAAC;IAClC,IAAIuD,MAAM,GAAG9C,IAAI,CAAC+C,aAAa,CAAC,CAAC;IACjC,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB3L,IAAI,CAACyL,MAAM,EAAE,UAAUG,SAAS,EAAE;MAChC;MACA,IAAIlJ,SAAS,GAAGkJ,SAAS,CAACC,SAAS;MACnC,IAAIrB,SAAS,GAAGvC,IAAI,CAACwC,YAAY,CAAC/H,SAAS,CAAC;MAC5C,IAAIoJ,gBAAgB,GAAGtB,SAAS,CAAC3F,QAAQ,CAAC,OAAO,CAAC;MAClD,IAAIkH,eAAe,GAAGvB,SAAS,CAAC3F,QAAQ,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;MAC/D,IAAImH,kBAAkB,GAAGxB,SAAS,CAAC3F,QAAQ,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;MAClE,IAAIyF,SAAS,GAAG3B,IAAI,CAAC4B,WAAW,CAACqB,SAAS,CAACC,SAAS,CAAC;MACrD,IAAII,MAAM,GAAG,IAAI3M,OAAO,CAAC4M,IAAI,CAAC;QAC5BnI,CAAC,EAAEuG,SAAS;QACZ1G,CAAC,EAAE,CAAC;QACJkC,QAAQ,EAAEG,UAAU,CAACf,aAAa,GAAGe,UAAU,CAACH,QAAQ;QACxDgF,OAAO,EAAE3K,IAAI,CAACgB,KAAK,CAAC4J,eAAe,EAAE5J,KAAK,EAAEuB,SAAS,CAAC;QACtDkH,MAAM,EAAE,KAAK;QACbH,KAAK,EAAElK,eAAe,CAACuM,gBAAgB,EAAE;UACvCK,IAAI,EAAEP,SAAS,CAACQ,cAAc;UAC9BC,KAAK,EAAEpG,UAAU,CAACF,UAAU;UAC5BuG,aAAa,EAAErG,UAAU,CAACD;QAC5B,CAAC;MACH,CAAC,CAAC;MACFiG,MAAM,CAACf,WAAW,CAAC,UAAU,CAAC,CAACzB,KAAK,GAAGlK,eAAe,CAACwM,eAAe,CAAC;MACvEE,MAAM,CAACf,WAAW,CAAC,UAAU,CAAC,CAACzB,KAAK,GAAGlK,eAAe,CAACyM,kBAAkB,CAAC;MAC1ElK,KAAK,CAACiH,GAAG,CAACkD,MAAM,CAAC;MACjBrL,mBAAmB,CAACqL,MAAM,CAAC;MAC3BjL,mBAAmB,CAACiL,MAAM,CAAC,CAACvJ,SAAS,GAAGA,SAAS;MACjDvB,KAAK,CAACwK,WAAW,CAACL,IAAI,CAACW,MAAM,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC;EACDhL,kBAAkB,CAACM,SAAS,CAACgL,cAAc,GAAG,UAAUtG,UAAU,EAAEnE,KAAK,EAAE6G,IAAI,EAAE/G,aAAa,EAAE;IAC9F,IAAImD,WAAW,GAAGkB,UAAU,CAAClB,WAAW;IACxC,IAAIe,QAAQ,GAAGG,UAAU,CAACH,QAAQ;IAClC,IAAI0G,SAAS,GAAG5K,aAAa,CAACiD,QAAQ,CAAC,cAAc,CAAC,CAACsG,YAAY,CAAC,CAAC;IACrE,IAAIsB,UAAU,GAAG7K,aAAa,CAACiD,QAAQ,CAAC,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,CAACsG,YAAY,CAAC,CAAC;IACpF,IAAIuB,SAAS,GAAG9K,aAAa,CAAC+K,YAAY,CAAC,CAAC;IAC5C,IAAIC,OAAO,GAAGhL,aAAa,CAACI,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;IAChD6K,OAAO,CAAC5G,UAAU,CAACZ,eAAe,EAAE,MAAM,EAAElF,IAAI,CAAC,IAAI,CAAC4K,eAAe,EAAE,IAAI,EAAE6B,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;IAClGC,OAAO,CAAC5G,UAAU,CAACb,eAAe,EAAE,MAAM,EAAEjF,IAAI,CAAC,IAAI,CAAC4K,eAAe,EAAE,IAAI,EAAE6B,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;IAClGC,OAAO,CAAC5G,UAAU,CAACd,YAAY,EAAEuH,SAAS,GAAG,MAAM,GAAG,MAAM,EAAEvM,IAAI,CAAC,IAAI,CAAC2M,gBAAgB,EAAE,IAAI,EAAE,CAACJ,SAAS,CAAC,EAAE,IAAI,CAAC;IAClH,SAASG,OAAOA,CAACE,QAAQ,EAAEC,QAAQ,EAAElC,OAAO,EAAEmC,UAAU,EAAE;MACxD,IAAI,CAACF,QAAQ,EAAE;QACb;MACF;MACA,IAAIG,QAAQ,GAAGzM,YAAY,CAACJ,SAAS,CAACuB,aAAa,CAACI,GAAG,CAAC,CAAC,cAAc,EAAEgL,QAAQ,GAAG,SAAS,CAAC,CAAC,EAAEjI,WAAW,CAAC,EAAEA,WAAW,CAAC;MAC3H,IAAI4C,IAAI,GAAG,CAAC,CAAC,EAAE,CAACuF,QAAQ,GAAG,CAAC,EAAEA,QAAQ,EAAEA,QAAQ,CAAC;MACjD,IAAIC,GAAG,GAAGC,eAAe,CAACxL,aAAa,EAAEoL,QAAQ,GAAG,MAAM,EAAErF,IAAI,EAAE;QAChE5D,CAAC,EAAEgJ,QAAQ,CAAC,CAAC,CAAC;QACdnJ,CAAC,EAAEmJ,QAAQ,CAAC,CAAC,CAAC;QACdtF,OAAO,EAAE1C,WAAW,GAAG,CAAC;QACxB2C,OAAO,EAAE,CAAC;QACV5B,QAAQ,EAAEmH,UAAU,GAAG,CAACnH,QAAQ,GAAG,CAAC;QACpCuH,SAAS,EAAE,IAAI;QACf5D,KAAK,EAAE+C,SAAS;QAChB1B,OAAO,EAAEA;MACX,CAAC,CAAC;MACFqC,GAAG,CAACjC,WAAW,CAAC,UAAU,CAAC,CAACzB,KAAK,GAAGgD,UAAU;MAC9C3K,KAAK,CAACiH,GAAG,CAACoE,GAAG,CAAC;MACdvM,mBAAmB,CAACuM,GAAG,CAAC;IAC1B;EACF,CAAC;EACDlM,kBAAkB,CAACM,SAAS,CAAC+L,qBAAqB,GAAG,UAAUrH,UAAU,EAAEnE,KAAK,EAAE6G,IAAI,EAAE/G,aAAa,EAAE;IACrG,IAAIqG,IAAI,GAAGrG,aAAa,CAACsG,OAAO,CAAC,CAAC;IAClC,IAAIqF,YAAY,GAAG3L,aAAa,CAAC4L,eAAe,CAAC,CAAC;IAClD,IAAIC,YAAY,GAAGxF,IAAI,CAACwC,YAAY,CAAC8C,YAAY,CAAC,CAAC1I,QAAQ,CAAC,iBAAiB,CAAC;IAC9E,IAAI6I,EAAE,GAAG,IAAI;IACb,IAAIC,QAAQ,GAAG;MACbC,QAAQ,EAAE,SAAAA,CAAUC,OAAO,EAAE;QAC3BA,OAAO,CAACC,SAAS,GAAG,IAAI;QACxBD,OAAO,CAACE,KAAK,GAAG5N,IAAI,CAACuN,EAAE,CAACM,kBAAkB,EAAEN,EAAE,CAAC;QAC/CG,OAAO,CAACI,SAAS,GAAG9N,IAAI,CAACuN,EAAE,CAACQ,qBAAqB,EAAER,EAAE,CAAC;QACtDS,aAAa,CAACN,OAAO,EAAEH,EAAE,CAAC3D,aAAa,EAAEwD,YAAY,EAAE5E,IAAI,EAAE/G,aAAa,EAAE,IAAI,CAAC;MACnF,CAAC;MACDwM,QAAQ,EAAE,SAAAA,CAAUP,OAAO,EAAE;QAC3BM,aAAa,CAACN,OAAO,EAAEH,EAAE,CAAC3D,aAAa,EAAEwD,YAAY,EAAE5E,IAAI,EAAE/G,aAAa,CAAC;MAC7E;IACF,CAAC;IACD;IACA,IAAI,CAACoI,eAAe,GAAGiB,UAAU,CAACwC,YAAY,EAAEA,YAAY,EAAE,IAAI,CAACtH,UAAU,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC6D,eAAe,EAAE2D,QAAQ,CAAC;EACpH,CAAC;EACD1M,kBAAkB,CAACM,SAAS,CAACuL,gBAAgB,GAAG,UAAUuB,SAAS,EAAE;IACnE,IAAI,CAAChL,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC3B,GAAG,CAAC4M,cAAc,CAAC;MACtBhN,IAAI,EAAE,oBAAoB;MAC1BoL,SAAS,EAAE2B,SAAS;MACpBxG,IAAI,EAAE,IAAI,CAAC0G;IACb,CAAC,CAAC;EACJ,CAAC;EACDtN,kBAAkB,CAACM,SAAS,CAACyM,kBAAkB,GAAG,UAAUQ,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAE;IACrE,IAAI,CAACrL,WAAW,CAAC,CAAC;IAClB,IAAI,CAACsL,sBAAsB,CAAC,CAACD,CAAC,CAACE,OAAO,EAAEF,CAAC,CAACG,OAAO,CAAC,CAAC;EACrD,CAAC;EACD5N,kBAAkB,CAACM,SAAS,CAAC2M,qBAAqB,GAAG,UAAUQ,CAAC,EAAE;IAChE,IAAI,CAACC,sBAAsB,CAAC,CAACD,CAAC,CAACE,OAAO,EAAEF,CAAC,CAACG,OAAO,CAAC,EAAE,IAAI,CAAC;EAC3D,CAAC;EACD5N,kBAAkB,CAACM,SAAS,CAACoN,sBAAsB,GAAG,UAAUG,QAAQ,EAAEC,OAAO,EAAE;IACjF,IAAIC,OAAO,GAAG,IAAI,CAACC,YAAY,CAACH,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAInG,IAAI,GAAG,IAAI,CAACpG,KAAK;IACrB,IAAIqD,UAAU,GAAG9F,UAAU,CAACoP,GAAG,CAACvG,IAAI,CAACM,SAAS,CAAC,CAAC,CAACkG,KAAK,CAAC,CAAC,CAAC;IACzDH,OAAO,GAAGpJ,UAAU,CAAC,CAAC,CAAC,KAAKoJ,OAAO,GAAGpJ,UAAU,CAAC,CAAC,CAAC,CAAC;IACpDoJ,OAAO,GAAGpJ,UAAU,CAAC,CAAC,CAAC,KAAKoJ,OAAO,GAAGpJ,UAAU,CAAC,CAAC,CAAC,CAAC;IACpD,IAAI,CAACoE,eAAe,CAACjG,CAAC,GAAGiL,OAAO;IAChC,IAAI,CAAChF,eAAe,CAACoF,UAAU,CAAC,CAAC;IACjC,IAAItF,YAAY,GAAG,IAAI,CAACC,aAAa;IACrC,IAAID,YAAY,EAAE;MAChBA,YAAY,CAACV,KAAK,CAACG,EAAE,GAAGyF,OAAO;MAC/BlF,YAAY,CAACuF,KAAK,CAAC,CAAC;IACtB;IACA,IAAIC,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAACP,OAAO,CAAC;IACpD,IAAIpN,aAAa,GAAG,IAAI,CAACC,KAAK;IAC9B,IAAIkN,OAAO,IAAIO,eAAe,KAAK1N,aAAa,CAAC4L,eAAe,CAAC,CAAC,IAAI5L,aAAa,CAACI,GAAG,CAAC,UAAU,CAAC,EAAE;MACnG,IAAI,CAAC+I,eAAe,CAACuE,eAAe,CAAC;IACvC;EACF,CAAC;EACDrO,kBAAkB,CAACM,SAAS,CAAC2B,WAAW,GAAG,YAAY;IACrD,IAAI/B,KAAK,GAAG,IAAI;IAChB,IAAI,CAACkC,WAAW,CAAC,CAAC;IAClB,IAAI,IAAI,CAACxB,KAAK,CAAC8K,YAAY,CAAC,CAAC,EAAE;MAC7B,IAAI,CAAC6C,MAAM,GAAGC,UAAU,CAAC,YAAY;QACnC;QACA,IAAI7N,aAAa,GAAGT,KAAK,CAACU,KAAK;QAC/BV,KAAK,CAAC4J,eAAe,CAACnJ,aAAa,CAAC4L,eAAe,CAAC,CAAC,IAAI5L,aAAa,CAACI,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;MACvG,CAAC,EAAE,IAAI,CAACH,KAAK,CAACG,GAAG,CAAC,cAAc,CAAC,CAAC;IACpC;EACF,CAAC;EACDf,kBAAkB,CAACM,SAAS,CAAC0N,YAAY,GAAG,UAAUS,MAAM,EAAE;IAC5D,IAAIC,KAAK,GAAG,IAAI,CAACxJ,UAAU,CAACyJ,iBAAiB,CAAC,CAAC;IAC/C,OAAOtQ,OAAO,CAACsH,cAAc,CAAC8I,MAAM,EAAEC,KAAK,EAAE,IAAI,CAAC;EACpD,CAAC;EACD1O,kBAAkB,CAACM,SAAS,CAACgO,gBAAgB,GAAG,UAAUM,SAAS,EAAE;IACnE,IAAI5H,IAAI,GAAG,IAAI,CAACpG,KAAK,CAACqG,OAAO,CAAC,CAAC;IAC/B,IAAI4H,IAAI,GAAGC,QAAQ;IACnB,IAAIT,eAAe;IACnB,IAAI3G,IAAI,GAAG,IAAI,CAACpG,KAAK;IACrB0F,IAAI,CAACjI,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,UAAU8C,KAAK,EAAEJ,SAAS,EAAE;MAC/C,IAAIsN,KAAK,GAAGrH,IAAI,CAAC4B,WAAW,CAACzH,KAAK,CAAC;MACnC,IAAImN,CAAC,GAAGlP,IAAI,CAACmP,GAAG,CAACF,KAAK,GAAGH,SAAS,CAAC;MACnC,IAAII,CAAC,GAAGH,IAAI,EAAE;QACZA,IAAI,GAAGG,CAAC;QACRX,eAAe,GAAG5M,SAAS;MAC7B;IACF,CAAC,CAAC;IACF,OAAO4M,eAAe;EACxB,CAAC;EACDrO,kBAAkB,CAACM,SAAS,CAAC8B,WAAW,GAAG,YAAY;IACrD,IAAI,IAAI,CAACmM,MAAM,EAAE;MACfW,YAAY,CAAC,IAAI,CAACX,MAAM,CAAC;MACzB,IAAI,CAACA,MAAM,GAAG,IAAI;IACpB;EACF,CAAC;EACDvO,kBAAkB,CAACM,SAAS,CAACwJ,eAAe,GAAG,UAAUqF,SAAS,EAAE;IAClE,IAAI7C,YAAY,GAAG,IAAI,CAAC1L,KAAK,CAAC2L,eAAe,CAAC,CAAC;IAC/C,IAAI4C,SAAS,KAAK,GAAG,EAAE;MACrBA,SAAS,GAAG7C,YAAY,GAAG,CAAC;IAC9B,CAAC,MAAM,IAAI6C,SAAS,KAAK,GAAG,EAAE;MAC5BA,SAAS,GAAG7C,YAAY,GAAG,CAAC;IAC9B;IACA,IAAI,CAAC7L,GAAG,CAAC4M,cAAc,CAAC;MACtBhN,IAAI,EAAE,gBAAgB;MACtBiM,YAAY,EAAE6C,SAAS;MACvBvI,IAAI,EAAE,IAAI,CAAC0G;IACb,CAAC,CAAC;EACJ,CAAC;EACDtN,kBAAkB,CAACM,SAAS,CAAC4B,kBAAkB,GAAG,YAAY;IAC5D,IAAIoK,YAAY,GAAG,IAAI,CAAC1L,KAAK,CAAC2L,eAAe,CAAC,CAAC;IAC/C,IAAI6C,WAAW,GAAG,IAAI,CAACjG,YAAY;IACnC,IAAIkG,UAAU,GAAG,IAAI,CAAC3E,WAAW;IACjC,IAAI0E,WAAW,EAAE;MACf,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QAC3CF,WAAW,IAAIA,WAAW,CAACE,CAAC,CAAC,IAAIF,WAAW,CAACE,CAAC,CAAC,CAACE,WAAW,CAAC,UAAU,EAAEF,CAAC,GAAGhD,YAAY,CAAC;MAC3F;IACF;IACA,IAAI+C,UAAU,EAAE;MACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1CD,UAAU,IAAIA,UAAU,CAACC,CAAC,CAAC,IAAID,UAAU,CAACC,CAAC,CAAC,CAACE,WAAW,CAAC,UAAU,EAAEzP,mBAAmB,CAACsP,UAAU,CAACC,CAAC,CAAC,CAAC,CAAC7N,SAAS,IAAI6K,YAAY,CAAC;MACpI;IACF;EACF,CAAC;EACDtM,kBAAkB,CAACK,IAAI,GAAG,iBAAiB;EAC3C,OAAOL,kBAAkB;AAC3B,CAAC,CAACxB,YAAY,CAAC;AACf,SAAS2I,kBAAkBA,CAACvG,KAAK,EAAEsG,QAAQ,EAAE;EAC3CA,QAAQ,GAAGA,QAAQ,IAAItG,KAAK,CAACG,GAAG,CAAC,MAAM,CAAC;EACxC,IAAImG,QAAQ,EAAE;IACZ,QAAQA,QAAQ;MACd;MACA,KAAK,UAAU;QACb,OAAO,IAAI7H,YAAY,CAAC;UACtBoQ,WAAW,EAAE7O,KAAK,CAAC8O,aAAa,CAAC,CAAC;UAClCC,MAAM,EAAE,CAACb,QAAQ,EAAE,CAACA,QAAQ;QAC9B,CAAC,CAAC;MACJ,KAAK,MAAM;QACT,OAAO,IAAIxP,SAAS,CAAC;UACnBsQ,MAAM,EAAEhP,KAAK,CAACJ,OAAO,CAACqP,cAAc,CAAC,CAAC;UACtCC,MAAM,EAAElP,KAAK,CAACJ,OAAO,CAACO,GAAG,CAAC,QAAQ;QACpC,CAAC,CAAC;MACJ;QACE;QACA,OAAO,IAAIxB,aAAa,CAAC,CAAC;IAC9B;EACF;AACF;AACA,SAASkD,WAAWA,CAAC7B,KAAK,EAAEH,GAAG,EAAE;EAC/B,OAAOlC,MAAM,CAACwR,aAAa,CAACnP,KAAK,CAACoP,kBAAkB,CAAC,CAAC,EAAE;IACtDjN,KAAK,EAAEtC,GAAG,CAACuC,QAAQ,CAAC,CAAC;IACrBJ,MAAM,EAAEnC,GAAG,CAACoC,SAAS,CAAC;EACxB,CAAC,EAAEjC,KAAK,CAACG,GAAG,CAAC,SAAS,CAAC,CAAC;AAC1B;AACA,SAASoL,eAAeA,CAACxL,aAAa,EAAEsP,OAAO,EAAEvJ,IAAI,EAAEwJ,IAAI,EAAE;EAC3D,IAAI1H,KAAK,GAAG0H,IAAI,CAAC1H,KAAK;EACtB,IAAI2H,IAAI,GAAG9R,OAAO,CAAC+R,UAAU,CAACzP,aAAa,CAACI,GAAG,CAAC,CAAC,cAAc,EAAEkP,OAAO,CAAC,CAAC,EAAEC,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI/R,YAAY,CAACuI,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7I;EACA,IAAI8B,KAAK,EAAE;IACT2H,IAAI,CAACE,QAAQ,CAAC7H,KAAK,CAAC;EACtB;EACA,OAAO2H,IAAI;AACb;AACA;AACA;AACA;AACA;AACA,SAASnG,UAAUA,CAACsG,SAAS,EAAE7G,cAAc,EAAE5I,KAAK,EAAE0P,GAAG,EAAEC,MAAM,EAAE9D,QAAQ,EAAE;EAC3E,IAAI+D,KAAK,GAAGhH,cAAc,CAAC1I,GAAG,CAAC,OAAO,CAAC;EACvC,IAAI,CAACyP,MAAM,EAAE;IACX,IAAIE,UAAU,GAAGJ,SAAS,CAACvP,GAAG,CAAC,QAAQ,CAAC;IACxCyP,MAAM,GAAG9R,YAAY,CAACgS,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAED,KAAK,CAAC;IACtDD,MAAM,CAACH,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC;IACtCxP,KAAK,CAACiH,GAAG,CAAC0I,MAAM,CAAC;IACjB9D,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,CAAC6D,MAAM,CAAC;EACvC,CAAC,MAAM;IACLA,MAAM,CAACG,QAAQ,CAACF,KAAK,CAAC;IACtB5P,KAAK,CAACiH,GAAG,CAAC0I,MAAM,CAAC,CAAC,CAAC;IACnB9D,QAAQ,IAAIA,QAAQ,CAACS,QAAQ,CAACqD,MAAM,CAAC;EACvC;EACA;EACA,IAAIjF,SAAS,GAAG9B,cAAc,CAACS,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC;EACtDsG,MAAM,CAACH,QAAQ,CAAC9E,SAAS,CAAC;EAC1B;EACAgF,GAAG,GAAGzR,KAAK,CAAC;IACVsN,SAAS,EAAE,IAAI;IACfxD,EAAE,EAAE;EACN,CAAC,EAAE2H,GAAG,EAAE,IAAI,CAAC;EACb,IAAIK,UAAU,GAAGhS,mBAAmB,CAAC0R,SAAS,CAACvP,GAAG,CAAC,YAAY,CAAC,CAAC;EACjEwP,GAAG,CAACM,MAAM,GAAGD,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;EAC9BL,GAAG,CAACO,MAAM,GAAGF,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;EAC9B,IAAIG,YAAY,GAAGpS,qBAAqB,CAAC2R,SAAS,CAACvP,GAAG,CAAC,cAAc,CAAC,EAAE6P,UAAU,CAAC;EACnF,IAAIG,YAAY,EAAE;IAChBR,GAAG,CAACzN,CAAC,GAAG,CAACyN,GAAG,CAACzN,CAAC,IAAI,CAAC,IAAIiO,YAAY,CAAC,CAAC,CAAC;IACtCR,GAAG,CAAC5N,CAAC,GAAG,CAAC4N,GAAG,CAAC5N,CAAC,IAAI,CAAC,IAAIoO,YAAY,CAAC,CAAC,CAAC;EACxC;EACA,IAAIC,YAAY,GAAGV,SAAS,CAACvP,GAAG,CAAC,cAAc,CAAC;EAChDwP,GAAG,CAAC1L,QAAQ,GAAG,CAACmM,YAAY,IAAI,CAAC,IAAIlR,IAAI,CAACD,EAAE,GAAG,GAAG,IAAI,CAAC;EACvD2Q,MAAM,CAACS,IAAI,CAACV,GAAG,CAAC;EAChB;EACA;EACA;EACA;EACA;EACA;EACAC,MAAM,CAACU,eAAe,CAAC,CAAC;EACxB,OAAOV,MAAM;AACf;AACA,SAAStD,aAAaA,CAACN,OAAO,EAAE/D,YAAY,EAAEpH,SAAS,EAAEiG,IAAI,EAAE/G,aAAa,EAAEwQ,WAAW,EAAE;EACzF,IAAIvE,OAAO,CAACwE,QAAQ,EAAE;IACpB;EACF;EACA,IAAI5E,YAAY,GAAG7L,aAAa,CAACiD,QAAQ,CAAC,iBAAiB,CAAC;EAC5D,IAAImK,OAAO,GAAGrG,IAAI,CAAC4B,WAAW,CAAC3I,aAAa,CAACsG,OAAO,CAAC,CAAC,CAAClG,GAAG,CAAC,OAAO,EAAEU,SAAS,CAAC,CAAC;EAC/E,IAAI0P,WAAW,IAAI,CAAC3E,YAAY,CAACzL,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;IACvD6L,OAAO,CAACqE,IAAI,CAAC;MACXnO,CAAC,EAAEiL,OAAO;MACVpL,CAAC,EAAE;IACL,CAAC,CAAC;IACFkG,YAAY,IAAIA,YAAY,CAACoI,IAAI,CAAC;MAChC9I,KAAK,EAAE;QACLG,EAAE,EAAEyF;MACN;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAIsD,YAAY,GAAG;MACjBC,QAAQ,EAAE9E,YAAY,CAACzL,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC;MACrDwQ,MAAM,EAAE/E,YAAY,CAACzL,GAAG,CAAC,iBAAiB,EAAE,IAAI;IAClD,CAAC;IACD6L,OAAO,CAAC4E,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC;IACjC5E,OAAO,CAAC6E,SAAS,CAAC;MAChB3O,CAAC,EAAEiL,OAAO;MACVpL,CAAC,EAAE;IACL,CAAC,EAAE0O,YAAY,CAAC;IAChBxI,YAAY,IAAIA,YAAY,CAAC4I,SAAS,CAAC;MACrCtJ,KAAK,EAAE;QACLG,EAAE,EAAEyF;MACN;IACF,CAAC,EAAEsD,YAAY,CAAC;EAClB;AACF;AACA,eAAerR,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}