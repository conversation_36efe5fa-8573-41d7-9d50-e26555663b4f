{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport './plugins/element.js';\nimport axios from 'axios';\nVue.prototype.$http = axios;\naxios.defaults.baseURL = 'http://localhost:8080/api';\nVue.config.productionTip = false;\nnew Vue({\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "axios", "prototype", "$http", "defaults", "baseURL", "config", "productionTip", "render", "h", "$mount"], "sources": ["E:/AllProject/datafront/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport './plugins/element.js'\nimport axios from 'axios'\n\nVue.prototype.$http = axios\naxios.defaults.baseURL = 'http://localhost:8080/api'\n\nVue.config.productionTip = false\n\nnew Vue({\n  render: h => h(App),\n}).$mount('#app')"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAO,sBAAsB;AAC7B,OAAOC,KAAK,MAAM,OAAO;AAEzBF,GAAG,CAACG,SAAS,CAACC,KAAK,GAAGF,KAAK;AAC3BA,KAAK,CAACG,QAAQ,CAACC,OAAO,GAAG,2BAA2B;AAEpDN,GAAG,CAACO,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIR,GAAG,CAAC;EACNS,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACT,GAAG;AACpB,CAAC,CAAC,CAACU,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}