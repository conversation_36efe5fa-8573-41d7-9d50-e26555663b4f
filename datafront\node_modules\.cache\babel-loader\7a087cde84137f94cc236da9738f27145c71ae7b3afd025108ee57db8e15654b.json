{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport parallelPreprocessor from '../../coord/parallel/parallelPreprocessor.js';\nimport ParallelView from './ParallelView.js';\nimport ParallelModel from '../../coord/parallel/ParallelModel.js';\nimport parallelCoordSysCreator from '../../coord/parallel/parallelCreator.js';\nimport axisModelCreator from '../../coord/axisModelCreator.js';\nimport ParallelAxisModel from '../../coord/parallel/AxisModel.js';\nimport ParallelAxisView from '../axis/ParallelAxisView.js';\nimport { installParallelActions } from '../axis/parallelAxisAction.js';\nvar defaultAxisOption = {\n  type: 'value',\n  areaSelectStyle: {\n    width: 20,\n    borderWidth: 1,\n    borderColor: 'rgba(160,197,232)',\n    color: 'rgba(160,197,232)',\n    opacity: 0.3\n  },\n  realtime: true,\n  z: 10\n};\nexport function install(registers) {\n  registers.registerComponentView(ParallelView);\n  registers.registerComponentModel(ParallelModel);\n  registers.registerCoordinateSystem('parallel', parallelCoordSysCreator);\n  registers.registerPreprocessor(parallelPreprocessor);\n  registers.registerComponentModel(ParallelAxisModel);\n  registers.registerComponentView(ParallelAxisView);\n  axisModelCreator(registers, 'parallel', ParallelAxisModel, defaultAxisOption);\n  installParallelActions(registers);\n}", "map": {"version": 3, "names": ["parallelPreprocessor", "<PERSON><PERSON><PERSON><PERSON>iew", "ParallelModel", "parallelCoordSysCreator", "axisModelCreator", "ParallelAxisModel", "ParallelAxisView", "installParallelActions", "defaultAxisOption", "type", "areaSelectStyle", "width", "borderWidth", "borderColor", "color", "opacity", "realtime", "z", "install", "registers", "registerComponentView", "registerComponentModel", "registerCoordinateSystem", "registerPreprocessor"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/parallel/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport parallelPreprocessor from '../../coord/parallel/parallelPreprocessor.js';\nimport ParallelView from './ParallelView.js';\nimport ParallelModel from '../../coord/parallel/ParallelModel.js';\nimport parallelCoordSysCreator from '../../coord/parallel/parallelCreator.js';\nimport axisModelCreator from '../../coord/axisModelCreator.js';\nimport ParallelAxisModel from '../../coord/parallel/AxisModel.js';\nimport ParallelAxisView from '../axis/ParallelAxisView.js';\nimport { installParallelActions } from '../axis/parallelAxisAction.js';\nvar defaultAxisOption = {\n  type: 'value',\n  areaSelectStyle: {\n    width: 20,\n    borderWidth: 1,\n    borderColor: 'rgba(160,197,232)',\n    color: 'rgba(160,197,232)',\n    opacity: 0.3\n  },\n  realtime: true,\n  z: 10\n};\nexport function install(registers) {\n  registers.registerComponentView(ParallelView);\n  registers.registerComponentModel(ParallelModel);\n  registers.registerCoordinateSystem('parallel', parallelCoordSysCreator);\n  registers.registerPreprocessor(parallelPreprocessor);\n  registers.registerComponentModel(ParallelAxisModel);\n  registers.registerComponentView(ParallelAxisView);\n  axisModelCreator(registers, 'parallel', ParallelAxisModel, defaultAxisOption);\n  installParallelActions(registers);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,sBAAsB,QAAQ,+BAA+B;AACtE,IAAIC,iBAAiB,GAAG;EACtBC,IAAI,EAAE,OAAO;EACbC,eAAe,EAAE;IACfC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE,mBAAmB;IAChCC,KAAK,EAAE,mBAAmB;IAC1BC,OAAO,EAAE;EACX,CAAC;EACDC,QAAQ,EAAE,IAAI;EACdC,CAAC,EAAE;AACL,CAAC;AACD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,qBAAqB,CAACnB,YAAY,CAAC;EAC7CkB,SAAS,CAACE,sBAAsB,CAACnB,aAAa,CAAC;EAC/CiB,SAAS,CAACG,wBAAwB,CAAC,UAAU,EAAEnB,uBAAuB,CAAC;EACvEgB,SAAS,CAACI,oBAAoB,CAACvB,oBAAoB,CAAC;EACpDmB,SAAS,CAACE,sBAAsB,CAAChB,iBAAiB,CAAC;EACnDc,SAAS,CAACC,qBAAqB,CAACd,gBAAgB,CAAC;EACjDF,gBAAgB,CAACe,SAAS,EAAE,UAAU,EAAEd,iBAAiB,EAAEG,iBAAiB,CAAC;EAC7ED,sBAAsB,CAACY,SAAS,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}