{"ast": null, "code": "'use strict';\n\nexport default function parseProtocol(url) {\n  const match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n  return match && match[1] || '';\n}", "map": {"version": 3, "names": ["parseProtocol", "url", "match", "exec"], "sources": ["D:/FastBI/datafront/node_modules/axios/lib/helpers/parseProtocol.js"], "sourcesContent": ["'use strict';\n\nexport default function parseProtocol(url) {\n  const match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n  return match && match[1] || '';\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,eAAe,SAASA,aAAaA,CAACC,GAAG,EAAE;EACzC,MAAMC,KAAK,GAAG,2BAA2B,CAACC,IAAI,CAACF,GAAG,CAAC;EACnD,OAAOC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}