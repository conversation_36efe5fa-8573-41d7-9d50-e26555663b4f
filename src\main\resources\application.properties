# application.properties

# ?????
server.port=8088

# DataEase API??
dataease.api.base-url=http://localhost:8080
dataease.api.token=your-api-token-here

# ??????
#langchain4j.community.dashscope.chat-model.api-key=sk-fa71d79ef68841f1b7908ebaef224c76
#langchain4j.community.dashscope.chat-model.model-name=qwen-turbo

# ??????
langchain4j.community.dashscope.streaming-chat-model.api-key=sk-fa71d79ef68841f1b7908ebaef224c76
langchain4j.community.dashscope.streaming-chat-model.model-name=qwen-plus-2025-07-14

## ????????
#langchain4j.community.dashscope.embedding-model.api-key=sk-fa71d79ef68841f1b7908ebaef224c76
#langchain4j.community.dashscope.embedding-model.model-name=text-embedding-v4

# MongoDB??
spring.data.mongodb.uri=mongodb://localhost:27017/indicator_qa_db

# ????
logging.level.com.dataease=INFO
logging.level.dev.langchain4j=INFO

dataease.token=eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6IjBmMWEyZjA4LTQwNzctNDU0NS1iNWJlLTgyZGFmY2I2ZGQxMSJ9.Xqu9_lEzWlm9wTDJrSd-XZlYY8b8yktzTaZoAtGRtQc_4PJxlwMdToSaCnIV4GvWJRfgcanogLxxI2RUbdLdDQ

dataease.url=http://************:8011/de2api/chartData/getData

spring.data.redis.host=localhost
spring.data.redis.port=6379