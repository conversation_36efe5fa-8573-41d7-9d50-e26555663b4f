{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\n/**\r\n * Can only be called after coordinate system creation stage.\r\n * (Can be called before coordinate system update stage).\r\n */\nexport function layout(gridModel, axisModel, opt) {\n  opt = opt || {};\n  var grid = gridModel.coordinateSystem;\n  var axis = axisModel.axis;\n  var layout = {};\n  var otherAxisOnZeroOf = axis.getAxesOnZeroOf()[0];\n  var rawAxisPosition = axis.position;\n  var axisPosition = otherAxisOnZeroOf ? 'onZero' : rawAxisPosition;\n  var axisDim = axis.dim;\n  var rect = grid.getRect();\n  var rectBound = [rect.x, rect.x + rect.width, rect.y, rect.y + rect.height];\n  var idx = {\n    left: 0,\n    right: 1,\n    top: 0,\n    bottom: 1,\n    onZero: 2\n  };\n  var axisOffset = axisModel.get('offset') || 0;\n  var posBound = axisDim === 'x' ? [rectBound[2] - axisOffset, rectBound[3] + axisOffset] : [rectBound[0] - axisOffset, rectBound[1] + axisOffset];\n  if (otherAxisOnZeroOf) {\n    var onZeroCoord = otherAxisOnZeroOf.toGlobalCoord(otherAxisOnZeroOf.dataToCoord(0));\n    posBound[idx.onZero] = Math.max(Math.min(onZeroCoord, posBound[1]), posBound[0]);\n  }\n  // Axis position\n  layout.position = [axisDim === 'y' ? posBound[idx[axisPosition]] : rectBound[0], axisDim === 'x' ? posBound[idx[axisPosition]] : rectBound[3]];\n  // Axis rotation\n  layout.rotation = Math.PI / 2 * (axisDim === 'x' ? 0 : 1);\n  // Tick and label direction, x y is axisDim\n  var dirMap = {\n    top: -1,\n    bottom: 1,\n    left: -1,\n    right: 1\n  };\n  layout.labelDirection = layout.tickDirection = layout.nameDirection = dirMap[rawAxisPosition];\n  layout.labelOffset = otherAxisOnZeroOf ? posBound[idx[rawAxisPosition]] - posBound[idx.onZero] : 0;\n  if (axisModel.get(['axisTick', 'inside'])) {\n    layout.tickDirection = -layout.tickDirection;\n  }\n  if (zrUtil.retrieve(opt.labelInside, axisModel.get(['axisLabel', 'inside']))) {\n    layout.labelDirection = -layout.labelDirection;\n  }\n  // Special label rotation\n  var labelRotate = axisModel.get(['axisLabel', 'rotate']);\n  layout.labelRotate = axisPosition === 'top' ? -labelRotate : labelRotate;\n  // Over splitLine and splitArea\n  layout.z2 = 1;\n  return layout;\n}\nexport function isCartesian2DSeries(seriesModel) {\n  return seriesModel.get('coordinateSystem') === 'cartesian2d';\n}\nexport function findAxisModels(seriesModel) {\n  var axisModelMap = {\n    xAxisModel: null,\n    yAxisModel: null\n  };\n  zrUtil.each(axisModelMap, function (v, key) {\n    var axisType = key.replace(/Model$/, '');\n    var axisModel = seriesModel.getReferringComponents(axisType, SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!axisModel) {\n        throw new Error(axisType + ' \"' + zrUtil.retrieve3(seriesModel.get(axisType + 'Index'), seriesModel.get(axisType + 'Id'), 0) + '\" not found');\n      }\n    }\n    axisModelMap[key] = axisModel;\n  });\n  return axisModelMap;\n}", "map": {"version": 3, "names": ["zrUtil", "SINGLE_REFERRING", "layout", "gridModel", "axisModel", "opt", "grid", "coordinateSystem", "axis", "otherAxisOnZeroOf", "getAxesOnZeroOf", "rawAxisPosition", "position", "axisPosition", "axisDim", "dim", "rect", "getRect", "rectBound", "x", "width", "y", "height", "idx", "left", "right", "top", "bottom", "onZero", "axisOffset", "get", "posBound", "onZeroCoord", "toGlobalCoord", "dataToCoord", "Math", "max", "min", "rotation", "PI", "dirMap", "labelDirection", "tickDirection", "nameDirection", "labelOffset", "retrieve", "labelInside", "labelRotate", "z2", "isCartesian2DSeries", "seriesModel", "findAxisModels", "axisModelMap", "xAxisModel", "yAxisModel", "each", "v", "key", "axisType", "replace", "getReferringComponents", "models", "process", "env", "NODE_ENV", "Error", "retrieve3"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/coord/cartesian/cartesianAxisHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\n/**\r\n * Can only be called after coordinate system creation stage.\r\n * (Can be called before coordinate system update stage).\r\n */\nexport function layout(gridModel, axisModel, opt) {\n  opt = opt || {};\n  var grid = gridModel.coordinateSystem;\n  var axis = axisModel.axis;\n  var layout = {};\n  var otherAxisOnZeroOf = axis.getAxesOnZeroOf()[0];\n  var rawAxisPosition = axis.position;\n  var axisPosition = otherAxisOnZeroOf ? 'onZero' : rawAxisPosition;\n  var axisDim = axis.dim;\n  var rect = grid.getRect();\n  var rectBound = [rect.x, rect.x + rect.width, rect.y, rect.y + rect.height];\n  var idx = {\n    left: 0,\n    right: 1,\n    top: 0,\n    bottom: 1,\n    onZero: 2\n  };\n  var axisOffset = axisModel.get('offset') || 0;\n  var posBound = axisDim === 'x' ? [rectBound[2] - axisOffset, rectBound[3] + axisOffset] : [rectBound[0] - axisOffset, rectBound[1] + axisOffset];\n  if (otherAxisOnZeroOf) {\n    var onZeroCoord = otherAxisOnZeroOf.toGlobalCoord(otherAxisOnZeroOf.dataToCoord(0));\n    posBound[idx.onZero] = Math.max(Math.min(onZeroCoord, posBound[1]), posBound[0]);\n  }\n  // Axis position\n  layout.position = [axisDim === 'y' ? posBound[idx[axisPosition]] : rectBound[0], axisDim === 'x' ? posBound[idx[axisPosition]] : rectBound[3]];\n  // Axis rotation\n  layout.rotation = Math.PI / 2 * (axisDim === 'x' ? 0 : 1);\n  // Tick and label direction, x y is axisDim\n  var dirMap = {\n    top: -1,\n    bottom: 1,\n    left: -1,\n    right: 1\n  };\n  layout.labelDirection = layout.tickDirection = layout.nameDirection = dirMap[rawAxisPosition];\n  layout.labelOffset = otherAxisOnZeroOf ? posBound[idx[rawAxisPosition]] - posBound[idx.onZero] : 0;\n  if (axisModel.get(['axisTick', 'inside'])) {\n    layout.tickDirection = -layout.tickDirection;\n  }\n  if (zrUtil.retrieve(opt.labelInside, axisModel.get(['axisLabel', 'inside']))) {\n    layout.labelDirection = -layout.labelDirection;\n  }\n  // Special label rotation\n  var labelRotate = axisModel.get(['axisLabel', 'rotate']);\n  layout.labelRotate = axisPosition === 'top' ? -labelRotate : labelRotate;\n  // Over splitLine and splitArea\n  layout.z2 = 1;\n  return layout;\n}\nexport function isCartesian2DSeries(seriesModel) {\n  return seriesModel.get('coordinateSystem') === 'cartesian2d';\n}\nexport function findAxisModels(seriesModel) {\n  var axisModelMap = {\n    xAxisModel: null,\n    yAxisModel: null\n  };\n  zrUtil.each(axisModelMap, function (v, key) {\n    var axisType = key.replace(/Model$/, '');\n    var axisModel = seriesModel.getReferringComponents(axisType, SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!axisModel) {\n        throw new Error(axisType + ' \"' + zrUtil.retrieve3(seriesModel.get(axisType + 'Index'), seriesModel.get(axisType + 'Id'), 0) + '\" not found');\n      }\n    }\n    axisModelMap[key] = axisModel;\n  });\n  return axisModelMap;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD;AACA;AACA;AACA;AACA,OAAO,SAASC,MAAMA,CAACC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAE;EAChDA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIC,IAAI,GAAGH,SAAS,CAACI,gBAAgB;EACrC,IAAIC,IAAI,GAAGJ,SAAS,CAACI,IAAI;EACzB,IAAIN,MAAM,GAAG,CAAC,CAAC;EACf,IAAIO,iBAAiB,GAAGD,IAAI,CAACE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,IAAIC,eAAe,GAAGH,IAAI,CAACI,QAAQ;EACnC,IAAIC,YAAY,GAAGJ,iBAAiB,GAAG,QAAQ,GAAGE,eAAe;EACjE,IAAIG,OAAO,GAAGN,IAAI,CAACO,GAAG;EACtB,IAAIC,IAAI,GAAGV,IAAI,CAACW,OAAO,CAAC,CAAC;EACzB,IAAIC,SAAS,GAAG,CAACF,IAAI,CAACG,CAAC,EAAEH,IAAI,CAACG,CAAC,GAAGH,IAAI,CAACI,KAAK,EAAEJ,IAAI,CAACK,CAAC,EAAEL,IAAI,CAACK,CAAC,GAAGL,IAAI,CAACM,MAAM,CAAC;EAC3E,IAAIC,GAAG,GAAG;IACRC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC;EACD,IAAIC,UAAU,GAAGzB,SAAS,CAAC0B,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;EAC7C,IAAIC,QAAQ,GAAGjB,OAAO,KAAK,GAAG,GAAG,CAACI,SAAS,CAAC,CAAC,CAAC,GAAGW,UAAU,EAAEX,SAAS,CAAC,CAAC,CAAC,GAAGW,UAAU,CAAC,GAAG,CAACX,SAAS,CAAC,CAAC,CAAC,GAAGW,UAAU,EAAEX,SAAS,CAAC,CAAC,CAAC,GAAGW,UAAU,CAAC;EAChJ,IAAIpB,iBAAiB,EAAE;IACrB,IAAIuB,WAAW,GAAGvB,iBAAiB,CAACwB,aAAa,CAACxB,iBAAiB,CAACyB,WAAW,CAAC,CAAC,CAAC,CAAC;IACnFH,QAAQ,CAACR,GAAG,CAACK,MAAM,CAAC,GAAGO,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,WAAW,EAAED,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClF;EACA;EACA7B,MAAM,CAACU,QAAQ,GAAG,CAACE,OAAO,KAAK,GAAG,GAAGiB,QAAQ,CAACR,GAAG,CAACV,YAAY,CAAC,CAAC,GAAGK,SAAS,CAAC,CAAC,CAAC,EAAEJ,OAAO,KAAK,GAAG,GAAGiB,QAAQ,CAACR,GAAG,CAACV,YAAY,CAAC,CAAC,GAAGK,SAAS,CAAC,CAAC,CAAC,CAAC;EAC9I;EACAhB,MAAM,CAACoC,QAAQ,GAAGH,IAAI,CAACI,EAAE,GAAG,CAAC,IAAIzB,OAAO,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;EACzD;EACA,IAAI0B,MAAM,GAAG;IACXd,GAAG,EAAE,CAAC,CAAC;IACPC,MAAM,EAAE,CAAC;IACTH,IAAI,EAAE,CAAC,CAAC;IACRC,KAAK,EAAE;EACT,CAAC;EACDvB,MAAM,CAACuC,cAAc,GAAGvC,MAAM,CAACwC,aAAa,GAAGxC,MAAM,CAACyC,aAAa,GAAGH,MAAM,CAAC7B,eAAe,CAAC;EAC7FT,MAAM,CAAC0C,WAAW,GAAGnC,iBAAiB,GAAGsB,QAAQ,CAACR,GAAG,CAACZ,eAAe,CAAC,CAAC,GAAGoB,QAAQ,CAACR,GAAG,CAACK,MAAM,CAAC,GAAG,CAAC;EAClG,IAAIxB,SAAS,CAAC0B,GAAG,CAAC,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,EAAE;IACzC5B,MAAM,CAACwC,aAAa,GAAG,CAACxC,MAAM,CAACwC,aAAa;EAC9C;EACA,IAAI1C,MAAM,CAAC6C,QAAQ,CAACxC,GAAG,CAACyC,WAAW,EAAE1C,SAAS,CAAC0B,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE;IAC5E5B,MAAM,CAACuC,cAAc,GAAG,CAACvC,MAAM,CAACuC,cAAc;EAChD;EACA;EACA,IAAIM,WAAW,GAAG3C,SAAS,CAAC0B,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;EACxD5B,MAAM,CAAC6C,WAAW,GAAGlC,YAAY,KAAK,KAAK,GAAG,CAACkC,WAAW,GAAGA,WAAW;EACxE;EACA7C,MAAM,CAAC8C,EAAE,GAAG,CAAC;EACb,OAAO9C,MAAM;AACf;AACA,OAAO,SAAS+C,mBAAmBA,CAACC,WAAW,EAAE;EAC/C,OAAOA,WAAW,CAACpB,GAAG,CAAC,kBAAkB,CAAC,KAAK,aAAa;AAC9D;AACA,OAAO,SAASqB,cAAcA,CAACD,WAAW,EAAE;EAC1C,IAAIE,YAAY,GAAG;IACjBC,UAAU,EAAE,IAAI;IAChBC,UAAU,EAAE;EACd,CAAC;EACDtD,MAAM,CAACuD,IAAI,CAACH,YAAY,EAAE,UAAUI,CAAC,EAAEC,GAAG,EAAE;IAC1C,IAAIC,QAAQ,GAAGD,GAAG,CAACE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;IACxC,IAAIvD,SAAS,GAAG8C,WAAW,CAACU,sBAAsB,CAACF,QAAQ,EAAEzD,gBAAgB,CAAC,CAAC4D,MAAM,CAAC,CAAC,CAAC;IACxF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAAC5D,SAAS,EAAE;QACd,MAAM,IAAI6D,KAAK,CAACP,QAAQ,GAAG,IAAI,GAAG1D,MAAM,CAACkE,SAAS,CAAChB,WAAW,CAACpB,GAAG,CAAC4B,QAAQ,GAAG,OAAO,CAAC,EAAER,WAAW,CAACpB,GAAG,CAAC4B,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC;MAC/I;IACF;IACAN,YAAY,CAACK,GAAG,CAAC,GAAGrD,SAAS;EAC/B,CAAC,CAAC;EACF,OAAOgD,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}