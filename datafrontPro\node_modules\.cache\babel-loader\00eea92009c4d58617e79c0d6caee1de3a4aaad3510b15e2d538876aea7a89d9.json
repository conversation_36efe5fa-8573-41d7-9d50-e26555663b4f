{"ast": null, "code": "export default {\n  name: 'AppLayout',\n  data() {\n    return {\n      isCollapse: false\n    };\n  },\n  computed: {\n    activeMenu() {\n      const route = this.$route;\n      return route.path;\n    }\n  },\n  methods: {\n    toggleSideBar() {\n      this.isCollapse = !this.isCollapse;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "isCollapse", "computed", "activeMenu", "route", "$route", "path", "methods", "toggleSideBar"], "sources": ["src/layout/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-wrapper\">\r\n    <!-- 顶部导航 -->\r\n    <div class=\"navbar\">\r\n      <div class=\"logo\">\r\n        <h1>DataEase</h1>\r\n      </div>\r\n      <div class=\"navbar-menu\">\r\n        <el-menu\r\n          :default-active=\"activeMenu\"\r\n          mode=\"horizontal\"\r\n          background-color=\"#304156\"\r\n          text-color=\"#fff\"\r\n          active-text-color=\"#ffd04b\"\r\n          router\r\n        >\r\n          <el-menu-item index=\"/dataset\">数据集管理</el-menu-item>\r\n          <el-menu-item index=\"/visualization\">可视化</el-menu-item>\r\n        </el-menu>\r\n      </div>\r\n      <div class=\"right-menu\">\r\n        <el-dropdown trigger=\"click\">\r\n          <div class=\"avatar-wrapper\">\r\n            <i class=\"el-icon-user\"></i>\r\n            <span>管理员</span>\r\n            <i class=\"el-icon-arrow-down\"></i>\r\n          </div>\r\n          <el-dropdown-menu slot=\"dropdown\">\r\n            <el-dropdown-item>个人中心</el-dropdown-item>\r\n            <el-dropdown-item divided>退出登录</el-dropdown-item>\r\n          </el-dropdown-menu>\r\n        </el-dropdown>\r\n      </div>\r\n    </div>\r\n    \r\n    <div class=\"main-container\">\r\n      <!-- 侧边栏 -->\r\n      <div class=\"sidebar-container\">\r\n        <el-menu\r\n          :default-active=\"activeMenu\"\r\n          class=\"sidebar-menu\"\r\n          background-color=\"#304156\"\r\n          text-color=\"#bfcbd9\"\r\n          active-text-color=\"#409EFF\"\r\n          :collapse=\"isCollapse\"\r\n          :unique-opened=\"true\"\r\n          router\r\n        >\r\n          <el-submenu index=\"1\">\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-s-data\"></i>\r\n              <span>数据集管理</span>\r\n            </template>\r\n            <el-menu-item index=\"/dataset\">数据集列表</el-menu-item>\r\n          </el-submenu>\r\n          \r\n          <el-submenu index=\"2\">\r\n            <template slot=\"title\">\r\n              <i class=\"el-icon-s-marketing\"></i>\r\n              <span>可视化管理</span>\r\n            </template>\r\n            <el-menu-item index=\"/visualization\">可视化列表</el-menu-item>\r\n          </el-submenu>\r\n        </el-menu>\r\n        \r\n        <!-- 折叠按钮 -->\r\n        <div class=\"collapse-btn\" @click=\"toggleSideBar\">\r\n          <i :class=\"isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'\"></i>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 主内容区 -->\r\n      <div class=\"app-main\">\r\n        <router-view />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'AppLayout',\r\n  data() {\r\n    return {\r\n      isCollapse: false\r\n    }\r\n  },\r\n  computed: {\r\n    activeMenu() {\r\n      const route = this.$route\r\n      return route.path\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSideBar() {\r\n      this.isCollapse = !this.isCollapse\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.app-wrapper {\r\n  position: relative;\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.navbar {\r\n  height: 50px;\r\n  background-color: #304156;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 15px;\r\n  color: white;\r\n  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);\r\n}\r\n\r\n.logo {\r\n  width: 200px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo h1 {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  font-weight: 600;\r\n}\r\n\r\n.navbar-menu {\r\n  flex: 1;\r\n}\r\n\r\n.right-menu {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.avatar-wrapper {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  padding: 0 8px;\r\n}\r\n\r\n.avatar-wrapper i {\r\n  margin-right: 5px;\r\n}\r\n\r\n.main-container {\r\n  display: flex;\r\n  height: calc(100% - 50px);\r\n}\r\n\r\n.sidebar-container {\r\n  position: relative;\r\n  background-color: #304156;\r\n  width: 210px;\r\n  height: 100%;\r\n  transition: width 0.3s;\r\n  overflow: hidden;\r\n}\r\n\r\n.sidebar-container.is-collapse {\r\n  width: 64px;\r\n}\r\n\r\n.sidebar-menu {\r\n  height: calc(100% - 50px);\r\n  border-right: none;\r\n}\r\n\r\n.collapse-btn {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #263445;\r\n  color: #bfcbd9;\r\n  cursor: pointer;\r\n}\r\n\r\n.app-main {\r\n  flex: 1;\r\n  overflow: auto;\r\n  background-color: #f0f2f5;\r\n  padding: 0;\r\n  position: relative;\r\n}\r\n</style> "], "mappings": "AAgFA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,UAAA;IACA;EACA;EACAC,QAAA;IACAC,WAAA;MACA,MAAAC,KAAA,QAAAC,MAAA;MACA,OAAAD,KAAA,CAAAE,IAAA;IACA;EACA;EACAC,OAAA;IACAC,cAAA;MACA,KAAAP,UAAA,SAAAA,UAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}