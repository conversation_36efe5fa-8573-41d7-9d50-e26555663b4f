{"ast": null, "code": "import \"core-js/modules/es.typed-array.with.js\";\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nconst _state = {};\nfunction v7(options, buf, offset) {\n  let bytes;\n  if (options) {\n    bytes = v7Bytes(options.random ?? options.rng?.() ?? rng(), options.msecs, options.seq, buf, offset);\n  } else {\n    const now = Date.now();\n    const rnds = rng();\n    updateV7State(_state, now, rnds);\n    bytes = v7Bytes(rnds, _state.msecs, _state.seq, buf, offset);\n  }\n  return buf ?? unsafeStringify(bytes);\n}\nexport function updateV7State(state, now, rnds) {\n  state.msecs ??= -Infinity;\n  state.seq ??= 0;\n  if (now > state.msecs) {\n    state.seq = rnds[6] << 23 | rnds[7] << 16 | rnds[8] << 8 | rnds[9];\n    state.msecs = now;\n  } else {\n    state.seq = state.seq + 1 | 0;\n    if (state.seq === 0) {\n      state.msecs++;\n    }\n  }\n  return state;\n}\nfunction v7Bytes(rnds, msecs, seq, buf, offset = 0) {\n  if (rnds.length < 16) {\n    throw new Error('Random bytes length must be >= 16');\n  }\n  if (!buf) {\n    buf = new Uint8Array(16);\n    offset = 0;\n  } else {\n    if (offset < 0 || offset + 16 > buf.length) {\n      throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n    }\n  }\n  msecs ??= Date.now();\n  seq ??= rnds[6] * 0x7f << 24 | rnds[7] << 16 | rnds[8] << 8 | rnds[9];\n  buf[offset++] = msecs / 0x10000000000 & 0xff;\n  buf[offset++] = msecs / 0x100000000 & 0xff;\n  buf[offset++] = msecs / 0x1000000 & 0xff;\n  buf[offset++] = msecs / 0x10000 & 0xff;\n  buf[offset++] = msecs / 0x100 & 0xff;\n  buf[offset++] = msecs & 0xff;\n  buf[offset++] = 0x70 | seq >>> 28 & 0x0f;\n  buf[offset++] = seq >>> 20 & 0xff;\n  buf[offset++] = 0x80 | seq >>> 14 & 0x3f;\n  buf[offset++] = seq >>> 6 & 0xff;\n  buf[offset++] = seq << 2 & 0xff | rnds[10] & 0x03;\n  buf[offset++] = rnds[11];\n  buf[offset++] = rnds[12];\n  buf[offset++] = rnds[13];\n  buf[offset++] = rnds[14];\n  buf[offset++] = rnds[15];\n  return buf;\n}\nexport default v7;", "map": {"version": 3, "names": ["rng", "unsafeStringify", "_state", "v7", "options", "buf", "offset", "bytes", "v7Bytes", "random", "msecs", "seq", "now", "Date", "rnds", "updateV7State", "state", "Infinity", "length", "Error", "Uint8Array", "RangeError"], "sources": ["E:/indicator-qa-service/datafront/node_modules/uuid/dist/esm-browser/v7.js"], "sourcesContent": ["import rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nconst _state = {};\nfunction v7(options, buf, offset) {\n    let bytes;\n    if (options) {\n        bytes = v7Bytes(options.random ?? options.rng?.() ?? rng(), options.msecs, options.seq, buf, offset);\n    }\n    else {\n        const now = Date.now();\n        const rnds = rng();\n        updateV7State(_state, now, rnds);\n        bytes = v7Bytes(rnds, _state.msecs, _state.seq, buf, offset);\n    }\n    return buf ?? unsafeStringify(bytes);\n}\nexport function updateV7State(state, now, rnds) {\n    state.msecs ??= -Infinity;\n    state.seq ??= 0;\n    if (now > state.msecs) {\n        state.seq = (rnds[6] << 23) | (rnds[7] << 16) | (rnds[8] << 8) | rnds[9];\n        state.msecs = now;\n    }\n    else {\n        state.seq = (state.seq + 1) | 0;\n        if (state.seq === 0) {\n            state.msecs++;\n        }\n    }\n    return state;\n}\nfunction v7Bytes(rnds, msecs, seq, buf, offset = 0) {\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    if (!buf) {\n        buf = new Uint8Array(16);\n        offset = 0;\n    }\n    else {\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n    }\n    msecs ??= Date.now();\n    seq ??= ((rnds[6] * 0x7f) << 24) | (rnds[7] << 16) | (rnds[8] << 8) | rnds[9];\n    buf[offset++] = (msecs / 0x10000000000) & 0xff;\n    buf[offset++] = (msecs / 0x100000000) & 0xff;\n    buf[offset++] = (msecs / 0x1000000) & 0xff;\n    buf[offset++] = (msecs / 0x10000) & 0xff;\n    buf[offset++] = (msecs / 0x100) & 0xff;\n    buf[offset++] = msecs & 0xff;\n    buf[offset++] = 0x70 | ((seq >>> 28) & 0x0f);\n    buf[offset++] = (seq >>> 20) & 0xff;\n    buf[offset++] = 0x80 | ((seq >>> 14) & 0x3f);\n    buf[offset++] = (seq >>> 6) & 0xff;\n    buf[offset++] = ((seq << 2) & 0xff) | (rnds[10] & 0x03);\n    buf[offset++] = rnds[11];\n    buf[offset++] = rnds[12];\n    buf[offset++] = rnds[13];\n    buf[offset++] = rnds[14];\n    buf[offset++] = rnds[15];\n    return buf;\n}\nexport default v7;\n"], "mappings": ";AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,MAAMC,MAAM,GAAG,CAAC,CAAC;AACjB,SAASC,EAAEA,CAACC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAC9B,IAAIC,KAAK;EACT,IAAIH,OAAO,EAAE;IACTG,KAAK,GAAGC,OAAO,CAACJ,OAAO,CAACK,MAAM,IAAIL,OAAO,CAACJ,GAAG,GAAG,CAAC,IAAIA,GAAG,CAAC,CAAC,EAAEI,OAAO,CAACM,KAAK,EAAEN,OAAO,CAACO,GAAG,EAAEN,GAAG,EAAEC,MAAM,CAAC;EACxG,CAAC,MACI;IACD,MAAMM,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,IAAI,GAAGd,GAAG,CAAC,CAAC;IAClBe,aAAa,CAACb,MAAM,EAAEU,GAAG,EAAEE,IAAI,CAAC;IAChCP,KAAK,GAAGC,OAAO,CAACM,IAAI,EAAEZ,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACS,GAAG,EAAEN,GAAG,EAAEC,MAAM,CAAC;EAChE;EACA,OAAOD,GAAG,IAAIJ,eAAe,CAACM,KAAK,CAAC;AACxC;AACA,OAAO,SAASQ,aAAaA,CAACC,KAAK,EAAEJ,GAAG,EAAEE,IAAI,EAAE;EAC5CE,KAAK,CAACN,KAAK,KAAK,CAACO,QAAQ;EACzBD,KAAK,CAACL,GAAG,KAAK,CAAC;EACf,IAAIC,GAAG,GAAGI,KAAK,CAACN,KAAK,EAAE;IACnBM,KAAK,CAACL,GAAG,GAAIG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,GAAKA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAG,GAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAE,GAAGA,IAAI,CAAC,CAAC,CAAC;IACxEE,KAAK,CAACN,KAAK,GAAGE,GAAG;EACrB,CAAC,MACI;IACDI,KAAK,CAACL,GAAG,GAAIK,KAAK,CAACL,GAAG,GAAG,CAAC,GAAI,CAAC;IAC/B,IAAIK,KAAK,CAACL,GAAG,KAAK,CAAC,EAAE;MACjBK,KAAK,CAACN,KAAK,EAAE;IACjB;EACJ;EACA,OAAOM,KAAK;AAChB;AACA,SAASR,OAAOA,CAACM,IAAI,EAAEJ,KAAK,EAAEC,GAAG,EAAEN,GAAG,EAAEC,MAAM,GAAG,CAAC,EAAE;EAChD,IAAIQ,IAAI,CAACI,MAAM,GAAG,EAAE,EAAE;IAClB,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;EACxD;EACA,IAAI,CAACd,GAAG,EAAE;IACNA,GAAG,GAAG,IAAIe,UAAU,CAAC,EAAE,CAAC;IACxBd,MAAM,GAAG,CAAC;EACd,CAAC,MACI;IACD,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,EAAE,GAAGD,GAAG,CAACa,MAAM,EAAE;MACxC,MAAM,IAAIG,UAAU,CAAC,mBAAmBf,MAAM,IAAIA,MAAM,GAAG,EAAE,0BAA0B,CAAC;IAC5F;EACJ;EACAI,KAAK,KAAKG,IAAI,CAACD,GAAG,CAAC,CAAC;EACpBD,GAAG,KAAOG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAK,EAAE,GAAKA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAG,GAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAE,GAAGA,IAAI,CAAC,CAAC,CAAC;EAC7ET,GAAG,CAACC,MAAM,EAAE,CAAC,GAAII,KAAK,GAAG,aAAa,GAAI,IAAI;EAC9CL,GAAG,CAACC,MAAM,EAAE,CAAC,GAAII,KAAK,GAAG,WAAW,GAAI,IAAI;EAC5CL,GAAG,CAACC,MAAM,EAAE,CAAC,GAAII,KAAK,GAAG,SAAS,GAAI,IAAI;EAC1CL,GAAG,CAACC,MAAM,EAAE,CAAC,GAAII,KAAK,GAAG,OAAO,GAAI,IAAI;EACxCL,GAAG,CAACC,MAAM,EAAE,CAAC,GAAII,KAAK,GAAG,KAAK,GAAI,IAAI;EACtCL,GAAG,CAACC,MAAM,EAAE,CAAC,GAAGI,KAAK,GAAG,IAAI;EAC5BL,GAAG,CAACC,MAAM,EAAE,CAAC,GAAG,IAAI,GAAKK,GAAG,KAAK,EAAE,GAAI,IAAK;EAC5CN,GAAG,CAACC,MAAM,EAAE,CAAC,GAAIK,GAAG,KAAK,EAAE,GAAI,IAAI;EACnCN,GAAG,CAACC,MAAM,EAAE,CAAC,GAAG,IAAI,GAAKK,GAAG,KAAK,EAAE,GAAI,IAAK;EAC5CN,GAAG,CAACC,MAAM,EAAE,CAAC,GAAIK,GAAG,KAAK,CAAC,GAAI,IAAI;EAClCN,GAAG,CAACC,MAAM,EAAE,CAAC,GAAKK,GAAG,IAAI,CAAC,GAAI,IAAI,GAAKG,IAAI,CAAC,EAAE,CAAC,GAAG,IAAK;EACvDT,GAAG,CAACC,MAAM,EAAE,CAAC,GAAGQ,IAAI,CAAC,EAAE,CAAC;EACxBT,GAAG,CAACC,MAAM,EAAE,CAAC,GAAGQ,IAAI,CAAC,EAAE,CAAC;EACxBT,GAAG,CAACC,MAAM,EAAE,CAAC,GAAGQ,IAAI,CAAC,EAAE,CAAC;EACxBT,GAAG,CAACC,MAAM,EAAE,CAAC,GAAGQ,IAAI,CAAC,EAAE,CAAC;EACxBT,GAAG,CAACC,MAAM,EAAE,CAAC,GAAGQ,IAAI,CAAC,EAAE,CAAC;EACxB,OAAOT,GAAG;AACd;AACA,eAAeF,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}