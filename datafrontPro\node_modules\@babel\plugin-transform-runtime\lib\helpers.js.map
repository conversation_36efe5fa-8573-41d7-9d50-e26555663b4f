{"version": 3, "names": ["_semver", "require", "hasMinVersion", "minVersion", "runtimeVersion", "semver", "valid", "intersects"], "sources": ["../src/helpers.ts"], "sourcesContent": ["import semver from \"semver\";\n\nexport function hasMinVersion(\n  minVersion: string,\n  runtimeVersion: string | void,\n) {\n  // If the range is unavailable, we're running the script during Babel's\n  // build process, and we want to assume that all versions are satisfied so\n  // that the built output will include all definitions.\n  if (!runtimeVersion) return true;\n\n  // semver.intersects() has some surprising behavior with comparing ranges\n  // with pre-release versions. We add '^' to ensure that we are always\n  // comparing ranges with ranges, which sidesteps this logic.\n  // For example:\n  //\n  //   semver.intersects(`<7.0.1`, \"7.0.0-beta.0\") // false - surprising\n  //   semver.intersects(`<7.0.1`, \"^7.0.0-beta.0\") // true - expected\n  //\n  // This is because the first falls back to\n  //\n  //   semver.satisfies(\"7.0.0-beta.0\", `<7.0.1`) // false - surprising\n  //\n  // and this fails because a prerelease version can only satisfy a range\n  // if it is a prerelease within the same major/minor/patch range.\n  //\n  // Note: If this is found to have issues, please also revisit the logic in\n  // babel-core's availableHelper() API.\n  if (semver.valid(runtimeVersion)) runtimeVersion = `^${runtimeVersion}`;\n\n  return (\n    !semver.intersects(`<${minVersion}`, runtimeVersion) &&\n    !semver.intersects(`>=8.0.0`, runtimeVersion)\n  );\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAEO,SAASC,aAAaA,CAC3BC,UAAkB,EAClBC,cAA6B,EAC7B;EAIA,IAAI,CAACA,cAAc,EAAE,OAAO,IAAI;EAmBhC,IAAIC,OAAM,CAACC,KAAK,CAACF,cAAc,CAAC,EAAEA,cAAc,GAAG,IAAIA,cAAc,EAAE;EAEvE,OACE,CAACC,OAAM,CAACE,UAAU,CAAC,IAAIJ,UAAU,EAAE,EAAEC,cAAc,CAAC,IACpD,CAACC,OAAM,CAACE,UAAU,CAAC,SAAS,EAAEH,cAAc,CAAC;AAEjD", "ignoreList": []}