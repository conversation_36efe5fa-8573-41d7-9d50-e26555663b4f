{"ast": null, "code": "import _typeof from 'babel-runtime/helpers/typeof';\nimport rules from '../rule/';\nfunction required(rule, value, callback, source, options) {\n  var errors = [];\n  var type = Array.isArray(value) ? 'array' : typeof value === 'undefined' ? 'undefined' : _typeof(value);\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n}\nexport default required;", "map": {"version": 3, "names": ["_typeof", "rules", "required", "rule", "value", "callback", "source", "options", "errors", "type", "Array", "isArray"], "sources": ["E:/AllProject/datafront/node_modules/async-validator/es/validator/required.js"], "sourcesContent": ["import _typeof from 'babel-runtime/helpers/typeof';\nimport rules from '../rule/';\n\nfunction required(rule, value, callback, source, options) {\n  var errors = [];\n  var type = Array.isArray(value) ? 'array' : typeof value === 'undefined' ? 'undefined' : _typeof(value);\n  rules.required(rule, value, source, errors, options, type);\n  callback(errors);\n}\n\nexport default required;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,8BAA8B;AAClD,OAAOC,KAAK,MAAM,UAAU;AAE5B,SAASC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACxD,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,IAAI,GAAGC,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,GAAG,OAAO,GAAG,OAAOA,KAAK,KAAK,WAAW,GAAG,WAAW,GAAGJ,OAAO,CAACI,KAAK,CAAC;EACvGH,KAAK,CAACC,QAAQ,CAACC,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,EAAEE,IAAI,CAAC;EAC1DJ,QAAQ,CAACG,MAAM,CAAC;AAClB;AAEA,eAAeN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}