{"ast": null, "code": "import { __extends } from \"tslib\";\nimport Element from '../Element.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { keys, extend, createObject } from '../core/util.js';\nimport { REDRAW_BIT, STYLE_CHANGED_BIT } from './constants.js';\nvar STYLE_MAGIC_KEY = '__zr_style_' + Math.round(Math.random() * 10);\nexport var DEFAULT_COMMON_STYLE = {\n  shadowBlur: 0,\n  shadowOffsetX: 0,\n  shadowOffsetY: 0,\n  shadowColor: '#000',\n  opacity: 1,\n  blend: 'source-over'\n};\nexport var DEFAULT_COMMON_ANIMATION_PROPS = {\n  style: {\n    shadowBlur: true,\n    shadowOffsetX: true,\n    shadowOffsetY: true,\n    shadowColor: true,\n    opacity: true\n  }\n};\nDEFAULT_COMMON_STYLE[STYLE_MAGIC_KEY] = true;\nvar PRIMARY_STATES_KEYS = ['z', 'z2', 'invisible'];\nvar PRIMARY_STATES_KEYS_IN_HOVER_LAYER = ['invisible'];\nvar Displayable = function (_super) {\n  __extends(Displayable, _super);\n  function Displayable(props) {\n    return _super.call(this, props) || this;\n  }\n  Displayable.prototype._init = function (props) {\n    var keysArr = keys(props);\n    for (var i = 0; i < keysArr.length; i++) {\n      var key = keysArr[i];\n      if (key === 'style') {\n        this.useStyle(props[key]);\n      } else {\n        _super.prototype.attrKV.call(this, key, props[key]);\n      }\n    }\n    if (!this.style) {\n      this.useStyle({});\n    }\n  };\n  Displayable.prototype.beforeBrush = function () {};\n  Displayable.prototype.afterBrush = function () {};\n  Displayable.prototype.innerBeforeBrush = function () {};\n  Displayable.prototype.innerAfterBrush = function () {};\n  Displayable.prototype.shouldBePainted = function (viewWidth, viewHeight, considerClipPath, considerAncestors) {\n    var m = this.transform;\n    if (this.ignore || this.invisible || this.style.opacity === 0 || this.culling && isDisplayableCulled(this, viewWidth, viewHeight) || m && !m[0] && !m[3]) {\n      return false;\n    }\n    if (considerClipPath && this.__clipPaths) {\n      for (var i = 0; i < this.__clipPaths.length; ++i) {\n        if (this.__clipPaths[i].isZeroArea()) {\n          return false;\n        }\n      }\n    }\n    if (considerAncestors && this.parent) {\n      var parent_1 = this.parent;\n      while (parent_1) {\n        if (parent_1.ignore) {\n          return false;\n        }\n        parent_1 = parent_1.parent;\n      }\n    }\n    return true;\n  };\n  Displayable.prototype.contain = function (x, y) {\n    return this.rectContain(x, y);\n  };\n  Displayable.prototype.traverse = function (cb, context) {\n    cb.call(context, this);\n  };\n  Displayable.prototype.rectContain = function (x, y) {\n    var coord = this.transformCoordToLocal(x, y);\n    var rect = this.getBoundingRect();\n    return rect.contain(coord[0], coord[1]);\n  };\n  Displayable.prototype.getPaintRect = function () {\n    var rect = this._paintRect;\n    if (!this._paintRect || this.__dirty) {\n      var transform = this.transform;\n      var elRect = this.getBoundingRect();\n      var style = this.style;\n      var shadowSize = style.shadowBlur || 0;\n      var shadowOffsetX = style.shadowOffsetX || 0;\n      var shadowOffsetY = style.shadowOffsetY || 0;\n      rect = this._paintRect || (this._paintRect = new BoundingRect(0, 0, 0, 0));\n      if (transform) {\n        BoundingRect.applyTransform(rect, elRect, transform);\n      } else {\n        rect.copy(elRect);\n      }\n      if (shadowSize || shadowOffsetX || shadowOffsetY) {\n        rect.width += shadowSize * 2 + Math.abs(shadowOffsetX);\n        rect.height += shadowSize * 2 + Math.abs(shadowOffsetY);\n        rect.x = Math.min(rect.x, rect.x + shadowOffsetX - shadowSize);\n        rect.y = Math.min(rect.y, rect.y + shadowOffsetY - shadowSize);\n      }\n      var tolerance = this.dirtyRectTolerance;\n      if (!rect.isZero()) {\n        rect.x = Math.floor(rect.x - tolerance);\n        rect.y = Math.floor(rect.y - tolerance);\n        rect.width = Math.ceil(rect.width + 1 + tolerance * 2);\n        rect.height = Math.ceil(rect.height + 1 + tolerance * 2);\n      }\n    }\n    return rect;\n  };\n  Displayable.prototype.setPrevPaintRect = function (paintRect) {\n    if (paintRect) {\n      this._prevPaintRect = this._prevPaintRect || new BoundingRect(0, 0, 0, 0);\n      this._prevPaintRect.copy(paintRect);\n    } else {\n      this._prevPaintRect = null;\n    }\n  };\n  Displayable.prototype.getPrevPaintRect = function () {\n    return this._prevPaintRect;\n  };\n  Displayable.prototype.animateStyle = function (loop) {\n    return this.animate('style', loop);\n  };\n  Displayable.prototype.updateDuringAnimation = function (targetKey) {\n    if (targetKey === 'style') {\n      this.dirtyStyle();\n    } else {\n      this.markRedraw();\n    }\n  };\n  Displayable.prototype.attrKV = function (key, value) {\n    if (key !== 'style') {\n      _super.prototype.attrKV.call(this, key, value);\n    } else {\n      if (!this.style) {\n        this.useStyle(value);\n      } else {\n        this.setStyle(value);\n      }\n    }\n  };\n  Displayable.prototype.setStyle = function (keyOrObj, value) {\n    if (typeof keyOrObj === 'string') {\n      this.style[keyOrObj] = value;\n    } else {\n      extend(this.style, keyOrObj);\n    }\n    this.dirtyStyle();\n    return this;\n  };\n  Displayable.prototype.dirtyStyle = function (notRedraw) {\n    if (!notRedraw) {\n      this.markRedraw();\n    }\n    this.__dirty |= STYLE_CHANGED_BIT;\n    if (this._rect) {\n      this._rect = null;\n    }\n  };\n  Displayable.prototype.dirty = function () {\n    this.dirtyStyle();\n  };\n  Displayable.prototype.styleChanged = function () {\n    return !!(this.__dirty & STYLE_CHANGED_BIT);\n  };\n  Displayable.prototype.styleUpdated = function () {\n    this.__dirty &= ~STYLE_CHANGED_BIT;\n  };\n  Displayable.prototype.createStyle = function (obj) {\n    return createObject(DEFAULT_COMMON_STYLE, obj);\n  };\n  Displayable.prototype.useStyle = function (obj) {\n    if (!obj[STYLE_MAGIC_KEY]) {\n      obj = this.createStyle(obj);\n    }\n    if (this.__inHover) {\n      this.__hoverStyle = obj;\n    } else {\n      this.style = obj;\n    }\n    this.dirtyStyle();\n  };\n  Displayable.prototype.isStyleObject = function (obj) {\n    return obj[STYLE_MAGIC_KEY];\n  };\n  Displayable.prototype._innerSaveToNormal = function (toState) {\n    _super.prototype._innerSaveToNormal.call(this, toState);\n    var normalState = this._normalState;\n    if (toState.style && !normalState.style) {\n      normalState.style = this._mergeStyle(this.createStyle(), this.style);\n    }\n    this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n  };\n  Displayable.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n    _super.prototype._applyStateObj.call(this, stateName, state, normalState, keepCurrentStates, transition, animationCfg);\n    var needsRestoreToNormal = !(state && keepCurrentStates);\n    var targetStyle;\n    if (state && state.style) {\n      if (transition) {\n        if (keepCurrentStates) {\n          targetStyle = state.style;\n        } else {\n          targetStyle = this._mergeStyle(this.createStyle(), normalState.style);\n          this._mergeStyle(targetStyle, state.style);\n        }\n      } else {\n        targetStyle = this._mergeStyle(this.createStyle(), keepCurrentStates ? this.style : normalState.style);\n        this._mergeStyle(targetStyle, state.style);\n      }\n    } else if (needsRestoreToNormal) {\n      targetStyle = normalState.style;\n    }\n    if (targetStyle) {\n      if (transition) {\n        var sourceStyle = this.style;\n        this.style = this.createStyle(needsRestoreToNormal ? {} : sourceStyle);\n        if (needsRestoreToNormal) {\n          var changedKeys = keys(sourceStyle);\n          for (var i = 0; i < changedKeys.length; i++) {\n            var key = changedKeys[i];\n            if (key in targetStyle) {\n              targetStyle[key] = targetStyle[key];\n              this.style[key] = sourceStyle[key];\n            }\n          }\n        }\n        var targetKeys = keys(targetStyle);\n        for (var i = 0; i < targetKeys.length; i++) {\n          var key = targetKeys[i];\n          this.style[key] = this.style[key];\n        }\n        this._transitionState(stateName, {\n          style: targetStyle\n        }, animationCfg, this.getAnimationStyleProps());\n      } else {\n        this.useStyle(targetStyle);\n      }\n    }\n    var statesKeys = this.__inHover ? PRIMARY_STATES_KEYS_IN_HOVER_LAYER : PRIMARY_STATES_KEYS;\n    for (var i = 0; i < statesKeys.length; i++) {\n      var key = statesKeys[i];\n      if (state && state[key] != null) {\n        this[key] = state[key];\n      } else if (needsRestoreToNormal) {\n        if (normalState[key] != null) {\n          this[key] = normalState[key];\n        }\n      }\n    }\n  };\n  Displayable.prototype._mergeStates = function (states) {\n    var mergedState = _super.prototype._mergeStates.call(this, states);\n    var mergedStyle;\n    for (var i = 0; i < states.length; i++) {\n      var state = states[i];\n      if (state.style) {\n        mergedStyle = mergedStyle || {};\n        this._mergeStyle(mergedStyle, state.style);\n      }\n    }\n    if (mergedStyle) {\n      mergedState.style = mergedStyle;\n    }\n    return mergedState;\n  };\n  Displayable.prototype._mergeStyle = function (targetStyle, sourceStyle) {\n    extend(targetStyle, sourceStyle);\n    return targetStyle;\n  };\n  Displayable.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_COMMON_ANIMATION_PROPS;\n  };\n  Displayable.initDefaultProps = function () {\n    var dispProto = Displayable.prototype;\n    dispProto.type = 'displayable';\n    dispProto.invisible = false;\n    dispProto.z = 0;\n    dispProto.z2 = 0;\n    dispProto.zlevel = 0;\n    dispProto.culling = false;\n    dispProto.cursor = 'pointer';\n    dispProto.rectHover = false;\n    dispProto.incremental = false;\n    dispProto._rect = null;\n    dispProto.dirtyRectTolerance = 0;\n    dispProto.__dirty = REDRAW_BIT | STYLE_CHANGED_BIT;\n  }();\n  return Displayable;\n}(Element);\nvar tmpRect = new BoundingRect(0, 0, 0, 0);\nvar viewRect = new BoundingRect(0, 0, 0, 0);\nfunction isDisplayableCulled(el, width, height) {\n  tmpRect.copy(el.getBoundingRect());\n  if (el.transform) {\n    tmpRect.applyTransform(el.transform);\n  }\n  viewRect.width = width;\n  viewRect.height = height;\n  return !tmpRect.intersect(viewRect);\n}\nexport default Displayable;", "map": {"version": 3, "names": ["__extends", "Element", "BoundingRect", "keys", "extend", "createObject", "REDRAW_BIT", "STYLE_CHANGED_BIT", "STYLE_MAGIC_KEY", "Math", "round", "random", "DEFAULT_COMMON_STYLE", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowOffsetY", "shadowColor", "opacity", "blend", "DEFAULT_COMMON_ANIMATION_PROPS", "style", "PRIMARY_STATES_KEYS", "PRIMARY_STATES_KEYS_IN_HOVER_LAYER", "Displayable", "_super", "props", "call", "prototype", "_init", "keysArr", "i", "length", "key", "useStyle", "attrKV", "beforeBrush", "afterBrush", "innerBeforeBrush", "innerAfterBrush", "shouldBePainted", "viewWidth", "viewHeight", "consider<PERSON>lip<PERSON>ath", "considerAncestors", "m", "transform", "ignore", "invisible", "culling", "isDisplayableCulled", "__clipPaths", "isZeroArea", "parent", "parent_1", "contain", "x", "y", "rectContain", "traverse", "cb", "context", "coord", "transformCoordToLocal", "rect", "getBoundingRect", "getPaintRect", "_paintRect", "__dirty", "elRect", "shadowSize", "applyTransform", "copy", "width", "abs", "height", "min", "tolerance", "dirtyRectTolerance", "isZero", "floor", "ceil", "setPrevPaintRect", "paintRect", "_prevPaintRect", "getPrevPaintRect", "animateStyle", "loop", "animate", "updateDuringAnimation", "<PERSON><PERSON><PERSON>", "dirtyStyle", "mark<PERSON><PERSON><PERSON>", "value", "setStyle", "key<PERSON>r<PERSON><PERSON><PERSON>", "notRedraw", "_rect", "dirty", "styleChanged", "styleUpdated", "createStyle", "obj", "__inHover", "__hoverStyle", "isStyleObject", "_innerSaveToNormal", "toState", "normalState", "_normalState", "_mergeStyle", "_savePrimaryToNormal", "_applyStateObj", "stateName", "state", "keepCurrentStates", "transition", "animationCfg", "needsRestoreToNormal", "targetStyle", "sourceStyle", "changed<PERSON><PERSON><PERSON>", "targetKeys", "_transitionState", "getAnimationStyleProps", "statesKeys", "_mergeStates", "states", "mergedState", "mergedStyle", "initDefaultProps", "dispProto", "type", "z", "z2", "zlevel", "cursor", "rectHover", "incremental", "tmpRect", "viewRect", "el", "intersect"], "sources": ["D:/FastBI/datafront/node_modules/zrender/lib/graphic/Displayable.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport Element from '../Element.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport { keys, extend, createObject } from '../core/util.js';\nimport { REDRAW_BIT, STYLE_CHANGED_BIT } from './constants.js';\nvar STYLE_MAGIC_KEY = '__zr_style_' + Math.round((Math.random() * 10));\nexport var DEFAULT_COMMON_STYLE = {\n    shadowBlur: 0,\n    shadowOffsetX: 0,\n    shadowOffsetY: 0,\n    shadowColor: '#000',\n    opacity: 1,\n    blend: 'source-over'\n};\nexport var DEFAULT_COMMON_ANIMATION_PROPS = {\n    style: {\n        shadowBlur: true,\n        shadowOffsetX: true,\n        shadowOffsetY: true,\n        shadowColor: true,\n        opacity: true\n    }\n};\nDEFAULT_COMMON_STYLE[STYLE_MAGIC_KEY] = true;\nvar PRIMARY_STATES_KEYS = ['z', 'z2', 'invisible'];\nvar PRIMARY_STATES_KEYS_IN_HOVER_LAYER = ['invisible'];\nvar Displayable = (function (_super) {\n    __extends(Displayable, _super);\n    function Displayable(props) {\n        return _super.call(this, props) || this;\n    }\n    Displayable.prototype._init = function (props) {\n        var keysArr = keys(props);\n        for (var i = 0; i < keysArr.length; i++) {\n            var key = keysArr[i];\n            if (key === 'style') {\n                this.useStyle(props[key]);\n            }\n            else {\n                _super.prototype.attrKV.call(this, key, props[key]);\n            }\n        }\n        if (!this.style) {\n            this.useStyle({});\n        }\n    };\n    Displayable.prototype.beforeBrush = function () { };\n    Displayable.prototype.afterBrush = function () { };\n    Displayable.prototype.innerBeforeBrush = function () { };\n    Displayable.prototype.innerAfterBrush = function () { };\n    Displayable.prototype.shouldBePainted = function (viewWidth, viewHeight, considerClipPath, considerAncestors) {\n        var m = this.transform;\n        if (this.ignore\n            || this.invisible\n            || this.style.opacity === 0\n            || (this.culling\n                && isDisplayableCulled(this, viewWidth, viewHeight))\n            || (m && !m[0] && !m[3])) {\n            return false;\n        }\n        if (considerClipPath && this.__clipPaths) {\n            for (var i = 0; i < this.__clipPaths.length; ++i) {\n                if (this.__clipPaths[i].isZeroArea()) {\n                    return false;\n                }\n            }\n        }\n        if (considerAncestors && this.parent) {\n            var parent_1 = this.parent;\n            while (parent_1) {\n                if (parent_1.ignore) {\n                    return false;\n                }\n                parent_1 = parent_1.parent;\n            }\n        }\n        return true;\n    };\n    Displayable.prototype.contain = function (x, y) {\n        return this.rectContain(x, y);\n    };\n    Displayable.prototype.traverse = function (cb, context) {\n        cb.call(context, this);\n    };\n    Displayable.prototype.rectContain = function (x, y) {\n        var coord = this.transformCoordToLocal(x, y);\n        var rect = this.getBoundingRect();\n        return rect.contain(coord[0], coord[1]);\n    };\n    Displayable.prototype.getPaintRect = function () {\n        var rect = this._paintRect;\n        if (!this._paintRect || this.__dirty) {\n            var transform = this.transform;\n            var elRect = this.getBoundingRect();\n            var style = this.style;\n            var shadowSize = style.shadowBlur || 0;\n            var shadowOffsetX = style.shadowOffsetX || 0;\n            var shadowOffsetY = style.shadowOffsetY || 0;\n            rect = this._paintRect || (this._paintRect = new BoundingRect(0, 0, 0, 0));\n            if (transform) {\n                BoundingRect.applyTransform(rect, elRect, transform);\n            }\n            else {\n                rect.copy(elRect);\n            }\n            if (shadowSize || shadowOffsetX || shadowOffsetY) {\n                rect.width += shadowSize * 2 + Math.abs(shadowOffsetX);\n                rect.height += shadowSize * 2 + Math.abs(shadowOffsetY);\n                rect.x = Math.min(rect.x, rect.x + shadowOffsetX - shadowSize);\n                rect.y = Math.min(rect.y, rect.y + shadowOffsetY - shadowSize);\n            }\n            var tolerance = this.dirtyRectTolerance;\n            if (!rect.isZero()) {\n                rect.x = Math.floor(rect.x - tolerance);\n                rect.y = Math.floor(rect.y - tolerance);\n                rect.width = Math.ceil(rect.width + 1 + tolerance * 2);\n                rect.height = Math.ceil(rect.height + 1 + tolerance * 2);\n            }\n        }\n        return rect;\n    };\n    Displayable.prototype.setPrevPaintRect = function (paintRect) {\n        if (paintRect) {\n            this._prevPaintRect = this._prevPaintRect || new BoundingRect(0, 0, 0, 0);\n            this._prevPaintRect.copy(paintRect);\n        }\n        else {\n            this._prevPaintRect = null;\n        }\n    };\n    Displayable.prototype.getPrevPaintRect = function () {\n        return this._prevPaintRect;\n    };\n    Displayable.prototype.animateStyle = function (loop) {\n        return this.animate('style', loop);\n    };\n    Displayable.prototype.updateDuringAnimation = function (targetKey) {\n        if (targetKey === 'style') {\n            this.dirtyStyle();\n        }\n        else {\n            this.markRedraw();\n        }\n    };\n    Displayable.prototype.attrKV = function (key, value) {\n        if (key !== 'style') {\n            _super.prototype.attrKV.call(this, key, value);\n        }\n        else {\n            if (!this.style) {\n                this.useStyle(value);\n            }\n            else {\n                this.setStyle(value);\n            }\n        }\n    };\n    Displayable.prototype.setStyle = function (keyOrObj, value) {\n        if (typeof keyOrObj === 'string') {\n            this.style[keyOrObj] = value;\n        }\n        else {\n            extend(this.style, keyOrObj);\n        }\n        this.dirtyStyle();\n        return this;\n    };\n    Displayable.prototype.dirtyStyle = function (notRedraw) {\n        if (!notRedraw) {\n            this.markRedraw();\n        }\n        this.__dirty |= STYLE_CHANGED_BIT;\n        if (this._rect) {\n            this._rect = null;\n        }\n    };\n    Displayable.prototype.dirty = function () {\n        this.dirtyStyle();\n    };\n    Displayable.prototype.styleChanged = function () {\n        return !!(this.__dirty & STYLE_CHANGED_BIT);\n    };\n    Displayable.prototype.styleUpdated = function () {\n        this.__dirty &= ~STYLE_CHANGED_BIT;\n    };\n    Displayable.prototype.createStyle = function (obj) {\n        return createObject(DEFAULT_COMMON_STYLE, obj);\n    };\n    Displayable.prototype.useStyle = function (obj) {\n        if (!obj[STYLE_MAGIC_KEY]) {\n            obj = this.createStyle(obj);\n        }\n        if (this.__inHover) {\n            this.__hoverStyle = obj;\n        }\n        else {\n            this.style = obj;\n        }\n        this.dirtyStyle();\n    };\n    Displayable.prototype.isStyleObject = function (obj) {\n        return obj[STYLE_MAGIC_KEY];\n    };\n    Displayable.prototype._innerSaveToNormal = function (toState) {\n        _super.prototype._innerSaveToNormal.call(this, toState);\n        var normalState = this._normalState;\n        if (toState.style && !normalState.style) {\n            normalState.style = this._mergeStyle(this.createStyle(), this.style);\n        }\n        this._savePrimaryToNormal(toState, normalState, PRIMARY_STATES_KEYS);\n    };\n    Displayable.prototype._applyStateObj = function (stateName, state, normalState, keepCurrentStates, transition, animationCfg) {\n        _super.prototype._applyStateObj.call(this, stateName, state, normalState, keepCurrentStates, transition, animationCfg);\n        var needsRestoreToNormal = !(state && keepCurrentStates);\n        var targetStyle;\n        if (state && state.style) {\n            if (transition) {\n                if (keepCurrentStates) {\n                    targetStyle = state.style;\n                }\n                else {\n                    targetStyle = this._mergeStyle(this.createStyle(), normalState.style);\n                    this._mergeStyle(targetStyle, state.style);\n                }\n            }\n            else {\n                targetStyle = this._mergeStyle(this.createStyle(), keepCurrentStates ? this.style : normalState.style);\n                this._mergeStyle(targetStyle, state.style);\n            }\n        }\n        else if (needsRestoreToNormal) {\n            targetStyle = normalState.style;\n        }\n        if (targetStyle) {\n            if (transition) {\n                var sourceStyle = this.style;\n                this.style = this.createStyle(needsRestoreToNormal ? {} : sourceStyle);\n                if (needsRestoreToNormal) {\n                    var changedKeys = keys(sourceStyle);\n                    for (var i = 0; i < changedKeys.length; i++) {\n                        var key = changedKeys[i];\n                        if (key in targetStyle) {\n                            targetStyle[key] = targetStyle[key];\n                            this.style[key] = sourceStyle[key];\n                        }\n                    }\n                }\n                var targetKeys = keys(targetStyle);\n                for (var i = 0; i < targetKeys.length; i++) {\n                    var key = targetKeys[i];\n                    this.style[key] = this.style[key];\n                }\n                this._transitionState(stateName, {\n                    style: targetStyle\n                }, animationCfg, this.getAnimationStyleProps());\n            }\n            else {\n                this.useStyle(targetStyle);\n            }\n        }\n        var statesKeys = this.__inHover ? PRIMARY_STATES_KEYS_IN_HOVER_LAYER : PRIMARY_STATES_KEYS;\n        for (var i = 0; i < statesKeys.length; i++) {\n            var key = statesKeys[i];\n            if (state && state[key] != null) {\n                this[key] = state[key];\n            }\n            else if (needsRestoreToNormal) {\n                if (normalState[key] != null) {\n                    this[key] = normalState[key];\n                }\n            }\n        }\n    };\n    Displayable.prototype._mergeStates = function (states) {\n        var mergedState = _super.prototype._mergeStates.call(this, states);\n        var mergedStyle;\n        for (var i = 0; i < states.length; i++) {\n            var state = states[i];\n            if (state.style) {\n                mergedStyle = mergedStyle || {};\n                this._mergeStyle(mergedStyle, state.style);\n            }\n        }\n        if (mergedStyle) {\n            mergedState.style = mergedStyle;\n        }\n        return mergedState;\n    };\n    Displayable.prototype._mergeStyle = function (targetStyle, sourceStyle) {\n        extend(targetStyle, sourceStyle);\n        return targetStyle;\n    };\n    Displayable.prototype.getAnimationStyleProps = function () {\n        return DEFAULT_COMMON_ANIMATION_PROPS;\n    };\n    Displayable.initDefaultProps = (function () {\n        var dispProto = Displayable.prototype;\n        dispProto.type = 'displayable';\n        dispProto.invisible = false;\n        dispProto.z = 0;\n        dispProto.z2 = 0;\n        dispProto.zlevel = 0;\n        dispProto.culling = false;\n        dispProto.cursor = 'pointer';\n        dispProto.rectHover = false;\n        dispProto.incremental = false;\n        dispProto._rect = null;\n        dispProto.dirtyRectTolerance = 0;\n        dispProto.__dirty = REDRAW_BIT | STYLE_CHANGED_BIT;\n    })();\n    return Displayable;\n}(Element));\nvar tmpRect = new BoundingRect(0, 0, 0, 0);\nvar viewRect = new BoundingRect(0, 0, 0, 0);\nfunction isDisplayableCulled(el, width, height) {\n    tmpRect.copy(el.getBoundingRect());\n    if (el.transform) {\n        tmpRect.applyTransform(el.transform);\n    }\n    viewRect.width = width;\n    viewRect.height = height;\n    return !tmpRect.intersect(viewRect);\n}\nexport default Displayable;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,YAAY,QAAQ,iBAAiB;AAC5D,SAASC,UAAU,EAAEC,iBAAiB,QAAQ,gBAAgB;AAC9D,IAAIC,eAAe,GAAG,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAED,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAG,CAAC;AACtE,OAAO,IAAIC,oBAAoB,GAAG;EAC9BC,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBC,WAAW,EAAE,MAAM;EACnBC,OAAO,EAAE,CAAC;EACVC,KAAK,EAAE;AACX,CAAC;AACD,OAAO,IAAIC,8BAA8B,GAAG;EACxCC,KAAK,EAAE;IACHP,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,IAAI;IACnBC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE;EACb;AACJ,CAAC;AACDL,oBAAoB,CAACJ,eAAe,CAAC,GAAG,IAAI;AAC5C,IAAIa,mBAAmB,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,WAAW,CAAC;AAClD,IAAIC,kCAAkC,GAAG,CAAC,WAAW,CAAC;AACtD,IAAIC,WAAW,GAAI,UAAUC,MAAM,EAAE;EACjCxB,SAAS,CAACuB,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAACE,KAAK,EAAE;IACxB,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,KAAK,CAAC,IAAI,IAAI;EAC3C;EACAF,WAAW,CAACI,SAAS,CAACC,KAAK,GAAG,UAAUH,KAAK,EAAE;IAC3C,IAAII,OAAO,GAAG1B,IAAI,CAACsB,KAAK,CAAC;IACzB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIE,GAAG,GAAGH,OAAO,CAACC,CAAC,CAAC;MACpB,IAAIE,GAAG,KAAK,OAAO,EAAE;QACjB,IAAI,CAACC,QAAQ,CAACR,KAAK,CAACO,GAAG,CAAC,CAAC;MAC7B,CAAC,MACI;QACDR,MAAM,CAACG,SAAS,CAACO,MAAM,CAACR,IAAI,CAAC,IAAI,EAAEM,GAAG,EAAEP,KAAK,CAACO,GAAG,CAAC,CAAC;MACvD;IACJ;IACA,IAAI,CAAC,IAAI,CAACZ,KAAK,EAAE;MACb,IAAI,CAACa,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrB;EACJ,CAAC;EACDV,WAAW,CAACI,SAAS,CAACQ,WAAW,GAAG,YAAY,CAAE,CAAC;EACnDZ,WAAW,CAACI,SAAS,CAACS,UAAU,GAAG,YAAY,CAAE,CAAC;EAClDb,WAAW,CAACI,SAAS,CAACU,gBAAgB,GAAG,YAAY,CAAE,CAAC;EACxDd,WAAW,CAACI,SAAS,CAACW,eAAe,GAAG,YAAY,CAAE,CAAC;EACvDf,WAAW,CAACI,SAAS,CAACY,eAAe,GAAG,UAAUC,SAAS,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAE;IAC1G,IAAIC,CAAC,GAAG,IAAI,CAACC,SAAS;IACtB,IAAI,IAAI,CAACC,MAAM,IACR,IAAI,CAACC,SAAS,IACd,IAAI,CAAC3B,KAAK,CAACH,OAAO,KAAK,CAAC,IACvB,IAAI,CAAC+B,OAAO,IACTC,mBAAmB,CAAC,IAAI,EAAET,SAAS,EAAEC,UAAU,CAAE,IACpDG,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAC,IAAI,CAACA,CAAC,CAAC,CAAC,CAAE,EAAE;MAC1B,OAAO,KAAK;IAChB;IACA,IAAIF,gBAAgB,IAAI,IAAI,CAACQ,WAAW,EAAE;MACtC,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACoB,WAAW,CAACnB,MAAM,EAAE,EAAED,CAAC,EAAE;QAC9C,IAAI,IAAI,CAACoB,WAAW,CAACpB,CAAC,CAAC,CAACqB,UAAU,CAAC,CAAC,EAAE;UAClC,OAAO,KAAK;QAChB;MACJ;IACJ;IACA,IAAIR,iBAAiB,IAAI,IAAI,CAACS,MAAM,EAAE;MAClC,IAAIC,QAAQ,GAAG,IAAI,CAACD,MAAM;MAC1B,OAAOC,QAAQ,EAAE;QACb,IAAIA,QAAQ,CAACP,MAAM,EAAE;UACjB,OAAO,KAAK;QAChB;QACAO,QAAQ,GAAGA,QAAQ,CAACD,MAAM;MAC9B;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD7B,WAAW,CAACI,SAAS,CAAC2B,OAAO,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAC5C,OAAO,IAAI,CAACC,WAAW,CAACF,CAAC,EAAEC,CAAC,CAAC;EACjC,CAAC;EACDjC,WAAW,CAACI,SAAS,CAAC+B,QAAQ,GAAG,UAAUC,EAAE,EAAEC,OAAO,EAAE;IACpDD,EAAE,CAACjC,IAAI,CAACkC,OAAO,EAAE,IAAI,CAAC;EAC1B,CAAC;EACDrC,WAAW,CAACI,SAAS,CAAC8B,WAAW,GAAG,UAAUF,CAAC,EAAEC,CAAC,EAAE;IAChD,IAAIK,KAAK,GAAG,IAAI,CAACC,qBAAqB,CAACP,CAAC,EAAEC,CAAC,CAAC;IAC5C,IAAIO,IAAI,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACjC,OAAOD,IAAI,CAACT,OAAO,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3C,CAAC;EACDtC,WAAW,CAACI,SAAS,CAACsC,YAAY,GAAG,YAAY;IAC7C,IAAIF,IAAI,GAAG,IAAI,CAACG,UAAU;IAC1B,IAAI,CAAC,IAAI,CAACA,UAAU,IAAI,IAAI,CAACC,OAAO,EAAE;MAClC,IAAItB,SAAS,GAAG,IAAI,CAACA,SAAS;MAC9B,IAAIuB,MAAM,GAAG,IAAI,CAACJ,eAAe,CAAC,CAAC;MACnC,IAAI5C,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAIiD,UAAU,GAAGjD,KAAK,CAACP,UAAU,IAAI,CAAC;MACtC,IAAIC,aAAa,GAAGM,KAAK,CAACN,aAAa,IAAI,CAAC;MAC5C,IAAIC,aAAa,GAAGK,KAAK,CAACL,aAAa,IAAI,CAAC;MAC5CgD,IAAI,GAAG,IAAI,CAACG,UAAU,KAAK,IAAI,CAACA,UAAU,GAAG,IAAIhE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1E,IAAI2C,SAAS,EAAE;QACX3C,YAAY,CAACoE,cAAc,CAACP,IAAI,EAAEK,MAAM,EAAEvB,SAAS,CAAC;MACxD,CAAC,MACI;QACDkB,IAAI,CAACQ,IAAI,CAACH,MAAM,CAAC;MACrB;MACA,IAAIC,UAAU,IAAIvD,aAAa,IAAIC,aAAa,EAAE;QAC9CgD,IAAI,CAACS,KAAK,IAAIH,UAAU,GAAG,CAAC,GAAG5D,IAAI,CAACgE,GAAG,CAAC3D,aAAa,CAAC;QACtDiD,IAAI,CAACW,MAAM,IAAIL,UAAU,GAAG,CAAC,GAAG5D,IAAI,CAACgE,GAAG,CAAC1D,aAAa,CAAC;QACvDgD,IAAI,CAACR,CAAC,GAAG9C,IAAI,CAACkE,GAAG,CAACZ,IAAI,CAACR,CAAC,EAAEQ,IAAI,CAACR,CAAC,GAAGzC,aAAa,GAAGuD,UAAU,CAAC;QAC9DN,IAAI,CAACP,CAAC,GAAG/C,IAAI,CAACkE,GAAG,CAACZ,IAAI,CAACP,CAAC,EAAEO,IAAI,CAACP,CAAC,GAAGzC,aAAa,GAAGsD,UAAU,CAAC;MAClE;MACA,IAAIO,SAAS,GAAG,IAAI,CAACC,kBAAkB;MACvC,IAAI,CAACd,IAAI,CAACe,MAAM,CAAC,CAAC,EAAE;QAChBf,IAAI,CAACR,CAAC,GAAG9C,IAAI,CAACsE,KAAK,CAAChB,IAAI,CAACR,CAAC,GAAGqB,SAAS,CAAC;QACvCb,IAAI,CAACP,CAAC,GAAG/C,IAAI,CAACsE,KAAK,CAAChB,IAAI,CAACP,CAAC,GAAGoB,SAAS,CAAC;QACvCb,IAAI,CAACS,KAAK,GAAG/D,IAAI,CAACuE,IAAI,CAACjB,IAAI,CAACS,KAAK,GAAG,CAAC,GAAGI,SAAS,GAAG,CAAC,CAAC;QACtDb,IAAI,CAACW,MAAM,GAAGjE,IAAI,CAACuE,IAAI,CAACjB,IAAI,CAACW,MAAM,GAAG,CAAC,GAAGE,SAAS,GAAG,CAAC,CAAC;MAC5D;IACJ;IACA,OAAOb,IAAI;EACf,CAAC;EACDxC,WAAW,CAACI,SAAS,CAACsD,gBAAgB,GAAG,UAAUC,SAAS,EAAE;IAC1D,IAAIA,SAAS,EAAE;MACX,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,IAAI,IAAIjF,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzE,IAAI,CAACiF,cAAc,CAACZ,IAAI,CAACW,SAAS,CAAC;IACvC,CAAC,MACI;MACD,IAAI,CAACC,cAAc,GAAG,IAAI;IAC9B;EACJ,CAAC;EACD5D,WAAW,CAACI,SAAS,CAACyD,gBAAgB,GAAG,YAAY;IACjD,OAAO,IAAI,CAACD,cAAc;EAC9B,CAAC;EACD5D,WAAW,CAACI,SAAS,CAAC0D,YAAY,GAAG,UAAUC,IAAI,EAAE;IACjD,OAAO,IAAI,CAACC,OAAO,CAAC,OAAO,EAAED,IAAI,CAAC;EACtC,CAAC;EACD/D,WAAW,CAACI,SAAS,CAAC6D,qBAAqB,GAAG,UAAUC,SAAS,EAAE;IAC/D,IAAIA,SAAS,KAAK,OAAO,EAAE;MACvB,IAAI,CAACC,UAAU,CAAC,CAAC;IACrB,CAAC,MACI;MACD,IAAI,CAACC,UAAU,CAAC,CAAC;IACrB;EACJ,CAAC;EACDpE,WAAW,CAACI,SAAS,CAACO,MAAM,GAAG,UAAUF,GAAG,EAAE4D,KAAK,EAAE;IACjD,IAAI5D,GAAG,KAAK,OAAO,EAAE;MACjBR,MAAM,CAACG,SAAS,CAACO,MAAM,CAACR,IAAI,CAAC,IAAI,EAAEM,GAAG,EAAE4D,KAAK,CAAC;IAClD,CAAC,MACI;MACD,IAAI,CAAC,IAAI,CAACxE,KAAK,EAAE;QACb,IAAI,CAACa,QAAQ,CAAC2D,KAAK,CAAC;MACxB,CAAC,MACI;QACD,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC;MACxB;IACJ;EACJ,CAAC;EACDrE,WAAW,CAACI,SAAS,CAACkE,QAAQ,GAAG,UAAUC,QAAQ,EAAEF,KAAK,EAAE;IACxD,IAAI,OAAOE,QAAQ,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAAC1E,KAAK,CAAC0E,QAAQ,CAAC,GAAGF,KAAK;IAChC,CAAC,MACI;MACDxF,MAAM,CAAC,IAAI,CAACgB,KAAK,EAAE0E,QAAQ,CAAC;IAChC;IACA,IAAI,CAACJ,UAAU,CAAC,CAAC;IACjB,OAAO,IAAI;EACf,CAAC;EACDnE,WAAW,CAACI,SAAS,CAAC+D,UAAU,GAAG,UAAUK,SAAS,EAAE;IACpD,IAAI,CAACA,SAAS,EAAE;MACZ,IAAI,CAACJ,UAAU,CAAC,CAAC;IACrB;IACA,IAAI,CAACxB,OAAO,IAAI5D,iBAAiB;IACjC,IAAI,IAAI,CAACyF,KAAK,EAAE;MACZ,IAAI,CAACA,KAAK,GAAG,IAAI;IACrB;EACJ,CAAC;EACDzE,WAAW,CAACI,SAAS,CAACsE,KAAK,GAAG,YAAY;IACtC,IAAI,CAACP,UAAU,CAAC,CAAC;EACrB,CAAC;EACDnE,WAAW,CAACI,SAAS,CAACuE,YAAY,GAAG,YAAY;IAC7C,OAAO,CAAC,EAAE,IAAI,CAAC/B,OAAO,GAAG5D,iBAAiB,CAAC;EAC/C,CAAC;EACDgB,WAAW,CAACI,SAAS,CAACwE,YAAY,GAAG,YAAY;IAC7C,IAAI,CAAChC,OAAO,IAAI,CAAC5D,iBAAiB;EACtC,CAAC;EACDgB,WAAW,CAACI,SAAS,CAACyE,WAAW,GAAG,UAAUC,GAAG,EAAE;IAC/C,OAAOhG,YAAY,CAACO,oBAAoB,EAAEyF,GAAG,CAAC;EAClD,CAAC;EACD9E,WAAW,CAACI,SAAS,CAACM,QAAQ,GAAG,UAAUoE,GAAG,EAAE;IAC5C,IAAI,CAACA,GAAG,CAAC7F,eAAe,CAAC,EAAE;MACvB6F,GAAG,GAAG,IAAI,CAACD,WAAW,CAACC,GAAG,CAAC;IAC/B;IACA,IAAI,IAAI,CAACC,SAAS,EAAE;MAChB,IAAI,CAACC,YAAY,GAAGF,GAAG;IAC3B,CAAC,MACI;MACD,IAAI,CAACjF,KAAK,GAAGiF,GAAG;IACpB;IACA,IAAI,CAACX,UAAU,CAAC,CAAC;EACrB,CAAC;EACDnE,WAAW,CAACI,SAAS,CAAC6E,aAAa,GAAG,UAAUH,GAAG,EAAE;IACjD,OAAOA,GAAG,CAAC7F,eAAe,CAAC;EAC/B,CAAC;EACDe,WAAW,CAACI,SAAS,CAAC8E,kBAAkB,GAAG,UAAUC,OAAO,EAAE;IAC1DlF,MAAM,CAACG,SAAS,CAAC8E,kBAAkB,CAAC/E,IAAI,CAAC,IAAI,EAAEgF,OAAO,CAAC;IACvD,IAAIC,WAAW,GAAG,IAAI,CAACC,YAAY;IACnC,IAAIF,OAAO,CAACtF,KAAK,IAAI,CAACuF,WAAW,CAACvF,KAAK,EAAE;MACrCuF,WAAW,CAACvF,KAAK,GAAG,IAAI,CAACyF,WAAW,CAAC,IAAI,CAACT,WAAW,CAAC,CAAC,EAAE,IAAI,CAAChF,KAAK,CAAC;IACxE;IACA,IAAI,CAAC0F,oBAAoB,CAACJ,OAAO,EAAEC,WAAW,EAAEtF,mBAAmB,CAAC;EACxE,CAAC;EACDE,WAAW,CAACI,SAAS,CAACoF,cAAc,GAAG,UAAUC,SAAS,EAAEC,KAAK,EAAEN,WAAW,EAAEO,iBAAiB,EAAEC,UAAU,EAAEC,YAAY,EAAE;IACzH5F,MAAM,CAACG,SAAS,CAACoF,cAAc,CAACrF,IAAI,CAAC,IAAI,EAAEsF,SAAS,EAAEC,KAAK,EAAEN,WAAW,EAAEO,iBAAiB,EAAEC,UAAU,EAAEC,YAAY,CAAC;IACtH,IAAIC,oBAAoB,GAAG,EAAEJ,KAAK,IAAIC,iBAAiB,CAAC;IACxD,IAAII,WAAW;IACf,IAAIL,KAAK,IAAIA,KAAK,CAAC7F,KAAK,EAAE;MACtB,IAAI+F,UAAU,EAAE;QACZ,IAAID,iBAAiB,EAAE;UACnBI,WAAW,GAAGL,KAAK,CAAC7F,KAAK;QAC7B,CAAC,MACI;UACDkG,WAAW,GAAG,IAAI,CAACT,WAAW,CAAC,IAAI,CAACT,WAAW,CAAC,CAAC,EAAEO,WAAW,CAACvF,KAAK,CAAC;UACrE,IAAI,CAACyF,WAAW,CAACS,WAAW,EAAEL,KAAK,CAAC7F,KAAK,CAAC;QAC9C;MACJ,CAAC,MACI;QACDkG,WAAW,GAAG,IAAI,CAACT,WAAW,CAAC,IAAI,CAACT,WAAW,CAAC,CAAC,EAAEc,iBAAiB,GAAG,IAAI,CAAC9F,KAAK,GAAGuF,WAAW,CAACvF,KAAK,CAAC;QACtG,IAAI,CAACyF,WAAW,CAACS,WAAW,EAAEL,KAAK,CAAC7F,KAAK,CAAC;MAC9C;IACJ,CAAC,MACI,IAAIiG,oBAAoB,EAAE;MAC3BC,WAAW,GAAGX,WAAW,CAACvF,KAAK;IACnC;IACA,IAAIkG,WAAW,EAAE;MACb,IAAIH,UAAU,EAAE;QACZ,IAAII,WAAW,GAAG,IAAI,CAACnG,KAAK;QAC5B,IAAI,CAACA,KAAK,GAAG,IAAI,CAACgF,WAAW,CAACiB,oBAAoB,GAAG,CAAC,CAAC,GAAGE,WAAW,CAAC;QACtE,IAAIF,oBAAoB,EAAE;UACtB,IAAIG,WAAW,GAAGrH,IAAI,CAACoH,WAAW,CAAC;UACnC,KAAK,IAAIzF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,WAAW,CAACzF,MAAM,EAAED,CAAC,EAAE,EAAE;YACzC,IAAIE,GAAG,GAAGwF,WAAW,CAAC1F,CAAC,CAAC;YACxB,IAAIE,GAAG,IAAIsF,WAAW,EAAE;cACpBA,WAAW,CAACtF,GAAG,CAAC,GAAGsF,WAAW,CAACtF,GAAG,CAAC;cACnC,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC,GAAGuF,WAAW,CAACvF,GAAG,CAAC;YACtC;UACJ;QACJ;QACA,IAAIyF,UAAU,GAAGtH,IAAI,CAACmH,WAAW,CAAC;QAClC,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2F,UAAU,CAAC1F,MAAM,EAAED,CAAC,EAAE,EAAE;UACxC,IAAIE,GAAG,GAAGyF,UAAU,CAAC3F,CAAC,CAAC;UACvB,IAAI,CAACV,KAAK,CAACY,GAAG,CAAC,GAAG,IAAI,CAACZ,KAAK,CAACY,GAAG,CAAC;QACrC;QACA,IAAI,CAAC0F,gBAAgB,CAACV,SAAS,EAAE;UAC7B5F,KAAK,EAAEkG;QACX,CAAC,EAAEF,YAAY,EAAE,IAAI,CAACO,sBAAsB,CAAC,CAAC,CAAC;MACnD,CAAC,MACI;QACD,IAAI,CAAC1F,QAAQ,CAACqF,WAAW,CAAC;MAC9B;IACJ;IACA,IAAIM,UAAU,GAAG,IAAI,CAACtB,SAAS,GAAGhF,kCAAkC,GAAGD,mBAAmB;IAC1F,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8F,UAAU,CAAC7F,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIE,GAAG,GAAG4F,UAAU,CAAC9F,CAAC,CAAC;MACvB,IAAImF,KAAK,IAAIA,KAAK,CAACjF,GAAG,CAAC,IAAI,IAAI,EAAE;QAC7B,IAAI,CAACA,GAAG,CAAC,GAAGiF,KAAK,CAACjF,GAAG,CAAC;MAC1B,CAAC,MACI,IAAIqF,oBAAoB,EAAE;QAC3B,IAAIV,WAAW,CAAC3E,GAAG,CAAC,IAAI,IAAI,EAAE;UAC1B,IAAI,CAACA,GAAG,CAAC,GAAG2E,WAAW,CAAC3E,GAAG,CAAC;QAChC;MACJ;IACJ;EACJ,CAAC;EACDT,WAAW,CAACI,SAAS,CAACkG,YAAY,GAAG,UAAUC,MAAM,EAAE;IACnD,IAAIC,WAAW,GAAGvG,MAAM,CAACG,SAAS,CAACkG,YAAY,CAACnG,IAAI,CAAC,IAAI,EAAEoG,MAAM,CAAC;IAClE,IAAIE,WAAW;IACf,KAAK,IAAIlG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgG,MAAM,CAAC/F,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAImF,KAAK,GAAGa,MAAM,CAAChG,CAAC,CAAC;MACrB,IAAImF,KAAK,CAAC7F,KAAK,EAAE;QACb4G,WAAW,GAAGA,WAAW,IAAI,CAAC,CAAC;QAC/B,IAAI,CAACnB,WAAW,CAACmB,WAAW,EAAEf,KAAK,CAAC7F,KAAK,CAAC;MAC9C;IACJ;IACA,IAAI4G,WAAW,EAAE;MACbD,WAAW,CAAC3G,KAAK,GAAG4G,WAAW;IACnC;IACA,OAAOD,WAAW;EACtB,CAAC;EACDxG,WAAW,CAACI,SAAS,CAACkF,WAAW,GAAG,UAAUS,WAAW,EAAEC,WAAW,EAAE;IACpEnH,MAAM,CAACkH,WAAW,EAAEC,WAAW,CAAC;IAChC,OAAOD,WAAW;EACtB,CAAC;EACD/F,WAAW,CAACI,SAAS,CAACgG,sBAAsB,GAAG,YAAY;IACvD,OAAOxG,8BAA8B;EACzC,CAAC;EACDI,WAAW,CAAC0G,gBAAgB,GAAI,YAAY;IACxC,IAAIC,SAAS,GAAG3G,WAAW,CAACI,SAAS;IACrCuG,SAAS,CAACC,IAAI,GAAG,aAAa;IAC9BD,SAAS,CAACnF,SAAS,GAAG,KAAK;IAC3BmF,SAAS,CAACE,CAAC,GAAG,CAAC;IACfF,SAAS,CAACG,EAAE,GAAG,CAAC;IAChBH,SAAS,CAACI,MAAM,GAAG,CAAC;IACpBJ,SAAS,CAAClF,OAAO,GAAG,KAAK;IACzBkF,SAAS,CAACK,MAAM,GAAG,SAAS;IAC5BL,SAAS,CAACM,SAAS,GAAG,KAAK;IAC3BN,SAAS,CAACO,WAAW,GAAG,KAAK;IAC7BP,SAAS,CAAClC,KAAK,GAAG,IAAI;IACtBkC,SAAS,CAACrD,kBAAkB,GAAG,CAAC;IAChCqD,SAAS,CAAC/D,OAAO,GAAG7D,UAAU,GAAGC,iBAAiB;EACtD,CAAC,CAAE,CAAC;EACJ,OAAOgB,WAAW;AACtB,CAAC,CAACtB,OAAO,CAAE;AACX,IAAIyI,OAAO,GAAG,IAAIxI,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1C,IAAIyI,QAAQ,GAAG,IAAIzI,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3C,SAAS+C,mBAAmBA,CAAC2F,EAAE,EAAEpE,KAAK,EAAEE,MAAM,EAAE;EAC5CgE,OAAO,CAACnE,IAAI,CAACqE,EAAE,CAAC5E,eAAe,CAAC,CAAC,CAAC;EAClC,IAAI4E,EAAE,CAAC/F,SAAS,EAAE;IACd6F,OAAO,CAACpE,cAAc,CAACsE,EAAE,CAAC/F,SAAS,CAAC;EACxC;EACA8F,QAAQ,CAACnE,KAAK,GAAGA,KAAK;EACtBmE,QAAQ,CAACjE,MAAM,GAAGA,MAAM;EACxB,OAAO,CAACgE,OAAO,CAACG,SAAS,CAACF,QAAQ,CAAC;AACvC;AACA,eAAepH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}