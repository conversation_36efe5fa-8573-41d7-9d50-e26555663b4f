{"ast": null, "code": "import { dataApi } from '../api';\nexport default {\n  name: 'TableSelector',\n  data() {\n    return {\n      tables: []\n    };\n  },\n  mounted() {\n    this.loadTables();\n  },\n  methods: {\n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables();\n        this.tables = response.data;\n      } catch (error) {\n        this.$message.error('加载数据表失败');\n        console.error(error);\n      }\n    },\n    selectTable(table) {\n      this.$emit('select-table', table);\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "name", "data", "tables", "mounted", "loadTables", "methods", "response", "getAllTables", "error", "$message", "console", "selectTable", "table", "$emit"], "sources": ["src/components/TableSelector.vue"], "sourcesContent": ["<template>\r\n  <div class=\"table-selector\">\r\n    <h2>请选择要查询的数据表</h2>\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"8\" v-for=\"table in tables\" :key=\"table.tableCode\">\r\n        <el-card class=\"table-card\" @click.native=\"selectTable(table)\">\r\n          <div slot=\"header\">\r\n            <span>{{ table.tableName }}</span>\r\n          </div>\r\n          <div class=\"table-description\">\r\n            {{ table.description }}\r\n          </div>\r\n          <div class=\"select-button\">\r\n            <el-button type=\"primary\" size=\"small\">选择</el-button>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { dataApi } from '../api'\r\n\r\nexport default {\r\n  name: 'TableSelector',\r\n  data() {\r\n    return {\r\n      tables: []\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadTables()\r\n  },\r\n  methods: {\r\n    async loadTables() {\r\n      try {\r\n        const response = await dataApi.getAllTables()\r\n        this.tables = response.data\r\n      } catch (error) {\r\n        this.$message.error('加载数据表失败')\r\n        console.error(error)\r\n      }\r\n    },\r\n    selectTable(table) {\r\n      this.$emit('select-table', table)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.table-card {\r\n  margin-bottom: 20px;\r\n  cursor: pointer;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.table-card:hover {\r\n  transform: translateY(-5px);\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.table-description {\r\n  min-height: 60px;\r\n  color: #606266;\r\n}\r\n\r\n.select-button {\r\n  margin-top: 15px;\r\n  text-align: center;\r\n}\r\n</style>"], "mappings": "AAsBA,SAAAA,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,MAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACA,MAAAD,WAAA;MACA;QACA,MAAAE,QAAA,SAAAP,OAAA,CAAAQ,YAAA;QACA,KAAAL,MAAA,GAAAI,QAAA,CAAAL,IAAA;MACA,SAAAO,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,CAAAA,KAAA;MACA;IACA;IACAG,YAAAC,KAAA;MACA,KAAAC,KAAA,iBAAAD,KAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}