{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, createHashMap, assert, map } from 'zrender/lib/core/util.js';\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nvar DimensionUserOuput = /** @class */function () {\n  function DimensionUserOuput(encode, dimRequest) {\n    this._encode = encode;\n    this._schema = dimRequest;\n  }\n  DimensionUserOuput.prototype.get = function () {\n    return {\n      // Do not generate full dimension name until fist used.\n      fullDimensions: this._getFullDimensionNames(),\n      encode: this._encode\n    };\n  };\n  /**\r\n   * Get all data store dimension names.\r\n   * Theoretically a series data store is defined both by series and used dataset (if any).\r\n   * If some dimensions are omitted for performance reason in `this.dimensions`,\r\n   * the dimension name may not be auto-generated if user does not specify a dimension name.\r\n   * In this case, the dimension name is `null`/`undefined`.\r\n   */\n  DimensionUserOuput.prototype._getFullDimensionNames = function () {\n    if (!this._cachedDimNames) {\n      this._cachedDimNames = this._schema ? this._schema.makeOutputDimensionNames() : [];\n    }\n    return this._cachedDimNames;\n  };\n  return DimensionUserOuput;\n}();\n;\nexport function summarizeDimensions(data, schema) {\n  var summary = {};\n  var encode = summary.encode = {};\n  var notExtraCoordDimMap = createHashMap();\n  var defaultedLabel = [];\n  var defaultedTooltip = [];\n  var userOutputEncode = {};\n  each(data.dimensions, function (dimName) {\n    var dimItem = data.getDimensionInfo(dimName);\n    var coordDim = dimItem.coordDim;\n    if (coordDim) {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(VISUAL_DIMENSIONS.get(coordDim) == null);\n      }\n      var coordDimIndex = dimItem.coordDimIndex;\n      getOrCreateEncodeArr(encode, coordDim)[coordDimIndex] = dimName;\n      if (!dimItem.isExtraCoord) {\n        notExtraCoordDimMap.set(coordDim, 1);\n        // Use the last coord dim (and label friendly) as default label,\n        // because when dataset is used, it is hard to guess which dimension\n        // can be value dimension. If both show x, y on label is not look good,\n        // and conventionally y axis is focused more.\n        if (mayLabelDimType(dimItem.type)) {\n          defaultedLabel[0] = dimName;\n        }\n        // User output encode do not contain generated coords.\n        // And it only has index. User can use index to retrieve value from the raw item array.\n        getOrCreateEncodeArr(userOutputEncode, coordDim)[coordDimIndex] = data.getDimensionIndex(dimItem.name);\n      }\n      if (dimItem.defaultTooltip) {\n        defaultedTooltip.push(dimName);\n      }\n    }\n    VISUAL_DIMENSIONS.each(function (v, otherDim) {\n      var encodeArr = getOrCreateEncodeArr(encode, otherDim);\n      var dimIndex = dimItem.otherDims[otherDim];\n      if (dimIndex != null && dimIndex !== false) {\n        encodeArr[dimIndex] = dimItem.name;\n      }\n    });\n  });\n  var dataDimsOnCoord = [];\n  var encodeFirstDimNotExtra = {};\n  notExtraCoordDimMap.each(function (v, coordDim) {\n    var dimArr = encode[coordDim];\n    encodeFirstDimNotExtra[coordDim] = dimArr[0];\n    // Not necessary to remove duplicate, because a data\n    // dim canot on more than one coordDim.\n    dataDimsOnCoord = dataDimsOnCoord.concat(dimArr);\n  });\n  summary.dataDimsOnCoord = dataDimsOnCoord;\n  summary.dataDimIndicesOnCoord = map(dataDimsOnCoord, function (dimName) {\n    return data.getDimensionInfo(dimName).storeDimIndex;\n  });\n  summary.encodeFirstDimNotExtra = encodeFirstDimNotExtra;\n  var encodeLabel = encode.label;\n  // FIXME `encode.label` is not recommended, because formatter cannot be set\n  // in this way. Use label.formatter instead. Maybe remove this approach someday.\n  if (encodeLabel && encodeLabel.length) {\n    defaultedLabel = encodeLabel.slice();\n  }\n  var encodeTooltip = encode.tooltip;\n  if (encodeTooltip && encodeTooltip.length) {\n    defaultedTooltip = encodeTooltip.slice();\n  } else if (!defaultedTooltip.length) {\n    defaultedTooltip = defaultedLabel.slice();\n  }\n  encode.defaultedLabel = defaultedLabel;\n  encode.defaultedTooltip = defaultedTooltip;\n  summary.userOutput = new DimensionUserOuput(userOutputEncode, schema);\n  return summary;\n}\nfunction getOrCreateEncodeArr(encode, dim) {\n  if (!encode.hasOwnProperty(dim)) {\n    encode[dim] = [];\n  }\n  return encode[dim];\n}\n// FIXME:TS should be type `AxisType`\nexport function getDimensionTypeByAxis(axisType) {\n  return axisType === 'category' ? 'ordinal' : axisType === 'time' ? 'time' : 'float';\n}\nfunction mayLabelDimType(dimType) {\n  // In most cases, ordinal and time do not suitable for label.\n  // Ordinal info can be displayed on axis. Time is too long.\n  return !(dimType === 'ordinal' || dimType === 'time');\n}\n// function findTheLastDimMayLabel(data) {\n//     // Get last value dim\n//     let dimensions = data.dimensions.slice();\n//     let valueType;\n//     let valueDim;\n//     while (dimensions.length && (\n//         valueDim = dimensions.pop(),\n//         valueType = data.getDimensionInfo(valueDim).type,\n//         valueType === 'ordinal' || valueType === 'time'\n//     )) {} // jshint ignore:line\n//     return valueDim;\n// }", "map": {"version": 3, "names": ["each", "createHashMap", "assert", "map", "VISUAL_DIMENSIONS", "DimensionUserOuput", "encode", "dimRequest", "_encode", "_schema", "prototype", "get", "fullDimensions", "_getFullDimensionNames", "_cachedDimNames", "makeOutputDimensionNames", "summarizeDimensions", "data", "schema", "summary", "notExtraCoordDimMap", "defaultedLabel", "defaultedTooltip", "userOutputEncode", "dimensions", "dimName", "dimItem", "getDimensionInfo", "coordDim", "process", "env", "NODE_ENV", "coordDimIndex", "getOrCreateEncodeArr", "isExtraCoord", "set", "mayLabelDimType", "type", "getDimensionIndex", "name", "defaultTooltip", "push", "v", "otherDim", "encodeArr", "dimIndex", "otherDims", "dataDimsOnCoord", "encodeFirstDimNotExtra", "dimArr", "concat", "dataDimIndicesOnCoord", "storeDimIndex", "encodeLabel", "label", "length", "slice", "encodeTooltip", "tooltip", "userOutput", "dim", "hasOwnProperty", "getDimensionTypeByAxis", "axisType", "dimType"], "sources": ["E:/frontCodeCode/datafront/node_modules/echarts/lib/data/helper/dimensionHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, createHashMap, assert, map } from 'zrender/lib/core/util.js';\nimport { VISUAL_DIMENSIONS } from '../../util/types.js';\nvar DimensionUserOuput = /** @class */function () {\n  function DimensionUserOuput(encode, dimRequest) {\n    this._encode = encode;\n    this._schema = dimRequest;\n  }\n  DimensionUserOuput.prototype.get = function () {\n    return {\n      // Do not generate full dimension name until fist used.\n      fullDimensions: this._getFullDimensionNames(),\n      encode: this._encode\n    };\n  };\n  /**\r\n   * Get all data store dimension names.\r\n   * Theoretically a series data store is defined both by series and used dataset (if any).\r\n   * If some dimensions are omitted for performance reason in `this.dimensions`,\r\n   * the dimension name may not be auto-generated if user does not specify a dimension name.\r\n   * In this case, the dimension name is `null`/`undefined`.\r\n   */\n  DimensionUserOuput.prototype._getFullDimensionNames = function () {\n    if (!this._cachedDimNames) {\n      this._cachedDimNames = this._schema ? this._schema.makeOutputDimensionNames() : [];\n    }\n    return this._cachedDimNames;\n  };\n  return DimensionUserOuput;\n}();\n;\nexport function summarizeDimensions(data, schema) {\n  var summary = {};\n  var encode = summary.encode = {};\n  var notExtraCoordDimMap = createHashMap();\n  var defaultedLabel = [];\n  var defaultedTooltip = [];\n  var userOutputEncode = {};\n  each(data.dimensions, function (dimName) {\n    var dimItem = data.getDimensionInfo(dimName);\n    var coordDim = dimItem.coordDim;\n    if (coordDim) {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(VISUAL_DIMENSIONS.get(coordDim) == null);\n      }\n      var coordDimIndex = dimItem.coordDimIndex;\n      getOrCreateEncodeArr(encode, coordDim)[coordDimIndex] = dimName;\n      if (!dimItem.isExtraCoord) {\n        notExtraCoordDimMap.set(coordDim, 1);\n        // Use the last coord dim (and label friendly) as default label,\n        // because when dataset is used, it is hard to guess which dimension\n        // can be value dimension. If both show x, y on label is not look good,\n        // and conventionally y axis is focused more.\n        if (mayLabelDimType(dimItem.type)) {\n          defaultedLabel[0] = dimName;\n        }\n        // User output encode do not contain generated coords.\n        // And it only has index. User can use index to retrieve value from the raw item array.\n        getOrCreateEncodeArr(userOutputEncode, coordDim)[coordDimIndex] = data.getDimensionIndex(dimItem.name);\n      }\n      if (dimItem.defaultTooltip) {\n        defaultedTooltip.push(dimName);\n      }\n    }\n    VISUAL_DIMENSIONS.each(function (v, otherDim) {\n      var encodeArr = getOrCreateEncodeArr(encode, otherDim);\n      var dimIndex = dimItem.otherDims[otherDim];\n      if (dimIndex != null && dimIndex !== false) {\n        encodeArr[dimIndex] = dimItem.name;\n      }\n    });\n  });\n  var dataDimsOnCoord = [];\n  var encodeFirstDimNotExtra = {};\n  notExtraCoordDimMap.each(function (v, coordDim) {\n    var dimArr = encode[coordDim];\n    encodeFirstDimNotExtra[coordDim] = dimArr[0];\n    // Not necessary to remove duplicate, because a data\n    // dim canot on more than one coordDim.\n    dataDimsOnCoord = dataDimsOnCoord.concat(dimArr);\n  });\n  summary.dataDimsOnCoord = dataDimsOnCoord;\n  summary.dataDimIndicesOnCoord = map(dataDimsOnCoord, function (dimName) {\n    return data.getDimensionInfo(dimName).storeDimIndex;\n  });\n  summary.encodeFirstDimNotExtra = encodeFirstDimNotExtra;\n  var encodeLabel = encode.label;\n  // FIXME `encode.label` is not recommended, because formatter cannot be set\n  // in this way. Use label.formatter instead. Maybe remove this approach someday.\n  if (encodeLabel && encodeLabel.length) {\n    defaultedLabel = encodeLabel.slice();\n  }\n  var encodeTooltip = encode.tooltip;\n  if (encodeTooltip && encodeTooltip.length) {\n    defaultedTooltip = encodeTooltip.slice();\n  } else if (!defaultedTooltip.length) {\n    defaultedTooltip = defaultedLabel.slice();\n  }\n  encode.defaultedLabel = defaultedLabel;\n  encode.defaultedTooltip = defaultedTooltip;\n  summary.userOutput = new DimensionUserOuput(userOutputEncode, schema);\n  return summary;\n}\nfunction getOrCreateEncodeArr(encode, dim) {\n  if (!encode.hasOwnProperty(dim)) {\n    encode[dim] = [];\n  }\n  return encode[dim];\n}\n// FIXME:TS should be type `AxisType`\nexport function getDimensionTypeByAxis(axisType) {\n  return axisType === 'category' ? 'ordinal' : axisType === 'time' ? 'time' : 'float';\n}\nfunction mayLabelDimType(dimType) {\n  // In most cases, ordinal and time do not suitable for label.\n  // Ordinal info can be displayed on axis. Time is too long.\n  return !(dimType === 'ordinal' || dimType === 'time');\n}\n// function findTheLastDimMayLabel(data) {\n//     // Get last value dim\n//     let dimensions = data.dimensions.slice();\n//     let valueType;\n//     let valueDim;\n//     while (dimensions.length && (\n//         valueDim = dimensions.pop(),\n//         valueType = data.getDimensionInfo(valueDim).type,\n//         valueType === 'ordinal' || valueType === 'time'\n//     )) {} // jshint ignore:line\n//     return valueDim;\n// }"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,aAAa,EAAEC,MAAM,EAAEC,GAAG,QAAQ,0BAA0B;AAC3E,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,IAAIC,kBAAkB,GAAG,aAAa,YAAY;EAChD,SAASA,kBAAkBA,CAACC,MAAM,EAAEC,UAAU,EAAE;IAC9C,IAAI,CAACC,OAAO,GAAGF,MAAM;IACrB,IAAI,CAACG,OAAO,GAAGF,UAAU;EAC3B;EACAF,kBAAkB,CAACK,SAAS,CAACC,GAAG,GAAG,YAAY;IAC7C,OAAO;MACL;MACAC,cAAc,EAAE,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC7CP,MAAM,EAAE,IAAI,CAACE;IACf,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACEH,kBAAkB,CAACK,SAAS,CAACG,sBAAsB,GAAG,YAAY;IAChE,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;MACzB,IAAI,CAACA,eAAe,GAAG,IAAI,CAACL,OAAO,GAAG,IAAI,CAACA,OAAO,CAACM,wBAAwB,CAAC,CAAC,GAAG,EAAE;IACpF;IACA,OAAO,IAAI,CAACD,eAAe;EAC7B,CAAC;EACD,OAAOT,kBAAkB;AAC3B,CAAC,CAAC,CAAC;AACH;AACA,OAAO,SAASW,mBAAmBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAChD,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIb,MAAM,GAAGa,OAAO,CAACb,MAAM,GAAG,CAAC,CAAC;EAChC,IAAIc,mBAAmB,GAAGnB,aAAa,CAAC,CAAC;EACzC,IAAIoB,cAAc,GAAG,EAAE;EACvB,IAAIC,gBAAgB,GAAG,EAAE;EACzB,IAAIC,gBAAgB,GAAG,CAAC,CAAC;EACzBvB,IAAI,CAACiB,IAAI,CAACO,UAAU,EAAE,UAAUC,OAAO,EAAE;IACvC,IAAIC,OAAO,GAAGT,IAAI,CAACU,gBAAgB,CAACF,OAAO,CAAC;IAC5C,IAAIG,QAAQ,GAAGF,OAAO,CAACE,QAAQ;IAC/B,IAAIA,QAAQ,EAAE;MACZ,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC7B,MAAM,CAACE,iBAAiB,CAACO,GAAG,CAACiB,QAAQ,CAAC,IAAI,IAAI,CAAC;MACjD;MACA,IAAII,aAAa,GAAGN,OAAO,CAACM,aAAa;MACzCC,oBAAoB,CAAC3B,MAAM,EAAEsB,QAAQ,CAAC,CAACI,aAAa,CAAC,GAAGP,OAAO;MAC/D,IAAI,CAACC,OAAO,CAACQ,YAAY,EAAE;QACzBd,mBAAmB,CAACe,GAAG,CAACP,QAAQ,EAAE,CAAC,CAAC;QACpC;QACA;QACA;QACA;QACA,IAAIQ,eAAe,CAACV,OAAO,CAACW,IAAI,CAAC,EAAE;UACjChB,cAAc,CAAC,CAAC,CAAC,GAAGI,OAAO;QAC7B;QACA;QACA;QACAQ,oBAAoB,CAACV,gBAAgB,EAAEK,QAAQ,CAAC,CAACI,aAAa,CAAC,GAAGf,IAAI,CAACqB,iBAAiB,CAACZ,OAAO,CAACa,IAAI,CAAC;MACxG;MACA,IAAIb,OAAO,CAACc,cAAc,EAAE;QAC1BlB,gBAAgB,CAACmB,IAAI,CAAChB,OAAO,CAAC;MAChC;IACF;IACArB,iBAAiB,CAACJ,IAAI,CAAC,UAAU0C,CAAC,EAAEC,QAAQ,EAAE;MAC5C,IAAIC,SAAS,GAAGX,oBAAoB,CAAC3B,MAAM,EAAEqC,QAAQ,CAAC;MACtD,IAAIE,QAAQ,GAAGnB,OAAO,CAACoB,SAAS,CAACH,QAAQ,CAAC;MAC1C,IAAIE,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,KAAK,EAAE;QAC1CD,SAAS,CAACC,QAAQ,CAAC,GAAGnB,OAAO,CAACa,IAAI;MACpC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAIQ,eAAe,GAAG,EAAE;EACxB,IAAIC,sBAAsB,GAAG,CAAC,CAAC;EAC/B5B,mBAAmB,CAACpB,IAAI,CAAC,UAAU0C,CAAC,EAAEd,QAAQ,EAAE;IAC9C,IAAIqB,MAAM,GAAG3C,MAAM,CAACsB,QAAQ,CAAC;IAC7BoB,sBAAsB,CAACpB,QAAQ,CAAC,GAAGqB,MAAM,CAAC,CAAC,CAAC;IAC5C;IACA;IACAF,eAAe,GAAGA,eAAe,CAACG,MAAM,CAACD,MAAM,CAAC;EAClD,CAAC,CAAC;EACF9B,OAAO,CAAC4B,eAAe,GAAGA,eAAe;EACzC5B,OAAO,CAACgC,qBAAqB,GAAGhD,GAAG,CAAC4C,eAAe,EAAE,UAAUtB,OAAO,EAAE;IACtE,OAAOR,IAAI,CAACU,gBAAgB,CAACF,OAAO,CAAC,CAAC2B,aAAa;EACrD,CAAC,CAAC;EACFjC,OAAO,CAAC6B,sBAAsB,GAAGA,sBAAsB;EACvD,IAAIK,WAAW,GAAG/C,MAAM,CAACgD,KAAK;EAC9B;EACA;EACA,IAAID,WAAW,IAAIA,WAAW,CAACE,MAAM,EAAE;IACrClC,cAAc,GAAGgC,WAAW,CAACG,KAAK,CAAC,CAAC;EACtC;EACA,IAAIC,aAAa,GAAGnD,MAAM,CAACoD,OAAO;EAClC,IAAID,aAAa,IAAIA,aAAa,CAACF,MAAM,EAAE;IACzCjC,gBAAgB,GAAGmC,aAAa,CAACD,KAAK,CAAC,CAAC;EAC1C,CAAC,MAAM,IAAI,CAAClC,gBAAgB,CAACiC,MAAM,EAAE;IACnCjC,gBAAgB,GAAGD,cAAc,CAACmC,KAAK,CAAC,CAAC;EAC3C;EACAlD,MAAM,CAACe,cAAc,GAAGA,cAAc;EACtCf,MAAM,CAACgB,gBAAgB,GAAGA,gBAAgB;EAC1CH,OAAO,CAACwC,UAAU,GAAG,IAAItD,kBAAkB,CAACkB,gBAAgB,EAAEL,MAAM,CAAC;EACrE,OAAOC,OAAO;AAChB;AACA,SAASc,oBAAoBA,CAAC3B,MAAM,EAAEsD,GAAG,EAAE;EACzC,IAAI,CAACtD,MAAM,CAACuD,cAAc,CAACD,GAAG,CAAC,EAAE;IAC/BtD,MAAM,CAACsD,GAAG,CAAC,GAAG,EAAE;EAClB;EACA,OAAOtD,MAAM,CAACsD,GAAG,CAAC;AACpB;AACA;AACA,OAAO,SAASE,sBAAsBA,CAACC,QAAQ,EAAE;EAC/C,OAAOA,QAAQ,KAAK,UAAU,GAAG,SAAS,GAAGA,QAAQ,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO;AACrF;AACA,SAAS3B,eAAeA,CAAC4B,OAAO,EAAE;EAChC;EACA;EACA,OAAO,EAAEA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,MAAM,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}