{"ast": null, "code": "'use strict';\n\nrequire(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.filter.js\");\nrequire(\"core-js/modules/es.iterator.for-each.js\");\nrequire(\"core-js/modules/es.iterator.map.js\");\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n  return typeof obj;\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n};\n\n/**\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version {{version}}\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n//\n// Cross module loader\n// Supported: Node, AMD, Browser globals\n//\n;\n(function (root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define(factory);\n  } else if ((typeof module === 'undefined' ? 'undefined' : _typeof(module)) === 'object' && module.exports) {\n    // Node. Does not work with strict CommonJS, but\n    // only CommonJS-like environments that support module.exports,\n    // like Node.\n    module.exports = factory();\n  } else {\n    // Browser globals (root is window)\n    root.Popper = factory();\n  }\n})(undefined, function () {\n  'use strict';\n\n  var root = window;\n\n  // default options\n  var DEFAULTS = {\n    // placement of the popper\n    placement: 'bottom',\n    gpuAcceleration: true,\n    // shift popper from its origin by the given amount of pixels (can be negative)\n    offset: 0,\n    // the element which will act as boundary of the popper\n    boundariesElement: 'viewport',\n    // amount of pixel used to define a minimum distance between the boundaries and the popper\n    boundariesPadding: 5,\n    // popper will try to prevent overflow following this order,\n    // by default, then, it could overflow on the left and on top of the boundariesElement\n    preventOverflowOrder: ['left', 'right', 'top', 'bottom'],\n    // the behavior used by flip to change the placement of the popper\n    flipBehavior: 'flip',\n    arrowElement: '[x-arrow]',\n    arrowOffset: 0,\n    // list of functions used to modify the offsets before they are applied to the popper\n    modifiers: ['shift', 'offset', 'preventOverflow', 'keepTogether', 'arrow', 'flip', 'applyStyle'],\n    modifiersIgnored: [],\n    forceAbsolute: false\n  };\n\n  /**\n   * Create a new Popper.js instance\n   * @constructor Popper\n   * @param {HTMLElement} reference - The reference element used to position the popper\n   * @param {HTMLElement|Object} popper\n   *      The HTML element used as popper, or a configuration used to generate the popper.\n   * @param {String} [popper.tagName='div'] The tag name of the generated popper.\n   * @param {Array} [popper.classNames=['popper']] Array of classes to apply to the generated popper.\n   * @param {Array} [popper.attributes] Array of attributes to apply, specify `attr:value` to assign a value to it.\n   * @param {HTMLElement|String} [popper.parent=window.document.body] The parent element, given as HTMLElement or as query string.\n   * @param {String} [popper.content=''] The content of the popper, it can be text, html, or node; if it is not text, set `contentType` to `html` or `node`.\n   * @param {String} [popper.contentType='text'] If `html`, the `content` will be parsed as HTML. If `node`, it will be appended as-is.\n   * @param {String} [popper.arrowTagName='div'] Same as `popper.tagName` but for the arrow element.\n   * @param {Array} [popper.arrowClassNames='popper__arrow'] Same as `popper.classNames` but for the arrow element.\n   * @param {String} [popper.arrowAttributes=['x-arrow']] Same as `popper.attributes` but for the arrow element.\n   * @param {Object} options\n   * @param {String} [options.placement=bottom]\n   *      Placement of the popper accepted values: `top(-start, -end), right(-start, -end), bottom(-start, -right),\n   *      left(-start, -end)`\n   *\n   * @param {HTMLElement|String} [options.arrowElement='[x-arrow]']\n   *      The DOM Node used as arrow for the popper, or a CSS selector used to get the DOM node. It must be child of\n   *      its parent Popper. Popper.js will apply to the given element the style required to align the arrow with its\n   *      reference element.\n   *      By default, it will look for a child node of the popper with the `x-arrow` attribute.\n   *\n   * @param {Boolean} [options.gpuAcceleration=true]\n   *      When this property is set to true, the popper position will be applied using CSS3 translate3d, allowing the\n   *      browser to use the GPU to accelerate the rendering.\n   *      If set to false, the popper will be placed using `top` and `left` properties, not using the GPU.\n   *\n   * @param {Number} [options.offset=0]\n   *      Amount of pixels the popper will be shifted (can be negative).\n   *\n   * @param {String|Element} [options.boundariesElement='viewport']\n   *      The element which will define the boundaries of the popper position, the popper will never be placed outside\n   *      of the defined boundaries (except if `keepTogether` is enabled)\n   *\n   * @param {Number} [options.boundariesPadding=5]\n   *      Additional padding for the boundaries\n   *\n   * @param {Array} [options.preventOverflowOrder=['left', 'right', 'top', 'bottom']]\n   *      Order used when Popper.js tries to avoid overflows from the boundaries, they will be checked in order,\n   *      this means that the last ones will never overflow\n   *\n   * @param {String|Array} [options.flipBehavior='flip']\n   *      The behavior used by the `flip` modifier to change the placement of the popper when the latter is trying to\n   *      overlap its reference element. Defining `flip` as value, the placement will be flipped on\n   *      its axis (`right - left`, `top - bottom`).\n   *      You can even pass an array of placements (eg: `['right', 'left', 'top']` ) to manually specify\n   *      how alter the placement when a flip is needed. (eg. in the above example, it would first flip from right to left,\n   *      then, if even in its new placement, the popper is overlapping its reference element, it will be moved to top)\n   *\n   * @param {Array} [options.modifiers=[ 'shift', 'offset', 'preventOverflow', 'keepTogether', 'arrow', 'flip', 'applyStyle']]\n   *      List of functions used to modify the data before they are applied to the popper, add your custom functions\n   *      to this array to edit the offsets and placement.\n   *      The function should reflect the @params and @returns of preventOverflow\n   *\n   * @param {Array} [options.modifiersIgnored=[]]\n   *      Put here any built-in modifier name you want to exclude from the modifiers list\n   *      The function should reflect the @params and @returns of preventOverflow\n   *\n   * @param {Boolean} [options.removeOnDestroy=false]\n   *      Set to true if you want to automatically remove the popper when you call the `destroy` method.\n   */\n  function Popper(reference, popper, options) {\n    this._reference = reference.jquery ? reference[0] : reference;\n    this.state = {};\n\n    // if the popper variable is a configuration object, parse it to generate an HTMLElement\n    // generate a default popper if is not defined\n    var isNotDefined = typeof popper === 'undefined' || popper === null;\n    var isConfig = popper && Object.prototype.toString.call(popper) === '[object Object]';\n    if (isNotDefined || isConfig) {\n      this._popper = this.parse(isConfig ? popper : {});\n    }\n    // otherwise, use the given HTMLElement as popper\n    else {\n      this._popper = popper.jquery ? popper[0] : popper;\n    }\n\n    // with {} we create a new object with the options inside it\n    this._options = Object.assign({}, DEFAULTS, options);\n\n    // refactoring modifiers' list\n    this._options.modifiers = this._options.modifiers.map(function (modifier) {\n      // remove ignored modifiers\n      if (this._options.modifiersIgnored.indexOf(modifier) !== -1) return;\n\n      // set the x-placement attribute before everything else because it could be used to add margins to the popper\n      // margins needs to be calculated to get the correct popper offsets\n      if (modifier === 'applyStyle') {\n        this._popper.setAttribute('x-placement', this._options.placement);\n      }\n\n      // return predefined modifier identified by string or keep the custom one\n      return this.modifiers[modifier] || modifier;\n    }.bind(this));\n\n    // make sure to apply the popper position before any computation\n    this.state.position = this._getPosition(this._popper, this._reference);\n    setStyle(this._popper, {\n      position: this.state.position,\n      top: 0\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    // setup event listeners, they will take care of update the position in specific situations\n    this._setupEventListeners();\n    return this;\n  }\n\n  //\n  // Methods\n  //\n  /**\n   * Destroy the popper\n   * @method\n   * @memberof Popper\n   */\n  Popper.prototype.destroy = function () {\n    this._popper.removeAttribute('x-placement');\n    this._popper.style.left = '';\n    this._popper.style.position = '';\n    this._popper.style.top = '';\n    this._popper.style[getSupportedPropertyName('transform')] = '';\n    this._removeEventListeners();\n\n    // remove the popper if user explicity asked for the deletion on destroy\n    if (this._options.removeOnDestroy) {\n      this._popper.remove();\n    }\n    return this;\n  };\n\n  /**\n   * Updates the position of the popper, computing the new offsets and applying the new style\n   * @method\n   * @memberof Popper\n   */\n  Popper.prototype.update = function () {\n    var data = {\n      instance: this,\n      styles: {}\n    };\n\n    // store placement inside the data object, modifiers will be able to edit `placement` if needed\n    // and refer to _originalPlacement to know the original value\n    data.placement = this._options.placement;\n    data._originalPlacement = this._options.placement;\n\n    // compute the popper and reference offsets and put them inside data.offsets\n    data.offsets = this._getOffsets(this._popper, this._reference, data.placement);\n\n    // get boundaries\n    data.boundaries = this._getBoundaries(data, this._options.boundariesPadding, this._options.boundariesElement);\n    data = this.runModifiers(data, this._options.modifiers);\n    if (typeof this.state.updateCallback === 'function') {\n      this.state.updateCallback(data);\n    }\n  };\n\n  /**\n   * If a function is passed, it will be executed after the initialization of popper with as first argument the Popper instance.\n   * @method\n   * @memberof Popper\n   * @param {Function} callback\n   */\n  Popper.prototype.onCreate = function (callback) {\n    // the createCallbacks return as first argument the popper instance\n    callback(this);\n    return this;\n  };\n\n  /**\n   * If a function is passed, it will be executed after each update of popper with as first argument the set of coordinates and informations\n   * used to style popper and its arrow.\n   * NOTE: it doesn't get fired on the first call of the `Popper.update()` method inside the `Popper` constructor!\n   * @method\n   * @memberof Popper\n   * @param {Function} callback\n   */\n  Popper.prototype.onUpdate = function (callback) {\n    this.state.updateCallback = callback;\n    return this;\n  };\n\n  /**\n   * Helper used to generate poppers from a configuration file\n   * @method\n   * @memberof Popper\n   * @param config {Object} configuration\n   * @returns {HTMLElement} popper\n   */\n  Popper.prototype.parse = function (config) {\n    var defaultConfig = {\n      tagName: 'div',\n      classNames: ['popper'],\n      attributes: [],\n      parent: root.document.body,\n      content: '',\n      contentType: 'text',\n      arrowTagName: 'div',\n      arrowClassNames: ['popper__arrow'],\n      arrowAttributes: ['x-arrow']\n    };\n    config = Object.assign({}, defaultConfig, config);\n    var d = root.document;\n    var popper = d.createElement(config.tagName);\n    addClassNames(popper, config.classNames);\n    addAttributes(popper, config.attributes);\n    if (config.contentType === 'node') {\n      popper.appendChild(config.content.jquery ? config.content[0] : config.content);\n    } else if (config.contentType === 'html') {\n      popper.innerHTML = config.content;\n    } else {\n      popper.textContent = config.content;\n    }\n    if (config.arrowTagName) {\n      var arrow = d.createElement(config.arrowTagName);\n      addClassNames(arrow, config.arrowClassNames);\n      addAttributes(arrow, config.arrowAttributes);\n      popper.appendChild(arrow);\n    }\n    var parent = config.parent.jquery ? config.parent[0] : config.parent;\n\n    // if the given parent is a string, use it to match an element\n    // if more than one element is matched, the first one will be used as parent\n    // if no elements are matched, the script will throw an error\n    if (typeof parent === 'string') {\n      parent = d.querySelectorAll(config.parent);\n      if (parent.length > 1) {\n        console.warn('WARNING: the given `parent` query(' + config.parent + ') matched more than one element, the first one will be used');\n      }\n      if (parent.length === 0) {\n        throw 'ERROR: the given `parent` doesn\\'t exists!';\n      }\n      parent = parent[0];\n    }\n    // if the given parent is a DOM nodes list or an array of nodes with more than one element,\n    // the first one will be used as parent\n    if (parent.length > 1 && parent instanceof Element === false) {\n      console.warn('WARNING: you have passed as parent a list of elements, the first one will be used');\n      parent = parent[0];\n    }\n\n    // append the generated popper to its parent\n    parent.appendChild(popper);\n    return popper;\n\n    /**\n     * Adds class names to the given element\n     * @function\n     * @ignore\n     * @param {HTMLElement} target\n     * @param {Array} classes\n     */\n    function addClassNames(element, classNames) {\n      classNames.forEach(function (className) {\n        element.classList.add(className);\n      });\n    }\n\n    /**\n     * Adds attributes to the given element\n     * @function\n     * @ignore\n     * @param {HTMLElement} target\n     * @param {Array} attributes\n     * @example\n     * addAttributes(element, [ 'data-info:foobar' ]);\n     */\n    function addAttributes(element, attributes) {\n      attributes.forEach(function (attribute) {\n        element.setAttribute(attribute.split(':')[0], attribute.split(':')[1] || '');\n      });\n    }\n  };\n\n  /**\n   * Helper used to get the position which will be applied to the popper\n   * @method\n   * @memberof Popper\n   * @param config {HTMLElement} popper element\n   * @param reference {HTMLElement} reference element\n   * @returns {String} position\n   */\n  Popper.prototype._getPosition = function (popper, reference) {\n    var container = getOffsetParent(reference);\n    if (this._options.forceAbsolute) {\n      return 'absolute';\n    }\n\n    // Decide if the popper will be fixed\n    // If the reference element is inside a fixed context, the popper will be fixed as well to allow them to scroll together\n    var isParentFixed = isFixed(reference, container);\n    return isParentFixed ? 'fixed' : 'absolute';\n  };\n\n  /**\n   * Get offsets to the popper\n   * @method\n   * @memberof Popper\n   * @access private\n   * @param {Element} popper - the popper element\n   * @param {Element} reference - the reference element (the popper will be relative to this)\n   * @returns {Object} An object containing the offsets which will be applied to the popper\n   */\n  Popper.prototype._getOffsets = function (popper, reference, placement) {\n    placement = placement.split('-')[0];\n    var popperOffsets = {};\n    popperOffsets.position = this.state.position;\n    var isParentFixed = popperOffsets.position === 'fixed';\n\n    //\n    // Get reference element position\n    //\n    var referenceOffsets = getOffsetRectRelativeToCustomParent(reference, getOffsetParent(popper), isParentFixed);\n\n    //\n    // Get popper sizes\n    //\n    var popperRect = getOuterSizes(popper);\n\n    //\n    // Compute offsets of popper\n    //\n\n    // depending by the popper placement we have to compute its offsets slightly differently\n    if (['right', 'left'].indexOf(placement) !== -1) {\n      popperOffsets.top = referenceOffsets.top + referenceOffsets.height / 2 - popperRect.height / 2;\n      if (placement === 'left') {\n        popperOffsets.left = referenceOffsets.left - popperRect.width;\n      } else {\n        popperOffsets.left = referenceOffsets.right;\n      }\n    } else {\n      popperOffsets.left = referenceOffsets.left + referenceOffsets.width / 2 - popperRect.width / 2;\n      if (placement === 'top') {\n        popperOffsets.top = referenceOffsets.top - popperRect.height;\n      } else {\n        popperOffsets.top = referenceOffsets.bottom;\n      }\n    }\n\n    // Add width and height to our offsets object\n    popperOffsets.width = popperRect.width;\n    popperOffsets.height = popperRect.height;\n    return {\n      popper: popperOffsets,\n      reference: referenceOffsets\n    };\n  };\n\n  /**\n   * Setup needed event listeners used to update the popper position\n   * @method\n   * @memberof Popper\n   * @access private\n   */\n  Popper.prototype._setupEventListeners = function () {\n    // NOTE: 1 DOM access here\n    this.state.updateBound = this.update.bind(this);\n    root.addEventListener('resize', this.state.updateBound);\n    // if the boundariesElement is window we don't need to listen for the scroll event\n    if (this._options.boundariesElement !== 'window') {\n      var target = getScrollParent(this._reference);\n      // here it could be both `body` or `documentElement` thanks to Firefox, we then check both\n      if (target === root.document.body || target === root.document.documentElement) {\n        target = root;\n      }\n      target.addEventListener('scroll', this.state.updateBound);\n      this.state.scrollTarget = target;\n    }\n  };\n\n  /**\n   * Remove event listeners used to update the popper position\n   * @method\n   * @memberof Popper\n   * @access private\n   */\n  Popper.prototype._removeEventListeners = function () {\n    // NOTE: 1 DOM access here\n    root.removeEventListener('resize', this.state.updateBound);\n    if (this._options.boundariesElement !== 'window' && this.state.scrollTarget) {\n      this.state.scrollTarget.removeEventListener('scroll', this.state.updateBound);\n      this.state.scrollTarget = null;\n    }\n    this.state.updateBound = null;\n  };\n\n  /**\n   * Computed the boundaries limits and return them\n   * @method\n   * @memberof Popper\n   * @access private\n   * @param {Object} data - Object containing the property \"offsets\" generated by `_getOffsets`\n   * @param {Number} padding - Boundaries padding\n   * @param {Element} boundariesElement - Element used to define the boundaries\n   * @returns {Object} Coordinates of the boundaries\n   */\n  Popper.prototype._getBoundaries = function (data, padding, boundariesElement) {\n    // NOTE: 1 DOM access here\n    var boundaries = {};\n    var width, height;\n    if (boundariesElement === 'window') {\n      var body = root.document.body,\n        html = root.document.documentElement;\n      height = Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);\n      width = Math.max(body.scrollWidth, body.offsetWidth, html.clientWidth, html.scrollWidth, html.offsetWidth);\n      boundaries = {\n        top: 0,\n        right: width,\n        bottom: height,\n        left: 0\n      };\n    } else if (boundariesElement === 'viewport') {\n      var offsetParent = getOffsetParent(this._popper);\n      var scrollParent = getScrollParent(this._popper);\n      var offsetParentRect = getOffsetRect(offsetParent);\n\n      // Thanks the fucking native API, `document.body.scrollTop` & `document.documentElement.scrollTop`\n      var getScrollTopValue = function getScrollTopValue(element) {\n        return element == document.body ? Math.max(document.documentElement.scrollTop, document.body.scrollTop) : element.scrollTop;\n      };\n      var getScrollLeftValue = function getScrollLeftValue(element) {\n        return element == document.body ? Math.max(document.documentElement.scrollLeft, document.body.scrollLeft) : element.scrollLeft;\n      };\n\n      // if the popper is fixed we don't have to substract scrolling from the boundaries\n      var scrollTop = data.offsets.popper.position === 'fixed' ? 0 : getScrollTopValue(scrollParent);\n      var scrollLeft = data.offsets.popper.position === 'fixed' ? 0 : getScrollLeftValue(scrollParent);\n      boundaries = {\n        top: 0 - (offsetParentRect.top - scrollTop),\n        right: root.document.documentElement.clientWidth - (offsetParentRect.left - scrollLeft),\n        bottom: root.document.documentElement.clientHeight - (offsetParentRect.top - scrollTop),\n        left: 0 - (offsetParentRect.left - scrollLeft)\n      };\n    } else {\n      if (getOffsetParent(this._popper) === boundariesElement) {\n        boundaries = {\n          top: 0,\n          left: 0,\n          right: boundariesElement.clientWidth,\n          bottom: boundariesElement.clientHeight\n        };\n      } else {\n        boundaries = getOffsetRect(boundariesElement);\n      }\n    }\n    boundaries.left += padding;\n    boundaries.right -= padding;\n    boundaries.top = boundaries.top + padding;\n    boundaries.bottom = boundaries.bottom - padding;\n    return boundaries;\n  };\n\n  /**\n   * Loop trough the list of modifiers and run them in order, each of them will then edit the data object\n   * @method\n   * @memberof Popper\n   * @access public\n   * @param {Object} data\n   * @param {Array} modifiers\n   * @param {Function} ends\n   */\n  Popper.prototype.runModifiers = function (data, modifiers, ends) {\n    var modifiersToRun = modifiers.slice();\n    if (ends !== undefined) {\n      modifiersToRun = this._options.modifiers.slice(0, getArrayKeyIndex(this._options.modifiers, ends));\n    }\n    modifiersToRun.forEach(function (modifier) {\n      if (isFunction(modifier)) {\n        data = modifier.call(this, data);\n      }\n    }.bind(this));\n    return data;\n  };\n\n  /**\n   * Helper used to know if the given modifier depends from another one.\n   * @method\n   * @memberof Popper\n   * @param {String} requesting - name of requesting modifier\n   * @param {String} requested - name of requested modifier\n   * @returns {Boolean}\n   */\n  Popper.prototype.isModifierRequired = function (requesting, requested) {\n    var index = getArrayKeyIndex(this._options.modifiers, requesting);\n    return !!this._options.modifiers.slice(0, index).filter(function (modifier) {\n      return modifier === requested;\n    }).length;\n  };\n\n  //\n  // Modifiers\n  //\n\n  /**\n   * Modifiers list\n   * @namespace Popper.modifiers\n   * @memberof Popper\n   * @type {Object}\n   */\n  Popper.prototype.modifiers = {};\n\n  /**\n   * Apply the computed styles to the popper element\n   * @method\n   * @memberof Popper.modifiers\n   * @argument {Object} data - The data object generated by `update` method\n   * @returns {Object} The same data object\n   */\n  Popper.prototype.modifiers.applyStyle = function (data) {\n    // apply the final offsets to the popper\n    // NOTE: 1 DOM access here\n    var styles = {\n      position: data.offsets.popper.position\n    };\n\n    // round top and left to avoid blurry text\n    var left = Math.round(data.offsets.popper.left);\n    var top = Math.round(data.offsets.popper.top);\n\n    // if gpuAcceleration is set to true and transform is supported, we use `translate3d` to apply the position to the popper\n    // we automatically use the supported prefixed version if needed\n    var prefixedProperty;\n    if (this._options.gpuAcceleration && (prefixedProperty = getSupportedPropertyName('transform'))) {\n      styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n      styles.top = 0;\n      styles.left = 0;\n    }\n    // othwerise, we use the standard `left` and `top` properties\n    else {\n      styles.left = left;\n      styles.top = top;\n    }\n\n    // any property present in `data.styles` will be applied to the popper,\n    // in this way we can make the 3rd party modifiers add custom styles to it\n    // Be aware, modifiers could override the properties defined in the previous\n    // lines of this modifier!\n    Object.assign(styles, data.styles);\n    setStyle(this._popper, styles);\n\n    // set an attribute which will be useful to style the tooltip (use it to properly position its arrow)\n    // NOTE: 1 DOM access here\n    this._popper.setAttribute('x-placement', data.placement);\n\n    // if the arrow modifier is required and the arrow style has been computed, apply the arrow style\n    if (this.isModifierRequired(this.modifiers.applyStyle, this.modifiers.arrow) && data.offsets.arrow) {\n      setStyle(data.arrowElement, data.offsets.arrow);\n    }\n    return data;\n  };\n\n  /**\n   * Modifier used to shift the popper on the start or end of its reference element side\n   * @method\n   * @memberof Popper.modifiers\n   * @argument {Object} data - The data object generated by `update` method\n   * @returns {Object} The data object, properly modified\n   */\n  Popper.prototype.modifiers.shift = function (data) {\n    var placement = data.placement;\n    var basePlacement = placement.split('-')[0];\n    var shiftVariation = placement.split('-')[1];\n\n    // if shift shiftVariation is specified, run the modifier\n    if (shiftVariation) {\n      var reference = data.offsets.reference;\n      var popper = getPopperClientRect(data.offsets.popper);\n      var shiftOffsets = {\n        y: {\n          start: {\n            top: reference.top\n          },\n          end: {\n            top: reference.top + reference.height - popper.height\n          }\n        },\n        x: {\n          start: {\n            left: reference.left\n          },\n          end: {\n            left: reference.left + reference.width - popper.width\n          }\n        }\n      };\n      var axis = ['bottom', 'top'].indexOf(basePlacement) !== -1 ? 'x' : 'y';\n      data.offsets.popper = Object.assign(popper, shiftOffsets[axis][shiftVariation]);\n    }\n    return data;\n  };\n\n  /**\n   * Modifier used to make sure the popper does not overflows from it's boundaries\n   * @method\n   * @memberof Popper.modifiers\n   * @argument {Object} data - The data object generated by `update` method\n   * @returns {Object} The data object, properly modified\n   */\n  Popper.prototype.modifiers.preventOverflow = function (data) {\n    var order = this._options.preventOverflowOrder;\n    var popper = getPopperClientRect(data.offsets.popper);\n    var check = {\n      left: function left() {\n        var left = popper.left;\n        if (popper.left < data.boundaries.left) {\n          left = Math.max(popper.left, data.boundaries.left);\n        }\n        return {\n          left: left\n        };\n      },\n      right: function right() {\n        var left = popper.left;\n        if (popper.right > data.boundaries.right) {\n          left = Math.min(popper.left, data.boundaries.right - popper.width);\n        }\n        return {\n          left: left\n        };\n      },\n      top: function top() {\n        var top = popper.top;\n        if (popper.top < data.boundaries.top) {\n          top = Math.max(popper.top, data.boundaries.top);\n        }\n        return {\n          top: top\n        };\n      },\n      bottom: function bottom() {\n        var top = popper.top;\n        if (popper.bottom > data.boundaries.bottom) {\n          top = Math.min(popper.top, data.boundaries.bottom - popper.height);\n        }\n        return {\n          top: top\n        };\n      }\n    };\n    order.forEach(function (direction) {\n      data.offsets.popper = Object.assign(popper, check[direction]());\n    });\n    return data;\n  };\n\n  /**\n   * Modifier used to make sure the popper is always near its reference\n   * @method\n   * @memberof Popper.modifiers\n   * @argument {Object} data - The data object generated by _update method\n   * @returns {Object} The data object, properly modified\n   */\n  Popper.prototype.modifiers.keepTogether = function (data) {\n    var popper = getPopperClientRect(data.offsets.popper);\n    var reference = data.offsets.reference;\n    var f = Math.floor;\n    if (popper.right < f(reference.left)) {\n      data.offsets.popper.left = f(reference.left) - popper.width;\n    }\n    if (popper.left > f(reference.right)) {\n      data.offsets.popper.left = f(reference.right);\n    }\n    if (popper.bottom < f(reference.top)) {\n      data.offsets.popper.top = f(reference.top) - popper.height;\n    }\n    if (popper.top > f(reference.bottom)) {\n      data.offsets.popper.top = f(reference.bottom);\n    }\n    return data;\n  };\n\n  /**\n   * Modifier used to flip the placement of the popper when the latter is starting overlapping its reference element.\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * **NOTE:** This modifier will run all its previous modifiers everytime it tries to flip the popper!\n   * @method\n   * @memberof Popper.modifiers\n   * @argument {Object} data - The data object generated by _update method\n   * @returns {Object} The data object, properly modified\n   */\n  Popper.prototype.modifiers.flip = function (data) {\n    // check if preventOverflow is in the list of modifiers before the flip modifier.\n    // otherwise flip would not work as expected.\n    if (!this.isModifierRequired(this.modifiers.flip, this.modifiers.preventOverflow)) {\n      console.warn('WARNING: preventOverflow modifier is required by flip modifier in order to work, be sure to include it before flip!');\n      return data;\n    }\n    if (data.flipped && data.placement === data._originalPlacement) {\n      // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n      return data;\n    }\n    var placement = data.placement.split('-')[0];\n    var placementOpposite = getOppositePlacement(placement);\n    var variation = data.placement.split('-')[1] || '';\n    var flipOrder = [];\n    if (this._options.flipBehavior === 'flip') {\n      flipOrder = [placement, placementOpposite];\n    } else {\n      flipOrder = this._options.flipBehavior;\n    }\n    flipOrder.forEach(function (step, index) {\n      if (placement !== step || flipOrder.length === index + 1) {\n        return;\n      }\n      placement = data.placement.split('-')[0];\n      placementOpposite = getOppositePlacement(placement);\n      var popperOffsets = getPopperClientRect(data.offsets.popper);\n\n      // this boolean is used to distinguish right and bottom from top and left\n      // they need different computations to get flipped\n      var a = ['right', 'bottom'].indexOf(placement) !== -1;\n\n      // using Math.floor because the reference offsets may contain decimals we are not going to consider here\n      if (a && Math.floor(data.offsets.reference[placement]) > Math.floor(popperOffsets[placementOpposite]) || !a && Math.floor(data.offsets.reference[placement]) < Math.floor(popperOffsets[placementOpposite])) {\n        // we'll use this boolean to detect any flip loop\n        data.flipped = true;\n        data.placement = flipOrder[index + 1];\n        if (variation) {\n          data.placement += '-' + variation;\n        }\n        data.offsets.popper = this._getOffsets(this._popper, this._reference, data.placement).popper;\n        data = this.runModifiers(data, this._options.modifiers, this._flip);\n      }\n    }.bind(this));\n    return data;\n  };\n\n  /**\n   * Modifier used to add an offset to the popper, useful if you more granularity positioning your popper.\n   * The offsets will shift the popper on the side of its reference element.\n   * @method\n   * @memberof Popper.modifiers\n   * @argument {Object} data - The data object generated by _update method\n   * @returns {Object} The data object, properly modified\n   */\n  Popper.prototype.modifiers.offset = function (data) {\n    var offset = this._options.offset;\n    var popper = data.offsets.popper;\n    if (data.placement.indexOf('left') !== -1) {\n      popper.top -= offset;\n    } else if (data.placement.indexOf('right') !== -1) {\n      popper.top += offset;\n    } else if (data.placement.indexOf('top') !== -1) {\n      popper.left -= offset;\n    } else if (data.placement.indexOf('bottom') !== -1) {\n      popper.left += offset;\n    }\n    return data;\n  };\n\n  /**\n   * Modifier used to move the arrows on the edge of the popper to make sure them are always between the popper and the reference element\n   * It will use the CSS outer size of the arrow element to know how many pixels of conjuction are needed\n   * @method\n   * @memberof Popper.modifiers\n   * @argument {Object} data - The data object generated by _update method\n   * @returns {Object} The data object, properly modified\n   */\n  Popper.prototype.modifiers.arrow = function (data) {\n    var arrow = this._options.arrowElement;\n    var arrowOffset = this._options.arrowOffset;\n\n    // if the arrowElement is a string, suppose it's a CSS selector\n    if (typeof arrow === 'string') {\n      arrow = this._popper.querySelector(arrow);\n    }\n\n    // if arrow element is not found, don't run the modifier\n    if (!arrow) {\n      return data;\n    }\n\n    // the arrow element must be child of its popper\n    if (!this._popper.contains(arrow)) {\n      console.warn('WARNING: `arrowElement` must be child of its popper element!');\n      return data;\n    }\n\n    // arrow depends on keepTogether in order to work\n    if (!this.isModifierRequired(this.modifiers.arrow, this.modifiers.keepTogether)) {\n      console.warn('WARNING: keepTogether modifier is required by arrow modifier in order to work, be sure to include it before arrow!');\n      return data;\n    }\n    var arrowStyle = {};\n    var placement = data.placement.split('-')[0];\n    var popper = getPopperClientRect(data.offsets.popper);\n    var reference = data.offsets.reference;\n    var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n    var len = isVertical ? 'height' : 'width';\n    var side = isVertical ? 'top' : 'left';\n    var translate = isVertical ? 'translateY' : 'translateX';\n    var altSide = isVertical ? 'left' : 'top';\n    var opSide = isVertical ? 'bottom' : 'right';\n    var arrowSize = getOuterSizes(arrow)[len];\n\n    //\n    // extends keepTogether behavior making sure the popper and its reference have enough pixels in conjuction\n    //\n\n    // top/left side\n    if (reference[opSide] - arrowSize < popper[side]) {\n      data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowSize);\n    }\n    // bottom/right side\n    if (reference[side] + arrowSize > popper[opSide]) {\n      data.offsets.popper[side] += reference[side] + arrowSize - popper[opSide];\n    }\n\n    // compute center of the popper\n    var center = reference[side] + (arrowOffset || reference[len] / 2 - arrowSize / 2);\n    var sideValue = center - popper[side];\n\n    // prevent arrow from being placed not contiguously to its popper\n    sideValue = Math.max(Math.min(popper[len] - arrowSize - 8, sideValue), 8);\n    arrowStyle[side] = sideValue;\n    arrowStyle[altSide] = ''; // make sure to remove any old style from the arrow\n\n    data.offsets.arrow = arrowStyle;\n    data.arrowElement = arrow;\n    return data;\n  };\n\n  //\n  // Helpers\n  //\n\n  /**\n   * Get the outer sizes of the given element (offset size + margins)\n   * @function\n   * @ignore\n   * @argument {Element} element\n   * @returns {Object} object containing width and height properties\n   */\n  function getOuterSizes(element) {\n    // NOTE: 1 DOM access here\n    var _display = element.style.display,\n      _visibility = element.style.visibility;\n    element.style.display = 'block';\n    element.style.visibility = 'hidden';\n    var calcWidthToForceRepaint = element.offsetWidth;\n\n    // original method\n    var styles = root.getComputedStyle(element);\n    var x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n    var y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n    var result = {\n      width: element.offsetWidth + y,\n      height: element.offsetHeight + x\n    };\n\n    // reset element styles\n    element.style.display = _display;\n    element.style.visibility = _visibility;\n    return result;\n  }\n\n  /**\n   * Get the opposite placement of the given one/\n   * @function\n   * @ignore\n   * @argument {String} placement\n   * @returns {String} flipped placement\n   */\n  function getOppositePlacement(placement) {\n    var hash = {\n      left: 'right',\n      right: 'left',\n      bottom: 'top',\n      top: 'bottom'\n    };\n    return placement.replace(/left|right|bottom|top/g, function (matched) {\n      return hash[matched];\n    });\n  }\n\n  /**\n   * Given the popper offsets, generate an output similar to getBoundingClientRect\n   * @function\n   * @ignore\n   * @argument {Object} popperOffsets\n   * @returns {Object} ClientRect like output\n   */\n  function getPopperClientRect(popperOffsets) {\n    var offsets = Object.assign({}, popperOffsets);\n    offsets.right = offsets.left + offsets.width;\n    offsets.bottom = offsets.top + offsets.height;\n    return offsets;\n  }\n\n  /**\n   * Given an array and the key to find, returns its index\n   * @function\n   * @ignore\n   * @argument {Array} arr\n   * @argument keyToFind\n   * @returns index or null\n   */\n  function getArrayKeyIndex(arr, keyToFind) {\n    var i = 0,\n      key;\n    for (key in arr) {\n      if (arr[key] === keyToFind) {\n        return i;\n      }\n      i++;\n    }\n    return null;\n  }\n\n  /**\n   * Get CSS computed property of the given element\n   * @function\n   * @ignore\n   * @argument {Eement} element\n   * @argument {String} property\n   */\n  function getStyleComputedProperty(element, property) {\n    // NOTE: 1 DOM access here\n    var css = root.getComputedStyle(element, null);\n    return css[property];\n  }\n\n  /**\n   * Returns the offset parent of the given element\n   * @function\n   * @ignore\n   * @argument {Element} element\n   * @returns {Element} offset parent\n   */\n  function getOffsetParent(element) {\n    // NOTE: 1 DOM access here\n    var offsetParent = element.offsetParent;\n    return offsetParent === root.document.body || !offsetParent ? root.document.documentElement : offsetParent;\n  }\n\n  /**\n   * Returns the scrolling parent of the given element\n   * @function\n   * @ignore\n   * @argument {Element} element\n   * @returns {Element} offset parent\n   */\n  function getScrollParent(element) {\n    var parent = element.parentNode;\n    if (!parent) {\n      return element;\n    }\n    if (parent === root.document) {\n      // Firefox puts the scrollTOp value on `documentElement` instead of `body`, we then check which of them is\n      // greater than 0 and return the proper element\n      if (root.document.body.scrollTop || root.document.body.scrollLeft) {\n        return root.document.body;\n      } else {\n        return root.document.documentElement;\n      }\n    }\n\n    // Firefox want us to check `-x` and `-y` variations as well\n    if (['scroll', 'auto'].indexOf(getStyleComputedProperty(parent, 'overflow')) !== -1 || ['scroll', 'auto'].indexOf(getStyleComputedProperty(parent, 'overflow-x')) !== -1 || ['scroll', 'auto'].indexOf(getStyleComputedProperty(parent, 'overflow-y')) !== -1) {\n      // If the detected scrollParent is body, we perform an additional check on its parentNode\n      // in this way we'll get body if the browser is Chrome-ish, or documentElement otherwise\n      // fixes issue #65\n      return parent;\n    }\n    return getScrollParent(element.parentNode);\n  }\n\n  /**\n   * Check if the given element is fixed or is inside a fixed parent\n   * @function\n   * @ignore\n   * @argument {Element} element\n   * @argument {Element} customContainer\n   * @returns {Boolean} answer to \"isFixed?\"\n   */\n  function isFixed(element) {\n    if (element === root.document.body) {\n      return false;\n    }\n    if (getStyleComputedProperty(element, 'position') === 'fixed') {\n      return true;\n    }\n    return element.parentNode ? isFixed(element.parentNode) : element;\n  }\n\n  /**\n   * Set the style to the given popper\n   * @function\n   * @ignore\n   * @argument {Element} element - Element to apply the style to\n   * @argument {Object} styles - Object with a list of properties and values which will be applied to the element\n   */\n  function setStyle(element, styles) {\n    function is_numeric(n) {\n      return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n    }\n    Object.keys(styles).forEach(function (prop) {\n      var unit = '';\n      // add unit if the value is numeric and is one of the following\n      if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && is_numeric(styles[prop])) {\n        unit = 'px';\n      }\n      element.style[prop] = styles[prop] + unit;\n    });\n  }\n\n  /**\n   * Check if the given variable is a function\n   * @function\n   * @ignore\n   * @argument {*} functionToCheck - variable to check\n   * @returns {Boolean} answer to: is a function?\n   */\n  function isFunction(functionToCheck) {\n    var getType = {};\n    return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n  }\n\n  /**\n   * Get the position of the given element, relative to its offset parent\n   * @function\n   * @ignore\n   * @param {Element} element\n   * @return {Object} position - Coordinates of the element and its `scrollTop`\n   */\n  function getOffsetRect(element) {\n    var elementRect = {\n      width: element.offsetWidth,\n      height: element.offsetHeight,\n      left: element.offsetLeft,\n      top: element.offsetTop\n    };\n    elementRect.right = elementRect.left + elementRect.width;\n    elementRect.bottom = elementRect.top + elementRect.height;\n\n    // position\n    return elementRect;\n  }\n\n  /**\n   * Get bounding client rect of given element\n   * @function\n   * @ignore\n   * @param {HTMLElement} element\n   * @return {Object} client rect\n   */\n  function getBoundingClientRect(element) {\n    var rect = element.getBoundingClientRect();\n\n    // whether the IE version is lower than 11\n    var isIE = navigator.userAgent.indexOf(\"MSIE\") != -1;\n\n    // fix ie document bounding top always 0 bug\n    var rectTop = isIE && element.tagName === 'HTML' ? -element.scrollTop : rect.top;\n    return {\n      left: rect.left,\n      top: rectTop,\n      right: rect.right,\n      bottom: rect.bottom,\n      width: rect.right - rect.left,\n      height: rect.bottom - rectTop\n    };\n  }\n\n  /**\n   * Given an element and one of its parents, return the offset\n   * @function\n   * @ignore\n   * @param {HTMLElement} element\n   * @param {HTMLElement} parent\n   * @return {Object} rect\n   */\n  function getOffsetRectRelativeToCustomParent(element, parent, fixed) {\n    var elementRect = getBoundingClientRect(element);\n    var parentRect = getBoundingClientRect(parent);\n    if (fixed) {\n      var scrollParent = getScrollParent(parent);\n      parentRect.top += scrollParent.scrollTop;\n      parentRect.bottom += scrollParent.scrollTop;\n      parentRect.left += scrollParent.scrollLeft;\n      parentRect.right += scrollParent.scrollLeft;\n    }\n    var rect = {\n      top: elementRect.top - parentRect.top,\n      left: elementRect.left - parentRect.left,\n      bottom: elementRect.top - parentRect.top + elementRect.height,\n      right: elementRect.left - parentRect.left + elementRect.width,\n      width: elementRect.width,\n      height: elementRect.height\n    };\n    return rect;\n  }\n\n  /**\n   * Get the prefixed supported property name\n   * @function\n   * @ignore\n   * @argument {String} property (camelCase)\n   * @returns {String} prefixed property (camelCase)\n   */\n  function getSupportedPropertyName(property) {\n    var prefixes = ['', 'ms', 'webkit', 'moz', 'o'];\n    for (var i = 0; i < prefixes.length; i++) {\n      var toCheck = prefixes[i] ? prefixes[i] + property.charAt(0).toUpperCase() + property.slice(1) : property;\n      if (typeof root.document.body.style[toCheck] !== 'undefined') {\n        return toCheck;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * The Object.assign() method is used to copy the values of all enumerable own properties from one or more source\n   * objects to a target object. It will return the target object.\n   * This polyfill doesn't support symbol properties, since ES5 doesn't have symbols anyway\n   * Source: https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n   * @function\n   * @ignore\n   */\n  if (!Object.assign) {\n    Object.defineProperty(Object, 'assign', {\n      enumerable: false,\n      configurable: true,\n      writable: true,\n      value: function value(target) {\n        if (target === undefined || target === null) {\n          throw new TypeError('Cannot convert first argument to object');\n        }\n        var to = Object(target);\n        for (var i = 1; i < arguments.length; i++) {\n          var nextSource = arguments[i];\n          if (nextSource === undefined || nextSource === null) {\n            continue;\n          }\n          nextSource = Object(nextSource);\n          var keysArray = Object.keys(nextSource);\n          for (var nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex++) {\n            var nextKey = keysArray[nextIndex];\n            var desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n            if (desc !== undefined && desc.enumerable) {\n              to[nextKey] = nextSource[nextKey];\n            }\n          }\n        }\n        return to;\n      }\n    });\n  }\n  return Popper;\n});", "map": {"version": 3, "names": ["require", "_typeof", "Symbol", "iterator", "obj", "constructor", "prototype", "root", "factory", "define", "amd", "module", "exports", "<PERSON><PERSON>", "undefined", "window", "DEFAULTS", "placement", "gpuAcceleration", "offset", "boundariesElement", "boundariesPadding", "preventOverflowOrder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "arrowElement", "arrowOffset", "modifiers", "modifiersIgnored", "forceAbsolute", "reference", "popper", "options", "_reference", "j<PERSON>y", "state", "isNotDefined", "isConfig", "Object", "toString", "call", "_popper", "parse", "_options", "assign", "map", "modifier", "indexOf", "setAttribute", "bind", "position", "_getPosition", "setStyle", "top", "update", "_setupEventListeners", "destroy", "removeAttribute", "style", "left", "getSupportedPropertyName", "_removeEventListeners", "removeOnDestroy", "remove", "data", "instance", "styles", "_originalPlacement", "offsets", "_getOffsets", "boundaries", "_getBoundaries", "runModifiers", "updateCallback", "onCreate", "callback", "onUpdate", "config", "defaultConfig", "tagName", "classNames", "attributes", "parent", "document", "body", "content", "contentType", "arrowTagName", "arrowClassNames", "arrowAttributes", "d", "createElement", "addClassNames", "addAttributes", "append<PERSON><PERSON><PERSON>", "innerHTML", "textContent", "arrow", "querySelectorAll", "length", "console", "warn", "Element", "element", "for<PERSON>ach", "className", "classList", "add", "attribute", "split", "container", "getOffsetParent", "isParentFixed", "isFixed", "popperOffsets", "referenceOffsets", "getOffsetRectRelativeToCustomParent", "popperRect", "getOuterSizes", "height", "width", "right", "bottom", "updateBound", "addEventListener", "target", "getScrollParent", "documentElement", "scrollTarget", "removeEventListener", "padding", "html", "Math", "max", "scrollHeight", "offsetHeight", "clientHeight", "scrollWidth", "offsetWidth", "clientWidth", "offsetParent", "scrollParent", "offsetParentRect", "getOffsetRect", "getScrollTopValue", "scrollTop", "getScrollLeftValue", "scrollLeft", "ends", "modifiersToRun", "slice", "getArrayKeyIndex", "isFunction", "isModifierRequired", "requesting", "requested", "index", "filter", "applyStyle", "round", "prefixedProperty", "shift", "basePlacement", "shiftVariation", "getPopperClientRect", "shiftOffsets", "y", "start", "end", "x", "axis", "preventOverflow", "order", "check", "min", "direction", "keepTogether", "f", "floor", "flip", "flipped", "placementOpposite", "getOppositePlacement", "variation", "flipOrder", "step", "a", "_flip", "querySelector", "contains", "arrowStyle", "isVertical", "len", "side", "translate", "altSide", "opSide", "arrowSize", "center", "sideValue", "_display", "display", "_visibility", "visibility", "calcWidthToForceRepaint", "getComputedStyle", "parseFloat", "marginTop", "marginBottom", "marginLeft", "marginRight", "result", "hash", "replace", "matched", "arr", "keyToFind", "i", "key", "getStyleComputedProperty", "property", "css", "parentNode", "is_numeric", "n", "isNaN", "isFinite", "keys", "prop", "unit", "functionToCheck", "getType", "elementRect", "offsetLeft", "offsetTop", "getBoundingClientRect", "rect", "isIE", "navigator", "userAgent", "rectTop", "fixed", "parentRect", "prefixes", "to<PERSON><PERSON><PERSON>", "char<PERSON>t", "toUpperCase", "defineProperty", "enumerable", "configurable", "writable", "value", "TypeError", "to", "arguments", "nextSource", "keysArray", "nextIndex", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor"], "sources": ["E:/frontCodeCode/datafront/node_modules/element-ui/lib/utils/popper.js"], "sourcesContent": ["'use strict';\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\n/**\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version {{version}}\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n//\n// Cross module loader\n// Supported: Node, AMD, Browser globals\n//\n;(function (root, factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define(factory);\n    } else if ((typeof module === 'undefined' ? 'undefined' : _typeof(module)) === 'object' && module.exports) {\n        // Node. Does not work with strict CommonJS, but\n        // only CommonJS-like environments that support module.exports,\n        // like Node.\n        module.exports = factory();\n    } else {\n        // Browser globals (root is window)\n        root.Popper = factory();\n    }\n})(undefined, function () {\n\n    'use strict';\n\n    var root = window;\n\n    // default options\n    var DEFAULTS = {\n        // placement of the popper\n        placement: 'bottom',\n\n        gpuAcceleration: true,\n\n        // shift popper from its origin by the given amount of pixels (can be negative)\n        offset: 0,\n\n        // the element which will act as boundary of the popper\n        boundariesElement: 'viewport',\n\n        // amount of pixel used to define a minimum distance between the boundaries and the popper\n        boundariesPadding: 5,\n\n        // popper will try to prevent overflow following this order,\n        // by default, then, it could overflow on the left and on top of the boundariesElement\n        preventOverflowOrder: ['left', 'right', 'top', 'bottom'],\n\n        // the behavior used by flip to change the placement of the popper\n        flipBehavior: 'flip',\n\n        arrowElement: '[x-arrow]',\n\n        arrowOffset: 0,\n\n        // list of functions used to modify the offsets before they are applied to the popper\n        modifiers: ['shift', 'offset', 'preventOverflow', 'keepTogether', 'arrow', 'flip', 'applyStyle'],\n\n        modifiersIgnored: [],\n\n        forceAbsolute: false\n    };\n\n    /**\n     * Create a new Popper.js instance\n     * @constructor Popper\n     * @param {HTMLElement} reference - The reference element used to position the popper\n     * @param {HTMLElement|Object} popper\n     *      The HTML element used as popper, or a configuration used to generate the popper.\n     * @param {String} [popper.tagName='div'] The tag name of the generated popper.\n     * @param {Array} [popper.classNames=['popper']] Array of classes to apply to the generated popper.\n     * @param {Array} [popper.attributes] Array of attributes to apply, specify `attr:value` to assign a value to it.\n     * @param {HTMLElement|String} [popper.parent=window.document.body] The parent element, given as HTMLElement or as query string.\n     * @param {String} [popper.content=''] The content of the popper, it can be text, html, or node; if it is not text, set `contentType` to `html` or `node`.\n     * @param {String} [popper.contentType='text'] If `html`, the `content` will be parsed as HTML. If `node`, it will be appended as-is.\n     * @param {String} [popper.arrowTagName='div'] Same as `popper.tagName` but for the arrow element.\n     * @param {Array} [popper.arrowClassNames='popper__arrow'] Same as `popper.classNames` but for the arrow element.\n     * @param {String} [popper.arrowAttributes=['x-arrow']] Same as `popper.attributes` but for the arrow element.\n     * @param {Object} options\n     * @param {String} [options.placement=bottom]\n     *      Placement of the popper accepted values: `top(-start, -end), right(-start, -end), bottom(-start, -right),\n     *      left(-start, -end)`\n     *\n     * @param {HTMLElement|String} [options.arrowElement='[x-arrow]']\n     *      The DOM Node used as arrow for the popper, or a CSS selector used to get the DOM node. It must be child of\n     *      its parent Popper. Popper.js will apply to the given element the style required to align the arrow with its\n     *      reference element.\n     *      By default, it will look for a child node of the popper with the `x-arrow` attribute.\n     *\n     * @param {Boolean} [options.gpuAcceleration=true]\n     *      When this property is set to true, the popper position will be applied using CSS3 translate3d, allowing the\n     *      browser to use the GPU to accelerate the rendering.\n     *      If set to false, the popper will be placed using `top` and `left` properties, not using the GPU.\n     *\n     * @param {Number} [options.offset=0]\n     *      Amount of pixels the popper will be shifted (can be negative).\n     *\n     * @param {String|Element} [options.boundariesElement='viewport']\n     *      The element which will define the boundaries of the popper position, the popper will never be placed outside\n     *      of the defined boundaries (except if `keepTogether` is enabled)\n     *\n     * @param {Number} [options.boundariesPadding=5]\n     *      Additional padding for the boundaries\n     *\n     * @param {Array} [options.preventOverflowOrder=['left', 'right', 'top', 'bottom']]\n     *      Order used when Popper.js tries to avoid overflows from the boundaries, they will be checked in order,\n     *      this means that the last ones will never overflow\n     *\n     * @param {String|Array} [options.flipBehavior='flip']\n     *      The behavior used by the `flip` modifier to change the placement of the popper when the latter is trying to\n     *      overlap its reference element. Defining `flip` as value, the placement will be flipped on\n     *      its axis (`right - left`, `top - bottom`).\n     *      You can even pass an array of placements (eg: `['right', 'left', 'top']` ) to manually specify\n     *      how alter the placement when a flip is needed. (eg. in the above example, it would first flip from right to left,\n     *      then, if even in its new placement, the popper is overlapping its reference element, it will be moved to top)\n     *\n     * @param {Array} [options.modifiers=[ 'shift', 'offset', 'preventOverflow', 'keepTogether', 'arrow', 'flip', 'applyStyle']]\n     *      List of functions used to modify the data before they are applied to the popper, add your custom functions\n     *      to this array to edit the offsets and placement.\n     *      The function should reflect the @params and @returns of preventOverflow\n     *\n     * @param {Array} [options.modifiersIgnored=[]]\n     *      Put here any built-in modifier name you want to exclude from the modifiers list\n     *      The function should reflect the @params and @returns of preventOverflow\n     *\n     * @param {Boolean} [options.removeOnDestroy=false]\n     *      Set to true if you want to automatically remove the popper when you call the `destroy` method.\n     */\n    function Popper(reference, popper, options) {\n        this._reference = reference.jquery ? reference[0] : reference;\n        this.state = {};\n\n        // if the popper variable is a configuration object, parse it to generate an HTMLElement\n        // generate a default popper if is not defined\n        var isNotDefined = typeof popper === 'undefined' || popper === null;\n        var isConfig = popper && Object.prototype.toString.call(popper) === '[object Object]';\n        if (isNotDefined || isConfig) {\n            this._popper = this.parse(isConfig ? popper : {});\n        }\n        // otherwise, use the given HTMLElement as popper\n        else {\n                this._popper = popper.jquery ? popper[0] : popper;\n            }\n\n        // with {} we create a new object with the options inside it\n        this._options = Object.assign({}, DEFAULTS, options);\n\n        // refactoring modifiers' list\n        this._options.modifiers = this._options.modifiers.map(function (modifier) {\n            // remove ignored modifiers\n            if (this._options.modifiersIgnored.indexOf(modifier) !== -1) return;\n\n            // set the x-placement attribute before everything else because it could be used to add margins to the popper\n            // margins needs to be calculated to get the correct popper offsets\n            if (modifier === 'applyStyle') {\n                this._popper.setAttribute('x-placement', this._options.placement);\n            }\n\n            // return predefined modifier identified by string or keep the custom one\n            return this.modifiers[modifier] || modifier;\n        }.bind(this));\n\n        // make sure to apply the popper position before any computation\n        this.state.position = this._getPosition(this._popper, this._reference);\n        setStyle(this._popper, { position: this.state.position, top: 0 });\n\n        // fire the first update to position the popper in the right place\n        this.update();\n\n        // setup event listeners, they will take care of update the position in specific situations\n        this._setupEventListeners();\n        return this;\n    }\n\n    //\n    // Methods\n    //\n    /**\n     * Destroy the popper\n     * @method\n     * @memberof Popper\n     */\n    Popper.prototype.destroy = function () {\n        this._popper.removeAttribute('x-placement');\n        this._popper.style.left = '';\n        this._popper.style.position = '';\n        this._popper.style.top = '';\n        this._popper.style[getSupportedPropertyName('transform')] = '';\n        this._removeEventListeners();\n\n        // remove the popper if user explicity asked for the deletion on destroy\n        if (this._options.removeOnDestroy) {\n            this._popper.remove();\n        }\n        return this;\n    };\n\n    /**\n     * Updates the position of the popper, computing the new offsets and applying the new style\n     * @method\n     * @memberof Popper\n     */\n    Popper.prototype.update = function () {\n        var data = { instance: this, styles: {} };\n\n        // store placement inside the data object, modifiers will be able to edit `placement` if needed\n        // and refer to _originalPlacement to know the original value\n        data.placement = this._options.placement;\n        data._originalPlacement = this._options.placement;\n\n        // compute the popper and reference offsets and put them inside data.offsets\n        data.offsets = this._getOffsets(this._popper, this._reference, data.placement);\n\n        // get boundaries\n        data.boundaries = this._getBoundaries(data, this._options.boundariesPadding, this._options.boundariesElement);\n\n        data = this.runModifiers(data, this._options.modifiers);\n\n        if (typeof this.state.updateCallback === 'function') {\n            this.state.updateCallback(data);\n        }\n    };\n\n    /**\n     * If a function is passed, it will be executed after the initialization of popper with as first argument the Popper instance.\n     * @method\n     * @memberof Popper\n     * @param {Function} callback\n     */\n    Popper.prototype.onCreate = function (callback) {\n        // the createCallbacks return as first argument the popper instance\n        callback(this);\n        return this;\n    };\n\n    /**\n     * If a function is passed, it will be executed after each update of popper with as first argument the set of coordinates and informations\n     * used to style popper and its arrow.\n     * NOTE: it doesn't get fired on the first call of the `Popper.update()` method inside the `Popper` constructor!\n     * @method\n     * @memberof Popper\n     * @param {Function} callback\n     */\n    Popper.prototype.onUpdate = function (callback) {\n        this.state.updateCallback = callback;\n        return this;\n    };\n\n    /**\n     * Helper used to generate poppers from a configuration file\n     * @method\n     * @memberof Popper\n     * @param config {Object} configuration\n     * @returns {HTMLElement} popper\n     */\n    Popper.prototype.parse = function (config) {\n        var defaultConfig = {\n            tagName: 'div',\n            classNames: ['popper'],\n            attributes: [],\n            parent: root.document.body,\n            content: '',\n            contentType: 'text',\n            arrowTagName: 'div',\n            arrowClassNames: ['popper__arrow'],\n            arrowAttributes: ['x-arrow']\n        };\n        config = Object.assign({}, defaultConfig, config);\n\n        var d = root.document;\n\n        var popper = d.createElement(config.tagName);\n        addClassNames(popper, config.classNames);\n        addAttributes(popper, config.attributes);\n        if (config.contentType === 'node') {\n            popper.appendChild(config.content.jquery ? config.content[0] : config.content);\n        } else if (config.contentType === 'html') {\n            popper.innerHTML = config.content;\n        } else {\n            popper.textContent = config.content;\n        }\n\n        if (config.arrowTagName) {\n            var arrow = d.createElement(config.arrowTagName);\n            addClassNames(arrow, config.arrowClassNames);\n            addAttributes(arrow, config.arrowAttributes);\n            popper.appendChild(arrow);\n        }\n\n        var parent = config.parent.jquery ? config.parent[0] : config.parent;\n\n        // if the given parent is a string, use it to match an element\n        // if more than one element is matched, the first one will be used as parent\n        // if no elements are matched, the script will throw an error\n        if (typeof parent === 'string') {\n            parent = d.querySelectorAll(config.parent);\n            if (parent.length > 1) {\n                console.warn('WARNING: the given `parent` query(' + config.parent + ') matched more than one element, the first one will be used');\n            }\n            if (parent.length === 0) {\n                throw 'ERROR: the given `parent` doesn\\'t exists!';\n            }\n            parent = parent[0];\n        }\n        // if the given parent is a DOM nodes list or an array of nodes with more than one element,\n        // the first one will be used as parent\n        if (parent.length > 1 && parent instanceof Element === false) {\n            console.warn('WARNING: you have passed as parent a list of elements, the first one will be used');\n            parent = parent[0];\n        }\n\n        // append the generated popper to its parent\n        parent.appendChild(popper);\n\n        return popper;\n\n        /**\n         * Adds class names to the given element\n         * @function\n         * @ignore\n         * @param {HTMLElement} target\n         * @param {Array} classes\n         */\n        function addClassNames(element, classNames) {\n            classNames.forEach(function (className) {\n                element.classList.add(className);\n            });\n        }\n\n        /**\n         * Adds attributes to the given element\n         * @function\n         * @ignore\n         * @param {HTMLElement} target\n         * @param {Array} attributes\n         * @example\n         * addAttributes(element, [ 'data-info:foobar' ]);\n         */\n        function addAttributes(element, attributes) {\n            attributes.forEach(function (attribute) {\n                element.setAttribute(attribute.split(':')[0], attribute.split(':')[1] || '');\n            });\n        }\n    };\n\n    /**\n     * Helper used to get the position which will be applied to the popper\n     * @method\n     * @memberof Popper\n     * @param config {HTMLElement} popper element\n     * @param reference {HTMLElement} reference element\n     * @returns {String} position\n     */\n    Popper.prototype._getPosition = function (popper, reference) {\n        var container = getOffsetParent(reference);\n\n        if (this._options.forceAbsolute) {\n            return 'absolute';\n        }\n\n        // Decide if the popper will be fixed\n        // If the reference element is inside a fixed context, the popper will be fixed as well to allow them to scroll together\n        var isParentFixed = isFixed(reference, container);\n        return isParentFixed ? 'fixed' : 'absolute';\n    };\n\n    /**\n     * Get offsets to the popper\n     * @method\n     * @memberof Popper\n     * @access private\n     * @param {Element} popper - the popper element\n     * @param {Element} reference - the reference element (the popper will be relative to this)\n     * @returns {Object} An object containing the offsets which will be applied to the popper\n     */\n    Popper.prototype._getOffsets = function (popper, reference, placement) {\n        placement = placement.split('-')[0];\n        var popperOffsets = {};\n\n        popperOffsets.position = this.state.position;\n        var isParentFixed = popperOffsets.position === 'fixed';\n\n        //\n        // Get reference element position\n        //\n        var referenceOffsets = getOffsetRectRelativeToCustomParent(reference, getOffsetParent(popper), isParentFixed);\n\n        //\n        // Get popper sizes\n        //\n        var popperRect = getOuterSizes(popper);\n\n        //\n        // Compute offsets of popper\n        //\n\n        // depending by the popper placement we have to compute its offsets slightly differently\n        if (['right', 'left'].indexOf(placement) !== -1) {\n            popperOffsets.top = referenceOffsets.top + referenceOffsets.height / 2 - popperRect.height / 2;\n            if (placement === 'left') {\n                popperOffsets.left = referenceOffsets.left - popperRect.width;\n            } else {\n                popperOffsets.left = referenceOffsets.right;\n            }\n        } else {\n            popperOffsets.left = referenceOffsets.left + referenceOffsets.width / 2 - popperRect.width / 2;\n            if (placement === 'top') {\n                popperOffsets.top = referenceOffsets.top - popperRect.height;\n            } else {\n                popperOffsets.top = referenceOffsets.bottom;\n            }\n        }\n\n        // Add width and height to our offsets object\n        popperOffsets.width = popperRect.width;\n        popperOffsets.height = popperRect.height;\n\n        return {\n            popper: popperOffsets,\n            reference: referenceOffsets\n        };\n    };\n\n    /**\n     * Setup needed event listeners used to update the popper position\n     * @method\n     * @memberof Popper\n     * @access private\n     */\n    Popper.prototype._setupEventListeners = function () {\n        // NOTE: 1 DOM access here\n        this.state.updateBound = this.update.bind(this);\n        root.addEventListener('resize', this.state.updateBound);\n        // if the boundariesElement is window we don't need to listen for the scroll event\n        if (this._options.boundariesElement !== 'window') {\n            var target = getScrollParent(this._reference);\n            // here it could be both `body` or `documentElement` thanks to Firefox, we then check both\n            if (target === root.document.body || target === root.document.documentElement) {\n                target = root;\n            }\n            target.addEventListener('scroll', this.state.updateBound);\n            this.state.scrollTarget = target;\n        }\n    };\n\n    /**\n     * Remove event listeners used to update the popper position\n     * @method\n     * @memberof Popper\n     * @access private\n     */\n    Popper.prototype._removeEventListeners = function () {\n        // NOTE: 1 DOM access here\n        root.removeEventListener('resize', this.state.updateBound);\n        if (this._options.boundariesElement !== 'window' && this.state.scrollTarget) {\n            this.state.scrollTarget.removeEventListener('scroll', this.state.updateBound);\n            this.state.scrollTarget = null;\n        }\n        this.state.updateBound = null;\n    };\n\n    /**\n     * Computed the boundaries limits and return them\n     * @method\n     * @memberof Popper\n     * @access private\n     * @param {Object} data - Object containing the property \"offsets\" generated by `_getOffsets`\n     * @param {Number} padding - Boundaries padding\n     * @param {Element} boundariesElement - Element used to define the boundaries\n     * @returns {Object} Coordinates of the boundaries\n     */\n    Popper.prototype._getBoundaries = function (data, padding, boundariesElement) {\n        // NOTE: 1 DOM access here\n        var boundaries = {};\n        var width, height;\n        if (boundariesElement === 'window') {\n            var body = root.document.body,\n                html = root.document.documentElement;\n\n            height = Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);\n            width = Math.max(body.scrollWidth, body.offsetWidth, html.clientWidth, html.scrollWidth, html.offsetWidth);\n\n            boundaries = {\n                top: 0,\n                right: width,\n                bottom: height,\n                left: 0\n            };\n        } else if (boundariesElement === 'viewport') {\n            var offsetParent = getOffsetParent(this._popper);\n            var scrollParent = getScrollParent(this._popper);\n            var offsetParentRect = getOffsetRect(offsetParent);\n\n            // Thanks the fucking native API, `document.body.scrollTop` & `document.documentElement.scrollTop`\n            var getScrollTopValue = function getScrollTopValue(element) {\n                return element == document.body ? Math.max(document.documentElement.scrollTop, document.body.scrollTop) : element.scrollTop;\n            };\n            var getScrollLeftValue = function getScrollLeftValue(element) {\n                return element == document.body ? Math.max(document.documentElement.scrollLeft, document.body.scrollLeft) : element.scrollLeft;\n            };\n\n            // if the popper is fixed we don't have to substract scrolling from the boundaries\n            var scrollTop = data.offsets.popper.position === 'fixed' ? 0 : getScrollTopValue(scrollParent);\n            var scrollLeft = data.offsets.popper.position === 'fixed' ? 0 : getScrollLeftValue(scrollParent);\n\n            boundaries = {\n                top: 0 - (offsetParentRect.top - scrollTop),\n                right: root.document.documentElement.clientWidth - (offsetParentRect.left - scrollLeft),\n                bottom: root.document.documentElement.clientHeight - (offsetParentRect.top - scrollTop),\n                left: 0 - (offsetParentRect.left - scrollLeft)\n            };\n        } else {\n            if (getOffsetParent(this._popper) === boundariesElement) {\n                boundaries = {\n                    top: 0,\n                    left: 0,\n                    right: boundariesElement.clientWidth,\n                    bottom: boundariesElement.clientHeight\n                };\n            } else {\n                boundaries = getOffsetRect(boundariesElement);\n            }\n        }\n        boundaries.left += padding;\n        boundaries.right -= padding;\n        boundaries.top = boundaries.top + padding;\n        boundaries.bottom = boundaries.bottom - padding;\n        return boundaries;\n    };\n\n    /**\n     * Loop trough the list of modifiers and run them in order, each of them will then edit the data object\n     * @method\n     * @memberof Popper\n     * @access public\n     * @param {Object} data\n     * @param {Array} modifiers\n     * @param {Function} ends\n     */\n    Popper.prototype.runModifiers = function (data, modifiers, ends) {\n        var modifiersToRun = modifiers.slice();\n        if (ends !== undefined) {\n            modifiersToRun = this._options.modifiers.slice(0, getArrayKeyIndex(this._options.modifiers, ends));\n        }\n\n        modifiersToRun.forEach(function (modifier) {\n            if (isFunction(modifier)) {\n                data = modifier.call(this, data);\n            }\n        }.bind(this));\n\n        return data;\n    };\n\n    /**\n     * Helper used to know if the given modifier depends from another one.\n     * @method\n     * @memberof Popper\n     * @param {String} requesting - name of requesting modifier\n     * @param {String} requested - name of requested modifier\n     * @returns {Boolean}\n     */\n    Popper.prototype.isModifierRequired = function (requesting, requested) {\n        var index = getArrayKeyIndex(this._options.modifiers, requesting);\n        return !!this._options.modifiers.slice(0, index).filter(function (modifier) {\n            return modifier === requested;\n        }).length;\n    };\n\n    //\n    // Modifiers\n    //\n\n    /**\n     * Modifiers list\n     * @namespace Popper.modifiers\n     * @memberof Popper\n     * @type {Object}\n     */\n    Popper.prototype.modifiers = {};\n\n    /**\n     * Apply the computed styles to the popper element\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by `update` method\n     * @returns {Object} The same data object\n     */\n    Popper.prototype.modifiers.applyStyle = function (data) {\n        // apply the final offsets to the popper\n        // NOTE: 1 DOM access here\n        var styles = {\n            position: data.offsets.popper.position\n        };\n\n        // round top and left to avoid blurry text\n        var left = Math.round(data.offsets.popper.left);\n        var top = Math.round(data.offsets.popper.top);\n\n        // if gpuAcceleration is set to true and transform is supported, we use `translate3d` to apply the position to the popper\n        // we automatically use the supported prefixed version if needed\n        var prefixedProperty;\n        if (this._options.gpuAcceleration && (prefixedProperty = getSupportedPropertyName('transform'))) {\n            styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n            styles.top = 0;\n            styles.left = 0;\n        }\n        // othwerise, we use the standard `left` and `top` properties\n        else {\n                styles.left = left;\n                styles.top = top;\n            }\n\n        // any property present in `data.styles` will be applied to the popper,\n        // in this way we can make the 3rd party modifiers add custom styles to it\n        // Be aware, modifiers could override the properties defined in the previous\n        // lines of this modifier!\n        Object.assign(styles, data.styles);\n\n        setStyle(this._popper, styles);\n\n        // set an attribute which will be useful to style the tooltip (use it to properly position its arrow)\n        // NOTE: 1 DOM access here\n        this._popper.setAttribute('x-placement', data.placement);\n\n        // if the arrow modifier is required and the arrow style has been computed, apply the arrow style\n        if (this.isModifierRequired(this.modifiers.applyStyle, this.modifiers.arrow) && data.offsets.arrow) {\n            setStyle(data.arrowElement, data.offsets.arrow);\n        }\n\n        return data;\n    };\n\n    /**\n     * Modifier used to shift the popper on the start or end of its reference element side\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by `update` method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.shift = function (data) {\n        var placement = data.placement;\n        var basePlacement = placement.split('-')[0];\n        var shiftVariation = placement.split('-')[1];\n\n        // if shift shiftVariation is specified, run the modifier\n        if (shiftVariation) {\n            var reference = data.offsets.reference;\n            var popper = getPopperClientRect(data.offsets.popper);\n\n            var shiftOffsets = {\n                y: {\n                    start: { top: reference.top },\n                    end: { top: reference.top + reference.height - popper.height }\n                },\n                x: {\n                    start: { left: reference.left },\n                    end: { left: reference.left + reference.width - popper.width }\n                }\n            };\n\n            var axis = ['bottom', 'top'].indexOf(basePlacement) !== -1 ? 'x' : 'y';\n\n            data.offsets.popper = Object.assign(popper, shiftOffsets[axis][shiftVariation]);\n        }\n\n        return data;\n    };\n\n    /**\n     * Modifier used to make sure the popper does not overflows from it's boundaries\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by `update` method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.preventOverflow = function (data) {\n        var order = this._options.preventOverflowOrder;\n        var popper = getPopperClientRect(data.offsets.popper);\n\n        var check = {\n            left: function left() {\n                var left = popper.left;\n                if (popper.left < data.boundaries.left) {\n                    left = Math.max(popper.left, data.boundaries.left);\n                }\n                return { left: left };\n            },\n            right: function right() {\n                var left = popper.left;\n                if (popper.right > data.boundaries.right) {\n                    left = Math.min(popper.left, data.boundaries.right - popper.width);\n                }\n                return { left: left };\n            },\n            top: function top() {\n                var top = popper.top;\n                if (popper.top < data.boundaries.top) {\n                    top = Math.max(popper.top, data.boundaries.top);\n                }\n                return { top: top };\n            },\n            bottom: function bottom() {\n                var top = popper.top;\n                if (popper.bottom > data.boundaries.bottom) {\n                    top = Math.min(popper.top, data.boundaries.bottom - popper.height);\n                }\n                return { top: top };\n            }\n        };\n\n        order.forEach(function (direction) {\n            data.offsets.popper = Object.assign(popper, check[direction]());\n        });\n\n        return data;\n    };\n\n    /**\n     * Modifier used to make sure the popper is always near its reference\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by _update method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.keepTogether = function (data) {\n        var popper = getPopperClientRect(data.offsets.popper);\n        var reference = data.offsets.reference;\n        var f = Math.floor;\n\n        if (popper.right < f(reference.left)) {\n            data.offsets.popper.left = f(reference.left) - popper.width;\n        }\n        if (popper.left > f(reference.right)) {\n            data.offsets.popper.left = f(reference.right);\n        }\n        if (popper.bottom < f(reference.top)) {\n            data.offsets.popper.top = f(reference.top) - popper.height;\n        }\n        if (popper.top > f(reference.bottom)) {\n            data.offsets.popper.top = f(reference.bottom);\n        }\n\n        return data;\n    };\n\n    /**\n     * Modifier used to flip the placement of the popper when the latter is starting overlapping its reference element.\n     * Requires the `preventOverflow` modifier before it in order to work.\n     * **NOTE:** This modifier will run all its previous modifiers everytime it tries to flip the popper!\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by _update method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.flip = function (data) {\n        // check if preventOverflow is in the list of modifiers before the flip modifier.\n        // otherwise flip would not work as expected.\n        if (!this.isModifierRequired(this.modifiers.flip, this.modifiers.preventOverflow)) {\n            console.warn('WARNING: preventOverflow modifier is required by flip modifier in order to work, be sure to include it before flip!');\n            return data;\n        }\n\n        if (data.flipped && data.placement === data._originalPlacement) {\n            // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n            return data;\n        }\n\n        var placement = data.placement.split('-')[0];\n        var placementOpposite = getOppositePlacement(placement);\n        var variation = data.placement.split('-')[1] || '';\n\n        var flipOrder = [];\n        if (this._options.flipBehavior === 'flip') {\n            flipOrder = [placement, placementOpposite];\n        } else {\n            flipOrder = this._options.flipBehavior;\n        }\n\n        flipOrder.forEach(function (step, index) {\n            if (placement !== step || flipOrder.length === index + 1) {\n                return;\n            }\n\n            placement = data.placement.split('-')[0];\n            placementOpposite = getOppositePlacement(placement);\n\n            var popperOffsets = getPopperClientRect(data.offsets.popper);\n\n            // this boolean is used to distinguish right and bottom from top and left\n            // they need different computations to get flipped\n            var a = ['right', 'bottom'].indexOf(placement) !== -1;\n\n            // using Math.floor because the reference offsets may contain decimals we are not going to consider here\n            if (a && Math.floor(data.offsets.reference[placement]) > Math.floor(popperOffsets[placementOpposite]) || !a && Math.floor(data.offsets.reference[placement]) < Math.floor(popperOffsets[placementOpposite])) {\n                // we'll use this boolean to detect any flip loop\n                data.flipped = true;\n                data.placement = flipOrder[index + 1];\n                if (variation) {\n                    data.placement += '-' + variation;\n                }\n                data.offsets.popper = this._getOffsets(this._popper, this._reference, data.placement).popper;\n\n                data = this.runModifiers(data, this._options.modifiers, this._flip);\n            }\n        }.bind(this));\n        return data;\n    };\n\n    /**\n     * Modifier used to add an offset to the popper, useful if you more granularity positioning your popper.\n     * The offsets will shift the popper on the side of its reference element.\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by _update method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.offset = function (data) {\n        var offset = this._options.offset;\n        var popper = data.offsets.popper;\n\n        if (data.placement.indexOf('left') !== -1) {\n            popper.top -= offset;\n        } else if (data.placement.indexOf('right') !== -1) {\n            popper.top += offset;\n        } else if (data.placement.indexOf('top') !== -1) {\n            popper.left -= offset;\n        } else if (data.placement.indexOf('bottom') !== -1) {\n            popper.left += offset;\n        }\n        return data;\n    };\n\n    /**\n     * Modifier used to move the arrows on the edge of the popper to make sure them are always between the popper and the reference element\n     * It will use the CSS outer size of the arrow element to know how many pixels of conjuction are needed\n     * @method\n     * @memberof Popper.modifiers\n     * @argument {Object} data - The data object generated by _update method\n     * @returns {Object} The data object, properly modified\n     */\n    Popper.prototype.modifiers.arrow = function (data) {\n        var arrow = this._options.arrowElement;\n        var arrowOffset = this._options.arrowOffset;\n\n        // if the arrowElement is a string, suppose it's a CSS selector\n        if (typeof arrow === 'string') {\n            arrow = this._popper.querySelector(arrow);\n        }\n\n        // if arrow element is not found, don't run the modifier\n        if (!arrow) {\n            return data;\n        }\n\n        // the arrow element must be child of its popper\n        if (!this._popper.contains(arrow)) {\n            console.warn('WARNING: `arrowElement` must be child of its popper element!');\n            return data;\n        }\n\n        // arrow depends on keepTogether in order to work\n        if (!this.isModifierRequired(this.modifiers.arrow, this.modifiers.keepTogether)) {\n            console.warn('WARNING: keepTogether modifier is required by arrow modifier in order to work, be sure to include it before arrow!');\n            return data;\n        }\n\n        var arrowStyle = {};\n        var placement = data.placement.split('-')[0];\n        var popper = getPopperClientRect(data.offsets.popper);\n        var reference = data.offsets.reference;\n        var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n        var len = isVertical ? 'height' : 'width';\n        var side = isVertical ? 'top' : 'left';\n        var translate = isVertical ? 'translateY' : 'translateX';\n        var altSide = isVertical ? 'left' : 'top';\n        var opSide = isVertical ? 'bottom' : 'right';\n        var arrowSize = getOuterSizes(arrow)[len];\n\n        //\n        // extends keepTogether behavior making sure the popper and its reference have enough pixels in conjuction\n        //\n\n        // top/left side\n        if (reference[opSide] - arrowSize < popper[side]) {\n            data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowSize);\n        }\n        // bottom/right side\n        if (reference[side] + arrowSize > popper[opSide]) {\n            data.offsets.popper[side] += reference[side] + arrowSize - popper[opSide];\n        }\n\n        // compute center of the popper\n        var center = reference[side] + (arrowOffset || reference[len] / 2 - arrowSize / 2);\n\n        var sideValue = center - popper[side];\n\n        // prevent arrow from being placed not contiguously to its popper\n        sideValue = Math.max(Math.min(popper[len] - arrowSize - 8, sideValue), 8);\n        arrowStyle[side] = sideValue;\n        arrowStyle[altSide] = ''; // make sure to remove any old style from the arrow\n\n        data.offsets.arrow = arrowStyle;\n        data.arrowElement = arrow;\n\n        return data;\n    };\n\n    //\n    // Helpers\n    //\n\n    /**\n     * Get the outer sizes of the given element (offset size + margins)\n     * @function\n     * @ignore\n     * @argument {Element} element\n     * @returns {Object} object containing width and height properties\n     */\n    function getOuterSizes(element) {\n        // NOTE: 1 DOM access here\n        var _display = element.style.display,\n            _visibility = element.style.visibility;\n        element.style.display = 'block';element.style.visibility = 'hidden';\n        var calcWidthToForceRepaint = element.offsetWidth;\n\n        // original method\n        var styles = root.getComputedStyle(element);\n        var x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n        var y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n        var result = { width: element.offsetWidth + y, height: element.offsetHeight + x };\n\n        // reset element styles\n        element.style.display = _display;element.style.visibility = _visibility;\n        return result;\n    }\n\n    /**\n     * Get the opposite placement of the given one/\n     * @function\n     * @ignore\n     * @argument {String} placement\n     * @returns {String} flipped placement\n     */\n    function getOppositePlacement(placement) {\n        var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n        return placement.replace(/left|right|bottom|top/g, function (matched) {\n            return hash[matched];\n        });\n    }\n\n    /**\n     * Given the popper offsets, generate an output similar to getBoundingClientRect\n     * @function\n     * @ignore\n     * @argument {Object} popperOffsets\n     * @returns {Object} ClientRect like output\n     */\n    function getPopperClientRect(popperOffsets) {\n        var offsets = Object.assign({}, popperOffsets);\n        offsets.right = offsets.left + offsets.width;\n        offsets.bottom = offsets.top + offsets.height;\n        return offsets;\n    }\n\n    /**\n     * Given an array and the key to find, returns its index\n     * @function\n     * @ignore\n     * @argument {Array} arr\n     * @argument keyToFind\n     * @returns index or null\n     */\n    function getArrayKeyIndex(arr, keyToFind) {\n        var i = 0,\n            key;\n        for (key in arr) {\n            if (arr[key] === keyToFind) {\n                return i;\n            }\n            i++;\n        }\n        return null;\n    }\n\n    /**\n     * Get CSS computed property of the given element\n     * @function\n     * @ignore\n     * @argument {Eement} element\n     * @argument {String} property\n     */\n    function getStyleComputedProperty(element, property) {\n        // NOTE: 1 DOM access here\n        var css = root.getComputedStyle(element, null);\n        return css[property];\n    }\n\n    /**\n     * Returns the offset parent of the given element\n     * @function\n     * @ignore\n     * @argument {Element} element\n     * @returns {Element} offset parent\n     */\n    function getOffsetParent(element) {\n        // NOTE: 1 DOM access here\n        var offsetParent = element.offsetParent;\n        return offsetParent === root.document.body || !offsetParent ? root.document.documentElement : offsetParent;\n    }\n\n    /**\n     * Returns the scrolling parent of the given element\n     * @function\n     * @ignore\n     * @argument {Element} element\n     * @returns {Element} offset parent\n     */\n    function getScrollParent(element) {\n        var parent = element.parentNode;\n\n        if (!parent) {\n            return element;\n        }\n\n        if (parent === root.document) {\n            // Firefox puts the scrollTOp value on `documentElement` instead of `body`, we then check which of them is\n            // greater than 0 and return the proper element\n            if (root.document.body.scrollTop || root.document.body.scrollLeft) {\n                return root.document.body;\n            } else {\n                return root.document.documentElement;\n            }\n        }\n\n        // Firefox want us to check `-x` and `-y` variations as well\n        if (['scroll', 'auto'].indexOf(getStyleComputedProperty(parent, 'overflow')) !== -1 || ['scroll', 'auto'].indexOf(getStyleComputedProperty(parent, 'overflow-x')) !== -1 || ['scroll', 'auto'].indexOf(getStyleComputedProperty(parent, 'overflow-y')) !== -1) {\n            // If the detected scrollParent is body, we perform an additional check on its parentNode\n            // in this way we'll get body if the browser is Chrome-ish, or documentElement otherwise\n            // fixes issue #65\n            return parent;\n        }\n        return getScrollParent(element.parentNode);\n    }\n\n    /**\n     * Check if the given element is fixed or is inside a fixed parent\n     * @function\n     * @ignore\n     * @argument {Element} element\n     * @argument {Element} customContainer\n     * @returns {Boolean} answer to \"isFixed?\"\n     */\n    function isFixed(element) {\n        if (element === root.document.body) {\n            return false;\n        }\n        if (getStyleComputedProperty(element, 'position') === 'fixed') {\n            return true;\n        }\n        return element.parentNode ? isFixed(element.parentNode) : element;\n    }\n\n    /**\n     * Set the style to the given popper\n     * @function\n     * @ignore\n     * @argument {Element} element - Element to apply the style to\n     * @argument {Object} styles - Object with a list of properties and values which will be applied to the element\n     */\n    function setStyle(element, styles) {\n        function is_numeric(n) {\n            return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n        }\n        Object.keys(styles).forEach(function (prop) {\n            var unit = '';\n            // add unit if the value is numeric and is one of the following\n            if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && is_numeric(styles[prop])) {\n                unit = 'px';\n            }\n            element.style[prop] = styles[prop] + unit;\n        });\n    }\n\n    /**\n     * Check if the given variable is a function\n     * @function\n     * @ignore\n     * @argument {*} functionToCheck - variable to check\n     * @returns {Boolean} answer to: is a function?\n     */\n    function isFunction(functionToCheck) {\n        var getType = {};\n        return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n    }\n\n    /**\n     * Get the position of the given element, relative to its offset parent\n     * @function\n     * @ignore\n     * @param {Element} element\n     * @return {Object} position - Coordinates of the element and its `scrollTop`\n     */\n    function getOffsetRect(element) {\n        var elementRect = {\n            width: element.offsetWidth,\n            height: element.offsetHeight,\n            left: element.offsetLeft,\n            top: element.offsetTop\n        };\n\n        elementRect.right = elementRect.left + elementRect.width;\n        elementRect.bottom = elementRect.top + elementRect.height;\n\n        // position\n        return elementRect;\n    }\n\n    /**\n     * Get bounding client rect of given element\n     * @function\n     * @ignore\n     * @param {HTMLElement} element\n     * @return {Object} client rect\n     */\n    function getBoundingClientRect(element) {\n        var rect = element.getBoundingClientRect();\n\n        // whether the IE version is lower than 11\n        var isIE = navigator.userAgent.indexOf(\"MSIE\") != -1;\n\n        // fix ie document bounding top always 0 bug\n        var rectTop = isIE && element.tagName === 'HTML' ? -element.scrollTop : rect.top;\n\n        return {\n            left: rect.left,\n            top: rectTop,\n            right: rect.right,\n            bottom: rect.bottom,\n            width: rect.right - rect.left,\n            height: rect.bottom - rectTop\n        };\n    }\n\n    /**\n     * Given an element and one of its parents, return the offset\n     * @function\n     * @ignore\n     * @param {HTMLElement} element\n     * @param {HTMLElement} parent\n     * @return {Object} rect\n     */\n    function getOffsetRectRelativeToCustomParent(element, parent, fixed) {\n        var elementRect = getBoundingClientRect(element);\n        var parentRect = getBoundingClientRect(parent);\n\n        if (fixed) {\n            var scrollParent = getScrollParent(parent);\n            parentRect.top += scrollParent.scrollTop;\n            parentRect.bottom += scrollParent.scrollTop;\n            parentRect.left += scrollParent.scrollLeft;\n            parentRect.right += scrollParent.scrollLeft;\n        }\n\n        var rect = {\n            top: elementRect.top - parentRect.top,\n            left: elementRect.left - parentRect.left,\n            bottom: elementRect.top - parentRect.top + elementRect.height,\n            right: elementRect.left - parentRect.left + elementRect.width,\n            width: elementRect.width,\n            height: elementRect.height\n        };\n        return rect;\n    }\n\n    /**\n     * Get the prefixed supported property name\n     * @function\n     * @ignore\n     * @argument {String} property (camelCase)\n     * @returns {String} prefixed property (camelCase)\n     */\n    function getSupportedPropertyName(property) {\n        var prefixes = ['', 'ms', 'webkit', 'moz', 'o'];\n\n        for (var i = 0; i < prefixes.length; i++) {\n            var toCheck = prefixes[i] ? prefixes[i] + property.charAt(0).toUpperCase() + property.slice(1) : property;\n            if (typeof root.document.body.style[toCheck] !== 'undefined') {\n                return toCheck;\n            }\n        }\n        return null;\n    }\n\n    /**\n     * The Object.assign() method is used to copy the values of all enumerable own properties from one or more source\n     * objects to a target object. It will return the target object.\n     * This polyfill doesn't support symbol properties, since ES5 doesn't have symbols anyway\n     * Source: https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n     * @function\n     * @ignore\n     */\n    if (!Object.assign) {\n        Object.defineProperty(Object, 'assign', {\n            enumerable: false,\n            configurable: true,\n            writable: true,\n            value: function value(target) {\n                if (target === undefined || target === null) {\n                    throw new TypeError('Cannot convert first argument to object');\n                }\n\n                var to = Object(target);\n                for (var i = 1; i < arguments.length; i++) {\n                    var nextSource = arguments[i];\n                    if (nextSource === undefined || nextSource === null) {\n                        continue;\n                    }\n                    nextSource = Object(nextSource);\n\n                    var keysArray = Object.keys(nextSource);\n                    for (var nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex++) {\n                        var nextKey = keysArray[nextIndex];\n                        var desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n                        if (desc !== undefined && desc.enumerable) {\n                            to[nextKey] = nextSource[nextKey];\n                        }\n                    }\n                }\n                return to;\n            }\n        });\n    }\n\n    return Popper;\n});"], "mappings": "AAAA,YAAY;;AAACA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAAAA,OAAA;AAEb,IAAIC,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAUC,GAAG,EAAE;EAAE,OAAO,OAAOA,GAAG;AAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAI,OAAOF,MAAM,KAAK,UAAU,IAAIE,GAAG,CAACC,WAAW,KAAKH,MAAM,IAAIE,GAAG,KAAKF,MAAM,CAACI,SAAS,GAAG,QAAQ,GAAG,OAAOF,GAAG;AAAE,CAAC;;AAE5Q;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAAC,CAAC,UAAUG,IAAI,EAAEC,OAAO,EAAE;EACvB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC5C;IACAD,MAAM,CAACD,OAAO,CAAC;EACnB,CAAC,MAAM,IAAI,CAAC,OAAOG,MAAM,KAAK,WAAW,GAAG,WAAW,GAAGV,OAAO,CAACU,MAAM,CAAC,MAAM,QAAQ,IAAIA,MAAM,CAACC,OAAO,EAAE;IACvG;IACA;IACA;IACAD,MAAM,CAACC,OAAO,GAAGJ,OAAO,CAAC,CAAC;EAC9B,CAAC,MAAM;IACH;IACAD,IAAI,CAACM,MAAM,GAAGL,OAAO,CAAC,CAAC;EAC3B;AACJ,CAAC,EAAEM,SAAS,EAAE,YAAY;EAEtB,YAAY;;EAEZ,IAAIP,IAAI,GAAGQ,MAAM;;EAEjB;EACA,IAAIC,QAAQ,GAAG;IACX;IACAC,SAAS,EAAE,QAAQ;IAEnBC,eAAe,EAAE,IAAI;IAErB;IACAC,MAAM,EAAE,CAAC;IAET;IACAC,iBAAiB,EAAE,UAAU;IAE7B;IACAC,iBAAiB,EAAE,CAAC;IAEpB;IACA;IACAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;IAExD;IACAC,YAAY,EAAE,MAAM;IAEpBC,YAAY,EAAE,WAAW;IAEzBC,WAAW,EAAE,CAAC;IAEd;IACAC,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,iBAAiB,EAAE,cAAc,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC;IAEhGC,gBAAgB,EAAE,EAAE;IAEpBC,aAAa,EAAE;EACnB,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASf,MAAMA,CAACgB,SAAS,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACxC,IAAI,CAACC,UAAU,GAAGH,SAAS,CAACI,MAAM,GAAGJ,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS;IAC7D,IAAI,CAACK,KAAK,GAAG,CAAC,CAAC;;IAEf;IACA;IACA,IAAIC,YAAY,GAAG,OAAOL,MAAM,KAAK,WAAW,IAAIA,MAAM,KAAK,IAAI;IACnE,IAAIM,QAAQ,GAAGN,MAAM,IAAIO,MAAM,CAAC/B,SAAS,CAACgC,QAAQ,CAACC,IAAI,CAACT,MAAM,CAAC,KAAK,iBAAiB;IACrF,IAAIK,YAAY,IAAIC,QAAQ,EAAE;MAC1B,IAAI,CAACI,OAAO,GAAG,IAAI,CAACC,KAAK,CAACL,QAAQ,GAAGN,MAAM,GAAG,CAAC,CAAC,CAAC;IACrD;IACA;IAAA,KACK;MACG,IAAI,CAACU,OAAO,GAAGV,MAAM,CAACG,MAAM,GAAGH,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM;IACrD;;IAEJ;IACA,IAAI,CAACY,QAAQ,GAAGL,MAAM,CAACM,MAAM,CAAC,CAAC,CAAC,EAAE3B,QAAQ,EAAEe,OAAO,CAAC;;IAEpD;IACA,IAAI,CAACW,QAAQ,CAAChB,SAAS,GAAG,IAAI,CAACgB,QAAQ,CAAChB,SAAS,CAACkB,GAAG,CAAC,UAAUC,QAAQ,EAAE;MACtE;MACA,IAAI,IAAI,CAACH,QAAQ,CAACf,gBAAgB,CAACmB,OAAO,CAACD,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;;MAE7D;MACA;MACA,IAAIA,QAAQ,KAAK,YAAY,EAAE;QAC3B,IAAI,CAACL,OAAO,CAACO,YAAY,CAAC,aAAa,EAAE,IAAI,CAACL,QAAQ,CAACzB,SAAS,CAAC;MACrE;;MAEA;MACA,OAAO,IAAI,CAACS,SAAS,CAACmB,QAAQ,CAAC,IAAIA,QAAQ;IAC/C,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;;IAEb;IACA,IAAI,CAACd,KAAK,CAACe,QAAQ,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,CAACV,OAAO,EAAE,IAAI,CAACR,UAAU,CAAC;IACtEmB,QAAQ,CAAC,IAAI,CAACX,OAAO,EAAE;MAAES,QAAQ,EAAE,IAAI,CAACf,KAAK,CAACe,QAAQ;MAAEG,GAAG,EAAE;IAAE,CAAC,CAAC;;IAEjE;IACA,IAAI,CAACC,MAAM,CAAC,CAAC;;IAEb;IACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,OAAO,IAAI;EACf;;EAEA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;EACIzC,MAAM,CAACP,SAAS,CAACiD,OAAO,GAAG,YAAY;IACnC,IAAI,CAACf,OAAO,CAACgB,eAAe,CAAC,aAAa,CAAC;IAC3C,IAAI,CAAChB,OAAO,CAACiB,KAAK,CAACC,IAAI,GAAG,EAAE;IAC5B,IAAI,CAAClB,OAAO,CAACiB,KAAK,CAACR,QAAQ,GAAG,EAAE;IAChC,IAAI,CAACT,OAAO,CAACiB,KAAK,CAACL,GAAG,GAAG,EAAE;IAC3B,IAAI,CAACZ,OAAO,CAACiB,KAAK,CAACE,wBAAwB,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE;IAC9D,IAAI,CAACC,qBAAqB,CAAC,CAAC;;IAE5B;IACA,IAAI,IAAI,CAAClB,QAAQ,CAACmB,eAAe,EAAE;MAC/B,IAAI,CAACrB,OAAO,CAACsB,MAAM,CAAC,CAAC;IACzB;IACA,OAAO,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;EACIjD,MAAM,CAACP,SAAS,CAAC+C,MAAM,GAAG,YAAY;IAClC,IAAIU,IAAI,GAAG;MAAEC,QAAQ,EAAE,IAAI;MAAEC,MAAM,EAAE,CAAC;IAAE,CAAC;;IAEzC;IACA;IACAF,IAAI,CAAC9C,SAAS,GAAG,IAAI,CAACyB,QAAQ,CAACzB,SAAS;IACxC8C,IAAI,CAACG,kBAAkB,GAAG,IAAI,CAACxB,QAAQ,CAACzB,SAAS;;IAEjD;IACA8C,IAAI,CAACI,OAAO,GAAG,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC5B,OAAO,EAAE,IAAI,CAACR,UAAU,EAAE+B,IAAI,CAAC9C,SAAS,CAAC;;IAE9E;IACA8C,IAAI,CAACM,UAAU,GAAG,IAAI,CAACC,cAAc,CAACP,IAAI,EAAE,IAAI,CAACrB,QAAQ,CAACrB,iBAAiB,EAAE,IAAI,CAACqB,QAAQ,CAACtB,iBAAiB,CAAC;IAE7G2C,IAAI,GAAG,IAAI,CAACQ,YAAY,CAACR,IAAI,EAAE,IAAI,CAACrB,QAAQ,CAAChB,SAAS,CAAC;IAEvD,IAAI,OAAO,IAAI,CAACQ,KAAK,CAACsC,cAAc,KAAK,UAAU,EAAE;MACjD,IAAI,CAACtC,KAAK,CAACsC,cAAc,CAACT,IAAI,CAAC;IACnC;EACJ,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;EACIlD,MAAM,CAACP,SAAS,CAACmE,QAAQ,GAAG,UAAUC,QAAQ,EAAE;IAC5C;IACAA,QAAQ,CAAC,IAAI,CAAC;IACd,OAAO,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI7D,MAAM,CAACP,SAAS,CAACqE,QAAQ,GAAG,UAAUD,QAAQ,EAAE;IAC5C,IAAI,CAACxC,KAAK,CAACsC,cAAc,GAAGE,QAAQ;IACpC,OAAO,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACI7D,MAAM,CAACP,SAAS,CAACmC,KAAK,GAAG,UAAUmC,MAAM,EAAE;IACvC,IAAIC,aAAa,GAAG;MAChBC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE,CAAC,QAAQ,CAAC;MACtBC,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE1E,IAAI,CAAC2E,QAAQ,CAACC,IAAI;MAC1BC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE,MAAM;MACnBC,YAAY,EAAE,KAAK;MACnBC,eAAe,EAAE,CAAC,eAAe,CAAC;MAClCC,eAAe,EAAE,CAAC,SAAS;IAC/B,CAAC;IACDZ,MAAM,GAAGvC,MAAM,CAACM,MAAM,CAAC,CAAC,CAAC,EAAEkC,aAAa,EAAED,MAAM,CAAC;IAEjD,IAAIa,CAAC,GAAGlF,IAAI,CAAC2E,QAAQ;IAErB,IAAIpD,MAAM,GAAG2D,CAAC,CAACC,aAAa,CAACd,MAAM,CAACE,OAAO,CAAC;IAC5Ca,aAAa,CAAC7D,MAAM,EAAE8C,MAAM,CAACG,UAAU,CAAC;IACxCa,aAAa,CAAC9D,MAAM,EAAE8C,MAAM,CAACI,UAAU,CAAC;IACxC,IAAIJ,MAAM,CAACS,WAAW,KAAK,MAAM,EAAE;MAC/BvD,MAAM,CAAC+D,WAAW,CAACjB,MAAM,CAACQ,OAAO,CAACnD,MAAM,GAAG2C,MAAM,CAACQ,OAAO,CAAC,CAAC,CAAC,GAAGR,MAAM,CAACQ,OAAO,CAAC;IAClF,CAAC,MAAM,IAAIR,MAAM,CAACS,WAAW,KAAK,MAAM,EAAE;MACtCvD,MAAM,CAACgE,SAAS,GAAGlB,MAAM,CAACQ,OAAO;IACrC,CAAC,MAAM;MACHtD,MAAM,CAACiE,WAAW,GAAGnB,MAAM,CAACQ,OAAO;IACvC;IAEA,IAAIR,MAAM,CAACU,YAAY,EAAE;MACrB,IAAIU,KAAK,GAAGP,CAAC,CAACC,aAAa,CAACd,MAAM,CAACU,YAAY,CAAC;MAChDK,aAAa,CAACK,KAAK,EAAEpB,MAAM,CAACW,eAAe,CAAC;MAC5CK,aAAa,CAACI,KAAK,EAAEpB,MAAM,CAACY,eAAe,CAAC;MAC5C1D,MAAM,CAAC+D,WAAW,CAACG,KAAK,CAAC;IAC7B;IAEA,IAAIf,MAAM,GAAGL,MAAM,CAACK,MAAM,CAAChD,MAAM,GAAG2C,MAAM,CAACK,MAAM,CAAC,CAAC,CAAC,GAAGL,MAAM,CAACK,MAAM;;IAEpE;IACA;IACA;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAGQ,CAAC,CAACQ,gBAAgB,CAACrB,MAAM,CAACK,MAAM,CAAC;MAC1C,IAAIA,MAAM,CAACiB,MAAM,GAAG,CAAC,EAAE;QACnBC,OAAO,CAACC,IAAI,CAAC,oCAAoC,GAAGxB,MAAM,CAACK,MAAM,GAAG,6DAA6D,CAAC;MACtI;MACA,IAAIA,MAAM,CAACiB,MAAM,KAAK,CAAC,EAAE;QACrB,MAAM,4CAA4C;MACtD;MACAjB,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;IACtB;IACA;IACA;IACA,IAAIA,MAAM,CAACiB,MAAM,GAAG,CAAC,IAAIjB,MAAM,YAAYoB,OAAO,KAAK,KAAK,EAAE;MAC1DF,OAAO,CAACC,IAAI,CAAC,mFAAmF,CAAC;MACjGnB,MAAM,GAAGA,MAAM,CAAC,CAAC,CAAC;IACtB;;IAEA;IACAA,MAAM,CAACY,WAAW,CAAC/D,MAAM,CAAC;IAE1B,OAAOA,MAAM;;IAEb;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,SAAS6D,aAAaA,CAACW,OAAO,EAAEvB,UAAU,EAAE;MACxCA,UAAU,CAACwB,OAAO,CAAC,UAAUC,SAAS,EAAE;QACpCF,OAAO,CAACG,SAAS,CAACC,GAAG,CAACF,SAAS,CAAC;MACpC,CAAC,CAAC;IACN;;IAEA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,SAASZ,aAAaA,CAACU,OAAO,EAAEtB,UAAU,EAAE;MACxCA,UAAU,CAACuB,OAAO,CAAC,UAAUI,SAAS,EAAE;QACpCL,OAAO,CAACvD,YAAY,CAAC4D,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAED,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;MAChF,CAAC,CAAC;IACN;EACJ,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI/F,MAAM,CAACP,SAAS,CAAC4C,YAAY,GAAG,UAAUpB,MAAM,EAAED,SAAS,EAAE;IACzD,IAAIgF,SAAS,GAAGC,eAAe,CAACjF,SAAS,CAAC;IAE1C,IAAI,IAAI,CAACa,QAAQ,CAACd,aAAa,EAAE;MAC7B,OAAO,UAAU;IACrB;;IAEA;IACA;IACA,IAAImF,aAAa,GAAGC,OAAO,CAACnF,SAAS,EAAEgF,SAAS,CAAC;IACjD,OAAOE,aAAa,GAAG,OAAO,GAAG,UAAU;EAC/C,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlG,MAAM,CAACP,SAAS,CAAC8D,WAAW,GAAG,UAAUtC,MAAM,EAAED,SAAS,EAAEZ,SAAS,EAAE;IACnEA,SAAS,GAAGA,SAAS,CAAC2F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACnC,IAAIK,aAAa,GAAG,CAAC,CAAC;IAEtBA,aAAa,CAAChE,QAAQ,GAAG,IAAI,CAACf,KAAK,CAACe,QAAQ;IAC5C,IAAI8D,aAAa,GAAGE,aAAa,CAAChE,QAAQ,KAAK,OAAO;;IAEtD;IACA;IACA;IACA,IAAIiE,gBAAgB,GAAGC,mCAAmC,CAACtF,SAAS,EAAEiF,eAAe,CAAChF,MAAM,CAAC,EAAEiF,aAAa,CAAC;;IAE7G;IACA;IACA;IACA,IAAIK,UAAU,GAAGC,aAAa,CAACvF,MAAM,CAAC;;IAEtC;IACA;IACA;;IAEA;IACA,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAACgB,OAAO,CAAC7B,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7CgG,aAAa,CAAC7D,GAAG,GAAG8D,gBAAgB,CAAC9D,GAAG,GAAG8D,gBAAgB,CAACI,MAAM,GAAG,CAAC,GAAGF,UAAU,CAACE,MAAM,GAAG,CAAC;MAC9F,IAAIrG,SAAS,KAAK,MAAM,EAAE;QACtBgG,aAAa,CAACvD,IAAI,GAAGwD,gBAAgB,CAACxD,IAAI,GAAG0D,UAAU,CAACG,KAAK;MACjE,CAAC,MAAM;QACHN,aAAa,CAACvD,IAAI,GAAGwD,gBAAgB,CAACM,KAAK;MAC/C;IACJ,CAAC,MAAM;MACHP,aAAa,CAACvD,IAAI,GAAGwD,gBAAgB,CAACxD,IAAI,GAAGwD,gBAAgB,CAACK,KAAK,GAAG,CAAC,GAAGH,UAAU,CAACG,KAAK,GAAG,CAAC;MAC9F,IAAItG,SAAS,KAAK,KAAK,EAAE;QACrBgG,aAAa,CAAC7D,GAAG,GAAG8D,gBAAgB,CAAC9D,GAAG,GAAGgE,UAAU,CAACE,MAAM;MAChE,CAAC,MAAM;QACHL,aAAa,CAAC7D,GAAG,GAAG8D,gBAAgB,CAACO,MAAM;MAC/C;IACJ;;IAEA;IACAR,aAAa,CAACM,KAAK,GAAGH,UAAU,CAACG,KAAK;IACtCN,aAAa,CAACK,MAAM,GAAGF,UAAU,CAACE,MAAM;IAExC,OAAO;MACHxF,MAAM,EAAEmF,aAAa;MACrBpF,SAAS,EAAEqF;IACf,CAAC;EACL,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;EACIrG,MAAM,CAACP,SAAS,CAACgD,oBAAoB,GAAG,YAAY;IAChD;IACA,IAAI,CAACpB,KAAK,CAACwF,WAAW,GAAG,IAAI,CAACrE,MAAM,CAACL,IAAI,CAAC,IAAI,CAAC;IAC/CzC,IAAI,CAACoH,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACzF,KAAK,CAACwF,WAAW,CAAC;IACvD;IACA,IAAI,IAAI,CAAChF,QAAQ,CAACtB,iBAAiB,KAAK,QAAQ,EAAE;MAC9C,IAAIwG,MAAM,GAAGC,eAAe,CAAC,IAAI,CAAC7F,UAAU,CAAC;MAC7C;MACA,IAAI4F,MAAM,KAAKrH,IAAI,CAAC2E,QAAQ,CAACC,IAAI,IAAIyC,MAAM,KAAKrH,IAAI,CAAC2E,QAAQ,CAAC4C,eAAe,EAAE;QAC3EF,MAAM,GAAGrH,IAAI;MACjB;MACAqH,MAAM,CAACD,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACzF,KAAK,CAACwF,WAAW,CAAC;MACzD,IAAI,CAACxF,KAAK,CAAC6F,YAAY,GAAGH,MAAM;IACpC;EACJ,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;EACI/G,MAAM,CAACP,SAAS,CAACsD,qBAAqB,GAAG,YAAY;IACjD;IACArD,IAAI,CAACyH,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC9F,KAAK,CAACwF,WAAW,CAAC;IAC1D,IAAI,IAAI,CAAChF,QAAQ,CAACtB,iBAAiB,KAAK,QAAQ,IAAI,IAAI,CAACc,KAAK,CAAC6F,YAAY,EAAE;MACzE,IAAI,CAAC7F,KAAK,CAAC6F,YAAY,CAACC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC9F,KAAK,CAACwF,WAAW,CAAC;MAC7E,IAAI,CAACxF,KAAK,CAAC6F,YAAY,GAAG,IAAI;IAClC;IACA,IAAI,CAAC7F,KAAK,CAACwF,WAAW,GAAG,IAAI;EACjC,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI7G,MAAM,CAACP,SAAS,CAACgE,cAAc,GAAG,UAAUP,IAAI,EAAEkE,OAAO,EAAE7G,iBAAiB,EAAE;IAC1E;IACA,IAAIiD,UAAU,GAAG,CAAC,CAAC;IACnB,IAAIkD,KAAK,EAAED,MAAM;IACjB,IAAIlG,iBAAiB,KAAK,QAAQ,EAAE;MAChC,IAAI+D,IAAI,GAAG5E,IAAI,CAAC2E,QAAQ,CAACC,IAAI;QACzB+C,IAAI,GAAG3H,IAAI,CAAC2E,QAAQ,CAAC4C,eAAe;MAExCR,MAAM,GAAGa,IAAI,CAACC,GAAG,CAACjD,IAAI,CAACkD,YAAY,EAAElD,IAAI,CAACmD,YAAY,EAAEJ,IAAI,CAACK,YAAY,EAAEL,IAAI,CAACG,YAAY,EAAEH,IAAI,CAACI,YAAY,CAAC;MAChHf,KAAK,GAAGY,IAAI,CAACC,GAAG,CAACjD,IAAI,CAACqD,WAAW,EAAErD,IAAI,CAACsD,WAAW,EAAEP,IAAI,CAACQ,WAAW,EAAER,IAAI,CAACM,WAAW,EAAEN,IAAI,CAACO,WAAW,CAAC;MAE1GpE,UAAU,GAAG;QACTjB,GAAG,EAAE,CAAC;QACNoE,KAAK,EAAED,KAAK;QACZE,MAAM,EAAEH,MAAM;QACd5D,IAAI,EAAE;MACV,CAAC;IACL,CAAC,MAAM,IAAItC,iBAAiB,KAAK,UAAU,EAAE;MACzC,IAAIuH,YAAY,GAAG7B,eAAe,CAAC,IAAI,CAACtE,OAAO,CAAC;MAChD,IAAIoG,YAAY,GAAGf,eAAe,CAAC,IAAI,CAACrF,OAAO,CAAC;MAChD,IAAIqG,gBAAgB,GAAGC,aAAa,CAACH,YAAY,CAAC;;MAElD;MACA,IAAII,iBAAiB,GAAG,SAASA,iBAAiBA,CAACzC,OAAO,EAAE;QACxD,OAAOA,OAAO,IAAIpB,QAAQ,CAACC,IAAI,GAAGgD,IAAI,CAACC,GAAG,CAAClD,QAAQ,CAAC4C,eAAe,CAACkB,SAAS,EAAE9D,QAAQ,CAACC,IAAI,CAAC6D,SAAS,CAAC,GAAG1C,OAAO,CAAC0C,SAAS;MAC/H,CAAC;MACD,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAAC3C,OAAO,EAAE;QAC1D,OAAOA,OAAO,IAAIpB,QAAQ,CAACC,IAAI,GAAGgD,IAAI,CAACC,GAAG,CAAClD,QAAQ,CAAC4C,eAAe,CAACoB,UAAU,EAAEhE,QAAQ,CAACC,IAAI,CAAC+D,UAAU,CAAC,GAAG5C,OAAO,CAAC4C,UAAU;MAClI,CAAC;;MAED;MACA,IAAIF,SAAS,GAAGjF,IAAI,CAACI,OAAO,CAACrC,MAAM,CAACmB,QAAQ,KAAK,OAAO,GAAG,CAAC,GAAG8F,iBAAiB,CAACH,YAAY,CAAC;MAC9F,IAAIM,UAAU,GAAGnF,IAAI,CAACI,OAAO,CAACrC,MAAM,CAACmB,QAAQ,KAAK,OAAO,GAAG,CAAC,GAAGgG,kBAAkB,CAACL,YAAY,CAAC;MAEhGvE,UAAU,GAAG;QACTjB,GAAG,EAAE,CAAC,IAAIyF,gBAAgB,CAACzF,GAAG,GAAG4F,SAAS,CAAC;QAC3CxB,KAAK,EAAEjH,IAAI,CAAC2E,QAAQ,CAAC4C,eAAe,CAACY,WAAW,IAAIG,gBAAgB,CAACnF,IAAI,GAAGwF,UAAU,CAAC;QACvFzB,MAAM,EAAElH,IAAI,CAAC2E,QAAQ,CAAC4C,eAAe,CAACS,YAAY,IAAIM,gBAAgB,CAACzF,GAAG,GAAG4F,SAAS,CAAC;QACvFtF,IAAI,EAAE,CAAC,IAAImF,gBAAgB,CAACnF,IAAI,GAAGwF,UAAU;MACjD,CAAC;IACL,CAAC,MAAM;MACH,IAAIpC,eAAe,CAAC,IAAI,CAACtE,OAAO,CAAC,KAAKpB,iBAAiB,EAAE;QACrDiD,UAAU,GAAG;UACTjB,GAAG,EAAE,CAAC;UACNM,IAAI,EAAE,CAAC;UACP8D,KAAK,EAAEpG,iBAAiB,CAACsH,WAAW;UACpCjB,MAAM,EAAErG,iBAAiB,CAACmH;QAC9B,CAAC;MACL,CAAC,MAAM;QACHlE,UAAU,GAAGyE,aAAa,CAAC1H,iBAAiB,CAAC;MACjD;IACJ;IACAiD,UAAU,CAACX,IAAI,IAAIuE,OAAO;IAC1B5D,UAAU,CAACmD,KAAK,IAAIS,OAAO;IAC3B5D,UAAU,CAACjB,GAAG,GAAGiB,UAAU,CAACjB,GAAG,GAAG6E,OAAO;IACzC5D,UAAU,CAACoD,MAAM,GAAGpD,UAAU,CAACoD,MAAM,GAAGQ,OAAO;IAC/C,OAAO5D,UAAU;EACrB,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIxD,MAAM,CAACP,SAAS,CAACiE,YAAY,GAAG,UAAUR,IAAI,EAAErC,SAAS,EAAEyH,IAAI,EAAE;IAC7D,IAAIC,cAAc,GAAG1H,SAAS,CAAC2H,KAAK,CAAC,CAAC;IACtC,IAAIF,IAAI,KAAKrI,SAAS,EAAE;MACpBsI,cAAc,GAAG,IAAI,CAAC1G,QAAQ,CAAChB,SAAS,CAAC2H,KAAK,CAAC,CAAC,EAAEC,gBAAgB,CAAC,IAAI,CAAC5G,QAAQ,CAAChB,SAAS,EAAEyH,IAAI,CAAC,CAAC;IACtG;IAEAC,cAAc,CAAC7C,OAAO,CAAC,UAAU1D,QAAQ,EAAE;MACvC,IAAI0G,UAAU,CAAC1G,QAAQ,CAAC,EAAE;QACtBkB,IAAI,GAAGlB,QAAQ,CAACN,IAAI,CAAC,IAAI,EAAEwB,IAAI,CAAC;MACpC;IACJ,CAAC,CAACf,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,OAAOe,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIlD,MAAM,CAACP,SAAS,CAACkJ,kBAAkB,GAAG,UAAUC,UAAU,EAAEC,SAAS,EAAE;IACnE,IAAIC,KAAK,GAAGL,gBAAgB,CAAC,IAAI,CAAC5G,QAAQ,CAAChB,SAAS,EAAE+H,UAAU,CAAC;IACjE,OAAO,CAAC,CAAC,IAAI,CAAC/G,QAAQ,CAAChB,SAAS,CAAC2H,KAAK,CAAC,CAAC,EAAEM,KAAK,CAAC,CAACC,MAAM,CAAC,UAAU/G,QAAQ,EAAE;MACxE,OAAOA,QAAQ,KAAK6G,SAAS;IACjC,CAAC,CAAC,CAACxD,MAAM;EACb,CAAC;;EAED;EACA;EACA;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACIrF,MAAM,CAACP,SAAS,CAACoB,SAAS,GAAG,CAAC,CAAC;;EAE/B;AACJ;AACA;AACA;AACA;AACA;AACA;EACIb,MAAM,CAACP,SAAS,CAACoB,SAAS,CAACmI,UAAU,GAAG,UAAU9F,IAAI,EAAE;IACpD;IACA;IACA,IAAIE,MAAM,GAAG;MACThB,QAAQ,EAAEc,IAAI,CAACI,OAAO,CAACrC,MAAM,CAACmB;IAClC,CAAC;;IAED;IACA,IAAIS,IAAI,GAAGyE,IAAI,CAAC2B,KAAK,CAAC/F,IAAI,CAACI,OAAO,CAACrC,MAAM,CAAC4B,IAAI,CAAC;IAC/C,IAAIN,GAAG,GAAG+E,IAAI,CAAC2B,KAAK,CAAC/F,IAAI,CAACI,OAAO,CAACrC,MAAM,CAACsB,GAAG,CAAC;;IAE7C;IACA;IACA,IAAI2G,gBAAgB;IACpB,IAAI,IAAI,CAACrH,QAAQ,CAACxB,eAAe,KAAK6I,gBAAgB,GAAGpG,wBAAwB,CAAC,WAAW,CAAC,CAAC,EAAE;MAC7FM,MAAM,CAAC8F,gBAAgB,CAAC,GAAG,cAAc,GAAGrG,IAAI,GAAG,MAAM,GAAGN,GAAG,GAAG,QAAQ;MAC1Ea,MAAM,CAACb,GAAG,GAAG,CAAC;MACda,MAAM,CAACP,IAAI,GAAG,CAAC;IACnB;IACA;IAAA,KACK;MACGO,MAAM,CAACP,IAAI,GAAGA,IAAI;MAClBO,MAAM,CAACb,GAAG,GAAGA,GAAG;IACpB;;IAEJ;IACA;IACA;IACA;IACAf,MAAM,CAACM,MAAM,CAACsB,MAAM,EAAEF,IAAI,CAACE,MAAM,CAAC;IAElCd,QAAQ,CAAC,IAAI,CAACX,OAAO,EAAEyB,MAAM,CAAC;;IAE9B;IACA;IACA,IAAI,CAACzB,OAAO,CAACO,YAAY,CAAC,aAAa,EAAEgB,IAAI,CAAC9C,SAAS,CAAC;;IAExD;IACA,IAAI,IAAI,CAACuI,kBAAkB,CAAC,IAAI,CAAC9H,SAAS,CAACmI,UAAU,EAAE,IAAI,CAACnI,SAAS,CAACsE,KAAK,CAAC,IAAIjC,IAAI,CAACI,OAAO,CAAC6B,KAAK,EAAE;MAChG7C,QAAQ,CAACY,IAAI,CAACvC,YAAY,EAAEuC,IAAI,CAACI,OAAO,CAAC6B,KAAK,CAAC;IACnD;IAEA,OAAOjC,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlD,MAAM,CAACP,SAAS,CAACoB,SAAS,CAACsI,KAAK,GAAG,UAAUjG,IAAI,EAAE;IAC/C,IAAI9C,SAAS,GAAG8C,IAAI,CAAC9C,SAAS;IAC9B,IAAIgJ,aAAa,GAAGhJ,SAAS,CAAC2F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAIsD,cAAc,GAAGjJ,SAAS,CAAC2F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;IAE5C;IACA,IAAIsD,cAAc,EAAE;MAChB,IAAIrI,SAAS,GAAGkC,IAAI,CAACI,OAAO,CAACtC,SAAS;MACtC,IAAIC,MAAM,GAAGqI,mBAAmB,CAACpG,IAAI,CAACI,OAAO,CAACrC,MAAM,CAAC;MAErD,IAAIsI,YAAY,GAAG;QACfC,CAAC,EAAE;UACCC,KAAK,EAAE;YAAElH,GAAG,EAAEvB,SAAS,CAACuB;UAAI,CAAC;UAC7BmH,GAAG,EAAE;YAAEnH,GAAG,EAAEvB,SAAS,CAACuB,GAAG,GAAGvB,SAAS,CAACyF,MAAM,GAAGxF,MAAM,CAACwF;UAAO;QACjE,CAAC;QACDkD,CAAC,EAAE;UACCF,KAAK,EAAE;YAAE5G,IAAI,EAAE7B,SAAS,CAAC6B;UAAK,CAAC;UAC/B6G,GAAG,EAAE;YAAE7G,IAAI,EAAE7B,SAAS,CAAC6B,IAAI,GAAG7B,SAAS,CAAC0F,KAAK,GAAGzF,MAAM,CAACyF;UAAM;QACjE;MACJ,CAAC;MAED,IAAIkD,IAAI,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC3H,OAAO,CAACmH,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;MAEtElG,IAAI,CAACI,OAAO,CAACrC,MAAM,GAAGO,MAAM,CAACM,MAAM,CAACb,MAAM,EAAEsI,YAAY,CAACK,IAAI,CAAC,CAACP,cAAc,CAAC,CAAC;IACnF;IAEA,OAAOnG,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlD,MAAM,CAACP,SAAS,CAACoB,SAAS,CAACgJ,eAAe,GAAG,UAAU3G,IAAI,EAAE;IACzD,IAAI4G,KAAK,GAAG,IAAI,CAACjI,QAAQ,CAACpB,oBAAoB;IAC9C,IAAIQ,MAAM,GAAGqI,mBAAmB,CAACpG,IAAI,CAACI,OAAO,CAACrC,MAAM,CAAC;IAErD,IAAI8I,KAAK,GAAG;MACRlH,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QAClB,IAAIA,IAAI,GAAG5B,MAAM,CAAC4B,IAAI;QACtB,IAAI5B,MAAM,CAAC4B,IAAI,GAAGK,IAAI,CAACM,UAAU,CAACX,IAAI,EAAE;UACpCA,IAAI,GAAGyE,IAAI,CAACC,GAAG,CAACtG,MAAM,CAAC4B,IAAI,EAAEK,IAAI,CAACM,UAAU,CAACX,IAAI,CAAC;QACtD;QACA,OAAO;UAAEA,IAAI,EAAEA;QAAK,CAAC;MACzB,CAAC;MACD8D,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;QACpB,IAAI9D,IAAI,GAAG5B,MAAM,CAAC4B,IAAI;QACtB,IAAI5B,MAAM,CAAC0F,KAAK,GAAGzD,IAAI,CAACM,UAAU,CAACmD,KAAK,EAAE;UACtC9D,IAAI,GAAGyE,IAAI,CAAC0C,GAAG,CAAC/I,MAAM,CAAC4B,IAAI,EAAEK,IAAI,CAACM,UAAU,CAACmD,KAAK,GAAG1F,MAAM,CAACyF,KAAK,CAAC;QACtE;QACA,OAAO;UAAE7D,IAAI,EAAEA;QAAK,CAAC;MACzB,CAAC;MACDN,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;QAChB,IAAIA,GAAG,GAAGtB,MAAM,CAACsB,GAAG;QACpB,IAAItB,MAAM,CAACsB,GAAG,GAAGW,IAAI,CAACM,UAAU,CAACjB,GAAG,EAAE;UAClCA,GAAG,GAAG+E,IAAI,CAACC,GAAG,CAACtG,MAAM,CAACsB,GAAG,EAAEW,IAAI,CAACM,UAAU,CAACjB,GAAG,CAAC;QACnD;QACA,OAAO;UAAEA,GAAG,EAAEA;QAAI,CAAC;MACvB,CAAC;MACDqE,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;QACtB,IAAIrE,GAAG,GAAGtB,MAAM,CAACsB,GAAG;QACpB,IAAItB,MAAM,CAAC2F,MAAM,GAAG1D,IAAI,CAACM,UAAU,CAACoD,MAAM,EAAE;UACxCrE,GAAG,GAAG+E,IAAI,CAAC0C,GAAG,CAAC/I,MAAM,CAACsB,GAAG,EAAEW,IAAI,CAACM,UAAU,CAACoD,MAAM,GAAG3F,MAAM,CAACwF,MAAM,CAAC;QACtE;QACA,OAAO;UAAElE,GAAG,EAAEA;QAAI,CAAC;MACvB;IACJ,CAAC;IAEDuH,KAAK,CAACpE,OAAO,CAAC,UAAUuE,SAAS,EAAE;MAC/B/G,IAAI,CAACI,OAAO,CAACrC,MAAM,GAAGO,MAAM,CAACM,MAAM,CAACb,MAAM,EAAE8I,KAAK,CAACE,SAAS,CAAC,CAAC,CAAC,CAAC;IACnE,CAAC,CAAC;IAEF,OAAO/G,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlD,MAAM,CAACP,SAAS,CAACoB,SAAS,CAACqJ,YAAY,GAAG,UAAUhH,IAAI,EAAE;IACtD,IAAIjC,MAAM,GAAGqI,mBAAmB,CAACpG,IAAI,CAACI,OAAO,CAACrC,MAAM,CAAC;IACrD,IAAID,SAAS,GAAGkC,IAAI,CAACI,OAAO,CAACtC,SAAS;IACtC,IAAImJ,CAAC,GAAG7C,IAAI,CAAC8C,KAAK;IAElB,IAAInJ,MAAM,CAAC0F,KAAK,GAAGwD,CAAC,CAACnJ,SAAS,CAAC6B,IAAI,CAAC,EAAE;MAClCK,IAAI,CAACI,OAAO,CAACrC,MAAM,CAAC4B,IAAI,GAAGsH,CAAC,CAACnJ,SAAS,CAAC6B,IAAI,CAAC,GAAG5B,MAAM,CAACyF,KAAK;IAC/D;IACA,IAAIzF,MAAM,CAAC4B,IAAI,GAAGsH,CAAC,CAACnJ,SAAS,CAAC2F,KAAK,CAAC,EAAE;MAClCzD,IAAI,CAACI,OAAO,CAACrC,MAAM,CAAC4B,IAAI,GAAGsH,CAAC,CAACnJ,SAAS,CAAC2F,KAAK,CAAC;IACjD;IACA,IAAI1F,MAAM,CAAC2F,MAAM,GAAGuD,CAAC,CAACnJ,SAAS,CAACuB,GAAG,CAAC,EAAE;MAClCW,IAAI,CAACI,OAAO,CAACrC,MAAM,CAACsB,GAAG,GAAG4H,CAAC,CAACnJ,SAAS,CAACuB,GAAG,CAAC,GAAGtB,MAAM,CAACwF,MAAM;IAC9D;IACA,IAAIxF,MAAM,CAACsB,GAAG,GAAG4H,CAAC,CAACnJ,SAAS,CAAC4F,MAAM,CAAC,EAAE;MAClC1D,IAAI,CAACI,OAAO,CAACrC,MAAM,CAACsB,GAAG,GAAG4H,CAAC,CAACnJ,SAAS,CAAC4F,MAAM,CAAC;IACjD;IAEA,OAAO1D,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlD,MAAM,CAACP,SAAS,CAACoB,SAAS,CAACwJ,IAAI,GAAG,UAAUnH,IAAI,EAAE;IAC9C;IACA;IACA,IAAI,CAAC,IAAI,CAACyF,kBAAkB,CAAC,IAAI,CAAC9H,SAAS,CAACwJ,IAAI,EAAE,IAAI,CAACxJ,SAAS,CAACgJ,eAAe,CAAC,EAAE;MAC/EvE,OAAO,CAACC,IAAI,CAAC,qHAAqH,CAAC;MACnI,OAAOrC,IAAI;IACf;IAEA,IAAIA,IAAI,CAACoH,OAAO,IAAIpH,IAAI,CAAC9C,SAAS,KAAK8C,IAAI,CAACG,kBAAkB,EAAE;MAC5D;MACA,OAAOH,IAAI;IACf;IAEA,IAAI9C,SAAS,GAAG8C,IAAI,CAAC9C,SAAS,CAAC2F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAIwE,iBAAiB,GAAGC,oBAAoB,CAACpK,SAAS,CAAC;IACvD,IAAIqK,SAAS,GAAGvH,IAAI,CAAC9C,SAAS,CAAC2F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;IAElD,IAAI2E,SAAS,GAAG,EAAE;IAClB,IAAI,IAAI,CAAC7I,QAAQ,CAACnB,YAAY,KAAK,MAAM,EAAE;MACvCgK,SAAS,GAAG,CAACtK,SAAS,EAAEmK,iBAAiB,CAAC;IAC9C,CAAC,MAAM;MACHG,SAAS,GAAG,IAAI,CAAC7I,QAAQ,CAACnB,YAAY;IAC1C;IAEAgK,SAAS,CAAChF,OAAO,CAAC,UAAUiF,IAAI,EAAE7B,KAAK,EAAE;MACrC,IAAI1I,SAAS,KAAKuK,IAAI,IAAID,SAAS,CAACrF,MAAM,KAAKyD,KAAK,GAAG,CAAC,EAAE;QACtD;MACJ;MAEA1I,SAAS,GAAG8C,IAAI,CAAC9C,SAAS,CAAC2F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxCwE,iBAAiB,GAAGC,oBAAoB,CAACpK,SAAS,CAAC;MAEnD,IAAIgG,aAAa,GAAGkD,mBAAmB,CAACpG,IAAI,CAACI,OAAO,CAACrC,MAAM,CAAC;;MAE5D;MACA;MACA,IAAI2J,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC3I,OAAO,CAAC7B,SAAS,CAAC,KAAK,CAAC,CAAC;;MAErD;MACA,IAAIwK,CAAC,IAAItD,IAAI,CAAC8C,KAAK,CAAClH,IAAI,CAACI,OAAO,CAACtC,SAAS,CAACZ,SAAS,CAAC,CAAC,GAAGkH,IAAI,CAAC8C,KAAK,CAAChE,aAAa,CAACmE,iBAAiB,CAAC,CAAC,IAAI,CAACK,CAAC,IAAItD,IAAI,CAAC8C,KAAK,CAAClH,IAAI,CAACI,OAAO,CAACtC,SAAS,CAACZ,SAAS,CAAC,CAAC,GAAGkH,IAAI,CAAC8C,KAAK,CAAChE,aAAa,CAACmE,iBAAiB,CAAC,CAAC,EAAE;QACzM;QACArH,IAAI,CAACoH,OAAO,GAAG,IAAI;QACnBpH,IAAI,CAAC9C,SAAS,GAAGsK,SAAS,CAAC5B,KAAK,GAAG,CAAC,CAAC;QACrC,IAAI2B,SAAS,EAAE;UACXvH,IAAI,CAAC9C,SAAS,IAAI,GAAG,GAAGqK,SAAS;QACrC;QACAvH,IAAI,CAACI,OAAO,CAACrC,MAAM,GAAG,IAAI,CAACsC,WAAW,CAAC,IAAI,CAAC5B,OAAO,EAAE,IAAI,CAACR,UAAU,EAAE+B,IAAI,CAAC9C,SAAS,CAAC,CAACa,MAAM;QAE5FiC,IAAI,GAAG,IAAI,CAACQ,YAAY,CAACR,IAAI,EAAE,IAAI,CAACrB,QAAQ,CAAChB,SAAS,EAAE,IAAI,CAACgK,KAAK,CAAC;MACvE;IACJ,CAAC,CAAC1I,IAAI,CAAC,IAAI,CAAC,CAAC;IACb,OAAOe,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIlD,MAAM,CAACP,SAAS,CAACoB,SAAS,CAACP,MAAM,GAAG,UAAU4C,IAAI,EAAE;IAChD,IAAI5C,MAAM,GAAG,IAAI,CAACuB,QAAQ,CAACvB,MAAM;IACjC,IAAIW,MAAM,GAAGiC,IAAI,CAACI,OAAO,CAACrC,MAAM;IAEhC,IAAIiC,IAAI,CAAC9C,SAAS,CAAC6B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;MACvChB,MAAM,CAACsB,GAAG,IAAIjC,MAAM;IACxB,CAAC,MAAM,IAAI4C,IAAI,CAAC9C,SAAS,CAAC6B,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;MAC/ChB,MAAM,CAACsB,GAAG,IAAIjC,MAAM;IACxB,CAAC,MAAM,IAAI4C,IAAI,CAAC9C,SAAS,CAAC6B,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7ChB,MAAM,CAAC4B,IAAI,IAAIvC,MAAM;IACzB,CAAC,MAAM,IAAI4C,IAAI,CAAC9C,SAAS,CAAC6B,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAChDhB,MAAM,CAAC4B,IAAI,IAAIvC,MAAM;IACzB;IACA,OAAO4C,IAAI;EACf,CAAC;;EAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIlD,MAAM,CAACP,SAAS,CAACoB,SAAS,CAACsE,KAAK,GAAG,UAAUjC,IAAI,EAAE;IAC/C,IAAIiC,KAAK,GAAG,IAAI,CAACtD,QAAQ,CAAClB,YAAY;IACtC,IAAIC,WAAW,GAAG,IAAI,CAACiB,QAAQ,CAACjB,WAAW;;IAE3C;IACA,IAAI,OAAOuE,KAAK,KAAK,QAAQ,EAAE;MAC3BA,KAAK,GAAG,IAAI,CAACxD,OAAO,CAACmJ,aAAa,CAAC3F,KAAK,CAAC;IAC7C;;IAEA;IACA,IAAI,CAACA,KAAK,EAAE;MACR,OAAOjC,IAAI;IACf;;IAEA;IACA,IAAI,CAAC,IAAI,CAACvB,OAAO,CAACoJ,QAAQ,CAAC5F,KAAK,CAAC,EAAE;MAC/BG,OAAO,CAACC,IAAI,CAAC,8DAA8D,CAAC;MAC5E,OAAOrC,IAAI;IACf;;IAEA;IACA,IAAI,CAAC,IAAI,CAACyF,kBAAkB,CAAC,IAAI,CAAC9H,SAAS,CAACsE,KAAK,EAAE,IAAI,CAACtE,SAAS,CAACqJ,YAAY,CAAC,EAAE;MAC7E5E,OAAO,CAACC,IAAI,CAAC,oHAAoH,CAAC;MAClI,OAAOrC,IAAI;IACf;IAEA,IAAI8H,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI5K,SAAS,GAAG8C,IAAI,CAAC9C,SAAS,CAAC2F,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAI9E,MAAM,GAAGqI,mBAAmB,CAACpG,IAAI,CAACI,OAAO,CAACrC,MAAM,CAAC;IACrD,IAAID,SAAS,GAAGkC,IAAI,CAACI,OAAO,CAACtC,SAAS;IACtC,IAAIiK,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAChJ,OAAO,CAAC7B,SAAS,CAAC,KAAK,CAAC,CAAC;IAE5D,IAAI8K,GAAG,GAAGD,UAAU,GAAG,QAAQ,GAAG,OAAO;IACzC,IAAIE,IAAI,GAAGF,UAAU,GAAG,KAAK,GAAG,MAAM;IACtC,IAAIG,SAAS,GAAGH,UAAU,GAAG,YAAY,GAAG,YAAY;IACxD,IAAII,OAAO,GAAGJ,UAAU,GAAG,MAAM,GAAG,KAAK;IACzC,IAAIK,MAAM,GAAGL,UAAU,GAAG,QAAQ,GAAG,OAAO;IAC5C,IAAIM,SAAS,GAAG/E,aAAa,CAACrB,KAAK,CAAC,CAAC+F,GAAG,CAAC;;IAEzC;IACA;IACA;;IAEA;IACA,IAAIlK,SAAS,CAACsK,MAAM,CAAC,GAAGC,SAAS,GAAGtK,MAAM,CAACkK,IAAI,CAAC,EAAE;MAC9CjI,IAAI,CAACI,OAAO,CAACrC,MAAM,CAACkK,IAAI,CAAC,IAAIlK,MAAM,CAACkK,IAAI,CAAC,IAAInK,SAAS,CAACsK,MAAM,CAAC,GAAGC,SAAS,CAAC;IAC/E;IACA;IACA,IAAIvK,SAAS,CAACmK,IAAI,CAAC,GAAGI,SAAS,GAAGtK,MAAM,CAACqK,MAAM,CAAC,EAAE;MAC9CpI,IAAI,CAACI,OAAO,CAACrC,MAAM,CAACkK,IAAI,CAAC,IAAInK,SAAS,CAACmK,IAAI,CAAC,GAAGI,SAAS,GAAGtK,MAAM,CAACqK,MAAM,CAAC;IAC7E;;IAEA;IACA,IAAIE,MAAM,GAAGxK,SAAS,CAACmK,IAAI,CAAC,IAAIvK,WAAW,IAAII,SAAS,CAACkK,GAAG,CAAC,GAAG,CAAC,GAAGK,SAAS,GAAG,CAAC,CAAC;IAElF,IAAIE,SAAS,GAAGD,MAAM,GAAGvK,MAAM,CAACkK,IAAI,CAAC;;IAErC;IACAM,SAAS,GAAGnE,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC0C,GAAG,CAAC/I,MAAM,CAACiK,GAAG,CAAC,GAAGK,SAAS,GAAG,CAAC,EAAEE,SAAS,CAAC,EAAE,CAAC,CAAC;IACzET,UAAU,CAACG,IAAI,CAAC,GAAGM,SAAS;IAC5BT,UAAU,CAACK,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;;IAE1BnI,IAAI,CAACI,OAAO,CAAC6B,KAAK,GAAG6F,UAAU;IAC/B9H,IAAI,CAACvC,YAAY,GAAGwE,KAAK;IAEzB,OAAOjC,IAAI;EACf,CAAC;;EAED;EACA;EACA;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASsD,aAAaA,CAACf,OAAO,EAAE;IAC5B;IACA,IAAIiG,QAAQ,GAAGjG,OAAO,CAAC7C,KAAK,CAAC+I,OAAO;MAChCC,WAAW,GAAGnG,OAAO,CAAC7C,KAAK,CAACiJ,UAAU;IAC1CpG,OAAO,CAAC7C,KAAK,CAAC+I,OAAO,GAAG,OAAO;IAAClG,OAAO,CAAC7C,KAAK,CAACiJ,UAAU,GAAG,QAAQ;IACnE,IAAIC,uBAAuB,GAAGrG,OAAO,CAACmC,WAAW;;IAEjD;IACA,IAAIxE,MAAM,GAAG1D,IAAI,CAACqM,gBAAgB,CAACtG,OAAO,CAAC;IAC3C,IAAIkE,CAAC,GAAGqC,UAAU,CAAC5I,MAAM,CAAC6I,SAAS,CAAC,GAAGD,UAAU,CAAC5I,MAAM,CAAC8I,YAAY,CAAC;IACtE,IAAI1C,CAAC,GAAGwC,UAAU,CAAC5I,MAAM,CAAC+I,UAAU,CAAC,GAAGH,UAAU,CAAC5I,MAAM,CAACgJ,WAAW,CAAC;IACtE,IAAIC,MAAM,GAAG;MAAE3F,KAAK,EAAEjB,OAAO,CAACmC,WAAW,GAAG4B,CAAC;MAAE/C,MAAM,EAAEhB,OAAO,CAACgC,YAAY,GAAGkC;IAAE,CAAC;;IAEjF;IACAlE,OAAO,CAAC7C,KAAK,CAAC+I,OAAO,GAAGD,QAAQ;IAACjG,OAAO,CAAC7C,KAAK,CAACiJ,UAAU,GAAGD,WAAW;IACvE,OAAOS,MAAM;EACjB;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAS7B,oBAAoBA,CAACpK,SAAS,EAAE;IACrC,IAAIkM,IAAI,GAAG;MAAEzJ,IAAI,EAAE,OAAO;MAAE8D,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,KAAK;MAAErE,GAAG,EAAE;IAAS,CAAC;IACzE,OAAOnC,SAAS,CAACmM,OAAO,CAAC,wBAAwB,EAAE,UAAUC,OAAO,EAAE;MAClE,OAAOF,IAAI,CAACE,OAAO,CAAC;IACxB,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASlD,mBAAmBA,CAAClD,aAAa,EAAE;IACxC,IAAI9C,OAAO,GAAG9B,MAAM,CAACM,MAAM,CAAC,CAAC,CAAC,EAAEsE,aAAa,CAAC;IAC9C9C,OAAO,CAACqD,KAAK,GAAGrD,OAAO,CAACT,IAAI,GAAGS,OAAO,CAACoD,KAAK;IAC5CpD,OAAO,CAACsD,MAAM,GAAGtD,OAAO,CAACf,GAAG,GAAGe,OAAO,CAACmD,MAAM;IAC7C,OAAOnD,OAAO;EAClB;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASmF,gBAAgBA,CAACgE,GAAG,EAAEC,SAAS,EAAE;IACtC,IAAIC,CAAC,GAAG,CAAC;MACLC,GAAG;IACP,KAAKA,GAAG,IAAIH,GAAG,EAAE;MACb,IAAIA,GAAG,CAACG,GAAG,CAAC,KAAKF,SAAS,EAAE;QACxB,OAAOC,CAAC;MACZ;MACAA,CAAC,EAAE;IACP;IACA,OAAO,IAAI;EACf;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASE,wBAAwBA,CAACpH,OAAO,EAAEqH,QAAQ,EAAE;IACjD;IACA,IAAIC,GAAG,GAAGrN,IAAI,CAACqM,gBAAgB,CAACtG,OAAO,EAAE,IAAI,CAAC;IAC9C,OAAOsH,GAAG,CAACD,QAAQ,CAAC;EACxB;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAS7G,eAAeA,CAACR,OAAO,EAAE;IAC9B;IACA,IAAIqC,YAAY,GAAGrC,OAAO,CAACqC,YAAY;IACvC,OAAOA,YAAY,KAAKpI,IAAI,CAAC2E,QAAQ,CAACC,IAAI,IAAI,CAACwD,YAAY,GAAGpI,IAAI,CAAC2E,QAAQ,CAAC4C,eAAe,GAAGa,YAAY;EAC9G;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASd,eAAeA,CAACvB,OAAO,EAAE;IAC9B,IAAIrB,MAAM,GAAGqB,OAAO,CAACuH,UAAU;IAE/B,IAAI,CAAC5I,MAAM,EAAE;MACT,OAAOqB,OAAO;IAClB;IAEA,IAAIrB,MAAM,KAAK1E,IAAI,CAAC2E,QAAQ,EAAE;MAC1B;MACA;MACA,IAAI3E,IAAI,CAAC2E,QAAQ,CAACC,IAAI,CAAC6D,SAAS,IAAIzI,IAAI,CAAC2E,QAAQ,CAACC,IAAI,CAAC+D,UAAU,EAAE;QAC/D,OAAO3I,IAAI,CAAC2E,QAAQ,CAACC,IAAI;MAC7B,CAAC,MAAM;QACH,OAAO5E,IAAI,CAAC2E,QAAQ,CAAC4C,eAAe;MACxC;IACJ;;IAEA;IACA,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAChF,OAAO,CAAC4K,wBAAwB,CAACzI,MAAM,EAAE,UAAU,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAACnC,OAAO,CAAC4K,wBAAwB,CAACzI,MAAM,EAAE,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAACnC,OAAO,CAAC4K,wBAAwB,CAACzI,MAAM,EAAE,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3P;MACA;MACA;MACA,OAAOA,MAAM;IACjB;IACA,OAAO4C,eAAe,CAACvB,OAAO,CAACuH,UAAU,CAAC;EAC9C;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS7G,OAAOA,CAACV,OAAO,EAAE;IACtB,IAAIA,OAAO,KAAK/F,IAAI,CAAC2E,QAAQ,CAACC,IAAI,EAAE;MAChC,OAAO,KAAK;IAChB;IACA,IAAIuI,wBAAwB,CAACpH,OAAO,EAAE,UAAU,CAAC,KAAK,OAAO,EAAE;MAC3D,OAAO,IAAI;IACf;IACA,OAAOA,OAAO,CAACuH,UAAU,GAAG7G,OAAO,CAACV,OAAO,CAACuH,UAAU,CAAC,GAAGvH,OAAO;EACrE;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASnD,QAAQA,CAACmD,OAAO,EAAErC,MAAM,EAAE;IAC/B,SAAS6J,UAAUA,CAACC,CAAC,EAAE;MACnB,OAAOA,CAAC,KAAK,EAAE,IAAI,CAACC,KAAK,CAACnB,UAAU,CAACkB,CAAC,CAAC,CAAC,IAAIE,QAAQ,CAACF,CAAC,CAAC;IAC3D;IACA1L,MAAM,CAAC6L,IAAI,CAACjK,MAAM,CAAC,CAACsC,OAAO,CAAC,UAAU4H,IAAI,EAAE;MACxC,IAAIC,IAAI,GAAG,EAAE;MACb;MACA,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAACtL,OAAO,CAACqL,IAAI,CAAC,KAAK,CAAC,CAAC,IAAIL,UAAU,CAAC7J,MAAM,CAACkK,IAAI,CAAC,CAAC,EAAE;QACxGC,IAAI,GAAG,IAAI;MACf;MACA9H,OAAO,CAAC7C,KAAK,CAAC0K,IAAI,CAAC,GAAGlK,MAAM,CAACkK,IAAI,CAAC,GAAGC,IAAI;IAC7C,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAS7E,UAAUA,CAAC8E,eAAe,EAAE;IACjC,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,OAAOD,eAAe,IAAIC,OAAO,CAAChM,QAAQ,CAACC,IAAI,CAAC8L,eAAe,CAAC,KAAK,mBAAmB;EAC5F;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASvF,aAAaA,CAACxC,OAAO,EAAE;IAC5B,IAAIiI,WAAW,GAAG;MACdhH,KAAK,EAAEjB,OAAO,CAACmC,WAAW;MAC1BnB,MAAM,EAAEhB,OAAO,CAACgC,YAAY;MAC5B5E,IAAI,EAAE4C,OAAO,CAACkI,UAAU;MACxBpL,GAAG,EAAEkD,OAAO,CAACmI;IACjB,CAAC;IAEDF,WAAW,CAAC/G,KAAK,GAAG+G,WAAW,CAAC7K,IAAI,GAAG6K,WAAW,CAAChH,KAAK;IACxDgH,WAAW,CAAC9G,MAAM,GAAG8G,WAAW,CAACnL,GAAG,GAAGmL,WAAW,CAACjH,MAAM;;IAEzD;IACA,OAAOiH,WAAW;EACtB;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASG,qBAAqBA,CAACpI,OAAO,EAAE;IACpC,IAAIqI,IAAI,GAAGrI,OAAO,CAACoI,qBAAqB,CAAC,CAAC;;IAE1C;IACA,IAAIE,IAAI,GAAGC,SAAS,CAACC,SAAS,CAAChM,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;;IAEpD;IACA,IAAIiM,OAAO,GAAGH,IAAI,IAAItI,OAAO,CAACxB,OAAO,KAAK,MAAM,GAAG,CAACwB,OAAO,CAAC0C,SAAS,GAAG2F,IAAI,CAACvL,GAAG;IAEhF,OAAO;MACHM,IAAI,EAAEiL,IAAI,CAACjL,IAAI;MACfN,GAAG,EAAE2L,OAAO;MACZvH,KAAK,EAAEmH,IAAI,CAACnH,KAAK;MACjBC,MAAM,EAAEkH,IAAI,CAAClH,MAAM;MACnBF,KAAK,EAAEoH,IAAI,CAACnH,KAAK,GAAGmH,IAAI,CAACjL,IAAI;MAC7B4D,MAAM,EAAEqH,IAAI,CAAClH,MAAM,GAAGsH;IAC1B,CAAC;EACL;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAAS5H,mCAAmCA,CAACb,OAAO,EAAErB,MAAM,EAAE+J,KAAK,EAAE;IACjE,IAAIT,WAAW,GAAGG,qBAAqB,CAACpI,OAAO,CAAC;IAChD,IAAI2I,UAAU,GAAGP,qBAAqB,CAACzJ,MAAM,CAAC;IAE9C,IAAI+J,KAAK,EAAE;MACP,IAAIpG,YAAY,GAAGf,eAAe,CAAC5C,MAAM,CAAC;MAC1CgK,UAAU,CAAC7L,GAAG,IAAIwF,YAAY,CAACI,SAAS;MACxCiG,UAAU,CAACxH,MAAM,IAAImB,YAAY,CAACI,SAAS;MAC3CiG,UAAU,CAACvL,IAAI,IAAIkF,YAAY,CAACM,UAAU;MAC1C+F,UAAU,CAACzH,KAAK,IAAIoB,YAAY,CAACM,UAAU;IAC/C;IAEA,IAAIyF,IAAI,GAAG;MACPvL,GAAG,EAAEmL,WAAW,CAACnL,GAAG,GAAG6L,UAAU,CAAC7L,GAAG;MACrCM,IAAI,EAAE6K,WAAW,CAAC7K,IAAI,GAAGuL,UAAU,CAACvL,IAAI;MACxC+D,MAAM,EAAE8G,WAAW,CAACnL,GAAG,GAAG6L,UAAU,CAAC7L,GAAG,GAAGmL,WAAW,CAACjH,MAAM;MAC7DE,KAAK,EAAE+G,WAAW,CAAC7K,IAAI,GAAGuL,UAAU,CAACvL,IAAI,GAAG6K,WAAW,CAAChH,KAAK;MAC7DA,KAAK,EAAEgH,WAAW,CAAChH,KAAK;MACxBD,MAAM,EAAEiH,WAAW,CAACjH;IACxB,CAAC;IACD,OAAOqH,IAAI;EACf;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAAShL,wBAAwBA,CAACgK,QAAQ,EAAE;IACxC,IAAIuB,QAAQ,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC;IAE/C,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,QAAQ,CAAChJ,MAAM,EAAEsH,CAAC,EAAE,EAAE;MACtC,IAAI2B,OAAO,GAAGD,QAAQ,CAAC1B,CAAC,CAAC,GAAG0B,QAAQ,CAAC1B,CAAC,CAAC,GAAGG,QAAQ,CAACyB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG1B,QAAQ,CAACtE,KAAK,CAAC,CAAC,CAAC,GAAGsE,QAAQ;MACzG,IAAI,OAAOpN,IAAI,CAAC2E,QAAQ,CAACC,IAAI,CAAC1B,KAAK,CAAC0L,OAAO,CAAC,KAAK,WAAW,EAAE;QAC1D,OAAOA,OAAO;MAClB;IACJ;IACA,OAAO,IAAI;EACf;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAI,CAAC9M,MAAM,CAACM,MAAM,EAAE;IAChBN,MAAM,CAACiN,cAAc,CAACjN,MAAM,EAAE,QAAQ,EAAE;MACpCkN,UAAU,EAAE,KAAK;MACjBC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,SAASA,KAAKA,CAAC9H,MAAM,EAAE;QAC1B,IAAIA,MAAM,KAAK9G,SAAS,IAAI8G,MAAM,KAAK,IAAI,EAAE;UACzC,MAAM,IAAI+H,SAAS,CAAC,yCAAyC,CAAC;QAClE;QAEA,IAAIC,EAAE,GAAGvN,MAAM,CAACuF,MAAM,CAAC;QACvB,KAAK,IAAI4F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,SAAS,CAAC3J,MAAM,EAAEsH,CAAC,EAAE,EAAE;UACvC,IAAIsC,UAAU,GAAGD,SAAS,CAACrC,CAAC,CAAC;UAC7B,IAAIsC,UAAU,KAAKhP,SAAS,IAAIgP,UAAU,KAAK,IAAI,EAAE;YACjD;UACJ;UACAA,UAAU,GAAGzN,MAAM,CAACyN,UAAU,CAAC;UAE/B,IAAIC,SAAS,GAAG1N,MAAM,CAAC6L,IAAI,CAAC4B,UAAU,CAAC;UACvC,KAAK,IAAIE,SAAS,GAAG,CAAC,EAAEjE,GAAG,GAAGgE,SAAS,CAAC7J,MAAM,EAAE8J,SAAS,GAAGjE,GAAG,EAAEiE,SAAS,EAAE,EAAE;YAC1E,IAAIC,OAAO,GAAGF,SAAS,CAACC,SAAS,CAAC;YAClC,IAAIE,IAAI,GAAG7N,MAAM,CAAC8N,wBAAwB,CAACL,UAAU,EAAEG,OAAO,CAAC;YAC/D,IAAIC,IAAI,KAAKpP,SAAS,IAAIoP,IAAI,CAACX,UAAU,EAAE;cACvCK,EAAE,CAACK,OAAO,CAAC,GAAGH,UAAU,CAACG,OAAO,CAAC;YACrC;UACJ;QACJ;QACA,OAAOL,EAAE;MACb;IACJ,CAAC,CAAC;EACN;EAEA,OAAO/O,MAAM;AACjB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}