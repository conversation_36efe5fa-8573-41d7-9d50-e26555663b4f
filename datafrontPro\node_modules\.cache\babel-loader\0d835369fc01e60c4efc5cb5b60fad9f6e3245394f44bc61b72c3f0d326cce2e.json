{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport * as eventUtil from './event.js';\nvar GestureMgr = function () {\n  function GestureMgr() {\n    this._track = [];\n  }\n  GestureMgr.prototype.recognize = function (event, target, root) {\n    this._doTrack(event, target, root);\n    return this._recognize(event);\n  };\n  GestureMgr.prototype.clear = function () {\n    this._track.length = 0;\n    return this;\n  };\n  GestureMgr.prototype._doTrack = function (event, target, root) {\n    var touches = event.touches;\n    if (!touches) {\n      return;\n    }\n    var trackItem = {\n      points: [],\n      touches: [],\n      target: target,\n      event: event\n    };\n    for (var i = 0, len = touches.length; i < len; i++) {\n      var touch = touches[i];\n      var pos = eventUtil.clientToLocal(root, touch, {});\n      trackItem.points.push([pos.zrX, pos.zrY]);\n      trackItem.touches.push(touch);\n    }\n    this._track.push(trackItem);\n  };\n  GestureMgr.prototype._recognize = function (event) {\n    for (var eventName in recognizers) {\n      if (recognizers.hasOwnProperty(eventName)) {\n        var gestureInfo = recognizers[eventName](this._track, event);\n        if (gestureInfo) {\n          return gestureInfo;\n        }\n      }\n    }\n  };\n  return GestureMgr;\n}();\nexport { GestureMgr };\nfunction dist(pointPair) {\n  var dx = pointPair[1][0] - pointPair[0][0];\n  var dy = pointPair[1][1] - pointPair[0][1];\n  return Math.sqrt(dx * dx + dy * dy);\n}\nfunction center(pointPair) {\n  return [(pointPair[0][0] + pointPair[1][0]) / 2, (pointPair[0][1] + pointPair[1][1]) / 2];\n}\nvar recognizers = {\n  pinch: function (tracks, event) {\n    var trackLen = tracks.length;\n    if (!trackLen) {\n      return;\n    }\n    var pinchEnd = (tracks[trackLen - 1] || {}).points;\n    var pinchPre = (tracks[trackLen - 2] || {}).points || pinchEnd;\n    if (pinchPre && pinchPre.length > 1 && pinchEnd && pinchEnd.length > 1) {\n      var pinchScale = dist(pinchEnd) / dist(pinchPre);\n      !isFinite(pinchScale) && (pinchScale = 1);\n      event.pinchScale = pinchScale;\n      var pinchCenter = center(pinchEnd);\n      event.pinchX = pinchCenter[0];\n      event.pinchY = pinchCenter[1];\n      return {\n        type: 'pinch',\n        target: tracks[0].target,\n        event: event\n      };\n    }\n  }\n};", "map": {"version": 3, "names": ["eventUtil", "GestureMgr", "_track", "prototype", "recognize", "event", "target", "root", "_doTrack", "_recognize", "clear", "length", "touches", "trackItem", "points", "i", "len", "touch", "pos", "clientToLocal", "push", "zrX", "zrY", "eventName", "recognizers", "hasOwnProperty", "gestureInfo", "dist", "pointPair", "dx", "dy", "Math", "sqrt", "center", "pinch", "tracks", "trackLen", "pinchEnd", "pinchPre", "pinchScale", "isFinite", "pinchCenter", "pinchX", "pinchY", "type"], "sources": ["E:/indicator-qa-service/datafront/node_modules/zrender/lib/core/GestureMgr.js"], "sourcesContent": ["import * as eventUtil from './event.js';\nvar GestureMgr = (function () {\n    function GestureMgr() {\n        this._track = [];\n    }\n    GestureMgr.prototype.recognize = function (event, target, root) {\n        this._doTrack(event, target, root);\n        return this._recognize(event);\n    };\n    GestureMgr.prototype.clear = function () {\n        this._track.length = 0;\n        return this;\n    };\n    GestureMgr.prototype._doTrack = function (event, target, root) {\n        var touches = event.touches;\n        if (!touches) {\n            return;\n        }\n        var trackItem = {\n            points: [],\n            touches: [],\n            target: target,\n            event: event\n        };\n        for (var i = 0, len = touches.length; i < len; i++) {\n            var touch = touches[i];\n            var pos = eventUtil.clientToLocal(root, touch, {});\n            trackItem.points.push([pos.zrX, pos.zrY]);\n            trackItem.touches.push(touch);\n        }\n        this._track.push(trackItem);\n    };\n    GestureMgr.prototype._recognize = function (event) {\n        for (var eventName in recognizers) {\n            if (recognizers.hasOwnProperty(eventName)) {\n                var gestureInfo = recognizers[eventName](this._track, event);\n                if (gestureInfo) {\n                    return gestureInfo;\n                }\n            }\n        }\n    };\n    return GestureMgr;\n}());\nexport { GestureMgr };\nfunction dist(pointPair) {\n    var dx = pointPair[1][0] - pointPair[0][0];\n    var dy = pointPair[1][1] - pointPair[0][1];\n    return Math.sqrt(dx * dx + dy * dy);\n}\nfunction center(pointPair) {\n    return [\n        (pointPair[0][0] + pointPair[1][0]) / 2,\n        (pointPair[0][1] + pointPair[1][1]) / 2\n    ];\n}\nvar recognizers = {\n    pinch: function (tracks, event) {\n        var trackLen = tracks.length;\n        if (!trackLen) {\n            return;\n        }\n        var pinchEnd = (tracks[trackLen - 1] || {}).points;\n        var pinchPre = (tracks[trackLen - 2] || {}).points || pinchEnd;\n        if (pinchPre\n            && pinchPre.length > 1\n            && pinchEnd\n            && pinchEnd.length > 1) {\n            var pinchScale = dist(pinchEnd) / dist(pinchPre);\n            !isFinite(pinchScale) && (pinchScale = 1);\n            event.pinchScale = pinchScale;\n            var pinchCenter = center(pinchEnd);\n            event.pinchX = pinchCenter[0];\n            event.pinchY = pinchCenter[1];\n            return {\n                type: 'pinch',\n                target: tracks[0].target,\n                event: event\n            };\n        }\n    }\n};\n"], "mappings": ";AAAA,OAAO,KAAKA,SAAS,MAAM,YAAY;AACvC,IAAIC,UAAU,GAAI,YAAY;EAC1B,SAASA,UAAUA,CAAA,EAAG;IAClB,IAAI,CAACC,MAAM,GAAG,EAAE;EACpB;EACAD,UAAU,CAACE,SAAS,CAACC,SAAS,GAAG,UAAUC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAE;IAC5D,IAAI,CAACC,QAAQ,CAACH,KAAK,EAAEC,MAAM,EAAEC,IAAI,CAAC;IAClC,OAAO,IAAI,CAACE,UAAU,CAACJ,KAAK,CAAC;EACjC,CAAC;EACDJ,UAAU,CAACE,SAAS,CAACO,KAAK,GAAG,YAAY;IACrC,IAAI,CAACR,MAAM,CAACS,MAAM,GAAG,CAAC;IACtB,OAAO,IAAI;EACf,CAAC;EACDV,UAAU,CAACE,SAAS,CAACK,QAAQ,GAAG,UAAUH,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAE;IAC3D,IAAIK,OAAO,GAAGP,KAAK,CAACO,OAAO;IAC3B,IAAI,CAACA,OAAO,EAAE;MACV;IACJ;IACA,IAAIC,SAAS,GAAG;MACZC,MAAM,EAAE,EAAE;MACVF,OAAO,EAAE,EAAE;MACXN,MAAM,EAAEA,MAAM;MACdD,KAAK,EAAEA;IACX,CAAC;IACD,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGJ,OAAO,CAACD,MAAM,EAAEI,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAChD,IAAIE,KAAK,GAAGL,OAAO,CAACG,CAAC,CAAC;MACtB,IAAIG,GAAG,GAAGlB,SAAS,CAACmB,aAAa,CAACZ,IAAI,EAAEU,KAAK,EAAE,CAAC,CAAC,CAAC;MAClDJ,SAAS,CAACC,MAAM,CAACM,IAAI,CAAC,CAACF,GAAG,CAACG,GAAG,EAAEH,GAAG,CAACI,GAAG,CAAC,CAAC;MACzCT,SAAS,CAACD,OAAO,CAACQ,IAAI,CAACH,KAAK,CAAC;IACjC;IACA,IAAI,CAACf,MAAM,CAACkB,IAAI,CAACP,SAAS,CAAC;EAC/B,CAAC;EACDZ,UAAU,CAACE,SAAS,CAACM,UAAU,GAAG,UAAUJ,KAAK,EAAE;IAC/C,KAAK,IAAIkB,SAAS,IAAIC,WAAW,EAAE;MAC/B,IAAIA,WAAW,CAACC,cAAc,CAACF,SAAS,CAAC,EAAE;QACvC,IAAIG,WAAW,GAAGF,WAAW,CAACD,SAAS,CAAC,CAAC,IAAI,CAACrB,MAAM,EAAEG,KAAK,CAAC;QAC5D,IAAIqB,WAAW,EAAE;UACb,OAAOA,WAAW;QACtB;MACJ;IACJ;EACJ,CAAC;EACD,OAAOzB,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU;AACnB,SAAS0B,IAAIA,CAACC,SAAS,EAAE;EACrB,IAAIC,EAAE,GAAGD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C,IAAIE,EAAE,GAAGF,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C,OAAOG,IAAI,CAACC,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;AACvC;AACA,SAASG,MAAMA,CAACL,SAAS,EAAE;EACvB,OAAO,CACH,CAACA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EACvC,CAACA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAC1C;AACL;AACA,IAAIJ,WAAW,GAAG;EACdU,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE9B,KAAK,EAAE;IAC5B,IAAI+B,QAAQ,GAAGD,MAAM,CAACxB,MAAM;IAC5B,IAAI,CAACyB,QAAQ,EAAE;MACX;IACJ;IACA,IAAIC,QAAQ,GAAG,CAACF,MAAM,CAACC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEtB,MAAM;IAClD,IAAIwB,QAAQ,GAAG,CAACH,MAAM,CAACC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEtB,MAAM,IAAIuB,QAAQ;IAC9D,IAAIC,QAAQ,IACLA,QAAQ,CAAC3B,MAAM,GAAG,CAAC,IACnB0B,QAAQ,IACRA,QAAQ,CAAC1B,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI4B,UAAU,GAAGZ,IAAI,CAACU,QAAQ,CAAC,GAAGV,IAAI,CAACW,QAAQ,CAAC;MAChD,CAACE,QAAQ,CAACD,UAAU,CAAC,KAAKA,UAAU,GAAG,CAAC,CAAC;MACzClC,KAAK,CAACkC,UAAU,GAAGA,UAAU;MAC7B,IAAIE,WAAW,GAAGR,MAAM,CAACI,QAAQ,CAAC;MAClChC,KAAK,CAACqC,MAAM,GAAGD,WAAW,CAAC,CAAC,CAAC;MAC7BpC,KAAK,CAACsC,MAAM,GAAGF,WAAW,CAAC,CAAC,CAAC;MAC7B,OAAO;QACHG,IAAI,EAAE,OAAO;QACbtC,MAAM,EAAE6B,MAAM,CAAC,CAAC,CAAC,CAAC7B,MAAM;QACxBD,KAAK,EAAEA;MACX,CAAC;IACL;EACJ;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}