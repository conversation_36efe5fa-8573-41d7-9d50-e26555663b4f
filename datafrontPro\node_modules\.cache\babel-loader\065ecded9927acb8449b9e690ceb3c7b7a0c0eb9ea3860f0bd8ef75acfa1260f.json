{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesData from '../../data/SeriesData.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { defaultEmphasis } from '../../util/model.js';\nimport Model from '../../model/Model.js';\nimport createGraphFromNodeEdge from '../helper/createGraphFromNodeEdge.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport SeriesModel from '../../model/Series.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nimport { defaultSeriesFormatTooltip } from '../../component/tooltip/seriesFormatTooltip.js';\nimport { initCurvenessList, createEdgeMapForCurveness } from '../helper/multipleGraphEdgeHelper.js';\nvar GraphSeriesModel = /** @class */function (_super) {\n  __extends(GraphSeriesModel, _super);\n  function GraphSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  GraphSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    var self = this;\n    function getCategoriesData() {\n      return self._categoriesData;\n    }\n    // Provide data for legend select\n    this.legendVisualProvider = new LegendVisualProvider(getCategoriesData, getCategoriesData);\n    this.fillDataTextStyle(option.edges || option.links);\n    this._updateCategoriesData();\n  };\n  GraphSeriesModel.prototype.mergeOption = function (option) {\n    _super.prototype.mergeOption.apply(this, arguments);\n    this.fillDataTextStyle(option.edges || option.links);\n    this._updateCategoriesData();\n  };\n  GraphSeriesModel.prototype.mergeDefaultAndTheme = function (option) {\n    _super.prototype.mergeDefaultAndTheme.apply(this, arguments);\n    defaultEmphasis(option, 'edgeLabel', ['show']);\n  };\n  GraphSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    var edges = option.edges || option.links || [];\n    var nodes = option.data || option.nodes || [];\n    var self = this;\n    if (nodes && edges) {\n      // auto curveness\n      initCurvenessList(this);\n      var graph = createGraphFromNodeEdge(nodes, edges, this, true, beforeLink);\n      zrUtil.each(graph.edges, function (edge) {\n        createEdgeMapForCurveness(edge.node1, edge.node2, this, edge.dataIndex);\n      }, this);\n      return graph.data;\n    }\n    function beforeLink(nodeData, edgeData) {\n      // Overwrite nodeData.getItemModel to\n      nodeData.wrapMethod('getItemModel', function (model) {\n        var categoriesModels = self._categoriesModels;\n        var categoryIdx = model.getShallow('category');\n        var categoryModel = categoriesModels[categoryIdx];\n        if (categoryModel) {\n          categoryModel.parentModel = model.parentModel;\n          model.parentModel = categoryModel;\n        }\n        return model;\n      });\n      // TODO Inherit resolveParentPath by default in Model#getModel?\n      var oldGetModel = Model.prototype.getModel;\n      function newGetModel(path, parentModel) {\n        var model = oldGetModel.call(this, path, parentModel);\n        model.resolveParentPath = resolveParentPath;\n        return model;\n      }\n      edgeData.wrapMethod('getItemModel', function (model) {\n        model.resolveParentPath = resolveParentPath;\n        model.getModel = newGetModel;\n        return model;\n      });\n      function resolveParentPath(pathArr) {\n        if (pathArr && (pathArr[0] === 'label' || pathArr[1] === 'label')) {\n          var newPathArr = pathArr.slice();\n          if (pathArr[0] === 'label') {\n            newPathArr[0] = 'edgeLabel';\n          } else if (pathArr[1] === 'label') {\n            newPathArr[1] = 'edgeLabel';\n          }\n          return newPathArr;\n        }\n        return pathArr;\n      }\n    }\n  };\n  GraphSeriesModel.prototype.getGraph = function () {\n    return this.getData().graph;\n  };\n  GraphSeriesModel.prototype.getEdgeData = function () {\n    return this.getGraph().edgeData;\n  };\n  GraphSeriesModel.prototype.getCategoriesData = function () {\n    return this._categoriesData;\n  };\n  GraphSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    if (dataType === 'edge') {\n      var nodeData = this.getData();\n      var params = this.getDataParams(dataIndex, dataType);\n      var edge = nodeData.graph.getEdgeByIndex(dataIndex);\n      var sourceName = nodeData.getName(edge.node1.dataIndex);\n      var targetName = nodeData.getName(edge.node2.dataIndex);\n      var nameArr = [];\n      sourceName != null && nameArr.push(sourceName);\n      targetName != null && nameArr.push(targetName);\n      return createTooltipMarkup('nameValue', {\n        name: nameArr.join(' > '),\n        value: params.value,\n        noValue: params.value == null\n      });\n    }\n    // dataType === 'node' or empty\n    var nodeMarkup = defaultSeriesFormatTooltip({\n      series: this,\n      dataIndex: dataIndex,\n      multipleSeries: multipleSeries\n    });\n    return nodeMarkup;\n  };\n  GraphSeriesModel.prototype._updateCategoriesData = function () {\n    var categories = zrUtil.map(this.option.categories || [], function (category) {\n      // Data must has value\n      return category.value != null ? category : zrUtil.extend({\n        value: 0\n      }, category);\n    });\n    var categoriesData = new SeriesData(['value'], this);\n    categoriesData.initData(categories);\n    this._categoriesData = categoriesData;\n    this._categoriesModels = categoriesData.mapArray(function (idx) {\n      return categoriesData.getItemModel(idx);\n    });\n  };\n  GraphSeriesModel.prototype.setZoom = function (zoom) {\n    this.option.zoom = zoom;\n  };\n  GraphSeriesModel.prototype.setCenter = function (center) {\n    this.option.center = center;\n  };\n  GraphSeriesModel.prototype.isAnimationEnabled = function () {\n    return _super.prototype.isAnimationEnabled.call(this)\n    // Not enable animation when do force layout\n    && !(this.get('layout') === 'force' && this.get(['force', 'layoutAnimation']));\n  };\n  GraphSeriesModel.type = 'series.graph';\n  GraphSeriesModel.dependencies = ['grid', 'polar', 'geo', 'singleAxis', 'calendar'];\n  GraphSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'view',\n    // Default option for all coordinate systems\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // polarIndex: 0,\n    // geoIndex: 0,\n    legendHoverLink: true,\n    layout: null,\n    // Configuration of circular layout\n    circular: {\n      rotateLabel: false\n    },\n    // Configuration of force directed layout\n    force: {\n      initLayout: null,\n      // Node repulsion. Can be an array to represent range.\n      repulsion: [0, 50],\n      gravity: 0.1,\n      // Initial friction\n      friction: 0.6,\n      // Edge length. Can be an array to represent range.\n      edgeLength: 30,\n      layoutAnimation: true\n    },\n    left: 'center',\n    top: 'center',\n    // right: null,\n    // bottom: null,\n    // width: '80%',\n    // height: '80%',\n    symbol: 'circle',\n    symbolSize: 10,\n    edgeSymbol: ['none', 'none'],\n    edgeSymbolSize: 10,\n    edgeLabel: {\n      position: 'middle',\n      distance: 5\n    },\n    draggable: false,\n    roam: false,\n    // Default on center of graph\n    center: null,\n    zoom: 1,\n    // Symbol size scale ratio in roam\n    nodeScaleRatio: 0.6,\n    // cursor: null,\n    // categories: [],\n    // data: []\n    // Or\n    // nodes: []\n    //\n    // links: []\n    // Or\n    // edges: []\n    label: {\n      show: false,\n      formatter: '{b}'\n    },\n    itemStyle: {},\n    lineStyle: {\n      color: '#aaa',\n      width: 1,\n      opacity: 0.5\n    },\n    emphasis: {\n      scale: true,\n      label: {\n        show: true\n      }\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  };\n  return GraphSeriesModel;\n}(SeriesModel);\nexport default GraphSeriesModel;", "map": {"version": 3, "names": ["__extends", "SeriesData", "zrUtil", "defaultEmphasis", "Model", "createGraphFromNodeEdge", "LegendVisualProvider", "SeriesModel", "createTooltipMarkup", "defaultSeriesFormatTooltip", "initCurvenessList", "createEdgeMapForCurveness", "GraphSeriesModel", "_super", "_this", "apply", "arguments", "type", "hasSymbolVisual", "prototype", "init", "option", "self", "getCategoriesData", "_categoriesData", "legend<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fillDataTextStyle", "edges", "links", "_updateCategoriesData", "mergeOption", "mergeDefaultAndTheme", "getInitialData", "ecModel", "nodes", "data", "graph", "beforeLink", "each", "edge", "node1", "node2", "dataIndex", "nodeData", "edgeData", "wrapMethod", "model", "categoriesModels", "_categoriesModels", "categoryIdx", "getShallow", "categoryModel", "parentModel", "oldGetModel", "getModel", "newGetModel", "path", "call", "resolveParentPath", "pathArr", "newPathArr", "slice", "getGraph", "getData", "getEdgeData", "formatTooltip", "multipleSeries", "dataType", "params", "getDataParams", "getEdgeByIndex", "sourceName", "getName", "targetName", "nameArr", "push", "name", "join", "value", "noValue", "nodeMarkup", "series", "categories", "map", "category", "extend", "categoriesData", "initData", "mapArray", "idx", "getItemModel", "setZoom", "zoom", "setCenter", "center", "isAnimationEnabled", "get", "dependencies", "defaultOption", "z", "coordinateSystem", "legendHoverLink", "layout", "circular", "rotateLabel", "force", "initLayout", "repulsion", "gravity", "friction", "edge<PERSON><PERSON><PERSON>", "layoutAnimation", "left", "top", "symbol", "symbolSize", "edgeSymbol", "edgeSymbolSize", "edgeLabel", "position", "distance", "draggable", "roam", "nodeScaleRatio", "label", "show", "formatter", "itemStyle", "lineStyle", "color", "width", "opacity", "emphasis", "scale", "select", "borderColor"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/chart/graph/GraphSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesData from '../../data/SeriesData.js';\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { defaultEmphasis } from '../../util/model.js';\nimport Model from '../../model/Model.js';\nimport createGraphFromNodeEdge from '../helper/createGraphFromNodeEdge.js';\nimport LegendVisualProvider from '../../visual/LegendVisualProvider.js';\nimport SeriesModel from '../../model/Series.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nimport { defaultSeriesFormatTooltip } from '../../component/tooltip/seriesFormatTooltip.js';\nimport { initCurvenessList, createEdgeMapForCurveness } from '../helper/multipleGraphEdgeHelper.js';\nvar GraphSeriesModel = /** @class */function (_super) {\n  __extends(GraphSeriesModel, _super);\n  function GraphSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = GraphSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    return _this;\n  }\n  GraphSeriesModel.prototype.init = function (option) {\n    _super.prototype.init.apply(this, arguments);\n    var self = this;\n    function getCategoriesData() {\n      return self._categoriesData;\n    }\n    // Provide data for legend select\n    this.legendVisualProvider = new LegendVisualProvider(getCategoriesData, getCategoriesData);\n    this.fillDataTextStyle(option.edges || option.links);\n    this._updateCategoriesData();\n  };\n  GraphSeriesModel.prototype.mergeOption = function (option) {\n    _super.prototype.mergeOption.apply(this, arguments);\n    this.fillDataTextStyle(option.edges || option.links);\n    this._updateCategoriesData();\n  };\n  GraphSeriesModel.prototype.mergeDefaultAndTheme = function (option) {\n    _super.prototype.mergeDefaultAndTheme.apply(this, arguments);\n    defaultEmphasis(option, 'edgeLabel', ['show']);\n  };\n  GraphSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    var edges = option.edges || option.links || [];\n    var nodes = option.data || option.nodes || [];\n    var self = this;\n    if (nodes && edges) {\n      // auto curveness\n      initCurvenessList(this);\n      var graph = createGraphFromNodeEdge(nodes, edges, this, true, beforeLink);\n      zrUtil.each(graph.edges, function (edge) {\n        createEdgeMapForCurveness(edge.node1, edge.node2, this, edge.dataIndex);\n      }, this);\n      return graph.data;\n    }\n    function beforeLink(nodeData, edgeData) {\n      // Overwrite nodeData.getItemModel to\n      nodeData.wrapMethod('getItemModel', function (model) {\n        var categoriesModels = self._categoriesModels;\n        var categoryIdx = model.getShallow('category');\n        var categoryModel = categoriesModels[categoryIdx];\n        if (categoryModel) {\n          categoryModel.parentModel = model.parentModel;\n          model.parentModel = categoryModel;\n        }\n        return model;\n      });\n      // TODO Inherit resolveParentPath by default in Model#getModel?\n      var oldGetModel = Model.prototype.getModel;\n      function newGetModel(path, parentModel) {\n        var model = oldGetModel.call(this, path, parentModel);\n        model.resolveParentPath = resolveParentPath;\n        return model;\n      }\n      edgeData.wrapMethod('getItemModel', function (model) {\n        model.resolveParentPath = resolveParentPath;\n        model.getModel = newGetModel;\n        return model;\n      });\n      function resolveParentPath(pathArr) {\n        if (pathArr && (pathArr[0] === 'label' || pathArr[1] === 'label')) {\n          var newPathArr = pathArr.slice();\n          if (pathArr[0] === 'label') {\n            newPathArr[0] = 'edgeLabel';\n          } else if (pathArr[1] === 'label') {\n            newPathArr[1] = 'edgeLabel';\n          }\n          return newPathArr;\n        }\n        return pathArr;\n      }\n    }\n  };\n  GraphSeriesModel.prototype.getGraph = function () {\n    return this.getData().graph;\n  };\n  GraphSeriesModel.prototype.getEdgeData = function () {\n    return this.getGraph().edgeData;\n  };\n  GraphSeriesModel.prototype.getCategoriesData = function () {\n    return this._categoriesData;\n  };\n  GraphSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    if (dataType === 'edge') {\n      var nodeData = this.getData();\n      var params = this.getDataParams(dataIndex, dataType);\n      var edge = nodeData.graph.getEdgeByIndex(dataIndex);\n      var sourceName = nodeData.getName(edge.node1.dataIndex);\n      var targetName = nodeData.getName(edge.node2.dataIndex);\n      var nameArr = [];\n      sourceName != null && nameArr.push(sourceName);\n      targetName != null && nameArr.push(targetName);\n      return createTooltipMarkup('nameValue', {\n        name: nameArr.join(' > '),\n        value: params.value,\n        noValue: params.value == null\n      });\n    }\n    // dataType === 'node' or empty\n    var nodeMarkup = defaultSeriesFormatTooltip({\n      series: this,\n      dataIndex: dataIndex,\n      multipleSeries: multipleSeries\n    });\n    return nodeMarkup;\n  };\n  GraphSeriesModel.prototype._updateCategoriesData = function () {\n    var categories = zrUtil.map(this.option.categories || [], function (category) {\n      // Data must has value\n      return category.value != null ? category : zrUtil.extend({\n        value: 0\n      }, category);\n    });\n    var categoriesData = new SeriesData(['value'], this);\n    categoriesData.initData(categories);\n    this._categoriesData = categoriesData;\n    this._categoriesModels = categoriesData.mapArray(function (idx) {\n      return categoriesData.getItemModel(idx);\n    });\n  };\n  GraphSeriesModel.prototype.setZoom = function (zoom) {\n    this.option.zoom = zoom;\n  };\n  GraphSeriesModel.prototype.setCenter = function (center) {\n    this.option.center = center;\n  };\n  GraphSeriesModel.prototype.isAnimationEnabled = function () {\n    return _super.prototype.isAnimationEnabled.call(this)\n    // Not enable animation when do force layout\n    && !(this.get('layout') === 'force' && this.get(['force', 'layoutAnimation']));\n  };\n  GraphSeriesModel.type = 'series.graph';\n  GraphSeriesModel.dependencies = ['grid', 'polar', 'geo', 'singleAxis', 'calendar'];\n  GraphSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'view',\n    // Default option for all coordinate systems\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    // polarIndex: 0,\n    // geoIndex: 0,\n    legendHoverLink: true,\n    layout: null,\n    // Configuration of circular layout\n    circular: {\n      rotateLabel: false\n    },\n    // Configuration of force directed layout\n    force: {\n      initLayout: null,\n      // Node repulsion. Can be an array to represent range.\n      repulsion: [0, 50],\n      gravity: 0.1,\n      // Initial friction\n      friction: 0.6,\n      // Edge length. Can be an array to represent range.\n      edgeLength: 30,\n      layoutAnimation: true\n    },\n    left: 'center',\n    top: 'center',\n    // right: null,\n    // bottom: null,\n    // width: '80%',\n    // height: '80%',\n    symbol: 'circle',\n    symbolSize: 10,\n    edgeSymbol: ['none', 'none'],\n    edgeSymbolSize: 10,\n    edgeLabel: {\n      position: 'middle',\n      distance: 5\n    },\n    draggable: false,\n    roam: false,\n    // Default on center of graph\n    center: null,\n    zoom: 1,\n    // Symbol size scale ratio in roam\n    nodeScaleRatio: 0.6,\n    // cursor: null,\n    // categories: [],\n    // data: []\n    // Or\n    // nodes: []\n    //\n    // links: []\n    // Or\n    // edges: []\n    label: {\n      show: false,\n      formatter: '{b}'\n    },\n    itemStyle: {},\n    lineStyle: {\n      color: '#aaa',\n      width: 1,\n      opacity: 0.5\n    },\n    emphasis: {\n      scale: true,\n      label: {\n        show: true\n      }\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  };\n  return GraphSeriesModel;\n}(SeriesModel);\nexport default GraphSeriesModel;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,SAASC,eAAe,QAAQ,qBAAqB;AACrD,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAOC,uBAAuB,MAAM,sCAAsC;AAC1E,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,SAASC,mBAAmB,QAAQ,0CAA0C;AAC9E,SAASC,0BAA0B,QAAQ,gDAAgD;AAC3F,SAASC,iBAAiB,EAAEC,yBAAyB,QAAQ,sCAAsC;AACnG,IAAIC,gBAAgB,GAAG,aAAa,UAAUC,MAAM,EAAE;EACpDb,SAAS,CAACY,gBAAgB,EAAEC,MAAM,CAAC;EACnC,SAASD,gBAAgBA,CAAA,EAAG;IAC1B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,gBAAgB,CAACK,IAAI;IAClCH,KAAK,CAACI,eAAe,GAAG,IAAI;IAC5B,OAAOJ,KAAK;EACd;EACAF,gBAAgB,CAACO,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAE;IAClDR,MAAM,CAACM,SAAS,CAACC,IAAI,CAACL,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC5C,IAAIM,IAAI,GAAG,IAAI;IACf,SAASC,iBAAiBA,CAAA,EAAG;MAC3B,OAAOD,IAAI,CAACE,eAAe;IAC7B;IACA;IACA,IAAI,CAACC,oBAAoB,GAAG,IAAInB,oBAAoB,CAACiB,iBAAiB,EAAEA,iBAAiB,CAAC;IAC1F,IAAI,CAACG,iBAAiB,CAACL,MAAM,CAACM,KAAK,IAAIN,MAAM,CAACO,KAAK,CAAC;IACpD,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAC9B,CAAC;EACDjB,gBAAgB,CAACO,SAAS,CAACW,WAAW,GAAG,UAAUT,MAAM,EAAE;IACzDR,MAAM,CAACM,SAAS,CAACW,WAAW,CAACf,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACnD,IAAI,CAACU,iBAAiB,CAACL,MAAM,CAACM,KAAK,IAAIN,MAAM,CAACO,KAAK,CAAC;IACpD,IAAI,CAACC,qBAAqB,CAAC,CAAC;EAC9B,CAAC;EACDjB,gBAAgB,CAACO,SAAS,CAACY,oBAAoB,GAAG,UAAUV,MAAM,EAAE;IAClER,MAAM,CAACM,SAAS,CAACY,oBAAoB,CAAChB,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC5Db,eAAe,CAACkB,MAAM,EAAE,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC;EAChD,CAAC;EACDT,gBAAgB,CAACO,SAAS,CAACa,cAAc,GAAG,UAAUX,MAAM,EAAEY,OAAO,EAAE;IACrE,IAAIN,KAAK,GAAGN,MAAM,CAACM,KAAK,IAAIN,MAAM,CAACO,KAAK,IAAI,EAAE;IAC9C,IAAIM,KAAK,GAAGb,MAAM,CAACc,IAAI,IAAId,MAAM,CAACa,KAAK,IAAI,EAAE;IAC7C,IAAIZ,IAAI,GAAG,IAAI;IACf,IAAIY,KAAK,IAAIP,KAAK,EAAE;MAClB;MACAjB,iBAAiB,CAAC,IAAI,CAAC;MACvB,IAAI0B,KAAK,GAAG/B,uBAAuB,CAAC6B,KAAK,EAAEP,KAAK,EAAE,IAAI,EAAE,IAAI,EAAEU,UAAU,CAAC;MACzEnC,MAAM,CAACoC,IAAI,CAACF,KAAK,CAACT,KAAK,EAAE,UAAUY,IAAI,EAAE;QACvC5B,yBAAyB,CAAC4B,IAAI,CAACC,KAAK,EAAED,IAAI,CAACE,KAAK,EAAE,IAAI,EAAEF,IAAI,CAACG,SAAS,CAAC;MACzE,CAAC,EAAE,IAAI,CAAC;MACR,OAAON,KAAK,CAACD,IAAI;IACnB;IACA,SAASE,UAAUA,CAACM,QAAQ,EAAEC,QAAQ,EAAE;MACtC;MACAD,QAAQ,CAACE,UAAU,CAAC,cAAc,EAAE,UAAUC,KAAK,EAAE;QACnD,IAAIC,gBAAgB,GAAGzB,IAAI,CAAC0B,iBAAiB;QAC7C,IAAIC,WAAW,GAAGH,KAAK,CAACI,UAAU,CAAC,UAAU,CAAC;QAC9C,IAAIC,aAAa,GAAGJ,gBAAgB,CAACE,WAAW,CAAC;QACjD,IAAIE,aAAa,EAAE;UACjBA,aAAa,CAACC,WAAW,GAAGN,KAAK,CAACM,WAAW;UAC7CN,KAAK,CAACM,WAAW,GAAGD,aAAa;QACnC;QACA,OAAOL,KAAK;MACd,CAAC,CAAC;MACF;MACA,IAAIO,WAAW,GAAGjD,KAAK,CAACe,SAAS,CAACmC,QAAQ;MAC1C,SAASC,WAAWA,CAACC,IAAI,EAAEJ,WAAW,EAAE;QACtC,IAAIN,KAAK,GAAGO,WAAW,CAACI,IAAI,CAAC,IAAI,EAAED,IAAI,EAAEJ,WAAW,CAAC;QACrDN,KAAK,CAACY,iBAAiB,GAAGA,iBAAiB;QAC3C,OAAOZ,KAAK;MACd;MACAF,QAAQ,CAACC,UAAU,CAAC,cAAc,EAAE,UAAUC,KAAK,EAAE;QACnDA,KAAK,CAACY,iBAAiB,GAAGA,iBAAiB;QAC3CZ,KAAK,CAACQ,QAAQ,GAAGC,WAAW;QAC5B,OAAOT,KAAK;MACd,CAAC,CAAC;MACF,SAASY,iBAAiBA,CAACC,OAAO,EAAE;QAClC,IAAIA,OAAO,KAAKA,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,EAAE;UACjE,IAAIC,UAAU,GAAGD,OAAO,CAACE,KAAK,CAAC,CAAC;UAChC,IAAIF,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;YAC1BC,UAAU,CAAC,CAAC,CAAC,GAAG,WAAW;UAC7B,CAAC,MAAM,IAAID,OAAO,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;YACjCC,UAAU,CAAC,CAAC,CAAC,GAAG,WAAW;UAC7B;UACA,OAAOA,UAAU;QACnB;QACA,OAAOD,OAAO;MAChB;IACF;EACF,CAAC;EACD/C,gBAAgB,CAACO,SAAS,CAAC2C,QAAQ,GAAG,YAAY;IAChD,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC3B,KAAK;EAC7B,CAAC;EACDxB,gBAAgB,CAACO,SAAS,CAAC6C,WAAW,GAAG,YAAY;IACnD,OAAO,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAClB,QAAQ;EACjC,CAAC;EACDhC,gBAAgB,CAACO,SAAS,CAACI,iBAAiB,GAAG,YAAY;IACzD,OAAO,IAAI,CAACC,eAAe;EAC7B,CAAC;EACDZ,gBAAgB,CAACO,SAAS,CAAC8C,aAAa,GAAG,UAAUvB,SAAS,EAAEwB,cAAc,EAAEC,QAAQ,EAAE;IACxF,IAAIA,QAAQ,KAAK,MAAM,EAAE;MACvB,IAAIxB,QAAQ,GAAG,IAAI,CAACoB,OAAO,CAAC,CAAC;MAC7B,IAAIK,MAAM,GAAG,IAAI,CAACC,aAAa,CAAC3B,SAAS,EAAEyB,QAAQ,CAAC;MACpD,IAAI5B,IAAI,GAAGI,QAAQ,CAACP,KAAK,CAACkC,cAAc,CAAC5B,SAAS,CAAC;MACnD,IAAI6B,UAAU,GAAG5B,QAAQ,CAAC6B,OAAO,CAACjC,IAAI,CAACC,KAAK,CAACE,SAAS,CAAC;MACvD,IAAI+B,UAAU,GAAG9B,QAAQ,CAAC6B,OAAO,CAACjC,IAAI,CAACE,KAAK,CAACC,SAAS,CAAC;MACvD,IAAIgC,OAAO,GAAG,EAAE;MAChBH,UAAU,IAAI,IAAI,IAAIG,OAAO,CAACC,IAAI,CAACJ,UAAU,CAAC;MAC9CE,UAAU,IAAI,IAAI,IAAIC,OAAO,CAACC,IAAI,CAACF,UAAU,CAAC;MAC9C,OAAOjE,mBAAmB,CAAC,WAAW,EAAE;QACtCoE,IAAI,EAAEF,OAAO,CAACG,IAAI,CAAC,KAAK,CAAC;QACzBC,KAAK,EAAEV,MAAM,CAACU,KAAK;QACnBC,OAAO,EAAEX,MAAM,CAACU,KAAK,IAAI;MAC3B,CAAC,CAAC;IACJ;IACA;IACA,IAAIE,UAAU,GAAGvE,0BAA0B,CAAC;MAC1CwE,MAAM,EAAE,IAAI;MACZvC,SAAS,EAAEA,SAAS;MACpBwB,cAAc,EAAEA;IAClB,CAAC,CAAC;IACF,OAAOc,UAAU;EACnB,CAAC;EACDpE,gBAAgB,CAACO,SAAS,CAACU,qBAAqB,GAAG,YAAY;IAC7D,IAAIqD,UAAU,GAAGhF,MAAM,CAACiF,GAAG,CAAC,IAAI,CAAC9D,MAAM,CAAC6D,UAAU,IAAI,EAAE,EAAE,UAAUE,QAAQ,EAAE;MAC5E;MACA,OAAOA,QAAQ,CAACN,KAAK,IAAI,IAAI,GAAGM,QAAQ,GAAGlF,MAAM,CAACmF,MAAM,CAAC;QACvDP,KAAK,EAAE;MACT,CAAC,EAAEM,QAAQ,CAAC;IACd,CAAC,CAAC;IACF,IAAIE,cAAc,GAAG,IAAIrF,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC;IACpDqF,cAAc,CAACC,QAAQ,CAACL,UAAU,CAAC;IACnC,IAAI,CAAC1D,eAAe,GAAG8D,cAAc;IACrC,IAAI,CAACtC,iBAAiB,GAAGsC,cAAc,CAACE,QAAQ,CAAC,UAAUC,GAAG,EAAE;MAC9D,OAAOH,cAAc,CAACI,YAAY,CAACD,GAAG,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC;EACD7E,gBAAgB,CAACO,SAAS,CAACwE,OAAO,GAAG,UAAUC,IAAI,EAAE;IACnD,IAAI,CAACvE,MAAM,CAACuE,IAAI,GAAGA,IAAI;EACzB,CAAC;EACDhF,gBAAgB,CAACO,SAAS,CAAC0E,SAAS,GAAG,UAAUC,MAAM,EAAE;IACvD,IAAI,CAACzE,MAAM,CAACyE,MAAM,GAAGA,MAAM;EAC7B,CAAC;EACDlF,gBAAgB,CAACO,SAAS,CAAC4E,kBAAkB,GAAG,YAAY;IAC1D,OAAOlF,MAAM,CAACM,SAAS,CAAC4E,kBAAkB,CAACtC,IAAI,CAAC,IAAI;IACpD;IAAA,GACG,EAAE,IAAI,CAACuC,GAAG,CAAC,QAAQ,CAAC,KAAK,OAAO,IAAI,IAAI,CAACA,GAAG,CAAC,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;EAChF,CAAC;EACDpF,gBAAgB,CAACK,IAAI,GAAG,cAAc;EACtCL,gBAAgB,CAACqF,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,CAAC;EAClFrF,gBAAgB,CAACsF,aAAa,GAAG;IAC/B;IACAC,CAAC,EAAE,CAAC;IACJC,gBAAgB,EAAE,MAAM;IACxB;IACA;IACA;IACA;IACA;IACAC,eAAe,EAAE,IAAI;IACrBC,MAAM,EAAE,IAAI;IACZ;IACAC,QAAQ,EAAE;MACRC,WAAW,EAAE;IACf,CAAC;IACD;IACAC,KAAK,EAAE;MACLC,UAAU,EAAE,IAAI;MAChB;MACAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;MAClBC,OAAO,EAAE,GAAG;MACZ;MACAC,QAAQ,EAAE,GAAG;MACb;MACAC,UAAU,EAAE,EAAE;MACdC,eAAe,EAAE;IACnB,CAAC;IACDC,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAE,QAAQ;IACb;IACA;IACA;IACA;IACAC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,EAAE;IACdC,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC5BC,cAAc,EAAE,EAAE;IAClBC,SAAS,EAAE;MACTC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE;IACZ,CAAC;IACDC,SAAS,EAAE,KAAK;IAChBC,IAAI,EAAE,KAAK;IACX;IACA5B,MAAM,EAAE,IAAI;IACZF,IAAI,EAAE,CAAC;IACP;IACA+B,cAAc,EAAE,GAAG;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,KAAK,EAAE;MACLC,IAAI,EAAE,KAAK;MACXC,SAAS,EAAE;IACb,CAAC;IACDC,SAAS,EAAE,CAAC,CAAC;IACbC,SAAS,EAAE;MACTC,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE;IACX,CAAC;IACDC,QAAQ,EAAE;MACRC,KAAK,EAAE,IAAI;MACXT,KAAK,EAAE;QACLC,IAAI,EAAE;MACR;IACF,CAAC;IACDS,MAAM,EAAE;MACNP,SAAS,EAAE;QACTQ,WAAW,EAAE;MACf;IACF;EACF,CAAC;EACD,OAAO3H,gBAAgB;AACzB,CAAC,CAACL,WAAW,CAAC;AACd,eAAeK,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}