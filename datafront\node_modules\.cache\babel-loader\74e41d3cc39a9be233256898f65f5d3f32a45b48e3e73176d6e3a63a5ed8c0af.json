{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport PictorialBarView from './PictorialBarView.js';\nimport PictorialBarSeriesModel from './PictorialBarSeries.js';\nimport { createProgressiveLayout, layout } from '../../layout/barGrid.js';\nimport { curry } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  registers.registerChartView(PictorialBarView);\n  registers.registerSeriesModel(PictorialBarSeriesModel);\n  registers.registerLayout(registers.PRIORITY.VISUAL.LAYOUT, curry(layout, 'pictorialBar'));\n  // Do layout after other overall layout, which can prepare some information.\n  registers.registerLayout(registers.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT, createProgressiveLayout('pictorialBar'));\n}", "map": {"version": 3, "names": ["PictorialBarView", "PictorialBarSeriesModel", "createProgressiveLayout", "layout", "curry", "install", "registers", "registerChartView", "registerSeriesModel", "registerLayout", "PRIORITY", "VISUAL", "LAYOUT", "PROGRESSIVE_LAYOUT"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/bar/installPictorialBar.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport PictorialBarView from './PictorialBarView.js';\nimport PictorialBarSeriesModel from './PictorialBarSeries.js';\nimport { createProgressiveLayout, layout } from '../../layout/barGrid.js';\nimport { curry } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  registers.registerChartView(PictorialBarView);\n  registers.registerSeriesModel(PictorialBarSeriesModel);\n  registers.registerLayout(registers.PRIORITY.VISUAL.LAYOUT, curry(layout, 'pictorialBar'));\n  // Do layout after other overall layout, which can prepare some information.\n  registers.registerLayout(registers.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT, createProgressiveLayout('pictorialBar'));\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,uBAAuB,MAAM,yBAAyB;AAC7D,SAASC,uBAAuB,EAAEC,MAAM,QAAQ,yBAAyB;AACzE,SAASC,KAAK,QAAQ,0BAA0B;AAChD,OAAO,SAASC,OAAOA,CAACC,SAAS,EAAE;EACjCA,SAAS,CAACC,iBAAiB,CAACP,gBAAgB,CAAC;EAC7CM,SAAS,CAACE,mBAAmB,CAACP,uBAAuB,CAAC;EACtDK,SAAS,CAACG,cAAc,CAACH,SAAS,CAACI,QAAQ,CAACC,MAAM,CAACC,MAAM,EAAER,KAAK,CAACD,MAAM,EAAE,cAAc,CAAC,CAAC;EACzF;EACAG,SAAS,CAACG,cAAc,CAACH,SAAS,CAACI,QAAQ,CAACC,MAAM,CAACE,kBAAkB,EAAEX,uBAAuB,CAAC,cAAc,CAAC,CAAC;AACjH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}