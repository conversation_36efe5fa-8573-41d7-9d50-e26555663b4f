{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { dataApi } from './api';\nimport { v4 as uuidv4 } from 'uuid';\nimport axios from 'axios';\nimport ChartDisplay from './components/ChartDisplay.vue';\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false,\n      // 新增：控制原始响应弹出层\n      lastRawResponse: '',\n      // 新增：存储最后收到的原始响应\n      drawer: false,\n      //抽屉展示\n      direction: 'rtl' //默认抽屉从又往左\n    };\n  },\n  mounted() {\n    this.loadTables();\n    this.initMemoryId();\n    this.addWelcomeMessage();\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\"帮我生成一个销售额柱状图\", \"展示近六个月的销售趋势折线图\", \"按照区域统计销售量并生成饼图\", \"帮我做一个按产品类别的销量对比图\"];\n  },\n  updated() {\n    this.scrollToBottom();\n  },\n  methods: {\n    SelectDataList() {\n      this.drawer = true;\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id');\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4());\n        localStorage.setItem('user_memory_id', storedMemoryId);\n      }\n      this.memoryId = storedMemoryId;\n    },\n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0;\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i];\n        number = number * 16 + (parseInt(hexValue, 16) || 0);\n      }\n      return number % 1000000;\n    },\n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      });\n    },\n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight;\n      }\n    },\n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables();\n        this.tables = response.data;\n      } catch (error) {\n        this.$message.error('加载数据表失败');\n        console.error(error);\n      }\n    },\n    async selectTable(table) {\n      try {\n        this.dialogVisible = true;\n        console.log(table);\n        const response = await dataApi.getTableIndicators(table);\n        this.tableIndicators = response.data;\n        console.log(this.tableIndicators);\n      } catch (error) {\n        this.$message.error('加载数据表失败');\n        console.error(error);\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n\n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedTable ? this.selectedTable.tableName : this.tables.length > 0 ? this.tables[0].tableName : \"未知数据集\";\n\n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      };\n      this.messages.push(userMsg);\n\n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      };\n      this.messages.push(botMsg);\n\n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1];\n\n      // 保存问题并清空输入框\n      const question = this.question;\n      this.question = '';\n      this.isSending = true;\n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`;\n\n        // 发送请求\n        await axios.post('http://localhost:8088/api/indicator/chat', {\n          memoryId: this.memoryId,\n          message\n        }, {\n          responseType: 'stream',\n          onDownloadProgress: e => {\n            const fullText = e.event.target.responseText; // 累积的完整文本\n            let newText = fullText.substring(lastMsg.content.length);\n            lastMsg.content += newText; // 增量更新\n            this.scrollToBottom(); // 实时滚动\n\n            // 保存原始响应\n            this.lastRawResponse = fullText;\n          }\n        });\n\n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false;\n        this.isSending = false;\n\n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error);\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。';\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      }\n    },\n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n\n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n\n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n\n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n\n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n\n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n\n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n\n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n\n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n\n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n\n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n\n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n\n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n\n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n\n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n\n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n\n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      this.messages.push(botMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n\n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n\n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [{\n            field: '类别1',\n            value: 100\n          }, {\n            field: '类别2',\n            value: 200\n          }, {\n            field: '类别3',\n            value: 150\n          }],\n          metrics: [{\n            name: '数值'\n          }]\n        }\n      };\n\n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n\n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [{\n              value: 1,\n              field: \"神朔\",\n              name: \"神朔\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"甘泉\",\n              name: \"甘泉\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"包神\",\n              name: \"包神\",\n              category: \"记录数*\"\n            }],\n            fields: [{\n              id: \"1746787308487\",\n              name: \"名称\",\n              groupType: \"d\"\n            }, {\n              id: \"-1\",\n              name: \"记录数*\",\n              groupType: \"q\"\n            }]\n          }\n        }\n      };\n\n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n\n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n\n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // 获取最后一条消息的完整内容\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "v4", "uuidv4", "axios", "ChartDisplay", "name", "components", "data", "description", "tablename", "tables", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "tableIndicators", "dialogVisible", "messages", "memoryId", "isSending", "messageListRef", "showRawResponsePanel", "lastRawResponse", "drawer", "direction", "mounted", "loadTables", "initMemoryId", "addWelcomeMessage", "updated", "scrollToBottom", "methods", "SelectDataList", "storedMemoryId", "localStorage", "getItem", "uuidToNumber", "setItem", "uuid", "number", "i", "length", "hexValue", "parseInt", "push", "isUser", "content", "isTyping", "$refs", "scrollTop", "scrollHeight", "response", "getAllTables", "error", "$message", "console", "selectTable", "table", "log", "getTableIndicators", "showSuggestions", "useQuestion", "q", "getTableFields", "tableCode", "submitQuestion", "trim", "warning", "currentDataset", "tableName", "userMsg", "botMsg", "lastMsg", "message", "post", "responseType", "onDownloadProgress", "e", "fullText", "event", "target", "responseText", "newText", "substring", "parseChartConfig", "chartDataIdMatch", "match", "chartDataId", "fetchChartDataById", "chartConfigMatch", "chartConfig", "JSON", "parse", "code", "type", "datasetId", "tableId", "replace", "jsonRegex", "jsonMatch", "jsonStr", "parseError", "includes", "defaultConfig", "id", "Date", "now", "toString", "title", "get", "status", "$forceUpdate", "test<PERSON>hart", "testChartConfig", "stringify", "testApiResponse", "msg", "field", "value", "metrics", "json<PERSON><PERSON><PERSON>", "apiResponseMsg", "testRealData", "realData", "category", "fields", "groupType", "realDataMsg", "showRawResponse"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n        </div>\n        \n        <div class=\"data-selection\">\n          <h3>目前可用数据</h3>\n          <div class=\"data-sets\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\" v-for=\"table in tables\" :key=\"table.tableCode\">\n                <el-card class=\"data-card\" @click.native=\"selectTable(table.datasetId)\">\n                  <div class=\"data-header\">\n                    <span class=\"sample-tag\">{{table.tableName}}</span>\n                    <span>{{ table.description }}</span>\n                  </div>\n                  <div class=\"data-body\">\n                    <div class=\"data-fields\">\n                      <div v-for=\"(field, index) in getTableFields(table)\" :key=\"index\" class=\"field-item\">\n                        {{ field }}\n                      </div>\n                    </div>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        \n        <!-- 聊天消息列表区域 -->\n        <div class=\"message-list\" ref=\"messageListRef\">\n          <div\n            v-for=\"(message, index) in messages\"\n            :key=\"index\"\n            :class=\"message.isUser ? 'message user-message' : 'message bot-message'\"\n          >\n            <!-- 聊天图标 -->\n            <div class=\"avatar-container\" v-if=\"!message.isUser\">\n              <div class=\"bot-avatar\">\n                <i class=\"el-icon-s-tools\"></i>\n              </div>\n            </div>\n            <!-- 消息内容 -->\n            <div class=\"message-content\">\n              <div v-html=\"message.content\"></div>\n              <!-- 如果消息中包含图表配置，则显示图表 -->\n              <div v-if=\"message.chartConfig\" class=\"chart-container\">\n                <chart-display :chart-config=\"message.chartConfig\"></chart-display>\n              </div>\n              <!-- loading动画 -->\n              <span\n                class=\"loading-dots\"\n                v-if=\"message.isTyping\"\n              >\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n              </span>\n            </div>\n            <div class=\"avatar-container\" v-if=\"message.isUser\">\n              <div class=\"user-avatar\">\n                <i class=\"el-icon-user\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 底部问题输入区域 -->\n    <div class=\"question-input-container\">\n      <span>👋直接问我问题，或在上方选择一个主题/数据开始！</span>\n      <div class=\"question-input-wrapper\">\n        <el-button type=\"text\" sty  @click=\"SelectDataList\">选择数据</el-button>\n        <!-- <div class=\"input-prefix\">👋</div> -->\n        <el-input \n          v-model=\"question\" \n          placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n          class=\"question-input\"\n          @keyup.enter.native=\"submitQuestion\"\n          :disabled=\"isSending\">\n        </el-input>\n        <div class=\"input-actions\">\n          <button class=\"action-btn\" @click=\"showSuggestions\">\n            <i class=\"el-icon-magic-stick\"></i>\n          </button>\n          <button class=\"action-btn test-btn\" @click=\"testChart\" title=\"测试图表功能\">\n            <i class=\"el-icon-data-line\"></i>\n          </button>\n          <button class=\"action-btn test-btn\" @click=\"testRealData\" title=\"测试实际数据\">\n            <i class=\"el-icon-s-data\"></i>\n          </button>\n          <button class=\"action-btn debug-btn\" @click=\"showRawResponse\" title=\"显示AI原始响应\">\n            <i class=\"el-icon-monitor\"></i>\n          </button>\n          <button class=\"action-btn send-btn\" @click=\"submitQuestion\" :disabled=\"isSending\">\n            <i class=\"el-icon-position\"></i>\n          </button>\n        </div>\n      </div>\n      \n      <!-- 建议问题弹出层 -->\n      <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n        <div class=\"suggestions-title\">\n          <i class=\"el-icon-s-promotion\"></i> 官方推荐\n        </div>\n        <div class=\"suggestions-list\">\n          <div \n            v-for=\"(suggestion, index) in suggestedQuestions\" \n            :key=\"index\"\n            class=\"suggestion-item\"\n            @click=\"useQuestion(suggestion)\">\n            {{ suggestion }}\n          </div>\n        </div>\n      </div>\n      \n      <!-- AI原始响应弹出层 -->\n      <div v-if=\"showRawResponsePanel\" class=\"raw-response-panel\">\n        <div class=\"raw-response-title\">\n          <i class=\"el-icon-monitor\"></i> AI原始响应\n          <button class=\"close-btn\" @click=\"showRawResponsePanel = false\">\n            <i class=\"el-icon-close\"></i>\n          </button>\n        </div>\n        <pre class=\"raw-response-content\">{{ lastRawResponse }}</pre>\n      </div>\n    </div>\n\n    <el-drawer\n      title=\"数据详情\"\n      :visible.sync=\"dialogVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <div style=\"padding: 20px\">\n        <el-table :data=\"tableIndicators\">\n          <el-table-column property=\"id\" ></el-table-column>\n          <el-table-column property=\"datasetTag\" ></el-table-column>\n          <el-table-column property=\"transactionDate\" ></el-table-column>\n          <el-table-column property=\"salesAmount\" ></el-table-column>\n        </el-table>\n      </div>\n    </el-drawer>\n    <el-drawer\n      title=\"数据展示\"\n      :visible.sync=\"drawer\"\n       direction=\"rtl\"\n      >\n      <span>我来啦!</span>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api'\nimport { v4 as uuidv4 } from 'uuid'\nimport axios from 'axios'\nimport ChartDisplay from './components/ChartDisplay.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false, // 新增：控制原始响应弹出层\n      lastRawResponse: '', // 新增：存储最后收到的原始响应\n      drawer:false,    //抽屉展示\n      direction: 'rtl', //默认抽屉从又往左\n    }\n  },\n  mounted() {\n    this.loadTables()\n    this.initMemoryId()\n    this.addWelcomeMessage()\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\n      \"帮我生成一个销售额柱状图\",\n      \"展示近六个月的销售趋势折线图\",\n      \"按照区域统计销售量并生成饼图\",\n      \"帮我做一个按产品类别的销量对比图\"\n    ]\n  },\n  updated() {\n    this.scrollToBottom()\n  },\n  methods: {\n    SelectDataList(){\n      this.drawer=true\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id')\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4())\n        localStorage.setItem('user_memory_id', storedMemoryId)\n      }\n      this.memoryId = storedMemoryId\n    },\n    \n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i]\n        number = number * 16 + (parseInt(hexValue, 16) || 0)\n      }\n      return number % 1000000\n    },\n    \n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      })\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight\n      }\n    },\n    \n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables()\n        this.tables = response.data\n      } catch (error) {\n        this.$message.error('加载数据表失败')\n        console.error(error)\n      }\n    },\n    async selectTable(table) {\n      try {\n        this.dialogVisible=true\n        console.log(table)\n      const response=await dataApi.getTableIndicators(table)\n      this.tableIndicators=response.data\n      console.log(this.tableIndicators)\n      }catch(error){\n        this.$message.error('加载数据表失败')\n        console.error(error)\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    \n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedTable ? \n        this.selectedTable.tableName : \n        (this.tables.length > 0 ? this.tables[0].tableName : \"未知数据集\")\n      \n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      }\n      this.messages.push(userMsg)\n      \n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      }\n      this.messages.push(botMsg)\n      \n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1]\n      \n      // 保存问题并清空输入框\n      const question = this.question\n      this.question = ''\n      this.isSending = true\n      \n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`\n        \n        // 发送请求\n        await axios.post(\n          'http://localhost:8088/api/indicator/chat',\n          { memoryId: this.memoryId, message },\n          {\n            responseType: 'stream',\n            onDownloadProgress: (e) => {\n              const fullText = e.event.target.responseText // 累积的完整文本\n              let newText = fullText.substring(lastMsg.content.length)\n              lastMsg.content += newText // 增量更新\n              this.scrollToBottom() // 实时滚动\n              \n              // 保存原始响应\n              this.lastRawResponse = fullText\n            }\n          }\n        )\n        \n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false\n        this.isSending = false\n        \n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error)\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'\n        lastMsg.isTyping = false\n        this.isSending = false\n      }\n    },\n    \n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n      \n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n        \n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n      \n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        \n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n          \n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n          \n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n            \n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, \n              '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            \n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      \n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n      \n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        \n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n          \n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          \n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            \n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n              \n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], \n                  '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n      \n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || \n          message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n        \n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        \n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    \n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        \n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n          \n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n          \n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n          \n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n          \n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    \n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n      \n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      \n      this.messages.push(botMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n      \n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n      \n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [\n            { field: '类别1', value: 100 },\n            { field: '类别2', value: 200 },\n            { field: '类别3', value: 150 }\n          ],\n          metrics: [{ name: '数值' }]\n        }\n      };\n      \n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      \n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      \n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    \n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n      \n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [\n              {value: 1, field: \"神朔\", name: \"神朔\", category: \"记录数*\"},\n              {value: 1, field: \"甘泉\", name: \"甘泉\", category: \"记录数*\"},\n              {value: 1, field: \"包神\", name: \"包神\", category: \"记录数*\"}\n            ],\n            fields: [\n              {id: \"1746787308487\", name: \"名称\", groupType: \"d\"},\n              {id: \"-1\", name: \"记录数*\", groupType: \"q\"}\n            ]\n          }\n        }\n      };\n      \n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n      \n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n      \n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      \n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // 获取最后一条消息的完整内容\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.app-layout {\n  display: flex;\n  height: calc(100vh - 100px); /* 为更高的底部输入框留出空间 */\n  overflow: hidden;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  background-color: #f9f9f9;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n\n}\n\n.logo h2 {\n  margin: 0;\n    font-size: 25px;\n    text-align: center;\n    white-space: nowrap;\n    letter-spacing: 2px;\n    /* 字体变粗 */\n    font-weight: 900;\n}\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  overflow-x: auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n  height: 100%; /* 确保主内容区域占满高度 */\n}\n\n.header {\n  margin-bottom: 20px;\n  \n}\n\n.header h2 {\n  margin: 0;\n  font-size: 30px;\n}\n\n.highlight {\n  color: #409eff;\n}\n\n.sub-title {\n  color: #606266;\n  font-size: 14px;\n  margin: 5px 0 0 0;\n}\n\n.data-card {\n  cursor: pointer;\n  margin-bottom: 15px;\n  transition: all 0.3s;\n}\n\n.data-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.data-header {\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.sample-tag {\n  background-color: #ecf5ff;\n  color: #409eff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n.data-fields {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.field-item {\n  background-color: #f5f7fa;\n  padding: 2px 8px;\n  margin: 4px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n/* 底部问题输入区域 */\n.question-input-container {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  background-color: #fff;\n  border-top: 1px solid #ebeef5;\n  padding: 25px 15px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.question-input-container > span {\n  margin-bottom: 12px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.question-input-wrapper {\n  /* display: flex; */\n  align-items: center;\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n  background-color: #f5f7fa;\n  border-radius: 20px;\n  padding: 8px 15px;\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: #fff;\n  border: 1px solid #dcdfe6;\n  margin-left: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n}\n\n.send-btn {\n  background-color: #409eff;\n  color: white;\n  border: none;\n}\n\n.test-btn {\n  background-color: #67c23a;\n  color: white;\n  border: none;\n}\n\n.debug-btn {\n  background-color: #909399;\n  color: white;\n  border: none;\n}\n\n.send-btn:disabled {\n  background-color: #c0c4cc;\n  cursor: not-allowed;\n}\n\n/* 建议问题面板 */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n\n/* 消息列表样式 */\n.message-list {\n  width: 100%;\n  max-height: calc(100vh - 250px); /* 修改为响应式高度，减去头部和底部的高度 */\n  overflow-y: auto;\n  margin-top: 20px;\n  padding: 10px;\n  background-color: transparent;\n  border-radius: 8px;\n  flex: 1; /* 让消息列表占据剩余空间 */\n}\n\n.message {\n  margin-bottom: 20px;\n  display: flex;\n  position: relative;\n  max-width: 80%;\n}\n\n.message-content {\n  padding: 12px 15px;\n  border-radius: 6px;\n  line-height: 1.5;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n  align-self: flex-end;\n  margin-left: auto;\n}\n\n.user-message .message-content {\n  background-color: #ecf5ff;\n  margin-right: 10px;\n}\n\n.bot-message {\n  align-self: flex-start;\n}\n\n.bot-message .message-content {\n  background-color: #f5f7fa;\n  margin-left: 10px;\n}\n\n.avatar-container {\n  display: flex;\n  align-items: flex-start;\n}\n\n.bot-avatar, .user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bot-avatar {\n  background-color: #4caf50;\n  color: white;\n}\n\n.user-avatar {\n  background-color: #2196f3;\n  color: white;\n}\n\n/* 加载动画 */\n.loading-dots {\n  display: inline-block;\n}\n\n.dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  background-color: #999;\n  border-radius: 50%;\n  margin: 0 2px;\n  animation: pulse 1.2s infinite ease-in-out both;\n}\n\n.dot:nth-child(2) {\n  animation-delay: -0.4s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: -0.8s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(0.6);\n    opacity: 0.4;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* 图表相关样式 */\n.chart-container {\n  margin-top: 15px;\n  margin-bottom: 15px;\n  border: 1px solid #e6e6e6;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.chart-notice {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n  font-weight: bold;\n}\n\n/* AI原始响应弹出层样式 */\n.raw-response-panel {\n  position: fixed;\n  bottom: 100px; /* 调整位置，使其不与底部输入框重叠 */\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 800px;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n  z-index: 1000;\n  max-height: 60%; /* 限制弹出层高度 */\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.raw-response-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 16px;\n  color: #303133;\n  background-color: #f5f7fa;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.raw-response-title .el-icon-monitor {\n  margin-right: 8px;\n}\n\n.raw-response-title .close-btn {\n  background-color: #f5f7fa;\n  border: 1px solid #ebeef5;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.raw-response-title .close-btn:hover {\n  background-color: #ebeef5;\n}\n\n.raw-response-content {\n  flex: 1;\n  padding: 15px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #303133;\n  white-space: pre-wrap; /* 保留换行和空格 */\n  word-break: break-all; /* 允许在单词中间换行 */\n  overflow-wrap: break-word; /* 允许在单词中间换行 */\n}\n</style>"], "mappings": ";AAkLA,SAAAA,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;MACAC,oBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,MAAA;MAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,UAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;IACA;IACA,KAAAf,kBAAA,IACA,gBACA,kBACA,kBACA,mBACA;EACA;EACAgB,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,eAAA;MACA,KAAAT,MAAA;IACA;IACA;IACAI,aAAA;MACA,IAAAM,cAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,KAAAF,cAAA;QACAA,cAAA,QAAAG,YAAA,CAAApC,MAAA;QACAkC,YAAA,CAAAG,OAAA,mBAAAJ,cAAA;MACA;MACA,KAAAf,QAAA,GAAAe,cAAA;IACA;IAEA;IACAG,aAAAE,IAAA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAG,MAAA,IAAAD,CAAA,MAAAA,CAAA;QACA,MAAAE,QAAA,GAAAJ,IAAA,CAAAE,CAAA;QACAD,MAAA,GAAAA,MAAA,SAAAI,QAAA,CAAAD,QAAA;MACA;MACA,OAAAH,MAAA;IACA;IAEA;IACAX,kBAAA;MACA,KAAAX,QAAA,CAAA2B,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IAEA;IACAjB,eAAA;MACA,SAAAkB,KAAA,CAAA5B,cAAA;QACA,KAAA4B,KAAA,CAAA5B,cAAA,CAAA6B,SAAA,QAAAD,KAAA,CAAA5B,cAAA,CAAA8B,YAAA;MACA;IACA;IAEA,MAAAxB,WAAA;MACA;QACA,MAAAyB,QAAA,SAAArD,OAAA,CAAAsD,YAAA;QACA,KAAA5C,MAAA,GAAA2C,QAAA,CAAA9C,IAAA;MACA,SAAAgD,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,CAAAA,KAAA;MACA;IACA;IACA,MAAAG,YAAAC,KAAA;MACA;QACA,KAAAzC,aAAA;QACAuC,OAAA,CAAAG,GAAA,CAAAD,KAAA;QACA,MAAAN,QAAA,SAAArD,OAAA,CAAA6D,kBAAA,CAAAF,KAAA;QACA,KAAA1C,eAAA,GAAAoC,QAAA,CAAA9C,IAAA;QACAkD,OAAA,CAAAG,GAAA,MAAA3C,eAAA;MACA,SAAAsC,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,CAAAA,KAAA;MACA;IACA;IACAO,gBAAA;MACA,KAAAhD,oBAAA,SAAAA,oBAAA;IACA;IACAiD,YAAAC,CAAA;MACA,KAAApD,QAAA,GAAAoD,CAAA;MACA,KAAAlD,oBAAA;IACA;IACAmD,eAAAN,KAAA;MACA,IAAAA,KAAA,CAAAO,SAAA;QACA;MACA;MACA;IACA;IAEA;IACA,MAAAC,eAAA;MACA,UAAAvD,QAAA,CAAAwD,IAAA,WAAA/C,SAAA;QACA,KAAAmC,QAAA,CAAAa,OAAA;QACA;MACA;;MAEA;MACA,MAAAC,cAAA,QAAA3D,aAAA,GACA,KAAAA,aAAA,CAAA4D,SAAA,GACA,KAAA7D,MAAA,CAAAiC,MAAA,YAAAjC,MAAA,IAAA6D,SAAA;;MAEA;MACA,MAAAC,OAAA;QACAzB,MAAA;QACAC,OAAA,OAAApC,QAAA,CAAAwD,IAAA;QACAnB,QAAA;MACA;MACA,KAAA9B,QAAA,CAAA2B,IAAA,CAAA0B,OAAA;;MAEA;MACA,MAAAC,MAAA;QACA1B,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAA9B,QAAA,CAAA2B,IAAA,CAAA2B,MAAA;;MAEA;MACA,MAAAC,OAAA,QAAAvD,QAAA,MAAAA,QAAA,CAAAwB,MAAA;;MAEA;MACA,MAAA/B,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA;MACA,KAAAS,SAAA;MAEA;QACA;QACA,MAAAsD,OAAA,MAAA/D,QAAA,WAAA0D,cAAA;;QAEA;QACA,MAAAnE,KAAA,CAAAyE,IAAA,CACA,4CACA;UAAAxD,QAAA,OAAAA,QAAA;UAAAuD;QAAA,GACA;UACAE,YAAA;UACAC,kBAAA,EAAAC,CAAA;YACA,MAAAC,QAAA,GAAAD,CAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAC,YAAA;YACA,IAAAC,OAAA,GAAAJ,QAAA,CAAAK,SAAA,CAAAX,OAAA,CAAA1B,OAAA,CAAAL,MAAA;YACA+B,OAAA,CAAA1B,OAAA,IAAAoC,OAAA;YACA,KAAApD,cAAA;;YAEA;YACA,KAAAR,eAAA,GAAAwD,QAAA;UACA;QACA,CACA;;QAEA;QACAN,OAAA,CAAAzB,QAAA;QACA,KAAA5B,SAAA;;QAEA;QACA,KAAAiE,gBAAA,CAAAZ,OAAA;MACA,SAAAnB,KAAA;QACAE,OAAA,CAAAF,KAAA,UAAAA,KAAA;QACAmB,OAAA,CAAA1B,OAAA;QACA0B,OAAA,CAAAzB,QAAA;QACA,KAAA5B,SAAA;MACA;IACA;IAEA;IACAiE,iBAAAX,OAAA;MACAlB,OAAA,CAAAG,GAAA,qBAAAe,OAAA,CAAA3B,OAAA;;MAEA;MACA,MAAAuC,gBAAA,GAAAZ,OAAA,CAAA3B,OAAA,CAAAwC,KAAA;MACA,IAAAD,gBAAA,IAAAA,gBAAA;QACA,MAAAE,WAAA,GAAAF,gBAAA;QACA9B,OAAA,CAAAG,GAAA,cAAA6B,WAAA;;QAEA;QACA,KAAAC,kBAAA,CAAAD,WAAA,EAAAd,OAAA;QACA;MACA;;MAEA;MACA,MAAAgB,gBAAA,GAAAhB,OAAA,CAAA3B,OAAA,CAAAwC,KAAA;MACA,IAAAG,gBAAA,IAAAA,gBAAA;QACAlC,OAAA,CAAAG,GAAA,eAAA+B,gBAAA;QAEA;UACA,IAAAC,WAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,gBAAA;UACAlC,OAAA,CAAAG,GAAA,gBAAAgC,WAAA;;UAEA;UACA,IAAAA,WAAA,CAAAG,IAAA,UAAAH,WAAA,CAAArF,IAAA;YACAkD,OAAA,CAAAG,GAAA,yBAAAgC,WAAA,CAAArF,IAAA;YACAqF,WAAA,GAAAA,WAAA,CAAArF,IAAA;UACA;;UAEA;UACA,IAAAqF,WAAA,CAAAI,IAAA,KAAAJ,WAAA,CAAAK,SAAA,IAAAL,WAAA,CAAAM,OAAA,IAAAN,WAAA,CAAArF,IAAA;YACAkD,OAAA,CAAAG,GAAA,eAAAgC,WAAA;YACAjB,OAAA,CAAAiB,WAAA,GAAAA,WAAA;;YAEA;YACAjB,OAAA,CAAA3B,OAAA,GAAA2B,OAAA,CAAA3B,OAAA,CAAAmD,OAAA,6BACA;YAEA;UACA;YACA1C,OAAA,CAAAG,GAAA,gBAAAgC,WAAA;UACA;QACA,SAAArC,KAAA;UACAE,OAAA,CAAAF,KAAA,cAAAA,KAAA;QACA;MACA;MAEAE,OAAA,CAAAG,GAAA;;MAEA;MACA;QACA;QACA,MAAAwC,SAAA;QACA,MAAAC,SAAA,GAAA1B,OAAA,CAAA3B,OAAA,CAAAwC,KAAA,CAAAY,SAAA;QAEA,IAAAC,SAAA;UACA5C,OAAA,CAAAG,GAAA,kBAAAyC,SAAA;;UAEA;UACA;UACA,MAAAC,OAAA,GAAAD,SAAA,IAAAF,OAAA;UACA1C,OAAA,CAAAG,GAAA,iBAAA0C,OAAA;UAEA;YACA,MAAAV,WAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAQ,OAAA;YACA7C,OAAA,CAAAG,GAAA,gBAAAgC,WAAA;YAEA,IAAAA,WAAA,CAAAG,IAAA,UAAAH,WAAA,CAAArF,IAAA;cACA,MAAAA,IAAA,GAAAqF,WAAA,CAAArF,IAAA;cACAkD,OAAA,CAAAG,GAAA,aAAArD,IAAA;;cAEA;cACA,IAAAA,IAAA,CAAAyF,IAAA,KAAAzF,IAAA,CAAA2F,OAAA,IAAA3F,IAAA,CAAAA,IAAA;gBACAkD,OAAA,CAAAG,GAAA,eAAArD,IAAA;gBACAoE,OAAA,CAAAiB,WAAA,GAAArF,IAAA;gBACAoE,OAAA,CAAA3B,OAAA,GAAA2B,OAAA,CAAA3B,OAAA,CAAAmD,OAAA,CAAAE,SAAA,KACA;gBACA;cACA;YACA;UACA,SAAAE,UAAA;YACA9C,OAAA,CAAAG,GAAA,cAAA2C,UAAA;UACA;QACA;MACA,SAAAhD,KAAA;QACAE,OAAA,CAAAG,GAAA,aAAAL,KAAA;MACA;;MAEA;MACA,IAAAoB,OAAA,CAAA3B,OAAA,CAAAwD,QAAA,iBACA7B,OAAA,CAAA3B,OAAA,CAAAwD,QAAA;QACA/C,OAAA,CAAAG,GAAA;;QAEA;QACA,MAAA6C,aAAA;UACAC,EAAA,EAAAC,IAAA,CAAAC,GAAA,GAAAC,QAAA;UACAb,IAAA;UACAc,KAAA;UACAZ,OAAA,OAAAvF,aAAA,EAAA+F,EAAA;QACA;QAEAjD,OAAA,CAAAG,GAAA,eAAA6C,aAAA;QACA9B,OAAA,CAAAiB,WAAA,GAAAa,aAAA;MACA;IACA;IAEA;IACA,MAAAf,mBAAAD,WAAA,EAAAd,OAAA;MACA;QACAlB,OAAA,CAAAG,GAAA,qBAAA6B,WAAA;QACA,MAAApC,QAAA,SAAAlD,KAAA,CAAA4G,GAAA,yCAAAtB,WAAA;QAEA,IAAApC,QAAA,CAAA9C,IAAA,IAAA8C,QAAA,CAAA2D,MAAA;UACAvD,OAAA,CAAAG,GAAA,cAAAP,QAAA,CAAA9C,IAAA;;UAEA;UACA,IAAAqF,WAAA,GAAAvC,QAAA,CAAA9C,IAAA;UACA,IAAAqF,WAAA,CAAAG,IAAA,UAAAH,WAAA,CAAArF,IAAA;YACAqF,WAAA,GAAAA,WAAA,CAAArF,IAAA;UACA;;UAEA;UACAoE,OAAA,CAAAiB,WAAA,GAAAA,WAAA;;UAEA;UACA,KAAAjB,OAAA,CAAA3B,OAAA,CAAAwD,QAAA;YACA7B,OAAA,CAAA3B,OAAA;UACA;;UAEA;UACA,KAAAiE,YAAA;QACA;UACAxD,OAAA,CAAAF,KAAA,cAAAF,QAAA;UACAsB,OAAA,CAAA3B,OAAA;QACA;MACA,SAAAO,KAAA;QACAE,OAAA,CAAAF,KAAA,cAAAA,KAAA;QACAoB,OAAA,CAAA3B,OAAA,0CAAAO,KAAA,CAAAoB,OAAA;MACA;IACA;IAEA;IACAuC,UAAA;MACA;MACA,MAAAC,eAAA;QACAT,EAAA;QACAV,IAAA;QACAc,KAAA;QACAb,SAAA;QACAC,OAAA;MACA;;MAEA;MACA,MAAAzB,MAAA;QACA1B,MAAA;QACAC,OAAA;QACAC,QAAA;QACA2C,WAAA,EAAAuB;MACA;MAEA,KAAAhG,QAAA,CAAA2B,IAAA,CAAA2B,MAAA;;MAEA;MACA,KAAAjD,eAAA,GAAAqE,IAAA,CAAAuB,SAAA,CAAAD,eAAA;;MAEA;MACA1D,OAAA,CAAAG,GAAA,iBAAAzC,QAAA;;MAEA;MACA,MAAAkG,eAAA;QACAtB,IAAA;QACAuB,GAAA;QACA/G,IAAA;UACAyF,IAAA;UACAzF,IAAA,GACA;YAAAgH,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,EACA;UACAC,OAAA;YAAApH,IAAA;UAAA;QACA;MACA;;MAEA;MACA,MAAAqH,WAAA,iBAAA7B,IAAA,CAAAuB,SAAA,CAAAC,eAAA;MACA,MAAAM,cAAA;QACA5E,MAAA;QACAC,OAAA,gBAAA0E,WAAA;QACAzE,QAAA;MACA;MAEA,KAAA9B,QAAA,CAAA2B,IAAA,CAAA6E,cAAA;MACA,KAAArC,gBAAA,CAAAqC,cAAA;;MAEA;MACA,KAAAnG,eAAA,GAAAkG,WAAA;MAEAjE,OAAA,CAAAG,GAAA,iBAAA+D,cAAA;IACA;IAEA;IACAC,aAAA;MACAnE,OAAA,CAAAG,GAAA;;MAEA;MACA,MAAAiE,QAAA;QACA9B,IAAA;QACAuB,GAAA;QACA/G,IAAA;UACAmG,EAAA;UACAI,KAAA;UACAZ,OAAA;UACAF,IAAA;UACAzF,IAAA;YACAA,IAAA,GACA;cAAAiH,KAAA;cAAAD,KAAA;cAAAlH,IAAA;cAAAyH,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAD,KAAA;cAAAlH,IAAA;cAAAyH,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAD,KAAA;cAAAlH,IAAA;cAAAyH,QAAA;YAAA,EACA;YACAC,MAAA,GACA;cAAArB,EAAA;cAAArG,IAAA;cAAA2H,SAAA;YAAA,GACA;cAAAtB,EAAA;cAAArG,IAAA;cAAA2H,SAAA;YAAA;UAEA;QACA;MACA;;MAEA;MACA,MAAAC,WAAA;QACAlF,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MAEA;MACAgF,WAAA,CAAArC,WAAA,GAAAiC,QAAA,CAAAtH,IAAA;;MAEA;MACA,KAAAY,QAAA,CAAA2B,IAAA,CAAAmF,WAAA;;MAEA;MACA,KAAAzG,eAAA,GAAAqE,IAAA,CAAAuB,SAAA,CAAAS,QAAA;MAEApE,OAAA,CAAAG,GAAA,gBAAAqE,WAAA;IACA;IAEA;IACAC,gBAAA;MACA,KAAA3G,oBAAA;MACA,KAAAC,eAAA,QAAAL,QAAA,MAAAA,QAAA,CAAAwB,MAAA,MAAAK,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}