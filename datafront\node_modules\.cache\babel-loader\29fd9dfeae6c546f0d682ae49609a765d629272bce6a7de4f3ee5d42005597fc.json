{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nexport default function sankeyVisual(ecModel) {\n  ecModel.eachSeriesByType('sankey', function (seriesModel) {\n    var graph = seriesModel.getGraph();\n    var nodes = graph.nodes;\n    var edges = graph.edges;\n    if (nodes.length) {\n      var minValue_1 = Infinity;\n      var maxValue_1 = -Infinity;\n      zrUtil.each(nodes, function (node) {\n        var nodeValue = node.getLayout().value;\n        if (nodeValue < minValue_1) {\n          minValue_1 = nodeValue;\n        }\n        if (nodeValue > maxValue_1) {\n          maxValue_1 = nodeValue;\n        }\n      });\n      zrUtil.each(nodes, function (node) {\n        var mapping = new VisualMapping({\n          type: 'color',\n          mappingMethod: 'linear',\n          dataExtent: [minValue_1, maxValue_1],\n          visual: seriesModel.get('color')\n        });\n        var mapValueToColor = mapping.mapValueToVisual(node.getLayout().value);\n        var customColor = node.getModel().get(['itemStyle', 'color']);\n        if (customColor != null) {\n          node.setVisual('color', customColor);\n          node.setVisual('style', {\n            fill: customColor\n          });\n        } else {\n          node.setVisual('color', mapValueToColor);\n          node.setVisual('style', {\n            fill: mapValueToColor\n          });\n        }\n      });\n    }\n    if (edges.length) {\n      zrUtil.each(edges, function (edge) {\n        var edgeStyle = edge.getModel().get('lineStyle');\n        edge.setVisual('style', edgeStyle);\n      });\n    }\n  });\n}", "map": {"version": 3, "names": ["zrUtil", "VisualMapping", "sankeyVisual", "ecModel", "eachSeriesByType", "seriesModel", "graph", "getGraph", "nodes", "edges", "length", "minValue_1", "Infinity", "maxValue_1", "each", "node", "nodeValue", "getLayout", "value", "mapping", "type", "mappingMethod", "dataExtent", "visual", "get", "mapValueToColor", "mapValueToVisual", "customColor", "getModel", "setVisual", "fill", "edge", "edgeStyle"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/sankey/sankeyVisual.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nexport default function sankeyVisual(ecModel) {\n  ecModel.eachSeriesByType('sankey', function (seriesModel) {\n    var graph = seriesModel.getGraph();\n    var nodes = graph.nodes;\n    var edges = graph.edges;\n    if (nodes.length) {\n      var minValue_1 = Infinity;\n      var maxValue_1 = -Infinity;\n      zrUtil.each(nodes, function (node) {\n        var nodeValue = node.getLayout().value;\n        if (nodeValue < minValue_1) {\n          minValue_1 = nodeValue;\n        }\n        if (nodeValue > maxValue_1) {\n          maxValue_1 = nodeValue;\n        }\n      });\n      zrUtil.each(nodes, function (node) {\n        var mapping = new VisualMapping({\n          type: 'color',\n          mappingMethod: 'linear',\n          dataExtent: [minValue_1, maxValue_1],\n          visual: seriesModel.get('color')\n        });\n        var mapValueToColor = mapping.mapValueToVisual(node.getLayout().value);\n        var customColor = node.getModel().get(['itemStyle', 'color']);\n        if (customColor != null) {\n          node.setVisual('color', customColor);\n          node.setVisual('style', {\n            fill: customColor\n          });\n        } else {\n          node.setVisual('color', mapValueToColor);\n          node.setVisual('style', {\n            fill: mapValueToColor\n          });\n        }\n      });\n    }\n    if (edges.length) {\n      zrUtil.each(edges, function (edge) {\n        var edgeStyle = edge.getModel().get('lineStyle');\n        edge.setVisual('style', edgeStyle);\n      });\n    }\n  });\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,eAAe,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC5CA,OAAO,CAACC,gBAAgB,CAAC,QAAQ,EAAE,UAAUC,WAAW,EAAE;IACxD,IAAIC,KAAK,GAAGD,WAAW,CAACE,QAAQ,CAAC,CAAC;IAClC,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACvB,IAAIC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACvB,IAAID,KAAK,CAACE,MAAM,EAAE;MAChB,IAAIC,UAAU,GAAGC,QAAQ;MACzB,IAAIC,UAAU,GAAG,CAACD,QAAQ;MAC1BZ,MAAM,CAACc,IAAI,CAACN,KAAK,EAAE,UAAUO,IAAI,EAAE;QACjC,IAAIC,SAAS,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC,CAACC,KAAK;QACtC,IAAIF,SAAS,GAAGL,UAAU,EAAE;UAC1BA,UAAU,GAAGK,SAAS;QACxB;QACA,IAAIA,SAAS,GAAGH,UAAU,EAAE;UAC1BA,UAAU,GAAGG,SAAS;QACxB;MACF,CAAC,CAAC;MACFhB,MAAM,CAACc,IAAI,CAACN,KAAK,EAAE,UAAUO,IAAI,EAAE;QACjC,IAAII,OAAO,GAAG,IAAIlB,aAAa,CAAC;UAC9BmB,IAAI,EAAE,OAAO;UACbC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,CAACX,UAAU,EAAEE,UAAU,CAAC;UACpCU,MAAM,EAAElB,WAAW,CAACmB,GAAG,CAAC,OAAO;QACjC,CAAC,CAAC;QACF,IAAIC,eAAe,GAAGN,OAAO,CAACO,gBAAgB,CAACX,IAAI,CAACE,SAAS,CAAC,CAAC,CAACC,KAAK,CAAC;QACtE,IAAIS,WAAW,GAAGZ,IAAI,CAACa,QAAQ,CAAC,CAAC,CAACJ,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QAC7D,IAAIG,WAAW,IAAI,IAAI,EAAE;UACvBZ,IAAI,CAACc,SAAS,CAAC,OAAO,EAAEF,WAAW,CAAC;UACpCZ,IAAI,CAACc,SAAS,CAAC,OAAO,EAAE;YACtBC,IAAI,EAAEH;UACR,CAAC,CAAC;QACJ,CAAC,MAAM;UACLZ,IAAI,CAACc,SAAS,CAAC,OAAO,EAAEJ,eAAe,CAAC;UACxCV,IAAI,CAACc,SAAS,CAAC,OAAO,EAAE;YACtBC,IAAI,EAAEL;UACR,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IACA,IAAIhB,KAAK,CAACC,MAAM,EAAE;MAChBV,MAAM,CAACc,IAAI,CAACL,KAAK,EAAE,UAAUsB,IAAI,EAAE;QACjC,IAAIC,SAAS,GAAGD,IAAI,CAACH,QAAQ,CAAC,CAAC,CAACJ,GAAG,CAAC,WAAW,CAAC;QAChDO,IAAI,CAACF,SAAS,CAAC,OAAO,EAAEG,SAAS,CAAC;MACpC,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}