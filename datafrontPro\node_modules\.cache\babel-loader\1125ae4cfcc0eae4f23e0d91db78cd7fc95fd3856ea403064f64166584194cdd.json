{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport { each, isObject, clone } from 'zrender/lib/core/util.js';\nimport { convertOptionIdName, getDataItemValue } from '../../util/model.js';\nvar TimelineModel = /** @class */function (_super) {\n  __extends(TimelineModel, _super);\n  function TimelineModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TimelineModel.type;\n    _this.layoutMode = 'box';\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TimelineModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n    this._initData();\n  };\n  /**\r\n   * @override\r\n   */\n  TimelineModel.prototype.mergeOption = function (option) {\n    _super.prototype.mergeOption.apply(this, arguments);\n    this._initData();\n  };\n  TimelineModel.prototype.setCurrentIndex = function (currentIndex) {\n    if (currentIndex == null) {\n      currentIndex = this.option.currentIndex;\n    }\n    var count = this._data.count();\n    if (this.option.loop) {\n      currentIndex = (currentIndex % count + count) % count;\n    } else {\n      currentIndex >= count && (currentIndex = count - 1);\n      currentIndex < 0 && (currentIndex = 0);\n    }\n    this.option.currentIndex = currentIndex;\n  };\n  /**\r\n   * @return {number} currentIndex\r\n   */\n  TimelineModel.prototype.getCurrentIndex = function () {\n    return this.option.currentIndex;\n  };\n  /**\r\n   * @return {boolean}\r\n   */\n  TimelineModel.prototype.isIndexMax = function () {\n    return this.getCurrentIndex() >= this._data.count() - 1;\n  };\n  /**\r\n   * @param {boolean} state true: play, false: stop\r\n   */\n  TimelineModel.prototype.setPlayState = function (state) {\n    this.option.autoPlay = !!state;\n  };\n  /**\r\n   * @return {boolean} true: play, false: stop\r\n   */\n  TimelineModel.prototype.getPlayState = function () {\n    return !!this.option.autoPlay;\n  };\n  /**\r\n   * @private\r\n   */\n  TimelineModel.prototype._initData = function () {\n    var thisOption = this.option;\n    var dataArr = thisOption.data || [];\n    var axisType = thisOption.axisType;\n    var names = this._names = [];\n    var processedDataArr;\n    if (axisType === 'category') {\n      processedDataArr = [];\n      each(dataArr, function (item, index) {\n        var value = convertOptionIdName(getDataItemValue(item), '');\n        var newItem;\n        if (isObject(item)) {\n          newItem = clone(item);\n          newItem.value = index;\n        } else {\n          newItem = index;\n        }\n        processedDataArr.push(newItem);\n        names.push(value);\n      });\n    } else {\n      processedDataArr = dataArr;\n    }\n    var dimType = {\n      category: 'ordinal',\n      time: 'time',\n      value: 'number'\n    }[axisType] || 'number';\n    var data = this._data = new SeriesData([{\n      name: 'value',\n      type: dimType\n    }], this);\n    data.initData(processedDataArr, names);\n  };\n  TimelineModel.prototype.getData = function () {\n    return this._data;\n  };\n  /**\r\n   * @public\r\n   * @return {Array.<string>} categoreis\r\n   */\n  TimelineModel.prototype.getCategories = function () {\n    if (this.get('axisType') === 'category') {\n      return this._names.slice();\n    }\n  };\n  TimelineModel.type = 'timeline';\n  /**\r\n   * @protected\r\n   */\n  TimelineModel.defaultOption = {\n    // zlevel: 0,                  // 一级层叠\n    z: 4,\n    show: true,\n    axisType: 'time',\n    realtime: true,\n    left: '20%',\n    top: null,\n    right: '20%',\n    bottom: 0,\n    width: null,\n    height: 40,\n    padding: 5,\n    controlPosition: 'left',\n    autoPlay: false,\n    rewind: false,\n    loop: true,\n    playInterval: 2000,\n    currentIndex: 0,\n    itemStyle: {},\n    label: {\n      color: '#000'\n    },\n    data: []\n  };\n  return TimelineModel;\n}(ComponentModel);\nexport default TimelineModel;", "map": {"version": 3, "names": ["__extends", "ComponentModel", "SeriesData", "each", "isObject", "clone", "convertOptionIdName", "getDataItemValue", "TimelineModel", "_super", "_this", "apply", "arguments", "type", "layoutMode", "prototype", "init", "option", "parentModel", "ecModel", "mergeDefaultAndTheme", "_initData", "mergeOption", "setCurrentIndex", "currentIndex", "count", "_data", "loop", "getCurrentIndex", "isIndexMax", "setPlayState", "state", "autoPlay", "getPlayState", "thisOption", "dataArr", "data", "axisType", "names", "_names", "processedDataArr", "item", "index", "value", "newItem", "push", "dimType", "category", "time", "name", "initData", "getData", "getCategories", "get", "slice", "defaultOption", "z", "show", "realtime", "left", "top", "right", "bottom", "width", "height", "padding", "controlPosition", "rewind", "playInterval", "itemStyle", "label", "color"], "sources": ["E:/frontCodeCode/datafront/node_modules/echarts/lib/component/timeline/TimelineModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport { each, isObject, clone } from 'zrender/lib/core/util.js';\nimport { convertOptionIdName, getDataItemValue } from '../../util/model.js';\nvar TimelineModel = /** @class */function (_super) {\n  __extends(TimelineModel, _super);\n  function TimelineModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TimelineModel.type;\n    _this.layoutMode = 'box';\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TimelineModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n    this._initData();\n  };\n  /**\r\n   * @override\r\n   */\n  TimelineModel.prototype.mergeOption = function (option) {\n    _super.prototype.mergeOption.apply(this, arguments);\n    this._initData();\n  };\n  TimelineModel.prototype.setCurrentIndex = function (currentIndex) {\n    if (currentIndex == null) {\n      currentIndex = this.option.currentIndex;\n    }\n    var count = this._data.count();\n    if (this.option.loop) {\n      currentIndex = (currentIndex % count + count) % count;\n    } else {\n      currentIndex >= count && (currentIndex = count - 1);\n      currentIndex < 0 && (currentIndex = 0);\n    }\n    this.option.currentIndex = currentIndex;\n  };\n  /**\r\n   * @return {number} currentIndex\r\n   */\n  TimelineModel.prototype.getCurrentIndex = function () {\n    return this.option.currentIndex;\n  };\n  /**\r\n   * @return {boolean}\r\n   */\n  TimelineModel.prototype.isIndexMax = function () {\n    return this.getCurrentIndex() >= this._data.count() - 1;\n  };\n  /**\r\n   * @param {boolean} state true: play, false: stop\r\n   */\n  TimelineModel.prototype.setPlayState = function (state) {\n    this.option.autoPlay = !!state;\n  };\n  /**\r\n   * @return {boolean} true: play, false: stop\r\n   */\n  TimelineModel.prototype.getPlayState = function () {\n    return !!this.option.autoPlay;\n  };\n  /**\r\n   * @private\r\n   */\n  TimelineModel.prototype._initData = function () {\n    var thisOption = this.option;\n    var dataArr = thisOption.data || [];\n    var axisType = thisOption.axisType;\n    var names = this._names = [];\n    var processedDataArr;\n    if (axisType === 'category') {\n      processedDataArr = [];\n      each(dataArr, function (item, index) {\n        var value = convertOptionIdName(getDataItemValue(item), '');\n        var newItem;\n        if (isObject(item)) {\n          newItem = clone(item);\n          newItem.value = index;\n        } else {\n          newItem = index;\n        }\n        processedDataArr.push(newItem);\n        names.push(value);\n      });\n    } else {\n      processedDataArr = dataArr;\n    }\n    var dimType = {\n      category: 'ordinal',\n      time: 'time',\n      value: 'number'\n    }[axisType] || 'number';\n    var data = this._data = new SeriesData([{\n      name: 'value',\n      type: dimType\n    }], this);\n    data.initData(processedDataArr, names);\n  };\n  TimelineModel.prototype.getData = function () {\n    return this._data;\n  };\n  /**\r\n   * @public\r\n   * @return {Array.<string>} categoreis\r\n   */\n  TimelineModel.prototype.getCategories = function () {\n    if (this.get('axisType') === 'category') {\n      return this._names.slice();\n    }\n  };\n  TimelineModel.type = 'timeline';\n  /**\r\n   * @protected\r\n   */\n  TimelineModel.defaultOption = {\n    // zlevel: 0,                  // 一级层叠\n    z: 4,\n    show: true,\n    axisType: 'time',\n    realtime: true,\n    left: '20%',\n    top: null,\n    right: '20%',\n    bottom: 0,\n    width: null,\n    height: 40,\n    padding: 5,\n    controlPosition: 'left',\n    autoPlay: false,\n    rewind: false,\n    loop: true,\n    playInterval: 2000,\n    currentIndex: 0,\n    itemStyle: {},\n    label: {\n      color: '#000'\n    },\n    data: []\n  };\n  return TimelineModel;\n}(ComponentModel);\nexport default TimelineModel;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,IAAI,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,0BAA0B;AAChE,SAASC,mBAAmB,EAAEC,gBAAgB,QAAQ,qBAAqB;AAC3E,IAAIC,aAAa,GAAG,aAAa,UAAUC,MAAM,EAAE;EACjDT,SAAS,CAACQ,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,aAAa,CAACK,IAAI;IAC/BH,KAAK,CAACI,UAAU,GAAG,KAAK;IACxB,OAAOJ,KAAK;EACd;EACA;AACF;AACA;EACEF,aAAa,CAACO,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACrE,IAAI,CAACC,oBAAoB,CAACH,MAAM,EAAEE,OAAO,CAAC;IAC1C,IAAI,CAACE,SAAS,CAAC,CAAC;EAClB,CAAC;EACD;AACF;AACA;EACEb,aAAa,CAACO,SAAS,CAACO,WAAW,GAAG,UAAUL,MAAM,EAAE;IACtDR,MAAM,CAACM,SAAS,CAACO,WAAW,CAACX,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACnD,IAAI,CAACS,SAAS,CAAC,CAAC;EAClB,CAAC;EACDb,aAAa,CAACO,SAAS,CAACQ,eAAe,GAAG,UAAUC,YAAY,EAAE;IAChE,IAAIA,YAAY,IAAI,IAAI,EAAE;MACxBA,YAAY,GAAG,IAAI,CAACP,MAAM,CAACO,YAAY;IACzC;IACA,IAAIC,KAAK,GAAG,IAAI,CAACC,KAAK,CAACD,KAAK,CAAC,CAAC;IAC9B,IAAI,IAAI,CAACR,MAAM,CAACU,IAAI,EAAE;MACpBH,YAAY,GAAG,CAACA,YAAY,GAAGC,KAAK,GAAGA,KAAK,IAAIA,KAAK;IACvD,CAAC,MAAM;MACLD,YAAY,IAAIC,KAAK,KAAKD,YAAY,GAAGC,KAAK,GAAG,CAAC,CAAC;MACnDD,YAAY,GAAG,CAAC,KAAKA,YAAY,GAAG,CAAC,CAAC;IACxC;IACA,IAAI,CAACP,MAAM,CAACO,YAAY,GAAGA,YAAY;EACzC,CAAC;EACD;AACF;AACA;EACEhB,aAAa,CAACO,SAAS,CAACa,eAAe,GAAG,YAAY;IACpD,OAAO,IAAI,CAACX,MAAM,CAACO,YAAY;EACjC,CAAC;EACD;AACF;AACA;EACEhB,aAAa,CAACO,SAAS,CAACc,UAAU,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACD,eAAe,CAAC,CAAC,IAAI,IAAI,CAACF,KAAK,CAACD,KAAK,CAAC,CAAC,GAAG,CAAC;EACzD,CAAC;EACD;AACF;AACA;EACEjB,aAAa,CAACO,SAAS,CAACe,YAAY,GAAG,UAAUC,KAAK,EAAE;IACtD,IAAI,CAACd,MAAM,CAACe,QAAQ,GAAG,CAAC,CAACD,KAAK;EAChC,CAAC;EACD;AACF;AACA;EACEvB,aAAa,CAACO,SAAS,CAACkB,YAAY,GAAG,YAAY;IACjD,OAAO,CAAC,CAAC,IAAI,CAAChB,MAAM,CAACe,QAAQ;EAC/B,CAAC;EACD;AACF;AACA;EACExB,aAAa,CAACO,SAAS,CAACM,SAAS,GAAG,YAAY;IAC9C,IAAIa,UAAU,GAAG,IAAI,CAACjB,MAAM;IAC5B,IAAIkB,OAAO,GAAGD,UAAU,CAACE,IAAI,IAAI,EAAE;IACnC,IAAIC,QAAQ,GAAGH,UAAU,CAACG,QAAQ;IAClC,IAAIC,KAAK,GAAG,IAAI,CAACC,MAAM,GAAG,EAAE;IAC5B,IAAIC,gBAAgB;IACpB,IAAIH,QAAQ,KAAK,UAAU,EAAE;MAC3BG,gBAAgB,GAAG,EAAE;MACrBrC,IAAI,CAACgC,OAAO,EAAE,UAAUM,IAAI,EAAEC,KAAK,EAAE;QACnC,IAAIC,KAAK,GAAGrC,mBAAmB,CAACC,gBAAgB,CAACkC,IAAI,CAAC,EAAE,EAAE,CAAC;QAC3D,IAAIG,OAAO;QACX,IAAIxC,QAAQ,CAACqC,IAAI,CAAC,EAAE;UAClBG,OAAO,GAAGvC,KAAK,CAACoC,IAAI,CAAC;UACrBG,OAAO,CAACD,KAAK,GAAGD,KAAK;QACvB,CAAC,MAAM;UACLE,OAAO,GAAGF,KAAK;QACjB;QACAF,gBAAgB,CAACK,IAAI,CAACD,OAAO,CAAC;QAC9BN,KAAK,CAACO,IAAI,CAACF,KAAK,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLH,gBAAgB,GAAGL,OAAO;IAC5B;IACA,IAAIW,OAAO,GAAG;MACZC,QAAQ,EAAE,SAAS;MACnBC,IAAI,EAAE,MAAM;MACZL,KAAK,EAAE;IACT,CAAC,CAACN,QAAQ,CAAC,IAAI,QAAQ;IACvB,IAAID,IAAI,GAAG,IAAI,CAACV,KAAK,GAAG,IAAIxB,UAAU,CAAC,CAAC;MACtC+C,IAAI,EAAE,OAAO;MACbpC,IAAI,EAAEiC;IACR,CAAC,CAAC,EAAE,IAAI,CAAC;IACTV,IAAI,CAACc,QAAQ,CAACV,gBAAgB,EAAEF,KAAK,CAAC;EACxC,CAAC;EACD9B,aAAa,CAACO,SAAS,CAACoC,OAAO,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACzB,KAAK;EACnB,CAAC;EACD;AACF;AACA;AACA;EACElB,aAAa,CAACO,SAAS,CAACqC,aAAa,GAAG,YAAY;IAClD,IAAI,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC,KAAK,UAAU,EAAE;MACvC,OAAO,IAAI,CAACd,MAAM,CAACe,KAAK,CAAC,CAAC;IAC5B;EACF,CAAC;EACD9C,aAAa,CAACK,IAAI,GAAG,UAAU;EAC/B;AACF;AACA;EACEL,aAAa,CAAC+C,aAAa,GAAG;IAC5B;IACAC,CAAC,EAAE,CAAC;IACJC,IAAI,EAAE,IAAI;IACVpB,QAAQ,EAAE,MAAM;IAChBqB,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,KAAK;IACXC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,CAAC;IACVC,eAAe,EAAE,MAAM;IACvBlC,QAAQ,EAAE,KAAK;IACfmC,MAAM,EAAE,KAAK;IACbxC,IAAI,EAAE,IAAI;IACVyC,YAAY,EAAE,IAAI;IAClB5C,YAAY,EAAE,CAAC;IACf6C,SAAS,EAAE,CAAC,CAAC;IACbC,KAAK,EAAE;MACLC,KAAK,EAAE;IACT,CAAC;IACDnC,IAAI,EAAE;EACR,CAAC;EACD,OAAO5B,aAAa;AACtB,CAAC,CAACP,cAAc,CAAC;AACjB,eAAeO,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}