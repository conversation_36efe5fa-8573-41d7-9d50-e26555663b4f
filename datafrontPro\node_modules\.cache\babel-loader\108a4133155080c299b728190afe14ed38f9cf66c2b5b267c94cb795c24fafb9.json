{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { dataApi } from './api/index.js';\nimport { v4 as uuidv4 } from 'uuid';\nimport axios from 'axios';\nimport ChartDisplay from './components/ChartDisplay.vue';\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas';\nimport { jsPDF } from 'jspdf';\nimport * as echarts from 'echarts';\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      // 原始树结构\n      datasets: [],\n      // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [],\n      // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false,\n      // 新增：控制原始响应弹出层\n      lastRawResponse: '',\n      // 新增：存储最后收到的原始响应\n      drawer: false,\n      //抽屉展示\n      direction: 'rtl',\n      //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      apiToken: 'eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2OTEyYzNiLTQ5ZmMtNDAyMC1iMzRiLWE3YWJlNmY1MTI0MSJ9.J4QCC6qh2GZ7ENDHuZQWQ1GvGOQ1HOr8gW34KXIHV9N_73Jppx-qmEwpq0AHlXM3DUjd5EAlpIVAvzJygm_ldg',\n      // 示例token，实际应该从登录后存储\n      currentAnalysisDataset: null,\n      // 存储当前用于智能分析的数据集信息\n      innerDrawer: false,\n      tableList: [],\n      exportingAll: false,\n      // 新增：控制完整对话导出状态\n\n      // 新增：数据集选择和预览相关状态\n      selectedDataset: null,\n      // 当前选择的数据集\n      showDatasetIndicator: false,\n      // 是否显示数据集指示器\n      previewCard: null,\n      // 当前预览的卡片\n      previewPopupStyle: {},\n      // 预览弹窗样式\n      isDatasetConfirmed: false,\n      // 数据集是否已确认选择\n      previewLoading: false // 预览数据加载状态\n    };\n  },\n  mounted() {\n    // 存储token到localStorage，供API使用\n    localStorage.setItem('de_token', this.apiToken);\n    this.loadTables();\n    this.initMemoryId();\n    this.addWelcomeMessage();\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\"帮我生成一个销售额柱状图\", \"展示近六个月的销售趋势折线图\", \"按照区域统计销售量并生成饼图\", \"帮我做一个按产品类别的销量对比图\"];\n  },\n  updated() {\n    this.scrollToBottom();\n  },\n  methods: {\n    SelectDataList() {\n      this.loadTables();\n      this.drawer = true;\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id');\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4());\n        localStorage.setItem('user_memory_id', storedMemoryId);\n      }\n      this.memoryId = storedMemoryId;\n    },\n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0;\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i];\n        number = number * 16 + (parseInt(hexValue, 16) || 0);\n      }\n      return number % 1000000;\n    },\n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      });\n    },\n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight;\n      }\n    },\n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables();\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data;\n          this.datasets = this.flattenDatasets(this.tables);\n          this.filteredDatasets = this.datasets;\n        } else {\n          this.tables = [];\n          this.datasets = [];\n          this.filteredDatasets = [];\n        }\n      } catch (e) {\n        this.tables = [];\n        this.datasets = [];\n        this.filteredDatasets = [];\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = [];\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node);\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children));\n        }\n      }\n      return result;\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase();\n      if (!keyword) {\n        this.filteredDatasets = this.datasets;\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword));\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n\n    // 测试详情请求方法\n    // async testDetailRequest() {\n    //   if (this.datasets.length > 0) {\n    //     const testDataset = this.datasets[0];\n    //     console.log('测试数据集:', testDataset);\n    //     alert(`正在请求数据集详情，ID: ${testDataset.id}`);\n    //     await this.showDatasetDetail(testDataset);\n    //   } else {\n    //     alert('没有可用的数据集');\n    //   }\n    // },\n\n    // 智能分析数据集 - 确认选择数据集\n    analyzeDataset() {\n      if (!this.currentDatasetDetail || !this.datasetFields.length) {\n        this.$message.warning('当前数据集没有可用字段');\n        return;\n      }\n\n      // 提取所需的字段信息\n      const fieldsInfo = this.datasetFields.map(field => {\n        // 只保留需要的字段属性\n        return {\n          id: field.id,\n          originName: field.originName || '',\n          name: field.name || '',\n          dataeaseName: field.dataeaseName || '',\n          groupType: field.groupType || '',\n          type: field.type || '',\n          datasourceId: field.datasourceId || '',\n          datasetTableId: field.datasetTableId || '',\n          datasetGroupId: field.datasetGroupId || ''\n        };\n      });\n\n      // 确认选择数据集\n      this.selectedDataset = {\n        id: this.currentDatasetDetail.id,\n        name: this.currentDatasetDetail.name,\n        fields: fieldsInfo\n      };\n\n      // 存储当前数据集信息，包括ID和字段\n      this.currentAnalysisDataset = this.selectedDataset;\n\n      // 显示数据集指示器，隐藏数据选择区域\n      this.showDatasetIndicator = true;\n      this.isDatasetConfirmed = true;\n\n      // 关闭详情抽屉，返回到聊天界面\n      this.dialogVisible = false;\n\n      // 提示用户\n      this.$message.success(`已选择数据集\"${this.currentDatasetDetail.name}\"进行智能分析，请在下方输入您的问题`);\n\n      // 自动聚焦到问题输入框\n      this.$nextTick(() => {\n        const inputEl = document.querySelector('.question-input input');\n        if (inputEl) inputEl.focus();\n      });\n    },\n    // 显示卡片预览\n    showCardPreview(table, event) {\n      this.previewCard = table;\n      const rect = event.currentTarget.getBoundingClientRect();\n      this.previewPopupStyle = {\n        position: 'fixed',\n        top: rect.top - 10 + 'px',\n        left: rect.right + 10 + 'px',\n        zIndex: 1000\n      };\n    },\n    // 隐藏卡片预览\n    hideCardPreview() {\n      this.previewCard = null;\n    },\n    // 预览数据集（从悬停弹窗触发）\n    previewDataset(table) {\n      this.hideCardPreview();\n      this.showDatasetDetail(table);\n    },\n    // 开始提问（从悬停弹窗触发）\n    startQuestionWithDataset(table) {\n      this.hideCardPreview();\n      // 直接选择数据集并确认\n      this.currentDatasetDetail = table;\n      this.datasetFields = table.fields || [];\n      this.analyzeDataset();\n    },\n    // 预览已选择的数据集\n    previewSelectedDataset() {\n      if (this.selectedDataset) {\n        this.showDatasetDetail(this.selectedDataset);\n      }\n    },\n    // 更换数据集\n    changeDataset() {\n      this.selectedDataset = null;\n      this.showDatasetIndicator = false;\n      this.isDatasetConfirmed = false;\n      this.currentAnalysisDataset = null;\n      this.$message.info('已取消数据集选择，请重新选择数据集');\n    },\n    // 获取推荐问题\n    getRecommendedQuestions() {\n      if (!this.currentDatasetDetail) return [];\n      const datasetName = this.currentDatasetDetail.name;\n      const dimensionFields = this.getDimensionFields();\n      const metricFields = this.getMetricFields();\n      const questions = [];\n      if (dimensionFields.length > 0 && metricFields.length > 0) {\n        questions.push(`上个月${dimensionFields[0].name}和${metricFields[0].name}的销售情况是多少？`);\n        questions.push(`上个月${dimensionFields[0].name}的${metricFields[0].name}是多少？`);\n      }\n      if (dimensionFields.length > 1) {\n        questions.push(`上个月${dimensionFields[0].name}的${dimensionFields[1].name}是多少？`);\n      }\n      questions.push(`上个月${datasetName}的销售数据是多少？`);\n      return questions.slice(0, 4);\n    },\n    // 使用推荐问题\n    useRecommendedQuestion(question) {\n      this.question = question;\n      this.analyzeDataset(); // 先确认选择数据集\n      this.$nextTick(() => {\n        this.submitQuestion(); // 然后提交问题\n      });\n    },\n    // 获取维度字段\n    getDimensionFields() {\n      return this.datasetFields.filter(field => field.groupType === 'd');\n    },\n    // 获取指标字段\n    getMetricFields() {\n      return this.datasetFields.filter(field => field.groupType === 'q');\n    },\n    // 获取其他字段\n    getOtherFields() {\n      return this.datasetFields.filter(field => !field.groupType || field.groupType !== 'd' && field.groupType !== 'q');\n    },\n    // 刷新预览数据\n    refreshPreviewData() {\n      if (this.currentDatasetDetail) {\n        this.previewLoading = true;\n        // 模拟刷新延迟\n        setTimeout(() => {\n          this.previewLoading = false;\n          this.$message.success('数据已刷新');\n        }, 1000);\n      }\n    },\n    async showDatasetDetail(dataset) {\n      // alert(`showDatasetDetail 被调用，数据集ID: ${dataset?.id || '未知'}`);\n      console.log('showDatasetDetail 被调用，参数:', dataset);\n\n      // 验证参数\n      if (!dataset || !dataset.id) {\n        console.error('无效的数据集参数:', dataset);\n        // alert('数据集参数无效，缺少ID');\n        return;\n      }\n\n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n      try {\n        console.log(`开始请求数据集详情，ID: ${dataset.id}`);\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n        console.log('数据集详情API响应:', res);\n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          console.log('数据集详情数据:', detail);\n          this.currentDatasetDetail = detail;\n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || detail.data && detail.data.fields || [];\n          // 数据内容\n          this.datasetData = detail.data && detail.data.data || [];\n          // 调试打印\n          console.log(`字段信息: ${this.datasetFields.length}个字段`);\n          console.log(`数据内容: ${this.datasetData.length}条记录`);\n          if (this.datasetFields.length === 0) {\n            console.warn('未找到字段信息');\n          }\n          if (this.datasetData.length === 0) {\n            console.warn('未找到数据内容');\n          }\n        } else {\n          console.error('API返回错误:', res.data);\n          alert(`API返回错误: ${res.data.msg || '未知错误'}`);\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n        }\n        this.dialogVisible = true;\n      } catch (e) {\n        console.error('请求数据集详情失败:', e);\n        alert(`请求失败: ${e.message || '未知错误'}`);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n\n      // 获取当前选中的数据集信息\n      // 构建发送给AI的完整消息\n      let aiMessage = this.question.trim();\n\n      // 如果有当前分析的数据集，添加数据集信息\n      if (this.currentAnalysisDataset) {\n        // 构建AI需要的格式\n        const datasetInfo = {\n          datasetId: this.currentAnalysisDataset.id,\n          datasetName: this.currentAnalysisDataset.name,\n          fields: this.currentAnalysisDataset.fields\n        };\n\n        // 将数据集信息添加到消息中\n        aiMessage = JSON.stringify({\n          question: this.question.trim(),\n          dataset: datasetInfo\n        });\n      }\n\n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      };\n      this.messages.push(userMsg);\n\n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      };\n      this.messages.push(botMsg);\n\n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1];\n\n      // 保存问题并清空输入框\n      const question = this.question;\n      this.question = '';\n      this.isSending = true;\n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = this.currentAnalysisDataset ? aiMessage : `${question}。当前数据集：未选择具体数据集`;\n\n        // 发送请求\n        await axios.post('http://localhost:8088/api/indicator/chat', {\n          memoryId: this.memoryId,\n          message\n        }, {\n          responseType: 'stream',\n          onDownloadProgress: e => {\n            const fullText = e.event.target.responseText; // 累积的完整文本\n            let newText = fullText.substring(lastMsg.content.length);\n            lastMsg.content += newText; // 增量更新\n            this.scrollToBottom(); // 实时滚动\n\n            // 保存原始响应\n            this.lastRawResponse = fullText;\n          }\n        });\n\n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false;\n        this.isSending = false;\n\n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error);\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。';\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      }\n    },\n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n\n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n\n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n\n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n\n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n\n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n\n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n\n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n\n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n\n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n\n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n\n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n\n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n\n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n\n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n\n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n\n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      this.messages.push(botMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n\n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n\n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [{\n            field: '类别1',\n            value: 100\n          }, {\n            field: '类别2',\n            value: 200\n          }, {\n            field: '类别3',\n            value: 150\n          }],\n          metrics: [{\n            name: '数值'\n          }]\n        }\n      };\n\n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n\n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [{\n              value: 1,\n              field: \"神朔\",\n              name: \"神朔\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"甘泉\",\n              name: \"甘泉\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"包神\",\n              name: \"包神\",\n              category: \"记录数*\"\n            }],\n            fields: [{\n              id: \"1746787308487\",\n              name: \"名称\",\n              groupType: \"d\"\n            }, {\n              id: \"-1\",\n              name: \"记录数*\",\n              groupType: \"q\"\n            }]\n          }\n        }\n      };\n\n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n\n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n\n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n\n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n\n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n\n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content.replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '').replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '').trim();\n\n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n\n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n\n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n\n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n\n        // 根据图表类型选择对应的处理函数\n        switch (chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n\n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n\n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n\n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2,\n          // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n\n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = canvas.height * imgWidth / canvas.width;\n\n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n\n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n\n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n\n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n\n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    // 导出完整对话方法\n    async exportAllConversation() {\n      try {\n        // 设置导出状态\n        this.exportingAll = true;\n\n        // 1. 提取所有图表消息\n        let chartMessages = [];\n\n        // 先找出所有包含图表的消息\n        for (let i = 0; i < this.messages.length; i++) {\n          const message = this.messages[i];\n          if (!message.isUser && message.chartConfig) {\n            chartMessages.push({\n              message: message,\n              index: i\n            });\n          }\n        }\n\n        // 如果没有图表，显示提示\n        if (chartMessages.length === 0) {\n          this.$message.warning('暂无图表数据。请先通过对话生成图表。');\n          this.exportingAll = false;\n          return;\n        }\n\n        // 2. 创建PDF\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 3. 处理每个图表，每个图表单独一页\n        for (let i = 0; i < chartMessages.length; i++) {\n          // 如果不是第一页，添加新页面\n          if (i > 0) {\n            pdf.addPage();\n          }\n          const chartMessage = chartMessages[i].message;\n          const chartConfig = chartMessage.chartConfig;\n\n          // 创建临时容器\n          const tempContainer = document.createElement('div');\n          tempContainer.style.position = 'absolute';\n          tempContainer.style.left = '-9999px';\n          tempContainer.style.width = '800px';\n          tempContainer.style.background = '#fff';\n          tempContainer.style.padding = '20px';\n          document.body.appendChild(tempContainer);\n\n          // 构建PDF标题和基本信息\n          const title = chartConfig.title || '数据分析图表';\n          const currentDate = new Date().toLocaleString();\n          const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n          let htmlContent = `\n            <div style=\"font-family: Arial, sans-serif; padding: 20px;\">\n              <h1 style=\"text-align: center; color: #333; font-size: 24px; margin-bottom: 30px;\">${title}</h1>\n              <div style=\"text-align: right; margin-bottom: 20px;\">\n                <p style=\"color: #666; margin: 5px 0;\">数据集: ${datasetName}</p>\n                <p style=\"color: #666; margin: 5px 0;\">生成时间: ${currentDate}</p>\n              </div>\n              \n              <div style=\"margin: 20px 0;\">\n                <h2 style=\"color: #333; font-size: 20px; margin-bottom: 15px;\">分析描述:</h2>\n          `;\n\n          // 添加描述\n          let description = chartMessage.content;\n\n          // 清理HTML标签，保留纯文本\n          if (description.includes('<')) {\n            const tempDiv = document.createElement('div');\n            tempDiv.innerHTML = description;\n            description = tempDiv.textContent || tempDiv.innerText || '';\n          }\n          htmlContent += `\n            <p style=\"margin: 15px 0; color: #333; line-height: 1.6;\">${description}</p>\n            \n            <div style=\"margin: 30px 0;\">\n              <div id=\"chart-container\" style=\"height: 400px; margin: 30px 0;\"></div>\n              \n              <div style=\"margin: 30px 0;\">\n                <h3 style=\"color: #333; font-size: 18px; margin-bottom: 15px;\">数据表格:</h3>\n                <div id=\"data-table\"></div>\n              </div>\n            </div>\n          `;\n          htmlContent += `</div>`;\n\n          // 设置HTML内容\n          tempContainer.innerHTML = htmlContent;\n\n          // 渲染图表\n          const chartContainer = tempContainer.querySelector('#chart-container');\n          const dataTableContainer = tempContainer.querySelector('#data-table');\n          if (chartContainer && dataTableContainer) {\n            // 初始化图表\n            const chartInstance = echarts.init(chartContainer);\n            let options;\n\n            // 复用你现有的图表处理逻辑\n            switch (chartConfig.type) {\n              case 'bar':\n                options = this.getBarChartOptions(chartConfig);\n                break;\n              case 'line':\n                options = this.getLineChartOptions(chartConfig);\n                break;\n              case 'pie':\n                options = this.getPieChartOptions(chartConfig);\n                break;\n              case 'bar-horizontal':\n                options = this.getBarHorizontalOptions(chartConfig);\n                break;\n              default:\n                options = this.getDefaultOptions(chartConfig);\n            }\n            chartInstance.setOption(options);\n\n            // 渲染数据表格\n            this.renderDataTable(dataTableContainer, chartConfig);\n\n            // 等待图表渲染完成\n            await new Promise(resolve => setTimeout(resolve, 1000));\n\n            // 使用html2canvas捕获当前页面内容\n            const canvas = await html2canvas(tempContainer, {\n              scale: 2,\n              useCORS: true,\n              allowTaint: true,\n              backgroundColor: '#ffffff'\n            });\n\n            // 将canvas转为图像\n            const imgData = canvas.toDataURL('image/png');\n\n            // 计算适当的宽度和高度以适应A4页面\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            const pageHeight = pdf.internal.pageSize.getHeight();\n            const imgWidth = pageWidth - 40; // 左右各20mm边距\n            const imgHeight = canvas.height * imgWidth / canvas.width;\n\n            // 如果图像高度超过页面高度，进行缩放\n            let finalImgHeight = imgHeight;\n            let finalImgWidth = imgWidth;\n            if (imgHeight > pageHeight - 40) {\n              // 上下各20mm边距\n              finalImgHeight = pageHeight - 40;\n              finalImgWidth = canvas.width * finalImgHeight / canvas.height;\n            }\n\n            // 添加图像到PDF，居中显示\n            const xPos = (pageWidth - finalImgWidth) / 2;\n            const yPos = 20; // 顶部边距\n\n            pdf.addImage(imgData, 'PNG', xPos, yPos, finalImgWidth, finalImgHeight);\n\n            // 清理\n            chartInstance.dispose();\n            document.body.removeChild(tempContainer);\n          }\n        }\n\n        // 4. 保存PDF\n        const fileName = `指标分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;\n        pdf.save(fileName);\n\n        // 5. 重置状态\n        this.exportingAll = false;\n        this.$message.success('指标导出成功');\n      } catch (error) {\n        console.error('指标导出失败:', error);\n        this.exportingAll = false;\n        this.$message.error('指标导出失败: ' + error.message);\n      }\n    },\n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n\n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n\n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n\n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n\n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n\n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n\n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      return options;\n    },\n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n\n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n\n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n\n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n\n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n\n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n\n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n\n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n\n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "v4", "uuidv4", "axios", "ChartDisplay", "html2canvas", "jsPDF", "echarts", "name", "components", "data", "description", "tablename", "tables", "datasets", "filteredDatasets", "searchKeyword", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "tableIndicators", "dialogVisible", "messages", "memoryId", "isSending", "messageListRef", "showRawResponsePanel", "lastRawResponse", "drawer", "direction", "currentDatasetDetail", "datasetFields", "datasetData", "apiToken", "currentAnalysisDataset", "innerDrawer", "tableList", "exportingAll", "selectedDataset", "showDatasetIndicator", "previewCard", "previewPopupStyle", "isDatasetConfirmed", "previewLoading", "mounted", "localStorage", "setItem", "loadTables", "initMemoryId", "addWelcomeMessage", "updated", "scrollToBottom", "methods", "SelectDataList", "storedMemoryId", "getItem", "uuidToNumber", "uuid", "number", "i", "length", "hexValue", "parseInt", "push", "isUser", "content", "isTyping", "$refs", "scrollTop", "scrollHeight", "res", "getAllTables", "code", "flattenDatasets", "e", "tree", "result", "node", "leaf", "children", "concat", "onSearchDataset", "keyword", "trim", "toLowerCase", "filter", "ds", "includes", "analyzeDataset", "$message", "warning", "fieldsInfo", "map", "field", "id", "originName", "dataeaseName", "groupType", "type", "datasourceId", "datasetTableId", "datasetGroupId", "fields", "success", "$nextTick", "inputEl", "document", "querySelector", "focus", "showCardPreview", "table", "event", "rect", "currentTarget", "getBoundingClientRect", "position", "top", "left", "right", "zIndex", "hideCardPreview", "previewDataset", "showDatasetDetail", "startQuestionWithDataset", "previewSelectedDataset", "changeDataset", "info", "getRecommendedQuestions", "datasetName", "dimensionFields", "getDimensionFields", "metricFields", "getMetricFields", "questions", "slice", "useRecommendedQuestion", "submitQuestion", "<PERSON><PERSON><PERSON><PERSON><PERSON>s", "refreshPreviewData", "setTimeout", "dataset", "console", "log", "error", "headers", "getDatasetDetail", "detail", "allFields", "warn", "alert", "msg", "message", "showSuggestions", "useQuestion", "q", "getTableFields", "tableCode", "aiMessage", "datasetInfo", "datasetId", "JSON", "stringify", "userMsg", "botMsg", "lastMsg", "post", "responseType", "onDownloadProgress", "fullText", "target", "responseText", "newText", "substring", "parseChartConfig", "chartDataIdMatch", "match", "chartDataId", "fetchChartDataById", "chartConfigMatch", "chartConfig", "parse", "tableId", "replace", "jsonRegex", "jsonMatch", "jsonStr", "parseError", "defaultConfig", "Date", "now", "toString", "title", "response", "get", "status", "$forceUpdate", "test<PERSON>hart", "testChartConfig", "testApiResponse", "value", "metrics", "json<PERSON><PERSON><PERSON>", "apiResponseMsg", "testRealData", "realData", "category", "realDataMsg", "showRawResponse", "exportToPDF", "$set", "tempContainer", "createElement", "style", "width", "background", "padding", "body", "append<PERSON><PERSON><PERSON>", "tableName", "currentDate", "toLocaleString", "tempDiv", "innerHTML", "textContent", "innerText", "chartContainer", "chart", "init", "options", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "setOption", "Promise", "resolve", "dataTableContainer", "renderDataTable", "canvas", "scale", "useCORS", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "imgData", "toDataURL", "pdf", "orientation", "unit", "format", "imgWidth", "imgHeight", "height", "addImage", "save", "getTime", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "exportAllConversation", "chartMessages", "index", "addPage", "chartMessage", "htmlContent", "chartInstance", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "finalImgHeight", "finalImgWidth", "xPos", "yPos", "fileName", "toISOString", "createBasicChartOptions", "categories", "item", "text", "tooltip", "trigger", "xAxis", "yAxis", "series", "seriesMap", "for<PERSON>ach", "s", "Array", "fill", "Object", "values", "chartData", "isArray", "f", "xAxisData", "categoriesSet", "Set", "add", "from", "seriesData", "find", "axisPointer", "legend", "formatter", "orient", "radius", "avoidLabelOverlap", "label", "show", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "container", "firstItem", "tableHTML", "header", "undefined"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <!-- 调试信息区域 -->\n    <div style=\"position: fixed; top: 10px; right: 10px; z-index: 9999; background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-width: 300px; max-height: 300px; overflow: auto;\">\n      <!-- <button @click=\"testDetailRequest()\">测试详情请求</button>\n      <div v-if=\"datasets.length > 0\">\n        <p>数据集数量: {{ datasets.length }}</p>\n        <p>第一个数据集ID: {{ datasets[0] && datasets[0].id }}</p>\n      </div> -->\n    </div>\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <div class=\"header-actions\" style=\"display: flex; align-items: center; margin-top: 10px;\">\n           \n          </div>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n        </div>\n        \n        <!-- 数据集选择区域 - 只在未选择数据集时显示 -->\n        <transition name=\"fade\">\n          <div class=\"data-selection\" v-if=\"!showDatasetIndicator\">\n            <h3>目前可用数据</h3>\n            <div class=\"data-sets\">\n              <el-row :gutter=\"16\">\n                <el-col :span=\"6\" v-for=\"(table, idx) in datasets.slice(0, 4)\" :key=\"table.id + '_' + idx\">\n                  <div class=\"data-card-wrapper\" @mouseenter=\"showCardPreview(table, $event)\" @mouseleave=\"hideCardPreview\">\n                    <el-card class=\"data-card\" @click.native=\"showDatasetDetail(table)\">\n                      <div class=\"data-header\">\n                        <span class=\"sample-tag\">样例</span>\n                        <span class=\"data-title\">{{ table.name }}</span>\n                        <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n                      </div>\n                      <div class=\"data-fields\">\n                        <el-tag\n                          v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])\"\n                          :key=\"field.id || idx\"\n                          size=\"mini\"\n                          type=\"info\"\n                          style=\"margin-right: 4px; margin-bottom: 4px;\"\n                        >\n                          {{ field.name || field }}\n                        </el-tag>\n                        <span v-if=\"table.fields && table.fields.length > 4\">...</span>\n                      </div>\n                    </el-card>\n\n                    <!-- 悬停预览弹窗 -->\n                    <div class=\"card-preview-popup\" v-if=\"previewCard && previewCard.id === table.id\" :style=\"previewPopupStyle\">\n                      <div class=\"preview-content\">\n                        <h4>{{ table.name }}</h4>\n                        <p class=\"preview-desc\">{{ table.fields ? table.fields.length : 0 }}个字段</p>\n                        <div class=\"preview-actions\">\n                          <el-button size=\"mini\" type=\"primary\" @click.stop=\"previewDataset(table)\">\n                            <i class=\"el-icon-view\"></i> 预览\n                          </el-button>\n                          <el-button size=\"mini\" type=\"success\" @click.stop=\"startQuestionWithDataset(table)\">\n                            <i class=\"el-icon-chat-dot-round\"></i> 提问\n                          </el-button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </el-col>\n              </el-row>\n            </div>\n          </div>\n        </transition>\n\n        <!-- 数据集指示器 - 选择数据集后显示 -->\n        <transition name=\"slide-down\">\n          <div class=\"dataset-indicator\" v-if=\"showDatasetIndicator && selectedDataset\">\n            <div class=\"dataset-info\">\n              <div class=\"dataset-main-info\">\n                <i class=\"el-icon-database\"></i>\n                <span class=\"dataset-name\">{{ selectedDataset.name }}</span>\n                <el-tag size=\"mini\" type=\"info\">{{ selectedDataset.fields ? selectedDataset.fields.length : 0 }}个字段</el-tag>\n              </div>\n              <div class=\"dataset-fields-preview\">\n                <el-tag\n                  v-for=\"(field, idx) in (selectedDataset.fields ? selectedDataset.fields.slice(0, 6) : [])\"\n                  :key=\"field.id || idx\"\n                  size=\"mini\"\n                  :type=\"field.groupType === 'd' ? 'primary' : 'success'\"\n                  style=\"margin-right: 4px; margin-bottom: 4px;\"\n                >\n                  {{ field.name || field }}\n                </el-tag>\n                <span v-if=\"selectedDataset.fields && selectedDataset.fields.length > 6\" class=\"more-fields\">\n                  +{{ selectedDataset.fields.length - 6 }}个字段\n                </span>\n              </div>\n            </div>\n            <div class=\"dataset-actions\">\n              <el-button size=\"mini\" icon=\"el-icon-view\" @click=\"previewSelectedDataset\">预览</el-button>\n              <el-button size=\"mini\" icon=\"el-icon-refresh\" @click=\"changeDataset\">更换</el-button>\n            </div>\n          </div>\n        </transition>\n        \n        <!-- 聊天消息列表区域 -->\n        <div class=\"message-list\" ref=\"messageListRef\">\n          <div\n            v-for=\"(message, index) in messages\"\n            :key=\"index\"\n            :class=\"message.isUser ? 'message user-message' : 'message bot-message'\"\n          >\n            <!-- 聊天图标 -->\n            <div class=\"avatar-container\" v-if=\"!message.isUser\">\n              <div class=\"bot-avatar\">\n                <i class=\"el-icon-s-tools\"></i>\n              </div>\n            </div>\n            <!-- 消息内容 -->\n            <div class=\"message-content\">\n              <div v-html=\"message.content\"></div>\n              <!-- 如果消息中包含图表配置，则显示图表和导出按钮 -->\n              <div v-if=\"message.chartConfig\" class=\"chart-container\">\n                <div class=\"chart-actions\">\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-download\" \n                            @click=\"exportToPDF(message)\" :loading=\"message.exporting\">\n                    导出PDF\n                  </el-button>\n                </div>\n                <chart-display :chart-config=\"message.chartConfig\" ref=\"chartDisplay\"></chart-display>\n              </div>\n              <!-- loading动画 -->\n              <span\n                class=\"loading-dots\"\n                v-if=\"message.isTyping\"\n              >\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n              </span>\n            </div>\n            <div class=\"avatar-container\" v-if=\"message.isUser\">\n              <div class=\"user-avatar\">\n                <i class=\"el-icon-user\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 底部问题输入区域（移至message-list下方） -->\n        <div class=\"question-input-container\">\n          <span>👋直接问我问题，或在上方选择一个主题/数据开始！</span>\n          <div class=\"question-input-wrapper\">\n            <el-button type=\"text\" \n            style=\"margin-left: 10px;\" \n            size=\"small\"\n            @click=\"SelectDataList\" \n            icon=\"el-icon-upload2\">\n            选择数据\n          </el-button>\n            <el-button \n              type=\"text\"\n              size=\"small\"\n              icon=\"el-icon-bottom\" \n              @click=\"exportAllConversation\"\n              :loading=\"exportingAll\"\n              :disabled=\"messages.length <= 1\"\n              style=\"margin-left: 20px;\">\n              导出完整指标\n            </el-button>\n            <el-input \n              style=\"margin-bottom: 12px;width: 800px;\"\n              v-model=\"question\" \n              placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n              class=\"question-input\"\n              @keyup.enter.native=\"submitQuestion\"\n              :disabled=\"isSending\">\n            </el-input>\n            <div class=\"input-actions\">\n              <button class=\"action-btn\" @click=\"showSuggestions\">\n                <i class=\"el-icon-magic-stick\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testChart\" title=\"测试图表功能\">\n                <i class=\"el-icon-data-line\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testRealData\" title=\"测试实际数据\">\n                <i class=\"el-icon-s-data\"></i>\n              </button>\n              <button class=\"action-btn debug-btn\" @click=\"showRawResponse\" title=\"显示AI原始响应\">\n                <i class=\"el-icon-monitor\"></i>\n              </button>\n              <button class=\"action-btn send-btn\" @click=\"submitQuestion\" :disabled=\"isSending\">\n                <i class=\"el-icon-position\"></i>\n              </button>\n              \n            </div>\n          </div>\n          \n          <!-- 建议问题弹出层 -->\n          <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n            <div class=\"suggestions-title\">\n              <i class=\"el-icon-s-promotion\"></i> 官方推荐\n            </div>\n            <div class=\"suggestions-list\">\n              <div \n                v-for=\"(suggestion, index) in suggestedQuestions\" \n                :key=\"index\"\n                class=\"suggestion-item\"\n                @click=\"useQuestion(suggestion)\">\n                {{ suggestion }}\n              </div>\n            </div>\n          </div>\n          \n          <!-- AI原始响应弹出层 -->\n          <div v-if=\"showRawResponsePanel\" class=\"raw-response-panel\">\n            <div class=\"raw-response-title\">\n              <i class=\"el-icon-monitor\"></i> AI原始响应\n              <button class=\"close-btn\" @click=\"showRawResponsePanel = false\">\n                <i class=\"el-icon-close\"></i>\n              </button>\n            </div>\n            <pre class=\"raw-response-content\">{{ lastRawResponse }}</pre>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <el-drawer\n      title=\"\"\n      :visible.sync=\"dialogVisible\"\n      direction=\"rtl\"\n      size=\"55%\"\n      class=\"dataset-detail-drawer\">\n\n      <div class=\"dataset-detail-container\" v-if=\"currentDatasetDetail\">\n        <!-- 头部区域 -->\n        <div class=\"dataset-detail-header\">\n          <div class=\"dataset-title-section\">\n            <h2 class=\"dataset-title\">\n              <i class=\"el-icon-database\"></i>\n              {{ currentDatasetDetail.name }}\n            </h2>\n            <p class=\"dataset-subtitle\">针对已选数据，为您推荐如下问题：</p>\n          </div>\n          <div class=\"dataset-actions-section\">\n            <el-button type=\"primary\" @click=\"analyzeDataset\" icon=\"el-icon-data-analysis\" size=\"medium\">\n              智能分析数据\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 推荐问题区域 -->\n        <div class=\"recommended-questions\">\n          <div class=\"question-tags\">\n            <el-tag\n              v-for=\"(question, index) in getRecommendedQuestions()\"\n              :key=\"index\"\n              class=\"question-tag\"\n              @click=\"useRecommendedQuestion(question)\"\n            >\n              {{ question }}\n            </el-tag>\n          </div>\n        </div>\n\n        <!-- 字段信息区域 -->\n        <div class=\"dataset-fields-section\">\n          <div class=\"section-header\">\n            <h3 class=\"section-title\">\n              <i class=\"el-icon-menu\"></i>\n              关键指标\n            </h3>\n            <div class=\"field-type-legend\">\n              <span class=\"legend-item\">\n                <el-tag size=\"mini\" type=\"primary\">维度</el-tag>\n              </span>\n              <span class=\"legend-item\">\n                <el-tag size=\"mini\" type=\"success\">指标</el-tag>\n              </span>\n              <span class=\"legend-item\">\n                <el-tag size=\"mini\" type=\"info\">其他</el-tag>\n              </span>\n            </div>\n          </div>\n\n          <div class=\"fields-grid\">\n            <div class=\"field-category\" v-if=\"getDimensionFields().length > 0\">\n              <h4 class=\"category-title\">分析维度</h4>\n              <div class=\"field-tags\">\n                <el-tag\n                  v-for=\"field in getDimensionFields()\"\n                  :key=\"field.id\"\n                  type=\"primary\"\n                  size=\"medium\"\n                  class=\"field-tag\"\n                >\n                  <i class=\"el-icon-collection-tag\"></i>\n                  {{ field.name }}\n                </el-tag>\n              </div>\n            </div>\n\n            <div class=\"field-category\" v-if=\"getMetricFields().length > 0\">\n              <h4 class=\"category-title\">销售指标</h4>\n              <div class=\"field-tags\">\n                <el-tag\n                  v-for=\"field in getMetricFields()\"\n                  :key=\"field.id\"\n                  type=\"success\"\n                  size=\"medium\"\n                  class=\"field-tag\"\n                >\n                  <i class=\"el-icon-data-line\"></i>\n                  {{ field.name }}\n                </el-tag>\n              </div>\n            </div>\n\n            <div class=\"field-category\" v-if=\"getOtherFields().length > 0\">\n              <h4 class=\"category-title\">其他</h4>\n              <div class=\"field-tags\">\n                <el-tag\n                  v-for=\"field in getOtherFields()\"\n                  :key=\"field.id\"\n                  type=\"info\"\n                  size=\"medium\"\n                  class=\"field-tag\"\n                >\n                  {{ field.name }}\n                </el-tag>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 数据预览区域 -->\n        <div class=\"dataset-preview-section\">\n          <div class=\"section-header\">\n            <h3 class=\"section-title\">\n              <i class=\"el-icon-view\"></i>\n              已选数据\n            </h3>\n            <div class=\"preview-actions\">\n              <el-button size=\"mini\" icon=\"el-icon-refresh\" @click=\"refreshPreviewData\">刷新数据</el-button>\n              <span class=\"data-count\">{{ datasetData.length }} 条数据</span>\n            </div>\n          </div>\n\n          <div class=\"preview-table-container\">\n            <el-table\n              :data=\"datasetData.slice(0, 10)\"\n              style=\"width: 100%\"\n              stripe\n              size=\"mini\"\n              max-height=\"300\"\n              :loading=\"previewLoading\"\n            >\n              <el-table-column\n                v-for=\"field in datasetFields.slice(0, 8)\"\n                :key=\"field.id || field.name\"\n                :prop=\"field.dataeaseName || field.name\"\n                :label=\"field.name\"\n                min-width=\"100\"\n                show-overflow-tooltip\n              >\n              </el-table-column>\n            </el-table>\n\n            <div class=\"preview-footer\">\n              <span class=\"preview-note\">\n                显示前10条数据，共 {{ datasetData.length }} 条\n                <span v-if=\"datasetFields.length > 8\">，显示前8个字段</span>\n              </span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 底部操作区域 -->\n        <div class=\"dataset-detail-footer\">\n          <div class=\"footer-info\">\n            <i class=\"el-icon-info\"></i>\n            <span>请直接向我提问，或输入/唤起快捷提问吧</span>\n          </div>\n          <div class=\"footer-actions\">\n            <el-button icon=\"el-icon-data-analysis\" @click=\"analyzeDataset\">数据概览</el-button>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n    <el-drawer\n      title=\"数据集列表\"\n      :visible.sync=\"drawer\"\n      direction=\"rtl\"\n      size=\"45%\">\n      <div style=\"padding: 20px\">\n        <el-input\n          v-model=\"searchKeyword\"\n          placeholder=\"搜索数据集\"\n          @input=\"onSearchDataset\"\n          style=\"margin-bottom: 16px; width: 300px;\"\n        />\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\" v-for=\"(table, idx) in filteredDatasets\" :key=\"table.id + '_' + idx\">\n            <el-card class=\"data-card\" @click.native=\"showDatasetDetail(table)\" style=\"background-color: #f9f9f9;\">\n              <div class=\"data-header\">\n                <span class=\"sample-tag\">样例</span>\n                <span class=\"data-title\">{{ table.name }}</span>\n                <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n          </div>\n              <div class=\"data-fields\">\n                <el-tag\n                  v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])\"\n                  :key=\"field.id || idx\"\n                  size=\"mini\"\n                  type=\"info\"\n                  style=\"margin-right: 4px; margin-bottom: 4px;\"\n                >\n                  {{ field.name || field }}\n                </el-tag>\n                <span v-if=\"table.fields && table.fields.length > 4\">...</span>\n          </div>\n        </el-card>\n          </el-col>\n        </el-row>\n      </div>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api/index.js'\nimport { v4 as uuidv4 } from 'uuid'\nimport axios from 'axios'\nimport ChartDisplay from './components/ChartDisplay.vue'\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas'\nimport { jsPDF } from 'jspdf'\nimport * as echarts from 'echarts'\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [], // 原始树结构\n      datasets: [], // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [], // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false, // 新增：控制原始响应弹出层\n      lastRawResponse: '', // 新增：存储最后收到的原始响应\n      drawer:false,    //抽屉展示\n      direction: 'rtl', //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      apiToken: 'eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2OTEyYzNiLTQ5ZmMtNDAyMC1iMzRiLWE3YWJlNmY1MTI0MSJ9.J4QCC6qh2GZ7ENDHuZQWQ1GvGOQ1HOr8gW34KXIHV9N_73Jppx-qmEwpq0AHlXM3DUjd5EAlpIVAvzJygm_ldg', // 示例token，实际应该从登录后存储\n      currentAnalysisDataset: null, // 存储当前用于智能分析的数据集信息\n      innerDrawer:false,\n      tableList:[],\n      exportingAll: false, // 新增：控制完整对话导出状态\n\n      // 新增：数据集选择和预览相关状态\n      selectedDataset: null, // 当前选择的数据集\n      showDatasetIndicator: false, // 是否显示数据集指示器\n      previewCard: null, // 当前预览的卡片\n      previewPopupStyle: {}, // 预览弹窗样式\n      isDatasetConfirmed: false, // 数据集是否已确认选择\n      previewLoading: false, // 预览数据加载状态\n    }\n  },\n  \n  mounted() {\n    // 存储token到localStorage，供API使用\n    localStorage.setItem('de_token', this.apiToken);\n    this.loadTables()\n    this.initMemoryId()\n    this.addWelcomeMessage()\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\n      \"帮我生成一个销售额柱状图\",\n      \"展示近六个月的销售趋势折线图\",\n      \"按照区域统计销售量并生成饼图\",\n      \"帮我做一个按产品类别的销量对比图\"\n    ]\n  },\n  updated() {\n    this.scrollToBottom()\n  },\n  methods: {\n    SelectDataList(){\n      this.loadTables()\n      this.drawer=true\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id')\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4())\n        localStorage.setItem('user_memory_id', storedMemoryId)\n      }\n      this.memoryId = storedMemoryId\n    },\n    \n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i]\n        number = number * 16 + (parseInt(hexValue, 16) || 0)\n      }\n      return number % 1000000\n    },\n    \n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      })\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight\n      }\n    },\n    \n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables()\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data\n          this.datasets = this.flattenDatasets(this.tables)\n          this.filteredDatasets = this.datasets\n        } else {\n          this.tables = []\n          this.datasets = []\n          this.filteredDatasets = []\n        }\n      } catch (e) {\n        this.tables = []\n        this.datasets = []\n        this.filteredDatasets = []\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = []\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node)\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children))\n        }\n      }\n      return result\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase()\n      if (!keyword) {\n        this.filteredDatasets = this.datasets\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword))\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n    \n    // 测试详情请求方法\n    // async testDetailRequest() {\n    //   if (this.datasets.length > 0) {\n    //     const testDataset = this.datasets[0];\n    //     console.log('测试数据集:', testDataset);\n    //     alert(`正在请求数据集详情，ID: ${testDataset.id}`);\n    //     await this.showDatasetDetail(testDataset);\n      //   } else {\n    //     alert('没有可用的数据集');\n    //   }\n    // },\n\n    // 智能分析数据集 - 确认选择数据集\n    analyzeDataset() {\n      if (!this.currentDatasetDetail || !this.datasetFields.length) {\n        this.$message.warning('当前数据集没有可用字段');\n        return;\n      }\n\n      // 提取所需的字段信息\n      const fieldsInfo = this.datasetFields.map(field => {\n        // 只保留需要的字段属性\n        return {\n          id: field.id,\n          originName: field.originName || '',\n          name: field.name || '',\n          dataeaseName: field.dataeaseName || '',\n          groupType: field.groupType || '',\n          type: field.type || '',\n          datasourceId: field.datasourceId || '',\n          datasetTableId: field.datasetTableId || '',\n          datasetGroupId: field.datasetGroupId || ''\n        };\n      });\n\n      // 确认选择数据集\n      this.selectedDataset = {\n        id: this.currentDatasetDetail.id,\n        name: this.currentDatasetDetail.name,\n        fields: fieldsInfo\n      };\n\n      // 存储当前数据集信息，包括ID和字段\n      this.currentAnalysisDataset = this.selectedDataset;\n\n      // 显示数据集指示器，隐藏数据选择区域\n      this.showDatasetIndicator = true;\n      this.isDatasetConfirmed = true;\n\n      // 关闭详情抽屉，返回到聊天界面\n      this.dialogVisible = false;\n\n      // 提示用户\n      this.$message.success(`已选择数据集\"${this.currentDatasetDetail.name}\"进行智能分析，请在下方输入您的问题`);\n\n      // 自动聚焦到问题输入框\n      this.$nextTick(() => {\n        const inputEl = document.querySelector('.question-input input');\n        if (inputEl) inputEl.focus();\n      });\n    },\n\n    // 显示卡片预览\n    showCardPreview(table, event) {\n      this.previewCard = table;\n      const rect = event.currentTarget.getBoundingClientRect();\n      this.previewPopupStyle = {\n        position: 'fixed',\n        top: rect.top - 10 + 'px',\n        left: rect.right + 10 + 'px',\n        zIndex: 1000\n      };\n    },\n\n    // 隐藏卡片预览\n    hideCardPreview() {\n      this.previewCard = null;\n    },\n\n    // 预览数据集（从悬停弹窗触发）\n    previewDataset(table) {\n      this.hideCardPreview();\n      this.showDatasetDetail(table);\n    },\n\n    // 开始提问（从悬停弹窗触发）\n    startQuestionWithDataset(table) {\n      this.hideCardPreview();\n      // 直接选择数据集并确认\n      this.currentDatasetDetail = table;\n      this.datasetFields = table.fields || [];\n      this.analyzeDataset();\n    },\n\n    // 预览已选择的数据集\n    previewSelectedDataset() {\n      if (this.selectedDataset) {\n        this.showDatasetDetail(this.selectedDataset);\n      }\n    },\n\n    // 更换数据集\n    changeDataset() {\n      this.selectedDataset = null;\n      this.showDatasetIndicator = false;\n      this.isDatasetConfirmed = false;\n      this.currentAnalysisDataset = null;\n      this.$message.info('已取消数据集选择，请重新选择数据集');\n    },\n\n    // 获取推荐问题\n    getRecommendedQuestions() {\n      if (!this.currentDatasetDetail) return [];\n\n      const datasetName = this.currentDatasetDetail.name;\n      const dimensionFields = this.getDimensionFields();\n      const metricFields = this.getMetricFields();\n\n      const questions = [];\n\n      if (dimensionFields.length > 0 && metricFields.length > 0) {\n        questions.push(`上个月${dimensionFields[0].name}和${metricFields[0].name}的销售情况是多少？`);\n        questions.push(`上个月${dimensionFields[0].name}的${metricFields[0].name}是多少？`);\n      }\n\n      if (dimensionFields.length > 1) {\n        questions.push(`上个月${dimensionFields[0].name}的${dimensionFields[1].name}是多少？`);\n      }\n\n      questions.push(`上个月${datasetName}的销售数据是多少？`);\n\n      return questions.slice(0, 4);\n    },\n\n    // 使用推荐问题\n    useRecommendedQuestion(question) {\n      this.question = question;\n      this.analyzeDataset(); // 先确认选择数据集\n      this.$nextTick(() => {\n        this.submitQuestion(); // 然后提交问题\n      });\n    },\n\n    // 获取维度字段\n    getDimensionFields() {\n      return this.datasetFields.filter(field => field.groupType === 'd');\n    },\n\n    // 获取指标字段\n    getMetricFields() {\n      return this.datasetFields.filter(field => field.groupType === 'q');\n    },\n\n    // 获取其他字段\n    getOtherFields() {\n      return this.datasetFields.filter(field => !field.groupType || (field.groupType !== 'd' && field.groupType !== 'q'));\n    },\n\n    // 刷新预览数据\n    refreshPreviewData() {\n      if (this.currentDatasetDetail) {\n        this.previewLoading = true;\n        // 模拟刷新延迟\n        setTimeout(() => {\n          this.previewLoading = false;\n          this.$message.success('数据已刷新');\n        }, 1000);\n      }\n    },\n  \n    async showDatasetDetail(dataset) {\n      // alert(`showDatasetDetail 被调用，数据集ID: ${dataset?.id || '未知'}`);\n      console.log('showDatasetDetail 被调用，参数:', dataset);\n      \n      // 验证参数\n      if (!dataset || !dataset.id) {\n        console.error('无效的数据集参数:', dataset);\n        // alert('数据集参数无效，缺少ID');\n        return;\n      }\n      \n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n      \n      try {\n        console.log(`开始请求数据集详情，ID: ${dataset.id}`);\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n        console.log('数据集详情API响应:', res);\n        \n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          console.log('数据集详情数据:', detail);\n          this.currentDatasetDetail = detail;\n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || (detail.data && detail.data.fields) || [];\n          // 数据内容\n          this.datasetData = (detail.data && detail.data.data) || [];\n          // 调试打印\n          console.log(`字段信息: ${this.datasetFields.length}个字段`);\n          console.log(`数据内容: ${this.datasetData.length}条记录`);\n          \n          if (this.datasetFields.length === 0) {\n            console.warn('未找到字段信息');\n          }\n          \n          if (this.datasetData.length === 0) {\n            console.warn('未找到数据内容');\n          }\n        } else {\n          console.error('API返回错误:', res.data);\n          alert(`API返回错误: ${res.data.msg || '未知错误'}`);\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n        }\n        this.dialogVisible = true;\n      } catch (e) {\n        console.error('请求数据集详情失败:', e);\n        alert(`请求失败: ${e.message || '未知错误'}`);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    \n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      // 获取当前选中的数据集信息\n      // 构建发送给AI的完整消息\n      let aiMessage = this.question.trim();\n      \n      // 如果有当前分析的数据集，添加数据集信息\n      if (this.currentAnalysisDataset) {\n        // 构建AI需要的格式\n        const datasetInfo = {\n          datasetId: this.currentAnalysisDataset.id,\n          datasetName: this.currentAnalysisDataset.name,\n          fields: this.currentAnalysisDataset.fields\n        };\n        \n        // 将数据集信息添加到消息中\n        aiMessage = JSON.stringify({\n          question: this.question.trim(),\n          dataset: datasetInfo\n        });\n      }\n      \n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      }\n      this.messages.push(userMsg)\n      \n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      }\n      this.messages.push(botMsg)\n      \n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1]\n      \n      // 保存问题并清空输入框\n      const question = this.question\n      this.question = ''\n      this.isSending = true\n      \n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = this.currentAnalysisDataset \n          ? aiMessage \n          : `${question}。当前数据集：未选择具体数据集`;\n        \n        // 发送请求\n        await axios.post(\n          'http://localhost:8088/api/indicator/chat',\n          { memoryId: this.memoryId, message },\n          {\n            responseType: 'stream',\n            onDownloadProgress: (e) => {\n              const fullText = e.event.target.responseText // 累积的完整文本\n              let newText = fullText.substring(lastMsg.content.length)\n              lastMsg.content += newText // 增量更新\n              this.scrollToBottom() // 实时滚动\n              \n              // 保存原始响应\n              this.lastRawResponse = fullText\n            }\n          }\n        )\n        \n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false\n        this.isSending = false\n        \n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error)\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'\n        lastMsg.isTyping = false\n        this.isSending = false\n      }\n    },\n    \n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n      \n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n        \n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n      \n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        \n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n          \n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n          \n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n            \n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, \n              '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            \n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      \n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n      \n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        \n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n          \n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          \n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            \n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n              \n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], \n                  '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n      \n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || \n          message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n        \n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        \n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    \n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        \n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n          \n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n          \n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n          \n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n          \n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    \n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n      \n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      \n      this.messages.push(botMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n      \n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n      \n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [\n            { field: '类别1', value: 100 },\n            { field: '类别2', value: 200 },\n            { field: '类别3', value: 150 }\n          ],\n          metrics: [{ name: '数值' }]\n        }\n      };\n      \n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      \n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      \n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    \n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n      \n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [\n              {value: 1, field: \"神朔\", name: \"神朔\", category: \"记录数*\"},\n              {value: 1, field: \"甘泉\", name: \"甘泉\", category: \"记录数*\"},\n              {value: 1, field: \"包神\", name: \"包神\", category: \"记录数*\"}\n            ],\n            fields: [\n              {id: \"1746787308487\", name: \"名称\", groupType: \"d\"},\n              {id: \"-1\", name: \"记录数*\", groupType: \"q\"}\n            ]\n          }\n        }\n      };\n      \n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n      \n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n      \n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      \n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      \n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n        \n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n        \n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n        \n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content\n          .replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '')\n          .replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '')\n          .trim();\n          \n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n        \n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n        \n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n        \n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n        \n        // 根据图表类型选择对应的处理函数\n        switch(chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n        \n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n        \n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        \n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n        \n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2, // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n        \n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = (canvas.height * imgWidth) / canvas.width;\n        \n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n        \n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n        \n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n        \n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n        \n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n        \n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    \n    // 导出完整对话方法\n    async exportAllConversation() {\n      try {\n        // 设置导出状态\n        this.exportingAll = true;\n        \n        // 1. 提取所有图表消息\n        let chartMessages = [];\n        \n        // 先找出所有包含图表的消息\n        for (let i = 0; i < this.messages.length; i++) {\n          const message = this.messages[i];\n          if (!message.isUser && message.chartConfig) {\n            chartMessages.push({\n              message: message,\n              index: i\n            });\n          }\n        }\n        \n        // 如果没有图表，显示提示\n        if (chartMessages.length === 0) {\n          this.$message.warning('暂无图表数据。请先通过对话生成图表。');\n          this.exportingAll = false;\n          return;\n        }\n        \n        // 2. 创建PDF\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 3. 处理每个图表，每个图表单独一页\n        for (let i = 0; i < chartMessages.length; i++) {\n          // 如果不是第一页，添加新页面\n          if (i > 0) {\n            pdf.addPage();\n          }\n          \n          const chartMessage = chartMessages[i].message;\n          const chartConfig = chartMessage.chartConfig;\n          \n          // 创建临时容器\n          const tempContainer = document.createElement('div');\n          tempContainer.style.position = 'absolute';\n          tempContainer.style.left = '-9999px';\n          tempContainer.style.width = '800px';\n          tempContainer.style.background = '#fff';\n          tempContainer.style.padding = '20px';\n          document.body.appendChild(tempContainer);\n          \n          // 构建PDF标题和基本信息\n          const title = chartConfig.title || '数据分析图表';\n          const currentDate = new Date().toLocaleString();\n          const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n          \n          let htmlContent = `\n            <div style=\"font-family: Arial, sans-serif; padding: 20px;\">\n              <h1 style=\"text-align: center; color: #333; font-size: 24px; margin-bottom: 30px;\">${title}</h1>\n              <div style=\"text-align: right; margin-bottom: 20px;\">\n                <p style=\"color: #666; margin: 5px 0;\">数据集: ${datasetName}</p>\n                <p style=\"color: #666; margin: 5px 0;\">生成时间: ${currentDate}</p>\n              </div>\n              \n              <div style=\"margin: 20px 0;\">\n                <h2 style=\"color: #333; font-size: 20px; margin-bottom: 15px;\">分析描述:</h2>\n          `;\n          \n          // 添加描述\n          let description = chartMessage.content;\n          \n          // 清理HTML标签，保留纯文本\n          if (description.includes('<')) {\n            const tempDiv = document.createElement('div');\n            tempDiv.innerHTML = description;\n            description = tempDiv.textContent || tempDiv.innerText || '';\n          }\n          \n          htmlContent += `\n            <p style=\"margin: 15px 0; color: #333; line-height: 1.6;\">${description}</p>\n            \n            <div style=\"margin: 30px 0;\">\n              <div id=\"chart-container\" style=\"height: 400px; margin: 30px 0;\"></div>\n              \n              <div style=\"margin: 30px 0;\">\n                <h3 style=\"color: #333; font-size: 18px; margin-bottom: 15px;\">数据表格:</h3>\n                <div id=\"data-table\"></div>\n              </div>\n            </div>\n          `;\n          \n          htmlContent += `</div>`;\n          \n          // 设置HTML内容\n          tempContainer.innerHTML = htmlContent;\n          \n          // 渲染图表\n          const chartContainer = tempContainer.querySelector('#chart-container');\n          const dataTableContainer = tempContainer.querySelector('#data-table');\n          \n          if (chartContainer && dataTableContainer) {\n            // 初始化图表\n            const chartInstance = echarts.init(chartContainer);\n            let options;\n            \n            // 复用你现有的图表处理逻辑\n            switch(chartConfig.type) {\n              case 'bar':\n                options = this.getBarChartOptions(chartConfig);\n                break;\n              case 'line':\n                options = this.getLineChartOptions(chartConfig);\n                break;\n              case 'pie':\n                options = this.getPieChartOptions(chartConfig);\n                break;\n              case 'bar-horizontal':\n                options = this.getBarHorizontalOptions(chartConfig);\n                break;\n              default:\n                options = this.getDefaultOptions(chartConfig);\n            }\n            \n            chartInstance.setOption(options);\n            \n            // 渲染数据表格\n            this.renderDataTable(dataTableContainer, chartConfig);\n            \n            // 等待图表渲染完成\n            await new Promise(resolve => setTimeout(resolve, 1000));\n            \n            // 使用html2canvas捕获当前页面内容\n            const canvas = await html2canvas(tempContainer, {\n              scale: 2,\n              useCORS: true,\n              allowTaint: true,\n              backgroundColor: '#ffffff'\n            });\n            \n            // 将canvas转为图像\n            const imgData = canvas.toDataURL('image/png');\n            \n            // 计算适当的宽度和高度以适应A4页面\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            const pageHeight = pdf.internal.pageSize.getHeight();\n            const imgWidth = pageWidth - 40; // 左右各20mm边距\n            const imgHeight = (canvas.height * imgWidth) / canvas.width;\n            \n            // 如果图像高度超过页面高度，进行缩放\n            let finalImgHeight = imgHeight;\n            let finalImgWidth = imgWidth;\n            \n            if (imgHeight > pageHeight - 40) { // 上下各20mm边距\n              finalImgHeight = pageHeight - 40;\n              finalImgWidth = (canvas.width * finalImgHeight) / canvas.height;\n            }\n            \n            // 添加图像到PDF，居中显示\n            const xPos = (pageWidth - finalImgWidth) / 2;\n            const yPos = 20; // 顶部边距\n            \n            pdf.addImage(imgData, 'PNG', xPos, yPos, finalImgWidth, finalImgHeight);\n            \n            // 清理\n            chartInstance.dispose();\n            document.body.removeChild(tempContainer);\n          }\n        }\n        \n        // 4. 保存PDF\n        const fileName = `指标分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;\n        pdf.save(fileName);\n        \n        // 5. 重置状态\n        this.exportingAll = false;\n        this.$message.success('指标导出成功');\n        \n      } catch (error) {\n        console.error('指标导出失败:', error);\n        this.exportingAll = false;\n        this.$message.error('指标导出失败: ' + error.message);\n      }\n    },\n    \n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n      \n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n      \n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n      \n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n          \n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n          \n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n          \n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      \n      return options;\n    },\n    \n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      \n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n      \n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        \n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n      \n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n      \n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n      \n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n        \n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n        \n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        \n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n      \n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #ffffff;\n}\n\n.app-layout {\n  display: flex;\n  height: 100vh; /* 占满整个视口高度 */\n  overflow: hidden;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  background-color: #f9f9f9;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 25px;\n  text-align: center;\n  white-space: nowrap;\n  letter-spacing: 2px;\n  font-weight: 900;\n}\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  /* overflow-x: auto; */\n  display: flex;\n  flex-direction: column; /* 垂直排列子元素 */\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n  height: 100vh; /* 占满整个视口高度 */\n  box-sizing: border-box; /* 包含padding和border */\n}\n\n.header {\n  margin-bottom: 20px;\n  width: 100%; /* 撑满父容器宽度 */\n   text-align: center; /* 让文本内容居中 */\n}\n\n.header h2 {\n  margin: 0;\n  font-size: 30px;\n}\n\n.highlight {\n  color: #409eff;\n}\n\n.sub-title {\n  color: #606266;\n  font-size: 14px;\n  margin: 5px 0 0 0;\n}\n\n/* 数据卡片容器 */\n.data-card-wrapper {\n  position: relative;\n  margin-bottom: 15px;\n}\n\n.data-card {\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  width: 280px;\n  height: 120px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  background-color: #ffffff;\n  border: 1px solid #e4e7ed;\n}\n\n.data-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n/* 卡片预览弹窗 */\n.card-preview-popup {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n  padding: 16px;\n  min-width: 200px;\n  border: 1px solid #e4e7ed;\n}\n\n.preview-content h4 {\n  margin: 0 0 8px 0;\n  font-size: 16px;\n  color: #303133;\n}\n\n.preview-desc {\n  margin: 0 0 12px 0;\n  color: #606266;\n  font-size: 14px;\n}\n\n.preview-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.preview-actions .el-button {\n  flex: 1;\n}\n\n.data-header {\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.sample-tag {\n  background-color: #ecf5ff;\n  color: #409eff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n.data-title {\n  margin-left: 5px;\n  margin-right: 5px;\n  font-weight: bold;\n}\n\n.common-tag {\n  background-color: #f5f7fa;\n  color: #909399;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-left: 5px;\n}\n\n.data-fields {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.field-item {\n  background-color: #f5f7fa;\n  padding: 2px 8px;\n  margin: 4px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.multi-line-input .el-input__inner {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  height: auto !important;\n  line-height: inherit;\n  padding: 6px 10px;\n  min-height: 40px;\n}\n\n/* 底部问题输入区域（关键修改） */\n.question-input-container {\n  /* 移除固定定位 */\n  /* position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%; */\n  \n  width: 100%;\n    /* background-color: #fff; */\n    /* border-top: 1px solid #ebeef5; */\n    padding: 25px 15px;\n    /* box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05); */\n    display: flex\n;\n    flex-direction: column;\n    align-items: center;\n    margin-top: 20px;\n}\n\n.question-input-container > span {\n  margin-bottom: 12px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.question-input-wrapper {\n  align-items: center;\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n  background-color: #f5f7fa;\n  border-radius: 20px;\n  padding: 8px 15px;\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n  float: right;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: #fff;\n  border: 1px solid #dcdfe6;\n  margin-left: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n}\n\n.send-btn {\n  background-color: #409eff;\n  color: white;\n  border: none;\n}\n\n.test-btn {\n  background-color: #67c23a;\n  color: white;\n  border: none;\n}\n\n.debug-btn {\n  background-color: #909399;\n  color: white;\n  border: none;\n}\n\n.send-btn:disabled {\n  background-color: #c0c4cc;\n  cursor: not-allowed;\n}\n\n/* Suggested Questions Panel */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n\n/* Message list style */\n.message-list {\n  width: 100%;\n  max-height: calc(100vh - 250px); /* 修改为响应式高度，减去头部和底部的高度 */\n  overflow-y: auto;\n  margin-top: 20px;\n  padding: 10px;\n  background-color: transparent;\n  border-radius: 8px;\n  flex: 1; /* 让消息列表占据剩余空间 */\n}\n\n.message {\n  margin-bottom: 20px;\n  display: flex;\n  position: relative;\n  max-width: 80%;\n  margin-left: 150px;\n}\n\n.message-content {\n  padding: 12px 15px;\n  border-radius: 6px;\n  line-height: 1.5;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n  align-self: flex-end;\n  margin-left: auto;\n}\n\n.user-message .message-content {\n  background-color: #ecf5ff;\n  margin-right: 10px;\n}\n\n.bot-message {\n  align-self: flex-start;\n}\n\n.bot-message .message-content {\n  background-color: #f5f7fa;\n  margin-left: 10px;\n}\n\n.avatar-container {\n  display: flex;\n  align-items: flex-start;\n}\n\n.bot-avatar, .user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bot-avatar {\n  background-color: #4caf50;\n  color: white;\n}\n\n.user-avatar {\n  background-color: #2196f3;\n  color: white;\n}\n\n/* Loading animation */\n.loading-dots {\n  display: inline-block;\n}\n\n.dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  background-color: #999;\n  border-radius: 50%;\n  margin: 0 2px;\n  animation: pulse 1.2s infinite ease-in-out both;\n}\n\n.dot:nth-child(2) {\n  animation-delay: -0.4s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: -0.8s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(0.6);\n    opacity: 0.4;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* Styles related to charts */\n.chart-container {\n  position: relative;\n  margin-top: 15px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 10px;\n  background-color: #fff;\n}\n\n/* 确保图表容器有足够高度 */\n.chart-canvas {\n  min-height: 300px;\n}\n\n.chart-notice {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n  font-weight: bold;\n}\n\n/* AI original response popup layer style */\n.raw-response-panel {\n  position: fixed;\n  bottom: 100px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 800px;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n  z-index: 1000;\n  max-height: 60%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.raw-response-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 16px;\n  color: #303133;\n  background-color: #f5f7fa;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.raw-response-title .el-icon-monitor {\n  margin-right: 8px;\n}\n\n.raw-response-title .close-btn {\n  background-color: #f5f7fa;\n  border: 1px solid #ebeef5;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.raw-response-title .close-btn:hover {\n  background-color: #ebeef5;\n}\n\n.raw-response-content {\n  flex: 1;\n  padding: 15px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #303133;\n  white-space: pre-wrap;\n  word-break: break-all;\n  overflow-wrap: break-word;\n}\n\n/* 添加PDF导出相关样式 */\n.chart-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-bottom: 10px;\n}\n\n/* 数据限制提示样式 */\n.data-limit-notice {\n  margin-top: 10px;\n  padding: 8px;\n  background-color: #f0f9eb;\n  color: #67c23a;\n  border-radius: 4px;\n  font-size: 13px;\n  text-align: center;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;AAgcA,SAAAA,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,YAAA;AACA;AACA,OAAAC,WAAA;AACA,SAAAC,KAAA;AACA,YAAAC,OAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MAAA;MACAC,QAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;MACAC,oBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,MAAA;MAAA;MACAC,SAAA;MAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MAAA;MACAC,sBAAA;MAAA;MACAC,WAAA;MACAC,SAAA;MACAC,YAAA;MAAA;;MAEA;MACAC,eAAA;MAAA;MACAC,oBAAA;MAAA;MACAC,WAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,cAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,YAAA,CAAAC,OAAA,kBAAAb,QAAA;IACA,KAAAc,UAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;IACA;IACA,KAAA/B,kBAAA,IACA,gBACA,kBACA,kBACA,mBACA;EACA;EACAgC,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,eAAA;MACA,KAAAN,UAAA;MACA,KAAAnB,MAAA;IACA;IACA;IACAoB,aAAA;MACA,IAAAM,cAAA,GAAAT,YAAA,CAAAU,OAAA;MACA,KAAAD,cAAA;QACAA,cAAA,QAAAE,YAAA,CAAAzD,MAAA;QACA8C,YAAA,CAAAC,OAAA,mBAAAQ,cAAA;MACA;MACA,KAAA/B,QAAA,GAAA+B,cAAA;IACA;IAEA;IACAE,aAAAC,IAAA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAG,MAAA,IAAAD,CAAA,MAAAA,CAAA;QACA,MAAAE,QAAA,GAAAJ,IAAA,CAAAE,CAAA;QACAD,MAAA,GAAAA,MAAA,SAAAI,QAAA,CAAAD,QAAA;MACA;MACA,OAAAH,MAAA;IACA;IAEA;IACAT,kBAAA;MACA,KAAA3B,QAAA,CAAAyC,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IAEA;IACAf,eAAA;MACA,SAAAgB,KAAA,CAAA1C,cAAA;QACA,KAAA0C,KAAA,CAAA1C,cAAA,CAAA2C,SAAA,QAAAD,KAAA,CAAA1C,cAAA,CAAA4C,YAAA;MACA;IACA;IAEA,MAAAtB,WAAA;MACA;QACA,MAAAuB,GAAA,SAAAzE,OAAA,CAAA0E,YAAA;QACA,IAAAD,GAAA,CAAA/D,IAAA,IAAA+D,GAAA,CAAA/D,IAAA,CAAAiE,IAAA;UACA,KAAA9D,MAAA,GAAA4D,GAAA,CAAA/D,IAAA,CAAAA,IAAA;UACA,KAAAI,QAAA,QAAA8D,eAAA,MAAA/D,MAAA;UACA,KAAAE,gBAAA,QAAAD,QAAA;QACA;UACA,KAAAD,MAAA;UACA,KAAAC,QAAA;UACA,KAAAC,gBAAA;QACA;MACA,SAAA8D,CAAA;QACA,KAAAhE,MAAA;QACA,KAAAC,QAAA;QACA,KAAAC,gBAAA;MACA;IACA;IACA;IACA6D,gBAAAE,IAAA;MACA,IAAAC,MAAA;MACA,WAAAC,IAAA,IAAAF,IAAA;QACA,IAAAE,IAAA,CAAAC,IAAA;UACAF,MAAA,CAAAb,IAAA,CAAAc,IAAA;QACA,WAAAA,IAAA,CAAAE,QAAA,IAAAF,IAAA,CAAAE,QAAA,CAAAnB,MAAA;UACAgB,MAAA,GAAAA,MAAA,CAAAI,MAAA,MAAAP,eAAA,CAAAI,IAAA,CAAAE,QAAA;QACA;MACA;MACA,OAAAH,MAAA;IACA;IACA;IACAK,gBAAA;MACA,MAAAC,OAAA,QAAArE,aAAA,CAAAsE,IAAA,GAAAC,WAAA;MACA,KAAAF,OAAA;QACA,KAAAtE,gBAAA,QAAAD,QAAA;MACA;QACA,KAAAC,gBAAA,QAAAD,QAAA,CAAA0E,MAAA,CAAAC,EAAA,IAAAA,EAAA,CAAAjF,IAAA,IAAAiF,EAAA,CAAAjF,IAAA,CAAA+E,WAAA,GAAAG,QAAA,CAAAL,OAAA;MACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAM,eAAA;MACA,UAAA1D,oBAAA,UAAAC,aAAA,CAAA6B,MAAA;QACA,KAAA6B,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAC,UAAA,QAAA5D,aAAA,CAAA6D,GAAA,CAAAC,KAAA;QACA;QACA;UACAC,EAAA,EAAAD,KAAA,CAAAC,EAAA;UACAC,UAAA,EAAAF,KAAA,CAAAE,UAAA;UACA1F,IAAA,EAAAwF,KAAA,CAAAxF,IAAA;UACA2F,YAAA,EAAAH,KAAA,CAAAG,YAAA;UACAC,SAAA,EAAAJ,KAAA,CAAAI,SAAA;UACAC,IAAA,EAAAL,KAAA,CAAAK,IAAA;UACAC,YAAA,EAAAN,KAAA,CAAAM,YAAA;UACAC,cAAA,EAAAP,KAAA,CAAAO,cAAA;UACAC,cAAA,EAAAR,KAAA,CAAAQ,cAAA;QACA;MACA;;MAEA;MACA,KAAA/D,eAAA;QACAwD,EAAA,OAAAhE,oBAAA,CAAAgE,EAAA;QACAzF,IAAA,OAAAyB,oBAAA,CAAAzB,IAAA;QACAiG,MAAA,EAAAX;MACA;;MAEA;MACA,KAAAzD,sBAAA,QAAAI,eAAA;;MAEA;MACA,KAAAC,oBAAA;MACA,KAAAG,kBAAA;;MAEA;MACA,KAAArB,aAAA;;MAEA;MACA,KAAAoE,QAAA,CAAAc,OAAA,gBAAAzE,oBAAA,CAAAzB,IAAA;;MAEA;MACA,KAAAmG,SAAA;QACA,MAAAC,OAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAF,OAAA,EAAAA,OAAA,CAAAG,KAAA;MACA;IACA;IAEA;IACAC,gBAAAC,KAAA,EAAAC,KAAA;MACA,KAAAvE,WAAA,GAAAsE,KAAA;MACA,MAAAE,IAAA,GAAAD,KAAA,CAAAE,aAAA,CAAAC,qBAAA;MACA,KAAAzE,iBAAA;QACA0E,QAAA;QACAC,GAAA,EAAAJ,IAAA,CAAAI,GAAA;QACAC,IAAA,EAAAL,IAAA,CAAAM,KAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAC,gBAAA;MACA,KAAAhF,WAAA;IACA;IAEA;IACAiF,eAAAX,KAAA;MACA,KAAAU,eAAA;MACA,KAAAE,iBAAA,CAAAZ,KAAA;IACA;IAEA;IACAa,yBAAAb,KAAA;MACA,KAAAU,eAAA;MACA;MACA,KAAA1F,oBAAA,GAAAgF,KAAA;MACA,KAAA/E,aAAA,GAAA+E,KAAA,CAAAR,MAAA;MACA,KAAAd,cAAA;IACA;IAEA;IACAoC,uBAAA;MACA,SAAAtF,eAAA;QACA,KAAAoF,iBAAA,MAAApF,eAAA;MACA;IACA;IAEA;IACAuF,cAAA;MACA,KAAAvF,eAAA;MACA,KAAAC,oBAAA;MACA,KAAAG,kBAAA;MACA,KAAAR,sBAAA;MACA,KAAAuD,QAAA,CAAAqC,IAAA;IACA;IAEA;IACAC,wBAAA;MACA,UAAAjG,oBAAA;MAEA,MAAAkG,WAAA,QAAAlG,oBAAA,CAAAzB,IAAA;MACA,MAAA4H,eAAA,QAAAC,kBAAA;MACA,MAAAC,YAAA,QAAAC,eAAA;MAEA,MAAAC,SAAA;MAEA,IAAAJ,eAAA,CAAArE,MAAA,QAAAuE,YAAA,CAAAvE,MAAA;QACAyE,SAAA,CAAAtE,IAAA,OAAAkE,eAAA,IAAA5H,IAAA,IAAA8H,YAAA,IAAA9H,IAAA;QACAgI,SAAA,CAAAtE,IAAA,OAAAkE,eAAA,IAAA5H,IAAA,IAAA8H,YAAA,IAAA9H,IAAA;MACA;MAEA,IAAA4H,eAAA,CAAArE,MAAA;QACAyE,SAAA,CAAAtE,IAAA,OAAAkE,eAAA,IAAA5H,IAAA,IAAA4H,eAAA,IAAA5H,IAAA;MACA;MAEAgI,SAAA,CAAAtE,IAAA,OAAAiE,WAAA;MAEA,OAAAK,SAAA,CAAAC,KAAA;IACA;IAEA;IACAC,uBAAAxH,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAyE,cAAA;MACA,KAAAgB,SAAA;QACA,KAAAgC,cAAA;MACA;IACA;IAEA;IACAN,mBAAA;MACA,YAAAnG,aAAA,CAAAsD,MAAA,CAAAQ,KAAA,IAAAA,KAAA,CAAAI,SAAA;IACA;IAEA;IACAmC,gBAAA;MACA,YAAArG,aAAA,CAAAsD,MAAA,CAAAQ,KAAA,IAAAA,KAAA,CAAAI,SAAA;IACA;IAEA;IACAwC,eAAA;MACA,YAAA1G,aAAA,CAAAsD,MAAA,CAAAQ,KAAA,KAAAA,KAAA,CAAAI,SAAA,IAAAJ,KAAA,CAAAI,SAAA,YAAAJ,KAAA,CAAAI,SAAA;IACA;IAEA;IACAyC,mBAAA;MACA,SAAA5G,oBAAA;QACA,KAAAa,cAAA;QACA;QACAgG,UAAA;UACA,KAAAhG,cAAA;UACA,KAAA8C,QAAA,CAAAc,OAAA;QACA;MACA;IACA;IAEA,MAAAmB,kBAAAkB,OAAA;MACA;MACAC,OAAA,CAAAC,GAAA,8BAAAF,OAAA;;MAEA;MACA,KAAAA,OAAA,KAAAA,OAAA,CAAA9C,EAAA;QACA+C,OAAA,CAAAE,KAAA,cAAAH,OAAA;QACA;QACA;MACA;;MAEA;MACA,MAAAI,OAAA;QACA,mBAAA/G,QAAA;QACA;QACA;QACA;MACA;MAEA;QACA4G,OAAA,CAAAC,GAAA,kBAAAF,OAAA,CAAA9C,EAAA;QACA,MAAAxB,GAAA,SAAAzE,OAAA,CAAAoJ,gBAAA,CAAAL,OAAA,CAAA9C,EAAA,SAAAkD,OAAA;QACAH,OAAA,CAAAC,GAAA,gBAAAxE,GAAA;QAEA,IAAAA,GAAA,CAAA/D,IAAA,IAAA+D,GAAA,CAAA/D,IAAA,CAAAiE,IAAA;UACA,MAAA0E,MAAA,GAAA5E,GAAA,CAAA/D,IAAA,CAAAA,IAAA;UACAsI,OAAA,CAAAC,GAAA,aAAAI,MAAA;UACA,KAAApH,oBAAA,GAAAoH,MAAA;UACA;UACA,KAAAnH,aAAA,GAAAmH,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAA3I,IAAA,IAAA2I,MAAA,CAAA3I,IAAA,CAAA+F,MAAA;UACA;UACA,KAAAtE,WAAA,GAAAkH,MAAA,CAAA3I,IAAA,IAAA2I,MAAA,CAAA3I,IAAA,CAAAA,IAAA;UACA;UACAsI,OAAA,CAAAC,GAAA,eAAA/G,aAAA,CAAA6B,MAAA;UACAiF,OAAA,CAAAC,GAAA,eAAA9G,WAAA,CAAA4B,MAAA;UAEA,SAAA7B,aAAA,CAAA6B,MAAA;YACAiF,OAAA,CAAAO,IAAA;UACA;UAEA,SAAApH,WAAA,CAAA4B,MAAA;YACAiF,OAAA,CAAAO,IAAA;UACA;QACA;UACAP,OAAA,CAAAE,KAAA,aAAAzE,GAAA,CAAA/D,IAAA;UACA8I,KAAA,aAAA/E,GAAA,CAAA/D,IAAA,CAAA+I,GAAA;UACA,KAAAxH,oBAAA;UACA,KAAAC,aAAA;UACA,KAAAC,WAAA;QACA;QACA,KAAAX,aAAA;MACA,SAAAqD,CAAA;QACAmE,OAAA,CAAAE,KAAA,eAAArE,CAAA;QACA2E,KAAA,UAAA3E,CAAA,CAAA6E,OAAA;QACA,KAAAzH,oBAAA;QACA,KAAAC,aAAA;QACA,KAAAC,WAAA;QACA,KAAAX,aAAA;MACA;IACA;IACAmI,gBAAA;MACA,KAAAvI,oBAAA,SAAAA,oBAAA;IACA;IACAwI,YAAAC,CAAA;MACA,KAAA3I,QAAA,GAAA2I,CAAA;MACA,KAAAzI,oBAAA;IACA;IACA0I,eAAA7C,KAAA;MACA,IAAAA,KAAA,CAAA8C,SAAA;QACA;MACA;MACA;IACA;IAEA;IACA,MAAApB,eAAA;MACA,UAAAzH,QAAA,CAAAoE,IAAA,WAAA3D,SAAA;QACA,KAAAiE,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA;MACA,IAAAmE,SAAA,QAAA9I,QAAA,CAAAoE,IAAA;;MAEA;MACA,SAAAjD,sBAAA;QACA;QACA,MAAA4H,WAAA;UACAC,SAAA,OAAA7H,sBAAA,CAAA4D,EAAA;UACAkC,WAAA,OAAA9F,sBAAA,CAAA7B,IAAA;UACAiG,MAAA,OAAApE,sBAAA,CAAAoE;QACA;;QAEA;QACAuD,SAAA,GAAAG,IAAA,CAAAC,SAAA;UACAlJ,QAAA,OAAAA,QAAA,CAAAoE,IAAA;UACAyD,OAAA,EAAAkB;QACA;MACA;;MAEA;MACA,MAAAI,OAAA;QACAlG,MAAA;QACAC,OAAA,OAAAlD,QAAA,CAAAoE,IAAA;QACAjB,QAAA;MACA;MACA,KAAA5C,QAAA,CAAAyC,IAAA,CAAAmG,OAAA;;MAEA;MACA,MAAAC,MAAA;QACAnG,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAA5C,QAAA,CAAAyC,IAAA,CAAAoG,MAAA;;MAEA;MACA,MAAAC,OAAA,QAAA9I,QAAA,MAAAA,QAAA,CAAAsC,MAAA;;MAEA;MACA,MAAA7C,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA;MACA,KAAAS,SAAA;MAEA;QACA;QACA,MAAA+H,OAAA,QAAArH,sBAAA,GACA2H,SAAA,GACA,GAAA9I,QAAA;;QAEA;QACA,MAAAf,KAAA,CAAAqK,IAAA,CACA,4CACA;UAAA9I,QAAA,OAAAA,QAAA;UAAAgI;QAAA,GACA;UACAe,YAAA;UACAC,kBAAA,EAAA7F,CAAA;YACA,MAAA8F,QAAA,GAAA9F,CAAA,CAAAqC,KAAA,CAAA0D,MAAA,CAAAC,YAAA;YACA,IAAAC,OAAA,GAAAH,QAAA,CAAAI,SAAA,CAAAR,OAAA,CAAAnG,OAAA,CAAAL,MAAA;YACAwG,OAAA,CAAAnG,OAAA,IAAA0G,OAAA;YACA,KAAAxH,cAAA;;YAEA;YACA,KAAAxB,eAAA,GAAA6I,QAAA;UACA;QACA,CACA;;QAEA;QACAJ,OAAA,CAAAlG,QAAA;QACA,KAAA1C,SAAA;;QAEA;QACA,KAAAqJ,gBAAA,CAAAT,OAAA;MACA,SAAArB,KAAA;QACAF,OAAA,CAAAE,KAAA,UAAAA,KAAA;QACAqB,OAAA,CAAAnG,OAAA;QACAmG,OAAA,CAAAlG,QAAA;QACA,KAAA1C,SAAA;MACA;IACA;IAEA;IACAqJ,iBAAAtB,OAAA;MACAV,OAAA,CAAAC,GAAA,qBAAAS,OAAA,CAAAtF,OAAA;;MAEA;MACA,MAAA6G,gBAAA,GAAAvB,OAAA,CAAAtF,OAAA,CAAA8G,KAAA;MACA,IAAAD,gBAAA,IAAAA,gBAAA;QACA,MAAAE,WAAA,GAAAF,gBAAA;QACAjC,OAAA,CAAAC,GAAA,cAAAkC,WAAA;;QAEA;QACA,KAAAC,kBAAA,CAAAD,WAAA,EAAAzB,OAAA;QACA;MACA;;MAEA;MACA,MAAA2B,gBAAA,GAAA3B,OAAA,CAAAtF,OAAA,CAAA8G,KAAA;MACA,IAAAG,gBAAA,IAAAA,gBAAA;QACArC,OAAA,CAAAC,GAAA,eAAAoC,gBAAA;QAEA;UACA,IAAAC,WAAA,GAAAnB,IAAA,CAAAoB,KAAA,CAAAF,gBAAA;UACArC,OAAA,CAAAC,GAAA,gBAAAqC,WAAA;;UAEA;UACA,IAAAA,WAAA,CAAA3G,IAAA,UAAA2G,WAAA,CAAA5K,IAAA;YACAsI,OAAA,CAAAC,GAAA,yBAAAqC,WAAA,CAAA5K,IAAA;YACA4K,WAAA,GAAAA,WAAA,CAAA5K,IAAA;UACA;;UAEA;UACA,IAAA4K,WAAA,CAAAjF,IAAA,KAAAiF,WAAA,CAAApB,SAAA,IAAAoB,WAAA,CAAAE,OAAA,IAAAF,WAAA,CAAA5K,IAAA;YACAsI,OAAA,CAAAC,GAAA,eAAAqC,WAAA;YACA5B,OAAA,CAAA4B,WAAA,GAAAA,WAAA;;YAEA;YACA5B,OAAA,CAAAtF,OAAA,GAAAsF,OAAA,CAAAtF,OAAA,CAAAqH,OAAA,6BACA;YAEA;UACA;YACAzC,OAAA,CAAAC,GAAA,gBAAAqC,WAAA;UACA;QACA,SAAApC,KAAA;UACAF,OAAA,CAAAE,KAAA,cAAAA,KAAA;QACA;MACA;MAEAF,OAAA,CAAAC,GAAA;;MAEA;MACA;QACA;QACA,MAAAyC,SAAA;QACA,MAAAC,SAAA,GAAAjC,OAAA,CAAAtF,OAAA,CAAA8G,KAAA,CAAAQ,SAAA;QAEA,IAAAC,SAAA;UACA3C,OAAA,CAAAC,GAAA,kBAAA0C,SAAA;;UAEA;UACA;UACA,MAAAC,OAAA,GAAAD,SAAA,IAAAF,OAAA;UACAzC,OAAA,CAAAC,GAAA,iBAAA2C,OAAA;UAEA;YACA,MAAAN,WAAA,GAAAnB,IAAA,CAAAoB,KAAA,CAAAK,OAAA;YACA5C,OAAA,CAAAC,GAAA,gBAAAqC,WAAA;YAEA,IAAAA,WAAA,CAAA3G,IAAA,UAAA2G,WAAA,CAAA5K,IAAA;cACA,MAAAA,IAAA,GAAA4K,WAAA,CAAA5K,IAAA;cACAsI,OAAA,CAAAC,GAAA,aAAAvI,IAAA;;cAEA;cACA,IAAAA,IAAA,CAAA2F,IAAA,KAAA3F,IAAA,CAAA8K,OAAA,IAAA9K,IAAA,CAAAA,IAAA;gBACAsI,OAAA,CAAAC,GAAA,eAAAvI,IAAA;gBACAgJ,OAAA,CAAA4B,WAAA,GAAA5K,IAAA;gBACAgJ,OAAA,CAAAtF,OAAA,GAAAsF,OAAA,CAAAtF,OAAA,CAAAqH,OAAA,CAAAE,SAAA,KACA;gBACA;cACA;YACA;UACA,SAAAE,UAAA;YACA7C,OAAA,CAAAC,GAAA,cAAA4C,UAAA;UACA;QACA;MACA,SAAA3C,KAAA;QACAF,OAAA,CAAAC,GAAA,aAAAC,KAAA;MACA;;MAEA;MACA,IAAAQ,OAAA,CAAAtF,OAAA,CAAAsB,QAAA,iBACAgE,OAAA,CAAAtF,OAAA,CAAAsB,QAAA;QACAsD,OAAA,CAAAC,GAAA;;QAEA;QACA,MAAA6C,aAAA;UACA7F,EAAA,EAAA8F,IAAA,CAAAC,GAAA,GAAAC,QAAA;UACA5F,IAAA;UACA6F,KAAA;UACAV,OAAA,OAAAvK,aAAA,EAAAgF,EAAA;QACA;QAEA+C,OAAA,CAAAC,GAAA,eAAA6C,aAAA;QACApC,OAAA,CAAA4B,WAAA,GAAAQ,aAAA;MACA;IACA;IAEA;IACA,MAAAV,mBAAAD,WAAA,EAAAzB,OAAA;MACA;QACAV,OAAA,CAAAC,GAAA,qBAAAkC,WAAA;QACA,MAAAgB,QAAA,SAAAhM,KAAA,CAAAiM,GAAA,yCAAAjB,WAAA;QAEA,IAAAgB,QAAA,CAAAzL,IAAA,IAAAyL,QAAA,CAAAE,MAAA;UACArD,OAAA,CAAAC,GAAA,cAAAkD,QAAA,CAAAzL,IAAA;;UAEA;UACA,IAAA4K,WAAA,GAAAa,QAAA,CAAAzL,IAAA;UACA,IAAA4K,WAAA,CAAA3G,IAAA,UAAA2G,WAAA,CAAA5K,IAAA;YACA4K,WAAA,GAAAA,WAAA,CAAA5K,IAAA;UACA;;UAEA;UACAgJ,OAAA,CAAA4B,WAAA,GAAAA,WAAA;;UAEA;UACA,KAAA5B,OAAA,CAAAtF,OAAA,CAAAsB,QAAA;YACAgE,OAAA,CAAAtF,OAAA;UACA;;UAEA;UACA,KAAAkI,YAAA;QACA;UACAtD,OAAA,CAAAE,KAAA,cAAAiD,QAAA;UACAzC,OAAA,CAAAtF,OAAA;QACA;MACA,SAAA8E,KAAA;QACAF,OAAA,CAAAE,KAAA,cAAAA,KAAA;QACAQ,OAAA,CAAAtF,OAAA,0CAAA8E,KAAA,CAAAQ,OAAA;MACA;IACA;IAEA;IACA6C,UAAA;MACA;MACA,MAAAC,eAAA;QACAvG,EAAA;QACAI,IAAA;QACA6F,KAAA;QACAhC,SAAA;QACAsB,OAAA;MACA;;MAEA;MACA,MAAAlB,MAAA;QACAnG,MAAA;QACAC,OAAA;QACAC,QAAA;QACAiH,WAAA,EAAAkB;MACA;MAEA,KAAA/K,QAAA,CAAAyC,IAAA,CAAAoG,MAAA;;MAEA;MACA,KAAAxI,eAAA,GAAAqI,IAAA,CAAAC,SAAA,CAAAoC,eAAA;;MAEA;MACAxD,OAAA,CAAAC,GAAA,iBAAAxH,QAAA;;MAEA;MACA,MAAAgL,eAAA;QACA9H,IAAA;QACA8E,GAAA;QACA/I,IAAA;UACA2F,IAAA;UACA3F,IAAA,GACA;YAAAsF,KAAA;YAAA0G,KAAA;UAAA,GACA;YAAA1G,KAAA;YAAA0G,KAAA;UAAA,GACA;YAAA1G,KAAA;YAAA0G,KAAA;UAAA,EACA;UACAC,OAAA;YAAAnM,IAAA;UAAA;QACA;MACA;;MAEA;MACA,MAAAoM,WAAA,iBAAAzC,IAAA,CAAAC,SAAA,CAAAqC,eAAA;MACA,MAAAI,cAAA;QACA1I,MAAA;QACAC,OAAA,gBAAAwI,WAAA;QACAvI,QAAA;MACA;MAEA,KAAA5C,QAAA,CAAAyC,IAAA,CAAA2I,cAAA;MACA,KAAA7B,gBAAA,CAAA6B,cAAA;;MAEA;MACA,KAAA/K,eAAA,GAAA8K,WAAA;MAEA5D,OAAA,CAAAC,GAAA,iBAAA4D,cAAA;IACA;IAEA;IACAC,aAAA;MACA9D,OAAA,CAAAC,GAAA;;MAEA;MACA,MAAA8D,QAAA;QACApI,IAAA;QACA8E,GAAA;QACA/I,IAAA;UACAuF,EAAA;UACAiG,KAAA;UACAV,OAAA;UACAnF,IAAA;UACA3F,IAAA;YACAA,IAAA,GACA;cAAAgM,KAAA;cAAA1G,KAAA;cAAAxF,IAAA;cAAAwM,QAAA;YAAA,GACA;cAAAN,KAAA;cAAA1G,KAAA;cAAAxF,IAAA;cAAAwM,QAAA;YAAA,GACA;cAAAN,KAAA;cAAA1G,KAAA;cAAAxF,IAAA;cAAAwM,QAAA;YAAA,EACA;YACAvG,MAAA,GACA;cAAAR,EAAA;cAAAzF,IAAA;cAAA4F,SAAA;YAAA,GACA;cAAAH,EAAA;cAAAzF,IAAA;cAAA4F,SAAA;YAAA;UAEA;QACA;MACA;;MAEA;MACA,MAAA6G,WAAA;QACA9I,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MAEA;MACA4I,WAAA,CAAA3B,WAAA,GAAAyB,QAAA,CAAArM,IAAA;;MAEA;MACA,KAAAe,QAAA,CAAAyC,IAAA,CAAA+I,WAAA;;MAEA;MACA,KAAAnL,eAAA,GAAAqI,IAAA,CAAAC,SAAA,CAAA2C,QAAA;MAEA/D,OAAA,CAAAC,GAAA,gBAAAgE,WAAA;IACA;IAEA;IACAC,gBAAA;MACA,KAAArL,oBAAA;MACA,KAAAC,eAAA,QAAAL,QAAA,MAAAA,QAAA,CAAAsC,MAAA,MAAAK,OAAA;IACA;IAEA;IACA,MAAA+I,YAAAzD,OAAA;MACA,KAAAA,OAAA,CAAA4B,WAAA;MAEA;QACA;QACA,KAAA8B,IAAA,CAAA1D,OAAA;;QAEA;QACA,MAAA2D,aAAA,GAAAxG,QAAA,CAAAyG,aAAA;QACAD,aAAA,CAAAE,KAAA,CAAAjG,QAAA;QACA+F,aAAA,CAAAE,KAAA,CAAA/F,IAAA;QACA6F,aAAA,CAAAE,KAAA,CAAAC,KAAA;QACAH,aAAA,CAAAE,KAAA,CAAAE,UAAA;QACAJ,aAAA,CAAAE,KAAA,CAAAG,OAAA;QACA7G,QAAA,CAAA8G,IAAA,CAAAC,WAAA,CAAAP,aAAA;;QAEA;QACA;QACA,MAAAnB,KAAA,GAAAxC,OAAA,CAAA4B,WAAA,CAAAY,KAAA;QACA,MAAA/D,WAAA,QAAAlH,aAAA,QAAAA,aAAA,CAAA4M,SAAA;QACA,MAAAC,WAAA,OAAA/B,IAAA,GAAAgC,cAAA;;QAEA;QACA,IAAApN,WAAA,GAAA+I,OAAA,CAAAtF,OAAA,CACAqH,OAAA,6CACAA,OAAA,8CACAnG,IAAA;;QAEA;QACA,IAAA3E,WAAA,CAAA+E,QAAA;UACA,MAAAsI,OAAA,GAAAnH,QAAA,CAAAyG,aAAA;UACAU,OAAA,CAAAC,SAAA,GAAAtN,WAAA;UACAA,WAAA,GAAAqN,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;QACA;;QAEA;QACAd,aAAA,CAAAY,SAAA;AACA;AACA,2DAAA/B,KAAA;AACA,8DAAA/D,WAAA;AACA,+DAAA2F,WAAA;AACA;;AAEA;AACA;AACA,mBAAAnN,WAAA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;QAEA;QACA,MAAAyN,cAAA,GAAAf,aAAA,CAAAvG,aAAA;QACA,MAAAuH,KAAA,GAAA9N,OAAA,CAAA+N,IAAA,CAAAF,cAAA;;QAEA;QACA,MAAA9C,WAAA,GAAA5B,OAAA,CAAA4B,WAAA;QACA,IAAAiD,OAAA;;QAEA;QACA,QAAAjD,WAAA,CAAAjF,IAAA;UACA;YACAkI,OAAA,QAAAC,kBAAA,CAAAlD,WAAA;YACA;UACA;YACAiD,OAAA,QAAAE,mBAAA,CAAAnD,WAAA;YACA;UACA;YACAiD,OAAA,QAAAG,kBAAA,CAAApD,WAAA;YACA;UACA;YACAiD,OAAA,QAAAI,uBAAA,CAAArD,WAAA;YACA;UACA;YACAiD,OAAA,QAAAK,iBAAA,CAAAtD,WAAA;QACA;;QAEA;QACA+C,KAAA,CAAAQ,SAAA,CAAAN,OAAA;QACAvF,OAAA,CAAAC,GAAA,mBAAAsF,OAAA;;QAEA;QACA,UAAAO,OAAA,CAAAC,OAAA,IAAAjG,UAAA,CAAAiG,OAAA;;QAEA;QACA,MAAAC,kBAAA,GAAA3B,aAAA,CAAAvG,aAAA;QACA,KAAAmI,eAAA,CAAAD,kBAAA,EAAAtF,OAAA,CAAA4B,WAAA;;QAEA;QACA,MAAA4D,MAAA,SAAA7O,WAAA,CAAAgN,aAAA;UACA8B,KAAA;UAAA;UACAC,OAAA;UACAC,UAAA;UACAC,eAAA;QACA;;QAEA;QACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;QACA,MAAAC,GAAA,OAAAnP,KAAA;UACAoP,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,MAAAC,QAAA;QACA,MAAAC,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAA1B,KAAA;;QAEA;QACAiC,GAAA,CAAAO,QAAA,CAAAT,OAAA,iBAAAM,QAAA,EAAAC,SAAA;;QAEA;QACAL,GAAA,CAAAQ,IAAA,IAAA/D,KAAA,QAAAH,IAAA,GAAAmE,OAAA;;QAEA;QACArJ,QAAA,CAAA8G,IAAA,CAAAwC,WAAA,CAAA9C,aAAA;QACAgB,KAAA,CAAA+B,OAAA;;QAEA;QACA,KAAAhD,IAAA,CAAA1D,OAAA;;QAEA;QACA,KAAA9D,QAAA,CAAAc,OAAA;MAEA,SAAAwC,KAAA;QACAF,OAAA,CAAAE,KAAA,aAAAA,KAAA;QACA,KAAAkE,IAAA,CAAA1D,OAAA;QACA,KAAA9D,QAAA,CAAAsD,KAAA,eAAAA,KAAA,CAAAQ,OAAA;MACA;IACA;IAEA;IACA,MAAA2G,sBAAA;MACA;QACA;QACA,KAAA7N,YAAA;;QAEA;QACA,IAAA8N,aAAA;;QAEA;QACA,SAAAxM,CAAA,MAAAA,CAAA,QAAArC,QAAA,CAAAsC,MAAA,EAAAD,CAAA;UACA,MAAA4F,OAAA,QAAAjI,QAAA,CAAAqC,CAAA;UACA,KAAA4F,OAAA,CAAAvF,MAAA,IAAAuF,OAAA,CAAA4B,WAAA;YACAgF,aAAA,CAAApM,IAAA;cACAwF,OAAA,EAAAA,OAAA;cACA6G,KAAA,EAAAzM;YACA;UACA;QACA;;QAEA;QACA,IAAAwM,aAAA,CAAAvM,MAAA;UACA,KAAA6B,QAAA,CAAAC,OAAA;UACA,KAAArD,YAAA;UACA;QACA;;QAEA;QACA,MAAAiN,GAAA,OAAAnP,KAAA;UACAoP,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,SAAA9L,CAAA,MAAAA,CAAA,GAAAwM,aAAA,CAAAvM,MAAA,EAAAD,CAAA;UACA;UACA,IAAAA,CAAA;YACA2L,GAAA,CAAAe,OAAA;UACA;UAEA,MAAAC,YAAA,GAAAH,aAAA,CAAAxM,CAAA,EAAA4F,OAAA;UACA,MAAA4B,WAAA,GAAAmF,YAAA,CAAAnF,WAAA;;UAEA;UACA,MAAA+B,aAAA,GAAAxG,QAAA,CAAAyG,aAAA;UACAD,aAAA,CAAAE,KAAA,CAAAjG,QAAA;UACA+F,aAAA,CAAAE,KAAA,CAAA/F,IAAA;UACA6F,aAAA,CAAAE,KAAA,CAAAC,KAAA;UACAH,aAAA,CAAAE,KAAA,CAAAE,UAAA;UACAJ,aAAA,CAAAE,KAAA,CAAAG,OAAA;UACA7G,QAAA,CAAA8G,IAAA,CAAAC,WAAA,CAAAP,aAAA;;UAEA;UACA,MAAAnB,KAAA,GAAAZ,WAAA,CAAAY,KAAA;UACA,MAAA4B,WAAA,OAAA/B,IAAA,GAAAgC,cAAA;UACA,MAAA5F,WAAA,QAAAlH,aAAA,QAAAA,aAAA,CAAA4M,SAAA;UAEA,IAAA6C,WAAA;AACA;AACA,mGAAAxE,KAAA;AACA;AACA,8DAAA/D,WAAA;AACA,+DAAA2F,WAAA;AACA;;AAEA;AACA;AACA;;UAEA;UACA,IAAAnN,WAAA,GAAA8P,YAAA,CAAArM,OAAA;;UAEA;UACA,IAAAzD,WAAA,CAAA+E,QAAA;YACA,MAAAsI,OAAA,GAAAnH,QAAA,CAAAyG,aAAA;YACAU,OAAA,CAAAC,SAAA,GAAAtN,WAAA;YACAA,WAAA,GAAAqN,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;UACA;UAEAuC,WAAA;AACA,wEAAA/P,WAAA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;UAEA+P,WAAA;;UAEA;UACArD,aAAA,CAAAY,SAAA,GAAAyC,WAAA;;UAEA;UACA,MAAAtC,cAAA,GAAAf,aAAA,CAAAvG,aAAA;UACA,MAAAkI,kBAAA,GAAA3B,aAAA,CAAAvG,aAAA;UAEA,IAAAsH,cAAA,IAAAY,kBAAA;YACA;YACA,MAAA2B,aAAA,GAAApQ,OAAA,CAAA+N,IAAA,CAAAF,cAAA;YACA,IAAAG,OAAA;;YAEA;YACA,QAAAjD,WAAA,CAAAjF,IAAA;cACA;gBACAkI,OAAA,QAAAC,kBAAA,CAAAlD,WAAA;gBACA;cACA;gBACAiD,OAAA,QAAAE,mBAAA,CAAAnD,WAAA;gBACA;cACA;gBACAiD,OAAA,QAAAG,kBAAA,CAAApD,WAAA;gBACA;cACA;gBACAiD,OAAA,QAAAI,uBAAA,CAAArD,WAAA;gBACA;cACA;gBACAiD,OAAA,QAAAK,iBAAA,CAAAtD,WAAA;YACA;YAEAqF,aAAA,CAAA9B,SAAA,CAAAN,OAAA;;YAEA;YACA,KAAAU,eAAA,CAAAD,kBAAA,EAAA1D,WAAA;;YAEA;YACA,UAAAwD,OAAA,CAAAC,OAAA,IAAAjG,UAAA,CAAAiG,OAAA;;YAEA;YACA,MAAAG,MAAA,SAAA7O,WAAA,CAAAgN,aAAA;cACA8B,KAAA;cACAC,OAAA;cACAC,UAAA;cACAC,eAAA;YACA;;YAEA;YACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;;YAEA;YACA,MAAAoB,SAAA,GAAAnB,GAAA,CAAAoB,QAAA,CAAAC,QAAA,CAAAC,QAAA;YACA,MAAAC,UAAA,GAAAvB,GAAA,CAAAoB,QAAA,CAAAC,QAAA,CAAAG,SAAA;YACA,MAAApB,QAAA,GAAAe,SAAA;YACA,MAAAd,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAA1B,KAAA;;YAEA;YACA,IAAA0D,cAAA,GAAApB,SAAA;YACA,IAAAqB,aAAA,GAAAtB,QAAA;YAEA,IAAAC,SAAA,GAAAkB,UAAA;cAAA;cACAE,cAAA,GAAAF,UAAA;cACAG,aAAA,GAAAjC,MAAA,CAAA1B,KAAA,GAAA0D,cAAA,GAAAhC,MAAA,CAAAa,MAAA;YACA;;YAEA;YACA,MAAAqB,IAAA,IAAAR,SAAA,GAAAO,aAAA;YACA,MAAAE,IAAA;;YAEA5B,GAAA,CAAAO,QAAA,CAAAT,OAAA,SAAA6B,IAAA,EAAAC,IAAA,EAAAF,aAAA,EAAAD,cAAA;;YAEA;YACAP,aAAA,CAAAP,OAAA;YACAvJ,QAAA,CAAA8G,IAAA,CAAAwC,WAAA,CAAA9C,aAAA;UACA;QACA;;QAEA;QACA,MAAAiE,QAAA,iBAAAvF,IAAA,GAAAwF,WAAA,GAAA9I,KAAA;QACAgH,GAAA,CAAAQ,IAAA,CAAAqB,QAAA;;QAEA;QACA,KAAA9O,YAAA;QACA,KAAAoD,QAAA,CAAAc,OAAA;MAEA,SAAAwC,KAAA;QACAF,OAAA,CAAAE,KAAA,YAAAA,KAAA;QACA,KAAA1G,YAAA;QACA,KAAAoD,QAAA,CAAAsD,KAAA,cAAAA,KAAA,CAAAQ,OAAA;MACA;IACA;IAEA;IACA8H,wBAAAlG,WAAA;MACA,MAAAjF,IAAA,GAAAiF,WAAA,CAAAjF,IAAA;MACA,IAAA3F,IAAA;MACA,IAAA+Q,UAAA;;MAEA;MACA,IAAAnG,WAAA,CAAA5K,IAAA,IAAA4K,WAAA,CAAA5K,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA4K,WAAA,CAAA5K,IAAA,CAAAA,IAAA;QACA+Q,UAAA,GAAA/Q,IAAA,CAAAqF,GAAA,CAAA2L,IAAA,IAAAA,IAAA,CAAA1L,KAAA,IAAA0L,IAAA,CAAAlR,IAAA;MACA;;MAEA;MACA,MAAA+N,OAAA;QACArC,KAAA;UACAyF,IAAA,EAAArG,WAAA,CAAAY,KAAA;QACA;QACA0F,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAzL,IAAA;UACA3F,IAAA,EAAA+Q;QACA;QACAM,KAAA;UACA1L,IAAA;QACA;QACA2L,MAAA;MACA;;MAEA;MACA,IAAAtR,IAAA,CAAAqD,MAAA;QACA,IAAArD,IAAA,IAAAsR,MAAA;UACA;UACA,MAAAC,SAAA;;UAEA;UACAvR,IAAA,CAAAwR,OAAA,CAAAR,IAAA;YACA,IAAAA,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACA,KAAAF,SAAA,CAAAE,CAAA,CAAAnF,QAAA;kBACAiF,SAAA,CAAAE,CAAA,CAAAnF,QAAA;oBACAxM,IAAA,EAAA2R,CAAA,CAAAnF,QAAA;oBACA3G,IAAA,EAAAA,IAAA;oBACA3F,IAAA,EAAA0R,KAAA,CAAAX,UAAA,CAAA1N,MAAA,EAAAsO,IAAA;kBACA;gBACA;cACA;YACA;UACA;;UAEA;UACA3R,IAAA,CAAAwR,OAAA,EAAAR,IAAA,EAAAnB,KAAA;YACA,IAAAmB,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACAF,SAAA,CAAAE,CAAA,CAAAnF,QAAA,EAAAtM,IAAA,CAAA6P,KAAA,IAAA4B,CAAA,CAAAzF,KAAA;cACA;YACA;UACA;;UAEA;UACA6B,OAAA,CAAAyD,MAAA,GAAAM,MAAA,CAAAC,MAAA,CAAAN,SAAA;QACA;UACA;UACA1D,OAAA,CAAAyD,MAAA,CAAA9N,IAAA;YACA1D,IAAA;YACA6F,IAAA,EAAAA,IAAA;YACA3F,IAAA,EAAAA,IAAA,CAAAqF,GAAA,CAAA2L,IAAA,IAAAA,IAAA,CAAAhF,KAAA;UACA;QACA;MACA;MAEA,OAAA6B,OAAA;IACA;IAEA;IACA;IACAC,mBAAAgE,SAAA;MACAxJ,OAAA,CAAAC,GAAA,gBAAAuJ,SAAA;;MAEA;MACA,IAAA9R,IAAA;MACA,IAAAiM,OAAA;;MAEA;MACA,IAAA6F,SAAA,CAAA9R,IAAA,IAAA0R,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAA9R,IAAA;QACAA,IAAA,GAAA8R,SAAA,CAAA9R,IAAA;QACAiM,OAAA,GAAA6F,SAAA,CAAA7F,OAAA;MACA;MACA;MAAA,KACA,IAAA6F,SAAA,CAAA9R,IAAA,IAAA8R,SAAA,CAAA9R,IAAA,CAAAA,IAAA,IAAA0R,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAA9R,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA8R,SAAA,CAAA9R,IAAA,CAAAA,IAAA;QACAiM,OAAA,GAAA6F,SAAA,CAAA9R,IAAA,CAAA+F,MAAA,GACA+L,SAAA,CAAA9R,IAAA,CAAA+F,MAAA,CAAAjB,MAAA,CAAAkN,CAAA,IAAAA,CAAA,CAAAtM,SAAA;MACA;MAEA4C,OAAA,CAAAC,GAAA,YAAAvI,IAAA;MACAsI,OAAA,CAAAC,GAAA,QAAA0D,OAAA;;MAEA;MACA,MAAAgG,SAAA,GAAAjS,IAAA,CAAAqF,GAAA,CAAA2L,IAAA,IAAAA,IAAA,CAAA1L,KAAA,IAAA0L,IAAA,CAAAlR,IAAA;;MAEA;MACA,MAAAwR,MAAA;MACA,IAAAtR,IAAA,CAAAqD,MAAA,QAAArD,IAAA,IAAAsR,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACAnS,IAAA,CAAAwR,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAAnF,QAAA;UACA;QACA;QAEA,MAAAyE,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAlF,QAAA;UACA,MAAAgG,UAAA,GAAAtS,IAAA,CAAAqF,GAAA,CAAA2L,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAAnF,QAAA,KAAAA,QAAA;YACA,OAAAgF,MAAA,GAAAA,MAAA,CAAAtF,KAAA;UACA;UAEAsF,MAAA,CAAA9N,IAAA;YACA1D,IAAA,EAAAwM,QAAA;YACA3G,IAAA;YACA3F,IAAA,EAAAsS;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAA9N,IAAA;UACA1D,IAAA,EAAAmM,OAAA,KAAAnM,IAAA;UACA6F,IAAA;UACA3F,IAAA,EAAAA,IAAA,CAAAqF,GAAA,CAAA2L,IAAA,IAAAA,IAAA,CAAAhF,KAAA;QACA;MACA;MAEA;QACAR,KAAA;UACAyF,IAAA,EAAAa,SAAA,CAAAtG,KAAA;QACA;QACA0F,OAAA;UACAC,OAAA;UACAqB,WAAA;YACA7M,IAAA;UACA;QACA;QACA8M,MAAA;UACAzS,IAAA,EAAAsR,MAAA,CAAAjM,GAAA,CAAAoM,CAAA,IAAAA,CAAA,CAAA3R,IAAA;QACA;QACAsR,KAAA;UACAzL,IAAA;UACA3F,IAAA,EAAAiS;QACA;QACAZ,KAAA;UACA1L,IAAA;QACA;QACA2L,MAAA,EAAAA;MACA;IACA;IAEA;IACAvD,oBAAA+D,SAAA;MACA,IAAA9R,IAAA;MACA,IAAAiM,OAAA;;MAEA;MACA,IAAA6F,SAAA,CAAA9R,IAAA,IAAA0R,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAA9R,IAAA;QACAA,IAAA,GAAA8R,SAAA,CAAA9R,IAAA;QACAiM,OAAA,GAAA6F,SAAA,CAAA7F,OAAA;MACA;MACA;MAAA,KACA,IAAA6F,SAAA,CAAA9R,IAAA,IAAA8R,SAAA,CAAA9R,IAAA,CAAAA,IAAA,IAAA0R,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAA9R,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA8R,SAAA,CAAA9R,IAAA,CAAAA,IAAA;QACAiM,OAAA,GAAA6F,SAAA,CAAA9R,IAAA,CAAA+F,MAAA,GACA+L,SAAA,CAAA9R,IAAA,CAAA+F,MAAA,CAAAjB,MAAA,CAAAkN,CAAA,IAAAA,CAAA,CAAAtM,SAAA;MACA;;MAEA;MACA,MAAAuM,SAAA,GAAAjS,IAAA,CAAAqF,GAAA,CAAA2L,IAAA,IAAAA,IAAA,CAAA1L,KAAA,IAAA0L,IAAA,CAAAlR,IAAA;;MAEA;MACA,MAAAwR,MAAA;MACA,IAAAtR,IAAA,CAAAqD,MAAA,QAAArD,IAAA,IAAAsR,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACAnS,IAAA,CAAAwR,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAAnF,QAAA;UACA;QACA;QAEA,MAAAyE,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAlF,QAAA;UACA,MAAAgG,UAAA,GAAAtS,IAAA,CAAAqF,GAAA,CAAA2L,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAAnF,QAAA,KAAAA,QAAA;YACA,OAAAgF,MAAA,GAAAA,MAAA,CAAAtF,KAAA;UACA;UAEAsF,MAAA,CAAA9N,IAAA;YACA1D,IAAA,EAAAwM,QAAA;YACA3G,IAAA;YACA3F,IAAA,EAAAsS;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAA9N,IAAA;UACA1D,IAAA,EAAAmM,OAAA,KAAAnM,IAAA;UACA6F,IAAA;UACA3F,IAAA,EAAAA,IAAA,CAAAqF,GAAA,CAAA2L,IAAA,IAAAA,IAAA,CAAAhF,KAAA;QACA;MACA;MAEA;QACAR,KAAA;UACAyF,IAAA,EAAAa,SAAA,CAAAtG,KAAA;QACA;QACA0F,OAAA;UACAC,OAAA;QACA;QACAsB,MAAA;UACAzS,IAAA,EAAAsR,MAAA,CAAAjM,GAAA,CAAAoM,CAAA,IAAAA,CAAA,CAAA3R,IAAA;QACA;QACAsR,KAAA;UACAzL,IAAA;UACA3F,IAAA,EAAAiS;QACA;QACAZ,KAAA;UACA1L,IAAA;QACA;QACA2L,MAAA,EAAAA;MACA;IACA;IAEA;IACAtD,mBAAA8D,SAAA;MACA,IAAA9R,IAAA;;MAEA;MACA,IAAA8R,SAAA,CAAA9R,IAAA,IAAA0R,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAA9R,IAAA;QACAA,IAAA,GAAA8R,SAAA,CAAA9R,IAAA;MACA;MACA;MAAA,KACA,IAAA8R,SAAA,CAAA9R,IAAA,IAAA8R,SAAA,CAAA9R,IAAA,CAAAA,IAAA,IAAA0R,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAA9R,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA8R,SAAA,CAAA9R,IAAA,CAAAA,IAAA;MACA;MAEA,MAAAsS,UAAA,GAAAtS,IAAA,CAAAqF,GAAA,CAAA2L,IAAA;QACAlR,IAAA,EAAAkR,IAAA,CAAA1L,KAAA,IAAA0L,IAAA,CAAAlR,IAAA;QACAkM,KAAA,EAAAgF,IAAA,CAAAhF;MACA;MAEA;QACAR,KAAA;UACAyF,IAAA,EAAAa,SAAA,CAAAtG,KAAA;QACA;QACA0F,OAAA;UACAC,OAAA;UACAuB,SAAA;QACA;QACAD,MAAA;UACAE,MAAA;UACA5L,KAAA;UACAF,GAAA;UACA7G,IAAA,EAAAsS,UAAA,CAAAjN,GAAA,CAAA2L,IAAA,IAAAA,IAAA,CAAAlR,IAAA;QACA;QACAwR,MAAA;UACAxR,IAAA;UACA6F,IAAA;UACAiN,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAnM,QAAA;UACA;UACAoM,QAAA;YACAF,KAAA;cACAC,IAAA;cACAE,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAJ,IAAA;UACA;UACA/S,IAAA,EAAAsS;QACA;MACA;IACA;IAEA;IACArE,wBAAA6D,SAAA;MACA;MACA,IAAA9R,IAAA;MACA,IAAAiM,OAAA;;MAEA;MACA,IAAA6F,SAAA,CAAA9R,IAAA,IAAA0R,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAA9R,IAAA;QACAA,IAAA,GAAA8R,SAAA,CAAA9R,IAAA;QACAiM,OAAA,GAAA6F,SAAA,CAAA7F,OAAA;MACA;MACA;MAAA,KACA,IAAA6F,SAAA,CAAA9R,IAAA,IAAA8R,SAAA,CAAA9R,IAAA,CAAAA,IAAA,IAAA0R,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAA9R,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA8R,SAAA,CAAA9R,IAAA,CAAAA,IAAA;QACAiM,OAAA,GAAA6F,SAAA,CAAA9R,IAAA,CAAA+F,MAAA,GACA+L,SAAA,CAAA9R,IAAA,CAAA+F,MAAA,CAAAjB,MAAA,CAAAkN,CAAA,IAAAA,CAAA,CAAAtM,SAAA;MACA;;MAEA;MACA,MAAA0N,SAAA,GAAApT,IAAA,CAAAqF,GAAA,CAAA2L,IAAA,IAAAA,IAAA,CAAA1L,KAAA,IAAA0L,IAAA,CAAAlR,IAAA;;MAEA;MACA,MAAAwR,MAAA;MACA,IAAAtR,IAAA,CAAAqD,MAAA,QAAArD,IAAA,IAAAsR,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACAnS,IAAA,CAAAwR,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAAnF,QAAA;UACA;QACA;QAEA,MAAAyE,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAlF,QAAA;UACA,MAAAgG,UAAA,GAAAtS,IAAA,CAAAqF,GAAA,CAAA2L,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAAnF,QAAA,KAAAA,QAAA;YACA,OAAAgF,MAAA,GAAAA,MAAA,CAAAtF,KAAA;UACA;UAEAsF,MAAA,CAAA9N,IAAA;YACA1D,IAAA,EAAAwM,QAAA;YACA3G,IAAA;YAAA;YACA3F,IAAA,EAAAsS;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAA9N,IAAA;UACA1D,IAAA,EAAAmM,OAAA,KAAAnM,IAAA;UACA6F,IAAA;UAAA;UACA3F,IAAA,EAAAA,IAAA,CAAAqF,GAAA,CAAA2L,IAAA,IAAAA,IAAA,CAAAhF,KAAA;QACA;MACA;MAEA;QACAR,KAAA;UACAyF,IAAA,EAAAa,SAAA,CAAAtG,KAAA;QACA;QACA0F,OAAA;UACAC,OAAA;UACAqB,WAAA;YACA7M,IAAA;UACA;QACA;QACA8M,MAAA;UACAzS,IAAA,EAAAsR,MAAA,CAAAjM,GAAA,CAAAoM,CAAA,IAAAA,CAAA,CAAA3R,IAAA;QACA;QACA;QACAsR,KAAA;UACAzL,IAAA;QACA;QACA0L,KAAA;UACA1L,IAAA;UAAA;UACA3F,IAAA,EAAAoT;QACA;QACA9B,MAAA,EAAAA;MACA;IACA;IAEA;IACApD,kBAAA4D,SAAA;MACA;QACAtG,KAAA;UACAyF,IAAA,EAAAa,SAAA,CAAAtG,KAAA;QACA;QACA0F,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAzL,IAAA;UACA3F,IAAA;QACA;QACAqR,KAAA;UACA1L,IAAA;QACA;QACA2L,MAAA;UACA3L,IAAA,EAAAmM,SAAA,CAAAnM,IAAA;UACA3F,IAAA;QACA;MACA;IACA;IAEA;IACAuO,gBAAA8E,SAAA,EAAAzI,WAAA;MACA;MACA,IAAA5K,IAAA;MACA,IAAAyI,OAAA;;MAEA;MACA,IAAAmC,WAAA,CAAA5K,IAAA,IAAA4K,WAAA,CAAA5K,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA4K,WAAA,CAAA5K,IAAA,CAAAA,IAAA;;QAEA;QACA,IAAAA,IAAA,CAAAqD,MAAA;UACA,IAAArD,IAAA,IAAAsR,MAAA;YACA;YACA7I,OAAA;YACA,MAAA6K,SAAA,GAAAtT,IAAA;YACA,IAAAsT,SAAA,CAAAhC,MAAA,IAAAI,KAAA,CAAAK,OAAA,CAAAuB,SAAA,CAAAhC,MAAA;cACAgC,SAAA,CAAAhC,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACAhJ,OAAA,CAAAjF,IAAA,CAAAiO,CAAA,CAAAnF,QAAA;cACA;YACA;UACA;YACA;YACA7D,OAAA;UACA;QACA;MACA;;MAEA;MACA,IAAA8K,SAAA;;MAEA;MACAA,SAAA;MACA9K,OAAA,CAAA+I,OAAA,CAAAgC,MAAA;QACAD,SAAA,sGAAAC,MAAA;MACA;MACAD,SAAA;;MAEA;MACAA,SAAA;MACAvT,IAAA,CAAAwR,OAAA,CAAAR,IAAA;QACAuC,SAAA;;QAEA;QACAA,SAAA,yDAAAvC,IAAA,CAAA1L,KAAA,IAAA0L,IAAA,CAAAlR,IAAA;;QAEA;QACA,IAAAkR,IAAA,CAAAM,MAAA;UACA;UACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;YACA8B,SAAA,yDAAA9B,CAAA,CAAAzF,KAAA,KAAAyH,SAAA,GAAAhC,CAAA,CAAAzF,KAAA;UACA;QACA;UACA;UACAuH,SAAA,yDAAAvC,IAAA,CAAAhF,KAAA,KAAAyH,SAAA,GAAAzC,IAAA,CAAAhF,KAAA;QACA;QAEAuH,SAAA;MACA;MACAA,SAAA;;MAEA;MACAF,SAAA,CAAA9F,SAAA,GAAAgG,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}