{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { parseRichText, parsePlainText } from './helper/parseText.js';\nimport TSpan from './TSpan.js';\nimport { retrieve2, each, normalizeCssArray, trim, retrieve3, extend, keys, defaults } from '../core/util.js';\nimport { adjustTextX, adjustTextY } from '../contain/text.js';\nimport ZRImage from './Image.js';\nimport Rect from './shape/Rect.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport Displayable, { DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_SIZE } from '../core/platform.js';\nvar DEFAULT_RICH_TEXT_COLOR = {\n  fill: '#000'\n};\nvar DEFAULT_STROKE_LINE_WIDTH = 2;\nexport var DEFAULT_TEXT_ANIMATION_PROPS = {\n  style: defaults({\n    fill: true,\n    stroke: true,\n    fillOpacity: true,\n    strokeOpacity: true,\n    lineWidth: true,\n    fontSize: true,\n    lineHeight: true,\n    width: true,\n    height: true,\n    textShadowColor: true,\n    textShadowBlur: true,\n    textShadowOffsetX: true,\n    textShadowOffsetY: true,\n    backgroundColor: true,\n    padding: true,\n    borderColor: true,\n    borderWidth: true,\n    borderRadius: true\n  }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nvar ZRText = function (_super) {\n  __extends(ZRText, _super);\n  function ZRText(opts) {\n    var _this = _super.call(this) || this;\n    _this.type = 'text';\n    _this._children = [];\n    _this._defaultStyle = DEFAULT_RICH_TEXT_COLOR;\n    _this.attr(opts);\n    return _this;\n  }\n  ZRText.prototype.childrenRef = function () {\n    return this._children;\n  };\n  ZRText.prototype.update = function () {\n    _super.prototype.update.call(this);\n    if (this.styleChanged()) {\n      this._updateSubTexts();\n    }\n    for (var i = 0; i < this._children.length; i++) {\n      var child = this._children[i];\n      child.zlevel = this.zlevel;\n      child.z = this.z;\n      child.z2 = this.z2;\n      child.culling = this.culling;\n      child.cursor = this.cursor;\n      child.invisible = this.invisible;\n    }\n  };\n  ZRText.prototype.updateTransform = function () {\n    var innerTransformable = this.innerTransformable;\n    if (innerTransformable) {\n      innerTransformable.updateTransform();\n      if (innerTransformable.transform) {\n        this.transform = innerTransformable.transform;\n      }\n    } else {\n      _super.prototype.updateTransform.call(this);\n    }\n  };\n  ZRText.prototype.getLocalTransform = function (m) {\n    var innerTransformable = this.innerTransformable;\n    return innerTransformable ? innerTransformable.getLocalTransform(m) : _super.prototype.getLocalTransform.call(this, m);\n  };\n  ZRText.prototype.getComputedTransform = function () {\n    if (this.__hostTarget) {\n      this.__hostTarget.getComputedTransform();\n      this.__hostTarget.updateInnerText(true);\n    }\n    return _super.prototype.getComputedTransform.call(this);\n  };\n  ZRText.prototype._updateSubTexts = function () {\n    this._childCursor = 0;\n    normalizeTextStyle(this.style);\n    this.style.rich ? this._updateRichTexts() : this._updatePlainTexts();\n    this._children.length = this._childCursor;\n    this.styleUpdated();\n  };\n  ZRText.prototype.addSelfToZr = function (zr) {\n    _super.prototype.addSelfToZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      this._children[i].__zr = zr;\n    }\n  };\n  ZRText.prototype.removeSelfFromZr = function (zr) {\n    _super.prototype.removeSelfFromZr.call(this, zr);\n    for (var i = 0; i < this._children.length; i++) {\n      this._children[i].__zr = null;\n    }\n  };\n  ZRText.prototype.getBoundingRect = function () {\n    if (this.styleChanged()) {\n      this._updateSubTexts();\n    }\n    if (!this._rect) {\n      var tmpRect = new BoundingRect(0, 0, 0, 0);\n      var children = this._children;\n      var tmpMat = [];\n      var rect = null;\n      for (var i = 0; i < children.length; i++) {\n        var child = children[i];\n        var childRect = child.getBoundingRect();\n        var transform = child.getLocalTransform(tmpMat);\n        if (transform) {\n          tmpRect.copy(childRect);\n          tmpRect.applyTransform(transform);\n          rect = rect || tmpRect.clone();\n          rect.union(tmpRect);\n        } else {\n          rect = rect || childRect.clone();\n          rect.union(childRect);\n        }\n      }\n      this._rect = rect || tmpRect;\n    }\n    return this._rect;\n  };\n  ZRText.prototype.setDefaultTextStyle = function (defaultTextStyle) {\n    this._defaultStyle = defaultTextStyle || DEFAULT_RICH_TEXT_COLOR;\n  };\n  ZRText.prototype.setTextContent = function (textContent) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error('Can\\'t attach text on another text');\n    }\n  };\n  ZRText.prototype._mergeStyle = function (targetStyle, sourceStyle) {\n    if (!sourceStyle) {\n      return targetStyle;\n    }\n    var sourceRich = sourceStyle.rich;\n    var targetRich = targetStyle.rich || sourceRich && {};\n    extend(targetStyle, sourceStyle);\n    if (sourceRich && targetRich) {\n      this._mergeRich(targetRich, sourceRich);\n      targetStyle.rich = targetRich;\n    } else if (targetRich) {\n      targetStyle.rich = targetRich;\n    }\n    return targetStyle;\n  };\n  ZRText.prototype._mergeRich = function (targetRich, sourceRich) {\n    var richNames = keys(sourceRich);\n    for (var i = 0; i < richNames.length; i++) {\n      var richName = richNames[i];\n      targetRich[richName] = targetRich[richName] || {};\n      extend(targetRich[richName], sourceRich[richName]);\n    }\n  };\n  ZRText.prototype.getAnimationStyleProps = function () {\n    return DEFAULT_TEXT_ANIMATION_PROPS;\n  };\n  ZRText.prototype._getOrCreateChild = function (Ctor) {\n    var child = this._children[this._childCursor];\n    if (!child || !(child instanceof Ctor)) {\n      child = new Ctor();\n    }\n    this._children[this._childCursor++] = child;\n    child.__zr = this.__zr;\n    child.parent = this;\n    return child;\n  };\n  ZRText.prototype._updatePlainTexts = function () {\n    var style = this.style;\n    var textFont = style.font || DEFAULT_FONT;\n    var textPadding = style.padding;\n    var text = getStyleText(style);\n    var contentBlock = parsePlainText(text, style);\n    var needDrawBg = needDrawBackground(style);\n    var bgColorDrawn = !!style.backgroundColor;\n    var outerHeight = contentBlock.outerHeight;\n    var outerWidth = contentBlock.outerWidth;\n    var contentWidth = contentBlock.contentWidth;\n    var textLines = contentBlock.lines;\n    var lineHeight = contentBlock.lineHeight;\n    var defaultStyle = this._defaultStyle;\n    this.isTruncated = !!contentBlock.isTruncated;\n    var baseX = style.x || 0;\n    var baseY = style.y || 0;\n    var textAlign = style.align || defaultStyle.align || 'left';\n    var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign || 'top';\n    var textX = baseX;\n    var textY = adjustTextY(baseY, contentBlock.contentHeight, verticalAlign);\n    if (needDrawBg || textPadding) {\n      var boxX = adjustTextX(baseX, outerWidth, textAlign);\n      var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n      needDrawBg && this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n    }\n    textY += lineHeight / 2;\n    if (textPadding) {\n      textX = getTextXForPadding(baseX, textAlign, textPadding);\n      if (verticalAlign === 'top') {\n        textY += textPadding[0];\n      } else if (verticalAlign === 'bottom') {\n        textY -= textPadding[2];\n      }\n    }\n    var defaultLineWidth = 0;\n    var useDefaultFill = false;\n    var textFill = getFill('fill' in style ? style.fill : (useDefaultFill = true, defaultStyle.fill));\n    var textStroke = getStroke('stroke' in style ? style.stroke : !bgColorDrawn && (!defaultStyle.autoStroke || useDefaultFill) ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke) : null);\n    var hasShadow = style.textShadowBlur > 0;\n    var fixedBoundingRect = style.width != null && (style.overflow === 'truncate' || style.overflow === 'break' || style.overflow === 'breakAll');\n    var calculatedLineHeight = contentBlock.calculatedLineHeight;\n    for (var i = 0; i < textLines.length; i++) {\n      var el = this._getOrCreateChild(TSpan);\n      var subElStyle = el.createStyle();\n      el.useStyle(subElStyle);\n      subElStyle.text = textLines[i];\n      subElStyle.x = textX;\n      subElStyle.y = textY;\n      if (textAlign) {\n        subElStyle.textAlign = textAlign;\n      }\n      subElStyle.textBaseline = 'middle';\n      subElStyle.opacity = style.opacity;\n      subElStyle.strokeFirst = true;\n      if (hasShadow) {\n        subElStyle.shadowBlur = style.textShadowBlur || 0;\n        subElStyle.shadowColor = style.textShadowColor || 'transparent';\n        subElStyle.shadowOffsetX = style.textShadowOffsetX || 0;\n        subElStyle.shadowOffsetY = style.textShadowOffsetY || 0;\n      }\n      subElStyle.stroke = textStroke;\n      subElStyle.fill = textFill;\n      if (textStroke) {\n        subElStyle.lineWidth = style.lineWidth || defaultLineWidth;\n        subElStyle.lineDash = style.lineDash;\n        subElStyle.lineDashOffset = style.lineDashOffset || 0;\n      }\n      subElStyle.font = textFont;\n      setSeparateFont(subElStyle, style);\n      textY += lineHeight;\n      if (fixedBoundingRect) {\n        el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, contentWidth, subElStyle.textAlign), adjustTextY(subElStyle.y, calculatedLineHeight, subElStyle.textBaseline), contentWidth, calculatedLineHeight));\n      }\n    }\n  };\n  ZRText.prototype._updateRichTexts = function () {\n    var style = this.style;\n    var text = getStyleText(style);\n    var contentBlock = parseRichText(text, style);\n    var contentWidth = contentBlock.width;\n    var outerWidth = contentBlock.outerWidth;\n    var outerHeight = contentBlock.outerHeight;\n    var textPadding = style.padding;\n    var baseX = style.x || 0;\n    var baseY = style.y || 0;\n    var defaultStyle = this._defaultStyle;\n    var textAlign = style.align || defaultStyle.align;\n    var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign;\n    this.isTruncated = !!contentBlock.isTruncated;\n    var boxX = adjustTextX(baseX, outerWidth, textAlign);\n    var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n    var xLeft = boxX;\n    var lineTop = boxY;\n    if (textPadding) {\n      xLeft += textPadding[3];\n      lineTop += textPadding[0];\n    }\n    var xRight = xLeft + contentWidth;\n    if (needDrawBackground(style)) {\n      this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n    }\n    var bgColorDrawn = !!style.backgroundColor;\n    for (var i = 0; i < contentBlock.lines.length; i++) {\n      var line = contentBlock.lines[i];\n      var tokens = line.tokens;\n      var tokenCount = tokens.length;\n      var lineHeight = line.lineHeight;\n      var remainedWidth = line.width;\n      var leftIndex = 0;\n      var lineXLeft = xLeft;\n      var lineXRight = xRight;\n      var rightIndex = tokenCount - 1;\n      var token = void 0;\n      while (leftIndex < tokenCount && (token = tokens[leftIndex], !token.align || token.align === 'left')) {\n        this._placeToken(token, style, lineHeight, lineTop, lineXLeft, 'left', bgColorDrawn);\n        remainedWidth -= token.width;\n        lineXLeft += token.width;\n        leftIndex++;\n      }\n      while (rightIndex >= 0 && (token = tokens[rightIndex], token.align === 'right')) {\n        this._placeToken(token, style, lineHeight, lineTop, lineXRight, 'right', bgColorDrawn);\n        remainedWidth -= token.width;\n        lineXRight -= token.width;\n        rightIndex--;\n      }\n      lineXLeft += (contentWidth - (lineXLeft - xLeft) - (xRight - lineXRight) - remainedWidth) / 2;\n      while (leftIndex <= rightIndex) {\n        token = tokens[leftIndex];\n        this._placeToken(token, style, lineHeight, lineTop, lineXLeft + token.width / 2, 'center', bgColorDrawn);\n        lineXLeft += token.width;\n        leftIndex++;\n      }\n      lineTop += lineHeight;\n    }\n  };\n  ZRText.prototype._placeToken = function (token, style, lineHeight, lineTop, x, textAlign, parentBgColorDrawn) {\n    var tokenStyle = style.rich[token.styleName] || {};\n    tokenStyle.text = token.text;\n    var verticalAlign = token.verticalAlign;\n    var y = lineTop + lineHeight / 2;\n    if (verticalAlign === 'top') {\n      y = lineTop + token.height / 2;\n    } else if (verticalAlign === 'bottom') {\n      y = lineTop + lineHeight - token.height / 2;\n    }\n    var needDrawBg = !token.isLineHolder && needDrawBackground(tokenStyle);\n    needDrawBg && this._renderBackground(tokenStyle, style, textAlign === 'right' ? x - token.width : textAlign === 'center' ? x - token.width / 2 : x, y - token.height / 2, token.width, token.height);\n    var bgColorDrawn = !!tokenStyle.backgroundColor;\n    var textPadding = token.textPadding;\n    if (textPadding) {\n      x = getTextXForPadding(x, textAlign, textPadding);\n      y -= token.height / 2 - textPadding[0] - token.innerHeight / 2;\n    }\n    var el = this._getOrCreateChild(TSpan);\n    var subElStyle = el.createStyle();\n    el.useStyle(subElStyle);\n    var defaultStyle = this._defaultStyle;\n    var useDefaultFill = false;\n    var defaultLineWidth = 0;\n    var textFill = getFill('fill' in tokenStyle ? tokenStyle.fill : 'fill' in style ? style.fill : (useDefaultFill = true, defaultStyle.fill));\n    var textStroke = getStroke('stroke' in tokenStyle ? tokenStyle.stroke : 'stroke' in style ? style.stroke : !bgColorDrawn && !parentBgColorDrawn && (!defaultStyle.autoStroke || useDefaultFill) ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke) : null);\n    var hasShadow = tokenStyle.textShadowBlur > 0 || style.textShadowBlur > 0;\n    subElStyle.text = token.text;\n    subElStyle.x = x;\n    subElStyle.y = y;\n    if (hasShadow) {\n      subElStyle.shadowBlur = tokenStyle.textShadowBlur || style.textShadowBlur || 0;\n      subElStyle.shadowColor = tokenStyle.textShadowColor || style.textShadowColor || 'transparent';\n      subElStyle.shadowOffsetX = tokenStyle.textShadowOffsetX || style.textShadowOffsetX || 0;\n      subElStyle.shadowOffsetY = tokenStyle.textShadowOffsetY || style.textShadowOffsetY || 0;\n    }\n    subElStyle.textAlign = textAlign;\n    subElStyle.textBaseline = 'middle';\n    subElStyle.font = token.font || DEFAULT_FONT;\n    subElStyle.opacity = retrieve3(tokenStyle.opacity, style.opacity, 1);\n    setSeparateFont(subElStyle, tokenStyle);\n    if (textStroke) {\n      subElStyle.lineWidth = retrieve3(tokenStyle.lineWidth, style.lineWidth, defaultLineWidth);\n      subElStyle.lineDash = retrieve2(tokenStyle.lineDash, style.lineDash);\n      subElStyle.lineDashOffset = style.lineDashOffset || 0;\n      subElStyle.stroke = textStroke;\n    }\n    if (textFill) {\n      subElStyle.fill = textFill;\n    }\n    var textWidth = token.contentWidth;\n    var textHeight = token.contentHeight;\n    el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, textWidth, subElStyle.textAlign), adjustTextY(subElStyle.y, textHeight, subElStyle.textBaseline), textWidth, textHeight));\n  };\n  ZRText.prototype._renderBackground = function (style, topStyle, x, y, width, height) {\n    var textBackgroundColor = style.backgroundColor;\n    var textBorderWidth = style.borderWidth;\n    var textBorderColor = style.borderColor;\n    var isImageBg = textBackgroundColor && textBackgroundColor.image;\n    var isPlainOrGradientBg = textBackgroundColor && !isImageBg;\n    var textBorderRadius = style.borderRadius;\n    var self = this;\n    var rectEl;\n    var imgEl;\n    if (isPlainOrGradientBg || style.lineHeight || textBorderWidth && textBorderColor) {\n      rectEl = this._getOrCreateChild(Rect);\n      rectEl.useStyle(rectEl.createStyle());\n      rectEl.style.fill = null;\n      var rectShape = rectEl.shape;\n      rectShape.x = x;\n      rectShape.y = y;\n      rectShape.width = width;\n      rectShape.height = height;\n      rectShape.r = textBorderRadius;\n      rectEl.dirtyShape();\n    }\n    if (isPlainOrGradientBg) {\n      var rectStyle = rectEl.style;\n      rectStyle.fill = textBackgroundColor || null;\n      rectStyle.fillOpacity = retrieve2(style.fillOpacity, 1);\n    } else if (isImageBg) {\n      imgEl = this._getOrCreateChild(ZRImage);\n      imgEl.onload = function () {\n        self.dirtyStyle();\n      };\n      var imgStyle = imgEl.style;\n      imgStyle.image = textBackgroundColor.image;\n      imgStyle.x = x;\n      imgStyle.y = y;\n      imgStyle.width = width;\n      imgStyle.height = height;\n    }\n    if (textBorderWidth && textBorderColor) {\n      var rectStyle = rectEl.style;\n      rectStyle.lineWidth = textBorderWidth;\n      rectStyle.stroke = textBorderColor;\n      rectStyle.strokeOpacity = retrieve2(style.strokeOpacity, 1);\n      rectStyle.lineDash = style.borderDash;\n      rectStyle.lineDashOffset = style.borderDashOffset || 0;\n      rectEl.strokeContainThreshold = 0;\n      if (rectEl.hasFill() && rectEl.hasStroke()) {\n        rectStyle.strokeFirst = true;\n        rectStyle.lineWidth *= 2;\n      }\n    }\n    var commonStyle = (rectEl || imgEl).style;\n    commonStyle.shadowBlur = style.shadowBlur || 0;\n    commonStyle.shadowColor = style.shadowColor || 'transparent';\n    commonStyle.shadowOffsetX = style.shadowOffsetX || 0;\n    commonStyle.shadowOffsetY = style.shadowOffsetY || 0;\n    commonStyle.opacity = retrieve3(style.opacity, topStyle.opacity, 1);\n  };\n  ZRText.makeFont = function (style) {\n    var font = '';\n    if (hasSeparateFont(style)) {\n      font = [style.fontStyle, style.fontWeight, parseFontSize(style.fontSize), style.fontFamily || 'sans-serif'].join(' ');\n    }\n    return font && trim(font) || style.textFont || style.font;\n  };\n  return ZRText;\n}(Displayable);\nvar VALID_TEXT_ALIGN = {\n  left: true,\n  right: 1,\n  center: 1\n};\nvar VALID_TEXT_VERTICAL_ALIGN = {\n  top: 1,\n  bottom: 1,\n  middle: 1\n};\nvar FONT_PARTS = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily'];\nexport function parseFontSize(fontSize) {\n  if (typeof fontSize === 'string' && (fontSize.indexOf('px') !== -1 || fontSize.indexOf('rem') !== -1 || fontSize.indexOf('em') !== -1)) {\n    return fontSize;\n  } else if (!isNaN(+fontSize)) {\n    return fontSize + 'px';\n  } else {\n    return DEFAULT_FONT_SIZE + 'px';\n  }\n}\nfunction setSeparateFont(targetStyle, sourceStyle) {\n  for (var i = 0; i < FONT_PARTS.length; i++) {\n    var fontProp = FONT_PARTS[i];\n    var val = sourceStyle[fontProp];\n    if (val != null) {\n      targetStyle[fontProp] = val;\n    }\n  }\n}\nexport function hasSeparateFont(style) {\n  return style.fontSize != null || style.fontFamily || style.fontWeight;\n}\nexport function normalizeTextStyle(style) {\n  normalizeStyle(style);\n  each(style.rich, normalizeStyle);\n  return style;\n}\nfunction normalizeStyle(style) {\n  if (style) {\n    style.font = ZRText.makeFont(style);\n    var textAlign = style.align;\n    textAlign === 'middle' && (textAlign = 'center');\n    style.align = textAlign == null || VALID_TEXT_ALIGN[textAlign] ? textAlign : 'left';\n    var verticalAlign = style.verticalAlign;\n    verticalAlign === 'center' && (verticalAlign = 'middle');\n    style.verticalAlign = verticalAlign == null || VALID_TEXT_VERTICAL_ALIGN[verticalAlign] ? verticalAlign : 'top';\n    var textPadding = style.padding;\n    if (textPadding) {\n      style.padding = normalizeCssArray(style.padding);\n    }\n  }\n}\nfunction getStroke(stroke, lineWidth) {\n  return stroke == null || lineWidth <= 0 || stroke === 'transparent' || stroke === 'none' ? null : stroke.image || stroke.colorStops ? '#000' : stroke;\n}\nfunction getFill(fill) {\n  return fill == null || fill === 'none' ? null : fill.image || fill.colorStops ? '#000' : fill;\n}\nfunction getTextXForPadding(x, textAlign, textPadding) {\n  return textAlign === 'right' ? x - textPadding[1] : textAlign === 'center' ? x + textPadding[3] / 2 - textPadding[1] / 2 : x + textPadding[3];\n}\nfunction getStyleText(style) {\n  var text = style.text;\n  text != null && (text += '');\n  return text;\n}\nfunction needDrawBackground(style) {\n  return !!(style.backgroundColor || style.lineHeight || style.borderWidth && style.borderColor);\n}\nexport default ZRText;", "map": {"version": 3, "names": ["__extends", "parseRichText", "parsePlainText", "TSpan", "retrieve2", "each", "normalizeCssArray", "trim", "retrieve3", "extend", "keys", "defaults", "adjustTextX", "adjustTextY", "ZRImage", "Rect", "BoundingRect", "Displayable", "DEFAULT_COMMON_ANIMATION_PROPS", "DEFAULT_FONT", "DEFAULT_FONT_SIZE", "DEFAULT_RICH_TEXT_COLOR", "fill", "DEFAULT_STROKE_LINE_WIDTH", "DEFAULT_TEXT_ANIMATION_PROPS", "style", "stroke", "fillOpacity", "strokeOpacity", "lineWidth", "fontSize", "lineHeight", "width", "height", "textShadowColor", "textShadowBlur", "textShadowOffsetX", "textShadowOffsetY", "backgroundColor", "padding", "borderColor", "borderWidth", "borderRadius", "ZRText", "_super", "opts", "_this", "call", "type", "_children", "_defaultStyle", "attr", "prototype", "childrenRef", "update", "styleChanged", "_updateSubTexts", "i", "length", "child", "zlevel", "z", "z2", "culling", "cursor", "invisible", "updateTransform", "innerTransformable", "transform", "getLocalTransform", "m", "getComputedTransform", "__host<PERSON><PERSON>get", "updateInnerText", "_childCursor", "normalizeTextStyle", "rich", "_updateRichTexts", "_updatePlainTexts", "styleUpdated", "addSelfToZr", "zr", "__zr", "removeSelfFromZr", "getBoundingRect", "_rect", "tmpRect", "children", "tmpMat", "rect", "childRect", "copy", "applyTransform", "clone", "union", "setDefaultTextStyle", "defaultTextStyle", "setTextContent", "textContent", "process", "env", "NODE_ENV", "Error", "_mergeStyle", "targetStyle", "sourceStyle", "sourceRich", "targetRich", "_mergeRich", "rich<PERSON><PERSON>s", "<PERSON><PERSON><PERSON>", "getAnimationStyleProps", "_getOr<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ctor", "parent", "textFont", "font", "textPadding", "text", "getStyleText", "contentBlock", "needDrawBg", "needDrawBackground", "bgColorDrawn", "outerHeight", "outerWidth", "contentWidth", "textLines", "lines", "defaultStyle", "isTruncated", "baseX", "x", "baseY", "y", "textAlign", "align", "verticalAlign", "textX", "textY", "contentHeight", "boxX", "boxY", "_renderBackground", "getTextXForPadding", "defaultLineWidth", "useDefaultFill", "textFill", "getFill", "textStroke", "getStroke", "autoStroke", "<PERSON><PERSON><PERSON><PERSON>", "fixedBoundingRect", "overflow", "calculatedLineHeight", "el", "subElStyle", "createStyle", "useStyle", "textBaseline", "opacity", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "shadowOffsetX", "shadowOffsetY", "lineDash", "lineDashOffset", "setSeparateFont", "setBoundingRect", "xLeft", "lineTop", "xRight", "line", "tokens", "tokenCount", "remainedWidth", "leftIndex", "lineXLeft", "lineXRight", "rightIndex", "token", "_placeToken", "parentBgColorDrawn", "tokenStyle", "styleName", "isLineHolder", "innerHeight", "textWidth", "textHeight", "topStyle", "textBackgroundColor", "textBorder<PERSON>idth", "textBorderColor", "isImageBg", "image", "isPlainOrGradientBg", "textBorderRadius", "self", "rectEl", "imgEl", "rectShape", "shape", "r", "dirtyShape", "rectStyle", "onload", "dirtyStyle", "imgStyle", "borderDash", "borderDashOffset", "strokeContainThreshold", "hasFill", "hasStroke", "commonStyle", "makeFont", "hasSeparateFont", "fontStyle", "fontWeight", "parseFontSize", "fontFamily", "join", "VALID_TEXT_ALIGN", "left", "right", "center", "VALID_TEXT_VERTICAL_ALIGN", "top", "bottom", "middle", "FONT_PARTS", "indexOf", "isNaN", "fontProp", "val", "normalizeStyle", "colorStops"], "sources": ["D:/FastBI/datafront/node_modules/zrender/lib/graphic/Text.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { parseRichText, parsePlainText } from './helper/parseText.js';\nimport TSpan from './TSpan.js';\nimport { retrieve2, each, normalizeCssArray, trim, retrieve3, extend, keys, defaults } from '../core/util.js';\nimport { adjustTextX, adjustTextY } from '../contain/text.js';\nimport ZRImage from './Image.js';\nimport Rect from './shape/Rect.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport Displayable, { DEFAULT_COMMON_ANIMATION_PROPS } from './Displayable.js';\nimport { DEFAULT_FONT, DEFAULT_FONT_SIZE } from '../core/platform.js';\nvar DEFAULT_RICH_TEXT_COLOR = {\n    fill: '#000'\n};\nvar DEFAULT_STROKE_LINE_WIDTH = 2;\nexport var DEFAULT_TEXT_ANIMATION_PROPS = {\n    style: defaults({\n        fill: true,\n        stroke: true,\n        fillOpacity: true,\n        strokeOpacity: true,\n        lineWidth: true,\n        fontSize: true,\n        lineHeight: true,\n        width: true,\n        height: true,\n        textShadowColor: true,\n        textShadowBlur: true,\n        textShadowOffsetX: true,\n        textShadowOffsetY: true,\n        backgroundColor: true,\n        padding: true,\n        borderColor: true,\n        borderWidth: true,\n        borderRadius: true\n    }, DEFAULT_COMMON_ANIMATION_PROPS.style)\n};\nvar ZRText = (function (_super) {\n    __extends(ZRText, _super);\n    function ZRText(opts) {\n        var _this = _super.call(this) || this;\n        _this.type = 'text';\n        _this._children = [];\n        _this._defaultStyle = DEFAULT_RICH_TEXT_COLOR;\n        _this.attr(opts);\n        return _this;\n    }\n    ZRText.prototype.childrenRef = function () {\n        return this._children;\n    };\n    ZRText.prototype.update = function () {\n        _super.prototype.update.call(this);\n        if (this.styleChanged()) {\n            this._updateSubTexts();\n        }\n        for (var i = 0; i < this._children.length; i++) {\n            var child = this._children[i];\n            child.zlevel = this.zlevel;\n            child.z = this.z;\n            child.z2 = this.z2;\n            child.culling = this.culling;\n            child.cursor = this.cursor;\n            child.invisible = this.invisible;\n        }\n    };\n    ZRText.prototype.updateTransform = function () {\n        var innerTransformable = this.innerTransformable;\n        if (innerTransformable) {\n            innerTransformable.updateTransform();\n            if (innerTransformable.transform) {\n                this.transform = innerTransformable.transform;\n            }\n        }\n        else {\n            _super.prototype.updateTransform.call(this);\n        }\n    };\n    ZRText.prototype.getLocalTransform = function (m) {\n        var innerTransformable = this.innerTransformable;\n        return innerTransformable\n            ? innerTransformable.getLocalTransform(m)\n            : _super.prototype.getLocalTransform.call(this, m);\n    };\n    ZRText.prototype.getComputedTransform = function () {\n        if (this.__hostTarget) {\n            this.__hostTarget.getComputedTransform();\n            this.__hostTarget.updateInnerText(true);\n        }\n        return _super.prototype.getComputedTransform.call(this);\n    };\n    ZRText.prototype._updateSubTexts = function () {\n        this._childCursor = 0;\n        normalizeTextStyle(this.style);\n        this.style.rich\n            ? this._updateRichTexts()\n            : this._updatePlainTexts();\n        this._children.length = this._childCursor;\n        this.styleUpdated();\n    };\n    ZRText.prototype.addSelfToZr = function (zr) {\n        _super.prototype.addSelfToZr.call(this, zr);\n        for (var i = 0; i < this._children.length; i++) {\n            this._children[i].__zr = zr;\n        }\n    };\n    ZRText.prototype.removeSelfFromZr = function (zr) {\n        _super.prototype.removeSelfFromZr.call(this, zr);\n        for (var i = 0; i < this._children.length; i++) {\n            this._children[i].__zr = null;\n        }\n    };\n    ZRText.prototype.getBoundingRect = function () {\n        if (this.styleChanged()) {\n            this._updateSubTexts();\n        }\n        if (!this._rect) {\n            var tmpRect = new BoundingRect(0, 0, 0, 0);\n            var children = this._children;\n            var tmpMat = [];\n            var rect = null;\n            for (var i = 0; i < children.length; i++) {\n                var child = children[i];\n                var childRect = child.getBoundingRect();\n                var transform = child.getLocalTransform(tmpMat);\n                if (transform) {\n                    tmpRect.copy(childRect);\n                    tmpRect.applyTransform(transform);\n                    rect = rect || tmpRect.clone();\n                    rect.union(tmpRect);\n                }\n                else {\n                    rect = rect || childRect.clone();\n                    rect.union(childRect);\n                }\n            }\n            this._rect = rect || tmpRect;\n        }\n        return this._rect;\n    };\n    ZRText.prototype.setDefaultTextStyle = function (defaultTextStyle) {\n        this._defaultStyle = defaultTextStyle || DEFAULT_RICH_TEXT_COLOR;\n    };\n    ZRText.prototype.setTextContent = function (textContent) {\n        if (process.env.NODE_ENV !== 'production') {\n            throw new Error('Can\\'t attach text on another text');\n        }\n    };\n    ZRText.prototype._mergeStyle = function (targetStyle, sourceStyle) {\n        if (!sourceStyle) {\n            return targetStyle;\n        }\n        var sourceRich = sourceStyle.rich;\n        var targetRich = targetStyle.rich || (sourceRich && {});\n        extend(targetStyle, sourceStyle);\n        if (sourceRich && targetRich) {\n            this._mergeRich(targetRich, sourceRich);\n            targetStyle.rich = targetRich;\n        }\n        else if (targetRich) {\n            targetStyle.rich = targetRich;\n        }\n        return targetStyle;\n    };\n    ZRText.prototype._mergeRich = function (targetRich, sourceRich) {\n        var richNames = keys(sourceRich);\n        for (var i = 0; i < richNames.length; i++) {\n            var richName = richNames[i];\n            targetRich[richName] = targetRich[richName] || {};\n            extend(targetRich[richName], sourceRich[richName]);\n        }\n    };\n    ZRText.prototype.getAnimationStyleProps = function () {\n        return DEFAULT_TEXT_ANIMATION_PROPS;\n    };\n    ZRText.prototype._getOrCreateChild = function (Ctor) {\n        var child = this._children[this._childCursor];\n        if (!child || !(child instanceof Ctor)) {\n            child = new Ctor();\n        }\n        this._children[this._childCursor++] = child;\n        child.__zr = this.__zr;\n        child.parent = this;\n        return child;\n    };\n    ZRText.prototype._updatePlainTexts = function () {\n        var style = this.style;\n        var textFont = style.font || DEFAULT_FONT;\n        var textPadding = style.padding;\n        var text = getStyleText(style);\n        var contentBlock = parsePlainText(text, style);\n        var needDrawBg = needDrawBackground(style);\n        var bgColorDrawn = !!(style.backgroundColor);\n        var outerHeight = contentBlock.outerHeight;\n        var outerWidth = contentBlock.outerWidth;\n        var contentWidth = contentBlock.contentWidth;\n        var textLines = contentBlock.lines;\n        var lineHeight = contentBlock.lineHeight;\n        var defaultStyle = this._defaultStyle;\n        this.isTruncated = !!contentBlock.isTruncated;\n        var baseX = style.x || 0;\n        var baseY = style.y || 0;\n        var textAlign = style.align || defaultStyle.align || 'left';\n        var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign || 'top';\n        var textX = baseX;\n        var textY = adjustTextY(baseY, contentBlock.contentHeight, verticalAlign);\n        if (needDrawBg || textPadding) {\n            var boxX = adjustTextX(baseX, outerWidth, textAlign);\n            var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n            needDrawBg && this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n        }\n        textY += lineHeight / 2;\n        if (textPadding) {\n            textX = getTextXForPadding(baseX, textAlign, textPadding);\n            if (verticalAlign === 'top') {\n                textY += textPadding[0];\n            }\n            else if (verticalAlign === 'bottom') {\n                textY -= textPadding[2];\n            }\n        }\n        var defaultLineWidth = 0;\n        var useDefaultFill = false;\n        var textFill = getFill('fill' in style\n            ? style.fill\n            : (useDefaultFill = true, defaultStyle.fill));\n        var textStroke = getStroke('stroke' in style\n            ? style.stroke\n            : (!bgColorDrawn\n                && (!defaultStyle.autoStroke || useDefaultFill))\n                ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke)\n                : null);\n        var hasShadow = style.textShadowBlur > 0;\n        var fixedBoundingRect = style.width != null\n            && (style.overflow === 'truncate' || style.overflow === 'break' || style.overflow === 'breakAll');\n        var calculatedLineHeight = contentBlock.calculatedLineHeight;\n        for (var i = 0; i < textLines.length; i++) {\n            var el = this._getOrCreateChild(TSpan);\n            var subElStyle = el.createStyle();\n            el.useStyle(subElStyle);\n            subElStyle.text = textLines[i];\n            subElStyle.x = textX;\n            subElStyle.y = textY;\n            if (textAlign) {\n                subElStyle.textAlign = textAlign;\n            }\n            subElStyle.textBaseline = 'middle';\n            subElStyle.opacity = style.opacity;\n            subElStyle.strokeFirst = true;\n            if (hasShadow) {\n                subElStyle.shadowBlur = style.textShadowBlur || 0;\n                subElStyle.shadowColor = style.textShadowColor || 'transparent';\n                subElStyle.shadowOffsetX = style.textShadowOffsetX || 0;\n                subElStyle.shadowOffsetY = style.textShadowOffsetY || 0;\n            }\n            subElStyle.stroke = textStroke;\n            subElStyle.fill = textFill;\n            if (textStroke) {\n                subElStyle.lineWidth = style.lineWidth || defaultLineWidth;\n                subElStyle.lineDash = style.lineDash;\n                subElStyle.lineDashOffset = style.lineDashOffset || 0;\n            }\n            subElStyle.font = textFont;\n            setSeparateFont(subElStyle, style);\n            textY += lineHeight;\n            if (fixedBoundingRect) {\n                el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, contentWidth, subElStyle.textAlign), adjustTextY(subElStyle.y, calculatedLineHeight, subElStyle.textBaseline), contentWidth, calculatedLineHeight));\n            }\n        }\n    };\n    ZRText.prototype._updateRichTexts = function () {\n        var style = this.style;\n        var text = getStyleText(style);\n        var contentBlock = parseRichText(text, style);\n        var contentWidth = contentBlock.width;\n        var outerWidth = contentBlock.outerWidth;\n        var outerHeight = contentBlock.outerHeight;\n        var textPadding = style.padding;\n        var baseX = style.x || 0;\n        var baseY = style.y || 0;\n        var defaultStyle = this._defaultStyle;\n        var textAlign = style.align || defaultStyle.align;\n        var verticalAlign = style.verticalAlign || defaultStyle.verticalAlign;\n        this.isTruncated = !!contentBlock.isTruncated;\n        var boxX = adjustTextX(baseX, outerWidth, textAlign);\n        var boxY = adjustTextY(baseY, outerHeight, verticalAlign);\n        var xLeft = boxX;\n        var lineTop = boxY;\n        if (textPadding) {\n            xLeft += textPadding[3];\n            lineTop += textPadding[0];\n        }\n        var xRight = xLeft + contentWidth;\n        if (needDrawBackground(style)) {\n            this._renderBackground(style, style, boxX, boxY, outerWidth, outerHeight);\n        }\n        var bgColorDrawn = !!(style.backgroundColor);\n        for (var i = 0; i < contentBlock.lines.length; i++) {\n            var line = contentBlock.lines[i];\n            var tokens = line.tokens;\n            var tokenCount = tokens.length;\n            var lineHeight = line.lineHeight;\n            var remainedWidth = line.width;\n            var leftIndex = 0;\n            var lineXLeft = xLeft;\n            var lineXRight = xRight;\n            var rightIndex = tokenCount - 1;\n            var token = void 0;\n            while (leftIndex < tokenCount\n                && (token = tokens[leftIndex], !token.align || token.align === 'left')) {\n                this._placeToken(token, style, lineHeight, lineTop, lineXLeft, 'left', bgColorDrawn);\n                remainedWidth -= token.width;\n                lineXLeft += token.width;\n                leftIndex++;\n            }\n            while (rightIndex >= 0\n                && (token = tokens[rightIndex], token.align === 'right')) {\n                this._placeToken(token, style, lineHeight, lineTop, lineXRight, 'right', bgColorDrawn);\n                remainedWidth -= token.width;\n                lineXRight -= token.width;\n                rightIndex--;\n            }\n            lineXLeft += (contentWidth - (lineXLeft - xLeft) - (xRight - lineXRight) - remainedWidth) / 2;\n            while (leftIndex <= rightIndex) {\n                token = tokens[leftIndex];\n                this._placeToken(token, style, lineHeight, lineTop, lineXLeft + token.width / 2, 'center', bgColorDrawn);\n                lineXLeft += token.width;\n                leftIndex++;\n            }\n            lineTop += lineHeight;\n        }\n    };\n    ZRText.prototype._placeToken = function (token, style, lineHeight, lineTop, x, textAlign, parentBgColorDrawn) {\n        var tokenStyle = style.rich[token.styleName] || {};\n        tokenStyle.text = token.text;\n        var verticalAlign = token.verticalAlign;\n        var y = lineTop + lineHeight / 2;\n        if (verticalAlign === 'top') {\n            y = lineTop + token.height / 2;\n        }\n        else if (verticalAlign === 'bottom') {\n            y = lineTop + lineHeight - token.height / 2;\n        }\n        var needDrawBg = !token.isLineHolder && needDrawBackground(tokenStyle);\n        needDrawBg && this._renderBackground(tokenStyle, style, textAlign === 'right'\n            ? x - token.width\n            : textAlign === 'center'\n                ? x - token.width / 2\n                : x, y - token.height / 2, token.width, token.height);\n        var bgColorDrawn = !!tokenStyle.backgroundColor;\n        var textPadding = token.textPadding;\n        if (textPadding) {\n            x = getTextXForPadding(x, textAlign, textPadding);\n            y -= token.height / 2 - textPadding[0] - token.innerHeight / 2;\n        }\n        var el = this._getOrCreateChild(TSpan);\n        var subElStyle = el.createStyle();\n        el.useStyle(subElStyle);\n        var defaultStyle = this._defaultStyle;\n        var useDefaultFill = false;\n        var defaultLineWidth = 0;\n        var textFill = getFill('fill' in tokenStyle ? tokenStyle.fill\n            : 'fill' in style ? style.fill\n                : (useDefaultFill = true, defaultStyle.fill));\n        var textStroke = getStroke('stroke' in tokenStyle ? tokenStyle.stroke\n            : 'stroke' in style ? style.stroke\n                : (!bgColorDrawn\n                    && !parentBgColorDrawn\n                    && (!defaultStyle.autoStroke || useDefaultFill)) ? (defaultLineWidth = DEFAULT_STROKE_LINE_WIDTH, defaultStyle.stroke)\n                    : null);\n        var hasShadow = tokenStyle.textShadowBlur > 0\n            || style.textShadowBlur > 0;\n        subElStyle.text = token.text;\n        subElStyle.x = x;\n        subElStyle.y = y;\n        if (hasShadow) {\n            subElStyle.shadowBlur = tokenStyle.textShadowBlur || style.textShadowBlur || 0;\n            subElStyle.shadowColor = tokenStyle.textShadowColor || style.textShadowColor || 'transparent';\n            subElStyle.shadowOffsetX = tokenStyle.textShadowOffsetX || style.textShadowOffsetX || 0;\n            subElStyle.shadowOffsetY = tokenStyle.textShadowOffsetY || style.textShadowOffsetY || 0;\n        }\n        subElStyle.textAlign = textAlign;\n        subElStyle.textBaseline = 'middle';\n        subElStyle.font = token.font || DEFAULT_FONT;\n        subElStyle.opacity = retrieve3(tokenStyle.opacity, style.opacity, 1);\n        setSeparateFont(subElStyle, tokenStyle);\n        if (textStroke) {\n            subElStyle.lineWidth = retrieve3(tokenStyle.lineWidth, style.lineWidth, defaultLineWidth);\n            subElStyle.lineDash = retrieve2(tokenStyle.lineDash, style.lineDash);\n            subElStyle.lineDashOffset = style.lineDashOffset || 0;\n            subElStyle.stroke = textStroke;\n        }\n        if (textFill) {\n            subElStyle.fill = textFill;\n        }\n        var textWidth = token.contentWidth;\n        var textHeight = token.contentHeight;\n        el.setBoundingRect(new BoundingRect(adjustTextX(subElStyle.x, textWidth, subElStyle.textAlign), adjustTextY(subElStyle.y, textHeight, subElStyle.textBaseline), textWidth, textHeight));\n    };\n    ZRText.prototype._renderBackground = function (style, topStyle, x, y, width, height) {\n        var textBackgroundColor = style.backgroundColor;\n        var textBorderWidth = style.borderWidth;\n        var textBorderColor = style.borderColor;\n        var isImageBg = textBackgroundColor && textBackgroundColor.image;\n        var isPlainOrGradientBg = textBackgroundColor && !isImageBg;\n        var textBorderRadius = style.borderRadius;\n        var self = this;\n        var rectEl;\n        var imgEl;\n        if (isPlainOrGradientBg || style.lineHeight || (textBorderWidth && textBorderColor)) {\n            rectEl = this._getOrCreateChild(Rect);\n            rectEl.useStyle(rectEl.createStyle());\n            rectEl.style.fill = null;\n            var rectShape = rectEl.shape;\n            rectShape.x = x;\n            rectShape.y = y;\n            rectShape.width = width;\n            rectShape.height = height;\n            rectShape.r = textBorderRadius;\n            rectEl.dirtyShape();\n        }\n        if (isPlainOrGradientBg) {\n            var rectStyle = rectEl.style;\n            rectStyle.fill = textBackgroundColor || null;\n            rectStyle.fillOpacity = retrieve2(style.fillOpacity, 1);\n        }\n        else if (isImageBg) {\n            imgEl = this._getOrCreateChild(ZRImage);\n            imgEl.onload = function () {\n                self.dirtyStyle();\n            };\n            var imgStyle = imgEl.style;\n            imgStyle.image = textBackgroundColor.image;\n            imgStyle.x = x;\n            imgStyle.y = y;\n            imgStyle.width = width;\n            imgStyle.height = height;\n        }\n        if (textBorderWidth && textBorderColor) {\n            var rectStyle = rectEl.style;\n            rectStyle.lineWidth = textBorderWidth;\n            rectStyle.stroke = textBorderColor;\n            rectStyle.strokeOpacity = retrieve2(style.strokeOpacity, 1);\n            rectStyle.lineDash = style.borderDash;\n            rectStyle.lineDashOffset = style.borderDashOffset || 0;\n            rectEl.strokeContainThreshold = 0;\n            if (rectEl.hasFill() && rectEl.hasStroke()) {\n                rectStyle.strokeFirst = true;\n                rectStyle.lineWidth *= 2;\n            }\n        }\n        var commonStyle = (rectEl || imgEl).style;\n        commonStyle.shadowBlur = style.shadowBlur || 0;\n        commonStyle.shadowColor = style.shadowColor || 'transparent';\n        commonStyle.shadowOffsetX = style.shadowOffsetX || 0;\n        commonStyle.shadowOffsetY = style.shadowOffsetY || 0;\n        commonStyle.opacity = retrieve3(style.opacity, topStyle.opacity, 1);\n    };\n    ZRText.makeFont = function (style) {\n        var font = '';\n        if (hasSeparateFont(style)) {\n            font = [\n                style.fontStyle,\n                style.fontWeight,\n                parseFontSize(style.fontSize),\n                style.fontFamily || 'sans-serif'\n            ].join(' ');\n        }\n        return font && trim(font) || style.textFont || style.font;\n    };\n    return ZRText;\n}(Displayable));\nvar VALID_TEXT_ALIGN = { left: true, right: 1, center: 1 };\nvar VALID_TEXT_VERTICAL_ALIGN = { top: 1, bottom: 1, middle: 1 };\nvar FONT_PARTS = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily'];\nexport function parseFontSize(fontSize) {\n    if (typeof fontSize === 'string'\n        && (fontSize.indexOf('px') !== -1\n            || fontSize.indexOf('rem') !== -1\n            || fontSize.indexOf('em') !== -1)) {\n        return fontSize;\n    }\n    else if (!isNaN(+fontSize)) {\n        return fontSize + 'px';\n    }\n    else {\n        return DEFAULT_FONT_SIZE + 'px';\n    }\n}\nfunction setSeparateFont(targetStyle, sourceStyle) {\n    for (var i = 0; i < FONT_PARTS.length; i++) {\n        var fontProp = FONT_PARTS[i];\n        var val = sourceStyle[fontProp];\n        if (val != null) {\n            targetStyle[fontProp] = val;\n        }\n    }\n}\nexport function hasSeparateFont(style) {\n    return style.fontSize != null || style.fontFamily || style.fontWeight;\n}\nexport function normalizeTextStyle(style) {\n    normalizeStyle(style);\n    each(style.rich, normalizeStyle);\n    return style;\n}\nfunction normalizeStyle(style) {\n    if (style) {\n        style.font = ZRText.makeFont(style);\n        var textAlign = style.align;\n        textAlign === 'middle' && (textAlign = 'center');\n        style.align = (textAlign == null || VALID_TEXT_ALIGN[textAlign]) ? textAlign : 'left';\n        var verticalAlign = style.verticalAlign;\n        verticalAlign === 'center' && (verticalAlign = 'middle');\n        style.verticalAlign = (verticalAlign == null || VALID_TEXT_VERTICAL_ALIGN[verticalAlign]) ? verticalAlign : 'top';\n        var textPadding = style.padding;\n        if (textPadding) {\n            style.padding = normalizeCssArray(style.padding);\n        }\n    }\n}\nfunction getStroke(stroke, lineWidth) {\n    return (stroke == null || lineWidth <= 0 || stroke === 'transparent' || stroke === 'none')\n        ? null\n        : (stroke.image || stroke.colorStops)\n            ? '#000'\n            : stroke;\n}\nfunction getFill(fill) {\n    return (fill == null || fill === 'none')\n        ? null\n        : (fill.image || fill.colorStops)\n            ? '#000'\n            : fill;\n}\nfunction getTextXForPadding(x, textAlign, textPadding) {\n    return textAlign === 'right'\n        ? (x - textPadding[1])\n        : textAlign === 'center'\n            ? (x + textPadding[3] / 2 - textPadding[1] / 2)\n            : (x + textPadding[3]);\n}\nfunction getStyleText(style) {\n    var text = style.text;\n    text != null && (text += '');\n    return text;\n}\nfunction needDrawBackground(style) {\n    return !!(style.backgroundColor\n        || style.lineHeight\n        || (style.borderWidth && style.borderColor));\n}\nexport default ZRText;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,aAAa,EAAEC,cAAc,QAAQ,uBAAuB;AACrE,OAAOC,KAAK,MAAM,YAAY;AAC9B,SAASC,SAAS,EAAEC,IAAI,EAAEC,iBAAiB,EAAEC,IAAI,EAAEC,SAAS,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,iBAAiB;AAC7G,SAASC,WAAW,EAAEC,WAAW,QAAQ,oBAAoB;AAC7D,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,WAAW,IAAIC,8BAA8B,QAAQ,kBAAkB;AAC9E,SAASC,YAAY,EAAEC,iBAAiB,QAAQ,qBAAqB;AACrE,IAAIC,uBAAuB,GAAG;EAC1BC,IAAI,EAAE;AACV,CAAC;AACD,IAAIC,yBAAyB,GAAG,CAAC;AACjC,OAAO,IAAIC,4BAA4B,GAAG;EACtCC,KAAK,EAAEd,QAAQ,CAAC;IACZW,IAAI,EAAE,IAAI;IACVI,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,IAAI;IACnBC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE,IAAI;IACdC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZC,eAAe,EAAE,IAAI;IACrBC,cAAc,EAAE,IAAI;IACpBC,iBAAiB,EAAE,IAAI;IACvBC,iBAAiB,EAAE,IAAI;IACvBC,eAAe,EAAE,IAAI;IACrBC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE,IAAI;IACjBC,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE;EAClB,CAAC,EAAExB,8BAA8B,CAACO,KAAK;AAC3C,CAAC;AACD,IAAIkB,MAAM,GAAI,UAAUC,MAAM,EAAE;EAC5B5C,SAAS,CAAC2C,MAAM,EAAEC,MAAM,CAAC;EACzB,SAASD,MAAMA,CAACE,IAAI,EAAE;IAClB,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,IAAI,GAAG,MAAM;IACnBF,KAAK,CAACG,SAAS,GAAG,EAAE;IACpBH,KAAK,CAACI,aAAa,GAAG7B,uBAAuB;IAC7CyB,KAAK,CAACK,IAAI,CAACN,IAAI,CAAC;IAChB,OAAOC,KAAK;EAChB;EACAH,MAAM,CAACS,SAAS,CAACC,WAAW,GAAG,YAAY;IACvC,OAAO,IAAI,CAACJ,SAAS;EACzB,CAAC;EACDN,MAAM,CAACS,SAAS,CAACE,MAAM,GAAG,YAAY;IAClCV,MAAM,CAACQ,SAAS,CAACE,MAAM,CAACP,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,IAAI,CAACQ,YAAY,CAAC,CAAC,EAAE;MACrB,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,SAAS,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAIE,KAAK,GAAG,IAAI,CAACV,SAAS,CAACQ,CAAC,CAAC;MAC7BE,KAAK,CAACC,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1BD,KAAK,CAACE,CAAC,GAAG,IAAI,CAACA,CAAC;MAChBF,KAAK,CAACG,EAAE,GAAG,IAAI,CAACA,EAAE;MAClBH,KAAK,CAACI,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5BJ,KAAK,CAACK,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1BL,KAAK,CAACM,SAAS,GAAG,IAAI,CAACA,SAAS;IACpC;EACJ,CAAC;EACDtB,MAAM,CAACS,SAAS,CAACc,eAAe,GAAG,YAAY;IAC3C,IAAIC,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;IAChD,IAAIA,kBAAkB,EAAE;MACpBA,kBAAkB,CAACD,eAAe,CAAC,CAAC;MACpC,IAAIC,kBAAkB,CAACC,SAAS,EAAE;QAC9B,IAAI,CAACA,SAAS,GAAGD,kBAAkB,CAACC,SAAS;MACjD;IACJ,CAAC,MACI;MACDxB,MAAM,CAACQ,SAAS,CAACc,eAAe,CAACnB,IAAI,CAAC,IAAI,CAAC;IAC/C;EACJ,CAAC;EACDJ,MAAM,CAACS,SAAS,CAACiB,iBAAiB,GAAG,UAAUC,CAAC,EAAE;IAC9C,IAAIH,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;IAChD,OAAOA,kBAAkB,GACnBA,kBAAkB,CAACE,iBAAiB,CAACC,CAAC,CAAC,GACvC1B,MAAM,CAACQ,SAAS,CAACiB,iBAAiB,CAACtB,IAAI,CAAC,IAAI,EAAEuB,CAAC,CAAC;EAC1D,CAAC;EACD3B,MAAM,CAACS,SAAS,CAACmB,oBAAoB,GAAG,YAAY;IAChD,IAAI,IAAI,CAACC,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACD,oBAAoB,CAAC,CAAC;MACxC,IAAI,CAACC,YAAY,CAACC,eAAe,CAAC,IAAI,CAAC;IAC3C;IACA,OAAO7B,MAAM,CAACQ,SAAS,CAACmB,oBAAoB,CAACxB,IAAI,CAAC,IAAI,CAAC;EAC3D,CAAC;EACDJ,MAAM,CAACS,SAAS,CAACI,eAAe,GAAG,YAAY;IAC3C,IAAI,CAACkB,YAAY,GAAG,CAAC;IACrBC,kBAAkB,CAAC,IAAI,CAAClD,KAAK,CAAC;IAC9B,IAAI,CAACA,KAAK,CAACmD,IAAI,GACT,IAAI,CAACC,gBAAgB,CAAC,CAAC,GACvB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC9B,IAAI,CAAC7B,SAAS,CAACS,MAAM,GAAG,IAAI,CAACgB,YAAY;IACzC,IAAI,CAACK,YAAY,CAAC,CAAC;EACvB,CAAC;EACDpC,MAAM,CAACS,SAAS,CAAC4B,WAAW,GAAG,UAAUC,EAAE,EAAE;IACzCrC,MAAM,CAACQ,SAAS,CAAC4B,WAAW,CAACjC,IAAI,CAAC,IAAI,EAAEkC,EAAE,CAAC;IAC3C,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,SAAS,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAI,CAACR,SAAS,CAACQ,CAAC,CAAC,CAACyB,IAAI,GAAGD,EAAE;IAC/B;EACJ,CAAC;EACDtC,MAAM,CAACS,SAAS,CAAC+B,gBAAgB,GAAG,UAAUF,EAAE,EAAE;IAC9CrC,MAAM,CAACQ,SAAS,CAAC+B,gBAAgB,CAACpC,IAAI,CAAC,IAAI,EAAEkC,EAAE,CAAC;IAChD,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,SAAS,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAI,CAACR,SAAS,CAACQ,CAAC,CAAC,CAACyB,IAAI,GAAG,IAAI;IACjC;EACJ,CAAC;EACDvC,MAAM,CAACS,SAAS,CAACgC,eAAe,GAAG,YAAY;IAC3C,IAAI,IAAI,CAAC7B,YAAY,CAAC,CAAC,EAAE;MACrB,IAAI,CAACC,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI,CAAC,IAAI,CAAC6B,KAAK,EAAE;MACb,IAAIC,OAAO,GAAG,IAAItE,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1C,IAAIuE,QAAQ,GAAG,IAAI,CAACtC,SAAS;MAC7B,IAAIuC,MAAM,GAAG,EAAE;MACf,IAAIC,IAAI,GAAG,IAAI;MACf,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,QAAQ,CAAC7B,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIE,KAAK,GAAG4B,QAAQ,CAAC9B,CAAC,CAAC;QACvB,IAAIiC,SAAS,GAAG/B,KAAK,CAACyB,eAAe,CAAC,CAAC;QACvC,IAAIhB,SAAS,GAAGT,KAAK,CAACU,iBAAiB,CAACmB,MAAM,CAAC;QAC/C,IAAIpB,SAAS,EAAE;UACXkB,OAAO,CAACK,IAAI,CAACD,SAAS,CAAC;UACvBJ,OAAO,CAACM,cAAc,CAACxB,SAAS,CAAC;UACjCqB,IAAI,GAAGA,IAAI,IAAIH,OAAO,CAACO,KAAK,CAAC,CAAC;UAC9BJ,IAAI,CAACK,KAAK,CAACR,OAAO,CAAC;QACvB,CAAC,MACI;UACDG,IAAI,GAAGA,IAAI,IAAIC,SAAS,CAACG,KAAK,CAAC,CAAC;UAChCJ,IAAI,CAACK,KAAK,CAACJ,SAAS,CAAC;QACzB;MACJ;MACA,IAAI,CAACL,KAAK,GAAGI,IAAI,IAAIH,OAAO;IAChC;IACA,OAAO,IAAI,CAACD,KAAK;EACrB,CAAC;EACD1C,MAAM,CAACS,SAAS,CAAC2C,mBAAmB,GAAG,UAAUC,gBAAgB,EAAE;IAC/D,IAAI,CAAC9C,aAAa,GAAG8C,gBAAgB,IAAI3E,uBAAuB;EACpE,CAAC;EACDsB,MAAM,CAACS,SAAS,CAAC6C,cAAc,GAAG,UAAUC,WAAW,EAAE;IACrD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACvC,MAAM,IAAIC,KAAK,CAAC,oCAAoC,CAAC;IACzD;EACJ,CAAC;EACD3D,MAAM,CAACS,SAAS,CAACmD,WAAW,GAAG,UAAUC,WAAW,EAAEC,WAAW,EAAE;IAC/D,IAAI,CAACA,WAAW,EAAE;MACd,OAAOD,WAAW;IACtB;IACA,IAAIE,UAAU,GAAGD,WAAW,CAAC7B,IAAI;IACjC,IAAI+B,UAAU,GAAGH,WAAW,CAAC5B,IAAI,IAAK8B,UAAU,IAAI,CAAC,CAAE;IACvDjG,MAAM,CAAC+F,WAAW,EAAEC,WAAW,CAAC;IAChC,IAAIC,UAAU,IAAIC,UAAU,EAAE;MAC1B,IAAI,CAACC,UAAU,CAACD,UAAU,EAAED,UAAU,CAAC;MACvCF,WAAW,CAAC5B,IAAI,GAAG+B,UAAU;IACjC,CAAC,MACI,IAAIA,UAAU,EAAE;MACjBH,WAAW,CAAC5B,IAAI,GAAG+B,UAAU;IACjC;IACA,OAAOH,WAAW;EACtB,CAAC;EACD7D,MAAM,CAACS,SAAS,CAACwD,UAAU,GAAG,UAAUD,UAAU,EAAED,UAAU,EAAE;IAC5D,IAAIG,SAAS,GAAGnG,IAAI,CAACgG,UAAU,CAAC;IAChC,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoD,SAAS,CAACnD,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,IAAIqD,QAAQ,GAAGD,SAAS,CAACpD,CAAC,CAAC;MAC3BkD,UAAU,CAACG,QAAQ,CAAC,GAAGH,UAAU,CAACG,QAAQ,CAAC,IAAI,CAAC,CAAC;MACjDrG,MAAM,CAACkG,UAAU,CAACG,QAAQ,CAAC,EAAEJ,UAAU,CAACI,QAAQ,CAAC,CAAC;IACtD;EACJ,CAAC;EACDnE,MAAM,CAACS,SAAS,CAAC2D,sBAAsB,GAAG,YAAY;IAClD,OAAOvF,4BAA4B;EACvC,CAAC;EACDmB,MAAM,CAACS,SAAS,CAAC4D,iBAAiB,GAAG,UAAUC,IAAI,EAAE;IACjD,IAAItD,KAAK,GAAG,IAAI,CAACV,SAAS,CAAC,IAAI,CAACyB,YAAY,CAAC;IAC7C,IAAI,CAACf,KAAK,IAAI,EAAEA,KAAK,YAAYsD,IAAI,CAAC,EAAE;MACpCtD,KAAK,GAAG,IAAIsD,IAAI,CAAC,CAAC;IACtB;IACA,IAAI,CAAChE,SAAS,CAAC,IAAI,CAACyB,YAAY,EAAE,CAAC,GAAGf,KAAK;IAC3CA,KAAK,CAACuB,IAAI,GAAG,IAAI,CAACA,IAAI;IACtBvB,KAAK,CAACuD,MAAM,GAAG,IAAI;IACnB,OAAOvD,KAAK;EAChB,CAAC;EACDhB,MAAM,CAACS,SAAS,CAAC0B,iBAAiB,GAAG,YAAY;IAC7C,IAAIrD,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI0F,QAAQ,GAAG1F,KAAK,CAAC2F,IAAI,IAAIjG,YAAY;IACzC,IAAIkG,WAAW,GAAG5F,KAAK,CAACc,OAAO;IAC/B,IAAI+E,IAAI,GAAGC,YAAY,CAAC9F,KAAK,CAAC;IAC9B,IAAI+F,YAAY,GAAGtH,cAAc,CAACoH,IAAI,EAAE7F,KAAK,CAAC;IAC9C,IAAIgG,UAAU,GAAGC,kBAAkB,CAACjG,KAAK,CAAC;IAC1C,IAAIkG,YAAY,GAAG,CAAC,CAAElG,KAAK,CAACa,eAAgB;IAC5C,IAAIsF,WAAW,GAAGJ,YAAY,CAACI,WAAW;IAC1C,IAAIC,UAAU,GAAGL,YAAY,CAACK,UAAU;IACxC,IAAIC,YAAY,GAAGN,YAAY,CAACM,YAAY;IAC5C,IAAIC,SAAS,GAAGP,YAAY,CAACQ,KAAK;IAClC,IAAIjG,UAAU,GAAGyF,YAAY,CAACzF,UAAU;IACxC,IAAIkG,YAAY,GAAG,IAAI,CAAC/E,aAAa;IACrC,IAAI,CAACgF,WAAW,GAAG,CAAC,CAACV,YAAY,CAACU,WAAW;IAC7C,IAAIC,KAAK,GAAG1G,KAAK,CAAC2G,CAAC,IAAI,CAAC;IACxB,IAAIC,KAAK,GAAG5G,KAAK,CAAC6G,CAAC,IAAI,CAAC;IACxB,IAAIC,SAAS,GAAG9G,KAAK,CAAC+G,KAAK,IAAIP,YAAY,CAACO,KAAK,IAAI,MAAM;IAC3D,IAAIC,aAAa,GAAGhH,KAAK,CAACgH,aAAa,IAAIR,YAAY,CAACQ,aAAa,IAAI,KAAK;IAC9E,IAAIC,KAAK,GAAGP,KAAK;IACjB,IAAIQ,KAAK,GAAG9H,WAAW,CAACwH,KAAK,EAAEb,YAAY,CAACoB,aAAa,EAAEH,aAAa,CAAC;IACzE,IAAIhB,UAAU,IAAIJ,WAAW,EAAE;MAC3B,IAAIwB,IAAI,GAAGjI,WAAW,CAACuH,KAAK,EAAEN,UAAU,EAAEU,SAAS,CAAC;MACpD,IAAIO,IAAI,GAAGjI,WAAW,CAACwH,KAAK,EAAET,WAAW,EAAEa,aAAa,CAAC;MACzDhB,UAAU,IAAI,IAAI,CAACsB,iBAAiB,CAACtH,KAAK,EAAEA,KAAK,EAAEoH,IAAI,EAAEC,IAAI,EAAEjB,UAAU,EAAED,WAAW,CAAC;IAC3F;IACAe,KAAK,IAAI5G,UAAU,GAAG,CAAC;IACvB,IAAIsF,WAAW,EAAE;MACbqB,KAAK,GAAGM,kBAAkB,CAACb,KAAK,EAAEI,SAAS,EAAElB,WAAW,CAAC;MACzD,IAAIoB,aAAa,KAAK,KAAK,EAAE;QACzBE,KAAK,IAAItB,WAAW,CAAC,CAAC,CAAC;MAC3B,CAAC,MACI,IAAIoB,aAAa,KAAK,QAAQ,EAAE;QACjCE,KAAK,IAAItB,WAAW,CAAC,CAAC,CAAC;MAC3B;IACJ;IACA,IAAI4B,gBAAgB,GAAG,CAAC;IACxB,IAAIC,cAAc,GAAG,KAAK;IAC1B,IAAIC,QAAQ,GAAGC,OAAO,CAAC,MAAM,IAAI3H,KAAK,GAChCA,KAAK,CAACH,IAAI,IACT4H,cAAc,GAAG,IAAI,EAAEjB,YAAY,CAAC3G,IAAI,CAAC,CAAC;IACjD,IAAI+H,UAAU,GAAGC,SAAS,CAAC,QAAQ,IAAI7H,KAAK,GACtCA,KAAK,CAACC,MAAM,GACX,CAACiG,YAAY,KACR,CAACM,YAAY,CAACsB,UAAU,IAAIL,cAAc,CAAC,IAC5CD,gBAAgB,GAAG1H,yBAAyB,EAAE0G,YAAY,CAACvG,MAAM,IAClE,IAAI,CAAC;IACf,IAAI8H,SAAS,GAAG/H,KAAK,CAACU,cAAc,GAAG,CAAC;IACxC,IAAIsH,iBAAiB,GAAGhI,KAAK,CAACO,KAAK,IAAI,IAAI,KACnCP,KAAK,CAACiI,QAAQ,KAAK,UAAU,IAAIjI,KAAK,CAACiI,QAAQ,KAAK,OAAO,IAAIjI,KAAK,CAACiI,QAAQ,KAAK,UAAU,CAAC;IACrG,IAAIC,oBAAoB,GAAGnC,YAAY,CAACmC,oBAAoB;IAC5D,KAAK,IAAIlG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsE,SAAS,CAACrE,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,IAAImG,EAAE,GAAG,IAAI,CAAC5C,iBAAiB,CAAC7G,KAAK,CAAC;MACtC,IAAI0J,UAAU,GAAGD,EAAE,CAACE,WAAW,CAAC,CAAC;MACjCF,EAAE,CAACG,QAAQ,CAACF,UAAU,CAAC;MACvBA,UAAU,CAACvC,IAAI,GAAGS,SAAS,CAACtE,CAAC,CAAC;MAC9BoG,UAAU,CAACzB,CAAC,GAAGM,KAAK;MACpBmB,UAAU,CAACvB,CAAC,GAAGK,KAAK;MACpB,IAAIJ,SAAS,EAAE;QACXsB,UAAU,CAACtB,SAAS,GAAGA,SAAS;MACpC;MACAsB,UAAU,CAACG,YAAY,GAAG,QAAQ;MAClCH,UAAU,CAACI,OAAO,GAAGxI,KAAK,CAACwI,OAAO;MAClCJ,UAAU,CAACK,WAAW,GAAG,IAAI;MAC7B,IAAIV,SAAS,EAAE;QACXK,UAAU,CAACM,UAAU,GAAG1I,KAAK,CAACU,cAAc,IAAI,CAAC;QACjD0H,UAAU,CAACO,WAAW,GAAG3I,KAAK,CAACS,eAAe,IAAI,aAAa;QAC/D2H,UAAU,CAACQ,aAAa,GAAG5I,KAAK,CAACW,iBAAiB,IAAI,CAAC;QACvDyH,UAAU,CAACS,aAAa,GAAG7I,KAAK,CAACY,iBAAiB,IAAI,CAAC;MAC3D;MACAwH,UAAU,CAACnI,MAAM,GAAG2H,UAAU;MAC9BQ,UAAU,CAACvI,IAAI,GAAG6H,QAAQ;MAC1B,IAAIE,UAAU,EAAE;QACZQ,UAAU,CAAChI,SAAS,GAAGJ,KAAK,CAACI,SAAS,IAAIoH,gBAAgB;QAC1DY,UAAU,CAACU,QAAQ,GAAG9I,KAAK,CAAC8I,QAAQ;QACpCV,UAAU,CAACW,cAAc,GAAG/I,KAAK,CAAC+I,cAAc,IAAI,CAAC;MACzD;MACAX,UAAU,CAACzC,IAAI,GAAGD,QAAQ;MAC1BsD,eAAe,CAACZ,UAAU,EAAEpI,KAAK,CAAC;MAClCkH,KAAK,IAAI5G,UAAU;MACnB,IAAI0H,iBAAiB,EAAE;QACnBG,EAAE,CAACc,eAAe,CAAC,IAAI1J,YAAY,CAACJ,WAAW,CAACiJ,UAAU,CAACzB,CAAC,EAAEN,YAAY,EAAE+B,UAAU,CAACtB,SAAS,CAAC,EAAE1H,WAAW,CAACgJ,UAAU,CAACvB,CAAC,EAAEqB,oBAAoB,EAAEE,UAAU,CAACG,YAAY,CAAC,EAAElC,YAAY,EAAE6B,oBAAoB,CAAC,CAAC;MACrN;IACJ;EACJ,CAAC;EACDhH,MAAM,CAACS,SAAS,CAACyB,gBAAgB,GAAG,YAAY;IAC5C,IAAIpD,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI6F,IAAI,GAAGC,YAAY,CAAC9F,KAAK,CAAC;IAC9B,IAAI+F,YAAY,GAAGvH,aAAa,CAACqH,IAAI,EAAE7F,KAAK,CAAC;IAC7C,IAAIqG,YAAY,GAAGN,YAAY,CAACxF,KAAK;IACrC,IAAI6F,UAAU,GAAGL,YAAY,CAACK,UAAU;IACxC,IAAID,WAAW,GAAGJ,YAAY,CAACI,WAAW;IAC1C,IAAIP,WAAW,GAAG5F,KAAK,CAACc,OAAO;IAC/B,IAAI4F,KAAK,GAAG1G,KAAK,CAAC2G,CAAC,IAAI,CAAC;IACxB,IAAIC,KAAK,GAAG5G,KAAK,CAAC6G,CAAC,IAAI,CAAC;IACxB,IAAIL,YAAY,GAAG,IAAI,CAAC/E,aAAa;IACrC,IAAIqF,SAAS,GAAG9G,KAAK,CAAC+G,KAAK,IAAIP,YAAY,CAACO,KAAK;IACjD,IAAIC,aAAa,GAAGhH,KAAK,CAACgH,aAAa,IAAIR,YAAY,CAACQ,aAAa;IACrE,IAAI,CAACP,WAAW,GAAG,CAAC,CAACV,YAAY,CAACU,WAAW;IAC7C,IAAIW,IAAI,GAAGjI,WAAW,CAACuH,KAAK,EAAEN,UAAU,EAAEU,SAAS,CAAC;IACpD,IAAIO,IAAI,GAAGjI,WAAW,CAACwH,KAAK,EAAET,WAAW,EAAEa,aAAa,CAAC;IACzD,IAAIkC,KAAK,GAAG9B,IAAI;IAChB,IAAI+B,OAAO,GAAG9B,IAAI;IAClB,IAAIzB,WAAW,EAAE;MACbsD,KAAK,IAAItD,WAAW,CAAC,CAAC,CAAC;MACvBuD,OAAO,IAAIvD,WAAW,CAAC,CAAC,CAAC;IAC7B;IACA,IAAIwD,MAAM,GAAGF,KAAK,GAAG7C,YAAY;IACjC,IAAIJ,kBAAkB,CAACjG,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACsH,iBAAiB,CAACtH,KAAK,EAAEA,KAAK,EAAEoH,IAAI,EAAEC,IAAI,EAAEjB,UAAU,EAAED,WAAW,CAAC;IAC7E;IACA,IAAID,YAAY,GAAG,CAAC,CAAElG,KAAK,CAACa,eAAgB;IAC5C,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,YAAY,CAACQ,KAAK,CAACtE,MAAM,EAAED,CAAC,EAAE,EAAE;MAChD,IAAIqH,IAAI,GAAGtD,YAAY,CAACQ,KAAK,CAACvE,CAAC,CAAC;MAChC,IAAIsH,MAAM,GAAGD,IAAI,CAACC,MAAM;MACxB,IAAIC,UAAU,GAAGD,MAAM,CAACrH,MAAM;MAC9B,IAAI3B,UAAU,GAAG+I,IAAI,CAAC/I,UAAU;MAChC,IAAIkJ,aAAa,GAAGH,IAAI,CAAC9I,KAAK;MAC9B,IAAIkJ,SAAS,GAAG,CAAC;MACjB,IAAIC,SAAS,GAAGR,KAAK;MACrB,IAAIS,UAAU,GAAGP,MAAM;MACvB,IAAIQ,UAAU,GAAGL,UAAU,GAAG,CAAC;MAC/B,IAAIM,KAAK,GAAG,KAAK,CAAC;MAClB,OAAOJ,SAAS,GAAGF,UAAU,KACrBM,KAAK,GAAGP,MAAM,CAACG,SAAS,CAAC,EAAE,CAACI,KAAK,CAAC9C,KAAK,IAAI8C,KAAK,CAAC9C,KAAK,KAAK,MAAM,CAAC,EAAE;QACxE,IAAI,CAAC+C,WAAW,CAACD,KAAK,EAAE7J,KAAK,EAAEM,UAAU,EAAE6I,OAAO,EAAEO,SAAS,EAAE,MAAM,EAAExD,YAAY,CAAC;QACpFsD,aAAa,IAAIK,KAAK,CAACtJ,KAAK;QAC5BmJ,SAAS,IAAIG,KAAK,CAACtJ,KAAK;QACxBkJ,SAAS,EAAE;MACf;MACA,OAAOG,UAAU,IAAI,CAAC,KACdC,KAAK,GAAGP,MAAM,CAACM,UAAU,CAAC,EAAEC,KAAK,CAAC9C,KAAK,KAAK,OAAO,CAAC,EAAE;QAC1D,IAAI,CAAC+C,WAAW,CAACD,KAAK,EAAE7J,KAAK,EAAEM,UAAU,EAAE6I,OAAO,EAAEQ,UAAU,EAAE,OAAO,EAAEzD,YAAY,CAAC;QACtFsD,aAAa,IAAIK,KAAK,CAACtJ,KAAK;QAC5BoJ,UAAU,IAAIE,KAAK,CAACtJ,KAAK;QACzBqJ,UAAU,EAAE;MAChB;MACAF,SAAS,IAAI,CAACrD,YAAY,IAAIqD,SAAS,GAAGR,KAAK,CAAC,IAAIE,MAAM,GAAGO,UAAU,CAAC,GAAGH,aAAa,IAAI,CAAC;MAC7F,OAAOC,SAAS,IAAIG,UAAU,EAAE;QAC5BC,KAAK,GAAGP,MAAM,CAACG,SAAS,CAAC;QACzB,IAAI,CAACK,WAAW,CAACD,KAAK,EAAE7J,KAAK,EAAEM,UAAU,EAAE6I,OAAO,EAAEO,SAAS,GAAGG,KAAK,CAACtJ,KAAK,GAAG,CAAC,EAAE,QAAQ,EAAE2F,YAAY,CAAC;QACxGwD,SAAS,IAAIG,KAAK,CAACtJ,KAAK;QACxBkJ,SAAS,EAAE;MACf;MACAN,OAAO,IAAI7I,UAAU;IACzB;EACJ,CAAC;EACDY,MAAM,CAACS,SAAS,CAACmI,WAAW,GAAG,UAAUD,KAAK,EAAE7J,KAAK,EAAEM,UAAU,EAAE6I,OAAO,EAAExC,CAAC,EAAEG,SAAS,EAAEiD,kBAAkB,EAAE;IAC1G,IAAIC,UAAU,GAAGhK,KAAK,CAACmD,IAAI,CAAC0G,KAAK,CAACI,SAAS,CAAC,IAAI,CAAC,CAAC;IAClDD,UAAU,CAACnE,IAAI,GAAGgE,KAAK,CAAChE,IAAI;IAC5B,IAAImB,aAAa,GAAG6C,KAAK,CAAC7C,aAAa;IACvC,IAAIH,CAAC,GAAGsC,OAAO,GAAG7I,UAAU,GAAG,CAAC;IAChC,IAAI0G,aAAa,KAAK,KAAK,EAAE;MACzBH,CAAC,GAAGsC,OAAO,GAAGU,KAAK,CAACrJ,MAAM,GAAG,CAAC;IAClC,CAAC,MACI,IAAIwG,aAAa,KAAK,QAAQ,EAAE;MACjCH,CAAC,GAAGsC,OAAO,GAAG7I,UAAU,GAAGuJ,KAAK,CAACrJ,MAAM,GAAG,CAAC;IAC/C;IACA,IAAIwF,UAAU,GAAG,CAAC6D,KAAK,CAACK,YAAY,IAAIjE,kBAAkB,CAAC+D,UAAU,CAAC;IACtEhE,UAAU,IAAI,IAAI,CAACsB,iBAAiB,CAAC0C,UAAU,EAAEhK,KAAK,EAAE8G,SAAS,KAAK,OAAO,GACvEH,CAAC,GAAGkD,KAAK,CAACtJ,KAAK,GACfuG,SAAS,KAAK,QAAQ,GAClBH,CAAC,GAAGkD,KAAK,CAACtJ,KAAK,GAAG,CAAC,GACnBoG,CAAC,EAAEE,CAAC,GAAGgD,KAAK,CAACrJ,MAAM,GAAG,CAAC,EAAEqJ,KAAK,CAACtJ,KAAK,EAAEsJ,KAAK,CAACrJ,MAAM,CAAC;IAC7D,IAAI0F,YAAY,GAAG,CAAC,CAAC8D,UAAU,CAACnJ,eAAe;IAC/C,IAAI+E,WAAW,GAAGiE,KAAK,CAACjE,WAAW;IACnC,IAAIA,WAAW,EAAE;MACbe,CAAC,GAAGY,kBAAkB,CAACZ,CAAC,EAAEG,SAAS,EAAElB,WAAW,CAAC;MACjDiB,CAAC,IAAIgD,KAAK,CAACrJ,MAAM,GAAG,CAAC,GAAGoF,WAAW,CAAC,CAAC,CAAC,GAAGiE,KAAK,CAACM,WAAW,GAAG,CAAC;IAClE;IACA,IAAIhC,EAAE,GAAG,IAAI,CAAC5C,iBAAiB,CAAC7G,KAAK,CAAC;IACtC,IAAI0J,UAAU,GAAGD,EAAE,CAACE,WAAW,CAAC,CAAC;IACjCF,EAAE,CAACG,QAAQ,CAACF,UAAU,CAAC;IACvB,IAAI5B,YAAY,GAAG,IAAI,CAAC/E,aAAa;IACrC,IAAIgG,cAAc,GAAG,KAAK;IAC1B,IAAID,gBAAgB,GAAG,CAAC;IACxB,IAAIE,QAAQ,GAAGC,OAAO,CAAC,MAAM,IAAIqC,UAAU,GAAGA,UAAU,CAACnK,IAAI,GACvD,MAAM,IAAIG,KAAK,GAAGA,KAAK,CAACH,IAAI,IACvB4H,cAAc,GAAG,IAAI,EAAEjB,YAAY,CAAC3G,IAAI,CAAC,CAAC;IACrD,IAAI+H,UAAU,GAAGC,SAAS,CAAC,QAAQ,IAAImC,UAAU,GAAGA,UAAU,CAAC/J,MAAM,GAC/D,QAAQ,IAAID,KAAK,GAAGA,KAAK,CAACC,MAAM,GAC3B,CAACiG,YAAY,IACT,CAAC6D,kBAAkB,KAClB,CAACvD,YAAY,CAACsB,UAAU,IAAIL,cAAc,CAAC,IAAKD,gBAAgB,GAAG1H,yBAAyB,EAAE0G,YAAY,CAACvG,MAAM,IACnH,IAAI,CAAC;IACnB,IAAI8H,SAAS,GAAGiC,UAAU,CAACtJ,cAAc,GAAG,CAAC,IACtCV,KAAK,CAACU,cAAc,GAAG,CAAC;IAC/B0H,UAAU,CAACvC,IAAI,GAAGgE,KAAK,CAAChE,IAAI;IAC5BuC,UAAU,CAACzB,CAAC,GAAGA,CAAC;IAChByB,UAAU,CAACvB,CAAC,GAAGA,CAAC;IAChB,IAAIkB,SAAS,EAAE;MACXK,UAAU,CAACM,UAAU,GAAGsB,UAAU,CAACtJ,cAAc,IAAIV,KAAK,CAACU,cAAc,IAAI,CAAC;MAC9E0H,UAAU,CAACO,WAAW,GAAGqB,UAAU,CAACvJ,eAAe,IAAIT,KAAK,CAACS,eAAe,IAAI,aAAa;MAC7F2H,UAAU,CAACQ,aAAa,GAAGoB,UAAU,CAACrJ,iBAAiB,IAAIX,KAAK,CAACW,iBAAiB,IAAI,CAAC;MACvFyH,UAAU,CAACS,aAAa,GAAGmB,UAAU,CAACpJ,iBAAiB,IAAIZ,KAAK,CAACY,iBAAiB,IAAI,CAAC;IAC3F;IACAwH,UAAU,CAACtB,SAAS,GAAGA,SAAS;IAChCsB,UAAU,CAACG,YAAY,GAAG,QAAQ;IAClCH,UAAU,CAACzC,IAAI,GAAGkE,KAAK,CAAClE,IAAI,IAAIjG,YAAY;IAC5C0I,UAAU,CAACI,OAAO,GAAGzJ,SAAS,CAACiL,UAAU,CAACxB,OAAO,EAAExI,KAAK,CAACwI,OAAO,EAAE,CAAC,CAAC;IACpEQ,eAAe,CAACZ,UAAU,EAAE4B,UAAU,CAAC;IACvC,IAAIpC,UAAU,EAAE;MACZQ,UAAU,CAAChI,SAAS,GAAGrB,SAAS,CAACiL,UAAU,CAAC5J,SAAS,EAAEJ,KAAK,CAACI,SAAS,EAAEoH,gBAAgB,CAAC;MACzFY,UAAU,CAACU,QAAQ,GAAGnK,SAAS,CAACqL,UAAU,CAAClB,QAAQ,EAAE9I,KAAK,CAAC8I,QAAQ,CAAC;MACpEV,UAAU,CAACW,cAAc,GAAG/I,KAAK,CAAC+I,cAAc,IAAI,CAAC;MACrDX,UAAU,CAACnI,MAAM,GAAG2H,UAAU;IAClC;IACA,IAAIF,QAAQ,EAAE;MACVU,UAAU,CAACvI,IAAI,GAAG6H,QAAQ;IAC9B;IACA,IAAI0C,SAAS,GAAGP,KAAK,CAACxD,YAAY;IAClC,IAAIgE,UAAU,GAAGR,KAAK,CAAC1C,aAAa;IACpCgB,EAAE,CAACc,eAAe,CAAC,IAAI1J,YAAY,CAACJ,WAAW,CAACiJ,UAAU,CAACzB,CAAC,EAAEyD,SAAS,EAAEhC,UAAU,CAACtB,SAAS,CAAC,EAAE1H,WAAW,CAACgJ,UAAU,CAACvB,CAAC,EAAEwD,UAAU,EAAEjC,UAAU,CAACG,YAAY,CAAC,EAAE6B,SAAS,EAAEC,UAAU,CAAC,CAAC;EAC3L,CAAC;EACDnJ,MAAM,CAACS,SAAS,CAAC2F,iBAAiB,GAAG,UAAUtH,KAAK,EAAEsK,QAAQ,EAAE3D,CAAC,EAAEE,CAAC,EAAEtG,KAAK,EAAEC,MAAM,EAAE;IACjF,IAAI+J,mBAAmB,GAAGvK,KAAK,CAACa,eAAe;IAC/C,IAAI2J,eAAe,GAAGxK,KAAK,CAACgB,WAAW;IACvC,IAAIyJ,eAAe,GAAGzK,KAAK,CAACe,WAAW;IACvC,IAAI2J,SAAS,GAAGH,mBAAmB,IAAIA,mBAAmB,CAACI,KAAK;IAChE,IAAIC,mBAAmB,GAAGL,mBAAmB,IAAI,CAACG,SAAS;IAC3D,IAAIG,gBAAgB,GAAG7K,KAAK,CAACiB,YAAY;IACzC,IAAI6J,IAAI,GAAG,IAAI;IACf,IAAIC,MAAM;IACV,IAAIC,KAAK;IACT,IAAIJ,mBAAmB,IAAI5K,KAAK,CAACM,UAAU,IAAKkK,eAAe,IAAIC,eAAgB,EAAE;MACjFM,MAAM,GAAG,IAAI,CAACxF,iBAAiB,CAACjG,IAAI,CAAC;MACrCyL,MAAM,CAACzC,QAAQ,CAACyC,MAAM,CAAC1C,WAAW,CAAC,CAAC,CAAC;MACrC0C,MAAM,CAAC/K,KAAK,CAACH,IAAI,GAAG,IAAI;MACxB,IAAIoL,SAAS,GAAGF,MAAM,CAACG,KAAK;MAC5BD,SAAS,CAACtE,CAAC,GAAGA,CAAC;MACfsE,SAAS,CAACpE,CAAC,GAAGA,CAAC;MACfoE,SAAS,CAAC1K,KAAK,GAAGA,KAAK;MACvB0K,SAAS,CAACzK,MAAM,GAAGA,MAAM;MACzByK,SAAS,CAACE,CAAC,GAAGN,gBAAgB;MAC9BE,MAAM,CAACK,UAAU,CAAC,CAAC;IACvB;IACA,IAAIR,mBAAmB,EAAE;MACrB,IAAIS,SAAS,GAAGN,MAAM,CAAC/K,KAAK;MAC5BqL,SAAS,CAACxL,IAAI,GAAG0K,mBAAmB,IAAI,IAAI;MAC5Cc,SAAS,CAACnL,WAAW,GAAGvB,SAAS,CAACqB,KAAK,CAACE,WAAW,EAAE,CAAC,CAAC;IAC3D,CAAC,MACI,IAAIwK,SAAS,EAAE;MAChBM,KAAK,GAAG,IAAI,CAACzF,iBAAiB,CAAClG,OAAO,CAAC;MACvC2L,KAAK,CAACM,MAAM,GAAG,YAAY;QACvBR,IAAI,CAACS,UAAU,CAAC,CAAC;MACrB,CAAC;MACD,IAAIC,QAAQ,GAAGR,KAAK,CAAChL,KAAK;MAC1BwL,QAAQ,CAACb,KAAK,GAAGJ,mBAAmB,CAACI,KAAK;MAC1Ca,QAAQ,CAAC7E,CAAC,GAAGA,CAAC;MACd6E,QAAQ,CAAC3E,CAAC,GAAGA,CAAC;MACd2E,QAAQ,CAACjL,KAAK,GAAGA,KAAK;MACtBiL,QAAQ,CAAChL,MAAM,GAAGA,MAAM;IAC5B;IACA,IAAIgK,eAAe,IAAIC,eAAe,EAAE;MACpC,IAAIY,SAAS,GAAGN,MAAM,CAAC/K,KAAK;MAC5BqL,SAAS,CAACjL,SAAS,GAAGoK,eAAe;MACrCa,SAAS,CAACpL,MAAM,GAAGwK,eAAe;MAClCY,SAAS,CAAClL,aAAa,GAAGxB,SAAS,CAACqB,KAAK,CAACG,aAAa,EAAE,CAAC,CAAC;MAC3DkL,SAAS,CAACvC,QAAQ,GAAG9I,KAAK,CAACyL,UAAU;MACrCJ,SAAS,CAACtC,cAAc,GAAG/I,KAAK,CAAC0L,gBAAgB,IAAI,CAAC;MACtDX,MAAM,CAACY,sBAAsB,GAAG,CAAC;MACjC,IAAIZ,MAAM,CAACa,OAAO,CAAC,CAAC,IAAIb,MAAM,CAACc,SAAS,CAAC,CAAC,EAAE;QACxCR,SAAS,CAAC5C,WAAW,GAAG,IAAI;QAC5B4C,SAAS,CAACjL,SAAS,IAAI,CAAC;MAC5B;IACJ;IACA,IAAI0L,WAAW,GAAG,CAACf,MAAM,IAAIC,KAAK,EAAEhL,KAAK;IACzC8L,WAAW,CAACpD,UAAU,GAAG1I,KAAK,CAAC0I,UAAU,IAAI,CAAC;IAC9CoD,WAAW,CAACnD,WAAW,GAAG3I,KAAK,CAAC2I,WAAW,IAAI,aAAa;IAC5DmD,WAAW,CAAClD,aAAa,GAAG5I,KAAK,CAAC4I,aAAa,IAAI,CAAC;IACpDkD,WAAW,CAACjD,aAAa,GAAG7I,KAAK,CAAC6I,aAAa,IAAI,CAAC;IACpDiD,WAAW,CAACtD,OAAO,GAAGzJ,SAAS,CAACiB,KAAK,CAACwI,OAAO,EAAE8B,QAAQ,CAAC9B,OAAO,EAAE,CAAC,CAAC;EACvE,CAAC;EACDtH,MAAM,CAAC6K,QAAQ,GAAG,UAAU/L,KAAK,EAAE;IAC/B,IAAI2F,IAAI,GAAG,EAAE;IACb,IAAIqG,eAAe,CAAChM,KAAK,CAAC,EAAE;MACxB2F,IAAI,GAAG,CACH3F,KAAK,CAACiM,SAAS,EACfjM,KAAK,CAACkM,UAAU,EAChBC,aAAa,CAACnM,KAAK,CAACK,QAAQ,CAAC,EAC7BL,KAAK,CAACoM,UAAU,IAAI,YAAY,CACnC,CAACC,IAAI,CAAC,GAAG,CAAC;IACf;IACA,OAAO1G,IAAI,IAAI7G,IAAI,CAAC6G,IAAI,CAAC,IAAI3F,KAAK,CAAC0F,QAAQ,IAAI1F,KAAK,CAAC2F,IAAI;EAC7D,CAAC;EACD,OAAOzE,MAAM;AACjB,CAAC,CAAC1B,WAAW,CAAE;AACf,IAAI8M,gBAAgB,GAAG;EAAEC,IAAI,EAAE,IAAI;EAAEC,KAAK,EAAE,CAAC;EAAEC,MAAM,EAAE;AAAE,CAAC;AAC1D,IAAIC,yBAAyB,GAAG;EAAEC,GAAG,EAAE,CAAC;EAAEC,MAAM,EAAE,CAAC;EAAEC,MAAM,EAAE;AAAE,CAAC;AAChE,IAAIC,UAAU,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC;AACtE,OAAO,SAASX,aAAaA,CAAC9L,QAAQ,EAAE;EACpC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,KACxBA,QAAQ,CAAC0M,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAC1B1M,QAAQ,CAAC0M,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAC9B1M,QAAQ,CAAC0M,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;IACvC,OAAO1M,QAAQ;EACnB,CAAC,MACI,IAAI,CAAC2M,KAAK,CAAC,CAAC3M,QAAQ,CAAC,EAAE;IACxB,OAAOA,QAAQ,GAAG,IAAI;EAC1B,CAAC,MACI;IACD,OAAOV,iBAAiB,GAAG,IAAI;EACnC;AACJ;AACA,SAASqJ,eAAeA,CAACjE,WAAW,EAAEC,WAAW,EAAE;EAC/C,KAAK,IAAIhD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8K,UAAU,CAAC7K,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,IAAIiL,QAAQ,GAAGH,UAAU,CAAC9K,CAAC,CAAC;IAC5B,IAAIkL,GAAG,GAAGlI,WAAW,CAACiI,QAAQ,CAAC;IAC/B,IAAIC,GAAG,IAAI,IAAI,EAAE;MACbnI,WAAW,CAACkI,QAAQ,CAAC,GAAGC,GAAG;IAC/B;EACJ;AACJ;AACA,OAAO,SAASlB,eAAeA,CAAChM,KAAK,EAAE;EACnC,OAAOA,KAAK,CAACK,QAAQ,IAAI,IAAI,IAAIL,KAAK,CAACoM,UAAU,IAAIpM,KAAK,CAACkM,UAAU;AACzE;AACA,OAAO,SAAShJ,kBAAkBA,CAAClD,KAAK,EAAE;EACtCmN,cAAc,CAACnN,KAAK,CAAC;EACrBpB,IAAI,CAACoB,KAAK,CAACmD,IAAI,EAAEgK,cAAc,CAAC;EAChC,OAAOnN,KAAK;AAChB;AACA,SAASmN,cAAcA,CAACnN,KAAK,EAAE;EAC3B,IAAIA,KAAK,EAAE;IACPA,KAAK,CAAC2F,IAAI,GAAGzE,MAAM,CAAC6K,QAAQ,CAAC/L,KAAK,CAAC;IACnC,IAAI8G,SAAS,GAAG9G,KAAK,CAAC+G,KAAK;IAC3BD,SAAS,KAAK,QAAQ,KAAKA,SAAS,GAAG,QAAQ,CAAC;IAChD9G,KAAK,CAAC+G,KAAK,GAAID,SAAS,IAAI,IAAI,IAAIwF,gBAAgB,CAACxF,SAAS,CAAC,GAAIA,SAAS,GAAG,MAAM;IACrF,IAAIE,aAAa,GAAGhH,KAAK,CAACgH,aAAa;IACvCA,aAAa,KAAK,QAAQ,KAAKA,aAAa,GAAG,QAAQ,CAAC;IACxDhH,KAAK,CAACgH,aAAa,GAAIA,aAAa,IAAI,IAAI,IAAI0F,yBAAyB,CAAC1F,aAAa,CAAC,GAAIA,aAAa,GAAG,KAAK;IACjH,IAAIpB,WAAW,GAAG5F,KAAK,CAACc,OAAO;IAC/B,IAAI8E,WAAW,EAAE;MACb5F,KAAK,CAACc,OAAO,GAAGjC,iBAAiB,CAACmB,KAAK,CAACc,OAAO,CAAC;IACpD;EACJ;AACJ;AACA,SAAS+G,SAASA,CAAC5H,MAAM,EAAEG,SAAS,EAAE;EAClC,OAAQH,MAAM,IAAI,IAAI,IAAIG,SAAS,IAAI,CAAC,IAAIH,MAAM,KAAK,aAAa,IAAIA,MAAM,KAAK,MAAM,GACnF,IAAI,GACHA,MAAM,CAAC0K,KAAK,IAAI1K,MAAM,CAACmN,UAAU,GAC9B,MAAM,GACNnN,MAAM;AACpB;AACA,SAAS0H,OAAOA,CAAC9H,IAAI,EAAE;EACnB,OAAQA,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAK,MAAM,GACjC,IAAI,GACHA,IAAI,CAAC8K,KAAK,IAAI9K,IAAI,CAACuN,UAAU,GAC1B,MAAM,GACNvN,IAAI;AAClB;AACA,SAAS0H,kBAAkBA,CAACZ,CAAC,EAAEG,SAAS,EAAElB,WAAW,EAAE;EACnD,OAAOkB,SAAS,KAAK,OAAO,GACrBH,CAAC,GAAGf,WAAW,CAAC,CAAC,CAAC,GACnBkB,SAAS,KAAK,QAAQ,GACjBH,CAAC,GAAGf,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,GAC3Ce,CAAC,GAAGf,WAAW,CAAC,CAAC,CAAE;AAClC;AACA,SAASE,YAAYA,CAAC9F,KAAK,EAAE;EACzB,IAAI6F,IAAI,GAAG7F,KAAK,CAAC6F,IAAI;EACrBA,IAAI,IAAI,IAAI,KAAKA,IAAI,IAAI,EAAE,CAAC;EAC5B,OAAOA,IAAI;AACf;AACA,SAASI,kBAAkBA,CAACjG,KAAK,EAAE;EAC/B,OAAO,CAAC,EAAEA,KAAK,CAACa,eAAe,IACxBb,KAAK,CAACM,UAAU,IACfN,KAAK,CAACgB,WAAW,IAAIhB,KAAK,CAACe,WAAY,CAAC;AACpD;AACA,eAAeG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}