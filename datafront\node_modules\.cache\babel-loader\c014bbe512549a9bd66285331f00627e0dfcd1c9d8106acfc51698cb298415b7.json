{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar coordsOffsetMap = {\n  '南海诸岛': [32, 80],\n  // 全国\n  '广东': [0, -10],\n  '香港': [10, 5],\n  '澳门': [-10, 10],\n  // '北京': [-10, 0],\n  '天津': [5, 5]\n};\nexport default function fixTextCoords(mapType, region) {\n  if (mapType === 'china') {\n    var coordFix = coordsOffsetMap[region.name];\n    if (coordFix) {\n      var cp = region.getCenter();\n      cp[0] += coordFix[0] / 10.5;\n      cp[1] += -coordFix[1] / (10.5 / 0.75);\n      region.setCenter(cp);\n    }\n  }\n}", "map": {"version": 3, "names": ["coordsOffsetMap", "fixTextCoords", "mapType", "region", "coordFix", "name", "cp", "getCenter", "setCenter"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/coord/geo/fix/textCoord.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar coordsOffsetMap = {\n  '南海诸岛': [32, 80],\n  // 全国\n  '广东': [0, -10],\n  '香港': [10, 5],\n  '澳门': [-10, 10],\n  // '北京': [-10, 0],\n  '天津': [5, 5]\n};\nexport default function fixTextCoords(mapType, region) {\n  if (mapType === 'china') {\n    var coordFix = coordsOffsetMap[region.name];\n    if (coordFix) {\n      var cp = region.getCenter();\n      cp[0] += coordFix[0] / 10.5;\n      cp[1] += -coordFix[1] / (10.5 / 0.75);\n      region.setCenter(cp);\n    }\n  }\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,eAAe,GAAG;EACpB,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EAChB;EACA,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EACd,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;EACb,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;EACf;EACA,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;AACb,CAAC;AACD,eAAe,SAASC,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACrD,IAAID,OAAO,KAAK,OAAO,EAAE;IACvB,IAAIE,QAAQ,GAAGJ,eAAe,CAACG,MAAM,CAACE,IAAI,CAAC;IAC3C,IAAID,QAAQ,EAAE;MACZ,IAAIE,EAAE,GAAGH,MAAM,CAACI,SAAS,CAAC,CAAC;MAC3BD,EAAE,CAAC,CAAC,CAAC,IAAIF,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI;MAC3BE,EAAE,CAAC,CAAC,CAAC,IAAI,CAACF,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC;MACrCD,MAAM,CAACK,SAAS,CAACF,EAAE,CAAC;IACtB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}