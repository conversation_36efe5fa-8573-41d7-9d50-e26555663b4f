{"ast": null, "code": "require(\"core-js/modules/es.iterator.constructor.js\");\nrequire(\"core-js/modules/es.iterator.reduce.js\");\nvar nestRE = /^(attrs|props|on|nativeOn|class|style|hook)$/;\nmodule.exports = function mergeJSXProps(objs) {\n  return objs.reduce(function (a, b) {\n    var aa, bb, key, nestedKey, temp;\n    for (key in b) {\n      aa = a[key];\n      bb = b[key];\n      if (aa && nestRE.test(key)) {\n        // normalize class\n        if (key === 'class') {\n          if (typeof aa === 'string') {\n            temp = aa;\n            a[key] = aa = {};\n            aa[temp] = true;\n          }\n          if (typeof bb === 'string') {\n            temp = bb;\n            b[key] = bb = {};\n            bb[temp] = true;\n          }\n        }\n        if (key === 'on' || key === 'nativeOn' || key === 'hook') {\n          // merge functions\n          for (nestedKey in bb) {\n            aa[nestedKey] = mergeFn(aa[nestedKey], bb[nestedKey]);\n          }\n        } else if (Array.isArray(aa)) {\n          a[key] = aa.concat(bb);\n        } else if (Array.isArray(bb)) {\n          a[key] = [aa].concat(bb);\n        } else {\n          for (nestedKey in bb) {\n            aa[nestedKey] = bb[nestedKey];\n          }\n        }\n      } else {\n        a[key] = b[key];\n      }\n    }\n    return a;\n  }, {});\n};\nfunction mergeFn(a, b) {\n  return function () {\n    a && a.apply(this, arguments);\n    b && b.apply(this, arguments);\n  };\n}", "map": {"version": 3, "names": ["nestRE", "module", "exports", "mergeJSXProps", "objs", "reduce", "a", "b", "aa", "bb", "key", "nested<PERSON><PERSON>", "temp", "test", "mergeFn", "Array", "isArray", "concat", "apply", "arguments"], "sources": ["D:/FastBI/datafront/node_modules/babel-helper-vue-jsx-merge-props/index.js"], "sourcesContent": ["var nestRE = /^(attrs|props|on|nativeOn|class|style|hook)$/\n\nmodule.exports = function mergeJSXProps (objs) {\n  return objs.reduce(function (a, b) {\n    var aa, bb, key, nestedKey, temp\n    for (key in b) {\n      aa = a[key]\n      bb = b[key]\n      if (aa && nestRE.test(key)) {\n        // normalize class\n        if (key === 'class') {\n          if (typeof aa === 'string') {\n            temp = aa\n            a[key] = aa = {}\n            aa[temp] = true\n          }\n          if (typeof bb === 'string') {\n            temp = bb\n            b[key] = bb = {}\n            bb[temp] = true\n          }\n        }\n        if (key === 'on' || key === 'nativeOn' || key === 'hook') {\n          // merge functions\n          for (nestedKey in bb) {\n            aa[nestedKey] = mergeFn(aa[nestedKey], bb[nestedKey])\n          }\n        } else if (Array.isArray(aa)) {\n          a[key] = aa.concat(bb)\n        } else if (Array.isArray(bb)) {\n          a[key] = [aa].concat(bb)\n        } else {\n          for (nested<PERSON>ey in bb) {\n            aa[nestedKey] = bb[nestedKey]\n          }\n        }\n      } else {\n        a[key] = b[key]\n      }\n    }\n    return a\n  }, {})\n}\n\nfunction mergeFn (a, b) {\n  return function () {\n    a && a.apply(this, arguments)\n    b && b.apply(this, arguments)\n  }\n}\n"], "mappings": ";;AAAA,IAAIA,MAAM,GAAG,8CAA8C;AAE3DC,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAAEC,IAAI,EAAE;EAC7C,OAAOA,IAAI,CAACC,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACjC,IAAIC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEC,SAAS,EAAEC,IAAI;IAChC,KAAKF,GAAG,IAAIH,CAAC,EAAE;MACbC,EAAE,GAAGF,CAAC,CAACI,GAAG,CAAC;MACXD,EAAE,GAAGF,CAAC,CAACG,GAAG,CAAC;MACX,IAAIF,EAAE,IAAIR,MAAM,CAACa,IAAI,CAACH,GAAG,CAAC,EAAE;QAC1B;QACA,IAAIA,GAAG,KAAK,OAAO,EAAE;UACnB,IAAI,OAAOF,EAAE,KAAK,QAAQ,EAAE;YAC1BI,IAAI,GAAGJ,EAAE;YACTF,CAAC,CAACI,GAAG,CAAC,GAAGF,EAAE,GAAG,CAAC,CAAC;YAChBA,EAAE,CAACI,IAAI,CAAC,GAAG,IAAI;UACjB;UACA,IAAI,OAAOH,EAAE,KAAK,QAAQ,EAAE;YAC1BG,IAAI,GAAGH,EAAE;YACTF,CAAC,CAACG,GAAG,CAAC,GAAGD,EAAE,GAAG,CAAC,CAAC;YAChBA,EAAE,CAACG,IAAI,CAAC,GAAG,IAAI;UACjB;QACF;QACA,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,KAAK,MAAM,EAAE;UACxD;UACA,KAAKC,SAAS,IAAIF,EAAE,EAAE;YACpBD,EAAE,CAACG,SAAS,CAAC,GAAGG,OAAO,CAACN,EAAE,CAACG,SAAS,CAAC,EAAEF,EAAE,CAACE,SAAS,CAAC,CAAC;UACvD;QACF,CAAC,MAAM,IAAII,KAAK,CAACC,OAAO,CAACR,EAAE,CAAC,EAAE;UAC5BF,CAAC,CAACI,GAAG,CAAC,GAAGF,EAAE,CAACS,MAAM,CAACR,EAAE,CAAC;QACxB,CAAC,MAAM,IAAIM,KAAK,CAACC,OAAO,CAACP,EAAE,CAAC,EAAE;UAC5BH,CAAC,CAACI,GAAG,CAAC,GAAG,CAACF,EAAE,CAAC,CAACS,MAAM,CAACR,EAAE,CAAC;QAC1B,CAAC,MAAM;UACL,KAAKE,SAAS,IAAIF,EAAE,EAAE;YACpBD,EAAE,CAACG,SAAS,CAAC,GAAGF,EAAE,CAACE,SAAS,CAAC;UAC/B;QACF;MACF,CAAC,MAAM;QACLL,CAAC,CAACI,GAAG,CAAC,GAAGH,CAAC,CAACG,GAAG,CAAC;MACjB;IACF;IACA,OAAOJ,CAAC;EACV,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;AAED,SAASQ,OAAOA,CAAER,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAO,YAAY;IACjBD,CAAC,IAAIA,CAAC,CAACY,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAC7BZ,CAAC,IAAIA,CAAC,CAACW,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC/B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}