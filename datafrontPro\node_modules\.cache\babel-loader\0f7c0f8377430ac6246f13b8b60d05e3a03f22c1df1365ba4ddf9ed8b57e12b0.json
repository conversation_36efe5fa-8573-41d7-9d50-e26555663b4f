{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { dataApi } from './api/index.js';\nimport { v4 as uuidv4 } from 'uuid';\nimport axios from 'axios';\nimport ChartDisplay from './components/ChartDisplay.vue';\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas';\nimport { jsPDF } from 'jspdf';\nimport * as echarts from 'echarts';\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      // 原始树结构\n      datasets: [],\n      // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [],\n      // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false,\n      // 新增：控制原始响应弹出层\n      lastRawResponse: '',\n      // 新增：存储最后收到的原始响应\n      drawer: false,\n      //抽屉展示\n      direction: 'rtl',\n      //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      innerDrawer: false,\n      tableList: []\n    };\n  },\n  mounted() {\n    this.loadTables();\n    this.initMemoryId();\n    this.addWelcomeMessage();\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\"帮我生成一个销售额柱状图\", \"展示近六个月的销售趋势折线图\", \"按照区域统计销售量并生成饼图\", \"帮我做一个按产品类别的销量对比图\"];\n  },\n  updated() {\n    this.scrollToBottom();\n  },\n  methods: {\n    SelectDataList() {\n      this.loadTables();\n      this.drawer = true;\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id');\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4());\n        localStorage.setItem('user_memory_id', storedMemoryId);\n      }\n      this.memoryId = storedMemoryId;\n    },\n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0;\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i];\n        number = number * 16 + (parseInt(hexValue, 16) || 0);\n      }\n      return number % 1000000;\n    },\n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      });\n    },\n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight;\n      }\n    },\n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables();\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data;\n          this.datasets = this.flattenDatasets(this.tables);\n          this.filteredDatasets = this.datasets;\n        } else {\n          this.tables = [];\n          this.datasets = [];\n          this.filteredDatasets = [];\n        }\n      } catch (e) {\n        this.tables = [];\n        this.datasets = [];\n        this.filteredDatasets = [];\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = [];\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node);\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children));\n        }\n      }\n      return result;\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase();\n      if (!keyword) {\n        this.filteredDatasets = this.datasets;\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword));\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n\n    async showDatasetDetail(dataset) {\n      alert('showDatasetDetail called');\n      console.log('showDatasetDetail called', dataset);\n\n      // 检查 dataset 是否有效\n      if (!dataset || !dataset.id) {\n        alert('数据集对象无效或缺少ID');\n        console.error('无效的数据集对象:', dataset);\n        return;\n      }\n      try {\n        console.log('开始请求数据集详情, ID:', dataset.id);\n        const res = await dataApi.getDatasetDetail(dataset.id, false);\n        console.log('数据集详情响应:', res);\n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          this.currentDatasetDetail = detail;\n          console.log('设置 currentDatasetDetail:', detail);\n\n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || detail.data && detail.data.fields || [];\n          console.log('设置 datasetFields:', this.datasetFields);\n\n          // 数据内容\n          this.datasetData = detail.data && detail.data.data || [];\n          console.log('设置 datasetData:', this.datasetData);\n        } else {\n          console.error('API返回错误:', res.data);\n          alert('获取数据集详情失败: ' + (res.data ? res.data.msg || '未知错误' : '未知错误'));\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n        }\n\n        // 确保显示抽屉\n        this.dialogVisible = true;\n        console.log('设置 dialogVisible = true');\n      } catch (e) {\n        console.error('请求数据集详情出错:', e);\n        alert('请求数据集详情出错: ' + e.message);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n\n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedTable ? this.selectedTable.tableName : this.tables.length > 0 ? this.tables[0].tableName : \"未知数据集\";\n\n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      };\n      this.messages.push(userMsg);\n\n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      };\n      this.messages.push(botMsg);\n\n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1];\n\n      // 保存问题并清空输入框\n      const question = this.question;\n      this.question = '';\n      this.isSending = true;\n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`;\n\n        // 发送请求\n        await axios.post('http://localhost:8088/api/indicator/chat', {\n          memoryId: this.memoryId,\n          message\n        }, {\n          responseType: 'stream',\n          onDownloadProgress: e => {\n            const fullText = e.event.target.responseText; // 累积的完整文本\n            let newText = fullText.substring(lastMsg.content.length);\n            lastMsg.content += newText; // 增量更新\n            this.scrollToBottom(); // 实时滚动\n\n            // 保存原始响应\n            this.lastRawResponse = fullText;\n          }\n        });\n\n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false;\n        this.isSending = false;\n\n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error);\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。';\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      }\n    },\n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n\n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n\n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n\n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n\n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n\n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n\n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n\n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n\n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n\n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n\n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n\n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n\n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n\n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n\n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n\n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n\n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      this.messages.push(botMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n\n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n\n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [{\n            field: '类别1',\n            value: 100\n          }, {\n            field: '类别2',\n            value: 200\n          }, {\n            field: '类别3',\n            value: 150\n          }],\n          metrics: [{\n            name: '数值'\n          }]\n        }\n      };\n\n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n\n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [{\n              value: 1,\n              field: \"神朔\",\n              name: \"神朔\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"甘泉\",\n              name: \"甘泉\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"包神\",\n              name: \"包神\",\n              category: \"记录数*\"\n            }],\n            fields: [{\n              id: \"1746787308487\",\n              name: \"名称\",\n              groupType: \"d\"\n            }, {\n              id: \"-1\",\n              name: \"记录数*\",\n              groupType: \"q\"\n            }]\n          }\n        }\n      };\n\n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n\n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n\n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n\n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n\n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n\n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content.replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '').replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '').trim();\n\n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n\n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n\n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n\n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n\n        // 根据图表类型选择对应的处理函数\n        switch (chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n\n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n\n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n\n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2,\n          // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n\n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = canvas.height * imgWidth / canvas.width;\n\n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n\n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n\n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n\n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n\n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n\n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n\n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n\n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n\n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n\n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n\n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      return options;\n    },\n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n\n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n\n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n\n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n\n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n\n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n\n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n\n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n\n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "v4", "uuidv4", "axios", "ChartDisplay", "html2canvas", "jsPDF", "echarts", "name", "components", "data", "description", "tablename", "tables", "datasets", "filteredDatasets", "searchKeyword", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "tableIndicators", "dialogVisible", "messages", "memoryId", "isSending", "messageListRef", "showRawResponsePanel", "lastRawResponse", "drawer", "direction", "currentDatasetDetail", "datasetFields", "datasetData", "innerDrawer", "tableList", "mounted", "loadTables", "initMemoryId", "addWelcomeMessage", "updated", "scrollToBottom", "methods", "SelectDataList", "storedMemoryId", "localStorage", "getItem", "uuidToNumber", "setItem", "uuid", "number", "i", "length", "hexValue", "parseInt", "push", "isUser", "content", "isTyping", "$refs", "scrollTop", "scrollHeight", "res", "getAllTables", "code", "flattenDatasets", "e", "tree", "result", "node", "leaf", "children", "concat", "onSearchDataset", "keyword", "trim", "toLowerCase", "filter", "ds", "includes", "showDatasetDetail", "dataset", "alert", "console", "log", "id", "error", "getDatasetDetail", "detail", "allFields", "fields", "msg", "message", "showSuggestions", "useQuestion", "q", "getTableFields", "table", "tableCode", "submitQuestion", "$message", "warning", "currentDataset", "tableName", "userMsg", "botMsg", "lastMsg", "post", "responseType", "onDownloadProgress", "fullText", "event", "target", "responseText", "newText", "substring", "parseChartConfig", "chartDataIdMatch", "match", "chartDataId", "fetchChartDataById", "chartConfigMatch", "chartConfig", "JSON", "parse", "type", "datasetId", "tableId", "replace", "jsonRegex", "jsonMatch", "jsonStr", "parseError", "defaultConfig", "Date", "now", "toString", "title", "response", "get", "status", "$forceUpdate", "test<PERSON>hart", "testChartConfig", "stringify", "testApiResponse", "field", "value", "metrics", "json<PERSON><PERSON><PERSON>", "apiResponseMsg", "testRealData", "realData", "category", "groupType", "realDataMsg", "showRawResponse", "exportToPDF", "$set", "tempContainer", "document", "createElement", "style", "position", "left", "width", "background", "padding", "body", "append<PERSON><PERSON><PERSON>", "datasetName", "currentDate", "toLocaleString", "tempDiv", "innerHTML", "textContent", "innerText", "chartContainer", "querySelector", "chart", "init", "options", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "setOption", "Promise", "resolve", "setTimeout", "dataTableContainer", "renderDataTable", "canvas", "scale", "useCORS", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "imgData", "toDataURL", "pdf", "orientation", "unit", "format", "imgWidth", "imgHeight", "height", "addImage", "save", "getTime", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "success", "createBasicChartOptions", "categories", "map", "item", "text", "tooltip", "trigger", "xAxis", "yAxis", "series", "seriesMap", "for<PERSON>ach", "s", "Array", "fill", "index", "Object", "values", "chartData", "isArray", "f", "xAxisData", "categoriesSet", "Set", "add", "from", "seriesData", "find", "axisPointer", "legend", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "container", "headers", "firstItem", "tableHTML", "header", "undefined"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <pre>{{ datasets }}</pre>\n        <button @click=\"showDatasetDetail({id: 'manual-test'})\">测试事件</button>\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n        </div>\n        \n        <div class=\"data-selection\">\n          <h3>目前可用数据</h3>\n          <div class=\"data-sets\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"8\" v-for=\"(table, idx) in datasets.slice(0, 3)\" :key=\"table.id + '_' + idx\">\n                <el-card class=\"data-card\" @click=\"showDatasetDetail(table)\">\n                  <div style=\"height:100%;background:rgba(255,0,0,0.1);padding:5px;\">\n                  <div class=\"data-header\">\n                    <span class=\"sample-tag\">样例</span>\n                    <span class=\"data-title\">{{ table.name }}</span>\n                    \n                    <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n                  </div>\n                  <div class=\"data-fields\">\n                    <el-tag\n                      v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])\"\n                      :key=\"field.id || idx\"\n                      size=\"mini\"\n                      type=\"info\"\n                      style=\"margin-right: 4px; margin-bottom: 4px;\"\n                    >\n                      {{ field.name || field }}\n                    </el-tag>\n                    <span v-if=\"table.fields && table.fields.length > 4\">...</span>\n                  </div>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        \n        <!-- 聊天消息列表区域 -->\n        <div class=\"message-list\" ref=\"messageListRef\">\n          <div\n            v-for=\"(message, index) in messages\"\n            :key=\"index\"\n            :class=\"message.isUser ? 'message user-message' : 'message bot-message'\"\n          >\n            <!-- 聊天图标 -->\n            <div class=\"avatar-container\" v-if=\"!message.isUser\">\n              <div class=\"bot-avatar\">\n                <i class=\"el-icon-s-tools\"></i>\n              </div>\n            </div>\n            <!-- 消息内容 -->\n            <div class=\"message-content\">\n              <div v-html=\"message.content\"></div>\n              <!-- 如果消息中包含图表配置，则显示图表和导出按钮 -->\n              <div v-if=\"message.chartConfig\" class=\"chart-container\">\n                <div class=\"chart-actions\">\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-download\" \n                            @click=\"exportToPDF(message)\" :loading=\"message.exporting\">\n                    导出PDF\n                  </el-button>\n                </div>\n                <chart-display :chart-config=\"message.chartConfig\" ref=\"chartDisplay\"></chart-display>\n              </div>\n              <!-- loading动画 -->\n              <span\n                class=\"loading-dots\"\n                v-if=\"message.isTyping\"\n              >\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n              </span>\n            </div>\n            <div class=\"avatar-container\" v-if=\"message.isUser\">\n              <div class=\"user-avatar\">\n                <i class=\"el-icon-user\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- 底部问题输入区域（移至message-list下方） -->\n        <div class=\"question-input-container\">\n          <span>👋直接问我问题，或在上方选择一个主题/数据开始！</span>\n          <div class=\"question-input-wrapper\">\n            <el-button type=\"text\" style=\"margin-left: 10px;\"  @click=\"SelectDataList\">选择数据</el-button>\n            <el-input \n              style=\"margin-bottom: 12px;width: 800px;\"\n              v-model=\"question\" \n              placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n              class=\"question-input\"\n              @keyup.enter.native=\"submitQuestion\"\n              :disabled=\"isSending\">\n            </el-input>\n            <div class=\"input-actions\">\n              <button class=\"action-btn\" @click=\"showSuggestions\">\n                <i class=\"el-icon-magic-stick\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testChart\" title=\"测试图表功能\">\n                <i class=\"el-icon-data-line\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testRealData\" title=\"测试实际数据\">\n                <i class=\"el-icon-s-data\"></i>\n              </button>\n              <button class=\"action-btn debug-btn\" @click=\"showRawResponse\" title=\"显示AI原始响应\">\n                <i class=\"el-icon-monitor\"></i>\n              </button>\n              <button class=\"action-btn send-btn\" @click=\"submitQuestion\" :disabled=\"isSending\">\n                <i class=\"el-icon-position\"></i>\n              </button>\n            </div>\n          </div>\n          \n          <!-- 建议问题弹出层 -->\n          <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n            <div class=\"suggestions-title\">\n              <i class=\"el-icon-s-promotion\"></i> 官方推荐\n            </div>\n            <div class=\"suggestions-list\">\n              <div \n                v-for=\"(suggestion, index) in suggestedQuestions\" \n                :key=\"index\"\n                class=\"suggestion-item\"\n                @click=\"useQuestion(suggestion)\">\n                {{ suggestion }}\n              </div>\n            </div>\n          </div>\n          \n          <!-- AI原始响应弹出层 -->\n          <div v-if=\"showRawResponsePanel\" class=\"raw-response-panel\">\n            <div class=\"raw-response-title\">\n              <i class=\"el-icon-monitor\"></i> AI原始响应\n              <button class=\"close-btn\" @click=\"showRawResponsePanel = false\">\n                <i class=\"el-icon-close\"></i>\n              </button>\n            </div>\n            <pre class=\"raw-response-content\">{{ lastRawResponse }}</pre>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <el-drawer\n      title=\"数据详情\"\n      :visible.sync=\"dialogVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <div style=\"padding: 20px\" v-if=\"currentDatasetDetail\">\n        <h3>{{currentDatasetDetail.name}}</h3>\n        <el-divider></el-divider>\n        \n        <h4>字段信息</h4>\n        <el-table :data=\"datasetFields\" style=\"width: 100%\">\n          <el-table-column prop=\"name\" label=\"字段名称\"></el-table-column>\n          <el-table-column prop=\"type\" label=\"字段类型\"></el-table-column>\n          <el-table-column prop=\"groupType\" label=\"分组类型\"></el-table-column>\n        </el-table>\n        \n        <el-divider></el-divider>\n        \n        <h4>数据预览</h4>\n        <el-table :data=\"datasetData\" style=\"width: 100%\">\n          <el-table-column \n            v-for=\"field in datasetFields\" \n            :key=\"field.id || field.name\"\n            :prop=\"field.dataeaseName || field.name\"\n            :label=\"field.name\">\n          </el-table-column>\n        </el-table>\n        <pre>{{ datasetData }}</pre>\n      </div>\n    </el-drawer>\n    <el-drawer\n      title=\"数据集列表\"\n      :visible.sync=\"drawer\"\n      direction=\"rtl\"\n      size=\"45%\">\n      <div style=\"padding: 20px\">\n        <el-input\n          v-model=\"searchKeyword\"\n          placeholder=\"搜索数据集\"\n          @input=\"onSearchDataset\"\n          style=\"margin-bottom: 16px; width: 300px;\"\n        />\n        <el-row :gutter=\"20\">\n          <el-col :span=\"8\" v-for=\"(table, idx) in filteredDatasets\" :key=\"table.id + '_' + idx\">\n            <el-card class=\"data-card\" @click=\"showDatasetDetail(table)\">\n              <div class=\"data-header\">\n                <span class=\"sample-tag\">样例</span>\n                <span class=\"data-title\">{{ table.id }}</span>\n                <span class=\"data-title\">{{ table.name }}</span>\n                <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n          </div>\n              <div class=\"data-fields\">\n                <el-tag\n                  v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])\"\n                  :key=\"field.id || idx\"\n                  size=\"mini\"\n                  type=\"info\"\n                  style=\"margin-right: 4px; margin-bottom: 4px;\"\n                >\n                  {{ field.name || field }}\n                </el-tag>\n                <span v-if=\"table.fields && table.fields.length > 4\">...</span>\n          </div>\n        </el-card>\n          </el-col>\n        </el-row>\n      </div>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api/index.js'\nimport { v4 as uuidv4 } from 'uuid'\nimport axios from 'axios'\nimport ChartDisplay from './components/ChartDisplay.vue'\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas'\nimport { jsPDF } from 'jspdf'\nimport * as echarts from 'echarts'\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [], // 原始树结构\n      datasets: [], // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [], // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false, // 新增：控制原始响应弹出层\n      lastRawResponse: '', // 新增：存储最后收到的原始响应\n      drawer:false,    //抽屉展示\n      direction: 'rtl', //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      innerDrawer:false,\n      tableList:[]\n    }\n  },\n  \n  mounted() {\n    this.loadTables()\n    this.initMemoryId()\n    this.addWelcomeMessage()\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\n      \"帮我生成一个销售额柱状图\",\n      \"展示近六个月的销售趋势折线图\",\n      \"按照区域统计销售量并生成饼图\",\n      \"帮我做一个按产品类别的销量对比图\"\n    ]\n  },\n  updated() {\n    this.scrollToBottom()\n  },\n  methods: {\n    SelectDataList(){\n      this.loadTables()\n      this.drawer=true\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id')\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4())\n        localStorage.setItem('user_memory_id', storedMemoryId)\n      }\n      this.memoryId = storedMemoryId\n    },\n    \n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i]\n        number = number * 16 + (parseInt(hexValue, 16) || 0)\n      }\n      return number % 1000000\n    },\n    \n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      })\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight\n      }\n    },\n    \n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables()\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data\n          this.datasets = this.flattenDatasets(this.tables)\n          this.filteredDatasets = this.datasets\n        } else {\n          this.tables = []\n          this.datasets = []\n          this.filteredDatasets = []\n        }\n      } catch (e) {\n        this.tables = []\n        this.datasets = []\n        this.filteredDatasets = []\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = []\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node)\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children))\n        }\n      }\n      return result\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase()\n      if (!keyword) {\n        this.filteredDatasets = this.datasets\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword))\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n    \n    async showDatasetDetail(dataset) {\n      alert('showDatasetDetail called');\n      console.log('showDatasetDetail called', dataset);\n      \n      // 检查 dataset 是否有效\n      if (!dataset || !dataset.id) {\n        alert('数据集对象无效或缺少ID');\n        console.error('无效的数据集对象:', dataset);\n        return;\n      }\n      \n      try {\n        console.log('开始请求数据集详情, ID:', dataset.id);\n        const res = await dataApi.getDatasetDetail(dataset.id, false);\n        console.log('数据集详情响应:', res);\n        \n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          this.currentDatasetDetail = detail;\n          console.log('设置 currentDatasetDetail:', detail);\n          \n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || (detail.data && detail.data.fields) || [];\n          console.log('设置 datasetFields:', this.datasetFields);\n          \n          // 数据内容\n          this.datasetData = (detail.data && detail.data.data) || [];\n          console.log('设置 datasetData:', this.datasetData);\n          \n        } else {\n          console.error('API返回错误:', res.data);\n          alert('获取数据集详情失败: ' + (res.data ? res.data.msg || '未知错误' : '未知错误'));\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n        }\n        \n        // 确保显示抽屉\n        this.dialogVisible = true;\n        console.log('设置 dialogVisible = true');\n        \n      } catch (e) {\n        console.error('请求数据集详情出错:', e);\n        alert('请求数据集详情出错: ' + e.message);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    \n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedTable ? \n        this.selectedTable.tableName : \n        (this.tables.length > 0 ? this.tables[0].tableName : \"未知数据集\")\n      \n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      }\n      this.messages.push(userMsg)\n      \n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      }\n      this.messages.push(botMsg)\n      \n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1]\n      \n      // 保存问题并清空输入框\n      const question = this.question\n      this.question = ''\n      this.isSending = true\n      \n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`\n        \n        // 发送请求\n        await axios.post(\n          'http://localhost:8088/api/indicator/chat',\n          { memoryId: this.memoryId, message },\n          {\n            responseType: 'stream',\n            onDownloadProgress: (e) => {\n              const fullText = e.event.target.responseText // 累积的完整文本\n              let newText = fullText.substring(lastMsg.content.length)\n              lastMsg.content += newText // 增量更新\n              this.scrollToBottom() // 实时滚动\n              \n              // 保存原始响应\n              this.lastRawResponse = fullText\n            }\n          }\n        )\n        \n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false\n        this.isSending = false\n        \n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error)\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'\n        lastMsg.isTyping = false\n        this.isSending = false\n      }\n    },\n    \n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n      \n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n        \n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n      \n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        \n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n          \n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n          \n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n            \n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, \n              '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n            \n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      \n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n      \n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        \n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n          \n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          \n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            \n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n              \n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], \n                  '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n      \n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || \n          message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n        \n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        \n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    \n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        \n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n          \n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n          \n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n          \n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n          \n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    \n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\"\n      };\n      \n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig\n      };\n      \n      this.messages.push(botMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n      \n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n      \n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [\n            { field: '类别1', value: 100 },\n            { field: '类别2', value: 200 },\n            { field: '类别3', value: 150 }\n          ],\n          metrics: [{ name: '数值' }]\n        }\n      };\n      \n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      \n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      \n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    \n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n      \n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [\n              {value: 1, field: \"神朔\", name: \"神朔\", category: \"记录数*\"},\n              {value: 1, field: \"甘泉\", name: \"甘泉\", category: \"记录数*\"},\n              {value: 1, field: \"包神\", name: \"包神\", category: \"记录数*\"}\n            ],\n            fields: [\n              {id: \"1746787308487\", name: \"名称\", groupType: \"d\"},\n              {id: \"-1\", name: \"记录数*\", groupType: \"q\"}\n            ]\n          }\n        }\n      };\n      \n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n      \n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n      \n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      \n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      \n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n        \n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n        \n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n        \n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content\n          .replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '')\n          .replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '')\n          .trim();\n          \n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n        \n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n        \n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n        \n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n        \n        // 根据图表类型选择对应的处理函数\n        switch(chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n        \n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n        \n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        \n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n        \n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2, // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n        \n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = (canvas.height * imgWidth) / canvas.width;\n        \n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n        \n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n        \n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n        \n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n        \n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n        \n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    \n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n      \n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n      \n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n      \n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n          \n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n          \n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n          \n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      \n      return options;\n    },\n    \n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      \n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n      \n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        \n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n      \n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n      \n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n      \n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n        \n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n        \n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        \n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n      \n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.app-layout {\n  display: flex;\n  height: 100vh; /* 占满整个视口高度 */\n  overflow: hidden;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  background-color: #f9f9f9;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 25px;\n  text-align: center;\n  white-space: nowrap;\n  letter-spacing: 2px;\n  font-weight: 900;\n}\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  /* overflow-x: auto; */\n  display: flex;\n  flex-direction: column; /* 垂直排列子元素 */\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n  height: 100vh; /* 占满整个视口高度 */\n  box-sizing: border-box; /* 包含padding和border */\n}\n\n.header {\n  margin-bottom: 20px;\n  width: 100%; /* 撑满父容器宽度 */\n   text-align: center; /* 让文本内容居中 */\n}\n\n.header h2 {\n  margin: 0;\n  font-size: 30px;\n}\n\n.highlight {\n  color: #409eff;\n}\n\n.sub-title {\n  color: #606266;\n  font-size: 14px;\n  margin: 5px 0 0 0;\n}\n\n.data-card {\n  cursor: pointer;\n  margin-bottom: 15px;\n  transition: all 0.3s;\n  width:200px;\n  height:100px;\n}\n\n.data-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.data-header {\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.sample-tag {\n  background-color: #ecf5ff;\n  color: #409eff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n.data-title {\n  margin-left: 5px;\n  margin-right: 5px;\n  font-weight: bold;\n}\n\n.common-tag {\n  background-color: #f5f7fa;\n  color: #909399;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-left: 5px;\n}\n\n.data-fields {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.field-item {\n  background-color: #f5f7fa;\n  padding: 2px 8px;\n  margin: 4px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.multi-line-input .el-input__inner {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  height: auto !important;\n  line-height: inherit;\n  padding: 6px 10px;\n  min-height: 40px;\n}\n\n/* 底部问题输入区域（关键修改） */\n.question-input-container {\n  /* 移除固定定位 */\n  /* position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%; */\n  \n  width: 100%;\n    /* background-color: #fff; */\n    /* border-top: 1px solid #ebeef5; */\n    padding: 25px 15px;\n    /* box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05); */\n    display: flex\n;\n    flex-direction: column;\n    align-items: center;\n    margin-top: 20px;\n}\n\n.question-input-container > span {\n  margin-bottom: 12px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.question-input-wrapper {\n  align-items: center;\n  width: 100%;\n  max-width: 800px;\n  margin: 0 auto;\n  background-color: #f5f7fa;\n  border-radius: 20px;\n  padding: 8px 15px;\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n  float: right;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: #fff;\n  border: 1px solid #dcdfe6;\n  margin-left: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n}\n\n.send-btn {\n  background-color: #409eff;\n  color: white;\n  border: none;\n}\n\n.test-btn {\n  background-color: #67c23a;\n  color: white;\n  border: none;\n}\n\n.debug-btn {\n  background-color: #909399;\n  color: white;\n  border: none;\n}\n\n.send-btn:disabled {\n  background-color: #c0c4cc;\n  cursor: not-allowed;\n}\n\n/* Suggested Questions Panel */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n\n/* Message list style */\n.message-list {\n  width: 100%;\n  max-height: calc(100vh - 250px); /* 修改为响应式高度，减去头部和底部的高度 */\n  overflow-y: auto;\n  margin-top: 20px;\n  padding: 10px;\n  background-color: transparent;\n  border-radius: 8px;\n  flex: 1; /* 让消息列表占据剩余空间 */\n}\n\n.message {\n  margin-bottom: 20px;\n  display: flex;\n  position: relative;\n  max-width: 80%;\n  margin-left: 150px;\n}\n\n.message-content {\n  padding: 12px 15px;\n  border-radius: 6px;\n  line-height: 1.5;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n  align-self: flex-end;\n  margin-left: auto;\n}\n\n.user-message .message-content {\n  background-color: #ecf5ff;\n  margin-right: 10px;\n}\n\n.bot-message {\n  align-self: flex-start;\n}\n\n.bot-message .message-content {\n  background-color: #f5f7fa;\n  margin-left: 10px;\n}\n\n.avatar-container {\n  display: flex;\n  align-items: flex-start;\n}\n\n.bot-avatar, .user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bot-avatar {\n  background-color: #4caf50;\n  color: white;\n}\n\n.user-avatar {\n  background-color: #2196f3;\n  color: white;\n}\n\n/* Loading animation */\n.loading-dots {\n  display: inline-block;\n}\n\n.dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  background-color: #999;\n  border-radius: 50%;\n  margin: 0 2px;\n  animation: pulse 1.2s infinite ease-in-out both;\n}\n\n.dot:nth-child(2) {\n  animation-delay: -0.4s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: -0.8s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(0.6);\n    opacity: 0.4;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* Styles related to charts */\n.chart-container {\n  position: relative;\n  margin-top: 15px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 10px;\n  background-color: #fff;\n}\n\n/* 确保图表容器有足够高度 */\n.chart-canvas {\n  min-height: 300px;\n}\n\n.chart-notice {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n  font-weight: bold;\n}\n\n/* AI original response popup layer style */\n.raw-response-panel {\n  position: fixed;\n  bottom: 100px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 800px;\n  background-color: #fff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n  z-index: 1000;\n  max-height: 60%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.raw-response-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 16px;\n  color: #303133;\n  background-color: #f5f7fa;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.raw-response-title .el-icon-monitor {\n  margin-right: 8px;\n}\n\n.raw-response-title .close-btn {\n  background-color: #f5f7fa;\n  border: 1px solid #ebeef5;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.raw-response-title .close-btn:hover {\n  background-color: #ebeef5;\n}\n\n.raw-response-content {\n  flex: 1;\n  padding: 15px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #303133;\n  white-space: pre-wrap;\n  word-break: break-all;\n  overflow-wrap: break-word;\n}\n\n/* 添加PDF导出相关样式 */\n.chart-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-bottom: 10px;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;AAkPA,SAAAA,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,YAAA;AACA;AACA,OAAAC,WAAA;AACA,SAAAC,KAAA;AACA,YAAAC,OAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MAAA;MACAC,QAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;MACAC,oBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,MAAA;MAAA;MACAC,SAAA;MAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,WAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,UAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;IACA;IACA,KAAApB,kBAAA,IACA,gBACA,kBACA,kBACA,mBACA;EACA;EACAqB,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAC,eAAA;MACA,KAAAN,UAAA;MACA,KAAAR,MAAA;IACA;IACA;IACAS,aAAA;MACA,IAAAM,cAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,KAAAF,cAAA;QACAA,cAAA,QAAAG,YAAA,CAAA/C,MAAA;QACA6C,YAAA,CAAAG,OAAA,mBAAAJ,cAAA;MACA;MACA,KAAApB,QAAA,GAAAoB,cAAA;IACA;IAEA;IACAG,aAAAE,IAAA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAG,MAAA,IAAAD,CAAA,MAAAA,CAAA;QACA,MAAAE,QAAA,GAAAJ,IAAA,CAAAE,CAAA;QACAD,MAAA,GAAAA,MAAA,SAAAI,QAAA,CAAAD,QAAA;MACA;MACA,OAAAH,MAAA;IACA;IAEA;IACAX,kBAAA;MACA,KAAAhB,QAAA,CAAAgC,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IAEA;IACAjB,eAAA;MACA,SAAAkB,KAAA,CAAAjC,cAAA;QACA,KAAAiC,KAAA,CAAAjC,cAAA,CAAAkC,SAAA,QAAAD,KAAA,CAAAjC,cAAA,CAAAmC,YAAA;MACA;IACA;IAEA,MAAAxB,WAAA;MACA;QACA,MAAAyB,GAAA,SAAAhE,OAAA,CAAAiE,YAAA;QACA,IAAAD,GAAA,CAAAtD,IAAA,IAAAsD,GAAA,CAAAtD,IAAA,CAAAwD,IAAA;UACA,KAAArD,MAAA,GAAAmD,GAAA,CAAAtD,IAAA,CAAAA,IAAA;UACA,KAAAI,QAAA,QAAAqD,eAAA,MAAAtD,MAAA;UACA,KAAAE,gBAAA,QAAAD,QAAA;QACA;UACA,KAAAD,MAAA;UACA,KAAAC,QAAA;UACA,KAAAC,gBAAA;QACA;MACA,SAAAqD,CAAA;QACA,KAAAvD,MAAA;QACA,KAAAC,QAAA;QACA,KAAAC,gBAAA;MACA;IACA;IACA;IACAoD,gBAAAE,IAAA;MACA,IAAAC,MAAA;MACA,WAAAC,IAAA,IAAAF,IAAA;QACA,IAAAE,IAAA,CAAAC,IAAA;UACAF,MAAA,CAAAb,IAAA,CAAAc,IAAA;QACA,WAAAA,IAAA,CAAAE,QAAA,IAAAF,IAAA,CAAAE,QAAA,CAAAnB,MAAA;UACAgB,MAAA,GAAAA,MAAA,CAAAI,MAAA,MAAAP,eAAA,CAAAI,IAAA,CAAAE,QAAA;QACA;MACA;MACA,OAAAH,MAAA;IACA;IACA;IACAK,gBAAA;MACA,MAAAC,OAAA,QAAA5D,aAAA,CAAA6D,IAAA,GAAAC,WAAA;MACA,KAAAF,OAAA;QACA,KAAA7D,gBAAA,QAAAD,QAAA;MACA;QACA,KAAAC,gBAAA,QAAAD,QAAA,CAAAiE,MAAA,CAAAC,EAAA,IAAAA,EAAA,CAAAxE,IAAA,IAAAwE,EAAA,CAAAxE,IAAA,CAAAsE,WAAA,GAAAG,QAAA,CAAAL,OAAA;MACA;IACA;IACA;IACA;IACA;IACA;;IAEA,MAAAM,kBAAAC,OAAA;MACAC,KAAA;MACAC,OAAA,CAAAC,GAAA,6BAAAH,OAAA;;MAEA;MACA,KAAAA,OAAA,KAAAA,OAAA,CAAAI,EAAA;QACAH,KAAA;QACAC,OAAA,CAAAG,KAAA,cAAAL,OAAA;QACA;MACA;MAEA;QACAE,OAAA,CAAAC,GAAA,mBAAAH,OAAA,CAAAI,EAAA;QACA,MAAAvB,GAAA,SAAAhE,OAAA,CAAAyF,gBAAA,CAAAN,OAAA,CAAAI,EAAA;QACAF,OAAA,CAAAC,GAAA,aAAAtB,GAAA;QAEA,IAAAA,GAAA,CAAAtD,IAAA,IAAAsD,GAAA,CAAAtD,IAAA,CAAAwD,IAAA;UACA,MAAAwB,MAAA,GAAA1B,GAAA,CAAAtD,IAAA,CAAAA,IAAA;UACA,KAAAuB,oBAAA,GAAAyD,MAAA;UACAL,OAAA,CAAAC,GAAA,6BAAAI,MAAA;;UAEA;UACA,KAAAxD,aAAA,GAAAwD,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAhF,IAAA,IAAAgF,MAAA,CAAAhF,IAAA,CAAAkF,MAAA;UACAP,OAAA,CAAAC,GAAA,2BAAApD,aAAA;;UAEA;UACA,KAAAC,WAAA,GAAAuD,MAAA,CAAAhF,IAAA,IAAAgF,MAAA,CAAAhF,IAAA,CAAAA,IAAA;UACA2E,OAAA,CAAAC,GAAA,yBAAAnD,WAAA;QAEA;UACAkD,OAAA,CAAAG,KAAA,aAAAxB,GAAA,CAAAtD,IAAA;UACA0E,KAAA,kBAAApB,GAAA,CAAAtD,IAAA,GAAAsD,GAAA,CAAAtD,IAAA,CAAAmF,GAAA;UACA,KAAA5D,oBAAA;UACA,KAAAC,aAAA;UACA,KAAAC,WAAA;QACA;;QAEA;QACA,KAAAX,aAAA;QACA6D,OAAA,CAAAC,GAAA;MAEA,SAAAlB,CAAA;QACAiB,OAAA,CAAAG,KAAA,eAAApB,CAAA;QACAgB,KAAA,iBAAAhB,CAAA,CAAA0B,OAAA;QACA,KAAA7D,oBAAA;QACA,KAAAC,aAAA;QACA,KAAAC,WAAA;QACA,KAAAX,aAAA;MACA;IACA;IACAuE,gBAAA;MACA,KAAA3E,oBAAA,SAAAA,oBAAA;IACA;IACA4E,YAAAC,CAAA;MACA,KAAA/E,QAAA,GAAA+E,CAAA;MACA,KAAA7E,oBAAA;IACA;IACA8E,eAAAC,KAAA;MACA,IAAAA,KAAA,CAAAC,SAAA;QACA;MACA;MACA;IACA;IAEA;IACA,MAAAC,eAAA;MACA,UAAAnF,QAAA,CAAA2D,IAAA,WAAAlD,SAAA;QACA,KAAA2E,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAC,cAAA,QAAAvF,aAAA,GACA,KAAAA,aAAA,CAAAwF,SAAA,GACA,KAAA5F,MAAA,CAAAyC,MAAA,YAAAzC,MAAA,IAAA4F,SAAA;;MAEA;MACA,MAAAC,OAAA;QACAhD,MAAA;QACAC,OAAA,OAAAzC,QAAA,CAAA2D,IAAA;QACAjB,QAAA;MACA;MACA,KAAAnC,QAAA,CAAAgC,IAAA,CAAAiD,OAAA;;MAEA;MACA,MAAAC,MAAA;QACAjD,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAAnC,QAAA,CAAAgC,IAAA,CAAAkD,MAAA;;MAEA;MACA,MAAAC,OAAA,QAAAnF,QAAA,MAAAA,QAAA,CAAA6B,MAAA;;MAEA;MACA,MAAApC,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA;MACA,KAAAS,SAAA;MAEA;QACA;QACA,MAAAmE,OAAA,MAAA5E,QAAA,WAAAsF,cAAA;;QAEA;QACA,MAAArG,KAAA,CAAA0G,IAAA,CACA,4CACA;UAAAnF,QAAA,OAAAA,QAAA;UAAAoE;QAAA,GACA;UACAgB,YAAA;UACAC,kBAAA,EAAA3C,CAAA;YACA,MAAA4C,QAAA,GAAA5C,CAAA,CAAA6C,KAAA,CAAAC,MAAA,CAAAC,YAAA;YACA,IAAAC,OAAA,GAAAJ,QAAA,CAAAK,SAAA,CAAAT,OAAA,CAAAjD,OAAA,CAAAL,MAAA;YACAsD,OAAA,CAAAjD,OAAA,IAAAyD,OAAA;YACA,KAAAzE,cAAA;;YAEA;YACA,KAAAb,eAAA,GAAAkF,QAAA;UACA;QACA,CACA;;QAEA;QACAJ,OAAA,CAAAhD,QAAA;QACA,KAAAjC,SAAA;;QAEA;QACA,KAAA2F,gBAAA,CAAAV,OAAA;MACA,SAAApB,KAAA;QACAH,OAAA,CAAAG,KAAA,UAAAA,KAAA;QACAoB,OAAA,CAAAjD,OAAA;QACAiD,OAAA,CAAAhD,QAAA;QACA,KAAAjC,SAAA;MACA;IACA;IAEA;IACA2F,iBAAAxB,OAAA;MACAT,OAAA,CAAAC,GAAA,qBAAAQ,OAAA,CAAAnC,OAAA;;MAEA;MACA,MAAA4D,gBAAA,GAAAzB,OAAA,CAAAnC,OAAA,CAAA6D,KAAA;MACA,IAAAD,gBAAA,IAAAA,gBAAA;QACA,MAAAE,WAAA,GAAAF,gBAAA;QACAlC,OAAA,CAAAC,GAAA,cAAAmC,WAAA;;QAEA;QACA,KAAAC,kBAAA,CAAAD,WAAA,EAAA3B,OAAA;QACA;MACA;;MAEA;MACA,MAAA6B,gBAAA,GAAA7B,OAAA,CAAAnC,OAAA,CAAA6D,KAAA;MACA,IAAAG,gBAAA,IAAAA,gBAAA;QACAtC,OAAA,CAAAC,GAAA,eAAAqC,gBAAA;QAEA;UACA,IAAAC,WAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,gBAAA;UACAtC,OAAA,CAAAC,GAAA,gBAAAsC,WAAA;;UAEA;UACA,IAAAA,WAAA,CAAA1D,IAAA,UAAA0D,WAAA,CAAAlH,IAAA;YACA2E,OAAA,CAAAC,GAAA,yBAAAsC,WAAA,CAAAlH,IAAA;YACAkH,WAAA,GAAAA,WAAA,CAAAlH,IAAA;UACA;;UAEA;UACA,IAAAkH,WAAA,CAAAG,IAAA,KAAAH,WAAA,CAAAI,SAAA,IAAAJ,WAAA,CAAAK,OAAA,IAAAL,WAAA,CAAAlH,IAAA;YACA2E,OAAA,CAAAC,GAAA,eAAAsC,WAAA;YACA9B,OAAA,CAAA8B,WAAA,GAAAA,WAAA;;YAEA;YACA9B,OAAA,CAAAnC,OAAA,GAAAmC,OAAA,CAAAnC,OAAA,CAAAuE,OAAA,6BACA;YAEA;UACA;YACA7C,OAAA,CAAAC,GAAA,gBAAAsC,WAAA;UACA;QACA,SAAApC,KAAA;UACAH,OAAA,CAAAG,KAAA,cAAAA,KAAA;QACA;MACA;MAEAH,OAAA,CAAAC,GAAA;;MAEA;MACA;QACA;QACA,MAAA6C,SAAA;QACA,MAAAC,SAAA,GAAAtC,OAAA,CAAAnC,OAAA,CAAA6D,KAAA,CAAAW,SAAA;QAEA,IAAAC,SAAA;UACA/C,OAAA,CAAAC,GAAA,kBAAA8C,SAAA;;UAEA;UACA;UACA,MAAAC,OAAA,GAAAD,SAAA,IAAAF,OAAA;UACA7C,OAAA,CAAAC,GAAA,iBAAA+C,OAAA;UAEA;YACA,MAAAT,WAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAO,OAAA;YACAhD,OAAA,CAAAC,GAAA,gBAAAsC,WAAA;YAEA,IAAAA,WAAA,CAAA1D,IAAA,UAAA0D,WAAA,CAAAlH,IAAA;cACA,MAAAA,IAAA,GAAAkH,WAAA,CAAAlH,IAAA;cACA2E,OAAA,CAAAC,GAAA,aAAA5E,IAAA;;cAEA;cACA,IAAAA,IAAA,CAAAqH,IAAA,KAAArH,IAAA,CAAAuH,OAAA,IAAAvH,IAAA,CAAAA,IAAA;gBACA2E,OAAA,CAAAC,GAAA,eAAA5E,IAAA;gBACAoF,OAAA,CAAA8B,WAAA,GAAAlH,IAAA;gBACAoF,OAAA,CAAAnC,OAAA,GAAAmC,OAAA,CAAAnC,OAAA,CAAAuE,OAAA,CAAAE,SAAA,KACA;gBACA;cACA;YACA;UACA,SAAAE,UAAA;YACAjD,OAAA,CAAAC,GAAA,cAAAgD,UAAA;UACA;QACA;MACA,SAAA9C,KAAA;QACAH,OAAA,CAAAC,GAAA,aAAAE,KAAA;MACA;;MAEA;MACA,IAAAM,OAAA,CAAAnC,OAAA,CAAAsB,QAAA,iBACAa,OAAA,CAAAnC,OAAA,CAAAsB,QAAA;QACAI,OAAA,CAAAC,GAAA;;QAEA;QACA,MAAAiD,aAAA;UACAhD,EAAA,EAAAiD,IAAA,CAAAC,GAAA,GAAAC,QAAA;UACAX,IAAA;UACAY,KAAA;UACAV,OAAA,OAAAhH,aAAA,EAAAsE,EAAA;QACA;QAEAF,OAAA,CAAAC,GAAA,eAAAiD,aAAA;QACAzC,OAAA,CAAA8B,WAAA,GAAAW,aAAA;MACA;IACA;IAEA;IACA,MAAAb,mBAAAD,WAAA,EAAA3B,OAAA;MACA;QACAT,OAAA,CAAAC,GAAA,qBAAAmC,WAAA;QACA,MAAAmB,QAAA,SAAAzI,KAAA,CAAA0I,GAAA,yCAAApB,WAAA;QAEA,IAAAmB,QAAA,CAAAlI,IAAA,IAAAkI,QAAA,CAAAE,MAAA;UACAzD,OAAA,CAAAC,GAAA,cAAAsD,QAAA,CAAAlI,IAAA;;UAEA;UACA,IAAAkH,WAAA,GAAAgB,QAAA,CAAAlI,IAAA;UACA,IAAAkH,WAAA,CAAA1D,IAAA,UAAA0D,WAAA,CAAAlH,IAAA;YACAkH,WAAA,GAAAA,WAAA,CAAAlH,IAAA;UACA;;UAEA;UACAoF,OAAA,CAAA8B,WAAA,GAAAA,WAAA;;UAEA;UACA,KAAA9B,OAAA,CAAAnC,OAAA,CAAAsB,QAAA;YACAa,OAAA,CAAAnC,OAAA;UACA;;UAEA;UACA,KAAAoF,YAAA;QACA;UACA1D,OAAA,CAAAG,KAAA,cAAAoD,QAAA;UACA9C,OAAA,CAAAnC,OAAA;QACA;MACA,SAAA6B,KAAA;QACAH,OAAA,CAAAG,KAAA,cAAAA,KAAA;QACAM,OAAA,CAAAnC,OAAA,0CAAA6B,KAAA,CAAAM,OAAA;MACA;IACA;IAEA;IACAkD,UAAA;MACA;MACA,MAAAC,eAAA;QACA1D,EAAA;QACAwC,IAAA;QACAY,KAAA;QACAX,SAAA;QACAC,OAAA;MACA;;MAEA;MACA,MAAAtB,MAAA;QACAjD,MAAA;QACAC,OAAA;QACAC,QAAA;QACAgE,WAAA,EAAAqB;MACA;MAEA,KAAAxH,QAAA,CAAAgC,IAAA,CAAAkD,MAAA;;MAEA;MACA,KAAA7E,eAAA,GAAA+F,IAAA,CAAAqB,SAAA,CAAAD,eAAA;;MAEA;MACA5D,OAAA,CAAAC,GAAA,iBAAA7D,QAAA;;MAEA;MACA,MAAA0H,eAAA;QACAjF,IAAA;QACA2B,GAAA;QACAnF,IAAA;UACAqH,IAAA;UACArH,IAAA,GACA;YAAA0I,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,GACA;YAAAD,KAAA;YAAAC,KAAA;UAAA,EACA;UACAC,OAAA;YAAA9I,IAAA;UAAA;QACA;MACA;;MAEA;MACA,MAAA+I,WAAA,iBAAA1B,IAAA,CAAAqB,SAAA,CAAAC,eAAA;MACA,MAAAK,cAAA;QACA9F,MAAA;QACAC,OAAA,gBAAA4F,WAAA;QACA3F,QAAA;MACA;MAEA,KAAAnC,QAAA,CAAAgC,IAAA,CAAA+F,cAAA;MACA,KAAAlC,gBAAA,CAAAkC,cAAA;;MAEA;MACA,KAAA1H,eAAA,GAAAyH,WAAA;MAEAlE,OAAA,CAAAC,GAAA,iBAAAkE,cAAA;IACA;IAEA;IACAC,aAAA;MACApE,OAAA,CAAAC,GAAA;;MAEA;MACA,MAAAoE,QAAA;QACAxF,IAAA;QACA2B,GAAA;QACAnF,IAAA;UACA6E,EAAA;UACAoD,KAAA;UACAV,OAAA;UACAF,IAAA;UACArH,IAAA;YACAA,IAAA,GACA;cAAA2I,KAAA;cAAAD,KAAA;cAAA5I,IAAA;cAAAmJ,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAD,KAAA;cAAA5I,IAAA;cAAAmJ,QAAA;YAAA,GACA;cAAAN,KAAA;cAAAD,KAAA;cAAA5I,IAAA;cAAAmJ,QAAA;YAAA,EACA;YACA/D,MAAA,GACA;cAAAL,EAAA;cAAA/E,IAAA;cAAAoJ,SAAA;YAAA,GACA;cAAArE,EAAA;cAAA/E,IAAA;cAAAoJ,SAAA;YAAA;UAEA;QACA;MACA;;MAEA;MACA,MAAAC,WAAA;QACAnG,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MAEA;MACAiG,WAAA,CAAAjC,WAAA,GAAA8B,QAAA,CAAAhJ,IAAA;;MAEA;MACA,KAAAe,QAAA,CAAAgC,IAAA,CAAAoG,WAAA;;MAEA;MACA,KAAA/H,eAAA,GAAA+F,IAAA,CAAAqB,SAAA,CAAAQ,QAAA;MAEArE,OAAA,CAAAC,GAAA,gBAAAuE,WAAA;IACA;IAEA;IACAC,gBAAA;MACA,KAAAjI,oBAAA;MACA,KAAAC,eAAA,QAAAL,QAAA,MAAAA,QAAA,CAAA6B,MAAA,MAAAK,OAAA;IACA;IAEA;IACA,MAAAoG,YAAAjE,OAAA;MACA,KAAAA,OAAA,CAAA8B,WAAA;MAEA;QACA;QACA,KAAAoC,IAAA,CAAAlE,OAAA;;QAEA;QACA,MAAAmE,aAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,aAAA,CAAAG,KAAA,CAAAC,QAAA;QACAJ,aAAA,CAAAG,KAAA,CAAAE,IAAA;QACAL,aAAA,CAAAG,KAAA,CAAAG,KAAA;QACAN,aAAA,CAAAG,KAAA,CAAAI,UAAA;QACAP,aAAA,CAAAG,KAAA,CAAAK,OAAA;QACAP,QAAA,CAAAQ,IAAA,CAAAC,WAAA,CAAAV,aAAA;;QAEA;QACA;QACA,MAAAtB,KAAA,GAAA7C,OAAA,CAAA8B,WAAA,CAAAe,KAAA;QACA,MAAAiC,WAAA,QAAA3J,aAAA,QAAAA,aAAA,CAAAwF,SAAA;QACA,MAAAoE,WAAA,OAAArC,IAAA,GAAAsC,cAAA;;QAEA;QACA,IAAAnK,WAAA,GAAAmF,OAAA,CAAAnC,OAAA,CACAuE,OAAA,6CACAA,OAAA,8CACArD,IAAA;;QAEA;QACA,IAAAlE,WAAA,CAAAsE,QAAA;UACA,MAAA8F,OAAA,GAAAb,QAAA,CAAAC,aAAA;UACAY,OAAA,CAAAC,SAAA,GAAArK,WAAA;UACAA,WAAA,GAAAoK,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;QACA;;QAEA;QACAjB,aAAA,CAAAe,SAAA;AACA;AACA,2DAAArC,KAAA;AACA,8DAAAiC,WAAA;AACA,+DAAAC,WAAA;AACA;;AAEA;AACA;AACA,mBAAAlK,WAAA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;QAEA;QACA,MAAAwK,cAAA,GAAAlB,aAAA,CAAAmB,aAAA;QACA,MAAAC,KAAA,GAAA9K,OAAA,CAAA+K,IAAA,CAAAH,cAAA;;QAEA;QACA,MAAAvD,WAAA,GAAA9B,OAAA,CAAA8B,WAAA;QACA,IAAA2D,OAAA;;QAEA;QACA,QAAA3D,WAAA,CAAAG,IAAA;UACA;YACAwD,OAAA,QAAAC,kBAAA,CAAA5D,WAAA;YACA;UACA;YACA2D,OAAA,QAAAE,mBAAA,CAAA7D,WAAA;YACA;UACA;YACA2D,OAAA,QAAAG,kBAAA,CAAA9D,WAAA;YACA;UACA;YACA2D,OAAA,QAAAI,uBAAA,CAAA/D,WAAA;YACA;UACA;YACA2D,OAAA,QAAAK,iBAAA,CAAAhE,WAAA;QACA;;QAEA;QACAyD,KAAA,CAAAQ,SAAA,CAAAN,OAAA;QACAlG,OAAA,CAAAC,GAAA,mBAAAiG,OAAA;;QAEA;QACA,UAAAO,OAAA,CAAAC,OAAA,IAAAC,UAAA,CAAAD,OAAA;;QAEA;QACA,MAAAE,kBAAA,GAAAhC,aAAA,CAAAmB,aAAA;QACA,KAAAc,eAAA,CAAAD,kBAAA,EAAAnG,OAAA,CAAA8B,WAAA;;QAEA;QACA,MAAAuE,MAAA,SAAA9L,WAAA,CAAA4J,aAAA;UACAmC,KAAA;UAAA;UACAC,OAAA;UACAC,UAAA;UACAC,eAAA;QACA;;QAEA;QACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;QACA,MAAAC,GAAA,OAAApM,KAAA;UACAqM,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,MAAAC,QAAA;QACA,MAAAC,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAA5B,KAAA;;QAEA;QACAmC,GAAA,CAAAO,QAAA,CAAAT,OAAA,iBAAAM,QAAA,EAAAC,SAAA;;QAEA;QACAL,GAAA,CAAAQ,IAAA,IAAAvE,KAAA,QAAAH,IAAA,GAAA2E,OAAA;;QAEA;QACAjD,QAAA,CAAAQ,IAAA,CAAA0C,WAAA,CAAAnD,aAAA;QACAoB,KAAA,CAAAgC,OAAA;;QAEA;QACA,KAAArD,IAAA,CAAAlE,OAAA;;QAEA;QACA,KAAAQ,QAAA,CAAAgH,OAAA;MAEA,SAAA9H,KAAA;QACAH,OAAA,CAAAG,KAAA,aAAAA,KAAA;QACA,KAAAwE,IAAA,CAAAlE,OAAA;QACA,KAAAQ,QAAA,CAAAd,KAAA,eAAAA,KAAA,CAAAM,OAAA;MACA;IACA;IAEA;IACAyH,wBAAA3F,WAAA;MACA,MAAAG,IAAA,GAAAH,WAAA,CAAAG,IAAA;MACA,IAAArH,IAAA;MACA,IAAA8M,UAAA;;MAEA;MACA,IAAA5F,WAAA,CAAAlH,IAAA,IAAAkH,WAAA,CAAAlH,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAkH,WAAA,CAAAlH,IAAA,CAAAA,IAAA;QACA8M,UAAA,GAAA9M,IAAA,CAAA+M,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAtE,KAAA,IAAAsE,IAAA,CAAAlN,IAAA;MACA;;MAEA;MACA,MAAA+K,OAAA;QACA5C,KAAA;UACAgF,IAAA,EAAA/F,WAAA,CAAAe,KAAA;QACA;QACAiF,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACA/F,IAAA;UACArH,IAAA,EAAA8M;QACA;QACAO,KAAA;UACAhG,IAAA;QACA;QACAiG,MAAA;MACA;;MAEA;MACA,IAAAtN,IAAA,CAAA4C,MAAA;QACA,IAAA5C,IAAA,IAAAsN,MAAA;UACA;UACA,MAAAC,SAAA;;UAEA;UACAvN,IAAA,CAAAwN,OAAA,CAAAR,IAAA;YACA,IAAAA,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACA,KAAAF,SAAA,CAAAE,CAAA,CAAAxE,QAAA;kBACAsE,SAAA,CAAAE,CAAA,CAAAxE,QAAA;oBACAnJ,IAAA,EAAA2N,CAAA,CAAAxE,QAAA;oBACA5B,IAAA,EAAAA,IAAA;oBACArH,IAAA,EAAA0N,KAAA,CAAAZ,UAAA,CAAAlK,MAAA,EAAA+K,IAAA;kBACA;gBACA;cACA;YACA;UACA;;UAEA;UACA3N,IAAA,CAAAwN,OAAA,EAAAR,IAAA,EAAAY,KAAA;YACA,IAAAZ,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACAF,SAAA,CAAAE,CAAA,CAAAxE,QAAA,EAAAjJ,IAAA,CAAA4N,KAAA,IAAAH,CAAA,CAAA9E,KAAA;cACA;YACA;UACA;;UAEA;UACAkC,OAAA,CAAAyC,MAAA,GAAAO,MAAA,CAAAC,MAAA,CAAAP,SAAA;QACA;UACA;UACA1C,OAAA,CAAAyC,MAAA,CAAAvK,IAAA;YACAjD,IAAA;YACAuH,IAAA,EAAAA,IAAA;YACArH,IAAA,EAAAA,IAAA,CAAA+M,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAArE,KAAA;UACA;QACA;MACA;MAEA,OAAAkC,OAAA;IACA;IAEA;IACA;IACAC,mBAAAiD,SAAA;MACApJ,OAAA,CAAAC,GAAA,gBAAAmJ,SAAA;;MAEA;MACA,IAAA/N,IAAA;MACA,IAAA4I,OAAA;;MAEA;MACA,IAAAmF,SAAA,CAAA/N,IAAA,IAAA0N,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAA/N,IAAA;QACAA,IAAA,GAAA+N,SAAA,CAAA/N,IAAA;QACA4I,OAAA,GAAAmF,SAAA,CAAAnF,OAAA;MACA;MACA;MAAA,KACA,IAAAmF,SAAA,CAAA/N,IAAA,IAAA+N,SAAA,CAAA/N,IAAA,CAAAA,IAAA,IAAA0N,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAA/N,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA+N,SAAA,CAAA/N,IAAA,CAAAA,IAAA;QACA4I,OAAA,GAAAmF,SAAA,CAAA/N,IAAA,CAAAkF,MAAA,GACA6I,SAAA,CAAA/N,IAAA,CAAAkF,MAAA,CAAAb,MAAA,CAAA4J,CAAA,IAAAA,CAAA,CAAA/E,SAAA;MACA;MAEAvE,OAAA,CAAAC,GAAA,YAAA5E,IAAA;MACA2E,OAAA,CAAAC,GAAA,QAAAgE,OAAA;;MAEA;MACA,MAAAsF,SAAA,GAAAlO,IAAA,CAAA+M,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAtE,KAAA,IAAAsE,IAAA,CAAAlN,IAAA;;MAEA;MACA,MAAAwN,MAAA;MACA,IAAAtN,IAAA,CAAA4C,MAAA,QAAA5C,IAAA,IAAAsN,MAAA;QACA;QACA,MAAAa,aAAA,OAAAC,GAAA;QACApO,IAAA,CAAAwN,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAU,aAAA,CAAAE,GAAA,CAAAZ,CAAA,CAAAxE,QAAA;UACA;QACA;QAEA,MAAA6D,UAAA,GAAAY,KAAA,CAAAY,IAAA,CAAAH,aAAA;QACArB,UAAA,CAAAU,OAAA,CAAAvE,QAAA;UACA,MAAAsF,UAAA,GAAAvO,IAAA,CAAA+M,GAAA,CAAAC,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAkB,IAAA,CAAAf,CAAA,IAAAA,CAAA,CAAAxE,QAAA,KAAAA,QAAA;YACA,OAAAqE,MAAA,GAAAA,MAAA,CAAA3E,KAAA;UACA;UAEA2E,MAAA,CAAAvK,IAAA;YACAjD,IAAA,EAAAmJ,QAAA;YACA5B,IAAA;YACArH,IAAA,EAAAuO;UACA;QACA;MACA;QACA;QACAjB,MAAA,CAAAvK,IAAA;UACAjD,IAAA,EAAA8I,OAAA,KAAA9I,IAAA;UACAuH,IAAA;UACArH,IAAA,EAAAA,IAAA,CAAA+M,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAArE,KAAA;QACA;MACA;MAEA;QACAV,KAAA;UACAgF,IAAA,EAAAc,SAAA,CAAA9F,KAAA;QACA;QACAiF,OAAA;UACAC,OAAA;UACAsB,WAAA;YACApH,IAAA;UACA;QACA;QACAqH,MAAA;UACA1O,IAAA,EAAAsN,MAAA,CAAAP,GAAA,CAAAU,CAAA,IAAAA,CAAA,CAAA3N,IAAA;QACA;QACAsN,KAAA;UACA/F,IAAA;UACArH,IAAA,EAAAkO;QACA;QACAb,KAAA;UACAhG,IAAA;QACA;QACAiG,MAAA,EAAAA;MACA;IACA;IAEA;IACAvC,oBAAAgD,SAAA;MACA,IAAA/N,IAAA;MACA,IAAA4I,OAAA;;MAEA;MACA,IAAAmF,SAAA,CAAA/N,IAAA,IAAA0N,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAA/N,IAAA;QACAA,IAAA,GAAA+N,SAAA,CAAA/N,IAAA;QACA4I,OAAA,GAAAmF,SAAA,CAAAnF,OAAA;MACA;MACA;MAAA,KACA,IAAAmF,SAAA,CAAA/N,IAAA,IAAA+N,SAAA,CAAA/N,IAAA,CAAAA,IAAA,IAAA0N,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAA/N,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA+N,SAAA,CAAA/N,IAAA,CAAAA,IAAA;QACA4I,OAAA,GAAAmF,SAAA,CAAA/N,IAAA,CAAAkF,MAAA,GACA6I,SAAA,CAAA/N,IAAA,CAAAkF,MAAA,CAAAb,MAAA,CAAA4J,CAAA,IAAAA,CAAA,CAAA/E,SAAA;MACA;;MAEA;MACA,MAAAgF,SAAA,GAAAlO,IAAA,CAAA+M,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAtE,KAAA,IAAAsE,IAAA,CAAAlN,IAAA;;MAEA;MACA,MAAAwN,MAAA;MACA,IAAAtN,IAAA,CAAA4C,MAAA,QAAA5C,IAAA,IAAAsN,MAAA;QACA;QACA,MAAAa,aAAA,OAAAC,GAAA;QACApO,IAAA,CAAAwN,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAU,aAAA,CAAAE,GAAA,CAAAZ,CAAA,CAAAxE,QAAA;UACA;QACA;QAEA,MAAA6D,UAAA,GAAAY,KAAA,CAAAY,IAAA,CAAAH,aAAA;QACArB,UAAA,CAAAU,OAAA,CAAAvE,QAAA;UACA,MAAAsF,UAAA,GAAAvO,IAAA,CAAA+M,GAAA,CAAAC,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAkB,IAAA,CAAAf,CAAA,IAAAA,CAAA,CAAAxE,QAAA,KAAAA,QAAA;YACA,OAAAqE,MAAA,GAAAA,MAAA,CAAA3E,KAAA;UACA;UAEA2E,MAAA,CAAAvK,IAAA;YACAjD,IAAA,EAAAmJ,QAAA;YACA5B,IAAA;YACArH,IAAA,EAAAuO;UACA;QACA;MACA;QACA;QACAjB,MAAA,CAAAvK,IAAA;UACAjD,IAAA,EAAA8I,OAAA,KAAA9I,IAAA;UACAuH,IAAA;UACArH,IAAA,EAAAA,IAAA,CAAA+M,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAArE,KAAA;QACA;MACA;MAEA;QACAV,KAAA;UACAgF,IAAA,EAAAc,SAAA,CAAA9F,KAAA;QACA;QACAiF,OAAA;UACAC,OAAA;QACA;QACAuB,MAAA;UACA1O,IAAA,EAAAsN,MAAA,CAAAP,GAAA,CAAAU,CAAA,IAAAA,CAAA,CAAA3N,IAAA;QACA;QACAsN,KAAA;UACA/F,IAAA;UACArH,IAAA,EAAAkO;QACA;QACAb,KAAA;UACAhG,IAAA;QACA;QACAiG,MAAA,EAAAA;MACA;IACA;IAEA;IACAtC,mBAAA+C,SAAA;MACA,IAAA/N,IAAA;;MAEA;MACA,IAAA+N,SAAA,CAAA/N,IAAA,IAAA0N,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAA/N,IAAA;QACAA,IAAA,GAAA+N,SAAA,CAAA/N,IAAA;MACA;MACA;MAAA,KACA,IAAA+N,SAAA,CAAA/N,IAAA,IAAA+N,SAAA,CAAA/N,IAAA,CAAAA,IAAA,IAAA0N,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAA/N,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA+N,SAAA,CAAA/N,IAAA,CAAAA,IAAA;MACA;MAEA,MAAAuO,UAAA,GAAAvO,IAAA,CAAA+M,GAAA,CAAAC,IAAA;QACAlN,IAAA,EAAAkN,IAAA,CAAAtE,KAAA,IAAAsE,IAAA,CAAAlN,IAAA;QACA6I,KAAA,EAAAqE,IAAA,CAAArE;MACA;MAEA;QACAV,KAAA;UACAgF,IAAA,EAAAc,SAAA,CAAA9F,KAAA;QACA;QACAiF,OAAA;UACAC,OAAA;UACAwB,SAAA;QACA;QACAD,MAAA;UACAE,MAAA;UACAC,KAAA;UACAC,GAAA;UACA9O,IAAA,EAAAuO,UAAA,CAAAxB,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAlN,IAAA;QACA;QACAwN,MAAA;UACAxN,IAAA;UACAuH,IAAA;UACA0H,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAvF,QAAA;UACA;UACAwF,QAAA;YACAF,KAAA;cACAC,IAAA;cACAE,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAJ,IAAA;UACA;UACAlP,IAAA,EAAAuO;QACA;MACA;IACA;IAEA;IACAtD,wBAAA8C,SAAA;MACA;MACA,IAAA/N,IAAA;MACA,IAAA4I,OAAA;;MAEA;MACA,IAAAmF,SAAA,CAAA/N,IAAA,IAAA0N,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAA/N,IAAA;QACAA,IAAA,GAAA+N,SAAA,CAAA/N,IAAA;QACA4I,OAAA,GAAAmF,SAAA,CAAAnF,OAAA;MACA;MACA;MAAA,KACA,IAAAmF,SAAA,CAAA/N,IAAA,IAAA+N,SAAA,CAAA/N,IAAA,CAAAA,IAAA,IAAA0N,KAAA,CAAAM,OAAA,CAAAD,SAAA,CAAA/N,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAA+N,SAAA,CAAA/N,IAAA,CAAAA,IAAA;QACA4I,OAAA,GAAAmF,SAAA,CAAA/N,IAAA,CAAAkF,MAAA,GACA6I,SAAA,CAAA/N,IAAA,CAAAkF,MAAA,CAAAb,MAAA,CAAA4J,CAAA,IAAAA,CAAA,CAAA/E,SAAA;MACA;;MAEA;MACA,MAAAqG,SAAA,GAAAvP,IAAA,CAAA+M,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAtE,KAAA,IAAAsE,IAAA,CAAAlN,IAAA;;MAEA;MACA,MAAAwN,MAAA;MACA,IAAAtN,IAAA,CAAA4C,MAAA,QAAA5C,IAAA,IAAAsN,MAAA;QACA;QACA,MAAAa,aAAA,OAAAC,GAAA;QACApO,IAAA,CAAAwN,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAU,aAAA,CAAAE,GAAA,CAAAZ,CAAA,CAAAxE,QAAA;UACA;QACA;QAEA,MAAA6D,UAAA,GAAAY,KAAA,CAAAY,IAAA,CAAAH,aAAA;QACArB,UAAA,CAAAU,OAAA,CAAAvE,QAAA;UACA,MAAAsF,UAAA,GAAAvO,IAAA,CAAA+M,GAAA,CAAAC,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAkB,IAAA,CAAAf,CAAA,IAAAA,CAAA,CAAAxE,QAAA,KAAAA,QAAA;YACA,OAAAqE,MAAA,GAAAA,MAAA,CAAA3E,KAAA;UACA;UAEA2E,MAAA,CAAAvK,IAAA;YACAjD,IAAA,EAAAmJ,QAAA;YACA5B,IAAA;YAAA;YACArH,IAAA,EAAAuO;UACA;QACA;MACA;QACA;QACAjB,MAAA,CAAAvK,IAAA;UACAjD,IAAA,EAAA8I,OAAA,KAAA9I,IAAA;UACAuH,IAAA;UAAA;UACArH,IAAA,EAAAA,IAAA,CAAA+M,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAArE,KAAA;QACA;MACA;MAEA;QACAV,KAAA;UACAgF,IAAA,EAAAc,SAAA,CAAA9F,KAAA;QACA;QACAiF,OAAA;UACAC,OAAA;UACAsB,WAAA;YACApH,IAAA;UACA;QACA;QACAqH,MAAA;UACA1O,IAAA,EAAAsN,MAAA,CAAAP,GAAA,CAAAU,CAAA,IAAAA,CAAA,CAAA3N,IAAA;QACA;QACA;QACAsN,KAAA;UACA/F,IAAA;QACA;QACAgG,KAAA;UACAhG,IAAA;UAAA;UACArH,IAAA,EAAAuP;QACA;QACAjC,MAAA,EAAAA;MACA;IACA;IAEA;IACApC,kBAAA6C,SAAA;MACA;QACA9F,KAAA;UACAgF,IAAA,EAAAc,SAAA,CAAA9F,KAAA;QACA;QACAiF,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACA/F,IAAA;UACArH,IAAA;QACA;QACAqN,KAAA;UACAhG,IAAA;QACA;QACAiG,MAAA;UACAjG,IAAA,EAAA0G,SAAA,CAAA1G,IAAA;UACArH,IAAA;QACA;MACA;IACA;IAEA;IACAwL,gBAAAgE,SAAA,EAAAtI,WAAA;MACA;MACA,IAAAlH,IAAA;MACA,IAAAyP,OAAA;;MAEA;MACA,IAAAvI,WAAA,CAAAlH,IAAA,IAAAkH,WAAA,CAAAlH,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAkH,WAAA,CAAAlH,IAAA,CAAAA,IAAA;;QAEA;QACA,IAAAA,IAAA,CAAA4C,MAAA;UACA,IAAA5C,IAAA,IAAAsN,MAAA;YACA;YACAmC,OAAA;YACA,MAAAC,SAAA,GAAA1P,IAAA;YACA,IAAA0P,SAAA,CAAApC,MAAA,IAAAI,KAAA,CAAAM,OAAA,CAAA0B,SAAA,CAAApC,MAAA;cACAoC,SAAA,CAAApC,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACAgC,OAAA,CAAA1M,IAAA,CAAA0K,CAAA,CAAAxE,QAAA;cACA;YACA;UACA;YACA;YACAwG,OAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAE,SAAA;;MAEA;MACAA,SAAA;MACAF,OAAA,CAAAjC,OAAA,CAAAoC,MAAA;QACAD,SAAA,sGAAAC,MAAA;MACA;MACAD,SAAA;;MAEA;MACAA,SAAA;MACA3P,IAAA,CAAAwN,OAAA,CAAAR,IAAA;QACA2C,SAAA;;QAEA;QACAA,SAAA,yDAAA3C,IAAA,CAAAtE,KAAA,IAAAsE,IAAA,CAAAlN,IAAA;;QAEA;QACA,IAAAkN,IAAA,CAAAM,MAAA;UACA;UACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;YACAkC,SAAA,yDAAAlC,CAAA,CAAA9E,KAAA,KAAAkH,SAAA,GAAApC,CAAA,CAAA9E,KAAA;UACA;QACA;UACA;UACAgH,SAAA,yDAAA3C,IAAA,CAAArE,KAAA,KAAAkH,SAAA,GAAA7C,IAAA,CAAArE,KAAA;QACA;QAEAgH,SAAA;MACA;MACAA,SAAA;;MAEA;MACAH,SAAA,CAAAlF,SAAA,GAAAqF,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}