{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { dataApi } from './api';\nimport { v4 as uuidv4 } from 'uuid';\nimport axios from 'axios';\nexport default {\n  name: 'App',\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [\"这个数据集包含哪些字段？\", \"帮我分析一下销售数据的趋势\", \"哪个产品的销售额最高？\"],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      // 新增数据集树结构相关数据\n      datasetTree: [],\n      defaultProps: {\n        children: 'children',\n        label: 'name'\n      },\n      selectedDataset: null,\n      datasetFields: [],\n      datasetData: [],\n      datasetColumns: []\n    };\n  },\n  mounted() {\n    this.loadDatasetTree();\n    this.initMemoryId();\n    this.addWelcomeMessage();\n  },\n  updated() {\n    this.scrollToBottom();\n  },\n  methods: {\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id');\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4());\n        localStorage.setItem('user_memory_id', storedMemoryId);\n      }\n      this.memoryId = storedMemoryId;\n    },\n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0;\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i];\n        number = number * 16 + (parseInt(hexValue, 16) || 0);\n      }\n      return number % 1000000;\n    },\n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      });\n    },\n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight;\n      }\n    },\n    // 加载数据集树结构\n    async loadDatasetTree() {\n      try {\n        const response = await dataApi.getDatasetTree();\n        if (response && response.data && response.data.data) {\n          this.datasetTree = response.data.data;\n        } else {\n          this.$message.warning('获取数据集列表失败，返回数据格式不正确');\n          console.log('API返回数据:', response);\n        }\n      } catch (error) {\n        this.$message.error('加载数据集列表失败');\n        console.error(error);\n      }\n    },\n    // 处理节点点击事件\n    async handleNodeClick(data) {\n      // 只有点击数据集节点才加载详情\n      if (data.nodeType !== 'folder') {\n        try {\n          const response = await dataApi.getDatasetInfo(data.id);\n          if (response && response.data && response.data.data) {\n            const datasetInfo = response.data.data;\n            this.selectedDataset = data;\n\n            // 处理字段列表\n            if (datasetInfo.fields && Array.isArray(datasetInfo.fields)) {\n              this.datasetFields = datasetInfo.fields.map(field => ({\n                name: field.name || field.fieldName,\n                type: field.type || field.fieldType,\n                remarks: field.remarks || ''\n              }));\n            }\n\n            // 处理数据预览\n            if (datasetInfo.data && Array.isArray(datasetInfo.data)) {\n              this.datasetData = datasetInfo.data;\n\n              // 生成表格列\n              if (this.datasetData.length > 0) {\n                this.datasetColumns = Object.keys(this.datasetData[0]).map(key => ({\n                  prop: key,\n                  label: key\n                }));\n              }\n            } else {\n              this.datasetData = [];\n              this.datasetColumns = [];\n            }\n          } else {\n            this.$message.warning('获取数据集详情失败，返回数据格式不正确');\n            console.log('API返回数据:', response);\n          }\n        } catch (error) {\n          this.$message.error('加载数据集详情失败');\n          console.error(error);\n        }\n      }\n    },\n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables();\n        this.tables = response.data;\n      } catch (error) {\n        this.$message.error('加载数据表失败');\n        console.error(error);\n      }\n    },\n    async selectTable(table) {\n      try {\n        this.dialogVisible = true;\n        console.log(table);\n        const response = await dataApi.getTableIndicators(table);\n        this.tableIndicators = response.data;\n        console.log(this.tableIndicators);\n      } catch (error) {\n        this.$message.error('加载数据表失败');\n        console.error(error);\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n\n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedDataset ? this.selectedDataset.name : this.datasetTree.length > 0 ? this.datasetTree[0].name : \"未知数据集\";\n\n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      };\n      this.messages.push(userMsg);\n\n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      };\n      this.messages.push(botMsg);\n\n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1];\n\n      // 保存问题并清空输入框\n      const question = this.question;\n      this.question = '';\n      this.isSending = true;\n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`;\n\n        // 发送请求\n        await axios.post('http://localhost:8088/api/indicator/chat', {\n          memoryId: this.memoryId,\n          message\n        }, {\n          responseType: 'stream',\n          onDownloadProgress: e => {\n            const fullText = e.event.target.responseText; // 累积的完整文本\n            let newText = fullText.substring(lastMsg.content.length);\n            lastMsg.content += newText; // 增量更新\n            this.scrollToBottom(); // 实时滚动\n          }\n        });\n\n        // 请求完成后\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      } catch (error) {\n        console.error('请求出错:', error);\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。';\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "v4", "uuidv4", "axios", "name", "data", "description", "tablename", "tables", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "tableIndicators", "dialogVisible", "messages", "memoryId", "isSending", "messageListRef", "datasetTree", "defaultProps", "children", "label", "selectedDataset", "datasetFields", "datasetData", "datasetColumns", "mounted", "loadDatasetTree", "initMemoryId", "addWelcomeMessage", "updated", "scrollToBottom", "methods", "storedMemoryId", "localStorage", "getItem", "uuidToNumber", "setItem", "uuid", "number", "i", "length", "hexValue", "parseInt", "push", "isUser", "content", "isTyping", "$refs", "scrollTop", "scrollHeight", "response", "getDatasetTree", "$message", "warning", "console", "log", "error", "handleNodeClick", "nodeType", "getDatasetInfo", "id", "datasetInfo", "fields", "Array", "isArray", "map", "field", "fieldName", "type", "fieldType", "remarks", "Object", "keys", "key", "prop", "loadTables", "getAllTables", "selectTable", "table", "getTableIndicators", "showSuggestions", "useQuestion", "q", "getTableFields", "tableCode", "submitQuestion", "trim", "currentDataset", "userMsg", "botMsg", "lastMsg", "message", "post", "responseType", "onDownloadProgress", "e", "fullText", "event", "target", "responseText", "newText", "substring"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下数据集中选择开始问吧！</p>\n        </div>\n        \n        <!-- 数据集树结构展示 -->\n        <div class=\"dataset-tree\">\n          <h3>数据集列表</h3>\n          <el-tree\n            :data=\"datasetTree\"\n            :props=\"defaultProps\"\n            @node-click=\"handleNodeClick\"\n            :highlight-current=\"true\"\n            node-key=\"id\"\n            :expand-on-click-node=\"false\"\n            default-expand-all>\n            <template #default=\"{ node, data }\">\n              <span class=\"custom-tree-node\">\n                <span>{{ node.label }}</span>\n                <span class=\"node-type\">{{ data.nodeType === 'folder' ? '目录' : '数据集' }}</span>\n              </span>\n            </template>\n          </el-tree>\n        </div>\n        \n        <!-- 数据集详情展示 -->\n        <div v-if=\"selectedDataset\" class=\"dataset-detail\">\n          <h3>数据集详情: {{ selectedDataset.name }}</h3>\n          \n          <!-- 字段列表 -->\n          <div class=\"field-list\">\n            <h4>字段列表</h4>\n            <el-table :data=\"datasetFields\" style=\"width: 100%\">\n              <el-table-column prop=\"name\" label=\"字段名称\"></el-table-column>\n              <el-table-column prop=\"type\" label=\"字段类型\"></el-table-column>\n              <el-table-column prop=\"remarks\" label=\"备注\"></el-table-column>\n            </el-table>\n          </div>\n          \n          <!-- 数据预览 -->\n          <div class=\"data-preview\">\n            <h4>数据预览</h4>\n            <el-table :data=\"datasetData\" style=\"width: 100%\" v-if=\"datasetData.length > 0\">\n              <el-table-column \n                v-for=\"column in datasetColumns\" \n                :key=\"column.prop\" \n                :prop=\"column.prop\" \n                :label=\"column.label\">\n              </el-table-column>\n            </el-table>\n            <div v-else class=\"no-data\">暂无数据</div>\n          </div>\n        </div>\n        \n        <!-- 聊天消息列表区域 -->\n        <div class=\"message-list\" ref=\"messageListRef\">\n          <div\n            v-for=\"(message, index) in messages\"\n            :key=\"index\"\n            :class=\"message.isUser ? 'message user-message' : 'message bot-message'\"\n          >\n            <!-- 聊天图标 -->\n            <div class=\"avatar-container\" v-if=\"!message.isUser\">\n              <div class=\"bot-avatar\">\n                <i class=\"el-icon-s-tools\"></i>\n              </div>\n            </div>\n            <!-- 消息内容 -->\n            <div class=\"message-content\">\n              <div v-html=\"message.content\"></div>\n              <!-- loading动画 -->\n              <span\n                class=\"loading-dots\"\n                v-if=\"message.isTyping\"\n              >\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n              </span>\n            </div>\n            <div class=\"avatar-container\" v-if=\"message.isUser\">\n              <div class=\"user-avatar\">\n                <i class=\"el-icon-user\"></i>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 底部问题输入区域 -->\n    <div class=\"question-input-container\">\n      <span>👋直接问我问题，或在上方选择一个主题/数据开始！</span>\n      <div class=\"question-input-wrapper\">\n       \n        <div class=\"input-prefix\">👋</div>\n        <el-input \n          v-model=\"question\" \n          placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n          class=\"question-input\"\n          @keyup.enter.native=\"submitQuestion\"\n          :disabled=\"isSending\">\n        </el-input>\n        <div class=\"input-actions\">\n          <button class=\"action-btn\" @click=\"showSuggestions\">\n            <i class=\"el-icon-magic-stick\"></i>\n          </button>\n          <button class=\"action-btn send-btn\" @click=\"submitQuestion\" :disabled=\"isSending\">\n            <i class=\"el-icon-position\"></i>\n          </button>\n        </div>\n      </div>\n      \n      <!-- 建议问题弹出层 -->\n      <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n        <div class=\"suggestions-title\">\n          <i class=\"el-icon-s-promotion\"></i> 官方推荐\n        </div>\n        <div class=\"suggestions-list\">\n          <div \n            v-for=\"(suggestion, index) in suggestedQuestions\" \n            :key=\"index\"\n            class=\"suggestion-item\"\n            @click=\"useQuestion(suggestion)\">\n            {{ suggestion }}\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <el-drawer\n      title=\"数据详情\"\n      :visible.sync=\"dialogVisible\"\n      direction=\"rtl\"\n      size=\"50%\">\n      <div style=\"padding: 20px\">\n        <el-table :data=\"tableIndicators\">\n          <el-table-column property=\"id\" ></el-table-column>\n          <el-table-column property=\"datasetTag\" ></el-table-column>\n          <el-table-column property=\"transactionDate\" ></el-table-column>\n          <el-table-column property=\"salesAmount\" ></el-table-column>\n        </el-table>\n      </div>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api'\nimport { v4 as uuidv4 } from 'uuid'\nimport axios from 'axios'\n\nexport default {\n  name: 'App',\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [\n        \"这个数据集包含哪些字段？\",\n        \"帮我分析一下销售数据的趋势\",\n        \"哪个产品的销售额最高？\"\n      ],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      \n      // 新增数据集树结构相关数据\n      datasetTree: [],\n      defaultProps: {\n        children: 'children',\n        label: 'name'\n      },\n      selectedDataset: null,\n      datasetFields: [],\n      datasetData: [],\n      datasetColumns: []\n    }\n  },\n  mounted() {\n    this.loadDatasetTree()\n    this.initMemoryId()\n    this.addWelcomeMessage()\n  },\n  updated() {\n    this.scrollToBottom()\n  },\n  methods: {\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id')\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4())\n        localStorage.setItem('user_memory_id', storedMemoryId)\n      }\n      this.memoryId = storedMemoryId\n    },\n    \n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i]\n        number = number * 16 + (parseInt(hexValue, 16) || 0)\n      }\n      return number % 1000000\n    },\n    \n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      })\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight\n      }\n    },\n    \n    // 加载数据集树结构\n    async loadDatasetTree() {\n      try {\n        const response = await dataApi.getDatasetTree()\n        if (response && response.data && response.data.data) {\n          this.datasetTree = response.data.data\n        } else {\n          this.$message.warning('获取数据集列表失败，返回数据格式不正确')\n          console.log('API返回数据:', response)\n        }\n      } catch (error) {\n        this.$message.error('加载数据集列表失败')\n        console.error(error)\n      }\n    },\n    \n    // 处理节点点击事件\n    async handleNodeClick(data) {\n      // 只有点击数据集节点才加载详情\n      if (data.nodeType !== 'folder') {\n        try {\n          const response = await dataApi.getDatasetInfo(data.id)\n          if (response && response.data && response.data.data) {\n            const datasetInfo = response.data.data\n            this.selectedDataset = data\n            \n            // 处理字段列表\n            if (datasetInfo.fields && Array.isArray(datasetInfo.fields)) {\n              this.datasetFields = datasetInfo.fields.map(field => ({\n                name: field.name || field.fieldName,\n                type: field.type || field.fieldType,\n                remarks: field.remarks || ''\n              }))\n            }\n            \n            // 处理数据预览\n            if (datasetInfo.data && Array.isArray(datasetInfo.data)) {\n              this.datasetData = datasetInfo.data\n              \n              // 生成表格列\n              if (this.datasetData.length > 0) {\n                this.datasetColumns = Object.keys(this.datasetData[0]).map(key => ({\n                  prop: key,\n                  label: key\n                }))\n              }\n            } else {\n              this.datasetData = []\n              this.datasetColumns = []\n            }\n          } else {\n            this.$message.warning('获取数据集详情失败，返回数据格式不正确')\n            console.log('API返回数据:', response)\n          }\n        } catch (error) {\n          this.$message.error('加载数据集详情失败')\n          console.error(error)\n        }\n      }\n    },\n    \n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables()\n        this.tables = response.data\n      } catch (error) {\n        this.$message.error('加载数据表失败')\n        console.error(error)\n      }\n    },\n    \n    async selectTable(table) {\n      try {\n        this.dialogVisible = true\n        console.log(table)\n        const response = await dataApi.getTableIndicators(table)\n        this.tableIndicators = response.data\n        console.log(this.tableIndicators)\n      } catch(error) {\n        this.$message.error('加载数据表失败')\n        console.error(error)\n      }\n    },\n    \n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    \n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    \n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    \n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      // 获取当前选中的数据集信息\n      const currentDataset = this.selectedDataset ? \n        this.selectedDataset.name : \n        (this.datasetTree.length > 0 ? this.datasetTree[0].name : \"未知数据集\")\n      \n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      }\n      this.messages.push(userMsg)\n      \n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      }\n      this.messages.push(botMsg)\n      \n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1]\n      \n      // 保存问题并清空输入框\n      const question = this.question\n      this.question = ''\n      this.isSending = true\n      \n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = `${question}。当前数据集： ${currentDataset}`\n        \n        // 发送请求\n        await axios.post(\n          'http://localhost:8088/api/indicator/chat',\n          { memoryId: this.memoryId, message },\n          {\n            responseType: 'stream',\n            onDownloadProgress: (e) => {\n              const fullText = e.event.target.responseText // 累积的完整文本\n              let newText = fullText.substring(lastMsg.content.length)\n              lastMsg.content += newText // 增量更新\n              this.scrollToBottom() // 实时滚动\n            }\n          }\n        )\n        \n        // 请求完成后\n        lastMsg.isTyping = false\n        this.isSending = false\n      } catch (error) {\n        console.error('请求出错:', error)\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'\n        lastMsg.isTyping = false\n        this.isSending = false\n      }\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.app-layout {\n  display: flex;\n  height: calc(100vh - 100px); /* 为更高的底部输入框留出空间 */\n  overflow: hidden;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  background-color: #f9f9f9;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 25px;\n  text-align: center;\n  white-space: nowrap;\n  letter-spacing: 2px;\n  font-weight: 900;\n}\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.header {\n  margin-bottom: 20px;\n}\n\n.header h2 {\n  margin: 0;\n  font-size: 30px;\n}\n\n.highlight {\n  color: #409eff;\n}\n\n.sub-title {\n  color: #606266;\n  font-size: 14px;\n  margin: 5px 0 0 0;\n}\n\n/* 数据集树结构样式 */\n.dataset-tree {\n  margin-bottom: 20px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 15px;\n  background-color: #fff;\n}\n\n.dataset-tree h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 18px;\n  color: #303133;\n}\n\n.custom-tree-node {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 14px;\n  padding-right: 8px;\n}\n\n.node-type {\n  font-size: 12px;\n  color: #909399;\n  background-color: #f5f7fa;\n  padding: 2px 6px;\n  border-radius: 2px;\n}\n\n/* 数据集详情样式 */\n.dataset-detail {\n  margin-bottom: 20px;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  padding: 15px;\n  background-color: #fff;\n}\n\n.dataset-detail h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 18px;\n  color: #303133;\n}\n\n.dataset-detail h4 {\n  margin-top: 20px;\n  margin-bottom: 10px;\n  font-size: 16px;\n  color: #606266;\n}\n\n.field-list, .data-preview {\n  margin-bottom: 20px;\n}\n\n.no-data {\n  color: #909399;\n  text-align: center;\n  padding: 20px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.data-card {\n  cursor: pointer;\n  margin-bottom: 15px;\n  transition: all 0.3s;\n}\n\n.data-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.data-header {\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.sample-tag {\n  background-color: #ecf5ff;\n  color: #409eff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n.data-fields {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.field-item {\n  background-color: #f5f7fa;\n  padding: 2px 8px;\n  margin: 4px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n/* 底部问题输入区域 */\n.question-input-container {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  background-color: #fff;\n  border-top: 1px solid #ebeef5;\n  padding: 25px 15px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n}\n\n.question-input-container > span {\n  margin-bottom: 12px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.question-input-wrapper {\n  display: flex;\n  align-items: center;\n  width: 70%;\n  max-width: 800px;\n  margin: 0 auto;\n  background-color: #f5f7fa;\n  border-radius: 20px;\n  padding: 8px 15px;\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: #fff;\n  border: 1px solid #dcdfe6;\n  margin-left: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n}\n\n.send-btn {\n  background-color: #409eff;\n  color: white;\n  border: none;\n}\n\n.send-btn:disabled {\n  background-color: #c0c4cc;\n  cursor: not-allowed;\n}\n\n/* 建议问题面板 */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n\n/* 消息列表样式 */\n.message-list {\n  width: 100%;\n  max-height: 420px;\n  overflow-y: auto;\n  margin-top: 20px;\n  padding: 10px;\n  background-color: transparent;\n  border-radius: 8px;\n}\n\n.message {\n  margin-bottom: 20px;\n  display: flex;\n  position: relative;\n  max-width: 80%;\n}\n\n.message-content {\n  padding: 12px 15px;\n  border-radius: 6px;\n  line-height: 1.5;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n  align-self: flex-end;\n  margin-left: auto;\n}\n\n.user-message .message-content {\n  background-color: #ecf5ff;\n  margin-right: 10px;\n}\n\n.bot-message {\n  align-self: flex-start;\n}\n\n.bot-message .message-content {\n  background-color: #f5f7fa;\n  margin-left: 10px;\n}\n\n.avatar-container {\n  display: flex;\n  align-items: flex-start;\n}\n\n.bot-avatar, .user-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.bot-avatar {\n  background-color: #4caf50;\n  color: white;\n}\n\n.user-avatar {\n  background-color: #2196f3;\n  color: white;\n}\n\n/* 加载动画 */\n.loading-dots {\n  display: inline-block;\n}\n\n.dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  background-color: #999;\n  border-radius: 50%;\n  margin: 0 2px;\n  animation: pulse 1.2s infinite ease-in-out both;\n}\n\n.dot:nth-child(2) {\n  animation-delay: -0.4s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: -0.8s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(0.6);\n    opacity: 0.4;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n</style>"], "mappings": ";;;AA6KA,SAAAA,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA,GACA,gBACA,iBACA,cACA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;MAEA;MACAC,WAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,eAAA;MACAC,aAAA;MACAC,WAAA;MACAC,cAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,eAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;EACA;EACAC,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAJ,aAAA;MACA,IAAAK,cAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,KAAAF,cAAA;QACAA,cAAA,QAAAG,YAAA,CAAArC,MAAA;QACAmC,YAAA,CAAAG,OAAA,mBAAAJ,cAAA;MACA;MACA,KAAAlB,QAAA,GAAAkB,cAAA;IACA;IAEA;IACAG,aAAAE,IAAA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAG,MAAA,IAAAD,CAAA,MAAAA,CAAA;QACA,MAAAE,QAAA,GAAAJ,IAAA,CAAAE,CAAA;QACAD,MAAA,GAAAA,MAAA,SAAAI,QAAA,CAAAD,QAAA;MACA;MACA,OAAAH,MAAA;IACA;IAEA;IACAV,kBAAA;MACA,KAAAf,QAAA,CAAA8B,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IAEA;IACAhB,eAAA;MACA,SAAAiB,KAAA,CAAA/B,cAAA;QACA,KAAA+B,KAAA,CAAA/B,cAAA,CAAAgC,SAAA,QAAAD,KAAA,CAAA/B,cAAA,CAAAiC,YAAA;MACA;IACA;IAEA;IACA,MAAAvB,gBAAA;MACA;QACA,MAAAwB,QAAA,SAAAtD,OAAA,CAAAuD,cAAA;QACA,IAAAD,QAAA,IAAAA,QAAA,CAAAjD,IAAA,IAAAiD,QAAA,CAAAjD,IAAA,CAAAA,IAAA;UACA,KAAAgB,WAAA,GAAAiC,QAAA,CAAAjD,IAAA,CAAAA,IAAA;QACA;UACA,KAAAmD,QAAA,CAAAC,OAAA;UACAC,OAAA,CAAAC,GAAA,aAAAL,QAAA;QACA;MACA,SAAAM,KAAA;QACA,KAAAJ,QAAA,CAAAI,KAAA;QACAF,OAAA,CAAAE,KAAA,CAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAC,gBAAAxD,IAAA;MACA;MACA,IAAAA,IAAA,CAAAyD,QAAA;QACA;UACA,MAAAR,QAAA,SAAAtD,OAAA,CAAA+D,cAAA,CAAA1D,IAAA,CAAA2D,EAAA;UACA,IAAAV,QAAA,IAAAA,QAAA,CAAAjD,IAAA,IAAAiD,QAAA,CAAAjD,IAAA,CAAAA,IAAA;YACA,MAAA4D,WAAA,GAAAX,QAAA,CAAAjD,IAAA,CAAAA,IAAA;YACA,KAAAoB,eAAA,GAAApB,IAAA;;YAEA;YACA,IAAA4D,WAAA,CAAAC,MAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAH,WAAA,CAAAC,MAAA;cACA,KAAAxC,aAAA,GAAAuC,WAAA,CAAAC,MAAA,CAAAG,GAAA,CAAAC,KAAA;gBACAlE,IAAA,EAAAkE,KAAA,CAAAlE,IAAA,IAAAkE,KAAA,CAAAC,SAAA;gBACAC,IAAA,EAAAF,KAAA,CAAAE,IAAA,IAAAF,KAAA,CAAAG,SAAA;gBACAC,OAAA,EAAAJ,KAAA,CAAAI,OAAA;cACA;YACA;;YAEA;YACA,IAAAT,WAAA,CAAA5D,IAAA,IAAA8D,KAAA,CAAAC,OAAA,CAAAH,WAAA,CAAA5D,IAAA;cACA,KAAAsB,WAAA,GAAAsC,WAAA,CAAA5D,IAAA;;cAEA;cACA,SAAAsB,WAAA,CAAAiB,MAAA;gBACA,KAAAhB,cAAA,GAAA+C,MAAA,CAAAC,IAAA,MAAAjD,WAAA,KAAA0C,GAAA,CAAAQ,GAAA;kBACAC,IAAA,EAAAD,GAAA;kBACArD,KAAA,EAAAqD;gBACA;cACA;YACA;cACA,KAAAlD,WAAA;cACA,KAAAC,cAAA;YACA;UACA;YACA,KAAA4B,QAAA,CAAAC,OAAA;YACAC,OAAA,CAAAC,GAAA,aAAAL,QAAA;UACA;QACA,SAAAM,KAAA;UACA,KAAAJ,QAAA,CAAAI,KAAA;UACAF,OAAA,CAAAE,KAAA,CAAAA,KAAA;QACA;MACA;IACA;IAEA,MAAAmB,WAAA;MACA;QACA,MAAAzB,QAAA,SAAAtD,OAAA,CAAAgF,YAAA;QACA,KAAAxE,MAAA,GAAA8C,QAAA,CAAAjD,IAAA;MACA,SAAAuD,KAAA;QACA,KAAAJ,QAAA,CAAAI,KAAA;QACAF,OAAA,CAAAE,KAAA,CAAAA,KAAA;MACA;IACA;IAEA,MAAAqB,YAAAC,KAAA;MACA;QACA,KAAAlE,aAAA;QACA0C,OAAA,CAAAC,GAAA,CAAAuB,KAAA;QACA,MAAA5B,QAAA,SAAAtD,OAAA,CAAAmF,kBAAA,CAAAD,KAAA;QACA,KAAAnE,eAAA,GAAAuC,QAAA,CAAAjD,IAAA;QACAqD,OAAA,CAAAC,GAAA,MAAA5C,eAAA;MACA,SAAA6C,KAAA;QACA,KAAAJ,QAAA,CAAAI,KAAA;QACAF,OAAA,CAAAE,KAAA,CAAAA,KAAA;MACA;IACA;IAEAwB,gBAAA;MACA,KAAAxE,oBAAA,SAAAA,oBAAA;IACA;IAEAyE,YAAAC,CAAA;MACA,KAAA5E,QAAA,GAAA4E,CAAA;MACA,KAAA1E,oBAAA;IACA;IAEA2E,eAAAL,KAAA;MACA,IAAAA,KAAA,CAAAM,SAAA;QACA;MACA;MACA;IACA;IAEA;IACA,MAAAC,eAAA;MACA,UAAA/E,QAAA,CAAAgF,IAAA,WAAAvE,SAAA;QACA,KAAAqC,QAAA,CAAAC,OAAA;QACA;MACA;;MAEA;MACA,MAAAkC,cAAA,QAAAlE,eAAA,GACA,KAAAA,eAAA,CAAArB,IAAA,GACA,KAAAiB,WAAA,CAAAuB,MAAA,YAAAvB,WAAA,IAAAjB,IAAA;;MAEA;MACA,MAAAwF,OAAA;QACA5C,MAAA;QACAC,OAAA,OAAAvC,QAAA,CAAAgF,IAAA;QACAxC,QAAA;MACA;MACA,KAAAjC,QAAA,CAAA8B,IAAA,CAAA6C,OAAA;;MAEA;MACA,MAAAC,MAAA;QACA7C,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAAjC,QAAA,CAAA8B,IAAA,CAAA8C,MAAA;;MAEA;MACA,MAAAC,OAAA,QAAA7E,QAAA,MAAAA,QAAA,CAAA2B,MAAA;;MAEA;MACA,MAAAlC,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA;MACA,KAAAS,SAAA;MAEA;QACA;QACA,MAAA4E,OAAA,MAAArF,QAAA,WAAAiF,cAAA;;QAEA;QACA,MAAAxF,KAAA,CAAA6F,IAAA,CACA,4CACA;UAAA9E,QAAA,OAAAA,QAAA;UAAA6E;QAAA,GACA;UACAE,YAAA;UACAC,kBAAA,EAAAC,CAAA;YACA,MAAAC,QAAA,GAAAD,CAAA,CAAAE,KAAA,CAAAC,MAAA,CAAAC,YAAA;YACA,IAAAC,OAAA,GAAAJ,QAAA,CAAAK,SAAA,CAAAX,OAAA,CAAA7C,OAAA,CAAAL,MAAA;YACAkD,OAAA,CAAA7C,OAAA,IAAAuD,OAAA;YACA,KAAAtE,cAAA;UACA;QACA,CACA;;QAEA;QACA4D,OAAA,CAAA5C,QAAA;QACA,KAAA/B,SAAA;MACA,SAAAyC,KAAA;QACAF,OAAA,CAAAE,KAAA,UAAAA,KAAA;QACAkC,OAAA,CAAA7C,OAAA;QACA6C,OAAA,CAAA5C,QAAA;QACA,KAAA/B,SAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}