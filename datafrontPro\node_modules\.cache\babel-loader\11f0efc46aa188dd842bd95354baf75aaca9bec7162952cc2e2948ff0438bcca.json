{"ast": null, "code": "import axios from 'axios';\nexport const dataApi = {\n  getAllTables() {\n    return axios.post('/api/dataease/datasets', {\n      busiFlag: \"dataset\"\n    });\n  },\n  getDatasetDetail(datasetId) {\n    return axios.post('/api/dataease/dataset/detail/${datasetId}', {\n      datasetId: datasetId\n    });\n  },\n  getTableSample(tableCode) {\n    return axios.get(`/data/tables/${tableCode}/sample`);\n  },\n  queryByQuestion(tableCode, question) {\n    return axios.get('/analytics/query', {\n      params: {\n        tableCode,\n        question\n      }\n    });\n  },\n  // 新增：从Redis获取图表数据的API\n  getChartDataById(id) {\n    return axios.get(`http://localhost:8088/api/chart/data/${id}`);\n  }\n};\nexport const getData = async data => {\n  console.log('调用getData API，请求参数:', data);\n\n  // 如果传入的配置本身就包含完整数据，直接返回格式化的结果\n  if (data.data) {\n    console.log('配置中已包含数据，直接返回');\n    return {\n      code: 0,\n      msg: 'success',\n      data: data\n    };\n  }\n\n  // 添加测试模式，如果请求参数包含test-dataset，则返回模拟数据\n  if (data.datasetId === 'test-dataset' || data.tableId === 'test-table') {\n    console.log('使用测试模式，返回模拟数据');\n    return getMockChartData(data.type);\n  }\n  try {\n    // 如果是直接传入的完整数据对象（从AI响应中提取的）\n    if (data.code === 0 && data.data) {\n      console.log('检测到完整API响应格式，直接返回');\n      return data;\n    }\n\n    // 如果包含chartDataId字段，使用专门的API获取数据\n    if (data.chartDataId) {\n      console.log('检测到chartDataId，从Redis获取数据:', data.chartDataId);\n      const response = await dataApi.getChartDataById(data.chartDataId);\n      console.log('Redis数据获取结果:', response);\n      return response.data;\n    }\n\n    // 正常API调用\n    console.log('发起API请求:', data);\n    const response = await axios.post('/api/chartData/getData-1', data);\n    console.log('getData API响应结果:', response);\n\n    // 检查响应格式\n    if (response.data) {\n      console.log('API返回数据:', response.data);\n      return response.data;\n    } else {\n      console.error('响应数据为空');\n      return {\n        code: -1,\n        msg: '响应数据为空',\n        data: null\n      };\n    }\n  } catch (error) {\n    console.error('getData API调用出错:', error);\n    return {\n      code: -1,\n      msg: error.message,\n      data: null\n    };\n  }\n};\n\n// 生成模拟图表数据，用于测试\nfunction getMockChartData(chartType) {\n  const mockData = {\n    code: 0,\n    msg: 'success',\n    data: {\n      type: chartType || 'bar',\n      data: [{\n        field: '类别A',\n        value: 120\n      }, {\n        field: '类别B',\n        value: 200\n      }, {\n        field: '类别C',\n        value: 150\n      }, {\n        field: '类别D',\n        value: 80\n      }, {\n        field: '类别E',\n        value: 170\n      }],\n      metrics: [{\n        name: '销售额'\n      }]\n    }\n  };\n  console.log('生成的模拟数据:', mockData);\n  return mockData;\n}", "map": {"version": 3, "names": ["axios", "dataApi", "getAllTables", "post", "busiFlag", "getDatasetDetail", "datasetId", "getTableSample", "tableCode", "get", "queryByQuestion", "question", "params", "getChartDataById", "id", "getData", "data", "console", "log", "code", "msg", "tableId", "getMockChartData", "type", "chartDataId", "response", "error", "message", "chartType", "mockData", "field", "value", "metrics", "name"], "sources": ["E:/indicator-qa-service/datafront/src/api/index.js"], "sourcesContent": ["import axios from 'axios'\n\nexport const dataApi = {\n  getAllTables() {\n    return axios.post('/api/dataease/datasets',{\n      busiFlag:\"dataset\"\n    })\n  },\n   getDatasetDetail(datasetId) {\n    return axios.post('/api/dataease/dataset/detail/${datasetId}', {\n      datasetId: datasetId\n    })\n    },\n  getTableSample(tableCode) {\n    return axios.get(`/data/tables/${tableCode}/sample`)\n  },\n  queryByQuestion(tableCode, question) {\n    return axios.get('/analytics/query', {\n      params: {\n        tableCode,\n        question\n      }\n    })\n  },\n  // 新增：从Redis获取图表数据的API\n  getChartDataById(id) {\n    return axios.get(`http://localhost:8088/api/chart/data/${id}`)\n  }\n}\n\nexport const getData = async (data) => {\n  console.log('调用getData API，请求参数:', data);\n  \n  // 如果传入的配置本身就包含完整数据，直接返回格式化的结果\n  if (data.data) {\n    console.log('配置中已包含数据，直接返回');\n    return {\n      code: 0,\n      msg: 'success',\n      data: data\n    };\n  }\n  \n  // 添加测试模式，如果请求参数包含test-dataset，则返回模拟数据\n  if (data.datasetId === 'test-dataset' || data.tableId === 'test-table') {\n    console.log('使用测试模式，返回模拟数据');\n    return getMockChartData(data.type);\n  }\n  \n  try {\n    // 如果是直接传入的完整数据对象（从AI响应中提取的）\n    if (data.code === 0 && data.data) {\n      console.log('检测到完整API响应格式，直接返回');\n      return data;\n    }\n    \n    // 如果包含chartDataId字段，使用专门的API获取数据\n    if (data.chartDataId) {\n      console.log('检测到chartDataId，从Redis获取数据:', data.chartDataId);\n      const response = await dataApi.getChartDataById(data.chartDataId);\n      console.log('Redis数据获取结果:', response);\n      return response.data;\n    }\n    \n    // 正常API调用\n    console.log('发起API请求:', data);\n    const response = await axios.post('/api/chartData/getData-1', data);\n    console.log('getData API响应结果:', response);\n    \n    // 检查响应格式\n    if (response.data) {\n      console.log('API返回数据:', response.data);\n      return response.data;\n    } else {\n      console.error('响应数据为空');\n      return { code: -1, msg: '响应数据为空', data: null };\n    }\n  } catch (error) {\n    console.error('getData API调用出错:', error);\n    return { code: -1, msg: error.message, data: null };\n    }\n}\n\n// 生成模拟图表数据，用于测试\nfunction getMockChartData(chartType) {\n  const mockData = {\n    code: 0,\n    msg: 'success',\n    data: {\n      type: chartType || 'bar',\n      data: [\n        { field: '类别A', value: 120 },\n        { field: '类别B', value: 200 },\n        { field: '类别C', value: 150 },\n        { field: '类别D', value: 80 },\n        { field: '类别E', value: 170 }\n      ],\n      metrics: [{ name: '销售额' }]\n    }\n  };\n  \n  console.log('生成的模拟数据:', mockData);\n  return mockData;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,MAAMC,OAAO,GAAG;EACrBC,YAAYA,CAAA,EAAG;IACb,OAAOF,KAAK,CAACG,IAAI,CAAC,wBAAwB,EAAC;MACzCC,QAAQ,EAAC;IACX,CAAC,CAAC;EACJ,CAAC;EACAC,gBAAgBA,CAACC,SAAS,EAAE;IAC3B,OAAON,KAAK,CAACG,IAAI,CAAC,2CAA2C,EAAE;MAC7DG,SAAS,EAAEA;IACb,CAAC,CAAC;EACF,CAAC;EACHC,cAAcA,CAACC,SAAS,EAAE;IACxB,OAAOR,KAAK,CAACS,GAAG,CAAC,gBAAgBD,SAAS,SAAS,CAAC;EACtD,CAAC;EACDE,eAAeA,CAACF,SAAS,EAAEG,QAAQ,EAAE;IACnC,OAAOX,KAAK,CAACS,GAAG,CAAC,kBAAkB,EAAE;MACnCG,MAAM,EAAE;QACNJ,SAAS;QACTG;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACD;EACAE,gBAAgBA,CAACC,EAAE,EAAE;IACnB,OAAOd,KAAK,CAACS,GAAG,CAAC,wCAAwCK,EAAE,EAAE,CAAC;EAChE;AACF,CAAC;AAED,OAAO,MAAMC,OAAO,GAAG,MAAOC,IAAI,IAAK;EACrCC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,IAAI,CAAC;;EAExC;EACA,IAAIA,IAAI,CAACA,IAAI,EAAE;IACbC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B,OAAO;MACLC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,SAAS;MACdJ,IAAI,EAAEA;IACR,CAAC;EACH;;EAEA;EACA,IAAIA,IAAI,CAACV,SAAS,KAAK,cAAc,IAAIU,IAAI,CAACK,OAAO,KAAK,YAAY,EAAE;IACtEJ,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC5B,OAAOI,gBAAgB,CAACN,IAAI,CAACO,IAAI,CAAC;EACpC;EAEA,IAAI;IACF;IACA,IAAIP,IAAI,CAACG,IAAI,KAAK,CAAC,IAAIH,IAAI,CAACA,IAAI,EAAE;MAChCC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,OAAOF,IAAI;IACb;;IAEA;IACA,IAAIA,IAAI,CAACQ,WAAW,EAAE;MACpBP,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,IAAI,CAACQ,WAAW,CAAC;MAC3D,MAAMC,QAAQ,GAAG,MAAMxB,OAAO,CAACY,gBAAgB,CAACG,IAAI,CAACQ,WAAW,CAAC;MACjEP,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEO,QAAQ,CAAC;MACrC,OAAOA,QAAQ,CAACT,IAAI;IACtB;;IAEA;IACAC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,IAAI,CAAC;IAC7B,MAAMS,QAAQ,GAAG,MAAMzB,KAAK,CAACG,IAAI,CAAC,0BAA0B,EAAEa,IAAI,CAAC;IACnEC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEO,QAAQ,CAAC;;IAEzC;IACA,IAAIA,QAAQ,CAACT,IAAI,EAAE;MACjBC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEO,QAAQ,CAACT,IAAI,CAAC;MACtC,OAAOS,QAAQ,CAACT,IAAI;IACtB,CAAC,MAAM;MACLC,OAAO,CAACS,KAAK,CAAC,QAAQ,CAAC;MACvB,OAAO;QAAEP,IAAI,EAAE,CAAC,CAAC;QAAEC,GAAG,EAAE,QAAQ;QAAEJ,IAAI,EAAE;MAAK,CAAC;IAChD;EACF,CAAC,CAAC,OAAOU,KAAK,EAAE;IACdT,OAAO,CAACS,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;IACxC,OAAO;MAAEP,IAAI,EAAE,CAAC,CAAC;MAAEC,GAAG,EAAEM,KAAK,CAACC,OAAO;MAAEX,IAAI,EAAE;IAAK,CAAC;EACnD;AACJ,CAAC;;AAED;AACA,SAASM,gBAAgBA,CAACM,SAAS,EAAE;EACnC,MAAMC,QAAQ,GAAG;IACfV,IAAI,EAAE,CAAC;IACPC,GAAG,EAAE,SAAS;IACdJ,IAAI,EAAE;MACJO,IAAI,EAAEK,SAAS,IAAI,KAAK;MACxBZ,IAAI,EAAE,CACJ;QAAEc,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAED,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAED,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI,CAAC,EAC5B;QAAED,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAG,CAAC,EAC3B;QAAED,KAAK,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI,CAAC,CAC7B;MACDC,OAAO,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAM,CAAC;IAC3B;EACF,CAAC;EAEDhB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEW,QAAQ,CAAC;EACjC,OAAOA,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}