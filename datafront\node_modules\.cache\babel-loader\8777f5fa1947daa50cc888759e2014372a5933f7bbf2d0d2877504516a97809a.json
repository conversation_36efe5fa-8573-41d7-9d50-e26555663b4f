{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport * as modelUtil from '../util/model.js';\nimport ComponentModel from './Component.js';\nimport { PaletteMixin } from './mixin/palette.js';\nimport { DataFormatMixin } from '../model/mixin/dataFormat.js';\nimport { getLayoutParams, mergeLayoutParam, fetchLayoutMode } from '../util/layout.js';\nimport { createTask } from '../core/task.js';\nimport { mountExtend } from '../util/clazz.js';\nimport { SourceManager } from '../data/helper/sourceManager.js';\nimport { defaultSeriesFormatTooltip } from '../component/tooltip/seriesFormatTooltip.js';\nvar inner = modelUtil.makeInner();\nfunction getSelectionKey(data, dataIndex) {\n  return data.getName(dataIndex) || data.getId(dataIndex);\n}\nexport var SERIES_UNIVERSAL_TRANSITION_PROP = '__universalTransitionEnabled';\nvar SeriesModel = /** @class */function (_super) {\n  __extends(SeriesModel, _super);\n  function SeriesModel() {\n    // [Caution]: Because this class or desecendants can be used as `XXX.extend(subProto)`,\n    // the class members must not be initialized in constructor or declaration place.\n    // Otherwise there is bad case:\n    //   class A {xxx = 1;}\n    //   enableClassExtend(A);\n    //   class B extends A {}\n    //   var C = B.extend({xxx: 5});\n    //   var c = new C();\n    //   console.log(c.xxx); // expect 5 but always 1.\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    // ---------------------------------------\n    // Props about data selection\n    // ---------------------------------------\n    _this._selectedDataIndicesMap = {};\n    return _this;\n  }\n  SeriesModel.prototype.init = function (option, parentModel, ecModel) {\n    this.seriesIndex = this.componentIndex;\n    this.dataTask = createTask({\n      count: dataTaskCount,\n      reset: dataTaskReset\n    });\n    this.dataTask.context = {\n      model: this\n    };\n    this.mergeDefaultAndTheme(option, ecModel);\n    var sourceManager = inner(this).sourceManager = new SourceManager(this);\n    sourceManager.prepareSource();\n    var data = this.getInitialData(option, ecModel);\n    wrapData(data, this);\n    this.dataTask.context.data = data;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.assert(data, 'getInitialData returned invalid data.');\n    }\n    inner(this).dataBeforeProcessed = data;\n    // If we reverse the order (make data firstly, and then make\n    // dataBeforeProcessed by cloneShallow), cloneShallow will\n    // cause data.graph.data !== data when using\n    // module:echarts/data/Graph or module:echarts/data/Tree.\n    // See module:echarts/data/helper/linkSeriesData\n    // Theoretically, it is unreasonable to call `seriesModel.getData()` in the model\n    // init or merge stage, because the data can be restored. So we do not `restoreData`\n    // and `setData` here, which forbids calling `seriesModel.getData()` in this stage.\n    // Call `seriesModel.getRawData()` instead.\n    // this.restoreData();\n    autoSeriesName(this);\n    this._initSelectedMapFromData(data);\n  };\n  /**\r\n   * Util for merge default and theme to option\r\n   */\n  SeriesModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n    var layoutMode = fetchLayoutMode(this);\n    var inputPositionParams = layoutMode ? getLayoutParams(option) : {};\n    // Backward compat: using subType on theme.\n    // But if name duplicate between series subType\n    // (for example: parallel) add component mainType,\n    // add suffix 'Series'.\n    var themeSubType = this.subType;\n    if (ComponentModel.hasClass(themeSubType)) {\n      themeSubType += 'Series';\n    }\n    zrUtil.merge(option, ecModel.getTheme().get(this.subType));\n    zrUtil.merge(option, this.getDefaultOption());\n    // Default label emphasis `show`\n    modelUtil.defaultEmphasis(option, 'label', ['show']);\n    this.fillDataTextStyle(option.data);\n    if (layoutMode) {\n      mergeLayoutParam(option, inputPositionParams, layoutMode);\n    }\n  };\n  SeriesModel.prototype.mergeOption = function (newSeriesOption, ecModel) {\n    // this.settingTask.dirty();\n    newSeriesOption = zrUtil.merge(this.option, newSeriesOption, true);\n    this.fillDataTextStyle(newSeriesOption.data);\n    var layoutMode = fetchLayoutMode(this);\n    if (layoutMode) {\n      mergeLayoutParam(this.option, newSeriesOption, layoutMode);\n    }\n    var sourceManager = inner(this).sourceManager;\n    sourceManager.dirty();\n    sourceManager.prepareSource();\n    var data = this.getInitialData(newSeriesOption, ecModel);\n    wrapData(data, this);\n    this.dataTask.dirty();\n    this.dataTask.context.data = data;\n    inner(this).dataBeforeProcessed = data;\n    autoSeriesName(this);\n    this._initSelectedMapFromData(data);\n  };\n  SeriesModel.prototype.fillDataTextStyle = function (data) {\n    // Default data label emphasis `show`\n    // FIXME Tree structure data ?\n    // FIXME Performance ?\n    if (data && !zrUtil.isTypedArray(data)) {\n      var props = ['show'];\n      for (var i = 0; i < data.length; i++) {\n        if (data[i] && data[i].label) {\n          modelUtil.defaultEmphasis(data[i], 'label', props);\n        }\n      }\n    }\n  };\n  /**\r\n   * Init a data structure from data related option in series\r\n   * Must be overridden.\r\n   */\n  SeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return;\n  };\n  /**\r\n   * Append data to list\r\n   */\n  SeriesModel.prototype.appendData = function (params) {\n    // FIXME ???\n    // (1) If data from dataset, forbidden append.\n    // (2) support append data of dataset.\n    var data = this.getRawData();\n    data.appendData(params.data);\n  };\n  /**\r\n   * Consider some method like `filter`, `map` need make new data,\r\n   * We should make sure that `seriesModel.getData()` get correct\r\n   * data in the stream procedure. So we fetch data from upstream\r\n   * each time `task.perform` called.\r\n   */\n  SeriesModel.prototype.getData = function (dataType) {\n    var task = getCurrentTask(this);\n    if (task) {\n      var data = task.context.data;\n      return dataType == null || !data.getLinkedData ? data : data.getLinkedData(dataType);\n    } else {\n      // When series is not alive (that may happen when click toolbox\n      // restore or setOption with not merge mode), series data may\n      // be still need to judge animation or something when graphic\n      // elements want to know whether fade out.\n      return inner(this).data;\n    }\n  };\n  SeriesModel.prototype.getAllData = function () {\n    var mainData = this.getData();\n    return mainData && mainData.getLinkedDataAll ? mainData.getLinkedDataAll() : [{\n      data: mainData\n    }];\n  };\n  SeriesModel.prototype.setData = function (data) {\n    var task = getCurrentTask(this);\n    if (task) {\n      var context = task.context;\n      // Consider case: filter, data sample.\n      // FIXME:TS never used, so comment it\n      // if (context.data !== data && task.modifyOutputEnd) {\n      //     task.setOutputEnd(data.count());\n      // }\n      context.outputData = data;\n      // Caution: setData should update context.data,\n      // Because getData may be called multiply in a\n      // single stage and expect to get the data just\n      // set. (For example, AxisProxy, x y both call\n      // getData and setDate sequentially).\n      // So the context.data should be fetched from\n      // upstream each time when a stage starts to be\n      // performed.\n      if (task !== this.dataTask) {\n        context.data = data;\n      }\n    }\n    inner(this).data = data;\n  };\n  SeriesModel.prototype.getEncode = function () {\n    var encode = this.get('encode', true);\n    if (encode) {\n      return zrUtil.createHashMap(encode);\n    }\n  };\n  SeriesModel.prototype.getSourceManager = function () {\n    return inner(this).sourceManager;\n  };\n  SeriesModel.prototype.getSource = function () {\n    return this.getSourceManager().getSource();\n  };\n  /**\r\n   * Get data before processed\r\n   */\n  SeriesModel.prototype.getRawData = function () {\n    return inner(this).dataBeforeProcessed;\n  };\n  SeriesModel.prototype.getColorBy = function () {\n    var colorBy = this.get('colorBy');\n    return colorBy || 'series';\n  };\n  SeriesModel.prototype.isColorBySeries = function () {\n    return this.getColorBy() === 'series';\n  };\n  /**\r\n   * Get base axis if has coordinate system and has axis.\r\n   * By default use coordSys.getBaseAxis();\r\n   * Can be overridden for some chart.\r\n   * @return {type} description\r\n   */\n  SeriesModel.prototype.getBaseAxis = function () {\n    var coordSys = this.coordinateSystem;\n    // @ts-ignore\n    return coordSys && coordSys.getBaseAxis && coordSys.getBaseAxis();\n  };\n  /**\r\n   * Default tooltip formatter\r\n   *\r\n   * @param dataIndex\r\n   * @param multipleSeries\r\n   * @param dataType\r\n   * @param renderMode valid values: 'html'(by default) and 'richText'.\r\n   *        'html' is used for rendering tooltip in extra DOM form, and the result\r\n   *        string is used as DOM HTML content.\r\n   *        'richText' is used for rendering tooltip in rich text form, for those where\r\n   *        DOM operation is not supported.\r\n   * @return formatted tooltip with `html` and `markers`\r\n   *        Notice: The override method can also return string\r\n   */\n  SeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    return defaultSeriesFormatTooltip({\n      series: this,\n      dataIndex: dataIndex,\n      multipleSeries: multipleSeries\n    });\n  };\n  SeriesModel.prototype.isAnimationEnabled = function () {\n    var ecModel = this.ecModel;\n    // Disable animation if using echarts in node but not give ssr flag.\n    // In ssr mode, renderToString will generate svg with css animation.\n    if (env.node && !(ecModel && ecModel.ssr)) {\n      return false;\n    }\n    var animationEnabled = this.getShallow('animation');\n    if (animationEnabled) {\n      if (this.getData().count() > this.getShallow('animationThreshold')) {\n        animationEnabled = false;\n      }\n    }\n    return !!animationEnabled;\n  };\n  SeriesModel.prototype.restoreData = function () {\n    this.dataTask.dirty();\n  };\n  SeriesModel.prototype.getColorFromPalette = function (name, scope, requestColorNum) {\n    var ecModel = this.ecModel;\n    // PENDING\n    var color = PaletteMixin.prototype.getColorFromPalette.call(this, name, scope, requestColorNum);\n    if (!color) {\n      color = ecModel.getColorFromPalette(name, scope, requestColorNum);\n    }\n    return color;\n  };\n  /**\r\n   * Use `data.mapDimensionsAll(coordDim)` instead.\r\n   * @deprecated\r\n   */\n  SeriesModel.prototype.coordDimToDataDim = function (coordDim) {\n    return this.getRawData().mapDimensionsAll(coordDim);\n  };\n  /**\r\n   * Get progressive rendering count each step\r\n   */\n  SeriesModel.prototype.getProgressive = function () {\n    return this.get('progressive');\n  };\n  /**\r\n   * Get progressive rendering count each step\r\n   */\n  SeriesModel.prototype.getProgressiveThreshold = function () {\n    return this.get('progressiveThreshold');\n  };\n  // PENGING If selectedMode is null ?\n  SeriesModel.prototype.select = function (innerDataIndices, dataType) {\n    this._innerSelect(this.getData(dataType), innerDataIndices);\n  };\n  SeriesModel.prototype.unselect = function (innerDataIndices, dataType) {\n    var selectedMap = this.option.selectedMap;\n    if (!selectedMap) {\n      return;\n    }\n    var selectedMode = this.option.selectedMode;\n    var data = this.getData(dataType);\n    if (selectedMode === 'series' || selectedMap === 'all') {\n      this.option.selectedMap = {};\n      this._selectedDataIndicesMap = {};\n      return;\n    }\n    for (var i = 0; i < innerDataIndices.length; i++) {\n      var dataIndex = innerDataIndices[i];\n      var nameOrId = getSelectionKey(data, dataIndex);\n      selectedMap[nameOrId] = false;\n      this._selectedDataIndicesMap[nameOrId] = -1;\n    }\n  };\n  SeriesModel.prototype.toggleSelect = function (innerDataIndices, dataType) {\n    var tmpArr = [];\n    for (var i = 0; i < innerDataIndices.length; i++) {\n      tmpArr[0] = innerDataIndices[i];\n      this.isSelected(innerDataIndices[i], dataType) ? this.unselect(tmpArr, dataType) : this.select(tmpArr, dataType);\n    }\n  };\n  SeriesModel.prototype.getSelectedDataIndices = function () {\n    if (this.option.selectedMap === 'all') {\n      return [].slice.call(this.getData().getIndices());\n    }\n    var selectedDataIndicesMap = this._selectedDataIndicesMap;\n    var nameOrIds = zrUtil.keys(selectedDataIndicesMap);\n    var dataIndices = [];\n    for (var i = 0; i < nameOrIds.length; i++) {\n      var dataIndex = selectedDataIndicesMap[nameOrIds[i]];\n      if (dataIndex >= 0) {\n        dataIndices.push(dataIndex);\n      }\n    }\n    return dataIndices;\n  };\n  SeriesModel.prototype.isSelected = function (dataIndex, dataType) {\n    var selectedMap = this.option.selectedMap;\n    if (!selectedMap) {\n      return false;\n    }\n    var data = this.getData(dataType);\n    return (selectedMap === 'all' || selectedMap[getSelectionKey(data, dataIndex)]) && !data.getItemModel(dataIndex).get(['select', 'disabled']);\n  };\n  SeriesModel.prototype.isUniversalTransitionEnabled = function () {\n    if (this[SERIES_UNIVERSAL_TRANSITION_PROP]) {\n      return true;\n    }\n    var universalTransitionOpt = this.option.universalTransition;\n    // Quick reject\n    if (!universalTransitionOpt) {\n      return false;\n    }\n    if (universalTransitionOpt === true) {\n      return true;\n    }\n    // Can be simply 'universalTransition: true'\n    return universalTransitionOpt && universalTransitionOpt.enabled;\n  };\n  SeriesModel.prototype._innerSelect = function (data, innerDataIndices) {\n    var _a, _b;\n    var option = this.option;\n    var selectedMode = option.selectedMode;\n    var len = innerDataIndices.length;\n    if (!selectedMode || !len) {\n      return;\n    }\n    if (selectedMode === 'series') {\n      option.selectedMap = 'all';\n    } else if (selectedMode === 'multiple') {\n      if (!zrUtil.isObject(option.selectedMap)) {\n        option.selectedMap = {};\n      }\n      var selectedMap = option.selectedMap;\n      for (var i = 0; i < len; i++) {\n        var dataIndex = innerDataIndices[i];\n        // TODO different types of data share same object.\n        var nameOrId = getSelectionKey(data, dataIndex);\n        selectedMap[nameOrId] = true;\n        this._selectedDataIndicesMap[nameOrId] = data.getRawIndex(dataIndex);\n      }\n    } else if (selectedMode === 'single' || selectedMode === true) {\n      var lastDataIndex = innerDataIndices[len - 1];\n      var nameOrId = getSelectionKey(data, lastDataIndex);\n      option.selectedMap = (_a = {}, _a[nameOrId] = true, _a);\n      this._selectedDataIndicesMap = (_b = {}, _b[nameOrId] = data.getRawIndex(lastDataIndex), _b);\n    }\n  };\n  SeriesModel.prototype._initSelectedMapFromData = function (data) {\n    // Ignore select info in data if selectedMap exists.\n    // NOTE It's only for legacy usage. edge data is not supported.\n    if (this.option.selectedMap) {\n      return;\n    }\n    var dataIndices = [];\n    if (data.hasItemOption) {\n      data.each(function (idx) {\n        var rawItem = data.getRawDataItem(idx);\n        if (rawItem && rawItem.selected) {\n          dataIndices.push(idx);\n        }\n      });\n    }\n    if (dataIndices.length > 0) {\n      this._innerSelect(data, dataIndices);\n    }\n  };\n  // /**\n  //  * @see {module:echarts/stream/Scheduler}\n  //  */\n  // abstract pipeTask: null\n  SeriesModel.registerClass = function (clz) {\n    return ComponentModel.registerClass(clz);\n  };\n  SeriesModel.protoInitialize = function () {\n    var proto = SeriesModel.prototype;\n    proto.type = 'series.__base__';\n    proto.seriesIndex = 0;\n    proto.ignoreStyleOnData = false;\n    proto.hasSymbolVisual = false;\n    proto.defaultSymbol = 'circle';\n    // Make sure the values can be accessed!\n    proto.visualStyleAccessPath = 'itemStyle';\n    proto.visualDrawType = 'fill';\n  }();\n  return SeriesModel;\n}(ComponentModel);\nzrUtil.mixin(SeriesModel, DataFormatMixin);\nzrUtil.mixin(SeriesModel, PaletteMixin);\nmountExtend(SeriesModel, ComponentModel);\n/**\r\n * MUST be called after `prepareSource` called\r\n * Here we need to make auto series, especially for auto legend. But we\r\n * do not modify series.name in option to avoid side effects.\r\n */\nfunction autoSeriesName(seriesModel) {\n  // User specified name has higher priority, otherwise it may cause\n  // series can not be queried unexpectedly.\n  var name = seriesModel.name;\n  if (!modelUtil.isNameSpecified(seriesModel)) {\n    seriesModel.name = getSeriesAutoName(seriesModel) || name;\n  }\n}\nfunction getSeriesAutoName(seriesModel) {\n  var data = seriesModel.getRawData();\n  var dataDims = data.mapDimensionsAll('seriesName');\n  var nameArr = [];\n  zrUtil.each(dataDims, function (dataDim) {\n    var dimInfo = data.getDimensionInfo(dataDim);\n    dimInfo.displayName && nameArr.push(dimInfo.displayName);\n  });\n  return nameArr.join(' ');\n}\nfunction dataTaskCount(context) {\n  return context.model.getRawData().count();\n}\nfunction dataTaskReset(context) {\n  var seriesModel = context.model;\n  seriesModel.setData(seriesModel.getRawData().cloneShallow());\n  return dataTaskProgress;\n}\nfunction dataTaskProgress(param, context) {\n  // Avoid repeat cloneShallow when data just created in reset.\n  if (context.outputData && param.end > context.outputData.count()) {\n    context.model.getRawData().cloneShallow(context.outputData);\n  }\n}\n// TODO refactor\nfunction wrapData(data, seriesModel) {\n  zrUtil.each(zrUtil.concatArray(data.CHANGABLE_METHODS, data.DOWNSAMPLE_METHODS), function (methodName) {\n    data.wrapMethod(methodName, zrUtil.curry(onDataChange, seriesModel));\n  });\n}\nfunction onDataChange(seriesModel, newList) {\n  var task = getCurrentTask(seriesModel);\n  if (task) {\n    // Consider case: filter, selectRange\n    task.setOutputEnd((newList || this).count());\n  }\n  return newList;\n}\nfunction getCurrentTask(seriesModel) {\n  var scheduler = (seriesModel.ecModel || {}).scheduler;\n  var pipeline = scheduler && scheduler.getPipeline(seriesModel.uid);\n  if (pipeline) {\n    // When pipline finished, the currrentTask keep the last\n    // task (renderTask).\n    var task = pipeline.currentTask;\n    if (task) {\n      var agentStubMap = task.agentStubMap;\n      if (agentStubMap) {\n        task = agentStubMap.get(seriesModel.uid);\n      }\n    }\n    return task;\n  }\n}\nexport default SeriesModel;", "map": {"version": 3, "names": ["__extends", "zrUtil", "env", "modelUtil", "ComponentModel", "Palette<PERSON><PERSON>in", "DataFormatMixin", "getLayoutParams", "mergeLayoutParam", "fetchLayoutMode", "createTask", "mountExtend", "SourceManager", "defaultSeriesFormatTooltip", "inner", "makeInner", "getSelectionKey", "data", "dataIndex", "getName", "getId", "SERIES_UNIVERSAL_TRANSITION_PROP", "SeriesModel", "_super", "_this", "apply", "arguments", "_selectedDataIndicesMap", "prototype", "init", "option", "parentModel", "ecModel", "seriesIndex", "componentIndex", "dataTask", "count", "dataTaskCount", "reset", "dataTaskReset", "context", "model", "mergeDefaultAndTheme", "sourceManager", "prepareSource", "getInitialData", "wrapData", "process", "NODE_ENV", "assert", "dataBeforeProcessed", "autoSeriesName", "_initSelectedMapFromData", "layoutMode", "inputPositionParams", "themeSubType", "subType", "hasClass", "merge", "getTheme", "get", "getDefaultOption", "defaultEmphasis", "fillDataTextStyle", "mergeOption", "newSeriesOption", "dirty", "isTypedArray", "props", "i", "length", "label", "appendData", "params", "getRawData", "getData", "dataType", "task", "getCurrentTask", "getLinkedData", "getAllData", "mainData", "getLinkedDataAll", "setData", "outputData", "getEncode", "encode", "createHashMap", "getSourceManager", "getSource", "getColorBy", "colorBy", "isColorBySeries", "getBaseAxis", "coordSys", "coordinateSystem", "formatTooltip", "multipleSeries", "series", "isAnimationEnabled", "node", "ssr", "animationEnabled", "getShallow", "restoreData", "getColorFromPalette", "name", "scope", "requestColorNum", "color", "call", "coordDimToDataDim", "coordDim", "mapDimensionsAll", "getProgressive", "getProgressiveThreshold", "select", "innerDataIndices", "_innerSelect", "unselect", "selectedMap", "selectedMode", "nameOrId", "toggleSelect", "tmpArr", "isSelected", "getSelectedDataIndices", "slice", "getIndices", "selectedDataIndicesMap", "nameOrIds", "keys", "dataIndices", "push", "getItemModel", "isUniversalTransitionEnabled", "universalTransitionOpt", "universalTransition", "enabled", "_a", "_b", "len", "isObject", "getRawIndex", "lastDataIndex", "hasItemOption", "each", "idx", "rawItem", "getRawDataItem", "selected", "registerClass", "clz", "protoInitialize", "proto", "type", "ignoreStyleOnData", "hasSymbolVisual", "defaultSymbol", "visualStyleAccessPath", "visualDrawType", "mixin", "seriesModel", "isNameSpecified", "getSeriesAutoName", "dataDims", "nameArr", "dataDim", "dimInfo", "getDimensionInfo", "displayName", "join", "cloneShallow", "dataTaskProgress", "param", "end", "concatArray", "CHANGABLE_METHODS", "DOWNSAMPLE_METHODS", "methodName", "wrapMethod", "curry", "onDataChange", "newList", "setOutputEnd", "scheduler", "pipeline", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uid", "currentTask", "agentStubMap"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/model/Series.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport * as modelUtil from '../util/model.js';\nimport ComponentModel from './Component.js';\nimport { PaletteMixin } from './mixin/palette.js';\nimport { DataFormatMixin } from '../model/mixin/dataFormat.js';\nimport { getLayoutParams, mergeLayoutParam, fetchLayoutMode } from '../util/layout.js';\nimport { createTask } from '../core/task.js';\nimport { mountExtend } from '../util/clazz.js';\nimport { SourceManager } from '../data/helper/sourceManager.js';\nimport { defaultSeriesFormatTooltip } from '../component/tooltip/seriesFormatTooltip.js';\nvar inner = modelUtil.makeInner();\nfunction getSelectionKey(data, dataIndex) {\n  return data.getName(dataIndex) || data.getId(dataIndex);\n}\nexport var SERIES_UNIVERSAL_TRANSITION_PROP = '__universalTransitionEnabled';\nvar SeriesModel = /** @class */function (_super) {\n  __extends(SeriesModel, _super);\n  function SeriesModel() {\n    // [Caution]: Because this class or desecendants can be used as `XXX.extend(subProto)`,\n    // the class members must not be initialized in constructor or declaration place.\n    // Otherwise there is bad case:\n    //   class A {xxx = 1;}\n    //   enableClassExtend(A);\n    //   class B extends A {}\n    //   var C = B.extend({xxx: 5});\n    //   var c = new C();\n    //   console.log(c.xxx); // expect 5 but always 1.\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    // ---------------------------------------\n    // Props about data selection\n    // ---------------------------------------\n    _this._selectedDataIndicesMap = {};\n    return _this;\n  }\n  SeriesModel.prototype.init = function (option, parentModel, ecModel) {\n    this.seriesIndex = this.componentIndex;\n    this.dataTask = createTask({\n      count: dataTaskCount,\n      reset: dataTaskReset\n    });\n    this.dataTask.context = {\n      model: this\n    };\n    this.mergeDefaultAndTheme(option, ecModel);\n    var sourceManager = inner(this).sourceManager = new SourceManager(this);\n    sourceManager.prepareSource();\n    var data = this.getInitialData(option, ecModel);\n    wrapData(data, this);\n    this.dataTask.context.data = data;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.assert(data, 'getInitialData returned invalid data.');\n    }\n    inner(this).dataBeforeProcessed = data;\n    // If we reverse the order (make data firstly, and then make\n    // dataBeforeProcessed by cloneShallow), cloneShallow will\n    // cause data.graph.data !== data when using\n    // module:echarts/data/Graph or module:echarts/data/Tree.\n    // See module:echarts/data/helper/linkSeriesData\n    // Theoretically, it is unreasonable to call `seriesModel.getData()` in the model\n    // init or merge stage, because the data can be restored. So we do not `restoreData`\n    // and `setData` here, which forbids calling `seriesModel.getData()` in this stage.\n    // Call `seriesModel.getRawData()` instead.\n    // this.restoreData();\n    autoSeriesName(this);\n    this._initSelectedMapFromData(data);\n  };\n  /**\r\n   * Util for merge default and theme to option\r\n   */\n  SeriesModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n    var layoutMode = fetchLayoutMode(this);\n    var inputPositionParams = layoutMode ? getLayoutParams(option) : {};\n    // Backward compat: using subType on theme.\n    // But if name duplicate between series subType\n    // (for example: parallel) add component mainType,\n    // add suffix 'Series'.\n    var themeSubType = this.subType;\n    if (ComponentModel.hasClass(themeSubType)) {\n      themeSubType += 'Series';\n    }\n    zrUtil.merge(option, ecModel.getTheme().get(this.subType));\n    zrUtil.merge(option, this.getDefaultOption());\n    // Default label emphasis `show`\n    modelUtil.defaultEmphasis(option, 'label', ['show']);\n    this.fillDataTextStyle(option.data);\n    if (layoutMode) {\n      mergeLayoutParam(option, inputPositionParams, layoutMode);\n    }\n  };\n  SeriesModel.prototype.mergeOption = function (newSeriesOption, ecModel) {\n    // this.settingTask.dirty();\n    newSeriesOption = zrUtil.merge(this.option, newSeriesOption, true);\n    this.fillDataTextStyle(newSeriesOption.data);\n    var layoutMode = fetchLayoutMode(this);\n    if (layoutMode) {\n      mergeLayoutParam(this.option, newSeriesOption, layoutMode);\n    }\n    var sourceManager = inner(this).sourceManager;\n    sourceManager.dirty();\n    sourceManager.prepareSource();\n    var data = this.getInitialData(newSeriesOption, ecModel);\n    wrapData(data, this);\n    this.dataTask.dirty();\n    this.dataTask.context.data = data;\n    inner(this).dataBeforeProcessed = data;\n    autoSeriesName(this);\n    this._initSelectedMapFromData(data);\n  };\n  SeriesModel.prototype.fillDataTextStyle = function (data) {\n    // Default data label emphasis `show`\n    // FIXME Tree structure data ?\n    // FIXME Performance ?\n    if (data && !zrUtil.isTypedArray(data)) {\n      var props = ['show'];\n      for (var i = 0; i < data.length; i++) {\n        if (data[i] && data[i].label) {\n          modelUtil.defaultEmphasis(data[i], 'label', props);\n        }\n      }\n    }\n  };\n  /**\r\n   * Init a data structure from data related option in series\r\n   * Must be overridden.\r\n   */\n  SeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return;\n  };\n  /**\r\n   * Append data to list\r\n   */\n  SeriesModel.prototype.appendData = function (params) {\n    // FIXME ???\n    // (1) If data from dataset, forbidden append.\n    // (2) support append data of dataset.\n    var data = this.getRawData();\n    data.appendData(params.data);\n  };\n  /**\r\n   * Consider some method like `filter`, `map` need make new data,\r\n   * We should make sure that `seriesModel.getData()` get correct\r\n   * data in the stream procedure. So we fetch data from upstream\r\n   * each time `task.perform` called.\r\n   */\n  SeriesModel.prototype.getData = function (dataType) {\n    var task = getCurrentTask(this);\n    if (task) {\n      var data = task.context.data;\n      return dataType == null || !data.getLinkedData ? data : data.getLinkedData(dataType);\n    } else {\n      // When series is not alive (that may happen when click toolbox\n      // restore or setOption with not merge mode), series data may\n      // be still need to judge animation or something when graphic\n      // elements want to know whether fade out.\n      return inner(this).data;\n    }\n  };\n  SeriesModel.prototype.getAllData = function () {\n    var mainData = this.getData();\n    return mainData && mainData.getLinkedDataAll ? mainData.getLinkedDataAll() : [{\n      data: mainData\n    }];\n  };\n  SeriesModel.prototype.setData = function (data) {\n    var task = getCurrentTask(this);\n    if (task) {\n      var context = task.context;\n      // Consider case: filter, data sample.\n      // FIXME:TS never used, so comment it\n      // if (context.data !== data && task.modifyOutputEnd) {\n      //     task.setOutputEnd(data.count());\n      // }\n      context.outputData = data;\n      // Caution: setData should update context.data,\n      // Because getData may be called multiply in a\n      // single stage and expect to get the data just\n      // set. (For example, AxisProxy, x y both call\n      // getData and setDate sequentially).\n      // So the context.data should be fetched from\n      // upstream each time when a stage starts to be\n      // performed.\n      if (task !== this.dataTask) {\n        context.data = data;\n      }\n    }\n    inner(this).data = data;\n  };\n  SeriesModel.prototype.getEncode = function () {\n    var encode = this.get('encode', true);\n    if (encode) {\n      return zrUtil.createHashMap(encode);\n    }\n  };\n  SeriesModel.prototype.getSourceManager = function () {\n    return inner(this).sourceManager;\n  };\n  SeriesModel.prototype.getSource = function () {\n    return this.getSourceManager().getSource();\n  };\n  /**\r\n   * Get data before processed\r\n   */\n  SeriesModel.prototype.getRawData = function () {\n    return inner(this).dataBeforeProcessed;\n  };\n  SeriesModel.prototype.getColorBy = function () {\n    var colorBy = this.get('colorBy');\n    return colorBy || 'series';\n  };\n  SeriesModel.prototype.isColorBySeries = function () {\n    return this.getColorBy() === 'series';\n  };\n  /**\r\n   * Get base axis if has coordinate system and has axis.\r\n   * By default use coordSys.getBaseAxis();\r\n   * Can be overridden for some chart.\r\n   * @return {type} description\r\n   */\n  SeriesModel.prototype.getBaseAxis = function () {\n    var coordSys = this.coordinateSystem;\n    // @ts-ignore\n    return coordSys && coordSys.getBaseAxis && coordSys.getBaseAxis();\n  };\n  /**\r\n   * Default tooltip formatter\r\n   *\r\n   * @param dataIndex\r\n   * @param multipleSeries\r\n   * @param dataType\r\n   * @param renderMode valid values: 'html'(by default) and 'richText'.\r\n   *        'html' is used for rendering tooltip in extra DOM form, and the result\r\n   *        string is used as DOM HTML content.\r\n   *        'richText' is used for rendering tooltip in rich text form, for those where\r\n   *        DOM operation is not supported.\r\n   * @return formatted tooltip with `html` and `markers`\r\n   *        Notice: The override method can also return string\r\n   */\n  SeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    return defaultSeriesFormatTooltip({\n      series: this,\n      dataIndex: dataIndex,\n      multipleSeries: multipleSeries\n    });\n  };\n  SeriesModel.prototype.isAnimationEnabled = function () {\n    var ecModel = this.ecModel;\n    // Disable animation if using echarts in node but not give ssr flag.\n    // In ssr mode, renderToString will generate svg with css animation.\n    if (env.node && !(ecModel && ecModel.ssr)) {\n      return false;\n    }\n    var animationEnabled = this.getShallow('animation');\n    if (animationEnabled) {\n      if (this.getData().count() > this.getShallow('animationThreshold')) {\n        animationEnabled = false;\n      }\n    }\n    return !!animationEnabled;\n  };\n  SeriesModel.prototype.restoreData = function () {\n    this.dataTask.dirty();\n  };\n  SeriesModel.prototype.getColorFromPalette = function (name, scope, requestColorNum) {\n    var ecModel = this.ecModel;\n    // PENDING\n    var color = PaletteMixin.prototype.getColorFromPalette.call(this, name, scope, requestColorNum);\n    if (!color) {\n      color = ecModel.getColorFromPalette(name, scope, requestColorNum);\n    }\n    return color;\n  };\n  /**\r\n   * Use `data.mapDimensionsAll(coordDim)` instead.\r\n   * @deprecated\r\n   */\n  SeriesModel.prototype.coordDimToDataDim = function (coordDim) {\n    return this.getRawData().mapDimensionsAll(coordDim);\n  };\n  /**\r\n   * Get progressive rendering count each step\r\n   */\n  SeriesModel.prototype.getProgressive = function () {\n    return this.get('progressive');\n  };\n  /**\r\n   * Get progressive rendering count each step\r\n   */\n  SeriesModel.prototype.getProgressiveThreshold = function () {\n    return this.get('progressiveThreshold');\n  };\n  // PENGING If selectedMode is null ?\n  SeriesModel.prototype.select = function (innerDataIndices, dataType) {\n    this._innerSelect(this.getData(dataType), innerDataIndices);\n  };\n  SeriesModel.prototype.unselect = function (innerDataIndices, dataType) {\n    var selectedMap = this.option.selectedMap;\n    if (!selectedMap) {\n      return;\n    }\n    var selectedMode = this.option.selectedMode;\n    var data = this.getData(dataType);\n    if (selectedMode === 'series' || selectedMap === 'all') {\n      this.option.selectedMap = {};\n      this._selectedDataIndicesMap = {};\n      return;\n    }\n    for (var i = 0; i < innerDataIndices.length; i++) {\n      var dataIndex = innerDataIndices[i];\n      var nameOrId = getSelectionKey(data, dataIndex);\n      selectedMap[nameOrId] = false;\n      this._selectedDataIndicesMap[nameOrId] = -1;\n    }\n  };\n  SeriesModel.prototype.toggleSelect = function (innerDataIndices, dataType) {\n    var tmpArr = [];\n    for (var i = 0; i < innerDataIndices.length; i++) {\n      tmpArr[0] = innerDataIndices[i];\n      this.isSelected(innerDataIndices[i], dataType) ? this.unselect(tmpArr, dataType) : this.select(tmpArr, dataType);\n    }\n  };\n  SeriesModel.prototype.getSelectedDataIndices = function () {\n    if (this.option.selectedMap === 'all') {\n      return [].slice.call(this.getData().getIndices());\n    }\n    var selectedDataIndicesMap = this._selectedDataIndicesMap;\n    var nameOrIds = zrUtil.keys(selectedDataIndicesMap);\n    var dataIndices = [];\n    for (var i = 0; i < nameOrIds.length; i++) {\n      var dataIndex = selectedDataIndicesMap[nameOrIds[i]];\n      if (dataIndex >= 0) {\n        dataIndices.push(dataIndex);\n      }\n    }\n    return dataIndices;\n  };\n  SeriesModel.prototype.isSelected = function (dataIndex, dataType) {\n    var selectedMap = this.option.selectedMap;\n    if (!selectedMap) {\n      return false;\n    }\n    var data = this.getData(dataType);\n    return (selectedMap === 'all' || selectedMap[getSelectionKey(data, dataIndex)]) && !data.getItemModel(dataIndex).get(['select', 'disabled']);\n  };\n  SeriesModel.prototype.isUniversalTransitionEnabled = function () {\n    if (this[SERIES_UNIVERSAL_TRANSITION_PROP]) {\n      return true;\n    }\n    var universalTransitionOpt = this.option.universalTransition;\n    // Quick reject\n    if (!universalTransitionOpt) {\n      return false;\n    }\n    if (universalTransitionOpt === true) {\n      return true;\n    }\n    // Can be simply 'universalTransition: true'\n    return universalTransitionOpt && universalTransitionOpt.enabled;\n  };\n  SeriesModel.prototype._innerSelect = function (data, innerDataIndices) {\n    var _a, _b;\n    var option = this.option;\n    var selectedMode = option.selectedMode;\n    var len = innerDataIndices.length;\n    if (!selectedMode || !len) {\n      return;\n    }\n    if (selectedMode === 'series') {\n      option.selectedMap = 'all';\n    } else if (selectedMode === 'multiple') {\n      if (!zrUtil.isObject(option.selectedMap)) {\n        option.selectedMap = {};\n      }\n      var selectedMap = option.selectedMap;\n      for (var i = 0; i < len; i++) {\n        var dataIndex = innerDataIndices[i];\n        // TODO different types of data share same object.\n        var nameOrId = getSelectionKey(data, dataIndex);\n        selectedMap[nameOrId] = true;\n        this._selectedDataIndicesMap[nameOrId] = data.getRawIndex(dataIndex);\n      }\n    } else if (selectedMode === 'single' || selectedMode === true) {\n      var lastDataIndex = innerDataIndices[len - 1];\n      var nameOrId = getSelectionKey(data, lastDataIndex);\n      option.selectedMap = (_a = {}, _a[nameOrId] = true, _a);\n      this._selectedDataIndicesMap = (_b = {}, _b[nameOrId] = data.getRawIndex(lastDataIndex), _b);\n    }\n  };\n  SeriesModel.prototype._initSelectedMapFromData = function (data) {\n    // Ignore select info in data if selectedMap exists.\n    // NOTE It's only for legacy usage. edge data is not supported.\n    if (this.option.selectedMap) {\n      return;\n    }\n    var dataIndices = [];\n    if (data.hasItemOption) {\n      data.each(function (idx) {\n        var rawItem = data.getRawDataItem(idx);\n        if (rawItem && rawItem.selected) {\n          dataIndices.push(idx);\n        }\n      });\n    }\n    if (dataIndices.length > 0) {\n      this._innerSelect(data, dataIndices);\n    }\n  };\n  // /**\n  //  * @see {module:echarts/stream/Scheduler}\n  //  */\n  // abstract pipeTask: null\n  SeriesModel.registerClass = function (clz) {\n    return ComponentModel.registerClass(clz);\n  };\n  SeriesModel.protoInitialize = function () {\n    var proto = SeriesModel.prototype;\n    proto.type = 'series.__base__';\n    proto.seriesIndex = 0;\n    proto.ignoreStyleOnData = false;\n    proto.hasSymbolVisual = false;\n    proto.defaultSymbol = 'circle';\n    // Make sure the values can be accessed!\n    proto.visualStyleAccessPath = 'itemStyle';\n    proto.visualDrawType = 'fill';\n  }();\n  return SeriesModel;\n}(ComponentModel);\nzrUtil.mixin(SeriesModel, DataFormatMixin);\nzrUtil.mixin(SeriesModel, PaletteMixin);\nmountExtend(SeriesModel, ComponentModel);\n/**\r\n * MUST be called after `prepareSource` called\r\n * Here we need to make auto series, especially for auto legend. But we\r\n * do not modify series.name in option to avoid side effects.\r\n */\nfunction autoSeriesName(seriesModel) {\n  // User specified name has higher priority, otherwise it may cause\n  // series can not be queried unexpectedly.\n  var name = seriesModel.name;\n  if (!modelUtil.isNameSpecified(seriesModel)) {\n    seriesModel.name = getSeriesAutoName(seriesModel) || name;\n  }\n}\nfunction getSeriesAutoName(seriesModel) {\n  var data = seriesModel.getRawData();\n  var dataDims = data.mapDimensionsAll('seriesName');\n  var nameArr = [];\n  zrUtil.each(dataDims, function (dataDim) {\n    var dimInfo = data.getDimensionInfo(dataDim);\n    dimInfo.displayName && nameArr.push(dimInfo.displayName);\n  });\n  return nameArr.join(' ');\n}\nfunction dataTaskCount(context) {\n  return context.model.getRawData().count();\n}\nfunction dataTaskReset(context) {\n  var seriesModel = context.model;\n  seriesModel.setData(seriesModel.getRawData().cloneShallow());\n  return dataTaskProgress;\n}\nfunction dataTaskProgress(param, context) {\n  // Avoid repeat cloneShallow when data just created in reset.\n  if (context.outputData && param.end > context.outputData.count()) {\n    context.model.getRawData().cloneShallow(context.outputData);\n  }\n}\n// TODO refactor\nfunction wrapData(data, seriesModel) {\n  zrUtil.each(zrUtil.concatArray(data.CHANGABLE_METHODS, data.DOWNSAMPLE_METHODS), function (methodName) {\n    data.wrapMethod(methodName, zrUtil.curry(onDataChange, seriesModel));\n  });\n}\nfunction onDataChange(seriesModel, newList) {\n  var task = getCurrentTask(seriesModel);\n  if (task) {\n    // Consider case: filter, selectRange\n    task.setOutputEnd((newList || this).count());\n  }\n  return newList;\n}\nfunction getCurrentTask(seriesModel) {\n  var scheduler = (seriesModel.ecModel || {}).scheduler;\n  var pipeline = scheduler && scheduler.getPipeline(seriesModel.uid);\n  if (pipeline) {\n    // When pipline finished, the currrentTask keep the last\n    // task (renderTask).\n    var task = pipeline.currentTask;\n    if (task) {\n      var agentStubMap = task.agentStubMap;\n      if (agentStubMap) {\n        task = agentStubMap.get(seriesModel.uid);\n      }\n    }\n    return task;\n  }\n}\nexport default SeriesModel;"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAO,KAAKC,MAAM,MAAM,0BAA0B;AAClD,OAAOC,GAAG,MAAM,yBAAyB;AACzC,OAAO,KAAKC,SAAS,MAAM,kBAAkB;AAC7C,OAAOC,cAAc,MAAM,gBAAgB;AAC3C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,SAASC,eAAe,EAAEC,gBAAgB,EAAEC,eAAe,QAAQ,mBAAmB;AACtF,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,0BAA0B,QAAQ,6CAA6C;AACxF,IAAIC,KAAK,GAAGX,SAAS,CAACY,SAAS,CAAC,CAAC;AACjC,SAASC,eAAeA,CAACC,IAAI,EAAEC,SAAS,EAAE;EACxC,OAAOD,IAAI,CAACE,OAAO,CAACD,SAAS,CAAC,IAAID,IAAI,CAACG,KAAK,CAACF,SAAS,CAAC;AACzD;AACA,OAAO,IAAIG,gCAAgC,GAAG,8BAA8B;AAC5E,IAAIC,WAAW,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC/CvB,SAAS,CAACsB,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpE;IACA;IACA;IACAF,KAAK,CAACG,uBAAuB,GAAG,CAAC,CAAC;IAClC,OAAOH,KAAK;EACd;EACAF,WAAW,CAACM,SAAS,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAE;IACnE,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,cAAc;IACtC,IAAI,CAACC,QAAQ,GAAGzB,UAAU,CAAC;MACzB0B,KAAK,EAAEC,aAAa;MACpBC,KAAK,EAAEC;IACT,CAAC,CAAC;IACF,IAAI,CAACJ,QAAQ,CAACK,OAAO,GAAG;MACtBC,KAAK,EAAE;IACT,CAAC;IACD,IAAI,CAACC,oBAAoB,CAACZ,MAAM,EAAEE,OAAO,CAAC;IAC1C,IAAIW,aAAa,GAAG7B,KAAK,CAAC,IAAI,CAAC,CAAC6B,aAAa,GAAG,IAAI/B,aAAa,CAAC,IAAI,CAAC;IACvE+B,aAAa,CAACC,aAAa,CAAC,CAAC;IAC7B,IAAI3B,IAAI,GAAG,IAAI,CAAC4B,cAAc,CAACf,MAAM,EAAEE,OAAO,CAAC;IAC/Cc,QAAQ,CAAC7B,IAAI,EAAE,IAAI,CAAC;IACpB,IAAI,CAACkB,QAAQ,CAACK,OAAO,CAACvB,IAAI,GAAGA,IAAI;IACjC,IAAI8B,OAAO,CAAC7C,GAAG,CAAC8C,QAAQ,KAAK,YAAY,EAAE;MACzC/C,MAAM,CAACgD,MAAM,CAAChC,IAAI,EAAE,uCAAuC,CAAC;IAC9D;IACAH,KAAK,CAAC,IAAI,CAAC,CAACoC,mBAAmB,GAAGjC,IAAI;IACtC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAkC,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI,CAACC,wBAAwB,CAACnC,IAAI,CAAC;EACrC,CAAC;EACD;AACF;AACA;EACEK,WAAW,CAACM,SAAS,CAACc,oBAAoB,GAAG,UAAUZ,MAAM,EAAEE,OAAO,EAAE;IACtE,IAAIqB,UAAU,GAAG5C,eAAe,CAAC,IAAI,CAAC;IACtC,IAAI6C,mBAAmB,GAAGD,UAAU,GAAG9C,eAAe,CAACuB,MAAM,CAAC,GAAG,CAAC,CAAC;IACnE;IACA;IACA;IACA;IACA,IAAIyB,YAAY,GAAG,IAAI,CAACC,OAAO;IAC/B,IAAIpD,cAAc,CAACqD,QAAQ,CAACF,YAAY,CAAC,EAAE;MACzCA,YAAY,IAAI,QAAQ;IAC1B;IACAtD,MAAM,CAACyD,KAAK,CAAC5B,MAAM,EAAEE,OAAO,CAAC2B,QAAQ,CAAC,CAAC,CAACC,GAAG,CAAC,IAAI,CAACJ,OAAO,CAAC,CAAC;IAC1DvD,MAAM,CAACyD,KAAK,CAAC5B,MAAM,EAAE,IAAI,CAAC+B,gBAAgB,CAAC,CAAC,CAAC;IAC7C;IACA1D,SAAS,CAAC2D,eAAe,CAAChC,MAAM,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC;IACpD,IAAI,CAACiC,iBAAiB,CAACjC,MAAM,CAACb,IAAI,CAAC;IACnC,IAAIoC,UAAU,EAAE;MACd7C,gBAAgB,CAACsB,MAAM,EAAEwB,mBAAmB,EAAED,UAAU,CAAC;IAC3D;EACF,CAAC;EACD/B,WAAW,CAACM,SAAS,CAACoC,WAAW,GAAG,UAAUC,eAAe,EAAEjC,OAAO,EAAE;IACtE;IACAiC,eAAe,GAAGhE,MAAM,CAACyD,KAAK,CAAC,IAAI,CAAC5B,MAAM,EAAEmC,eAAe,EAAE,IAAI,CAAC;IAClE,IAAI,CAACF,iBAAiB,CAACE,eAAe,CAAChD,IAAI,CAAC;IAC5C,IAAIoC,UAAU,GAAG5C,eAAe,CAAC,IAAI,CAAC;IACtC,IAAI4C,UAAU,EAAE;MACd7C,gBAAgB,CAAC,IAAI,CAACsB,MAAM,EAAEmC,eAAe,EAAEZ,UAAU,CAAC;IAC5D;IACA,IAAIV,aAAa,GAAG7B,KAAK,CAAC,IAAI,CAAC,CAAC6B,aAAa;IAC7CA,aAAa,CAACuB,KAAK,CAAC,CAAC;IACrBvB,aAAa,CAACC,aAAa,CAAC,CAAC;IAC7B,IAAI3B,IAAI,GAAG,IAAI,CAAC4B,cAAc,CAACoB,eAAe,EAAEjC,OAAO,CAAC;IACxDc,QAAQ,CAAC7B,IAAI,EAAE,IAAI,CAAC;IACpB,IAAI,CAACkB,QAAQ,CAAC+B,KAAK,CAAC,CAAC;IACrB,IAAI,CAAC/B,QAAQ,CAACK,OAAO,CAACvB,IAAI,GAAGA,IAAI;IACjCH,KAAK,CAAC,IAAI,CAAC,CAACoC,mBAAmB,GAAGjC,IAAI;IACtCkC,cAAc,CAAC,IAAI,CAAC;IACpB,IAAI,CAACC,wBAAwB,CAACnC,IAAI,CAAC;EACrC,CAAC;EACDK,WAAW,CAACM,SAAS,CAACmC,iBAAiB,GAAG,UAAU9C,IAAI,EAAE;IACxD;IACA;IACA;IACA,IAAIA,IAAI,IAAI,CAAChB,MAAM,CAACkE,YAAY,CAAClD,IAAI,CAAC,EAAE;MACtC,IAAImD,KAAK,GAAG,CAAC,MAAM,CAAC;MACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpD,IAAI,CAACqD,MAAM,EAAED,CAAC,EAAE,EAAE;QACpC,IAAIpD,IAAI,CAACoD,CAAC,CAAC,IAAIpD,IAAI,CAACoD,CAAC,CAAC,CAACE,KAAK,EAAE;UAC5BpE,SAAS,CAAC2D,eAAe,CAAC7C,IAAI,CAACoD,CAAC,CAAC,EAAE,OAAO,EAAED,KAAK,CAAC;QACpD;MACF;IACF;EACF,CAAC;EACD;AACF;AACA;AACA;EACE9C,WAAW,CAACM,SAAS,CAACiB,cAAc,GAAG,UAAUf,MAAM,EAAEE,OAAO,EAAE;IAChE;EACF,CAAC;EACD;AACF;AACA;EACEV,WAAW,CAACM,SAAS,CAAC4C,UAAU,GAAG,UAAUC,MAAM,EAAE;IACnD;IACA;IACA;IACA,IAAIxD,IAAI,GAAG,IAAI,CAACyD,UAAU,CAAC,CAAC;IAC5BzD,IAAI,CAACuD,UAAU,CAACC,MAAM,CAACxD,IAAI,CAAC;EAC9B,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEK,WAAW,CAACM,SAAS,CAAC+C,OAAO,GAAG,UAAUC,QAAQ,EAAE;IAClD,IAAIC,IAAI,GAAGC,cAAc,CAAC,IAAI,CAAC;IAC/B,IAAID,IAAI,EAAE;MACR,IAAI5D,IAAI,GAAG4D,IAAI,CAACrC,OAAO,CAACvB,IAAI;MAC5B,OAAO2D,QAAQ,IAAI,IAAI,IAAI,CAAC3D,IAAI,CAAC8D,aAAa,GAAG9D,IAAI,GAAGA,IAAI,CAAC8D,aAAa,CAACH,QAAQ,CAAC;IACtF,CAAC,MAAM;MACL;MACA;MACA;MACA;MACA,OAAO9D,KAAK,CAAC,IAAI,CAAC,CAACG,IAAI;IACzB;EACF,CAAC;EACDK,WAAW,CAACM,SAAS,CAACoD,UAAU,GAAG,YAAY;IAC7C,IAAIC,QAAQ,GAAG,IAAI,CAACN,OAAO,CAAC,CAAC;IAC7B,OAAOM,QAAQ,IAAIA,QAAQ,CAACC,gBAAgB,GAAGD,QAAQ,CAACC,gBAAgB,CAAC,CAAC,GAAG,CAAC;MAC5EjE,IAAI,EAAEgE;IACR,CAAC,CAAC;EACJ,CAAC;EACD3D,WAAW,CAACM,SAAS,CAACuD,OAAO,GAAG,UAAUlE,IAAI,EAAE;IAC9C,IAAI4D,IAAI,GAAGC,cAAc,CAAC,IAAI,CAAC;IAC/B,IAAID,IAAI,EAAE;MACR,IAAIrC,OAAO,GAAGqC,IAAI,CAACrC,OAAO;MAC1B;MACA;MACA;MACA;MACA;MACAA,OAAO,CAAC4C,UAAU,GAAGnE,IAAI;MACzB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI4D,IAAI,KAAK,IAAI,CAAC1C,QAAQ,EAAE;QAC1BK,OAAO,CAACvB,IAAI,GAAGA,IAAI;MACrB;IACF;IACAH,KAAK,CAAC,IAAI,CAAC,CAACG,IAAI,GAAGA,IAAI;EACzB,CAAC;EACDK,WAAW,CAACM,SAAS,CAACyD,SAAS,GAAG,YAAY;IAC5C,IAAIC,MAAM,GAAG,IAAI,CAAC1B,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;IACrC,IAAI0B,MAAM,EAAE;MACV,OAAOrF,MAAM,CAACsF,aAAa,CAACD,MAAM,CAAC;IACrC;EACF,CAAC;EACDhE,WAAW,CAACM,SAAS,CAAC4D,gBAAgB,GAAG,YAAY;IACnD,OAAO1E,KAAK,CAAC,IAAI,CAAC,CAAC6B,aAAa;EAClC,CAAC;EACDrB,WAAW,CAACM,SAAS,CAAC6D,SAAS,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACD,gBAAgB,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;EAC5C,CAAC;EACD;AACF;AACA;EACEnE,WAAW,CAACM,SAAS,CAAC8C,UAAU,GAAG,YAAY;IAC7C,OAAO5D,KAAK,CAAC,IAAI,CAAC,CAACoC,mBAAmB;EACxC,CAAC;EACD5B,WAAW,CAACM,SAAS,CAAC8D,UAAU,GAAG,YAAY;IAC7C,IAAIC,OAAO,GAAG,IAAI,CAAC/B,GAAG,CAAC,SAAS,CAAC;IACjC,OAAO+B,OAAO,IAAI,QAAQ;EAC5B,CAAC;EACDrE,WAAW,CAACM,SAAS,CAACgE,eAAe,GAAG,YAAY;IAClD,OAAO,IAAI,CAACF,UAAU,CAAC,CAAC,KAAK,QAAQ;EACvC,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEpE,WAAW,CAACM,SAAS,CAACiE,WAAW,GAAG,YAAY;IAC9C,IAAIC,QAAQ,GAAG,IAAI,CAACC,gBAAgB;IACpC;IACA,OAAOD,QAAQ,IAAIA,QAAQ,CAACD,WAAW,IAAIC,QAAQ,CAACD,WAAW,CAAC,CAAC;EACnE,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEvE,WAAW,CAACM,SAAS,CAACoE,aAAa,GAAG,UAAU9E,SAAS,EAAE+E,cAAc,EAAErB,QAAQ,EAAE;IACnF,OAAO/D,0BAA0B,CAAC;MAChCqF,MAAM,EAAE,IAAI;MACZhF,SAAS,EAAEA,SAAS;MACpB+E,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC;EACD3E,WAAW,CAACM,SAAS,CAACuE,kBAAkB,GAAG,YAAY;IACrD,IAAInE,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B;IACA;IACA,IAAI9B,GAAG,CAACkG,IAAI,IAAI,EAAEpE,OAAO,IAAIA,OAAO,CAACqE,GAAG,CAAC,EAAE;MACzC,OAAO,KAAK;IACd;IACA,IAAIC,gBAAgB,GAAG,IAAI,CAACC,UAAU,CAAC,WAAW,CAAC;IACnD,IAAID,gBAAgB,EAAE;MACpB,IAAI,IAAI,CAAC3B,OAAO,CAAC,CAAC,CAACvC,KAAK,CAAC,CAAC,GAAG,IAAI,CAACmE,UAAU,CAAC,oBAAoB,CAAC,EAAE;QAClED,gBAAgB,GAAG,KAAK;MAC1B;IACF;IACA,OAAO,CAAC,CAACA,gBAAgB;EAC3B,CAAC;EACDhF,WAAW,CAACM,SAAS,CAAC4E,WAAW,GAAG,YAAY;IAC9C,IAAI,CAACrE,QAAQ,CAAC+B,KAAK,CAAC,CAAC;EACvB,CAAC;EACD5C,WAAW,CAACM,SAAS,CAAC6E,mBAAmB,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAEC,eAAe,EAAE;IAClF,IAAI5E,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B;IACA,IAAI6E,KAAK,GAAGxG,YAAY,CAACuB,SAAS,CAAC6E,mBAAmB,CAACK,IAAI,CAAC,IAAI,EAAEJ,IAAI,EAAEC,KAAK,EAAEC,eAAe,CAAC;IAC/F,IAAI,CAACC,KAAK,EAAE;MACVA,KAAK,GAAG7E,OAAO,CAACyE,mBAAmB,CAACC,IAAI,EAAEC,KAAK,EAAEC,eAAe,CAAC;IACnE;IACA,OAAOC,KAAK;EACd,CAAC;EACD;AACF;AACA;AACA;EACEvF,WAAW,CAACM,SAAS,CAACmF,iBAAiB,GAAG,UAAUC,QAAQ,EAAE;IAC5D,OAAO,IAAI,CAACtC,UAAU,CAAC,CAAC,CAACuC,gBAAgB,CAACD,QAAQ,CAAC;EACrD,CAAC;EACD;AACF;AACA;EACE1F,WAAW,CAACM,SAAS,CAACsF,cAAc,GAAG,YAAY;IACjD,OAAO,IAAI,CAACtD,GAAG,CAAC,aAAa,CAAC;EAChC,CAAC;EACD;AACF;AACA;EACEtC,WAAW,CAACM,SAAS,CAACuF,uBAAuB,GAAG,YAAY;IAC1D,OAAO,IAAI,CAACvD,GAAG,CAAC,sBAAsB,CAAC;EACzC,CAAC;EACD;EACAtC,WAAW,CAACM,SAAS,CAACwF,MAAM,GAAG,UAAUC,gBAAgB,EAAEzC,QAAQ,EAAE;IACnE,IAAI,CAAC0C,YAAY,CAAC,IAAI,CAAC3C,OAAO,CAACC,QAAQ,CAAC,EAAEyC,gBAAgB,CAAC;EAC7D,CAAC;EACD/F,WAAW,CAACM,SAAS,CAAC2F,QAAQ,GAAG,UAAUF,gBAAgB,EAAEzC,QAAQ,EAAE;IACrE,IAAI4C,WAAW,GAAG,IAAI,CAAC1F,MAAM,CAAC0F,WAAW;IACzC,IAAI,CAACA,WAAW,EAAE;MAChB;IACF;IACA,IAAIC,YAAY,GAAG,IAAI,CAAC3F,MAAM,CAAC2F,YAAY;IAC3C,IAAIxG,IAAI,GAAG,IAAI,CAAC0D,OAAO,CAACC,QAAQ,CAAC;IACjC,IAAI6C,YAAY,KAAK,QAAQ,IAAID,WAAW,KAAK,KAAK,EAAE;MACtD,IAAI,CAAC1F,MAAM,CAAC0F,WAAW,GAAG,CAAC,CAAC;MAC5B,IAAI,CAAC7F,uBAAuB,GAAG,CAAC,CAAC;MACjC;IACF;IACA,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,gBAAgB,CAAC/C,MAAM,EAAED,CAAC,EAAE,EAAE;MAChD,IAAInD,SAAS,GAAGmG,gBAAgB,CAAChD,CAAC,CAAC;MACnC,IAAIqD,QAAQ,GAAG1G,eAAe,CAACC,IAAI,EAAEC,SAAS,CAAC;MAC/CsG,WAAW,CAACE,QAAQ,CAAC,GAAG,KAAK;MAC7B,IAAI,CAAC/F,uBAAuB,CAAC+F,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC7C;EACF,CAAC;EACDpG,WAAW,CAACM,SAAS,CAAC+F,YAAY,GAAG,UAAUN,gBAAgB,EAAEzC,QAAQ,EAAE;IACzE,IAAIgD,MAAM,GAAG,EAAE;IACf,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,gBAAgB,CAAC/C,MAAM,EAAED,CAAC,EAAE,EAAE;MAChDuD,MAAM,CAAC,CAAC,CAAC,GAAGP,gBAAgB,CAAChD,CAAC,CAAC;MAC/B,IAAI,CAACwD,UAAU,CAACR,gBAAgB,CAAChD,CAAC,CAAC,EAAEO,QAAQ,CAAC,GAAG,IAAI,CAAC2C,QAAQ,CAACK,MAAM,EAAEhD,QAAQ,CAAC,GAAG,IAAI,CAACwC,MAAM,CAACQ,MAAM,EAAEhD,QAAQ,CAAC;IAClH;EACF,CAAC;EACDtD,WAAW,CAACM,SAAS,CAACkG,sBAAsB,GAAG,YAAY;IACzD,IAAI,IAAI,CAAChG,MAAM,CAAC0F,WAAW,KAAK,KAAK,EAAE;MACrC,OAAO,EAAE,CAACO,KAAK,CAACjB,IAAI,CAAC,IAAI,CAACnC,OAAO,CAAC,CAAC,CAACqD,UAAU,CAAC,CAAC,CAAC;IACnD;IACA,IAAIC,sBAAsB,GAAG,IAAI,CAACtG,uBAAuB;IACzD,IAAIuG,SAAS,GAAGjI,MAAM,CAACkI,IAAI,CAACF,sBAAsB,CAAC;IACnD,IAAIG,WAAW,GAAG,EAAE;IACpB,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6D,SAAS,CAAC5D,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAInD,SAAS,GAAG+G,sBAAsB,CAACC,SAAS,CAAC7D,CAAC,CAAC,CAAC;MACpD,IAAInD,SAAS,IAAI,CAAC,EAAE;QAClBkH,WAAW,CAACC,IAAI,CAACnH,SAAS,CAAC;MAC7B;IACF;IACA,OAAOkH,WAAW;EACpB,CAAC;EACD9G,WAAW,CAACM,SAAS,CAACiG,UAAU,GAAG,UAAU3G,SAAS,EAAE0D,QAAQ,EAAE;IAChE,IAAI4C,WAAW,GAAG,IAAI,CAAC1F,MAAM,CAAC0F,WAAW;IACzC,IAAI,CAACA,WAAW,EAAE;MAChB,OAAO,KAAK;IACd;IACA,IAAIvG,IAAI,GAAG,IAAI,CAAC0D,OAAO,CAACC,QAAQ,CAAC;IACjC,OAAO,CAAC4C,WAAW,KAAK,KAAK,IAAIA,WAAW,CAACxG,eAAe,CAACC,IAAI,EAAEC,SAAS,CAAC,CAAC,KAAK,CAACD,IAAI,CAACqH,YAAY,CAACpH,SAAS,CAAC,CAAC0C,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;EAC9I,CAAC;EACDtC,WAAW,CAACM,SAAS,CAAC2G,4BAA4B,GAAG,YAAY;IAC/D,IAAI,IAAI,CAAClH,gCAAgC,CAAC,EAAE;MAC1C,OAAO,IAAI;IACb;IACA,IAAImH,sBAAsB,GAAG,IAAI,CAAC1G,MAAM,CAAC2G,mBAAmB;IAC5D;IACA,IAAI,CAACD,sBAAsB,EAAE;MAC3B,OAAO,KAAK;IACd;IACA,IAAIA,sBAAsB,KAAK,IAAI,EAAE;MACnC,OAAO,IAAI;IACb;IACA;IACA,OAAOA,sBAAsB,IAAIA,sBAAsB,CAACE,OAAO;EACjE,CAAC;EACDpH,WAAW,CAACM,SAAS,CAAC0F,YAAY,GAAG,UAAUrG,IAAI,EAAEoG,gBAAgB,EAAE;IACrE,IAAIsB,EAAE,EAAEC,EAAE;IACV,IAAI9G,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI2F,YAAY,GAAG3F,MAAM,CAAC2F,YAAY;IACtC,IAAIoB,GAAG,GAAGxB,gBAAgB,CAAC/C,MAAM;IACjC,IAAI,CAACmD,YAAY,IAAI,CAACoB,GAAG,EAAE;MACzB;IACF;IACA,IAAIpB,YAAY,KAAK,QAAQ,EAAE;MAC7B3F,MAAM,CAAC0F,WAAW,GAAG,KAAK;IAC5B,CAAC,MAAM,IAAIC,YAAY,KAAK,UAAU,EAAE;MACtC,IAAI,CAACxH,MAAM,CAAC6I,QAAQ,CAAChH,MAAM,CAAC0F,WAAW,CAAC,EAAE;QACxC1F,MAAM,CAAC0F,WAAW,GAAG,CAAC,CAAC;MACzB;MACA,IAAIA,WAAW,GAAG1F,MAAM,CAAC0F,WAAW;MACpC,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwE,GAAG,EAAExE,CAAC,EAAE,EAAE;QAC5B,IAAInD,SAAS,GAAGmG,gBAAgB,CAAChD,CAAC,CAAC;QACnC;QACA,IAAIqD,QAAQ,GAAG1G,eAAe,CAACC,IAAI,EAAEC,SAAS,CAAC;QAC/CsG,WAAW,CAACE,QAAQ,CAAC,GAAG,IAAI;QAC5B,IAAI,CAAC/F,uBAAuB,CAAC+F,QAAQ,CAAC,GAAGzG,IAAI,CAAC8H,WAAW,CAAC7H,SAAS,CAAC;MACtE;IACF,CAAC,MAAM,IAAIuG,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,IAAI,EAAE;MAC7D,IAAIuB,aAAa,GAAG3B,gBAAgB,CAACwB,GAAG,GAAG,CAAC,CAAC;MAC7C,IAAInB,QAAQ,GAAG1G,eAAe,CAACC,IAAI,EAAE+H,aAAa,CAAC;MACnDlH,MAAM,CAAC0F,WAAW,IAAImB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACjB,QAAQ,CAAC,GAAG,IAAI,EAAEiB,EAAE,CAAC;MACvD,IAAI,CAAChH,uBAAuB,IAAIiH,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAAClB,QAAQ,CAAC,GAAGzG,IAAI,CAAC8H,WAAW,CAACC,aAAa,CAAC,EAAEJ,EAAE,CAAC;IAC9F;EACF,CAAC;EACDtH,WAAW,CAACM,SAAS,CAACwB,wBAAwB,GAAG,UAAUnC,IAAI,EAAE;IAC/D;IACA;IACA,IAAI,IAAI,CAACa,MAAM,CAAC0F,WAAW,EAAE;MAC3B;IACF;IACA,IAAIY,WAAW,GAAG,EAAE;IACpB,IAAInH,IAAI,CAACgI,aAAa,EAAE;MACtBhI,IAAI,CAACiI,IAAI,CAAC,UAAUC,GAAG,EAAE;QACvB,IAAIC,OAAO,GAAGnI,IAAI,CAACoI,cAAc,CAACF,GAAG,CAAC;QACtC,IAAIC,OAAO,IAAIA,OAAO,CAACE,QAAQ,EAAE;UAC/BlB,WAAW,CAACC,IAAI,CAACc,GAAG,CAAC;QACvB;MACF,CAAC,CAAC;IACJ;IACA,IAAIf,WAAW,CAAC9D,MAAM,GAAG,CAAC,EAAE;MAC1B,IAAI,CAACgD,YAAY,CAACrG,IAAI,EAAEmH,WAAW,CAAC;IACtC;EACF,CAAC;EACD;EACA;EACA;EACA;EACA9G,WAAW,CAACiI,aAAa,GAAG,UAAUC,GAAG,EAAE;IACzC,OAAOpJ,cAAc,CAACmJ,aAAa,CAACC,GAAG,CAAC;EAC1C,CAAC;EACDlI,WAAW,CAACmI,eAAe,GAAG,YAAY;IACxC,IAAIC,KAAK,GAAGpI,WAAW,CAACM,SAAS;IACjC8H,KAAK,CAACC,IAAI,GAAG,iBAAiB;IAC9BD,KAAK,CAACzH,WAAW,GAAG,CAAC;IACrByH,KAAK,CAACE,iBAAiB,GAAG,KAAK;IAC/BF,KAAK,CAACG,eAAe,GAAG,KAAK;IAC7BH,KAAK,CAACI,aAAa,GAAG,QAAQ;IAC9B;IACAJ,KAAK,CAACK,qBAAqB,GAAG,WAAW;IACzCL,KAAK,CAACM,cAAc,GAAG,MAAM;EAC/B,CAAC,CAAC,CAAC;EACH,OAAO1I,WAAW;AACpB,CAAC,CAAClB,cAAc,CAAC;AACjBH,MAAM,CAACgK,KAAK,CAAC3I,WAAW,EAAEhB,eAAe,CAAC;AAC1CL,MAAM,CAACgK,KAAK,CAAC3I,WAAW,EAAEjB,YAAY,CAAC;AACvCM,WAAW,CAACW,WAAW,EAAElB,cAAc,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA,SAAS+C,cAAcA,CAAC+G,WAAW,EAAE;EACnC;EACA;EACA,IAAIxD,IAAI,GAAGwD,WAAW,CAACxD,IAAI;EAC3B,IAAI,CAACvG,SAAS,CAACgK,eAAe,CAACD,WAAW,CAAC,EAAE;IAC3CA,WAAW,CAACxD,IAAI,GAAG0D,iBAAiB,CAACF,WAAW,CAAC,IAAIxD,IAAI;EAC3D;AACF;AACA,SAAS0D,iBAAiBA,CAACF,WAAW,EAAE;EACtC,IAAIjJ,IAAI,GAAGiJ,WAAW,CAACxF,UAAU,CAAC,CAAC;EACnC,IAAI2F,QAAQ,GAAGpJ,IAAI,CAACgG,gBAAgB,CAAC,YAAY,CAAC;EAClD,IAAIqD,OAAO,GAAG,EAAE;EAChBrK,MAAM,CAACiJ,IAAI,CAACmB,QAAQ,EAAE,UAAUE,OAAO,EAAE;IACvC,IAAIC,OAAO,GAAGvJ,IAAI,CAACwJ,gBAAgB,CAACF,OAAO,CAAC;IAC5CC,OAAO,CAACE,WAAW,IAAIJ,OAAO,CAACjC,IAAI,CAACmC,OAAO,CAACE,WAAW,CAAC;EAC1D,CAAC,CAAC;EACF,OAAOJ,OAAO,CAACK,IAAI,CAAC,GAAG,CAAC;AAC1B;AACA,SAAStI,aAAaA,CAACG,OAAO,EAAE;EAC9B,OAAOA,OAAO,CAACC,KAAK,CAACiC,UAAU,CAAC,CAAC,CAACtC,KAAK,CAAC,CAAC;AAC3C;AACA,SAASG,aAAaA,CAACC,OAAO,EAAE;EAC9B,IAAI0H,WAAW,GAAG1H,OAAO,CAACC,KAAK;EAC/ByH,WAAW,CAAC/E,OAAO,CAAC+E,WAAW,CAACxF,UAAU,CAAC,CAAC,CAACkG,YAAY,CAAC,CAAC,CAAC;EAC5D,OAAOC,gBAAgB;AACzB;AACA,SAASA,gBAAgBA,CAACC,KAAK,EAAEtI,OAAO,EAAE;EACxC;EACA,IAAIA,OAAO,CAAC4C,UAAU,IAAI0F,KAAK,CAACC,GAAG,GAAGvI,OAAO,CAAC4C,UAAU,CAAChD,KAAK,CAAC,CAAC,EAAE;IAChEI,OAAO,CAACC,KAAK,CAACiC,UAAU,CAAC,CAAC,CAACkG,YAAY,CAACpI,OAAO,CAAC4C,UAAU,CAAC;EAC7D;AACF;AACA;AACA,SAAStC,QAAQA,CAAC7B,IAAI,EAAEiJ,WAAW,EAAE;EACnCjK,MAAM,CAACiJ,IAAI,CAACjJ,MAAM,CAAC+K,WAAW,CAAC/J,IAAI,CAACgK,iBAAiB,EAAEhK,IAAI,CAACiK,kBAAkB,CAAC,EAAE,UAAUC,UAAU,EAAE;IACrGlK,IAAI,CAACmK,UAAU,CAACD,UAAU,EAAElL,MAAM,CAACoL,KAAK,CAACC,YAAY,EAAEpB,WAAW,CAAC,CAAC;EACtE,CAAC,CAAC;AACJ;AACA,SAASoB,YAAYA,CAACpB,WAAW,EAAEqB,OAAO,EAAE;EAC1C,IAAI1G,IAAI,GAAGC,cAAc,CAACoF,WAAW,CAAC;EACtC,IAAIrF,IAAI,EAAE;IACR;IACAA,IAAI,CAAC2G,YAAY,CAAC,CAACD,OAAO,IAAI,IAAI,EAAEnJ,KAAK,CAAC,CAAC,CAAC;EAC9C;EACA,OAAOmJ,OAAO;AAChB;AACA,SAASzG,cAAcA,CAACoF,WAAW,EAAE;EACnC,IAAIuB,SAAS,GAAG,CAACvB,WAAW,CAAClI,OAAO,IAAI,CAAC,CAAC,EAAEyJ,SAAS;EACrD,IAAIC,QAAQ,GAAGD,SAAS,IAAIA,SAAS,CAACE,WAAW,CAACzB,WAAW,CAAC0B,GAAG,CAAC;EAClE,IAAIF,QAAQ,EAAE;IACZ;IACA;IACA,IAAI7G,IAAI,GAAG6G,QAAQ,CAACG,WAAW;IAC/B,IAAIhH,IAAI,EAAE;MACR,IAAIiH,YAAY,GAAGjH,IAAI,CAACiH,YAAY;MACpC,IAAIA,YAAY,EAAE;QAChBjH,IAAI,GAAGiH,YAAY,CAAClI,GAAG,CAACsG,WAAW,CAAC0B,GAAG,CAAC;MAC1C;IACF;IACA,OAAO/G,IAAI;EACb;AACF;AACA,eAAevD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}