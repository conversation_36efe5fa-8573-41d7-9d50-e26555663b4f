{"ast": null, "code": "import { dataApi } from './api';\nexport default {\n  name: 'App',\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: []\n    };\n  },\n  mounted() {\n    this.loadTables();\n  },\n  methods: {\n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables();\n        this.tables = response.data;\n      } catch (error) {\n        this.$message.error('加载数据表失败');\n        console.error(error);\n      }\n    },\n    selectTable(table) {\n      this.selectedTable = table;\n      this.queryResult = null;\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    async submitQuestion() {\n      if (!this.question.trim()) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n      try {\n        // 如果用户没选择表，默认使用第一个\n        const tableCode = this.selectedTable ? this.selectedTable.tableCode : this.tables.length > 0 ? this.tables[0].tableCode : null;\n        if (!tableCode) {\n          this.$message.warning('请先选择数据表');\n          return;\n        }\n        const response = await dataApi.queryByQuestion(tableCode, this.question);\n        this.queryResult = response.data;\n\n        // 清空输入框\n        this.question = '';\n      } catch (error) {\n        this.$message.error('查询出错，请稍后重试');\n        console.error(error);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "name", "data", "description", "tablename", "tables", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "mounted", "loadTables", "methods", "response", "getAllTables", "error", "$message", "console", "selectTable", "table", "showSuggestions", "useQuestion", "q", "getTableFields", "tableCode", "submitQuestion", "trim", "warning", "length", "queryByQuestion"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <div class=\"header\">\n          <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n          <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n        </div>\n        \n        <div class=\"data-selection\">\n          <h3>目前可用数据</h3>\n          <div class=\"data-sets\">\n            <el-row :gutter=\"20\">\n              <el-col :span=\"12\" v-for=\"table in tables\" :key=\"table.tableCode\">\n                <el-card class=\"data-card\" @click.native=\"selectTable(table)\">\n                  <div class=\"data-header\">\n                    <span class=\"sample-tag\">{{table.tableName}}</span>\n                    <span>{{ table.description }}</span>\n                  </div>\n                  <div class=\"data-body\">\n                    <div class=\"data-fields\">\n                      <div v-for=\"(field, index) in getTableFields(table)\" :key=\"index\" class=\"field-item\">\n                        {{ field }}\n                      </div>\n                    </div>\n                  </div>\n                </el-card>\n              </el-col>\n            </el-row>\n          </div>\n        </div>\n        \n        <div v-if=\"queryResult\" class=\"result-section\">\n          <div class=\"answer-text\">\n            {{ queryResult.answer }}\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 底部问题输入区域 -->\n    <div class=\"question-input-container\">\n      <div class=\"question-input-wrapper\">\n        <div class=\"input-prefix\">👋</div>\n        <el-input \n          v-model=\"question\" \n          placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n          class=\"question-input\"\n          @keyup.enter.native=\"submitQuestion\">\n        </el-input>\n        <div class=\"input-actions\">\n          <button class=\"action-btn\" @click=\"showSuggestions\">\n            <i class=\"el-icon-magic-stick\"></i>\n          </button>\n          <button class=\"action-btn send-btn\" @click=\"submitQuestion\">\n            <i class=\"el-icon-position\"></i>\n          </button>\n        </div>\n      </div>\n      \n      <!-- 建议问题弹出层 -->\n      <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n        <div class=\"suggestions-title\">\n          <i class=\"el-icon-s-promotion\"></i> 官方推荐\n        </div>\n        <div class=\"suggestions-list\">\n          <div \n            v-for=\"(suggestion, index) in suggestedQuestions\" \n            :key=\"index\"\n            class=\"suggestion-item\"\n            @click=\"useQuestion(suggestion)\">\n            {{ suggestion }}\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api'\n\nexport default {\n  name: 'App',\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: []\n    }\n  },\n  mounted() {\n    this.loadTables()\n  },\n  methods: {\n    async loadTables() {\n      try {\n        const response = await dataApi.getAllTables()\n        this.tables = response.data\n      } catch (error) {\n        this.$message.error('加载数据表失败')\n        console.error(error)\n      }\n    },\n    selectTable(table) {\n      this.selectedTable = table\n      this.queryResult = null\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    async submitQuestion() {\n      if (!this.question.trim()) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      try {\n        // 如果用户没选择表，默认使用第一个\n        const tableCode = this.selectedTable ? this.selectedTable.tableCode : \n                         (this.tables.length > 0 ? this.tables[0].tableCode : null)\n        \n        if (!tableCode) {\n          this.$message.warning('请先选择数据表')\n          return\n        }\n        \n        const response = await dataApi.queryByQuestion(tableCode, this.question)\n        this.queryResult = response.data\n        \n        // 清空输入框\n        this.question = ''\n      } catch (error) {\n        this.$message.error('查询出错，请稍后重试')\n        console.error(error)\n      }\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n}\n\n.app-layout {\n  display: flex;\n  height: calc(100vh - 70px); /* 留出底部输入框的空间 */\n  overflow: hidden;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  background-color: #f9f9f9;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n\n}\n\n  .logo h2 {\n    margin: 0;\n    font-size: 14px;\n    text-align: center;\n    white-space: nowrap;\n    letter-spacing: 5px;\n    /* 字体变粗 */\n    font-weight: 900;\n  }\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  overflow-x: auto;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.header {\n  margin-bottom: 20px;\n}\n\n.header h2 {\n  margin: 0;\n  font-size: 20px;\n}\n\n.highlight {\n  color: #409eff;\n}\n\n.sub-title {\n  color: #606266;\n  font-size: 14px;\n  margin: 5px 0 0 0;\n}\n\n.data-card {\n  cursor: pointer;\n  margin-bottom: 15px;\n  transition: all 0.3s;\n}\n\n.data-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.data-header {\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.sample-tag {\n  background-color: #ecf5ff;\n  color: #409eff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-right: 10px;\n}\n\n.data-fields {\n  display: flex;\n  flex-wrap: wrap;\n  margin-top: 10px;\n}\n\n.field-item {\n  background-color: #f5f7fa;\n  padding: 2px 8px;\n  margin: 4px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n/* 底部问题输入区域 */\n.question-input-container {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  background-color: #fff;\n  border-top: 1px solid #ebeef5;\n  padding: 15px;\n  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\n}\n\n.question-input-wrapper {\n  display: flex;\n  align-items: center;\n  max-width: 900px;\n  margin: 0 auto;\n  background-color: #f5f7fa;\n  border-radius: 20px;\n  padding: 5px 15px;\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n}\n\n.action-btn {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background-color: #fff;\n  border: 1px solid #dcdfe6;\n  margin-left: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n}\n\n.send-btn {\n  background-color: #409eff;\n  color: white;\n  border: none;\n}\n\n/* 建议问题面板 */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: white;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n</style>"], "mappings": "AAoGA,SAAAA,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACA,MAAAD,WAAA;MACA;QACA,MAAAE,QAAA,SAAAf,OAAA,CAAAgB,YAAA;QACA,KAAAX,MAAA,GAAAU,QAAA,CAAAb,IAAA;MACA,SAAAe,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,CAAAA,KAAA;MACA;IACA;IACAG,YAAAC,KAAA;MACA,KAAAf,aAAA,GAAAe,KAAA;MACA,KAAAb,WAAA;IACA;IACAc,gBAAA;MACA,KAAAb,oBAAA,SAAAA,oBAAA;IACA;IACAc,YAAAC,CAAA;MACA,KAAAjB,QAAA,GAAAiB,CAAA;MACA,KAAAf,oBAAA;IACA;IACAgB,eAAAJ,KAAA;MACA,IAAAA,KAAA,CAAAK,SAAA;QACA;MACA;MACA;IACA;IACA,MAAAC,eAAA;MACA,UAAApB,QAAA,CAAAqB,IAAA;QACA,KAAAV,QAAA,CAAAW,OAAA;QACA;MACA;MAEA;QACA;QACA,MAAAH,SAAA,QAAApB,aAAA,QAAAA,aAAA,CAAAoB,SAAA,GACA,KAAArB,MAAA,CAAAyB,MAAA,YAAAzB,MAAA,IAAAqB,SAAA;QAEA,KAAAA,SAAA;UACA,KAAAR,QAAA,CAAAW,OAAA;UACA;QACA;QAEA,MAAAd,QAAA,SAAAf,OAAA,CAAA+B,eAAA,CAAAL,SAAA,OAAAnB,QAAA;QACA,KAAAC,WAAA,GAAAO,QAAA,CAAAb,IAAA;;QAEA;QACA,KAAAK,QAAA;MACA,SAAAU,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,CAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}