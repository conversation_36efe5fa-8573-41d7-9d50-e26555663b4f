{"ast": null, "code": "import sha1 from './sha1.js';\nimport v35, { DNS, URL } from './v35.js';\nexport { DNS, URL } from './v35.js';\nfunction v5(value, namespace, buf, offset) {\n  return v35(0x50, sha1, value, namespace, buf, offset);\n}\nv5.DNS = DNS;\nv5.URL = URL;\nexport default v5;", "map": {"version": 3, "names": ["sha1", "v35", "DNS", "URL", "v5", "value", "namespace", "buf", "offset"], "sources": ["E:/indicator-qa-service/datafront/node_modules/uuid/dist/esm-browser/v5.js"], "sourcesContent": ["import sha1 from './sha1.js';\nimport v35, { DNS, URL } from './v35.js';\nexport { DNS, URL } from './v35.js';\nfunction v5(value, namespace, buf, offset) {\n    return v35(0x50, sha1, value, namespace, buf, offset);\n}\nv5.DNS = DNS;\nv5.URL = URL;\nexport default v5;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAC5B,OAAOC,GAAG,IAAIC,GAAG,EAAEC,GAAG,QAAQ,UAAU;AACxC,SAASD,GAAG,EAAEC,GAAG,QAAQ,UAAU;AACnC,SAASC,EAAEA,CAACC,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAE;EACvC,OAAOP,GAAG,CAAC,IAAI,EAAED,IAAI,EAAEK,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,CAAC;AACzD;AACAJ,EAAE,CAACF,GAAG,GAAGA,GAAG;AACZE,EAAE,CAACD,GAAG,GAAGA,GAAG;AACZ,eAAeC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}