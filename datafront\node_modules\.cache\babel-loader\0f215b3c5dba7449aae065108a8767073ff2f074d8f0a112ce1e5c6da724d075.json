{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'el-icon-s-data',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'el-icon-connection',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'el-icon-pie-chart',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'el-icon-s-operation',\n          compatible: true\n        }\n      }\n    };\n  },\n  computed: {\n    // 是否显示图表切换器\n    showChartSwitcher() {\n      return this.availableChartTypes.length > 1 && !this.loading && !this.error;\n    },\n    // 可用的图表类型列表\n    availableChartTypes() {\n      const types = [];\n\n      // 添加当前图表类型\n      if (this.currentChartType && this.chartTypeConfig[this.currentChartType]) {\n        types.push({\n          type: this.currentChartType,\n          ...this.chartTypeConfig[this.currentChartType]\n        });\n      }\n\n      // 添加推荐的图表类型\n      this.recommendedChartTypes.forEach(type => {\n        if (type !== this.currentChartType && this.chartTypeConfig[type]) {\n          types.push({\n            type: type,\n            ...this.chartTypeConfig[type],\n            compatible: this.isChartTypeCompatible(type)\n          });\n        }\n      });\n      return types;\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler(newConfig) {\n        // 更新当前图表类型\n        if (newConfig && newConfig.type) {\n          this.currentChartType = newConfig.type;\n        }\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    // 初始化当前图表类型\n    if (this.chartConfig && this.chartConfig.type) {\n      this.currentChartType = this.chartConfig.type;\n    }\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    // 切换图表类型\n    async switchChartType(newType) {\n      if (newType === this.currentChartType || this.switchingChart) {\n        return;\n      }\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n      try {\n        this.switchingChart = true;\n\n        // 检查兼容性\n        if (!this.isChartTypeCompatible(newType)) {\n          this.$message.warning(`当前数据不适合 ${this.chartTypeConfig[newType]?.name} 图表`);\n          return;\n        }\n\n        // 更新图表类型\n        this.currentChartType = newType;\n\n        // 创建新的图表配置，复用相同数据\n        const newChartConfig = {\n          ...this.chartConfig,\n          type: newType\n        };\n\n        // 触发重新渲染\n        this.$emit('chart-type-changed', newChartConfig);\n\n        // 如果父组件没有监听事件，直接更新本地配置\n        await this.$nextTick();\n        if (this.chartConfig.type !== newType) {\n          // 直接修改配置并重新渲染\n          this.chartConfig.type = newType;\n          this.renderChart();\n        }\n        this.$message.success(`已切换到${this.chartTypeConfig[newType]?.name}`);\n      } catch (error) {\n        console.error('图表切换失败:', error);\n        this.$message.error('图表切换失败: ' + error.message);\n      } finally {\n        this.switchingChart = false;\n      }\n    },\n    // 检查图表类型兼容性\n    isChartTypeCompatible(chartType) {\n      // 检查是否有数据\n      if (!this.chartConfig || !this.chartConfig.data) {\n        return false;\n      }\n      let data = [];\n      if (this.chartConfig.data && Array.isArray(this.chartConfig.data)) {\n        data = this.chartConfig.data;\n      } else if (this.chartConfig.data && this.chartConfig.data.data && Array.isArray(this.chartConfig.data.data)) {\n        data = this.chartConfig.data.data;\n      }\n\n      // 基本检查：是否有数据\n      if (!data || data.length === 0) {\n        return false;\n      }\n\n      // 检查数据格式是否包含必要字段\n      const firstItem = data[0];\n      if (!firstItem || typeof firstItem.value === 'undefined' || !firstItem.field) {\n        return false;\n      }\n\n      // 所有当前支持的图表类型都兼容这种数据格式\n      const supportedTypes = ['bar', 'line', 'pie', 'bar-horizontal'];\n      return supportedTypes.includes(chartType);\n    },\n    renderChart() {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n      console.log('开始渲染图表，配置:', this.chartConfig);\n\n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartConfig);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n\n      // 如果没有数据，才调用API获取\n      getData(this.chartConfig).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n\n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch (this.chartConfig.type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n\n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n\n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n\n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        console.log('提取的数据:', extractedData);\n\n        // 根据提取的数据类型调用不同的处理函数\n        switch (extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const {\n        data = [],\n        metrics = []\n      } = chartData;\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const {\n        data = []\n      } = chartData;\n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getData", "echarts", "name", "props", "chartConfig", "type", "Object", "required", "default", "recommendedChartTypes", "Array", "data", "loading", "error", "errorMsg", "chartInstance", "<PERSON><PERSON><PERSON>", "currentChartType", "chartTypeConfig", "icon", "compatible", "computed", "showChartSwitcher", "availableChartTypes", "length", "types", "push", "for<PERSON>ach", "isChartTypeCompatible", "watch", "deep", "handler", "newConfig", "<PERSON><PERSON><PERSON>", "mounted", "window", "addEventListener", "resizeChart", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "dispose", "methods", "switchChartType", "newType", "console", "log", "$message", "warning", "newChartConfig", "$emit", "$nextTick", "success", "message", "chartType", "isArray", "firstItem", "value", "field", "supportedTypes", "includes", "options", "convertToChartOptions", "<PERSON><PERSON><PERSON>", "then", "response", "code", "msg", "chartData", "catch", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "extractedData", "metrics", "fields", "filter", "f", "groupType", "xAxisData", "map", "item", "series", "categoriesSet", "Set", "s", "add", "category", "categories", "from", "seriesData", "find", "title", "text", "tooltip", "trigger", "axisPointer", "legend", "xAxis", "yAxis", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "position", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "$refs", "chartRef", "init", "setOption", "resize"], "sources": ["src/components/ChartDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <!-- 图表切换工具栏 -->\n    <div v-if=\"showChartSwitcher\" class=\"chart-switcher\">\n      <div class=\"chart-switcher-title\">图表切换</div>\n      <div class=\"chart-type-buttons\">\n        <button\n          v-for=\"chartType in availableChartTypes\"\n          :key=\"chartType.type\"\n          :class=\"['chart-type-btn', {\n            'active': currentChartType === chartType.type,\n            'disabled': !chartType.compatible\n          }]\"\n          :disabled=\"!chartType.compatible || switchingChart\"\n          @click=\"switchChartType(chartType.type)\"\n          :title=\"chartType.name\"\n        >\n          <i :class=\"chartType.icon\"></i>\n          <span>{{ chartType.name }}</span>\n          <div v-if=\"switchingChart && currentChartType === chartType.type\" class=\"switching-loader\">\n            <i class=\"el-icon-loading\"></i>\n          </div>\n        </button>\n      </div>\n    </div>\n\n    <!-- 图表显示区域 -->\n    <div v-if=\"loading\" class=\"chart-loading\">\n      <i class=\"el-icon-loading\"></i>\n      <span>加载图表中...</span>\n    </div>\n    <div v-else-if=\"error\" class=\"chart-error\">\n      <i class=\"el-icon-warning\"></i>\n      <span>{{ errorMsg }}</span>\n    </div>\n    <div v-else ref=\"chartRef\" class=\"chart-canvas\"></div>\n  </div>\n</template>\n\n<script>\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\n\nexport default {\n  name: 'ChartDisplay',\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 推荐的图表类型列表\n    recommendedChartTypes: {\n      type: Array,\n      default: () => []\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      switchingChart: false,\n      currentChartType: '',\n      // 支持的图表类型配置\n      chartTypeConfig: {\n        'bar': {\n          name: '柱图',\n          icon: 'el-icon-s-data',\n          compatible: true\n        },\n        'line': {\n          name: '线图',\n          icon: 'el-icon-connection',\n          compatible: true\n        },\n        'pie': {\n          name: '饼图',\n          icon: 'el-icon-pie-chart',\n          compatible: true\n        },\n        'bar-horizontal': {\n          name: '条形图',\n          icon: 'el-icon-s-operation',\n          compatible: true\n        }\n      }\n    }\n  },\n  computed: {\n    // 是否显示图表切换器\n    showChartSwitcher() {\n      return this.availableChartTypes.length > 1 && !this.loading && !this.error;\n    },\n\n    // 可用的图表类型列表\n    availableChartTypes() {\n      const types = [];\n\n      // 添加当前图表类型\n      if (this.currentChartType && this.chartTypeConfig[this.currentChartType]) {\n        types.push({\n          type: this.currentChartType,\n          ...this.chartTypeConfig[this.currentChartType]\n        });\n      }\n\n      // 添加推荐的图表类型\n      this.recommendedChartTypes.forEach(type => {\n        if (type !== this.currentChartType && this.chartTypeConfig[type]) {\n          types.push({\n            type: type,\n            ...this.chartTypeConfig[type],\n            compatible: this.isChartTypeCompatible(type)\n          });\n        }\n      });\n\n      return types;\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler(newConfig) {\n        // 更新当前图表类型\n        if (newConfig && newConfig.type) {\n          this.currentChartType = newConfig.type;\n        }\n        this.renderChart();\n      }\n    }\n  },\n  mounted() {\n    // 初始化当前图表类型\n    if (this.chartConfig && this.chartConfig.type) {\n      this.currentChartType = this.chartConfig.type;\n    }\n    this.renderChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁图表实例\n    if (this.chartInstance) {\n      this.chartInstance.dispose();\n    }\n  },\n  methods: {\n    // 切换图表类型\n    async switchChartType(newType) {\n      if (newType === this.currentChartType || this.switchingChart) {\n        return;\n      }\n\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n\n      try {\n        this.switchingChart = true;\n\n        // 检查兼容性\n        if (!this.isChartTypeCompatible(newType)) {\n          this.$message.warning(`当前数据不适合 ${this.chartTypeConfig[newType]?.name} 图表`);\n          return;\n        }\n\n        // 更新图表类型\n        this.currentChartType = newType;\n\n        // 创建新的图表配置，复用相同数据\n        const newChartConfig = {\n          ...this.chartConfig,\n          type: newType\n        };\n\n        // 触发重新渲染\n        this.$emit('chart-type-changed', newChartConfig);\n\n        // 如果父组件没有监听事件，直接更新本地配置\n        await this.$nextTick();\n        if (this.chartConfig.type !== newType) {\n          // 直接修改配置并重新渲染\n          this.chartConfig.type = newType;\n          this.renderChart();\n        }\n\n        this.$message.success(`已切换到${this.chartTypeConfig[newType]?.name}`);\n\n      } catch (error) {\n        console.error('图表切换失败:', error);\n        this.$message.error('图表切换失败: ' + error.message);\n      } finally {\n        this.switchingChart = false;\n      }\n    },\n\n    // 检查图表类型兼容性\n    isChartTypeCompatible(chartType) {\n      // 检查是否有数据\n      if (!this.chartConfig || !this.chartConfig.data) {\n        return false;\n      }\n\n      let data = [];\n      if (this.chartConfig.data && Array.isArray(this.chartConfig.data)) {\n        data = this.chartConfig.data;\n      } else if (this.chartConfig.data && this.chartConfig.data.data && Array.isArray(this.chartConfig.data.data)) {\n        data = this.chartConfig.data.data;\n      }\n\n      // 基本检查：是否有数据\n      if (!data || data.length === 0) {\n        return false;\n      }\n\n      // 检查数据格式是否包含必要字段\n      const firstItem = data[0];\n      if (!firstItem || typeof firstItem.value === 'undefined' || !firstItem.field) {\n        return false;\n      }\n\n      // 所有当前支持的图表类型都兼容这种数据格式\n      const supportedTypes = ['bar', 'line', 'pie', 'bar-horizontal'];\n      return supportedTypes.includes(chartType);\n    },\n\n    renderChart() {\n      this.loading = true;\n      this.error = false;\n      \n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n      \n      console.log('开始渲染图表，配置:', this.chartConfig);\n      \n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.loading = false;\n        \n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(this.chartConfig);\n          console.log('生成的echarts选项:', options);\n          \n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n        return;\n      }\n      \n      // 如果没有数据，才调用API获取\n      getData(this.chartConfig).then(response => {\n        console.log('图表数据API响应:', response);\n        this.loading = false;\n        \n        if (!response) {\n          this.error = true;\n          this.errorMsg = '获取数据失败: 响应为空';\n          console.error('图表数据响应为空');\n          return;\n        }\n        \n        if (response.code !== 0) {\n          this.error = true;\n          this.errorMsg = response.msg || '获取数据失败: ' + response.code;\n          console.error('图表数据获取失败:', response);\n          return;\n        }\n        \n        const chartData = response.data || {};\n        console.log('解析后的图表数据:', chartData);\n        \n        try {\n          // 将原始数据转换为echarts选项\n          const options = this.convertToChartOptions(chartData);\n          console.log('生成的echarts选项:', options);\n          \n          // 渲染图表\n          this.drawChart(options);\n        } catch (error) {\n          console.error('处理图表数据失败:', error);\n          this.error = true;\n          this.errorMsg = '处理图表数据失败: ' + error.message;\n        }\n      }).catch(error => {\n        console.error('图表数据获取失败:', error);\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '图表数据获取失败: ' + error.message;\n      });\n    },\n    \n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData) {\n      console.log('转换图表数据为echarts选项，数据:', chartData);\n      \n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch(this.chartConfig.type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n        \n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n        \n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n        \n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        \n        console.log('提取的数据:', extractedData);\n        \n        // 根据提取的数据类型调用不同的处理函数\n        switch(extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    \n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const { data = [], metrics = [] } = chartData;\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const { data = [] } = chartData;\n      \n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n        \n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n        \n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    \n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.chart-container {\n  width: 100%;\n  position: relative;\n  max-width: 650px;\n  margin: 0 auto;\n  z-index: 1;\n  box-sizing: border-box;\n  isolation: isolate; /* 创建新的层叠上下文 */\n}\n\n/* 图表切换工具栏样式 */\n.chart-switcher {\n  background: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 8px 8px 0 0;\n  padding: 12px 16px;\n  margin-bottom: 0;\n}\n\n.chart-switcher-title {\n  font-size: 14px;\n  font-weight: 500;\n  color: #495057;\n  margin-bottom: 8px;\n}\n\n.chart-type-buttons {\n  display: flex;\n  gap: 8px;\n  flex-wrap: wrap;\n}\n\n.chart-type-btn {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 8px 12px;\n  border: 1px solid #dee2e6;\n  border-radius: 6px;\n  background: #fff;\n  color: #495057;\n  font-size: 12px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n  min-width: 70px;\n  justify-content: center;\n}\n\n.chart-type-btn:hover:not(.disabled) {\n  border-color: #409eff;\n  color: #409eff;\n  background: #ecf5ff;\n}\n\n.chart-type-btn.active {\n  border-color: #409eff;\n  background: #409eff;\n  color: #fff;\n}\n\n.chart-type-btn.disabled {\n  background: #f5f5f5;\n  color: #c0c4cc;\n  cursor: not-allowed;\n  border-color: #e4e7ed;\n}\n\n.chart-type-btn i {\n  font-size: 14px;\n}\n\n.switching-loader {\n  position: absolute;\n  top: 50%;\n  right: 4px;\n  transform: translateY(-50%);\n}\n\n.switching-loader i {\n  font-size: 12px;\n  animation: rotating 1s linear infinite;\n}\n\n@keyframes rotating {\n  from { transform: rotate(0deg); }\n  to { transform: rotate(360deg); }\n}\n\n.chart-canvas {\n  width: 100%;\n  height: 350px;\n  position: relative;\n  z-index: 1;\n  border: 1px solid #e9ecef;\n  border-top: none;\n  border-radius: 0 0 8px 8px;\n  background: #fff;\n}\n\n.chart-loading, .chart-error {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  color: #909399;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 2;\n  background: rgba(255,255,255,0.9);\n}\n\n.chart-loading i, .chart-error i {\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.chart-error {\n  color: #F56C6C;\n}\n</style> "], "mappings": ";;;;;;;;;;;;;AAwCA,SAAAA,OAAA;AACA,YAAAC,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACA;IACAC,qBAAA;MACAJ,IAAA,EAAAK,KAAA;MACAF,OAAA,EAAAA,CAAA;IACA;EACA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;MACAC,aAAA;MACAC,cAAA;MACAC,gBAAA;MACA;MACAC,eAAA;QACA;UACAhB,IAAA;UACAiB,IAAA;UACAC,UAAA;QACA;QACA;UACAlB,IAAA;UACAiB,IAAA;UACAC,UAAA;QACA;QACA;UACAlB,IAAA;UACAiB,IAAA;UACAC,UAAA;QACA;QACA;UACAlB,IAAA;UACAiB,IAAA;UACAC,UAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,kBAAA;MACA,YAAAC,mBAAA,CAAAC,MAAA,cAAAZ,OAAA,UAAAC,KAAA;IACA;IAEA;IACAU,oBAAA;MACA,MAAAE,KAAA;;MAEA;MACA,SAAAR,gBAAA,SAAAC,eAAA,MAAAD,gBAAA;QACAQ,KAAA,CAAAC,IAAA;UACArB,IAAA,OAAAY,gBAAA;UACA,QAAAC,eAAA,MAAAD,gBAAA;QACA;MACA;;MAEA;MACA,KAAAR,qBAAA,CAAAkB,OAAA,CAAAtB,IAAA;QACA,IAAAA,IAAA,UAAAY,gBAAA,SAAAC,eAAA,CAAAb,IAAA;UACAoB,KAAA,CAAAC,IAAA;YACArB,IAAA,EAAAA,IAAA;YACA,QAAAa,eAAA,CAAAb,IAAA;YACAe,UAAA,OAAAQ,qBAAA,CAAAvB,IAAA;UACA;QACA;MACA;MAEA,OAAAoB,KAAA;IACA;EACA;EACAI,KAAA;IACAzB,WAAA;MACA0B,IAAA;MACAC,QAAAC,SAAA;QACA;QACA,IAAAA,SAAA,IAAAA,SAAA,CAAA3B,IAAA;UACA,KAAAY,gBAAA,GAAAe,SAAA,CAAA3B,IAAA;QACA;QACA,KAAA4B,WAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACA,SAAA9B,WAAA,SAAAA,WAAA,CAAAC,IAAA;MACA,KAAAY,gBAAA,QAAAb,WAAA,CAAAC,IAAA;IACA;IACA,KAAA4B,WAAA;IACA;IACAE,MAAA,CAAAC,gBAAA,gBAAAC,WAAA;EACA;EACAC,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,WAAA;IACA;IACA,SAAAtB,aAAA;MACA,KAAAA,aAAA,CAAAyB,OAAA;IACA;EACA;EACAC,OAAA;IACA;IACA,MAAAC,gBAAAC,OAAA;MACA,IAAAA,OAAA,UAAA1B,gBAAA,SAAAD,cAAA;QACA;MACA;MAEA4B,OAAA,CAAAC,GAAA,iBAAA5B,gBAAA,QAAA0B,OAAA;MAEA;QACA,KAAA3B,cAAA;;QAEA;QACA,UAAAY,qBAAA,CAAAe,OAAA;UACA,KAAAG,QAAA,CAAAC,OAAA,iBAAA7B,eAAA,CAAAyB,OAAA,GAAAzC,IAAA;UACA;QACA;;QAEA;QACA,KAAAe,gBAAA,GAAA0B,OAAA;;QAEA;QACA,MAAAK,cAAA;UACA,QAAA5C,WAAA;UACAC,IAAA,EAAAsC;QACA;;QAEA;QACA,KAAAM,KAAA,uBAAAD,cAAA;;QAEA;QACA,WAAAE,SAAA;QACA,SAAA9C,WAAA,CAAAC,IAAA,KAAAsC,OAAA;UACA;UACA,KAAAvC,WAAA,CAAAC,IAAA,GAAAsC,OAAA;UACA,KAAAV,WAAA;QACA;QAEA,KAAAa,QAAA,CAAAK,OAAA,aAAAjC,eAAA,CAAAyB,OAAA,GAAAzC,IAAA;MAEA,SAAAW,KAAA;QACA+B,OAAA,CAAA/B,KAAA,YAAAA,KAAA;QACA,KAAAiC,QAAA,CAAAjC,KAAA,cAAAA,KAAA,CAAAuC,OAAA;MACA;QACA,KAAApC,cAAA;MACA;IACA;IAEA;IACAY,sBAAAyB,SAAA;MACA;MACA,UAAAjD,WAAA,UAAAA,WAAA,CAAAO,IAAA;QACA;MACA;MAEA,IAAAA,IAAA;MACA,SAAAP,WAAA,CAAAO,IAAA,IAAAD,KAAA,CAAA4C,OAAA,MAAAlD,WAAA,CAAAO,IAAA;QACAA,IAAA,QAAAP,WAAA,CAAAO,IAAA;MACA,gBAAAP,WAAA,CAAAO,IAAA,SAAAP,WAAA,CAAAO,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAA4C,OAAA,MAAAlD,WAAA,CAAAO,IAAA,CAAAA,IAAA;QACAA,IAAA,QAAAP,WAAA,CAAAO,IAAA,CAAAA,IAAA;MACA;;MAEA;MACA,KAAAA,IAAA,IAAAA,IAAA,CAAAa,MAAA;QACA;MACA;;MAEA;MACA,MAAA+B,SAAA,GAAA5C,IAAA;MACA,KAAA4C,SAAA,WAAAA,SAAA,CAAAC,KAAA,qBAAAD,SAAA,CAAAE,KAAA;QACA;MACA;;MAEA;MACA,MAAAC,cAAA;MACA,OAAAA,cAAA,CAAAC,QAAA,CAAAN,SAAA;IACA;IAEApB,YAAA;MACA,KAAArB,OAAA;MACA,KAAAC,KAAA;;MAEA;MACA,UAAAT,WAAA,UAAAA,WAAA,CAAAC,IAAA;QACA,KAAAO,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA;QACA8B,OAAA,CAAA/B,KAAA,iBAAAT,WAAA;QACA;MACA;MAEAwC,OAAA,CAAAC,GAAA,oBAAAzC,WAAA;;MAEA;MACA,SAAAA,WAAA,CAAAO,IAAA;QACAiC,OAAA,CAAAC,GAAA;QACA,KAAAjC,OAAA;QAEA;UACA;UACA,MAAAgD,OAAA,QAAAC,qBAAA,MAAAzD,WAAA;UACAwC,OAAA,CAAAC,GAAA,kBAAAe,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAA/C,KAAA;UACA+B,OAAA,CAAA/B,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAuC,OAAA;QACA;QACA;MACA;;MAEA;MACApD,OAAA,MAAAI,WAAA,EAAA2D,IAAA,CAAAC,QAAA;QACApB,OAAA,CAAAC,GAAA,eAAAmB,QAAA;QACA,KAAApD,OAAA;QAEA,KAAAoD,QAAA;UACA,KAAAnD,KAAA;UACA,KAAAC,QAAA;UACA8B,OAAA,CAAA/B,KAAA;UACA;QACA;QAEA,IAAAmD,QAAA,CAAAC,IAAA;UACA,KAAApD,KAAA;UACA,KAAAC,QAAA,GAAAkD,QAAA,CAAAE,GAAA,iBAAAF,QAAA,CAAAC,IAAA;UACArB,OAAA,CAAA/B,KAAA,cAAAmD,QAAA;UACA;QACA;QAEA,MAAAG,SAAA,GAAAH,QAAA,CAAArD,IAAA;QACAiC,OAAA,CAAAC,GAAA,cAAAsB,SAAA;QAEA;UACA;UACA,MAAAP,OAAA,QAAAC,qBAAA,CAAAM,SAAA;UACAvB,OAAA,CAAAC,GAAA,kBAAAe,OAAA;;UAEA;UACA,KAAAE,SAAA,CAAAF,OAAA;QACA,SAAA/C,KAAA;UACA+B,OAAA,CAAA/B,KAAA,cAAAA,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAuC,OAAA;QACA;MACA,GAAAgB,KAAA,CAAAvD,KAAA;QACA+B,OAAA,CAAA/B,KAAA,cAAAA,KAAA;QACA,KAAAD,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA,kBAAAD,KAAA,CAAAuC,OAAA;MACA;IACA;IAEA;IACAS,sBAAAM,SAAA;MACAvB,OAAA,CAAAC,GAAA,yBAAAsB,SAAA;;MAEA;MACA,IAAAA,SAAA,CAAAxD,IAAA,IAAAD,KAAA,CAAA4C,OAAA,CAAAa,SAAA,CAAAxD,IAAA;QACAiC,OAAA,CAAAC,GAAA;QACA;QACA,aAAAzC,WAAA,CAAAC,IAAA;UACA;YACA,YAAAgE,kBAAA,CAAAF,SAAA;UACA;YACA,YAAAG,mBAAA,CAAAH,SAAA;UACA;YACA,YAAAI,kBAAA,CAAAJ,SAAA;UACA;YACA,YAAAK,uBAAA,CAAAL,SAAA;UACA;YACA,YAAAM,iBAAA;QACA;MACA;QACA7B,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAA6B,aAAA;;QAEA;QACA,IAAAP,SAAA,CAAAxD,IAAA,IAAAwD,SAAA,CAAAxD,IAAA,CAAAA,IAAA;UACAiC,OAAA,CAAAC,GAAA;UACA6B,aAAA;YACArE,IAAA,EAAA8D,SAAA,CAAAxD,IAAA,CAAAN,IAAA,SAAAD,WAAA,CAAAC,IAAA;YACAM,IAAA,EAAAwD,SAAA,CAAAxD,IAAA,CAAAA,IAAA;YACAgE,OAAA,EAAAR,SAAA,CAAAxD,IAAA,CAAAiE,MAAA,GAAAT,SAAA,CAAAxD,IAAA,CAAAiE,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;UACA;QACA;;QAEA;QACA,KAAAL,aAAA;UACA9B,OAAA,CAAAC,GAAA;UACA,YAAA4B,iBAAA;QACA;QAEA7B,OAAA,CAAAC,GAAA,WAAA6B,aAAA;;QAEA;QACA,QAAAA,aAAA,CAAArE,IAAA;UACA;YACA,YAAAgE,kBAAA,CAAAK,aAAA;UACA;YACA,YAAAJ,mBAAA,CAAAI,aAAA;UACA;YACA,YAAAH,kBAAA,CAAAG,aAAA;UACA;YACA,YAAAF,uBAAA,CAAAE,aAAA;UACA;YACA,YAAAD,iBAAA;QACA;MACA;IACA;IAEA;IACAJ,mBAAAF,SAAA;MACAvB,OAAA,CAAAC,GAAA,gBAAAsB,SAAA;;MAEA;MACA,IAAAxD,IAAA;MACA,IAAAgE,OAAA;;MAEA;MACA,IAAAR,SAAA,CAAAxD,IAAA,IAAAD,KAAA,CAAA4C,OAAA,CAAAa,SAAA,CAAAxD,IAAA;QACAA,IAAA,GAAAwD,SAAA,CAAAxD,IAAA;QACAgE,OAAA,GAAAR,SAAA,CAAAQ,OAAA;MACA;MACA;MAAA,KACA,IAAAR,SAAA,CAAAxD,IAAA,IAAAwD,SAAA,CAAAxD,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAA4C,OAAA,CAAAa,SAAA,CAAAxD,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAwD,SAAA,CAAAxD,IAAA,CAAAA,IAAA;QACAgE,OAAA,GAAAR,SAAA,CAAAxD,IAAA,CAAAiE,MAAA,GACAT,SAAA,CAAAxD,IAAA,CAAAiE,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAnC,OAAA,CAAAC,GAAA,YAAAlC,IAAA;MACAiC,OAAA,CAAAC,GAAA,QAAA8B,OAAA;;MAEA;MACA,MAAAK,SAAA,GAAArE,IAAA,CAAAsE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAzB,KAAA,IAAAyB,IAAA,CAAAhF,IAAA;;MAEA;MACA,MAAAiF,MAAA;MACA,IAAAxE,IAAA,CAAAa,MAAA,QAAAb,IAAA,IAAAwE,MAAA;QACA;QACA,MAAAC,aAAA,OAAAC,GAAA;QACA1E,IAAA,CAAAgB,OAAA,CAAAuD,IAAA;UACA,IAAAA,IAAA,CAAAC,MAAA;YACAD,IAAA,CAAAC,MAAA,CAAAxD,OAAA,CAAA2D,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAA/E,KAAA,CAAAgF,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAA9D,OAAA,CAAA6D,QAAA;UACA,MAAAG,UAAA,GAAAhF,IAAA,CAAAsE,GAAA,CAAAC,IAAA;YACA,MAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA,EAAAS,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAL,MAAA,GAAAA,MAAA,CAAA3B,KAAA;UACA;UAEA2B,MAAA,CAAAzD,IAAA;YACAxB,IAAA,EAAAsF,QAAA;YACAnF,IAAA;YACAM,IAAA,EAAAgF;UACA;QACA;MACA;QACA;QACAR,MAAA,CAAAzD,IAAA;UACAxB,IAAA,EAAAyE,OAAA,KAAAzE,IAAA;UACAG,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAAsE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA1B,KAAA;QACA;MACA;MAEA;QACAqC,KAAA;UACAC,IAAA,OAAA1F,WAAA,CAAAyF,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA5F,IAAA;UACA;QACA;QACA6F,MAAA;UACAvF,IAAA,EAAAwE,MAAA,CAAAF,GAAA,CAAAK,CAAA,IAAAA,CAAA,CAAApF,IAAA;QACA;QACAiG,KAAA;UACA9F,IAAA;UACAM,IAAA,EAAAqE;QACA;QACAoB,KAAA;UACA/F,IAAA;QACA;QACA8E,MAAA,EAAAA;MACA;IACA;IAEA;IACAb,oBAAAH,SAAA;MACA;QAAAxD,IAAA;QAAAgE,OAAA;MAAA,IAAAR,SAAA;;MAEA;MACA,MAAAa,SAAA,GAAArE,IAAA,CAAAsE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAzB,KAAA;;MAEA;MACA,MAAA0B,MAAA;MACA,IAAAxE,IAAA,CAAAa,MAAA,QAAAb,IAAA,IAAAwE,MAAA;QACA;QACA,MAAAC,aAAA,OAAAC,GAAA;QACA1E,IAAA,CAAAgB,OAAA,CAAAuD,IAAA;UACA,IAAAA,IAAA,CAAAC,MAAA;YACAD,IAAA,CAAAC,MAAA,CAAAxD,OAAA,CAAA2D,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAA/E,KAAA,CAAAgF,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAA9D,OAAA,CAAA6D,QAAA;UACA,MAAAG,UAAA,GAAAhF,IAAA,CAAAsE,GAAA,CAAAC,IAAA;YACA,MAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA,EAAAS,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAL,MAAA,GAAAA,MAAA,CAAA3B,KAAA;UACA;UAEA2B,MAAA,CAAAzD,IAAA;YACAxB,IAAA,EAAAsF,QAAA;YACAnF,IAAA;YACAM,IAAA,EAAAgF;UACA;QACA;MACA;QACA;QACAR,MAAA,CAAAzD,IAAA;UACAxB,IAAA,EAAAyE,OAAA,KAAAzE,IAAA;UACAG,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAAsE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA1B,KAAA;QACA;MACA;MAEA;QACAqC,KAAA;UACAC,IAAA,OAAA1F,WAAA,CAAAyF,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAE,MAAA;UACAvF,IAAA,EAAAwE,MAAA,CAAAF,GAAA,CAAAK,CAAA,IAAAA,CAAA,CAAApF,IAAA;QACA;QACAiG,KAAA;UACA9F,IAAA;UACAM,IAAA,EAAAqE;QACA;QACAoB,KAAA;UACA/F,IAAA;QACA;QACA8E,MAAA,EAAAA;MACA;IACA;IAEA;IACAZ,mBAAAJ,SAAA;MACA;QAAAxD,IAAA;MAAA,IAAAwD,SAAA;MAEA,MAAAwB,UAAA,GAAAhF,IAAA,CAAAsE,GAAA,CAAAC,IAAA;QACAhF,IAAA,EAAAgF,IAAA,CAAAzB,KAAA;QACAD,KAAA,EAAA0B,IAAA,CAAA1B;MACA;MAEA;QACAqC,KAAA;UACAC,IAAA,OAAA1F,WAAA,CAAAyF,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAK,SAAA;QACA;QACAH,MAAA;UACAI,MAAA;UACAC,KAAA;UACAC,GAAA;UACA7F,IAAA,EAAAgF,UAAA,CAAAV,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAhF,IAAA;QACA;QACAiF,MAAA;UACAjF,IAAA;UACAG,IAAA;UACAoG,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACAH,KAAA;cACAC,IAAA;cACAG,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAL,IAAA;UACA;UACAjG,IAAA,EAAAgF;QACA;MACA;IACA;IAEA;IACAnB,wBAAAL,SAAA;MACAvB,OAAA,CAAAC,GAAA,gBAAAsB,SAAA;;MAEA;MACA,IAAAxD,IAAA;MACA,IAAAgE,OAAA;;MAEA;MACA,IAAAR,SAAA,CAAAxD,IAAA,IAAAD,KAAA,CAAA4C,OAAA,CAAAa,SAAA,CAAAxD,IAAA;QACAA,IAAA,GAAAwD,SAAA,CAAAxD,IAAA;QACAgE,OAAA,GAAAR,SAAA,CAAAQ,OAAA;MACA;MACA;MAAA,KACA,IAAAR,SAAA,CAAAxD,IAAA,IAAAwD,SAAA,CAAAxD,IAAA,CAAAA,IAAA,IAAAD,KAAA,CAAA4C,OAAA,CAAAa,SAAA,CAAAxD,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAwD,SAAA,CAAAxD,IAAA,CAAAA,IAAA;QACAgE,OAAA,GAAAR,SAAA,CAAAxD,IAAA,CAAAiE,MAAA,GACAT,SAAA,CAAAxD,IAAA,CAAAiE,MAAA,CAAAC,MAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAnC,OAAA,CAAAC,GAAA,YAAAlC,IAAA;MACAiC,OAAA,CAAAC,GAAA,QAAA8B,OAAA;;MAEA;MACA,MAAAuC,SAAA,GAAAvG,IAAA,CAAAsE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAzB,KAAA,IAAAyB,IAAA,CAAAhF,IAAA;;MAEA;MACA,MAAAiF,MAAA;MACA,IAAAxE,IAAA,CAAAa,MAAA,QAAAb,IAAA,IAAAwE,MAAA;QACA;QACA,MAAAC,aAAA,OAAAC,GAAA;QACA1E,IAAA,CAAAgB,OAAA,CAAAuD,IAAA;UACA,IAAAA,IAAA,CAAAC,MAAA;YACAD,IAAA,CAAAC,MAAA,CAAAxD,OAAA,CAAA2D,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAA/E,KAAA,CAAAgF,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAA9D,OAAA,CAAA6D,QAAA;UACA,MAAAG,UAAA,GAAAhF,IAAA,CAAAsE,GAAA,CAAAC,IAAA;YACA,MAAAC,MAAA,GAAAD,IAAA,CAAAC,MAAA,EAAAS,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAL,MAAA,GAAAA,MAAA,CAAA3B,KAAA;UACA;UAEA2B,MAAA,CAAAzD,IAAA;YACAxB,IAAA,EAAAsF,QAAA;YACAnF,IAAA;YAAA;YACAM,IAAA,EAAAgF;UACA;QACA;MACA;QACA;QACAR,MAAA,CAAAzD,IAAA;UACAxB,IAAA,EAAAyE,OAAA,KAAAzE,IAAA;UACAG,IAAA;UAAA;UACAM,IAAA,EAAAA,IAAA,CAAAsE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA1B,KAAA;QACA;MACA;MAEA;QACAqC,KAAA;UACAC,IAAA,OAAA1F,WAAA,CAAAyF,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACA5F,IAAA;UACA;QACA;QACA6F,MAAA;UACAvF,IAAA,EAAAwE,MAAA,CAAAF,GAAA,CAAAK,CAAA,IAAAA,CAAA,CAAApF,IAAA;QACA;QACA;QACAiG,KAAA;UACA9F,IAAA;QACA;QACA+F,KAAA;UACA/F,IAAA;UAAA;UACAM,IAAA,EAAAuG;QACA;QACA/B,MAAA,EAAAA;MACA;IACA;IAEA;IACAV,kBAAA;MACA7B,OAAA,CAAAC,GAAA;MACA;QACAgD,KAAA;UACAC,IAAA,OAAA1F,WAAA,CAAAyF,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAG,KAAA;UACA9F,IAAA;UACAM,IAAA;QACA;QACAyF,KAAA;UACA/F,IAAA;QACA;QACA8E,MAAA;UACA9E,IAAA,OAAAD,WAAA,CAAAC,IAAA;UACAM,IAAA;QACA;MACA;IACA;IAEA;IACAmD,UAAAF,OAAA;MACA;QACAhB,OAAA,CAAAC,GAAA;QACA,UAAAsE,KAAA,CAAAC,QAAA;UACAxE,OAAA,CAAA/B,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA;UACA;QACA;;QAEA;QACA,SAAAC,aAAA;UACA,KAAAA,aAAA,CAAAyB,OAAA;QACA;;QAEA;QACA,KAAAzB,aAAA,GAAAd,OAAA,CAAAoH,IAAA,MAAAF,KAAA,CAAAC,QAAA;QACA,KAAArG,aAAA,CAAAuG,SAAA,CAAA1D,OAAA;QACAhB,OAAA,CAAAC,GAAA;MACA,SAAAhC,KAAA;QACA+B,OAAA,CAAA/B,KAAA,YAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,gBAAAD,KAAA,CAAAuC,OAAA;MACA;IACA;IAEA;IACAf,YAAA;MACA,SAAAtB,aAAA;QACA,KAAAA,aAAA,CAAAwG,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}