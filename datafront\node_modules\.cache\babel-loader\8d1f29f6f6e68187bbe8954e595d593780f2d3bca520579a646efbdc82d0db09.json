{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { dataApi } from './api/index.js';\nimport { v4 as uuidv4 } from 'uuid';\nimport axios from 'axios';\nimport ChartDisplay from './components/ChartDisplay.vue';\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas';\nimport { jsPDF } from 'jspdf';\nimport * as echarts from 'echarts';\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [],\n      // 原始树结构\n      datasets: [],\n      // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [],\n      // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false,\n      // 新增：控制原始响应弹出层\n      lastRawResponse: '',\n      // 新增：存储最后收到的原始响应\n      drawer: false,\n      //抽屉展示\n      direction: 'rtl',\n      //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      apiToken: 'eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2OTEyYzNiLTQ5ZmMtNDAyMC1iMzRiLWE3YWJlNmY1MTI0MSJ9.J4QCC6qh2GZ7ENDHuZQWQ1GvGOQ1HOr8gW34KXIHV9N_73Jppx-qmEwpq0AHlXM3DUjd5EAlpIVAvzJygm_ldg',\n      // 示例token，实际应该从登录后存储\n      currentAnalysisDataset: null,\n      // 存储当前用于智能分析的数据集信息\n      selectedIndicators: [],\n      // 选中的指标字段ID\n      selectedDimensions: [],\n      // 选中的维度字段ID\n      innerDrawer: false,\n      tableList: [],\n      exportingAll: false,\n      // 新增：控制完整对话导出状态\n      activeTab: 'fields',\n      // 新增：控制数据集详情页面的标签页切换，默认显示字段信息\n      fieldSearchKeyword: '',\n      // 字段搜索关键词\n      // 数据预览分页相关\n      currentPage: 1,\n      // 当前页码\n      pageSize: 20 // 每页显示条数\n    };\n  },\n  computed: {\n    // 指标字段（数值类型）\n    indicatorFields() {\n      if (!this.currentAnalysisDataset || !this.currentAnalysisDataset.fields) {\n        return [];\n      }\n      return this.currentAnalysisDataset.fields.filter(field => field.deType === 1 ||\n      // 数值类型\n      field.groupType === 'q' ||\n      // 度量字段\n      ['LONG', 'DOUBLE', 'BIGINT', 'INT', 'FLOAT', 'DECIMAL'].includes(field.type?.toUpperCase()));\n    },\n    // 维度字段（文本、日期等）\n    dimensionFields() {\n      if (!this.currentAnalysisDataset || !this.currentAnalysisDataset.fields) {\n        return [];\n      }\n      return this.currentAnalysisDataset.fields.filter(field => field.deType === 0 ||\n      // 文本类型\n      field.groupType === 'd' ||\n      // 维度字段\n      ['TEXT', 'STRING', 'VARCHAR', 'DATE', 'DATETIME', 'TIMESTAMP'].includes(field.type?.toUpperCase()));\n    },\n    // 过滤后的字段列表\n    filteredFields() {\n      if (!this.fieldSearchKeyword) {\n        return this.datasetFields;\n      }\n      return this.datasetFields.filter(field => field.name.toLowerCase().includes(this.fieldSearchKeyword.toLowerCase()) || field.dataeaseName && field.dataeaseName.toLowerCase().includes(this.fieldSearchKeyword.toLowerCase()));\n    },\n    // 维度字段数量\n    dimensionFieldsCount() {\n      return this.datasetFields.filter(field => field.groupType === 'd').length;\n    },\n    // 度量字段数量\n    measureFieldsCount() {\n      return this.datasetFields.filter(field => field.groupType === 'q').length;\n    },\n    // 数据预览分页相关计算属性\n    totalRecords() {\n      return Math.min(this.datasetData.length, 100); // 最多显示100条\n    },\n    currentPageData() {\n      const start = (this.currentPage - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      return this.datasetData.slice(start, Math.min(end, 100)); // 确保不超过100条\n    }\n  },\n  mounted() {\n    // 存储token到localStorage，供API使用\n    localStorage.setItem('de_token', this.apiToken);\n    this.loadTables();\n    this.initMemoryId();\n    this.addWelcomeMessage();\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\"帮我生成一个销售额柱状图\", \"展示近六个月的销售趋势折线图\", \"按照区域统计销售量并生成饼图\", \"帮我做一个按产品类别的销量对比图\"];\n    // 添加点击外部关闭下拉框的监听\n    document.addEventListener('click', this.handleClickOutside);\n  },\n  beforeDestroy() {\n    // 移除点击外部监听\n    document.removeEventListener('click', this.handleClickOutside);\n  },\n  updated() {\n    this.scrollToBottom();\n  },\n  methods: {\n    // 处理图表类型切换\n    onChartTypeChanged(newChartConfig) {\n      console.log('图表类型已切换:', newChartConfig);\n\n      // 找到对应的消息并更新其图表配置\n      for (let message of this.messages) {\n        if (message.chartConfig && message.chartConfig.id === newChartConfig.id) {\n          message.chartConfig = newChartConfig;\n          break;\n        }\n      }\n    },\n    SelectDataList() {\n      this.loadTables();\n      this.drawer = true;\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id');\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4());\n        localStorage.setItem('user_memory_id', storedMemoryId);\n      }\n      this.memoryId = storedMemoryId;\n    },\n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0;\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i];\n        number = number * 16 + (parseInt(hexValue, 16) || 0);\n      }\n      return number % 1000000;\n    },\n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      });\n    },\n    // 清除欢迎消息\n    clearWelcomeMessage() {\n      // 移除AI的欢迎消息（通常是第一条消息）\n      this.messages = this.messages.filter(message => message.isUser || !message.content.includes('您好！我是数据助手'));\n    },\n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight;\n      }\n    },\n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables();\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data;\n          this.datasets = this.flattenDatasets(this.tables);\n          this.filteredDatasets = this.datasets;\n        } else {\n          this.tables = [];\n          this.datasets = [];\n          this.filteredDatasets = [];\n        }\n      } catch (e) {\n        this.tables = [];\n        this.datasets = [];\n        this.filteredDatasets = [];\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = [];\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node);\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children));\n        }\n      }\n      return result;\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase();\n      if (!keyword) {\n        this.filteredDatasets = this.datasets;\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword));\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n\n    // 测试详情请求方法\n    // async testDetailRequest() {\n    //   if (this.datasets.length > 0) {\n    //     const testDataset = this.datasets[0];\n    //     console.log('测试数据集:', testDataset);\n    //     alert(`正在请求数据集详情，ID: ${testDataset.id}`);\n    //     await this.showDatasetDetail(testDataset);\n    //   } else {\n    //     alert('没有可用的数据集');\n    //   }\n    // },\n\n    // 智能分析数据集\n    analyzeDataset(dataset = null) {\n      // 如果传入了数据集参数，先设置为当前数据集\n      if (dataset) {\n        this.showDatasetDetail(dataset);\n        // 等待数据加载完成后再进行分析\n        this.$nextTick(() => {\n          this.performAnalysis();\n        });\n        return;\n      }\n      this.performAnalysis();\n    },\n    // 重置到首页状态\n    resetToHome() {\n      // 清除当前选择的数据集\n      this.currentAnalysisDataset = null;\n      this.currentDatasetDetail = null;\n      this.datasetFields = [];\n      this.datasetData = [];\n\n      // 关闭所有抽屉和对话框\n      this.dialogVisible = false;\n      this.drawerVisible = false;\n\n      // 清除聊天记录（可选，根据需求决定）\n      // this.messages = [];\n\n      // 重置其他状态\n      this.activeTab = 'fields';\n      this.currentPage = 1;\n      this.selectedIndicators = [];\n      this.selectedDimensions = [];\n\n      // 显示欢迎消息\n      this.showWelcomeMessage();\n\n      // 提示用户\n      this.$message.success('已返回首页，请选择数据集开始分析');\n    },\n    // 显示欢迎消息\n    showWelcomeMessage() {\n      // 如果没有消息或者第一条不是欢迎消息，则添加欢迎消息\n      if (this.messages.length === 0 || this.messages[0].type !== 'bot' || !this.messages[0].content.includes('欢迎')) {\n        this.messages.unshift({\n          type: 'bot',\n          content: '您好！欢迎使用智能问数系统。请先选择一个数据集，然后您就可以用自然语言提问了。',\n          timestamp: new Date().toLocaleTimeString()\n        });\n      }\n    },\n    // 快速分析数据集（从卡片悬停按钮触发）\n    async quickAnalyzeDataset(dataset) {\n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n      try {\n        // 获取数据集详情和字段信息\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          // 获取字段信息\n          const fields = detail.allFields || detail.data && detail.data.fields || [];\n\n          // 提取所需的字段信息\n          const fieldsInfo = fields.map(field => {\n            return {\n              id: field.id,\n              originName: field.originName || '',\n              name: field.name || '',\n              dataeaseName: field.dataeaseName || '',\n              groupType: field.groupType || '',\n              type: field.type || '',\n              datasourceId: field.datasourceId || '',\n              datasetTableId: field.datasetTableId || '',\n              datasetGroupId: field.datasetGroupId || ''\n            };\n          });\n\n          // 存储当前数据集信息\n          this.currentAnalysisDataset = {\n            id: dataset.id,\n            name: dataset.name,\n            fields: fieldsInfo\n          };\n\n          // 清除AI的欢迎消息\n          this.clearWelcomeMessage();\n\n          // 提示用户\n          this.$message.success(`已选择数据集\"${dataset.name}\"，可以开始提问了`);\n\n          // 自动聚焦到问题输入框\n          this.$nextTick(() => {\n            const inputEl = document.querySelector('.question-input input');\n            if (inputEl) inputEl.focus();\n          });\n        } else {\n          this.$message.error('获取数据集信息失败，请重试');\n        }\n      } catch (error) {\n        console.error('获取数据集字段失败:', error);\n        this.$message.error('获取数据集字段失败，请重试');\n      }\n    },\n    // 执行分析的具体逻辑\n    performAnalysis() {\n      console.log('performAnalysis 被调用');\n      console.log('currentDatasetDetail:', this.currentDatasetDetail);\n      console.log('datasetFields:', this.datasetFields);\n      if (!this.currentDatasetDetail || !this.datasetFields.length) {\n        console.log('数据集或字段信息缺失');\n        this.$message.warning('当前数据集没有可用字段');\n        return;\n      }\n\n      // 提取所需的字段信息\n      const fieldsInfo = this.datasetFields.map(field => {\n        // 只保留需要的字段属性\n        return {\n          id: field.id,\n          originName: field.originName || '',\n          name: field.name || '',\n          dataeaseName: field.dataeaseName || '',\n          groupType: field.groupType || '',\n          type: field.type || '',\n          datasourceId: field.datasourceId || '',\n          datasetTableId: field.datasetTableId || '',\n          datasetGroupId: field.datasetGroupId || ''\n        };\n      });\n\n      // 存储当前数据集信息，包括ID和字段\n      this.currentAnalysisDataset = {\n        id: this.currentDatasetDetail.id,\n        name: this.currentDatasetDetail.name,\n        fields: fieldsInfo\n      };\n\n      // 选择数据集后清除AI的欢迎消息\n      this.clearWelcomeMessage();\n\n      // 关闭详情抽屉，返回到聊天界面\n      this.dialogVisible = false;\n\n      // 提示用户\n      this.$message.success(`已选择数据集\"${this.currentDatasetDetail.name}\"进行智能分析，请在下方输入您的问题`);\n\n      // 自动聚焦到问题输入框\n      this.$nextTick(() => {\n        const inputEl = document.querySelector('.question-input input');\n        if (inputEl) inputEl.focus();\n      });\n    },\n    // 切换字段选择\n    toggleFieldSelection(type, field) {\n      if (type === 'indicator') {\n        const index = this.selectedIndicators.indexOf(field.id);\n        if (index > -1) {\n          this.selectedIndicators.splice(index, 1);\n        } else {\n          this.selectedIndicators.push(field.id);\n        }\n      } else if (type === 'dimension') {\n        const index = this.selectedDimensions.indexOf(field.id);\n        if (index > -1) {\n          this.selectedDimensions.splice(index, 1);\n        } else {\n          this.selectedDimensions.push(field.id);\n        }\n      }\n    },\n    // 切换数据集\n    switchDataset() {\n      this.currentAnalysisDataset = null;\n      this.selectedIndicators = [];\n      this.selectedDimensions = [];\n      this.$message.info('请重新选择数据集');\n    },\n    async showDatasetDetail(dataset) {\n      // alert(`showDatasetDetail 被调用，数据集ID: ${dataset?.id || '未知'}`);\n      console.log('showDatasetDetail 被调用，参数:', dataset);\n\n      // 验证参数\n      if (!dataset || !dataset.id) {\n        console.error('无效的数据集参数:', dataset);\n        // alert('数据集参数无效，缺少ID');\n        return;\n      }\n\n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n      try {\n        console.log(`开始请求数据集详情，ID: ${dataset.id}`);\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n        console.log('数据集详情API响应:', res);\n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          console.log('数据集详情数据:', detail);\n          this.currentDatasetDetail = detail;\n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || detail.data && detail.data.fields || [];\n          // 数据内容\n          this.datasetData = detail.data && detail.data.data || [];\n          // 重置标签页到字段信息\n          this.activeTab = 'fields';\n          // 重置分页状态\n          this.currentPage = 1;\n          // 调试打印\n          console.log(`字段信息: ${this.datasetFields.length}个字段`);\n          console.log(`数据内容: ${this.datasetData.length}条记录`);\n          if (this.datasetFields.length === 0) {\n            console.warn('未找到字段信息');\n          }\n          if (this.datasetData.length === 0) {\n            console.warn('未找到数据内容');\n          }\n        } else {\n          console.error('API返回错误:', res.data);\n          alert(`API返回错误: ${res.data.msg || '未知错误'}`);\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n          this.activeTab = 'fields'; // 重置标签页\n        }\n        this.dialogVisible = true;\n      } catch (e) {\n        console.error('请求数据集详情失败:', e);\n        alert(`请求失败: ${e.message || '未知错误'}`);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel;\n    },\n    useQuestion(q) {\n      this.question = q;\n      this.showSuggestionsPanel = false;\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return [];\n      }\n      return [];\n    },\n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题');\n        return;\n      }\n\n      // 获取当前选中的数据集信息\n      // 构建发送给AI的完整消息\n      let aiMessage = this.question.trim();\n\n      // 如果有当前分析的数据集，添加数据集信息\n      if (this.currentAnalysisDataset) {\n        // 构建AI需要的格式\n        const datasetInfo = {\n          datasetId: this.currentAnalysisDataset.id,\n          datasetName: this.currentAnalysisDataset.name,\n          fields: this.currentAnalysisDataset.fields\n        };\n\n        // 将数据集信息添加到消息中\n        aiMessage = JSON.stringify({\n          question: this.question.trim(),\n          dataset: datasetInfo\n        });\n      }\n\n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      };\n      this.messages.push(userMsg);\n\n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      };\n      this.messages.push(botMsg);\n\n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1];\n\n      // 保存问题并清空输入框\n      const question = this.question;\n      this.question = '';\n      this.isSending = true;\n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = this.currentAnalysisDataset ? aiMessage : `${question}。当前数据集：未选择具体数据集`;\n\n        // 发送请求\n        await axios.post('http://localhost:8088/api/indicator/chat', {\n          memoryId: this.memoryId,\n          message\n        }, {\n          responseType: 'stream',\n          onDownloadProgress: e => {\n            const fullText = e.event.target.responseText; // 累积的完整文本\n            let newText = fullText.substring(lastMsg.content.length);\n            lastMsg.content += newText; // 增量更新\n            this.scrollToBottom(); // 实时滚动\n\n            // 保存原始响应\n            this.lastRawResponse = fullText;\n          }\n        });\n\n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false;\n        this.isSending = false;\n\n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error);\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。';\n        lastMsg.isTyping = false;\n        this.isSending = false;\n      }\n    },\n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n\n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n\n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n\n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n\n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n\n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n\n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, '<div class=\"chart-notice\"></div>');\n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n\n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n\n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n\n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], '<div class=\"chart-notice\"></div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n\n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n\n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n\n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n\n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n\n          // 设置所有支持的图表类型（除了当前类型）\n          message.recommendedChartTypes = this.getAllSupportedChartTypes(chartConfig.type);\n\n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n\n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n    // 获取所有支持的图表类型\n    getAllSupportedChartTypes() {\n      const allTypes = ['bar', 'line', 'pie', 'bar-horizontal'];\n      return allTypes;\n    },\n    // 获取图表类型的图标\n    getChartTypeIcon(chartType) {\n      const iconMap = {\n        'bar': 'chart-icon-bar',\n        'line': 'chart-icon-line',\n        'pie': 'chart-icon-pie',\n        'bar-horizontal': 'chart-icon-bar-horizontal'\n      };\n      return iconMap[chartType] || 'chart-icon-bar';\n    },\n    // 获取图表类型的名称\n    getChartTypeName(chartType) {\n      const nameMap = {\n        'bar': '柱图',\n        'line': '线图',\n        'pie': '饼图',\n        'bar-horizontal': '条形图'\n      };\n      return nameMap[chartType] || '柱图';\n    },\n    // 获取当前图表类型的图标\n    getCurrentChartIcon(chartType) {\n      return this.getChartTypeIcon(chartType);\n    },\n    // 切换图表下拉框状态\n    toggleChartDropdown(message) {\n      this.$set(message, 'dropdownOpen', !message.dropdownOpen);\n    },\n    // 切换消息的图表类型\n    switchMessageChartType(message, newType) {\n      if (newType === message.chartConfig.type) {\n        message.dropdownOpen = false;\n        return;\n      }\n\n      // 更新图表配置的类型\n      this.$set(message.chartConfig, 'type', newType);\n\n      // 关闭下拉框\n      message.dropdownOpen = false;\n\n      // 触发图表重新渲染\n      this.$nextTick(() => {\n        // 强制更新组件\n        this.$forceUpdate();\n      });\n    },\n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置，包含完整数据\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\",\n        data: {\n          data: [{\n            field: \"北京\",\n            name: \"北京\",\n            value: 1200,\n            category: \"销售额\"\n          }, {\n            field: \"上海\",\n            name: \"上海\",\n            value: 980,\n            category: \"销售额\"\n          }, {\n            field: \"广州\",\n            name: \"广州\",\n            value: 850,\n            category: \"销售额\"\n          }, {\n            field: \"深圳\",\n            name: \"深圳\",\n            value: 1100,\n            category: \"销售额\"\n          }, {\n            field: \"杭州\",\n            name: \"杭州\",\n            value: 720,\n            category: \"销售额\"\n          }],\n          fields: [{\n            id: \"region_id\",\n            name: \"地区\",\n            groupType: \"d\"\n          }, {\n            id: \"sales_id\",\n            name: \"销售额\",\n            groupType: \"q\"\n          }]\n        }\n      };\n\n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig,\n        recommendedChartTypes: ['line', 'pie', 'bar-horizontal'] // 所有其他图表类型\n      };\n      this.messages.push(botMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n\n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n\n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [{\n            field: '类别1',\n            value: 100\n          }, {\n            field: '类别2',\n            value: 200\n          }, {\n            field: '类别3',\n            value: 150\n          }],\n          metrics: [{\n            name: '数值'\n          }]\n        }\n      };\n\n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n\n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [{\n              value: 1,\n              field: \"神朔\",\n              name: \"神朔\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"甘泉\",\n              name: \"甘泉\",\n              category: \"记录数*\"\n            }, {\n              value: 1,\n              field: \"包神\",\n              name: \"包神\",\n              category: \"记录数*\"\n            }],\n            fields: [{\n              id: \"1746787308487\",\n              name: \"名称\",\n              groupType: \"d\"\n            }, {\n              id: \"-1\",\n              name: \"记录数*\",\n              groupType: \"q\"\n            }]\n          }\n        }\n      };\n\n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n\n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n\n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n\n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n    // 获取字段类型的CSS类名\n    getFieldTypeClass(type) {\n      const typeMap = {\n        'TEXT': 'text-type',\n        'STRING': 'text-type',\n        'VARCHAR': 'text-type',\n        'INT': 'number-type',\n        'INTEGER': 'number-type',\n        'BIGINT': 'number-type',\n        'DECIMAL': 'number-type',\n        'DOUBLE': 'number-type',\n        'FLOAT': 'number-type',\n        'DATE': 'date-type',\n        'DATETIME': 'date-type',\n        'TIMESTAMP': 'date-type',\n        'BOOLEAN': 'boolean-type'\n      };\n      return typeMap[type?.toUpperCase()] || 'default-type';\n    },\n    // 获取字段类型的显示标签\n    getFieldTypeLabel(type) {\n      const labelMap = {\n        'TEXT': '文本',\n        'STRING': '文本',\n        'VARCHAR': '文本',\n        'INT': '数值',\n        'INTEGER': '数值',\n        'BIGINT': '数值',\n        'DECIMAL': '数值',\n        'DOUBLE': '数值',\n        'FLOAT': '数值',\n        'DATE': '日期',\n        'DATETIME': '日期时间',\n        'TIMESTAMP': '时间戳',\n        'BOOLEAN': '布尔'\n      };\n      return labelMap[type?.toUpperCase()] || type || '未知';\n    },\n    // 获取字段描述\n    getFieldDescription(field) {\n      if (field.description) {\n        return field.description;\n      }\n\n      // 根据字段类型和分组类型生成默认描述\n      const typeDesc = this.getFieldTypeLabel(field.type);\n      const groupDesc = field.groupType === 'd' ? '维度' : '度量';\n\n      // 根据字段名称推测用途\n      const fieldName = (field.name || '').toLowerCase();\n      let purposeDesc = '';\n      if (fieldName.includes('id') || fieldName.includes('编号')) {\n        purposeDesc = '，用于唯一标识';\n      } else if (fieldName.includes('name') || fieldName.includes('名称')) {\n        purposeDesc = '，用于分类标识';\n      } else if (fieldName.includes('date') || fieldName.includes('时间') || fieldName.includes('日期')) {\n        purposeDesc = '，用于时间分析';\n      } else if (fieldName.includes('amount') || fieldName.includes('金额') || fieldName.includes('价格')) {\n        purposeDesc = '，用于金额统计';\n      } else if (fieldName.includes('count') || fieldName.includes('数量') || fieldName.includes('个数')) {\n        purposeDesc = '，用于数量统计';\n      } else if (field.groupType === 'q') {\n        purposeDesc = '，用于数值分析';\n      } else {\n        purposeDesc = '，用于数据分组';\n      }\n      return `${typeDesc}类型的${groupDesc}字段${purposeDesc}`;\n    },\n    // 获取字段图标类名\n    getFieldIconClass(field) {\n      const typeMap = {\n        'TEXT': 'text-icon',\n        'STRING': 'text-icon',\n        'VARCHAR': 'text-icon',\n        'INT': 'number-icon',\n        'INTEGER': 'number-icon',\n        'BIGINT': 'number-icon',\n        'DECIMAL': 'number-icon',\n        'DOUBLE': 'number-icon',\n        'FLOAT': 'number-icon',\n        'DATE': 'date-icon',\n        'DATETIME': 'date-icon',\n        'TIMESTAMP': 'date-icon',\n        'BOOLEAN': 'boolean-icon'\n      };\n      return typeMap[field.type?.toUpperCase()] || 'default-icon';\n    },\n    // 获取字段图标名称\n    getFieldIconName(field) {\n      const iconMap = {\n        'TEXT': 'el-icon-document',\n        'STRING': 'el-icon-document',\n        'VARCHAR': 'el-icon-document',\n        'INT': 'el-icon-s-data',\n        'INTEGER': 'el-icon-s-data',\n        'BIGINT': 'el-icon-s-data',\n        'DECIMAL': 'el-icon-s-data',\n        'DOUBLE': 'el-icon-s-data',\n        'FLOAT': 'el-icon-s-data',\n        'DATE': 'el-icon-date',\n        'DATETIME': 'el-icon-time',\n        'TIMESTAMP': 'el-icon-time',\n        'BOOLEAN': 'el-icon-switch-button'\n      };\n      return iconMap[field.type?.toUpperCase()] || 'el-icon-info';\n    },\n    // 数据预览相关方法\n    handlePageChange(page) {\n      this.currentPage = page;\n    },\n    getRowIndex(index) {\n      return (this.currentPage - 1) * this.pageSize + index + 1;\n    },\n    getCellValue(row, field) {\n      const fieldName = field.dataeaseName || field.name;\n      const value = row[fieldName];\n\n      // 处理null、undefined等值\n      if (value === null || value === undefined) {\n        return '-';\n      }\n\n      // 处理日期类型\n      if (field.type && ['DATE', 'DATETIME', 'TIMESTAMP'].includes(field.type.toUpperCase())) {\n        if (value) {\n          try {\n            return new Date(value).toLocaleString();\n          } catch (e) {\n            return value;\n          }\n        }\n      }\n\n      // 处理数值类型\n      if (field.type && ['INT', 'INTEGER', 'BIGINT', 'DECIMAL', 'DOUBLE', 'FLOAT'].includes(field.type.toUpperCase())) {\n        if (typeof value === 'number') {\n          return value.toLocaleString();\n        }\n      }\n      return value;\n    },\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n\n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n\n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n\n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content.replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '').replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '').trim();\n\n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n\n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n\n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n\n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n\n        // 根据图表类型选择对应的处理函数\n        switch (chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n\n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n\n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n\n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n\n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2,\n          // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n\n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = canvas.height * imgWidth / canvas.width;\n\n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n\n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n\n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n\n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n\n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    // 导出完整对话方法\n    async exportAllConversation() {\n      try {\n        // 设置导出状态\n        this.exportingAll = true;\n\n        // 1. 提取所有图表消息\n        let chartMessages = [];\n\n        // 先找出所有包含图表的消息\n        for (let i = 0; i < this.messages.length; i++) {\n          const message = this.messages[i];\n          if (!message.isUser && message.chartConfig) {\n            chartMessages.push({\n              message: message,\n              index: i\n            });\n          }\n        }\n\n        // 如果没有图表，显示提示\n        if (chartMessages.length === 0) {\n          this.$message.warning('暂无图表数据。请先通过对话生成图表。');\n          this.exportingAll = false;\n          return;\n        }\n\n        // 2. 创建PDF\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n\n        // 3. 处理每个图表，每个图表单独一页\n        for (let i = 0; i < chartMessages.length; i++) {\n          // 如果不是第一页，添加新页面\n          if (i > 0) {\n            pdf.addPage();\n          }\n          const chartMessage = chartMessages[i].message;\n          const chartConfig = chartMessage.chartConfig;\n\n          // 创建临时容器\n          const tempContainer = document.createElement('div');\n          tempContainer.style.position = 'absolute';\n          tempContainer.style.left = '-9999px';\n          tempContainer.style.width = '800px';\n          tempContainer.style.background = '#fff';\n          tempContainer.style.padding = '20px';\n          document.body.appendChild(tempContainer);\n\n          // 构建PDF标题和基本信息\n          const title = chartConfig.title || '数据分析图表';\n          const currentDate = new Date().toLocaleString();\n          const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n          let htmlContent = `\n            <div style=\"font-family: Arial, sans-serif; padding: 20px;\">\n              <h1 style=\"text-align: center; color: #333; font-size: 24px; margin-bottom: 30px;\">${title}</h1>\n              <div style=\"text-align: right; margin-bottom: 20px;\">\n                <p style=\"color: #666; margin: 5px 0;\">数据集: ${datasetName}</p>\n                <p style=\"color: #666; margin: 5px 0;\">生成时间: ${currentDate}</p>\n              </div>\n              \n              <div style=\"margin: 20px 0;\">\n                <h2 style=\"color: #333; font-size: 20px; margin-bottom: 15px;\">分析描述:</h2>\n          `;\n\n          // 添加描述\n          let description = chartMessage.content;\n\n          // 清理HTML标签，保留纯文本\n          if (description.includes('<')) {\n            const tempDiv = document.createElement('div');\n            tempDiv.innerHTML = description;\n            description = tempDiv.textContent || tempDiv.innerText || '';\n          }\n          htmlContent += `\n            <p style=\"margin: 15px 0; color: #333; line-height: 1.6;\">${description}</p>\n            \n            <div style=\"margin: 30px 0;\">\n              <div id=\"chart-container\" style=\"height: 400px; margin: 30px 0;\"></div>\n              \n              <div style=\"margin: 30px 0;\">\n                <h3 style=\"color: #333; font-size: 18px; margin-bottom: 15px;\">数据表格:</h3>\n                <div id=\"data-table\"></div>\n              </div>\n            </div>\n          `;\n          htmlContent += `</div>`;\n\n          // 设置HTML内容\n          tempContainer.innerHTML = htmlContent;\n\n          // 渲染图表\n          const chartContainer = tempContainer.querySelector('#chart-container');\n          const dataTableContainer = tempContainer.querySelector('#data-table');\n          if (chartContainer && dataTableContainer) {\n            // 初始化图表\n            const chartInstance = echarts.init(chartContainer);\n            let options;\n\n            // 复用你现有的图表处理逻辑\n            switch (chartConfig.type) {\n              case 'bar':\n                options = this.getBarChartOptions(chartConfig);\n                break;\n              case 'line':\n                options = this.getLineChartOptions(chartConfig);\n                break;\n              case 'pie':\n                options = this.getPieChartOptions(chartConfig);\n                break;\n              case 'bar-horizontal':\n                options = this.getBarHorizontalOptions(chartConfig);\n                break;\n              default:\n                options = this.getDefaultOptions(chartConfig);\n            }\n            chartInstance.setOption(options);\n\n            // 渲染数据表格\n            this.renderDataTable(dataTableContainer, chartConfig);\n\n            // 等待图表渲染完成\n            await new Promise(resolve => setTimeout(resolve, 1000));\n\n            // 使用html2canvas捕获当前页面内容\n            const canvas = await html2canvas(tempContainer, {\n              scale: 2,\n              useCORS: true,\n              allowTaint: true,\n              backgroundColor: '#ffffff'\n            });\n\n            // 将canvas转为图像\n            const imgData = canvas.toDataURL('image/png');\n\n            // 计算适当的宽度和高度以适应A4页面\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            const pageHeight = pdf.internal.pageSize.getHeight();\n            const imgWidth = pageWidth - 40; // 左右各20mm边距\n            const imgHeight = canvas.height * imgWidth / canvas.width;\n\n            // 如果图像高度超过页面高度，进行缩放\n            let finalImgHeight = imgHeight;\n            let finalImgWidth = imgWidth;\n            if (imgHeight > pageHeight - 40) {\n              // 上下各20mm边距\n              finalImgHeight = pageHeight - 40;\n              finalImgWidth = canvas.width * finalImgHeight / canvas.height;\n            }\n\n            // 添加图像到PDF，居中显示\n            const xPos = (pageWidth - finalImgWidth) / 2;\n            const yPos = 20; // 顶部边距\n\n            pdf.addImage(imgData, 'PNG', xPos, yPos, finalImgWidth, finalImgHeight);\n\n            // 清理\n            chartInstance.dispose();\n            document.body.removeChild(tempContainer);\n          }\n        }\n\n        // 4. 保存PDF\n        const fileName = `指标分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;\n        pdf.save(fileName);\n\n        // 5. 重置状态\n        this.exportingAll = false;\n        this.$message.success('指标导出成功');\n      } catch (error) {\n        console.error('指标导出失败:', error);\n        this.exportingAll = false;\n        this.$message.error('指标导出失败: ' + error.message);\n      }\n    },\n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n\n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n\n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n\n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n\n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n\n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n\n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      return options;\n    },\n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n\n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n\n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n\n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n\n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n\n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n\n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n\n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n\n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n};", "map": {"version": 3, "names": ["dataApi", "v4", "uuidv4", "axios", "ChartDisplay", "html2canvas", "jsPDF", "echarts", "name", "components", "data", "description", "tablename", "tables", "datasets", "filteredDatasets", "searchKeyword", "selectedTable", "question", "query<PERSON><PERSON>ult", "showSuggestionsPanel", "suggestedQuestions", "recentChats", "tableIndicators", "dialogVisible", "messages", "memoryId", "isSending", "messageListRef", "showRawResponsePanel", "lastRawResponse", "drawer", "direction", "currentDatasetDetail", "datasetFields", "datasetData", "apiToken", "currentAnalysisDataset", "selectedIndicators", "selectedDimensions", "innerDrawer", "tableList", "exportingAll", "activeTab", "fieldSearchKeyword", "currentPage", "pageSize", "computed", "indicatorFields", "fields", "filter", "field", "deType", "groupType", "includes", "type", "toUpperCase", "dimensionFields", "filteredFields", "toLowerCase", "dataeaseName", "dimensionFieldsCount", "length", "measureFieldsCount", "totalRecords", "Math", "min", "currentPageData", "start", "end", "slice", "mounted", "localStorage", "setItem", "loadTables", "initMemoryId", "addWelcomeMessage", "document", "addEventListener", "handleClickOutside", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "updated", "scrollToBottom", "methods", "onChartTypeChanged", "newChartConfig", "console", "log", "message", "chartConfig", "id", "SelectDataList", "storedMemoryId", "getItem", "uuidToNumber", "uuid", "number", "i", "hexValue", "parseInt", "push", "isUser", "content", "isTyping", "clearWelcomeMessage", "$refs", "scrollTop", "scrollHeight", "res", "getAllTables", "code", "flattenDatasets", "e", "tree", "result", "node", "leaf", "children", "concat", "onSearchDataset", "keyword", "trim", "ds", "analyzeDataset", "dataset", "showDatasetDetail", "$nextTick", "performAnalysis", "resetToHome", "drawerVisible", "showWelcomeMessage", "$message", "success", "unshift", "timestamp", "Date", "toLocaleTimeString", "quickAnalyzeDataset", "headers", "getDatasetDetail", "detail", "allFields", "fieldsInfo", "map", "originName", "datasourceId", "datasetTableId", "datasetGroupId", "inputEl", "querySelector", "focus", "error", "warning", "toggleFieldSelection", "index", "indexOf", "splice", "switchDataset", "info", "warn", "alert", "msg", "showSuggestions", "useQuestion", "q", "getTableFields", "table", "tableCode", "submitQuestion", "aiMessage", "datasetInfo", "datasetId", "datasetName", "JSON", "stringify", "userMsg", "botMsg", "lastMsg", "post", "responseType", "onDownloadProgress", "fullText", "event", "target", "responseText", "newText", "substring", "parseChartConfig", "chartDataIdMatch", "match", "chartDataId", "fetchChartDataById", "chartConfigMatch", "parse", "tableId", "replace", "jsonRegex", "jsonMatch", "jsonStr", "parseError", "defaultConfig", "now", "toString", "title", "response", "get", "status", "recommendedChartTypes", "getAllSupportedChartTypes", "$forceUpdate", "allTypes", "getChartTypeIcon", "chartType", "iconMap", "getChartTypeName", "nameMap", "getCurrentChartIcon", "toggleChartDropdown", "$set", "dropdownOpen", "switchMessageChartType", "newType", "test<PERSON>hart", "testChartConfig", "value", "category", "testApiResponse", "metrics", "json<PERSON><PERSON><PERSON>", "apiResponseMsg", "testRealData", "realData", "realDataMsg", "showRawResponse", "getFieldTypeClass", "typeMap", "getFieldTypeLabel", "labelMap", "getFieldDescription", "typeDesc", "groupDesc", "fieldName", "purposeDesc", "getFieldIconClass", "getFieldIconName", "handlePageChange", "page", "getRowIndex", "getCellValue", "row", "undefined", "toLocaleString", "exportToPDF", "tempContainer", "createElement", "style", "position", "left", "width", "background", "padding", "body", "append<PERSON><PERSON><PERSON>", "tableName", "currentDate", "tempDiv", "innerHTML", "textContent", "innerText", "chartContainer", "chart", "init", "options", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "setOption", "Promise", "resolve", "setTimeout", "dataTableContainer", "renderDataTable", "canvas", "scale", "useCORS", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "imgData", "toDataURL", "pdf", "orientation", "unit", "format", "imgWidth", "imgHeight", "height", "addImage", "save", "getTime", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "exportAllConversation", "chartMessages", "addPage", "chartMessage", "htmlContent", "chartInstance", "pageWidth", "internal", "getWidth", "pageHeight", "getHeight", "finalImgHeight", "finalImgWidth", "xPos", "yPos", "fileName", "toISOString", "createBasicChartOptions", "categories", "item", "text", "tooltip", "trigger", "xAxis", "yAxis", "series", "seriesMap", "for<PERSON>ach", "s", "Array", "fill", "Object", "values", "chartData", "isArray", "f", "xAxisData", "categoriesSet", "Set", "add", "from", "seriesData", "find", "axisPointer", "legend", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "container", "firstItem", "tableHTML", "header"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <!-- 调试信息区域 -->\n    <div style=\"position: fixed; top: 10px; right: 10px; z-index: 9999; background: #f9f9f9; padding: 10px; border: 1px solid #ddd; max-width: 300px; max-height: 300px; overflow: auto;\">\n      <!-- <button @click=\"testDetailRequest()\">测试详情请求</button>\n      <div v-if=\"datasets.length > 0\">\n        <p>数据集数量: {{ datasets.length }}</p>\n        <p>第一个数据集ID: {{ datasets[0] && datasets[0].id }}</p>\n      </div> -->\n    </div>\n    <div class=\"app-layout\">\n      <div class=\"sidebar\">\n        <div class=\"logo\">\n          <h2>Fast BI</h2>\n        </div>\n        <div class=\"menu\">\n          <div class=\"menu-item active\" @click=\"resetToHome\">\n            <i class=\"el-icon-data-line\"></i>\n            <span>智能问数</span>\n          </div>\n        </div>\n        <div class=\"recent-chats\">\n          <div class=\"chat-list\">\n            <div class=\"chat-item\" v-for=\"(item, index) in recentChats\" :key=\"index\">\n              {{ item.title }}\n              <div class=\"chat-time\">{{ item.time }}</div>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"main-content\">\n        <!-- 欢迎区域 - 带过渡动画 -->\n        <transition name=\"welcome-fade\" mode=\"out-in\">\n          <div v-if=\"!currentAnalysisDataset\" class=\"header\" key=\"welcome\">\n            <h2>您好，欢迎使用 <span class=\"highlight\">智能问数</span></h2>\n            <div class=\"header-actions\" style=\"display: flex; align-items: center; margin-top: 10px;\">\n\n            </div>\n            <p class=\"sub-title\">通过对话的方式进行数据二次计算与可视化分析，立即从以下问数据集中选择开始问吧！</p>\n          </div>\n        </transition>\n        \n        <!-- 数据选择区域 - 带过渡动画 -->\n        <transition name=\"datasets-slide\" mode=\"out-in\">\n          <div v-if=\"!currentAnalysisDataset\" class=\"data-selection\" key=\"datasets\">\n            <h3>可用数据</h3>\n            <div class=\"data-sets\">\n              <div class=\"datasets-single-row\">\n                <transition-group name=\"card-stagger\" tag=\"div\" class=\"card-container-single-row\">\n                  <div\n                    v-for=\"(table, idx) in datasets.slice(0, 3)\"\n                    :key=\"table.id + '_' + idx\"\n                    class=\"data-card-wrapper\"\n                    :style=\"{ transitionDelay: idx * 100 + 'ms' }\">\n                    <el-card class=\"data-card\" style=\"background-color: #f9f9f9;\">\n\n                      <div class=\"data-header\">\n                        <span class=\"sample-tag\">样例</span>\n                        <span class=\"data-title\" :title=\"table.name\">{{ table.name }}</span>\n\n                        <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n                      </div>\n                      <div class=\"data-fields\">\n                        <el-tag\n                          v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 3) : [])\"\n                          :key=\"field.id || idx\"\n                          size=\"mini\"\n                          type=\"info\"\n                          class=\"field-tag-single-line\"\n                        >\n                          {{ field.name || field }}\n                        </el-tag>\n                        <span v-if=\"table.fields && table.fields.length > 3\" class=\"more-fields-indicator\">...</span>\n                      </div>\n\n                      <!-- 悬停按钮 -->\n                      <div class=\"card-hover-buttons\">\n                        <button class=\"card-btn preview-btn\" @click.stop=\"showDatasetDetail(table)\">\n                          预览\n                        </button>\n                        <button class=\"card-btn ask-btn\" @click.stop=\"quickAnalyzeDataset(table)\">\n                          提问\n                        </button>\n                      </div>\n                    </el-card>\n                  </div>\n                </transition-group>\n              </div>\n            </div>\n          </div>\n        </transition>\n        \n        <!-- 聊天消息列表区域 - 瞬间切换 -->\n        <div class=\"message-list\" ref=\"messageListRef\" :class=\"{ 'expanded-position': currentAnalysisDataset }\">\n          <div\n            v-for=\"(message, index) in messages\"\n            :key=\"index\"\n            :class=\"message.isUser ? 'message user-message' : 'message bot-message'\"\n          >\n            <!-- 聊天图标 -->\n            <div class=\"avatar-container\" v-if=\"!message.isUser\">\n              <div class=\"bot-avatar\">\n                FastBI\n              </div>\n            </div>\n            <!-- 消息内容 -->\n            <div class=\"message-content\">\n              <div v-html=\"message.content\"></div>\n              <!-- 如果消息中包含图表配置，则显示图表和导出按钮 -->\n              <div v-if=\"message.chartConfig\" class=\"chart-container\">\n                <div class=\"chart-actions\">\n                  <!-- 图表切换下拉选择器 -->\n                  <div class=\"chart-switcher-dropdown\">\n                    <div class=\"chart-dropdown-wrapper\" @click=\"toggleChartDropdown(message)\" :class=\"{ 'open': message.dropdownOpen }\">\n                      <!-- 当前选中的图表类型按钮 -->\n                      <div class=\"chart-current-btn\">\n                        <div class=\"chart-icon-wrapper\">\n                          <i :class=\"getCurrentChartIcon(message.chartConfig.type)\"></i>\n                        </div>\n                        <span class=\"chart-type-text\">图表切换</span>\n                        <i class=\"el-icon-arrow-down dropdown-arrow\"></i>\n                      </div>\n\n                      <!-- 下拉选项 -->\n                      <div v-show=\"message.dropdownOpen\" class=\"chart-dropdown-options\">\n                        <div class=\"chart-type-grid\">\n                          <button\n                            v-for=\"chartType in getAllSupportedChartTypes()\"\n                            :key=\"chartType\"\n                            :class=\"['chart-type-btn', {\n                              'active': message.chartConfig.type === chartType\n                            }]\"\n                            @click.stop=\"switchMessageChartType(message, chartType)\"\n                            :title=\"getChartTypeName(chartType)\"\n                          >\n                            <div class=\"chart-icon-wrapper\">\n                              <i :class=\"getChartTypeIcon(chartType)\"></i>\n                            </div>\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-download\"\n                            @click=\"exportToPDF(message)\" :loading=\"message.exporting\">\n                    导出PDF\n                  </el-button>\n                </div>\n                <chart-display\n                  :chart-config=\"message.chartConfig\"\n                  :recommended-chart-types=\"message.recommendedChartTypes || []\"\n                  :key=\"'chart-' + message.id + '-' + message.chartConfig.type\"\n                  ref=\"chartDisplay\">\n                </chart-display>\n              </div>\n              <!-- loading动画 -->\n              <span\n                class=\"loading-dots\"\n                v-if=\"message.isTyping\"\n              >\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n                <span class=\"dot\"></span>\n              </span>\n            </div>\n            <!-- 用户消息不显示头像 -->\n          </div>\n        </div>\n\n        <!-- 底部问题输入区域（移至message-list下方） -->\n        <div class=\"question-input-container\">\n          <!-- 字段选择区域 - 带入场动画 -->\n          <transition name=\"field-selection-slide\" mode=\"in-out\">\n            <div v-if=\"currentAnalysisDataset\" class=\"field-selection-mini\" key=\"field-selection\">\n              <!-- 第一行：关键指标（仅在有指标字段时显示） -->\n              <transition name=\"field-row-fade\" appear v-if=\"indicatorFields.length > 0\">\n                <div class=\"field-mini-row\">\n                  <span class=\"field-mini-label\">关键指标</span>\n                  <div class=\"field-tags-mini\">\n                    <transition-group name=\"field-tag-stagger\" tag=\"div\" class=\"field-tags-container\">\n                      <el-tag\n                        v-for=\"(field, index) in indicatorFields\"\n                        :key=\"field.id\"\n                        :class=\"{ 'selected': selectedIndicators.includes(field.id) }\"\n                        @click=\"toggleFieldSelection('indicator', field)\"\n                        class=\"field-tag-mini indicator-tag\"\n                        size=\"mini\"\n                        :title=\"field.name\"\n                        :style=\"{ transitionDelay: index * 50 + 'ms' }\">\n                        {{ field.name }}\n                      </el-tag>\n                    </transition-group>\n                  </div>\n                </div>\n              </transition>\n\n              <!-- 第二行：分析维度（仅在有维度字段时显示） -->\n              <transition name=\"field-row-fade\" appear v-if=\"dimensionFields.length > 0\">\n                <div class=\"field-mini-row\" style=\"transition-delay: 200ms;\">\n                  <span class=\"field-mini-label\">分析维度</span>\n                  <div class=\"field-tags-mini\">\n                    <transition-group name=\"field-tag-stagger\" tag=\"div\" class=\"field-tags-container\">\n                      <el-tag\n                        v-for=\"(field, index) in dimensionFields\"\n                        :key=\"field.id\"\n                        :class=\"{ 'selected': selectedDimensions.includes(field.id) }\"\n                        @click=\"toggleFieldSelection('dimension', field)\"\n                        class=\"field-tag-mini dimension-tag\"\n                        size=\"mini\"\n                        :title=\"field.name\"\n                        :style=\"{ transitionDelay: (index + indicatorFields.length) * 50 + 200 + 'ms' }\">\n                        {{ field.name }}\n                      </el-tag>\n                    </transition-group>\n                  </div>\n                </div>\n              </transition>\n            </div>\n          </transition>\n\n          <!-- 提示文字 - 带过渡动画 -->\n          <transition name=\"hint-fade\" mode=\"out-in\">\n            <span v-if=\"!currentAnalysisDataset\" key=\"hint\">👋直接问我问题，或在上方选择一个主题/数据开始！</span>\n          </transition>\n          <div class=\"question-input-wrapper\">\n            <el-button type=\"text\"\n            class=\"header-action-btn select-data-btn\"\n            size=\"small\"\n            @click=\"SelectDataList\"\n            icon=\"el-icon-upload2\">\n            选择数据\n          </el-button>\n            <el-button\n              type=\"text\"\n              class=\"header-action-btn export-btn\"\n              size=\"small\"\n              icon=\"el-icon-bottom\"\n              @click=\"exportAllConversation\"\n              :loading=\"exportingAll\"\n              :disabled=\"messages.length <= 1\">\n              导出完整指标\n            </el-button>\n            <el-input\n              style=\"margin-bottom: 12px;width: 800px;\"\n              v-model=\"question\"\n              placeholder=\"请直接向我提问，或输入/唤起快捷提问吧\"\n              class=\"question-input\"\n              @keyup.enter.native=\"submitQuestion\"\n              :disabled=\"isSending\">\n            </el-input>\n            <div class=\"input-actions\">\n              <button class=\"action-btn\" @click=\"showSuggestions\">\n                <i class=\"el-icon-magic-stick\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testChart\" title=\"测试图表功能\">\n                <i class=\"el-icon-data-line\"></i>\n              </button>\n              <button class=\"action-btn test-btn\" @click=\"testRealData\" title=\"测试实际数据\">\n                <i class=\"el-icon-s-data\"></i>\n              </button>\n              <button class=\"action-btn debug-btn\" @click=\"showRawResponse\" title=\"显示AI原始响应\">\n                <i class=\"el-icon-monitor\"></i>\n              </button>\n              <button class=\"action-btn send-btn\" @click=\"submitQuestion\" :disabled=\"isSending\">\n                <i class=\"el-icon-position\"></i>\n              </button>\n              \n            </div>\n          </div>\n          \n          <!-- 建议问题弹出层 -->\n          <div v-if=\"showSuggestionsPanel\" class=\"suggestions-panel\">\n            <div class=\"suggestions-title\">\n              <i class=\"el-icon-s-promotion\"></i> 官方推荐\n            </div>\n            <div class=\"suggestions-list\">\n              <div \n                v-for=\"(suggestion, index) in suggestedQuestions\" \n                :key=\"index\"\n                class=\"suggestion-item\"\n                @click=\"useQuestion(suggestion)\">\n                {{ suggestion }}\n              </div>\n            </div>\n          </div>\n          \n          <!-- AI原始响应弹出层 -->\n          <div v-if=\"showRawResponsePanel\" class=\"raw-response-panel\">\n            <div class=\"raw-response-title\">\n              <i class=\"el-icon-monitor\"></i> AI原始响应\n              <button class=\"close-btn\" @click=\"showRawResponsePanel = false\">\n                <i class=\"el-icon-close\"></i>\n              </button>\n            </div>\n            <pre class=\"raw-response-content\">{{ lastRawResponse }}</pre>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <el-drawer\n      title=\"数据详情\"\n      :visible.sync=\"dialogVisible\"\n      direction=\"rtl\"\n      size=\"42%\"\n      class=\"dataset-detail-drawer\">\n\n      <div class=\"detail-content\" v-if=\"currentDatasetDetail\">\n        <!-- 头部信息区域 -->\n        <div class=\"detail-header\">\n          <div class=\"dataset-info-unified\">\n            <div class=\"dataset-title-row\">\n              <i class=\"el-icon-data-board dataset-icon\"></i>\n              <h2 class=\"dataset-name\">{{ currentDatasetDetail.name }}</h2>\n            </div>\n            <div class=\"dataset-stats\">\n              <span class=\"stat-badge\">\n                <i class=\"el-icon-files\"></i>\n                {{ datasetFields.length }} 个字段\n              </span>\n              <span class=\"stat-badge\">\n                <i class=\"el-icon-document\"></i>\n                {{ datasetData.length }} 条记录\n              </span>\n            </div>\n          </div>\n\n          <!-- 悬浮提问按钮 -->\n          <el-button\n            type=\"primary\"\n            @click=\"analyzeDataset\"\n            icon=\"el-icon-chat-dot-round\"\n            class=\"floating-ask-btn\">\n            提问\n          </el-button>\n        </div>\n\n        <!-- 标签页切换区域 -->\n        <div class=\"detail-tabs\">\n          <el-tabs v-model=\"activeTab\" type=\"card\" class=\"dataset-tabs\">\n            <el-tab-pane label=\"字段信息\" name=\"fields\">\n              <template slot=\"label\">\n                <span>\n                  <i class=\"el-icon-menu\"></i>\n                  字段信息\n                </span>\n              </template>\n\n              <!-- 字段信息区域 -->\n              <div class=\"fields-section\">\n                <div class=\"section-header\">\n                  <div class=\"field-stats\">\n                    <span class=\"stat-item dimension\">\n                      <i class=\"el-icon-s-grid\"></i>\n                      维度字段 {{ datasetFields.filter(f => f.groupType === 'd').length }}\n                    </span>\n                    <span class=\"stat-item measure\">\n                      <i class=\"el-icon-s-data\"></i>\n                      度量字段 {{ datasetFields.filter(f => f.groupType === 'q').length }}\n                    </span>\n                  </div>\n                </div>\n\n                <!-- 字段表格 -->\n                <div class=\"fields-table-container\">\n                  <!-- 搜索框 -->\n                  <div class=\"table-search-bar\">\n                    <el-input\n                      v-model=\"fieldSearchKeyword\"\n                      placeholder=\"搜索字段...\"\n                      prefix-icon=\"el-icon-search\"\n                      size=\"small\"\n                      clearable\n                      class=\"field-search-input\">\n                    </el-input>\n                  </div>\n\n                  <!-- 表格 -->\n                  <div class=\"clean-table\">\n                    <div class=\"table-header\">\n                      <div class=\"header-cell name-col\">字段名称</div>\n                      <div class=\"header-cell type-col\">类型</div>\n                      <div class=\"header-cell group-col\">维度/度量</div>\n                      <div class=\"header-cell desc-col\">字段描述</div>\n                    </div>\n\n                    <div class=\"table-body\">\n                      <div\n                        v-for=\"(field, index) in filteredFields\"\n                        :key=\"field.id || index\"\n                        class=\"table-row\">\n\n                        <div class=\"table-cell name-col\">\n                          <div class=\"field-name-wrapper\">\n                            <div class=\"field-type-icon\" :class=\"getFieldIconClass(field)\">\n                              <i :class=\"getFieldIconName(field)\"></i>\n                            </div>\n                            <span class=\"field-name-text\">{{ field.name }}</span>\n                          </div>\n                        </div>\n\n                        <div class=\"table-cell type-col\">\n                          <span class=\"field-type-text\">{{ getFieldTypeLabel(field.type) }}</span>\n                        </div>\n\n                        <div class=\"table-cell group-col\">\n                          <span class=\"group-type-text\" :class=\"field.groupType === 'd' ? 'dimension-text' : 'measure-text'\">\n                            {{ field.groupType === 'd' ? '维度' : '度量' }}\n                          </span>\n                        </div>\n\n                        <div class=\"table-cell desc-col\">\n                          <span class=\"field-desc-text\">{{ field.name }}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 底部统计 -->\n                  <div class=\"table-footer\">\n                    <div class=\"field-stats-summary\">\n                      <span class=\"total-count\">总字段数 {{ datasetFields.length }}</span>\n                      <div class=\"type-counts\">\n                        <span class=\"dimension-count\">\n                          <span class=\"count-dot dimension-dot\"></span>\n                          分析维度 {{ dimensionFieldsCount }}\n                        </span>\n                        <span class=\"measure-count\">\n                          <span class=\"count-dot measure-dot\"></span>\n                          关键指标 {{ measureFieldsCount }}\n                        </span>\n                      </div>\n                      <span class=\"preview-limit\">最多预览前100行数据</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </el-tab-pane>\n\n            <el-tab-pane label=\"数据预览\" name=\"preview\">\n              <template slot=\"label\">\n                <span>\n                  <i class=\"el-icon-view\"></i>\n                  数据预览\n                </span>\n              </template>\n\n              <!-- 数据预览区域 -->\n              <div class=\"preview-section\">\n                <div class=\"preview-header\">\n                  <div class=\"preview-stats\">\n                    <span class=\"total-records\">\n                      共 {{ totalRecords }} 条记录\n                    </span>\n                    <span class=\"current-page-info\">\n                      当前显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalRecords) }} 条\n                    </span>\n                  </div>\n                  <div class=\"preview-pagination\">\n                    <el-pagination\n                      @current-change=\"handlePageChange\"\n                      :current-page=\"currentPage\"\n                      :page-size=\"pageSize\"\n                      :total=\"totalRecords\"\n                      layout=\"prev, pager, next\"\n                      :small=\"true\">\n                    </el-pagination>\n                  </div>\n                </div>\n\n                <div class=\"preview-table-wrapper\">\n                  <el-table\n                    :data=\"currentPageData\"\n                    class=\"preview-table\"\n                    stripe\n                    border\n                    size=\"small\"\n                    style=\"width: 100%\"\n                    height=\"auto\">\n                    <el-table-column\n                      type=\"index\"\n                      label=\"序号\"\n                      width=\"60\"\n                      :index=\"getRowIndex\">\n                    </el-table-column>\n                    <el-table-column\n                      v-for=\"field in datasetFields\"\n                      :key=\"field.id || field.name\"\n                      :prop=\"field.dataeaseName || field.name\"\n                      :label=\"field.name\"\n                      :min-width=\"120\"\n                      show-overflow-tooltip>\n                      <template slot-scope=\"scope\">\n                        <span class=\"cell-content\">{{ getCellValue(scope.row, field) }}</span>\n                      </template>\n                    </el-table-column>\n                  </el-table>\n                </div>\n              </div>\n            </el-tab-pane>\n          </el-tabs>\n        </div>\n      </div>\n    </el-drawer>\n    <el-drawer\n      title=\"数据集列表\"\n      :visible.sync=\"drawer\"\n      direction=\"rtl\"\n      size=\"45%\"\n      class=\"dataset-drawer\">\n      <div class=\"drawer-content\">\n        <div class=\"search-section\">\n          <el-input\n            v-model=\"searchKeyword\"\n            placeholder=\"搜索数据集名称...\"\n            @input=\"onSearchDataset\"\n            class=\"dataset-search-input\"\n            prefix-icon=\"el-icon-search\"\n            clearable\n            size=\"medium\"\n          />\n          <div class=\"search-stats\" v-if=\"searchKeyword\">\n            找到 {{ filteredDatasets.length }} 个数据集\n          </div>\n        </div>\n\n        <div class=\"datasets-grid\">\n          <div v-if=\"filteredDatasets.length === 0\" class=\"empty-state\">\n            <i class=\"el-icon-search\"></i>\n            <p v-if=\"searchKeyword\">未找到包含 \"{{ searchKeyword }}\" 的数据集</p>\n            <p v-else>暂无可用数据集</p>\n          </div>\n          <el-row :gutter=\"20\" v-else>\n            <el-col :span=\"8\" v-for=\"(table, idx) in filteredDatasets\" :key=\"table.id + '_' + idx\">\n              <el-card class=\"data-card drawer-data-card\" @click.native=\"showDatasetDetail(table)\">\n                <div class=\"data-header\">\n                  <span class=\"sample-tag\">数据集</span>\n                  <span class=\"data-title\" :title=\"table.name\">{{ table.name }}</span>\n                  <span class=\"common-tag\" v-if=\"table.common\">常用</span>\n                </div>\n                <div class=\"data-fields\">\n                  <el-tag\n                    v-for=\"(field, idx) in (table.fields ? table.fields.slice(0, 4) : [])\"\n                    :key=\"field.id || idx\"\n                    size=\"mini\"\n                    type=\"info\"\n                    class=\"field-tag\"\n                  >\n                    {{ field.name || field }}\n                  </el-tag>\n                  <span v-if=\"table.fields && table.fields.length > 4\" class=\"more-fields\">+{{ table.fields.length - 4 }}个字段</span>\n                </div>\n              </el-card>\n            </el-col>\n          </el-row>\n        </div>\n      </div>\n    </el-drawer>\n\n  </div>\n</template>\n\n<script>\nimport { dataApi } from './api/index.js'\nimport { v4 as uuidv4 } from 'uuid'\nimport axios from 'axios'\nimport ChartDisplay from './components/ChartDisplay.vue'\n// 导入PDF导出所需的库\nimport html2canvas from 'html2canvas'\nimport { jsPDF } from 'jspdf'\nimport * as echarts from 'echarts'\n// import { log } from '@antv/g2plot/lib/utils/invariant.js'\n\nexport default {\n  name: 'App',\n  components: {\n    ChartDisplay\n  },\n  data() {\n    return {\n      description: null,\n      tablename: null,\n      tables: [], // 原始树结构\n      datasets: [], // 扁平化后的数据集（leaf: true）\n      filteredDatasets: [], // 搜索过滤后的数据集\n      searchKeyword: '',\n      selectedTable: null,\n      question: '',\n      queryResult: null,\n      showSuggestionsPanel: false,\n      suggestedQuestions: [],\n      recentChats: [],\n      tableIndicators: [],\n      dialogVisible: false,\n      // 新增流式输出相关数据\n      messages: [],\n      memoryId: null,\n      isSending: false,\n      messageListRef: null,\n      showRawResponsePanel: false, // 新增：控制原始响应弹出层\n      lastRawResponse: '', // 新增：存储最后收到的原始响应\n      drawer:false,    //抽屉展示\n      direction: 'rtl', //默认抽屉从又往左\n      currentDatasetDetail: null,\n      datasetFields: [],\n      datasetData: [],\n      apiToken: 'eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOjEsIm9pZCI6MTA1LCJsb2dpbl91c2VyX2tleSI6ImI2OTEyYzNiLTQ5ZmMtNDAyMC1iMzRiLWE3YWJlNmY1MTI0MSJ9.J4QCC6qh2GZ7ENDHuZQWQ1GvGOQ1HOr8gW34KXIHV9N_73Jppx-qmEwpq0AHlXM3DUjd5EAlpIVAvzJygm_ldg', // 示例token，实际应该从登录后存储\n      currentAnalysisDataset: null, // 存储当前用于智能分析的数据集信息\n      selectedIndicators: [], // 选中的指标字段ID\n      selectedDimensions: [], // 选中的维度字段ID\n      innerDrawer:false,\n      tableList:[],\n      exportingAll: false, // 新增：控制完整对话导出状态\n      activeTab: 'fields', // 新增：控制数据集详情页面的标签页切换，默认显示字段信息\n      fieldSearchKeyword: '', // 字段搜索关键词\n      // 数据预览分页相关\n      currentPage: 1, // 当前页码\n      pageSize: 20, // 每页显示条数\n    }\n  },\n\n  computed: {\n    // 指标字段（数值类型）\n    indicatorFields() {\n      if (!this.currentAnalysisDataset || !this.currentAnalysisDataset.fields) {\n        return [];\n      }\n      return this.currentAnalysisDataset.fields.filter(field =>\n        field.deType === 1 || // 数值类型\n        field.groupType === 'q' || // 度量字段\n        ['LONG', 'DOUBLE', 'BIGINT', 'INT', 'FLOAT', 'DECIMAL'].includes(field.type?.toUpperCase())\n      );\n    },\n\n    // 维度字段（文本、日期等）\n    dimensionFields() {\n      if (!this.currentAnalysisDataset || !this.currentAnalysisDataset.fields) {\n        return [];\n      }\n      return this.currentAnalysisDataset.fields.filter(field =>\n        field.deType === 0 || // 文本类型\n        field.groupType === 'd' || // 维度字段\n        ['TEXT', 'STRING', 'VARCHAR', 'DATE', 'DATETIME', 'TIMESTAMP'].includes(field.type?.toUpperCase())\n      );\n    },\n\n    // 过滤后的字段列表\n    filteredFields() {\n      if (!this.fieldSearchKeyword) {\n        return this.datasetFields;\n      }\n      return this.datasetFields.filter(field =>\n        field.name.toLowerCase().includes(this.fieldSearchKeyword.toLowerCase()) ||\n        (field.dataeaseName && field.dataeaseName.toLowerCase().includes(this.fieldSearchKeyword.toLowerCase()))\n      );\n    },\n\n    // 维度字段数量\n    dimensionFieldsCount() {\n      return this.datasetFields.filter(field => field.groupType === 'd').length;\n    },\n\n    // 度量字段数量\n    measureFieldsCount() {\n      return this.datasetFields.filter(field => field.groupType === 'q').length;\n    },\n\n    // 数据预览分页相关计算属性\n    totalRecords() {\n      return Math.min(this.datasetData.length, 100); // 最多显示100条\n    },\n\n    currentPageData() {\n      const start = (this.currentPage - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      return this.datasetData.slice(start, Math.min(end, 100)); // 确保不超过100条\n    }\n  },\n\n  mounted() {\n    // 存储token到localStorage，供API使用\n    localStorage.setItem('de_token', this.apiToken);\n    this.loadTables()\n    this.initMemoryId()\n    this.addWelcomeMessage()\n    // 添加图表相关的建议问题\n    this.suggestedQuestions = [\n      \"帮我生成一个销售额柱状图\",\n      \"展示近六个月的销售趋势折线图\",\n      \"按照区域统计销售量并生成饼图\",\n      \"帮我做一个按产品类别的销量对比图\"\n    ]\n    // 添加点击外部关闭下拉框的监听\n    document.addEventListener('click', this.handleClickOutside);\n  },\n\n  beforeDestroy() {\n    // 移除点击外部监听\n    document.removeEventListener('click', this.handleClickOutside);\n  },\n  updated() {\n    this.scrollToBottom()\n  },\n  methods: {\n    // 处理图表类型切换\n    onChartTypeChanged(newChartConfig) {\n      console.log('图表类型已切换:', newChartConfig);\n\n      // 找到对应的消息并更新其图表配置\n      for (let message of this.messages) {\n        if (message.chartConfig && message.chartConfig.id === newChartConfig.id) {\n          message.chartConfig = newChartConfig;\n          break;\n        }\n      }\n    },\n\n    SelectDataList(){\n      this.loadTables()\n      this.drawer=true\n    },\n    // 初始化用户ID\n    initMemoryId() {\n      let storedMemoryId = localStorage.getItem('user_memory_id')\n      if (!storedMemoryId) {\n        storedMemoryId = this.uuidToNumber(uuidv4())\n        localStorage.setItem('user_memory_id', storedMemoryId)\n      }\n      this.memoryId = storedMemoryId\n    },\n    \n    // 将UUID转换为数字\n    uuidToNumber(uuid) {\n      let number = 0\n      for (let i = 0; i < uuid.length && i < 6; i++) {\n        const hexValue = uuid[i]\n        number = number * 16 + (parseInt(hexValue, 16) || 0)\n      }\n      return number % 1000000\n    },\n    \n    // 添加欢迎消息\n    addWelcomeMessage() {\n      this.messages.push({\n        isUser: false,\n        content: `您好！我是数据助手，请告诉我您想了解什么数据信息？`,\n        isTyping: false\n      })\n    },\n\n    // 清除欢迎消息\n    clearWelcomeMessage() {\n      // 移除AI的欢迎消息（通常是第一条消息）\n      this.messages = this.messages.filter(message =>\n        message.isUser || !message.content.includes('您好！我是数据助手')\n      );\n    },\n    \n    // 滚动到底部\n    scrollToBottom() {\n      if (this.$refs.messageListRef) {\n        this.$refs.messageListRef.scrollTop = this.$refs.messageListRef.scrollHeight\n      }\n    },\n    \n    async loadTables() {\n      try {\n        const res = await dataApi.getAllTables()\n        if (res.data && res.data.code === 0) {\n          this.tables = res.data.data\n          this.datasets = this.flattenDatasets(this.tables)\n          this.filteredDatasets = this.datasets\n        } else {\n          this.tables = []\n          this.datasets = []\n          this.filteredDatasets = []\n        }\n      } catch (e) {\n        this.tables = []\n        this.datasets = []\n        this.filteredDatasets = []\n      }\n    },\n    // 递归扁平化树结构，只保留leaf: true的数据集\n    flattenDatasets(tree) {\n      let result = []\n      for (const node of tree) {\n        if (node.leaf) {\n          result.push(node)\n        } else if (node.children && node.children.length > 0) {\n          result = result.concat(this.flattenDatasets(node.children))\n        }\n      }\n      return result\n    },\n    // 搜索功能\n    onSearchDataset() {\n      const keyword = this.searchKeyword.trim().toLowerCase()\n      if (!keyword) {\n        this.filteredDatasets = this.datasets\n      } else {\n        this.filteredDatasets = this.datasets.filter(ds => ds.name && ds.name.toLowerCase().includes(keyword))\n      }\n    },\n    // selectDataset(dataset) {\n    //   this.selectedTable = dataset;\n    //   this.drawer = true;\n    // },\n    \n    // 测试详情请求方法\n    // async testDetailRequest() {\n    //   if (this.datasets.length > 0) {\n    //     const testDataset = this.datasets[0];\n    //     console.log('测试数据集:', testDataset);\n    //     alert(`正在请求数据集详情，ID: ${testDataset.id}`);\n    //     await this.showDatasetDetail(testDataset);\n      //   } else {\n    //     alert('没有可用的数据集');\n    //   }\n    // },\n\n    // 智能分析数据集\n    analyzeDataset(dataset = null) {\n      // 如果传入了数据集参数，先设置为当前数据集\n      if (dataset) {\n        this.showDatasetDetail(dataset);\n        // 等待数据加载完成后再进行分析\n        this.$nextTick(() => {\n          this.performAnalysis();\n        });\n        return;\n      }\n\n      this.performAnalysis();\n    },\n\n    // 重置到首页状态\n    resetToHome() {\n      // 清除当前选择的数据集\n      this.currentAnalysisDataset = null;\n      this.currentDatasetDetail = null;\n      this.datasetFields = [];\n      this.datasetData = [];\n\n      // 关闭所有抽屉和对话框\n      this.dialogVisible = false;\n      this.drawerVisible = false;\n\n      // 清除聊天记录（可选，根据需求决定）\n      // this.messages = [];\n\n      // 重置其他状态\n      this.activeTab = 'fields';\n      this.currentPage = 1;\n      this.selectedIndicators = [];\n      this.selectedDimensions = [];\n\n      // 显示欢迎消息\n      this.showWelcomeMessage();\n\n      // 提示用户\n      this.$message.success('已返回首页，请选择数据集开始分析');\n    },\n\n    // 显示欢迎消息\n    showWelcomeMessage() {\n      // 如果没有消息或者第一条不是欢迎消息，则添加欢迎消息\n      if (this.messages.length === 0 || this.messages[0].type !== 'bot' || !this.messages[0].content.includes('欢迎')) {\n        this.messages.unshift({\n          type: 'bot',\n          content: '您好！欢迎使用智能问数系统。请先选择一个数据集，然后您就可以用自然语言提问了。',\n          timestamp: new Date().toLocaleTimeString()\n        });\n      }\n    },\n\n    // 快速分析数据集（从卡片悬停按钮触发）\n    async quickAnalyzeDataset(dataset) {\n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n\n      try {\n        // 获取数据集详情和字段信息\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n\n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          // 获取字段信息\n          const fields = detail.allFields || (detail.data && detail.data.fields) || [];\n\n          // 提取所需的字段信息\n          const fieldsInfo = fields.map(field => {\n            return {\n              id: field.id,\n              originName: field.originName || '',\n              name: field.name || '',\n              dataeaseName: field.dataeaseName || '',\n              groupType: field.groupType || '',\n              type: field.type || '',\n              datasourceId: field.datasourceId || '',\n              datasetTableId: field.datasetTableId || '',\n              datasetGroupId: field.datasetGroupId || ''\n            };\n          });\n\n          // 存储当前数据集信息\n          this.currentAnalysisDataset = {\n            id: dataset.id,\n            name: dataset.name,\n            fields: fieldsInfo\n          };\n\n          // 清除AI的欢迎消息\n          this.clearWelcomeMessage();\n\n          // 提示用户\n          this.$message.success(`已选择数据集\"${dataset.name}\"，可以开始提问了`);\n\n          // 自动聚焦到问题输入框\n          this.$nextTick(() => {\n            const inputEl = document.querySelector('.question-input input');\n            if (inputEl) inputEl.focus();\n          });\n        } else {\n          this.$message.error('获取数据集信息失败，请重试');\n        }\n      } catch (error) {\n        console.error('获取数据集字段失败:', error);\n        this.$message.error('获取数据集字段失败，请重试');\n      }\n    },\n\n    // 执行分析的具体逻辑\n    performAnalysis() {\n      console.log('performAnalysis 被调用');\n      console.log('currentDatasetDetail:', this.currentDatasetDetail);\n      console.log('datasetFields:', this.datasetFields);\n\n      if (!this.currentDatasetDetail || !this.datasetFields.length) {\n        console.log('数据集或字段信息缺失');\n        this.$message.warning('当前数据集没有可用字段');\n        return;\n      }\n\n      // 提取所需的字段信息\n      const fieldsInfo = this.datasetFields.map(field => {\n        // 只保留需要的字段属性\n        return {\n          id: field.id,\n          originName: field.originName || '',\n          name: field.name || '',\n          dataeaseName: field.dataeaseName || '',\n          groupType: field.groupType || '',\n          type: field.type || '',\n          datasourceId: field.datasourceId || '',\n          datasetTableId: field.datasetTableId || '',\n          datasetGroupId: field.datasetGroupId || ''\n        };\n      });\n\n      // 存储当前数据集信息，包括ID和字段\n      this.currentAnalysisDataset = {\n        id: this.currentDatasetDetail.id,\n        name: this.currentDatasetDetail.name,\n        fields: fieldsInfo\n      };\n\n      // 选择数据集后清除AI的欢迎消息\n      this.clearWelcomeMessage();\n\n      // 关闭详情抽屉，返回到聊天界面\n      this.dialogVisible = false;\n\n      // 提示用户\n      this.$message.success(`已选择数据集\"${this.currentDatasetDetail.name}\"进行智能分析，请在下方输入您的问题`);\n\n      // 自动聚焦到问题输入框\n      this.$nextTick(() => {\n        const inputEl = document.querySelector('.question-input input');\n        if (inputEl) inputEl.focus();\n      });\n    },\n\n    // 切换字段选择\n    toggleFieldSelection(type, field) {\n      if (type === 'indicator') {\n        const index = this.selectedIndicators.indexOf(field.id);\n        if (index > -1) {\n          this.selectedIndicators.splice(index, 1);\n        } else {\n          this.selectedIndicators.push(field.id);\n        }\n      } else if (type === 'dimension') {\n        const index = this.selectedDimensions.indexOf(field.id);\n        if (index > -1) {\n          this.selectedDimensions.splice(index, 1);\n        } else {\n          this.selectedDimensions.push(field.id);\n        }\n      }\n    },\n\n    // 切换数据集\n    switchDataset() {\n      this.currentAnalysisDataset = null;\n      this.selectedIndicators = [];\n      this.selectedDimensions = [];\n      this.$message.info('请重新选择数据集');\n    },\n  \n    async showDatasetDetail(dataset) {\n      // alert(`showDatasetDetail 被调用，数据集ID: ${dataset?.id || '未知'}`);\n      console.log('showDatasetDetail 被调用，参数:', dataset);\n      \n      // 验证参数\n      if (!dataset || !dataset.id) {\n        console.error('无效的数据集参数:', dataset);\n        // alert('数据集参数无效，缺少ID');\n        return;\n      }\n      \n      // 准备请求headers\n      const headers = {\n        'X-DE-TOKEN': this.apiToken,\n        'out_auth_platform': 'default',\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer 7b226964223a312c22757365726e616d65223a2261646d696e222c2270617373776f7264223a226531306164633339343962613539616262653536653035376632306638383365222c22726f6c65223a312c227065726d697373696f6e223a6e756c6c7d'\n      };\n      \n      try {\n        console.log(`开始请求数据集详情，ID: ${dataset.id}`);\n        const res = await dataApi.getDatasetDetail(dataset.id, false, headers);\n        console.log('数据集详情API响应:', res);\n        \n        if (res.data && res.data.code === 0) {\n          const detail = res.data.data;\n          console.log('数据集详情数据:', detail);\n          this.currentDatasetDetail = detail;\n          // 字段信息优先allFields，否则data.fields\n          this.datasetFields = detail.allFields || (detail.data && detail.data.fields) || [];\n          // 数据内容\n          this.datasetData = (detail.data && detail.data.data) || [];\n          // 重置标签页到字段信息\n          this.activeTab = 'fields';\n          // 重置分页状态\n          this.currentPage = 1;\n          // 调试打印\n          console.log(`字段信息: ${this.datasetFields.length}个字段`);\n          console.log(`数据内容: ${this.datasetData.length}条记录`);\n\n          if (this.datasetFields.length === 0) {\n            console.warn('未找到字段信息');\n          }\n\n          if (this.datasetData.length === 0) {\n            console.warn('未找到数据内容');\n          }\n        } else {\n          console.error('API返回错误:', res.data);\n          alert(`API返回错误: ${res.data.msg || '未知错误'}`);\n          this.currentDatasetDetail = null;\n          this.datasetFields = [];\n          this.datasetData = [];\n          this.activeTab = 'fields'; // 重置标签页\n        }\n        this.dialogVisible = true;\n      } catch (e) {\n        console.error('请求数据集详情失败:', e);\n        alert(`请求失败: ${e.message || '未知错误'}`);\n        this.currentDatasetDetail = null;\n        this.datasetFields = [];\n        this.datasetData = [];\n        this.dialogVisible = true;\n      }\n    },\n    showSuggestions() {\n      this.showSuggestionsPanel = !this.showSuggestionsPanel\n    },\n    useQuestion(q) {\n      this.question = q\n      this.showSuggestionsPanel = false\n    },\n    getTableFields(table) {\n      if (table.tableCode === 'SALES') {\n        return []\n      }\n      return []\n    },\n    \n    // 修改为流式输出的问题提交\n    async submitQuestion() {\n      if (!this.question.trim() || this.isSending) {\n        this.$message.warning('请输入问题')\n        return\n      }\n      \n      // 获取当前选中的数据集信息\n      // 构建发送给AI的完整消息\n      let aiMessage = this.question.trim();\n      \n      // 如果有当前分析的数据集，添加数据集信息\n      if (this.currentAnalysisDataset) {\n        // 构建AI需要的格式\n        const datasetInfo = {\n          datasetId: this.currentAnalysisDataset.id,\n          datasetName: this.currentAnalysisDataset.name,\n          fields: this.currentAnalysisDataset.fields\n        };\n        \n        // 将数据集信息添加到消息中\n        aiMessage = JSON.stringify({\n          question: this.question.trim(),\n          dataset: datasetInfo\n        });\n      }\n      \n      // 添加用户消息\n      const userMsg = {\n        isUser: true,\n        content: this.question.trim(),\n        isTyping: false\n      }\n      this.messages.push(userMsg)\n      \n      // 添加机器人占位符消息\n      const botMsg = {\n        isUser: false,\n        content: '',\n        isTyping: true\n      }\n      this.messages.push(botMsg)\n      \n      // 获取最后一条消息的引用\n      const lastMsg = this.messages[this.messages.length - 1]\n      \n      // 保存问题并清空输入框\n      const question = this.question\n      this.question = ''\n      this.isSending = true\n      \n      try {\n        // 构建发送的消息，包含当前数据集信息\n        const message = this.currentAnalysisDataset \n          ? aiMessage \n          : `${question}。当前数据集：未选择具体数据集`;\n        \n        // 发送请求\n        await axios.post(\n          'http://localhost:8088/api/indicator/chat',\n          { memoryId: this.memoryId, message },\n          {\n            responseType: 'stream',\n            onDownloadProgress: (e) => {\n              const fullText = e.event.target.responseText // 累积的完整文本\n              let newText = fullText.substring(lastMsg.content.length)\n              lastMsg.content += newText // 增量更新\n              this.scrollToBottom() // 实时滚动\n              \n              // 保存原始响应\n              this.lastRawResponse = fullText\n            }\n          }\n        )\n        \n        // 请求完成后，解析可能的图表配置\n        lastMsg.isTyping = false\n        this.isSending = false\n        \n        // 尝试从回复中解析图表配置\n        this.parseChartConfig(lastMsg);\n      } catch (error) {\n        console.error('请求出错:', error)\n        lastMsg.content = '很抱歉，请求出现错误，请稍后重试。'\n        lastMsg.isTyping = false\n        this.isSending = false\n      }\n    },\n    \n    // 解析响应中的图表配置\n    parseChartConfig(message) {\n      console.log('开始解析图表配置，原始消息内容:', message.content);\n      \n      // 首先尝试查找图表数据ID\n      const chartDataIdMatch = message.content.match(/<chart_data_id>(.*?)<\\/chart_data_id>/);\n      if (chartDataIdMatch && chartDataIdMatch[1]) {\n        const chartDataId = chartDataIdMatch[1];\n        console.log('找到图表数据ID:', chartDataId);\n        \n        // 使用ID从后端获取完整图表数据\n        this.fetchChartDataById(chartDataId, message);\n        return;\n      }\n      \n      // 如果没有找到图表数据ID，尝试查找JSON代码块\n      const chartConfigMatch = message.content.match(/```json\\s*([\\s\\S]*?)\\s*```/);\n      if (chartConfigMatch && chartConfigMatch[1]) {\n        console.log('找到JSON代码块:', chartConfigMatch[1]);\n        \n        try {\n          let chartConfig = JSON.parse(chartConfigMatch[1]);\n          console.log('解析后的JSON对象:', chartConfig);\n          \n          // 检查是否是API响应格式（包含code和data字段）\n          if (chartConfig.code === 0 && chartConfig.data) {\n            console.log('检测到API响应格式，提取data字段:', chartConfig.data);\n            chartConfig = chartConfig.data;\n          }\n          \n          // 如果包含必要的图表配置字段，则视为有效的图表配置\n          if (chartConfig.type && (chartConfig.datasetId || chartConfig.tableId || chartConfig.data)) {\n            console.log('找到有效的图表配置:', chartConfig);\n            message.chartConfig = chartConfig;\n            \n            // 可以选择性地从消息中移除JSON代码块\n            message.content = message.content.replace(/```json\\s*[\\s\\S]*?\\s*```/, \n              '<div class=\"chart-notice\"></div>');\n            \n            return; // 找到有效配置后直接返回\n          } else {\n            console.log('图表配置缺少必要字段:', chartConfig);\n          }\n        } catch (error) {\n          console.error('JSON解析错误:', error);\n        }\n      }\n      \n      console.log('未找到JSON代码块或解析失败，尝试直接解析响应内容');\n      \n      // 如果没有找到JSON代码块，尝试从整个消息内容中提取JSON\n      try {\n        // 尝试查找可能的JSON字符串\n        const jsonRegex = /\\{.*code\\s*:\\s*0.*data\\s*:.*\\}/s;\n        const jsonMatch = message.content.match(jsonRegex);\n        \n        if (jsonMatch) {\n          console.log('找到可能的JSON字符串:', jsonMatch[0]);\n          \n          // 尝试将字符串转换为JSON对象\n          // 注意：这里可能需要一些额外的处理，因为消息中的JSON可能不是标准格式\n          const jsonStr = jsonMatch[0].replace(/([{,]\\s*)(\\w+)(\\s*:)/g, '$1\"$2\"$3');\n          console.log('处理后的JSON字符串:', jsonStr);\n          \n          try {\n            const chartConfig = JSON.parse(jsonStr);\n            console.log('解析后的JSON对象:', chartConfig);\n            \n            if (chartConfig.code === 0 && chartConfig.data) {\n              const data = chartConfig.data;\n              console.log('提取的图表数据:', data);\n              \n              // 检查是否包含必要的图表字段\n              if (data.type && (data.tableId || data.data)) {\n                console.log('找到有效的图表配置:', data);\n                message.chartConfig = data;\n                message.content = message.content.replace(jsonMatch[0], \n                  '<div class=\"chart-notice\"></div>');\n                return; // 找到有效配置后直接返回\n              }\n            }\n          } catch (parseError) {\n            console.log('JSON解析错误:', parseError);\n          }\n        }\n      } catch (error) {\n        console.log('解析过程中出错:', error);\n      }\n      \n      // 最后的尝试：检查消息中是否包含图表数据的关键词\n      if (message.content.includes('图表数据已成功获取') || \n          message.content.includes('已生成图表')) {\n        console.log('消息中包含图表相关关键词，尝试构建默认图表配置');\n        \n        // 构建一个默认的图表配置\n        const defaultConfig = {\n          id: Date.now().toString(),\n          type: 'bar',\n          title: '数据图表',\n          tableId: this.selectedTable?.id || '1114644377738809344'\n        };\n        \n        console.log('构建的默认图表配置:', defaultConfig);\n        message.chartConfig = defaultConfig;\n      }\n    },\n    \n    // 从后端获取完整图表数据\n    async fetchChartDataById(chartDataId, message) {\n      try {\n        console.log('开始从后端获取图表数据, ID:', chartDataId);\n        const response = await axios.get(`http://localhost:8088/api/chart/data/${chartDataId}`);\n        \n        if (response.data && response.status === 200) {\n          console.log('成功获取图表数据:', response.data);\n          \n          // 如果响应包含code和data字段，则提取data字段\n          let chartConfig = response.data;\n          if (chartConfig.code === 0 && chartConfig.data) {\n            chartConfig = chartConfig.data;\n          }\n          \n          // 将图表配置添加到消息对象\n          message.chartConfig = chartConfig;\n\n          // 设置所有支持的图表类型（除了当前类型）\n          message.recommendedChartTypes = this.getAllSupportedChartTypes(chartConfig.type);\n\n          // 在消息中添加图表提示\n          if (!message.content.includes('已生成图表')) {\n            message.content += '<div class=\"chart-notice\">↓ 已生成图表 ↓</div>';\n          }\n          \n          // 强制更新视图\n          this.$forceUpdate();\n        } else {\n          console.error('获取图表数据失败:', response);\n          message.content += '<div class=\"chart-error\">获取图表数据失败</div>';\n        }\n      } catch (error) {\n        console.error('获取图表数据出错:', error);\n        message.content += `<div class=\"chart-error\">获取图表数据失败: ${error.message}</div>`;\n      }\n    },\n\n    // 获取所有支持的图表类型\n    getAllSupportedChartTypes() {\n      const allTypes = ['bar', 'line', 'pie', 'bar-horizontal'];\n      return allTypes;\n    },\n\n    // 获取图表类型的图标\n    getChartTypeIcon(chartType) {\n      const iconMap = {\n        'bar': 'chart-icon-bar',\n        'line': 'chart-icon-line',\n        'pie': 'chart-icon-pie',\n        'bar-horizontal': 'chart-icon-bar-horizontal'\n      };\n      return iconMap[chartType] || 'chart-icon-bar';\n    },\n\n    // 获取图表类型的名称\n    getChartTypeName(chartType) {\n      const nameMap = {\n        'bar': '柱图',\n        'line': '线图',\n        'pie': '饼图',\n        'bar-horizontal': '条形图'\n      };\n      return nameMap[chartType] || '柱图';\n    },\n\n    // 获取当前图表类型的图标\n    getCurrentChartIcon(chartType) {\n      return this.getChartTypeIcon(chartType);\n    },\n\n    // 切换图表下拉框状态\n    toggleChartDropdown(message) {\n      this.$set(message, 'dropdownOpen', !message.dropdownOpen);\n    },\n\n    // 切换消息的图表类型\n    switchMessageChartType(message, newType) {\n      if (newType === message.chartConfig.type) {\n        message.dropdownOpen = false;\n        return;\n      }\n\n      // 更新图表配置的类型\n      this.$set(message.chartConfig, 'type', newType);\n\n      // 关闭下拉框\n      message.dropdownOpen = false;\n\n      // 触发图表重新渲染\n      this.$nextTick(() => {\n        // 强制更新组件\n        this.$forceUpdate();\n      });\n    },\n\n    // 测试图表功能的方法（仅用于开发测试）\n    testChart() {\n      // 基本测试配置，包含完整数据\n      const testChartConfig = {\n        id: \"test-chart-001\",\n        type: \"bar\",\n        title: \"测试销售数据图表\",\n        datasetId: \"test-dataset\",\n        tableId: \"test-table\",\n        data: {\n          data: [\n            { field: \"北京\", name: \"北京\", value: 1200, category: \"销售额\" },\n            { field: \"上海\", name: \"上海\", value: 980, category: \"销售额\" },\n            { field: \"广州\", name: \"广州\", value: 850, category: \"销售额\" },\n            { field: \"深圳\", name: \"深圳\", value: 1100, category: \"销售额\" },\n            { field: \"杭州\", name: \"杭州\", value: 720, category: \"销售额\" }\n          ],\n          fields: [\n            { id: \"region_id\", name: \"地区\", groupType: \"d\" },\n            { id: \"sales_id\", name: \"销售额\", groupType: \"q\" }\n          ]\n        }\n      };\n      \n      // 创建测试消息\n      const botMsg = {\n        isUser: false,\n        content: '这是一个测试图表：<div class=\"chart-notice\">↓ 已生成图表 ↓</div>',\n        isTyping: false,\n        chartConfig: testChartConfig,\n        recommendedChartTypes: ['line', 'pie', 'bar-horizontal'] // 所有其他图表类型\n      };\n      \n      this.messages.push(botMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(testChartConfig, null, 2);\n      \n      // 打印当前消息列表，检查图表配置是否正确传递\n      console.log('当前消息列表:', this.messages);\n      \n      // 模拟API响应格式的测试\n      const testApiResponse = {\n        code: 0,\n        msg: 'success',\n        data: {\n          type: 'bar',\n          data: [\n            { field: '类别1', value: 100 },\n            { field: '类别2', value: 200 },\n            { field: '类别3', value: 150 }\n          ],\n          metrics: [{ name: '数值' }]\n        }\n      };\n      \n      // 将API响应格式添加到消息中，测试解析功能\n      const jsonContent = '```json\\n' + JSON.stringify(testApiResponse, null, 2) + '\\n```';\n      const apiResponseMsg = {\n        isUser: false,\n        content: `测试API响应格式: ${jsonContent}`,\n        isTyping: false\n      };\n      \n      this.messages.push(apiResponseMsg);\n      this.parseChartConfig(apiResponseMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = jsonContent;\n      \n      console.log('API响应解析后的消息:', apiResponseMsg);\n    },\n    \n    // 测试后端实际返回的数据格式\n    testRealData() {\n      console.log('测试后端实际返回的数据格式');\n      \n      // 模拟后端返回的数据（从柱状图返回前端的数据文件中提取）\n      const realData = {\n        code: 0,\n        msg: null,\n        data: {\n          id: \"1719830816000\",\n          title: \"按名称分组的记录数柱状图\",\n          tableId: \"1114644377738809344\",\n          type: \"bar\",\n          data: {\n            data: [\n              {value: 1, field: \"神朔\", name: \"神朔\", category: \"记录数*\"},\n              {value: 1, field: \"甘泉\", name: \"甘泉\", category: \"记录数*\"},\n              {value: 1, field: \"包神\", name: \"包神\", category: \"记录数*\"}\n            ],\n            fields: [\n              {id: \"1746787308487\", name: \"名称\", groupType: \"d\"},\n              {id: \"-1\", name: \"记录数*\", groupType: \"q\"}\n            ]\n          }\n        }\n      };\n      \n      // 创建包含实际数据的消息\n      const realDataMsg = {\n        isUser: false,\n        content: '图表数据已成功获取！以下是名称分组的记录数统计结果。',\n        isTyping: false\n      };\n      \n      // 直接设置图表配置\n      realDataMsg.chartConfig = realData.data;\n      \n      // 添加到消息列表\n      this.messages.push(realDataMsg);\n      \n      // 保存原始响应\n      this.lastRawResponse = JSON.stringify(realData, null, 2);\n      \n      console.log('添加了实际数据的消息:', realDataMsg);\n    },\n\n    // 显示AI原始响应的方法\n    showRawResponse() {\n      this.showRawResponsePanel = true;\n      this.lastRawResponse = this.messages[this.messages.length - 1].content; // Get the full content of the last message\n    },\n\n    // 获取字段类型的CSS类名\n    getFieldTypeClass(type) {\n      const typeMap = {\n        'TEXT': 'text-type',\n        'STRING': 'text-type',\n        'VARCHAR': 'text-type',\n        'INT': 'number-type',\n        'INTEGER': 'number-type',\n        'BIGINT': 'number-type',\n        'DECIMAL': 'number-type',\n        'DOUBLE': 'number-type',\n        'FLOAT': 'number-type',\n        'DATE': 'date-type',\n        'DATETIME': 'date-type',\n        'TIMESTAMP': 'date-type',\n        'BOOLEAN': 'boolean-type'\n      };\n      return typeMap[type?.toUpperCase()] || 'default-type';\n    },\n\n    // 获取字段类型的显示标签\n    getFieldTypeLabel(type) {\n      const labelMap = {\n        'TEXT': '文本',\n        'STRING': '文本',\n        'VARCHAR': '文本',\n        'INT': '数值',\n        'INTEGER': '数值',\n        'BIGINT': '数值',\n        'DECIMAL': '数值',\n        'DOUBLE': '数值',\n        'FLOAT': '数值',\n        'DATE': '日期',\n        'DATETIME': '日期时间',\n        'TIMESTAMP': '时间戳',\n        'BOOLEAN': '布尔'\n      };\n      return labelMap[type?.toUpperCase()] || type || '未知';\n    },\n\n    // 获取字段描述\n    getFieldDescription(field) {\n      if (field.description) {\n        return field.description;\n      }\n\n      // 根据字段类型和分组类型生成默认描述\n      const typeDesc = this.getFieldTypeLabel(field.type);\n      const groupDesc = field.groupType === 'd' ? '维度' : '度量';\n\n      // 根据字段名称推测用途\n      const fieldName = (field.name || '').toLowerCase();\n      let purposeDesc = '';\n\n      if (fieldName.includes('id') || fieldName.includes('编号')) {\n        purposeDesc = '，用于唯一标识';\n      } else if (fieldName.includes('name') || fieldName.includes('名称')) {\n        purposeDesc = '，用于分类标识';\n      } else if (fieldName.includes('date') || fieldName.includes('时间') || fieldName.includes('日期')) {\n        purposeDesc = '，用于时间分析';\n      } else if (fieldName.includes('amount') || fieldName.includes('金额') || fieldName.includes('价格')) {\n        purposeDesc = '，用于金额统计';\n      } else if (fieldName.includes('count') || fieldName.includes('数量') || fieldName.includes('个数')) {\n        purposeDesc = '，用于数量统计';\n      } else if (field.groupType === 'q') {\n        purposeDesc = '，用于数值分析';\n      } else {\n        purposeDesc = '，用于数据分组';\n      }\n\n      return `${typeDesc}类型的${groupDesc}字段${purposeDesc}`;\n    },\n\n    // 获取字段图标类名\n    getFieldIconClass(field) {\n      const typeMap = {\n        'TEXT': 'text-icon',\n        'STRING': 'text-icon',\n        'VARCHAR': 'text-icon',\n        'INT': 'number-icon',\n        'INTEGER': 'number-icon',\n        'BIGINT': 'number-icon',\n        'DECIMAL': 'number-icon',\n        'DOUBLE': 'number-icon',\n        'FLOAT': 'number-icon',\n        'DATE': 'date-icon',\n        'DATETIME': 'date-icon',\n        'TIMESTAMP': 'date-icon',\n        'BOOLEAN': 'boolean-icon'\n      };\n      return typeMap[field.type?.toUpperCase()] || 'default-icon';\n    },\n\n    // 获取字段图标名称\n    getFieldIconName(field) {\n      const iconMap = {\n        'TEXT': 'el-icon-document',\n        'STRING': 'el-icon-document',\n        'VARCHAR': 'el-icon-document',\n        'INT': 'el-icon-s-data',\n        'INTEGER': 'el-icon-s-data',\n        'BIGINT': 'el-icon-s-data',\n        'DECIMAL': 'el-icon-s-data',\n        'DOUBLE': 'el-icon-s-data',\n        'FLOAT': 'el-icon-s-data',\n        'DATE': 'el-icon-date',\n        'DATETIME': 'el-icon-time',\n        'TIMESTAMP': 'el-icon-time',\n        'BOOLEAN': 'el-icon-switch-button'\n      };\n      return iconMap[field.type?.toUpperCase()] || 'el-icon-info';\n    },\n\n    // 数据预览相关方法\n    handlePageChange(page) {\n      this.currentPage = page;\n    },\n\n    getRowIndex(index) {\n      return (this.currentPage - 1) * this.pageSize + index + 1;\n    },\n\n    getCellValue(row, field) {\n      const fieldName = field.dataeaseName || field.name;\n      const value = row[fieldName];\n\n      // 处理null、undefined等值\n      if (value === null || value === undefined) {\n        return '-';\n      }\n\n      // 处理日期类型\n      if (field.type && ['DATE', 'DATETIME', 'TIMESTAMP'].includes(field.type.toUpperCase())) {\n        if (value) {\n          try {\n            return new Date(value).toLocaleString();\n          } catch (e) {\n            return value;\n          }\n        }\n      }\n\n      // 处理数值类型\n      if (field.type && ['INT', 'INTEGER', 'BIGINT', 'DECIMAL', 'DOUBLE', 'FLOAT'].includes(field.type.toUpperCase())) {\n        if (typeof value === 'number') {\n          return value.toLocaleString();\n        }\n      }\n\n      return value;\n    },\n\n    // PDF导出方法\n    async exportToPDF(message) {\n      if (!message.chartConfig) return;\n      \n      try {\n        // 设置导出状态\n        this.$set(message, 'exporting', true);\n        \n        // 1. 创建临时容器\n        const tempContainer = document.createElement('div');\n        tempContainer.style.position = 'absolute';\n        tempContainer.style.left = '-9999px';\n        tempContainer.style.width = '800px'; // 固定宽度\n        tempContainer.style.background = '#fff';\n        tempContainer.style.padding = '20px';\n        document.body.appendChild(tempContainer);\n        \n        // 2. 构建导出内容\n        // 标题 - 使用数据集名称\n        const title = message.chartConfig.title || '数据分析报告';\n        const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n        const currentDate = new Date().toLocaleString();\n        \n        // 提取AI描述文本 (去除HTML标签和chart_data_id)\n        let description = message.content\n          .replace(/<chart_data_id>.*?<\\/chart_data_id>/g, '')\n          .replace(/<div class=\"chart-notice\">.*?<\\/div>/g, '')\n          .trim();\n          \n        // 如果描述中有HTML标签，创建临时元素解析纯文本\n        if (description.includes('<')) {\n          const tempDiv = document.createElement('div');\n          tempDiv.innerHTML = description;\n          description = tempDiv.textContent || tempDiv.innerText || '';\n        }\n        \n        // 构建HTML内容\n        tempContainer.innerHTML = `\n          <div style=\"font-family: Arial, sans-serif;\">\n            <h1 style=\"text-align: center; color: #333;\">${title}</h1>\n            <p style=\"text-align: right; color: #666;\">数据集: ${datasetName}</p>\n            <p style=\"text-align: right; color: #666;\">生成时间: ${currentDate}</p>\n            <hr style=\"margin: 20px 0;\">\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>分析描述:</h3>\n              <p>${description}</p>\n            </div>\n            \n            <div id=\"chart-container\" style=\"height: 400px; margin: 20px 0;\"></div>\n            \n            <div style=\"margin: 20px 0;\">\n              <h3>数据表格:</h3>\n              <div id=\"data-table\"></div>\n            </div>\n          </div>\n        `;\n        \n        // 3. 渲染图表\n        const chartContainer = tempContainer.querySelector('#chart-container');\n        const chart = echarts.init(chartContainer);\n        \n        // 直接使用与问答界面相同的逻辑\n        const chartConfig = message.chartConfig;\n        let options;\n        \n        // 根据图表类型选择对应的处理函数\n        switch(chartConfig.type) {\n          case 'bar':\n            options = this.getBarChartOptions(chartConfig);\n            break;\n          case 'line':\n            options = this.getLineChartOptions(chartConfig);\n            break;\n          case 'pie':\n            options = this.getPieChartOptions(chartConfig);\n            break;\n          case 'bar-horizontal':\n            options = this.getBarHorizontalOptions(chartConfig);\n            break;\n          default:\n            options = this.getDefaultOptions(chartConfig);\n        }\n        \n        // 设置图表配置\n        chart.setOption(options);\n        console.log('PDF导出: 图表配置已设置', options);\n        \n        // 等待足够长的时间确保渲染完成\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        \n        // 4. 渲染数据表格\n        const dataTableContainer = tempContainer.querySelector('#data-table');\n        this.renderDataTable(dataTableContainer, message.chartConfig);\n        \n        // 5. 使用html2canvas捕获内容\n        const canvas = await html2canvas(tempContainer, {\n          scale: 2, // 提高清晰度\n          useCORS: true,\n          allowTaint: true,\n          backgroundColor: '#ffffff'\n        });\n        \n        // 6. 创建PDF\n        const imgData = canvas.toDataURL('image/png');\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 计算适当的宽度和高度以适应A4页面\n        const imgWidth = 210 - 40; // A4宽度减去边距\n        const imgHeight = (canvas.height * imgWidth) / canvas.width;\n        \n        // 添加图像到PDF\n        pdf.addImage(imgData, 'PNG', 20, 20, imgWidth, imgHeight);\n        \n        // 7. 保存PDF\n        pdf.save(`${title}-${new Date().getTime()}.pdf`);\n        \n        // 8. 清理\n        document.body.removeChild(tempContainer);\n        chart.dispose();\n        \n        // 重置导出状态\n        this.$set(message, 'exporting', false);\n        \n        // 显示成功消息\n        this.$message.success('PDF导出成功');\n        \n      } catch (error) {\n        console.error('PDF导出失败:', error);\n        this.$set(message, 'exporting', false);\n        this.$message.error('PDF导出失败: ' + error.message);\n      }\n    },\n    \n    // 导出完整对话方法\n    async exportAllConversation() {\n      try {\n        // 设置导出状态\n        this.exportingAll = true;\n        \n        // 1. 提取所有图表消息\n        let chartMessages = [];\n        \n        // 先找出所有包含图表的消息\n        for (let i = 0; i < this.messages.length; i++) {\n          const message = this.messages[i];\n          if (!message.isUser && message.chartConfig) {\n            chartMessages.push({\n              message: message,\n              index: i\n            });\n          }\n        }\n        \n        // 如果没有图表，显示提示\n        if (chartMessages.length === 0) {\n          this.$message.warning('暂无图表数据。请先通过对话生成图表。');\n          this.exportingAll = false;\n          return;\n        }\n        \n        // 2. 创建PDF\n        const pdf = new jsPDF({\n          orientation: 'portrait',\n          unit: 'mm',\n          format: 'a4'\n        });\n        \n        // 3. 处理每个图表，每个图表单独一页\n        for (let i = 0; i < chartMessages.length; i++) {\n          // 如果不是第一页，添加新页面\n          if (i > 0) {\n            pdf.addPage();\n          }\n          \n          const chartMessage = chartMessages[i].message;\n          const chartConfig = chartMessage.chartConfig;\n          \n          // 创建临时容器\n          const tempContainer = document.createElement('div');\n          tempContainer.style.position = 'absolute';\n          tempContainer.style.left = '-9999px';\n          tempContainer.style.width = '800px';\n          tempContainer.style.background = '#fff';\n          tempContainer.style.padding = '20px';\n          document.body.appendChild(tempContainer);\n          \n          // 构建PDF标题和基本信息\n          const title = chartConfig.title || '数据分析图表';\n          const currentDate = new Date().toLocaleString();\n          const datasetName = this.selectedTable ? this.selectedTable.tableName : '未知数据集';\n          \n          let htmlContent = `\n            <div style=\"font-family: Arial, sans-serif; padding: 20px;\">\n              <h1 style=\"text-align: center; color: #333; font-size: 24px; margin-bottom: 30px;\">${title}</h1>\n              <div style=\"text-align: right; margin-bottom: 20px;\">\n                <p style=\"color: #666; margin: 5px 0;\">数据集: ${datasetName}</p>\n                <p style=\"color: #666; margin: 5px 0;\">生成时间: ${currentDate}</p>\n              </div>\n              \n              <div style=\"margin: 20px 0;\">\n                <h2 style=\"color: #333; font-size: 20px; margin-bottom: 15px;\">分析描述:</h2>\n          `;\n          \n          // 添加描述\n          let description = chartMessage.content;\n          \n          // 清理HTML标签，保留纯文本\n          if (description.includes('<')) {\n            const tempDiv = document.createElement('div');\n            tempDiv.innerHTML = description;\n            description = tempDiv.textContent || tempDiv.innerText || '';\n          }\n          \n          htmlContent += `\n            <p style=\"margin: 15px 0; color: #333; line-height: 1.6;\">${description}</p>\n            \n            <div style=\"margin: 30px 0;\">\n              <div id=\"chart-container\" style=\"height: 400px; margin: 30px 0;\"></div>\n              \n              <div style=\"margin: 30px 0;\">\n                <h3 style=\"color: #333; font-size: 18px; margin-bottom: 15px;\">数据表格:</h3>\n                <div id=\"data-table\"></div>\n              </div>\n            </div>\n          `;\n          \n          htmlContent += `</div>`;\n          \n          // 设置HTML内容\n          tempContainer.innerHTML = htmlContent;\n          \n          // 渲染图表\n          const chartContainer = tempContainer.querySelector('#chart-container');\n          const dataTableContainer = tempContainer.querySelector('#data-table');\n          \n          if (chartContainer && dataTableContainer) {\n            // 初始化图表\n            const chartInstance = echarts.init(chartContainer);\n            let options;\n            \n            // 复用你现有的图表处理逻辑\n            switch(chartConfig.type) {\n              case 'bar':\n                options = this.getBarChartOptions(chartConfig);\n                break;\n              case 'line':\n                options = this.getLineChartOptions(chartConfig);\n                break;\n              case 'pie':\n                options = this.getPieChartOptions(chartConfig);\n                break;\n              case 'bar-horizontal':\n                options = this.getBarHorizontalOptions(chartConfig);\n                break;\n              default:\n                options = this.getDefaultOptions(chartConfig);\n            }\n            \n            chartInstance.setOption(options);\n            \n            // 渲染数据表格\n            this.renderDataTable(dataTableContainer, chartConfig);\n            \n            // 等待图表渲染完成\n            await new Promise(resolve => setTimeout(resolve, 1000));\n            \n            // 使用html2canvas捕获当前页面内容\n            const canvas = await html2canvas(tempContainer, {\n              scale: 2,\n              useCORS: true,\n              allowTaint: true,\n              backgroundColor: '#ffffff'\n            });\n            \n            // 将canvas转为图像\n            const imgData = canvas.toDataURL('image/png');\n            \n            // 计算适当的宽度和高度以适应A4页面\n            const pageWidth = pdf.internal.pageSize.getWidth();\n            const pageHeight = pdf.internal.pageSize.getHeight();\n            const imgWidth = pageWidth - 40; // 左右各20mm边距\n            const imgHeight = (canvas.height * imgWidth) / canvas.width;\n            \n            // 如果图像高度超过页面高度，进行缩放\n            let finalImgHeight = imgHeight;\n            let finalImgWidth = imgWidth;\n            \n            if (imgHeight > pageHeight - 40) { // 上下各20mm边距\n              finalImgHeight = pageHeight - 40;\n              finalImgWidth = (canvas.width * finalImgHeight) / canvas.height;\n            }\n            \n            // 添加图像到PDF，居中显示\n            const xPos = (pageWidth - finalImgWidth) / 2;\n            const yPos = 20; // 顶部边距\n            \n            pdf.addImage(imgData, 'PNG', xPos, yPos, finalImgWidth, finalImgHeight);\n            \n            // 清理\n            chartInstance.dispose();\n            document.body.removeChild(tempContainer);\n          }\n        }\n        \n        // 4. 保存PDF\n        const fileName = `指标分析报告_${new Date().toISOString().slice(0, 10)}.pdf`;\n        pdf.save(fileName);\n        \n        // 5. 重置状态\n        this.exportingAll = false;\n        this.$message.success('指标导出成功');\n        \n      } catch (error) {\n        console.error('指标导出失败:', error);\n        this.exportingAll = false;\n        this.$message.error('指标导出失败: ' + error.message);\n      }\n    },\n    \n    // 创建基本图表配置\n    createBasicChartOptions(chartConfig) {\n      const type = chartConfig.type || 'bar';\n      let data = [];\n      let categories = [];\n      \n      // 尝试提取数据\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        categories = data.map(item => item.field || item.name);\n      }\n      \n      // 创建基本配置\n      const options = {\n        title: {\n          text: chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: categories\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: []\n      };\n      \n      // 添加系列数据\n      if (data.length > 0) {\n        if (data[0].series) {\n          // 多系列数据\n          const seriesMap = {};\n          \n          // 收集所有系列\n          data.forEach(item => {\n            if (item.series) {\n              item.series.forEach(s => {\n                if (!seriesMap[s.category]) {\n                  seriesMap[s.category] = {\n                    name: s.category,\n                    type: type,\n                    data: Array(categories.length).fill(null)\n                  };\n                }\n              });\n            }\n          });\n          \n          // 填充数据\n          data.forEach((item, index) => {\n            if (item.series) {\n              item.series.forEach(s => {\n                seriesMap[s.category].data[index] = s.value;\n              });\n            }\n          });\n          \n          // 添加到series\n          options.series = Object.values(seriesMap);\n        } else {\n          // 单系列数据\n          options.series.push({\n            name: '数值',\n            type: type,\n            data: data.map(item => item.value)\n          });\n        }\n      }\n      \n      return options;\n    },\n    \n    // 从ChartDisplay组件复制的图表处理函数\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      let data = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n      }\n      \n      const seriesData = data.map(item => ({\n        name: item.field || item.name,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: chartData.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions(chartData) {\n      return {\n        title: {\n          text: chartData.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: chartData.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 渲染数据表格\n    renderDataTable(container, chartConfig) {\n      // 提取数据\n      let data = [];\n      let headers = [];\n      \n      // 处理不同的数据格式\n      if (chartConfig.data && chartConfig.data.data) {\n        data = chartConfig.data.data;\n        \n        // 尝试从数据中提取表头\n        if (data.length > 0) {\n          if (data[0].series) {\n            // 多系列数据\n            headers = ['维度'];\n            const firstItem = data[0];\n            if (firstItem.series && Array.isArray(firstItem.series)) {\n              firstItem.series.forEach(s => {\n                headers.push(s.category || '数值');\n              });\n            }\n          } else {\n            // 单系列数据\n            headers = ['维度', '数值'];\n          }\n        }\n      }\n      \n      // 创建表格HTML\n      let tableHTML = '<table style=\"width:100%; border-collapse: collapse;\">';\n      \n      // 添加表头\n      tableHTML += '<thead><tr>';\n      headers.forEach(header => {\n        tableHTML += `<th style=\"border: 1px solid #ddd; padding: 8px; text-align: left; background-color: #f2f2f2;\">${header}</th>`;\n      });\n      tableHTML += '</tr></thead>';\n      \n      // 添加数据行\n      tableHTML += '<tbody>';\n      data.forEach(item => {\n        tableHTML += '<tr>';\n        \n        // 添加维度列\n        tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.field || item.name || ''}</td>`;\n        \n        // 添加数值列\n        if (item.series) {\n          // 多系列数据\n          item.series.forEach(s => {\n            tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${s.value !== undefined ? s.value : ''}</td>`;\n          });\n        } else {\n          // 单系列数据\n          tableHTML += `<td style=\"border: 1px solid #ddd; padding: 8px;\">${item.value !== undefined ? item.value : ''}</td>`;\n        }\n        \n        tableHTML += '</tr>';\n      });\n      tableHTML += '</tbody></table>';\n      \n      // 设置表格HTML\n      container.innerHTML = tableHTML;\n    }\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  color: #2c3e50;\n  position: relative;\n  height: 100vh;\n  overflow: hidden;\n  background-color: #f5f5f5;\n}\n\n.app-layout {\n  display: flex;\n  height: 100vh; /* 占满整个视口高度 */\n  overflow: hidden;\n  background-color: #f5f5f5;\n}\n\n.sidebar {\n  width: 200px;\n  border-right: 1px solid #ebeef5;\n  background-color: #fff;\n  height: 100%;\n  overflow-y: auto;\n}\n\n.logo {\n  padding: 20px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.logo h2 {\n  margin: 0;\n  font-size: 25px;\n  text-align: center;\n  white-space: nowrap;\n  letter-spacing: 2px;\n  font-weight: 900;\n}\n\n.menu {\n  padding: 10px 0;\n}\n\n.menu-item {\n  padding: 10px 20px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  transition: all 0.2s ease;\n}\n\n.menu-item:hover {\n  background-color: #f5f7fa;\n}\n\n.menu-item i {\n  margin-right: 5px;\n}\n\n.menu-item.active {\n  background-color: #ecf5ff;\n  color: #409eff;\n}\n\n.main-content {\n  flex: 1;\n  padding: 20px;\n  overflow: visible; /* 允许子元素特效溢出 */\n  display: flex;\n  flex-direction: column; /* 垂直排列子元素 */\n  align-items: center;\n  max-width: 1200px;\n  margin: 0 auto;\n  height: 100vh; /* 占满整个视口高度 */\n  box-sizing: border-box; /* 包含padding和border */\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1); /* 平滑过渡 */\n  position: relative; /* 为子元素定位 */\n  background-color: #f5f5f5; /* 更灰的背景色 */\n}\n\n.header {\n  margin-bottom: 15px;\n  width: 100%;\n  text-align: center;\n  padding: 20px 0;\n}\n\n.header h2 {\n  margin: 0;\n  font-size: 30px;\n  color: #2c3e50;\n  font-weight: 600;\n}\n\n.highlight {\n  background: linear-gradient(135deg, #667eea 0%, #6432e4 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 800;\n  position: relative;\n  display: inline-block;\n  transition: all 0.3s ease;\n}\n\n.highlight::after {\n  content: '';\n  position: absolute;\n  bottom: -3px;\n  left: 0;\n  width: 100%;\n  height: 3px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 2px;\n  opacity: 0.7;\n  transform: scaleX(0);\n  transform-origin: left;\n  transition: transform 0.3s ease;\n}\n\n.highlight:hover::after {\n  transform: scaleX(1);\n}\n\n.data-selection {\n  margin-top: 0;\n  padding-top: 0;\n  overflow: visible; /* 允许子元素特效溢出 */\n  position: relative;\n  z-index: 1; /* 确保在正确层级 */\n  padding: 30px 20px;\n  border-radius: 20px;\n}\n\n.data-selection::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(118, 120, 240, 0.08);\n  border-radius: 20px;\n  z-index: -1;\n}\n\n.data-selection h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 18px;\n  color: #2c3e50;\n  font-weight: 600;\n}\n\n.data-sets {\n  width: 100%;\n  max-width: 1000px;\n  margin: 0 auto;\n  overflow: visible; /* 允许卡片特效溢出 */\n  position: relative;\n}\n\n.data-sets .el-row {\n  justify-content: center;\n}\n\n/* 欢迎容器 */\n.welcome-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 25px;\n  gap: 20px;\n}\n\n.welcome-icon {\n  font-size: 48px;\n  animation: float 3s ease-in-out infinite;\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n.welcome-content {\n  text-align: left;\n}\n\n.welcome-title {\n  margin: 0;\n  font-size: 32px;\n  font-weight: 700;\n  color: #1a1a1a;\n  line-height: 1.2;\n  margin-bottom: 8px;\n}\n\n.highlight-gradient {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  font-weight: 800;\n  position: relative;\n}\n\n.highlight-gradient::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  left: 0;\n  width: 100%;\n  height: 3px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 2px;\n  opacity: 0.6;\n}\n\n.welcome-subtitle {\n  color: #666;\n  font-size: 16px;\n  margin: 0;\n  line-height: 1.5;\n  max-width: 500px;\n}\n\n/* 功能徽章 */\n.feature-badges {\n  display: flex;\n  justify-content: center;\n  gap: 20px;\n  flex-wrap: wrap;\n}\n\n.badge {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n  color: white;\n  padding: 8px 16px;\n  border-radius: 20px;\n  font-size: 14px;\n  font-weight: 500;\n  box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);\n  transition: all 0.3s ease;\n  cursor: default;\n}\n\n.badge:nth-child(2) {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n}\n\n.badge:nth-child(3) {\n  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);\n  color: #8b4513;\n  box-shadow: 0 4px 12px rgba(252, 182, 159, 0.3);\n}\n\n.badge:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(240, 147, 251, 0.4);\n}\n\n.badge:nth-child(2):hover {\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n}\n\n.badge:nth-child(3):hover {\n  box-shadow: 0 6px 20px rgba(252, 182, 159, 0.4);\n}\n\n.badge-icon {\n  font-size: 16px;\n}\n\n.badge-text {\n  font-weight: 600;\n  letter-spacing: 0.5px;\n}\n\n.data-card {\n  cursor: pointer;\n  margin-bottom: 15px;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  width: 100%;\n  height: 110px;\n  background: #fff;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);\n  border-radius: 26px !important;\n  overflow: hidden; /* 隐藏按钮溢出 */\n  position: relative;\n  z-index: 2; /* 确保悬停时在上层 */\n}\n\n.data-card::before {\n  content: '';\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.03) 50%, rgba(255, 255, 255, 0.02) 100%);\n  border-radius: 28px;\n  z-index: -1;\n}\n\n\n.data-card:hover {\n  transform: translateY(-8px) scale(1.05);\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);\n  /* background: #acddf6; */\n  background: linear-gradient(135deg, #ffffff 0%, #eef4f8 20%, #eef4f8 50%, #dff0f9 100%);\n  z-index: 10; /* 悬停时提升层级 */\n}\n\n/* 悬停按钮容器 */\n.card-hover-buttons {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 50px;\n  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.03) 0%, rgba(255, 255, 255, 0.02) 0%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 12px;\n  padding: 0 4%;\n  transform: translateY(100%);\n  transition: transform 0.36s cubic-bezier(0.1, 0.2, 0.2, 0.3);\n}\n.card-btn.preview-btn {\n  font-size: 13px;\n}\n\n.data-card:hover .card-hover-buttons {\n  transform: translateY(0);\n}\n\n/* 悬停按钮样式 */\n.card-btn {\n  flex: 1;\n  height: 32px;\n  border: none;\n  border-radius: 16px;\n  font-size: 13px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  outline: none;\n}\n\n.preview-btn {\n  background: white;\n  color: #666;\n  border: 1px solid #e1e8ed;\n}\n\n.preview-btn:hover {\n  background: #f8f9fa;\n  border-color: #d1d9e0;\n}\n\n.ask-btn {\n  background: #6b6bec;\n  color: rgb(240, 243, 247);\n}\n\n.ask-btn:hover {\n  background: #6366f1;\n}\n\n.data-header {\n  padding-bottom: 10px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.3);\n  position: relative;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  justify-content: flex-start;\n  flex-wrap: nowrap;\n  min-height: 28px;\n}\n\n.sample-tag {\n  background-color: rgba(64, 158, 255, 0.9);\n  color: #ffffff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-right: 8px;\n  font-weight: 500;\n  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);\n  white-space: nowrap;\n  flex-shrink: 0;\n}\n\n.data-title {\n  margin-left: 5px;\n  margin-right: 5px;\n  font-weight: bold;\n  color: #2c3e50;\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 150px;\n  display: inline-block;\n  vertical-align: middle;\n}\n\n.common-tag {\n  background-color: rgba(144, 147, 153, 0.9);\n  color: #ffffff;\n  padding: 2px 6px;\n  font-size: 12px;\n  border-radius: 4px;\n  margin-left: 8px;\n  font-weight: 500;\n  box-shadow: 0 2px 4px rgba(144, 147, 153, 0.3);\n  white-space: nowrap;\n  flex-shrink: 0;\n}\n\n.data-fields {\n  display: flex;\n  flex-wrap: nowrap;\n  margin-top: 10px;\n  position: relative;\n  z-index: 2;\n  overflow: hidden;\n  align-items: center;\n  gap: 4px;\n}\n\n.field-tag-single-line {\n  margin-right: 0 !important;\n  margin-bottom: 0 !important;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 60px;\n  flex-shrink: 0;\n}\n\n.more-fields-indicator {\n  color: #909399;\n  font-size: 12px;\n  margin-left: 4px;\n  flex-shrink: 0;\n}\n\n.field-item {\n  background-color: #f5f7fa;\n  padding: 2px 8px;\n  margin: 4px;\n  border-radius: 2px;\n  font-size: 12px;\n}\n\n.result-section {\n  margin-top: 20px;\n  padding: 15px;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n}\n\n.answer-text {\n  font-size: 14px;\n  line-height: 1.6;\n  margin-bottom: 15px;\n}\n\n.recent-chats {\n  margin-top: 20px;\n}\n\n.recent-chats .title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  font-size: 14px;\n  color: #606266;\n}\n\n.chat-list {\n  margin-top: 5px;\n}\n\n.chat-item {\n  padding: 8px 15px;\n  font-size: 12px;\n  color: #303133;\n  cursor: pointer;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.chat-time {\n  font-size: 10px;\n  color: #909399;\n  margin-top: 4px;\n}\n\n.multi-line-input .el-input__inner {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  height: auto !important;\n  line-height: inherit;\n  padding: 6px 10px;\n  min-height: 40px;\n}\n\n/* 底部问题输入区域（关键修改） */\n.question-input-container {\n  width: 100%;\n  padding: 25px 15px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  margin-top: 20px;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  min-height: 120px;\n}\n\n/* 移除展开状态样式，保持简洁 */\n\n.question-input-container > span {\n  margin-bottom: 18px; /* 增大10%: 16px * 1.1 ≈ 18px */\n  color: #606266;\n  font-size: 15px; /* 增大10%: 14px * 1.1 ≈ 15px */\n  font-weight: 500;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n}\n\n.question-input-wrapper {\n  align-items: center;\n  width: 100%;\n  max-width: 715px; /* 增大10%: 650px * 1.1 = 715px */\n  margin: 0 auto;\n  background: #fff;\n  border-radius: 29px; /* 增大10%: 26px * 1.1 ≈ 29px */\n  padding: 14px 24px; /* 增大10%: 13px * 1.1 ≈ 14px, 22px * 1.1 ≈ 24px */\n  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.025),\n              0 1px 2px rgba(0, 0, 0, 0.015);\n  border: 1px solid rgba(255, 255, 255, 0.8);\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n/* 当有字段选择区域时，调整输入框的上边框圆角 */\n.field-selection-mini ~ .question-input-wrapper {\n  border-radius: 0 0 29px 29px; /* 与输入框容器保持一致 */\n  border-top: none;\n}\n\n.question-input-wrapper::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);\n}\n\n.question-input-wrapper:hover {\n  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12),\n              0 3px 12px rgba(0, 0, 0, 0.06);\n  transform: translateY(-1px);\n}\n\n.question-input-wrapper:focus-within {\n  box-shadow: 0 8px 30px rgba(64, 158, 255, 0.15),\n              0 4px 15px rgba(64, 158, 255, 0.08);\n  border-color: rgba(64, 158, 255, 0.3);\n}\n\n.input-prefix {\n  margin-right: 10px;\n}\n\n.question-input {\n  flex: 1;\n}\n\n.question-input .el-input__inner {\n  border: none;\n  background-color: transparent;\n  font-size: 17px; /* 增大10%: 15px * 1.1 ≈ 17px */\n  font-weight: 400;\n  color: #2c3e50;\n  padding: 9px 13px; /* 增大10%: 8px * 1.1 ≈ 9px, 12px * 1.1 ≈ 13px */\n  transition: all 0.2s ease;\n}\n\n.question-input .el-input__inner::placeholder {\n  color: #a0a8b0;\n  font-weight: 400;\n}\n\n.question-input .el-input__inner:focus {\n  color: #1a202c;\n}\n\n.input-actions {\n  display: flex;\n  align-items: center;\n  float: right;\n  gap: 9px; /* 增大10%: 8px * 1.1 ≈ 9px */\n}\n\n.action-btn {\n  width: 40px; /* 增大10%: 36px * 1.1 = 40px */\n  height: 40px; /* 增大10%: 36px * 1.1 = 40px */\n  border-radius: 50%;\n  background: #fff; /* 与卡片相同的纯白色背景 */\n  border: 1px solid rgba(0, 0, 0, 0.1); /* 与卡片相同的边框 */\n  margin-left: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  outline: none;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* 与卡片相同的过渡时间 */\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03); /* 与卡片相同的极浅阴影 */\n  position: relative;\n  overflow: hidden;\n}\n\n.action-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.8) 0%, transparent 70%);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.action-btn:hover {\n  transform: translateY(-8px) scale(1.05); /* 与卡片相同的悬停效果 */\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2); /* 与卡片相同的阴影 */\n  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%); /* 与卡片相同的悬停渐变 */\n}\n\n.action-btn:hover::before {\n  opacity: 1;\n}\n\n.action-btn:active {\n  transform: translateY(-1px) scale(1.02);\n  transition: all 0.1s ease;\n}\n\n.send-btn {\n  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);\n  color: white;\n  border: none;\n  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3),\n              0 2px 8px rgba(64, 158, 255, 0.2);\n}\n\n.send-btn:hover {\n  background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);\n  box-shadow: 0 15px 35px rgba(64, 158, 255, 0.4); /* 与卡片相同的阴影强度 */\n  transform: translateY(-8px) scale(1.05); /* 与卡片相同的悬停效果 */\n}\n\n.test-btn {\n  background: #fff; /* 与卡片相同的纯白色背景 */\n  color: #333333; /* 统一文字颜色 */\n  border: 1px solid rgba(0, 0, 0, 0.1); /* 与卡片相同的边框 */\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03); /* 与卡片相同的极浅阴影 */\n}\n\n.test-btn:hover {\n  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%); /* 与卡片相同的悬停渐变 */\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2); /* 与卡片相同的阴影 */\n  transform: translateY(-8px) scale(1.05); /* 与卡片相同的悬停效果 */\n}\n\n.debug-btn {\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 30%, #e9ecef 70%, #ced4da 100%); /* 与卡片相同的渐变 */\n  color: #333333; /* 统一文字颜色 */\n  border: 1px solid rgba(0, 0, 0, 0.1); /* 与卡片相同的边框 */\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 与卡片相同的阴影 */\n}\n\n.debug-btn:hover {\n  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%); /* 与卡片相同的悬停渐变 */\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2); /* 与卡片相同的阴影 */\n  transform: translateY(-8px) scale(1.05); /* 与卡片相同的悬停效果 */\n}\n\n.send-btn:disabled {\n  background: linear-gradient(135deg, #c0c4cc 0%, #d3d4d6 100%);\n  cursor: not-allowed;\n  box-shadow: 0 2px 8px rgba(192, 196, 204, 0.2);\n  transform: none !important;\n}\n\n.send-btn:disabled:hover {\n  transform: none !important;\n  box-shadow: 0 2px 8px rgba(192, 196, 204, 0.2);\n}\n\n/* 按钮图标样式优化 */\n.action-btn i {\n  font-size: 18px; /* 增大10%: 16px * 1.1 ≈ 18px */\n  transition: all 0.3s ease;\n}\n\n.action-btn:hover i {\n  transform: scale(1.1);\n}\n\n.send-btn i {\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n.test-btn i {\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n.debug-btn i {\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n/* 输入框容器的微光效果 */\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n/* 为整个输入容器添加特效（当没有字段选择区域时） */\n.question-input-container:focus-within .question-input-wrapper::after {\n  content: '';\n  position: absolute;\n  top: 1px;\n  left: 1px;\n  right: 1px;\n  bottom: 1px;\n  background: linear-gradient(\n    90deg,\n    transparent,\n    rgba(64, 158, 255, 0.1),\n    transparent\n  );\n  background-size: 200% 100%;\n  animation: shimmer 2s infinite;\n  pointer-events: none;\n  border-radius: 28px; /* 稍小于容器圆角，避免边框遮挡 */\n  z-index: 1;\n}\n\n/* 当有字段选择区域时，隐藏输入框的特效，使用整体特效 */\n.question-input-container:focus-within .field-selection-mini ~ .question-input-wrapper::after {\n  display: none;\n}\n\n/* 当有字段选择区域时，调整特效的定位和圆角 */\n.field-selection-mini ~ .question-input-wrapper:focus-within::after {\n  top: 0; /* 顶部没有边框，从0开始 */\n  border-radius: 0 0 28px 28px; /* 只有底部圆角 */\n}\n\n/* 为整个输入区域添加统一特效（包括字段选择区域和输入框） */\n.question-input-container:focus-within .field-selection-mini + .question-input-wrapper::before {\n  content: '';\n  position: absolute;\n  top: -100%; /* 向上延伸覆盖字段选择区域 */\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    90deg,\n    transparent,\n    rgba(64, 158, 255, 0.1),\n    transparent\n  );\n  background-size: 200% 100%;\n  animation: shimmer 2s infinite;\n  pointer-events: none;\n  border-radius: 31px 31px 28px 28px; /* 上部与字段选择区域圆角匹配，下部与输入框匹配 */\n  z-index: 0; /* 放在内容下方 */\n}\n\n/* 头部操作按钮样式优化 - 与卡片统一 */\n.header-action-btn {\n  margin-left: 13px !important; /* 增大10%: 12px * 1.1 ≈ 13px */\n  padding: 9px 18px !important; /* 增大10%: 8px * 1.1 ≈ 9px, 16px * 1.1 ≈ 18px */\n  border-radius: 22px !important; /* 增大10%: 20px * 1.1 = 22px */\n  font-weight: 500 !important;\n  font-size: 15px !important; /* 增大10%: 默认14px * 1.1 ≈ 15px */\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important; /* 与卡片相同的过渡时间 */\n  position: relative !important;\n  overflow: hidden !important;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03) !important; /* 与卡片相同的极浅阴影 */\n  border: 1px solid rgba(0, 0, 0, 0.1) !important; /* 与卡片相同的边框 */\n  background: #fff !important; /* 纯白色背景 */\n  color: #333333 !important; /* 统一文字颜色 */\n}\n\n.header-action-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(135deg, #ffffff 0%, #eef4f8 20%, #eef4f8 50%, #dff0f9 100%);\n\n  /* background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent); */\n  transition: left 0.5s ease;\n}\n\n.header-action-btn:hover::before {\n  left: 100%;\n}\n\n.select-data-btn:hover {\n  background: linear-gradient(135deg, #ffffff 0%, #eef4f8 20%, #eef4f8 50%, #dff0f9 100%);\n\n  /* background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%) !important; 与卡片相同的悬停渐变 */\n  color: #333333 !important;\n  transform: translateY(-8px) scale(1.05) !important; /* 与卡片相同的悬停效果 */\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important; /* 与卡片相同的阴影 */\n}\n\n.export-btn:hover {\n  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 20%, #dee2e6 50%, #adb5bd 100%) !important; /* 与卡片相同的悬停渐变 */\n  color: #333333 !important;\n  transform: translateY(-8px) scale(1.05) !important; /* 与卡片相同的悬停效果 */\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2) !important; /* 与卡片相同的阴影 */\n}\n\n.header-action-btn:disabled {\n  background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 100%) !important;\n  color: #bdbdbd !important;\n  border-color: rgba(189, 189, 189, 0.3) !important;\n  transform: none !important;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;\n}\n\n.header-action-btn i {\n  margin-right: 6px;\n  transition: transform 0.3s ease;\n}\n\n.header-action-btn:hover i {\n  transform: scale(1.1) rotate(5deg);\n}\n\n/* 数据集抽屉样式优化 */\n.dataset-drawer .el-drawer__header {\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n  padding: 20px 24px;\n  margin-bottom: 0;\n}\n\n.dataset-drawer .el-drawer__title {\n  font-size: 18px;\n  font-weight: 600;\n  color: #2c3e50;\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);\n}\n\n.drawer-content {\n  padding: 24px;\n  background-color: #f5f5f5;\n  min-height: 100%;\n}\n\n.search-section {\n  margin-bottom: 24px;\n  background: white;\n  padding: 20px;\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);\n  border: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.dataset-search-input {\n  margin-bottom: 12px;\n}\n\n.dataset-search-input .el-input__inner {\n  border-radius: 8px;\n  border: 2px solid #e9ecef;\n  padding: 12px 16px;\n  font-size: 15px;\n  transition: all 0.3s ease;\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\n}\n\n.dataset-search-input .el-input__inner:focus {\n  border-color: #409eff;\n  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);\n  background: #ffffff;\n}\n\n.dataset-search-input .el-input__prefix {\n  color: #909399;\n  font-size: 16px;\n}\n\n.search-stats {\n  color: #606266;\n  font-size: 13px;\n  font-weight: 500;\n  text-align: right;\n  margin-top: 8px;\n  padding: 4px 8px;\n  background: rgba(64, 158, 255, 0.1);\n  border-radius: 12px;\n  float: right;\n}\n\n.datasets-grid {\n  background: transparent;\n}\n\n.drawer-data-card {\n  margin-bottom: 20px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  border: 1px solid rgba(0, 0, 0, 0.08);\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);\n  position: relative;\n}\n\n.drawer-data-card::before {\n  content: '';\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.03) 50%, rgba(255, 255, 255, 0.02) 100%);\n  border-radius: 6px;\n  z-index: -1;\n}\n\n.drawer-data-card:hover {\n  transform: translateY(-4px) scale(1.02);\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);\n}\n\n.field-tag {\n  margin-right: 6px !important;\n  margin-bottom: 6px !important;\n  background: rgba(64, 158, 255, 0.1) !important;\n  border: 1px solid rgba(64, 158, 255, 0.2) !important;\n  color: #409eff !important;\n  font-weight: 500 !important;\n}\n\n.more-fields {\n  color: #909399;\n  font-size: 12px;\n  font-style: italic;\n  margin-left: 4px;\n}\n\n/* 抽屉整体样式优化 */\n.dataset-drawer .el-drawer {\n  border-radius: 12px 0 0 12px;\n  overflow: hidden;\n}\n\n.dataset-drawer .el-drawer__body {\n  padding: 0;\n  background: #fafbfc;\n}\n\n/* 优化滚动条样式 */\n.dataset-drawer .el-drawer__body::-webkit-scrollbar {\n  width: 6px;\n}\n\n.dataset-drawer .el-drawer__body::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.05);\n}\n\n.dataset-drawer .el-drawer__body::-webkit-scrollbar-thumb {\n  background: rgba(64, 158, 255, 0.3);\n  border-radius: 3px;\n}\n\n.dataset-drawer .el-drawer__body::-webkit-scrollbar-thumb:hover {\n  background: rgba(64, 158, 255, 0.5);\n}\n\n/* 空状态样式 */\n.empty-state {\n  text-align: center;\n  padding: 60px 20px;\n  color: #909399;\n}\n\n.empty-state i {\n  font-size: 48px;\n  margin-bottom: 16px;\n  color: #c0c4cc;\n}\n\n.empty-state p {\n  font-size: 16px;\n  margin: 0;\n}\n\n/* 新的字段表格样式 - 干净整齐的设计 */\n.fields-table-container {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);\n  border: 1px solid #e9ecef;\n  margin: 16px;\n}\n\n/* 搜索栏样式 */\n.table-search-bar {\n  padding: 12px 16px;\n  border-bottom: 1px solid #f1f3f4;\n  background: #fafbfc;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.field-search-input {\n  width: 180px;\n}\n\n.field-search-input .el-input__inner {\n  border-radius: 16px;\n  border: 1px solid #e1e8ed;\n  background: white;\n  font-size: 13px;\n  height: 32px;\n}\n\n/* 表格样式 */\n.clean-table {\n  width: 100%;\n}\n\n.table-header {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1.5fr 2fr;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.header-cell {\n  padding: 10px 14px;\n  font-size: 13px;\n  font-weight: 600;\n  color: #495057;\n  border-right: 1px solid #e9ecef;\n  display: flex;\n  align-items: center;\n}\n\n.header-cell:last-child {\n  border-right: none;\n}\n\n.table-body {\n  background: white;\n}\n\n.table-row {\n  display: grid;\n  grid-template-columns: 2fr 1fr 1.5fr 2fr;\n  border-bottom: 1px solid #f1f3f4;\n  transition: background-color 0.2s ease;\n  background-color: white;\n}\n\n.table-row:nth-child(even) {\n  background-color: #fafbfc;\n}\n\n.table-row:hover {\n  background-color: #e8f4fd !important;\n  cursor: pointer;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.table-cell {\n  padding: 8px 14px;\n  border-right: 1px solid #f1f3f4;\n  display: flex;\n  align-items: center;\n  min-height: 40px;\n}\n\n.table-cell:last-child {\n  border-right: none;\n}\n\n/* 字段名称列样式 */\n.field-name-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.field-type-icon {\n  width: 18px;\n  height: 18px;\n  border-radius: 3px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n  font-size: 11px;\n}\n\n.field-type-icon.text-icon {\n  background: rgba(64, 158, 255, 0.1);\n  color: #409eff;\n}\n\n.field-type-icon.number-icon {\n  background: rgba(103, 194, 58, 0.1);\n  color: #67c23a;\n}\n\n.field-type-icon.date-icon {\n  background: rgba(230, 162, 60, 0.1);\n  color: #e6a23c;\n}\n\n.field-type-icon.boolean-icon {\n  background: rgba(144, 147, 153, 0.1);\n  color: #909399;\n}\n\n.field-type-icon.default-icon {\n  background: rgba(144, 147, 153, 0.1);\n  color: #909399;\n}\n\n.field-name-text {\n  font-size: 13px;\n  font-weight: 500;\n  color: #2c3e50;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 类型列样式 */\n.field-type-text {\n  font-size: 13px;\n  color: #495057;\n  font-weight: 400;\n}\n\n/* 维度/度量列样式 */\n.group-type-text {\n  font-size: 12px;\n  font-weight: 500;\n  padding: 3px 6px;\n  border-radius: 10px;\n  display: inline-block;\n}\n\n.group-type-text.dimension-text {\n  background: rgba(64, 158, 255, 0.1);\n  color: #409eff;\n}\n\n.group-type-text.measure-text {\n  background: rgba(103, 194, 58, 0.1);\n  color: #67c23a;\n}\n\n/* 字段描述列样式 */\n.field-desc-text {\n  font-size: 13px;\n  color: #495057;\n  font-weight: 400;\n}\n\n.field-type-badge {\n  padding: 3px 10px;\n  border-radius: 12px;\n  font-size: 14px;\n  font-weight: 500;\n  white-space: nowrap;\n}\n\n.field-type-badge.text-type {\n  background: rgba(103, 194, 58, 0.1);\n  color: #67c23a;\n  border: 1px solid rgba(103, 194, 58, 0.3);\n}\n\n.field-type-badge.number-type {\n  background: rgba(64, 158, 255, 0.1);\n  color: #409eff;\n  border: 1px solid rgba(64, 158, 255, 0.3);\n}\n\n.field-type-badge.date-type {\n  background: rgba(230, 162, 60, 0.1);\n  color: #e6a23c;\n  border: 1px solid rgba(230, 162, 60, 0.3);\n}\n\n.field-type-badge.default-type {\n  background: rgba(144, 147, 153, 0.1);\n  color: #909399;\n  border: 1px solid rgba(144, 147, 153, 0.3);\n}\n\n.field-group-type {\n  font-size: 12px;\n  padding: 1px 6px;\n  border-radius: 8px;\n  font-weight: 500;\n}\n\n.field-group-type.dimension-type {\n  background: rgba(64, 158, 255, 0.1);\n  color: #409eff;\n}\n\n.field-group-type.measure-type {\n  background: rgba(103, 194, 58, 0.1);\n  color: #67c23a;\n}\n\n\n\n/* 字段描述列样式 */\n.field-desc-content {\n  width: 100%;\n}\n\n.field-desc {\n  font-size: 15px;\n  color: #606266;\n  line-height: 1.4;\n  word-break: break-word;\n}\n\n.no-desc {\n  font-size: 15px;\n  color: #909399;\n  font-style: italic;\n  line-height: 1.4;\n}\n\n/* 底部统计区域样式 */\n.table-footer {\n  padding: 10px 16px;\n  background: #f8f9fa;\n  border-top: 1px solid #e9ecef;\n}\n\n.field-stats-summary {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  font-size: 13px;\n  color: #495057;\n}\n\n.total-count {\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.type-counts {\n  display: flex;\n  gap: 16px;\n}\n\n.dimension-count,\n.measure-count {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-weight: 500;\n}\n\n.count-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n}\n\n.dimension-dot {\n  background: #409eff;\n}\n\n.measure-dot {\n  background: #67c23a;\n}\n\n.preview-limit {\n  color: #6c757d;\n  font-size: 12px;\n}\n\n/* 表格滚动条优化 */\n.table-body::-webkit-scrollbar {\n  width: 6px;\n}\n\n.table-body::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n\n.table-body::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n\n.table-body::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .table-header,\n  .table-row {\n    grid-template-columns: 1.5fr 1fr 1fr 1.5fr;\n  }\n\n  .field-name-text {\n    font-size: 12px;\n  }\n\n  .field-desc-text {\n    font-size: 12px;\n  }\n}\n\n@media (max-width: 768px) {\n  .table-header,\n  .table-row {\n    grid-template-columns: 1fr;\n  }\n\n  .table-cell {\n    border-right: none;\n    border-bottom: 1px solid #f1f3f4;\n    padding: 8px 12px;\n    min-height: auto;\n  }\n\n  .header-cell::before {\n    content: attr(data-label);\n    font-weight: 600;\n    margin-right: 8px;\n  }\n\n  .field-stats-summary {\n    flex-direction: column;\n    gap: 8px;\n    align-items: flex-start;\n  }\n\n  .type-counts {\n    gap: 12px;\n  }\n}\n\n/* 数据详情抽屉样式 */\n.dataset-detail-drawer .el-drawer__header {\n  \n  color: #2c3e50;\n  padding: 20px 24px;\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.dataset-detail-drawer .el-drawer__header::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transition: left 0.6s ease;\n}\n\n.dataset-detail-drawer .el-drawer__header:hover::before {\n  left: 100%;\n}\n\n.dataset-detail-drawer .el-drawer__title {\n  color: #2c3e50;\n  font-size: 18px;\n  font-weight: 600;\n  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);\n}\n\n.dataset-detail-drawer .el-drawer__body {\n  padding: 0;\n  background: #f8f9fa;\n  overflow-y: auto;\n  height: 100%;\n}\n\n/* 让数据详情内容自然展示，通过抽屉本身的滚动来处理溢出 */\n.detail-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 头部信息区域 */\n.detail-header {\n  background: white;\n  padding: 20px;\n  border-bottom: none;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  position: relative;\n}\n\n/* 统一的数据集信息区域 */\n.dataset-info-unified {\n  flex: 1;\n  background: #f8f9fa;\n  padding: 16px;\n  border-radius: 8px;\n}\n\n.dataset-title-row {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 12px;\n}\n\n.dataset-icon {\n  color: #667eea;\n  font-size: 20px;\n}\n\n.dataset-name {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.dataset-stats {\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n}\n\n.stat-badge {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 13px;\n  color: #495057;\n  background: white;\n  padding: 4px 10px;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.stat-badge i {\n  color: #6c757d;\n  font-size: 12px;\n}\n\n/* 悬浮提问按钮 */\n.floating-ask-btn {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border: none;\n  border-radius: 25px;\n  padding: 10px 24px;\n  font-weight: 500;\n  color: white;\n  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);\n  transition: all 0.3s ease;\n  position: absolute;\n  top: 16px;\n  right: 16px;\n  overflow: hidden;\n  font-size: 14px;\n  z-index: 10;\n}\n\n.floating-ask-btn::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\n  transition: left 0.6s ease;\n}\n\n.floating-ask-btn:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);\n  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);\n}\n\n.floating-ask-btn:hover::before {\n  left: 100%;\n}\n\n.floating-ask-btn:active {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n}\n\n/* 字段信息区域 */\n.fields-section {\n  background: transparent;\n  margin: 0;\n  padding: 0;\n}\n\n/* 字段头部样式 */\n.fields-header {\n  padding: 20px 20px 16px 20px;\n  background: transparent;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 16px;\n}\n\n.fields-summary {\n  flex: 1;\n}\n\n.summary-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 8px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.summary-title i {\n  color: #667eea;\n}\n\n.summary-stats {\n  display: flex;\n  gap: 12px;\n  flex-wrap: wrap;\n}\n\n.stat-badge {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 12px;\n  padding: 4px 10px;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.stat-badge.dimension {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.stat-badge.measure {\n  background: #e8f5e8;\n  color: #388e3c;\n}\n\n.stat-badge.total {\n  background: #f3e5f5;\n  color: #7b1fa2;\n}\n\n.fields-search {\n  flex-shrink: 0;\n}\n\n.field-search-input {\n  width: 200px;\n}\n\n/* 字段卡片列表 */\n.fields-cards {\n  padding: 0 20px 20px 20px;\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 16px;\n}\n\n.field-card {\n  background: #fafbfc;\n  border-radius: 6px;\n  padding: 16px;\n  transition: all 0.2s ease;\n}\n\n.field-card:hover {\n  background: #f1f3f4;\n}\n\n.field-card-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 8px;\n}\n\n.field-info {\n  display: flex;\n  align-items: flex-start;\n  gap: 10px;\n  flex: 1;\n}\n\n.field-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  color: white;\n  flex-shrink: 0;\n}\n\n.field-details {\n  flex: 1;\n}\n\n.field-name {\n  font-size: 14px;\n  font-weight: 600;\n  color: #2c3e50;\n  margin-bottom: 2px;\n}\n\n.field-type {\n  font-size: 12px;\n  color: #6c757d;\n}\n\n.field-badge {\n  flex-shrink: 0;\n}\n\n.group-badge {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 11px;\n  padding: 3px 8px;\n  border-radius: 10px;\n  font-weight: 500;\n}\n\n.group-badge.dimension {\n  background: #e3f2fd;\n  color: #1976d2;\n}\n\n.group-badge.measure {\n  background: #e8f5e8;\n  color: #388e3c;\n}\n\n.field-description {\n  font-size: 12px;\n  color: #6c757d;\n  line-height: 1.4;\n  padding-top: 8px;\n  margin-top: 8px;\n}\n\n.section-header h3 i {\n  color: #667eea;\n}\n\n.field-stats {\n  display: flex;\n  gap: 16px;\n}\n\n.stat-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 14px;\n  padding: 6px 12px;\n  border-radius: 12px;\n  font-weight: 500;\n}\n\n.stat-item.dimension {\n  background: rgba(52, 152, 219, 0.1);\n  color: #3498db;\n}\n\n.stat-item.measure {\n  background: rgba(46, 204, 113, 0.1);\n  color: #2ecc71;\n}\n\n.fields-grid {\n  padding: 20px;\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 16px;\n}\n\n.field-card {\n  background: #fff;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  padding: 16px;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.field-card::after {\n  content: '';\n  position: absolute;\n  top: -2px;\n  left: -2px;\n  right: -2px;\n  bottom: -2px;\n  background: linear-gradient(135deg, rgba(64, 158, 255, 0.08) 0%, rgba(64, 158, 255, 0.03) 50%, rgba(255, 255, 255, 0.02) 100%);\n  border-radius: 10px;\n  z-index: -1;\n}\n\n.field-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 4px;\n  height: 100%;\n  background: #e9ecef;\n  transition: all 0.3s ease;\n}\n\n.field-card.dimension-field::before {\n  background: linear-gradient(180deg, #3498db 0%, #2980b9 100%);\n}\n\n.field-card.measure-field::before {\n  background: linear-gradient(180deg, #2ecc71 0%, #27ae60 100%);\n}\n\n.field-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  border-color: #d1ecf1;\n}\n\n.field-header {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  margin-bottom: 12px;\n}\n\n.field-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.dimension-field .field-icon {\n  background: rgba(52, 152, 219, 0.1);\n  color: #3498db;\n}\n\n.measure-field .field-icon {\n  background: rgba(46, 204, 113, 0.1);\n  color: #2ecc71;\n}\n\n.field-info {\n  flex: 1;\n  min-width: 0;\n}\n\n.field-name {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 14px;\n  margin-bottom: 2px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.field-origin {\n  font-size: 12px;\n  color: #6c757d;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.field-type-badge {\n  padding: 4px 8px;\n  border-radius: 12px;\n  font-size: 11px;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  flex-shrink: 0;\n}\n\n.field-type-badge.text-type {\n  background: rgba(108, 117, 125, 0.1);\n  color: #6c757d;\n}\n\n.field-type-badge.number-type {\n  background: rgba(255, 193, 7, 0.1);\n  color: #f39c12;\n}\n\n.field-type-badge.date-type {\n  background: rgba(156, 39, 176, 0.1);\n  color: #9c27b0;\n}\n\n.field-type-badge.boolean-type {\n  background: rgba(76, 175, 80, 0.1);\n  color: #4caf50;\n}\n\n.field-type-badge.default-type {\n  background: rgba(158, 158, 158, 0.1);\n  color: #9e9e9e;\n}\n\n.field-details {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  font-size: 12px;\n}\n\n.detail-label {\n  color: #6c757d;\n  font-weight: 500;\n}\n\n.detail-value {\n  color: #495057;\n  font-weight: 400;\n}\n\n/* 数据预览区域 */\n.preview-section {\n  background: transparent;\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  height: calc(100vh - 200px);\n}\n\n/* 数据预览头部 */\n.preview-header {\n  padding: 20px 20px 16px 20px;\n  background: transparent;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.preview-stats {\n  display: flex;\n  gap: 16px;\n  align-items: center;\n  flex-wrap: wrap;\n}\n\n.total-records {\n  font-weight: 600;\n  color: #2c3e50;\n  font-size: 14px;\n}\n\n.current-page-info {\n  color: #6c757d;\n  font-size: 13px;\n}\n\n.preview-pagination {\n  flex-shrink: 0;\n}\n\n/* 数据预览表格容器 */\n.preview-table-wrapper {\n  padding: 0;\n  overflow: auto;\n  flex: 1;\n  min-height: 0;\n}\n\n.preview-table {\n  width: 100%;\n  background: white;\n}\n\n.preview-table .cell-content {\n  font-size: 13px;\n  color: #495057;\n  word-break: break-word;\n}\n\n.preview-table .el-table__header {\n  background: #f8f9fa;\n}\n\n.preview-table .el-table__header th {\n  background: #f8f9fa;\n  color: #495057;\n  font-weight: 600;\n  font-size: 15px;\n  border-bottom: 2px solid #dee2e6;\n}\n\n.preview-table .el-table__body tr:hover {\n  background: rgba(102, 126, 234, 0.05);\n}\n\n.preview-table .el-table__body td {\n  border-bottom: 1px solid #f1f3f4;\n  font-size: 15px;\n  color: #606266;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .fields-grid {\n    grid-template-columns: 1fr;\n    padding: 16px;\n  }\n\n  .detail-header {\n    flex-direction: column;\n    gap: 16px;\n    align-items: stretch;\n    padding: 16px;\n  }\n\n  .floating-ask-btn {\n    position: static;\n    align-self: flex-end;\n    margin-top: 12px;\n  }\n\n  .preview-header {\n    flex-direction: column;\n    align-items: stretch;\n    gap: 12px;\n  }\n\n  .preview-stats {\n    justify-content: center;\n  }\n\n  .preview-pagination {\n    align-self: center;\n  }\n\n  .dataset-meta {\n    flex-direction: column;\n    gap: 8px;\n  }\n}\n\n/* Suggested Questions Panel */\n.suggestions-panel {\n  position: absolute;\n  bottom: 75px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 600px;\n  background-color: #e8f4f8;\n  border-radius: 4px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  z-index: 100;\n}\n\n.suggestions-title {\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 14px;\n  color: #606266;\n}\n\n.suggestion-item {\n  padding: 10px 15px;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.suggestion-item:hover {\n  background-color: #f5f7fa;\n}\n\n/* Message list style - 瞬间位置切换 */\n.message-list {\n  width: 100%;\n  max-height: calc(100vh - 250px); /* 修改为响应式高度，减去头部和底部的高度 */\n  overflow-y: auto;\n  margin-top: 20px;\n  padding: 10px;\n  background-color: transparent;\n  border-radius: 8px;\n  flex: 1; /* 让消息列表占据剩余空间 */\n  transition: none !important; /* 强制移除所有过渡动画 */\n  position: relative;\n}\n\n/* 选择数据集后，消息列表瞬间移动到顶部位置 */\n.message-list.expanded-position {\n  margin-top: 0 !important; /* 瞬间移动到顶部 */\n  padding-top: 20px;\n  max-height: calc(100vh - 180px); /* 调整高度，为字段选择区域留空间 */\n}\n\n.message {\n  margin-bottom: 20px;\n  display: flex;\n  position: relative;\n  max-width: 80%;\n  margin-left: 150px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n}\n\n.message-content {\n  padding: 12px 15px;\n  border-radius: 6px;\n  line-height: 1.5;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  transform: translateZ(0);\n  position: relative;\n  isolation: isolate;\n  z-index: 1;\n}\n\n.user-message {\n  flex-direction: row-reverse;\n  align-self: flex-end;\n  margin-left: auto;\n}\n\n.user-message .message-content {\n  background-color: #ecf5ff;\n  margin-right: 0; /* 移除右边距，因为没有头像了 */\n}\n\n.bot-message {\n  align-self: flex-start;\n}\n\n.bot-message .message-content {\n  background-color: #f5f7fa;\n  margin-left: 10px;\n}\n\n.avatar-container {\n  display: flex;\n  align-items: flex-start;\n}\n\n.bot-avatar {\n  width: 60px; /* 增大宽度以容纳FastBI文字 */\n  height: 36px;\n  border-radius: 18px; /* 椭圆形：高度的一半 */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #ffffff; /* 白底 */\n  color: #333333; /* 黑字 */\n  border: 2px solid #e1e8ed; /* 添加边框 */\n  font-size: 12px; /* 稍大字体 */\n  font-weight: 600; /* 加粗 */\n  font-family: 'Arial', sans-serif; /* 清晰字体 */\n}\n\n/* 用户头像样式已移除 */\n\n/* Loading animation */\n.loading-dots {\n  display: inline-block;\n}\n\n.dot {\n  display: inline-block;\n  width: 6px;\n  height: 6px;\n  background-color: #999;\n  border-radius: 50%;\n  margin: 0 2px;\n  animation: pulse 1.2s infinite ease-in-out both;\n}\n\n.dot:nth-child(2) {\n  animation-delay: -0.4s;\n}\n\n.dot:nth-child(3) {\n  animation-delay: -0.8s;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(0.6);\n    opacity: 0.4;\n  }\n  50% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n\n/* Styles related to charts */\n.message-content .chart-container {\n  position: relative;\n  margin: 15px 0;\n  border: 1px solid #ebeef5;\n  border-radius: 8px;\n  padding: 10px;\n  background-color: #e8f4f8;\n  overflow: hidden;\n  z-index: 5;\n  box-sizing: border-box;\n  max-width: 100%; /* 确保不超出消息容器 */\n}\n\n/* 确保图表容器有足够高度 */\n.message-content .chart-canvas {\n  min-height: 300px;\n  position: relative;\n  z-index: 10;\n}\n\n.chart-notice {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n  font-weight: bold;\n}\n\n/* AI original response popup layer style */\n.raw-response-panel {\n  position: fixed;\n  bottom: 100px;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 90%;\n  max-width: 800px;\n  background-color: #e8f4f8;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n  z-index: 1000;\n  max-height: 60%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.raw-response-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 15px;\n  border-bottom: 1px solid #ebeef5;\n  font-size: 16px;\n  color: #303133;\n  background-color: #f5f7fa;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.raw-response-title .el-icon-monitor {\n  margin-right: 8px;\n}\n\n.raw-response-title .close-btn {\n  background-color: #f5f7fa;\n  border: 1px solid #ebeef5;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.raw-response-title .close-btn:hover {\n  background-color: #ebeef5;\n}\n\n.raw-response-content {\n  flex: 1;\n  padding: 15px;\n  font-size: 14px;\n  line-height: 1.6;\n  color: #303133;\n  white-space: pre-wrap;\n  word-break: break-all;\n  overflow-wrap: break-word;\n}\n\n/* 添加PDF导出相关样式 */\n.chart-actions {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n}\n\n/* 图表切换下拉选择器样式 */\n.chart-switcher-dropdown {\n  position: relative;\n  display: inline-block;\n}\n\n.chart-dropdown-wrapper {\n  position: relative;\n  cursor: pointer;\n}\n\n.chart-current-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 6px;\n  height: 28px;\n  padding: 0 12px;\n  border: 1px solid #dcdfe6;\n  border-radius: 4px;\n  background: #409eff;\n  color: #fff;\n  font-size: 12px;\n  line-height: 1;\n  white-space: nowrap;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-sizing: border-box;\n  min-width: 100px;\n}\n\n.chart-current-btn:hover {\n  background: #66b1ff;\n  border-color: #66b1ff;\n}\n\n.chart-dropdown-wrapper.open .chart-current-btn {\n  background: #66b1ff;\n  border-color: #66b1ff;\n}\n\n.chart-type-text {\n  font-size: 12px;\n  color: inherit;\n}\n\n.dropdown-arrow {\n  font-size: 10px;\n  color: inherit;\n  transition: transform 0.3s ease;\n}\n\n.chart-dropdown-wrapper.open .dropdown-arrow {\n  transform: rotate(180deg);\n}\n\n.chart-dropdown-options {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  background: #fff;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n  margin-top: 4px;\n  padding: 8px;\n  min-width: 120px;\n}\n\n.chart-type-grid {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: 8px;\n}\n\n.chart-type-btn {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 50px;\n  height: 50px;\n  border: 2px solid #dee2e6;\n  border-radius: 8px;\n  background: #fff;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.chart-type-btn:hover {\n  border-color: #409eff;\n  background: #ecf5ff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);\n}\n\n.chart-type-btn.active {\n  border-color: #409eff;\n  background: #409eff;\n  color: #fff;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n}\n\n.chart-icon-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n}\n\n/* 自定义图表图标样式 */\n.chart-icon-bar::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M7 17h2v-7H7v7zm4 0h2V7h-2v10zm4 0h2v-4h-2v4z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-icon-line::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-icon-pie::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M11 2v20c-5.07-.5-9-4.79-9-10s3.93-9.5 9-10zm2.03 0v8.99H22c-.47-4.74-4.24-8.52-8.97-8.99zm0 11.01V22c4.74-.47 8.5-4.25 8.97-8.99h-8.97z'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n.chart-icon-bar-horizontal::before {\n  content: '';\n  display: inline-block;\n  width: 24px;\n  height: 24px;\n  background: currentColor;\n  mask: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z' transform='rotate(90 12 12)'/%3E%3C/svg%3E\") no-repeat center;\n  mask-size: contain;\n}\n\n/* 数据限制提示样式 */\n.data-limit-notice {\n  margin-top: 10px;\n  padding: 8px;\n  background-color: #f0f9eb;\n  color: #67c23a;\n  border-radius: 4px;\n  font-size: 13px;\n  text-align: center;\n}\n\n/* 对话框浮动悬停效果 - 增强版 */\n.message:hover {\n  transform: translateY(-4px) scale(1.02);\n}\n\n.message:hover .message-content {\n  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.18) !important;\n}\n\n.user-message:hover .message-content {\n  box-shadow: 0 12px 35px rgba(64, 158, 255, 0.25) !important;\n  background-color: #e1f3ff !important;\n}\n\n.bot-message:hover .message-content {\n  box-shadow: 0 12px 35px rgba(103, 194, 58, 0.25) !important;\n  background-color: #f0f9eb !important;\n}\n\n/* 数据集详情标签页样式 - 简洁版本 */\n.detail-tabs {\n  margin-top: 15px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n}\n\n.dataset-tabs {\n  background-color: transparent;\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.dataset-tabs .el-tabs__content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n}\n\n.dataset-tabs .el-tab-pane {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 0;\n}\n\n.dataset-tabs .el-tabs__header {\n  margin: 0 0 20px 0;\n  border-bottom: none;\n}\n\n.dataset-tabs .el-tabs__nav {\n  border: none;\n}\n\n.dataset-tabs .el-tabs__item {\n  height: 40px;\n  line-height: 40px;\n  padding: 0 20px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #909399;\n  border: none;\n  margin-right: 0;\n  border-radius: 0;\n  background-color: transparent;\n  transition: all 0.3s ease;\n}\n\n.dataset-tabs .el-tabs__item:hover {\n  color: #409eff;\n}\n\n.dataset-tabs .el-tabs__item.is-active {\n  color: #409eff;\n  background-color: transparent;\n  font-weight: 600;\n}\n\n.dataset-tabs .el-tabs__content {\n  padding: 0;\n  background-color: transparent;\n  border: none;\n  box-shadow: none;\n}\n\n/* 移除标签页最小高度限制，让内容自然展示 */\n\n/* 字段信息区域样式优化 */\n.fields-section .section-header {\n  margin-bottom: 12px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.fields-section .field-stats {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n  font-size: 14px;\n}\n\n/* 数据预览区域样式优化 */\n.preview-section .section-header {\n  margin-bottom: 12px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #ebeef5;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.preview-section .preview-stats {\n  color: #909399;\n  font-size: 15px;\n}\n\n.preview-section .preview-table {\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n/* 标签页图标样式 */\n.dataset-tabs .el-tabs__item i {\n  margin-right: 8px;\n  font-size: 16px;\n}\n\n/* 紧凑型字段选择区域样式 */\n.field-selection-compact {\n  background: #f8f9fa;\n  border: 1px solid #e9ecef;\n  border-radius: 8px;\n  padding: 12px 16px;\n  margin-bottom: 12px;\n  width: 100%;\n  max-width: 800px;\n}\n\n.field-row {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 8px;\n  gap: 12px;\n}\n\n.field-row:last-child {\n  margin-bottom: 0;\n}\n\n.field-row-label {\n  font-size: 14px;\n  font-weight: 500;\n  color: #606266;\n  min-width: 80px;\n  line-height: 24px;\n  flex-shrink: 0;\n}\n\n.field-tags-inline {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n  flex: 1;\n}\n\n.dataset-info-inline {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  flex: 1;\n}\n\n.dataset-name-compact {\n  background: #409eff;\n  color: white;\n  padding: 2px 8px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.switch-btn-compact {\n  color: #409eff;\n  padding: 2px 4px;\n  font-size: 12px;\n}\n\n.switch-btn-compact:hover {\n  background: rgba(64, 158, 255, 0.1);\n}\n\n/* 超紧凑字段选择区域样式 - 对称布局 */\n.field-selection-mini {\n  background: #fff;\n  border: 2px solid #e1e8ed;\n  border-radius: 32px 32px 0 0; /* 增大10%: 29px * 1.1 ≈ 32px */\n  border-bottom: none;\n  padding: 17px 26px 11px 26px; /* 增大10%: 15px * 1.1 ≈ 17px, 24px * 1.1 ≈ 26px, 10px * 1.1 ≈ 11px */\n  margin-bottom: 0;\n  width: 100%;\n  max-width: 715px; /* 与输入框宽度保持一致: 715px */\n  margin-left: auto;\n  margin-right: auto;\n  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.015);\n  position: relative;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  gap: 8px; /* 行间距 */\n  z-index: 2; /* 确保在特效之上 */\n}\n\n.field-selection-mini::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  transition: left 0.6s ease;\n}\n\n.field-selection-mini:hover::before {\n  left: 100%;\n}\n\n.field-mini-row {\n  display: flex;\n  align-items: center;\n  justify-content: flex-start; /* 左对齐 */\n  margin-bottom: 0; /* 移除底部边距，使用父容器的gap */\n  gap: 12px; /* 增大间距: 8px * 1.5 = 12px */\n  width: 100%;\n}\n\n.field-mini-row:last-child {\n  margin-bottom: 0;\n}\n\n.field-mini-label {\n  font-size: 14px; /* 增大字体: 12px * 1.17 ≈ 14px */\n  font-weight: 600; /* 加粗字体 */\n  color: #606266;\n  min-width: 80px; /* 增大标签宽度: 60px * 1.33 ≈ 80px */\n  flex-shrink: 0;\n  text-align: left; /* 标签文字左对齐 */\n}\n\n.field-tags-mini {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px; /* 增大间距: 4px * 1.5 = 6px */\n  flex: 1;\n  align-items: center;\n  justify-content: flex-start; /* 标签左对齐 */\n  overflow: hidden;\n  width: 100%;\n  max-width: 650px; /* 保持与输入框内部输入区域的宽度一致 */\n}\n\n.more-fields {\n  font-size: 11px;\n  color: #909399;\n  margin-left: 4px;\n}\n\n/* 数据集显示样式已移除 */\n\n/* 切换按钮样式已移除，使用\"选择数据\"按钮代替 */\n\n/* 过渡动画样式 */\n\n/* 欢迎区域淡入淡出动画 - 加速消失 */\n.welcome-fade-enter-active {\n  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.welcome-fade-leave-active {\n  transition: all 0.5s cubic-bezier(0.6, 0, 0.4, 1);\n}\n\n.welcome-fade-enter, .welcome-fade-leave-to {\n  opacity: 0;\n  transform: translateY(-30px) scale(0.95);\n}\n\n.welcome-fade-enter-to, .welcome-fade-leave {\n  opacity: 1;\n  transform: translateY(0) scale(1);\n}\n\n/* 数据集卡片滑动动画 - 加速消失 */\n.datasets-slide-enter-active {\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.datasets-slide-leave-active {\n  transition: all 0.4s cubic-bezier(0.6, 0, 0.4, 1);\n}\n\n.datasets-slide-enter, .datasets-slide-leave-to {\n  opacity: 0;\n  transform: translateY(50px);\n}\n\n.datasets-slide-enter-to, .datasets-slide-leave {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n/* 单行数据集布局 - 允许特效溢出 */\n.datasets-single-row {\n  width: 100%;\n  overflow: visible; /* 允许卡片特效溢出 */\n  padding: 10px 0; /* 为特效留出空间 */\n}\n\n.card-container-single-row {\n  display: flex;\n  flex-wrap: nowrap;\n  gap: 20px;\n  justify-content: center;\n  align-items: stretch;\n  max-width: 1200px;\n  margin: 0 auto;\n  overflow: visible; /* 确保容器也允许溢出 */\n}\n\n.data-card-wrapper {\n  flex: 1;\n  max-width: 320px;\n  min-width: 280px;\n  position: relative; /* 为特效定位 */\n  z-index: 1; /* 确保在正确层级 */\n}\n\n/* 数据卡片错位动画 */\n.card-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20px;\n}\n\n.card-stagger-enter-active, .card-stagger-leave-active {\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.card-stagger-enter, .card-stagger-leave-to {\n  opacity: 0;\n  transform: translateY(30px) scale(0.9);\n}\n\n.card-stagger-enter-to, .card-stagger-leave {\n  opacity: 1;\n  transform: translateY(0) scale(1);\n}\n\n/* 字段选择区域滑入动画 */\n.field-selection-slide-enter-active {\n  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.field-selection-slide-leave-active {\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.field-selection-slide-enter {\n  opacity: 0;\n  transform: translateY(-40px) scale(0.9);\n}\n\n.field-selection-slide-leave-to {\n  opacity: 0;\n  transform: translateY(-20px) scale(0.95);\n}\n\n.field-selection-slide-enter-to, .field-selection-slide-leave {\n  opacity: 1;\n  transform: translateY(0) scale(1);\n}\n\n/* 字段行淡入动画 */\n.field-row-fade-enter-active {\n  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.field-row-fade-enter {\n  opacity: 0;\n  transform: translateX(-30px);\n}\n\n.field-row-fade-enter-to {\n  opacity: 1;\n  transform: translateX(0);\n}\n\n/* 字段标签容器 */\n.field-tags-container {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px; /* 增大间距: 4px * 1.5 = 6px */\n  align-items: center;\n  justify-content: flex-start; /* 标签左对齐 */\n  width: 100%;\n}\n\n/* 字段标签错位动画 */\n.field-tag-stagger-enter-active {\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.field-tag-stagger-leave-active {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.field-tag-stagger-enter {\n  opacity: 0;\n  transform: translateY(-10px) scale(0.8);\n}\n\n.field-tag-stagger-leave-to {\n  opacity: 0;\n  transform: translateY(10px) scale(0.8);\n}\n\n.field-tag-stagger-enter-to, .field-tag-stagger-leave {\n  opacity: 1;\n  transform: translateY(0) scale(1);\n}\n\n/* 提示文字淡入淡出动画 */\n.hint-fade-enter-active, .hint-fade-leave-active {\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.hint-fade-enter, .hint-fade-leave-to {\n  opacity: 0;\n  transform: translateY(20px);\n}\n\n.hint-fade-enter-to, .hint-fade-leave {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n/* 增强字段选择区域的入场特效 */\n.field-selection-mini {\n  animation: fieldSelectionGlow 1s ease-out;\n}\n\n@keyframes fieldSelectionGlow {\n  0% {\n    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\n  }\n  50% {\n    box-shadow: 0 -4px 20px rgba(64, 158, 255, 0.2), 0 -2px 8px rgba(0, 0, 0, 0.1);\n  }\n  100% {\n    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);\n  }\n}\n\n/* 重复样式已合并到上面的 .message-list 中 */\n\n.field-selection-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.dataset-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.dataset-label {\n  font-size: 14px;\n  color: #666;\n  font-weight: 500;\n}\n\n.dataset-name {\n  background: #409eff;\n  color: white;\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.switch-btn {\n  color: #409eff;\n  padding: 4px 8px;\n}\n\n.switch-btn:hover {\n  background: rgba(64, 158, 255, 0.1);\n}\n\n.field-categories {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.field-category {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.category-header {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.category-label {\n  font-size: 14px;\n  font-weight: 600;\n  color: #2c3e50;\n}\n\n.field-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n}\n\n/* 紧凑型字段标签样式 */\n.field-tag-compact {\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border: 1px solid transparent;\n  font-weight: 500;\n  margin: 0;\n}\n\n.field-tag-compact:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n/* 指标字段样式 - 绿色 */\n.field-tag-compact.indicator-tag {\n  background: #f0f9ff;\n  color: #059669;\n  border-color: #10b981;\n}\n\n.field-tag-compact.indicator-tag:hover {\n  background: #ecfdf5;\n  border-color: #059669;\n}\n\n.field-tag-compact.indicator-tag.selected {\n  background: #10b981;\n  color: white;\n  border-color: #059669;\n}\n\n/* 维度字段样式 - 蓝色 */\n.field-tag-compact.dimension-tag {\n  background: #eff6ff;\n  color: #2563eb;\n  border-color: #3b82f6;\n}\n\n.field-tag-compact.dimension-tag:hover {\n  background: #dbeafe;\n  border-color: #2563eb;\n}\n\n.field-tag-compact.dimension-tag.selected {\n  background: #3b82f6;\n  color: white;\n  border-color: #2563eb;\n}\n\n/* 超紧凑字段标签样式 - 增大字体和尺寸 */\n.field-tag-mini {\n  cursor: pointer;\n  transition: all 0.2s ease;\n  border: 1px solid transparent;\n  font-weight: 500;\n  margin: 0;\n  font-size: 13px !important; /* 增大字体: 11px * 1.18 ≈ 13px */\n  height: 24px !important; /* 增大高度: 20px * 1.2 = 24px */\n  line-height: 22px !important; /* 增大行高: 18px * 1.22 ≈ 22px */\n  padding: 0 8px !important; /* 增大内边距: 6px * 1.33 ≈ 8px */\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 140px; /* 增大最大宽度: 120px * 1.17 ≈ 140px */\n  flex-shrink: 1;\n}\n\n.field-tag-mini:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n/* 超紧凑指标字段样式 - 绿色 */\n.field-tag-mini.indicator-tag {\n  background: #f0f9ff;\n  color: #059669;\n  border-color: #10b981;\n}\n\n.field-tag-mini.indicator-tag:hover {\n  background: #ecfdf5;\n  border-color: #059669;\n}\n\n.field-tag-mini.indicator-tag.selected {\n  background: #10b981;\n  color: white;\n  border-color: #059669;\n}\n\n/* 超紧凑维度字段样式 - 蓝色 */\n.field-tag-mini.dimension-tag {\n  background: #eff6ff;\n  color: #2563eb;\n  border-color: #3b82f6;\n}\n\n.field-tag-mini.dimension-tag:hover {\n  background: #dbeafe;\n  border-color: #2563eb;\n}\n\n.field-tag-mini.dimension-tag.selected {\n  background: #3b82f6;\n  color: white;\n  border-color: #2563eb;\n}\n\n/* 头像悬停效果 */\n.message:hover .bot-avatar {\n  transform: scale(1.1);\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15); /* 调整为适合白底的阴影 */\n}\n\n/* 用户头像悬停效果已移除 */\n\n.bot-avatar {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.key-metrics, .key-dimensions {\n  width: 100%;\n  max-width: 650px; /* 统一组件宽度 */\n  margin: 0 auto;\n}\n\n/* 优化图表在消息流中的显示 */\n.chart-container {\n  border-radius: 8px;\n  margin-top: 12px;\n  margin-bottom: 12px;\n  overflow: hidden;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);\n  background-color: #fff;\n  position: relative;\n  display: block;\n  clear: both;\n}\n\n/* 修复消息中图表的层级问题 */\n.message .chart-container {\n  position: relative;\n  z-index: 10;\n  transform: translateZ(0);\n  will-change: transform;\n  overflow: visible !important;\n}\n</style>"], "mappings": ";;;;;;;;;;;;;AAojBA,SAAAA,OAAA;AACA,SAAAC,EAAA,IAAAC,MAAA;AACA,OAAAC,KAAA;AACA,OAAAC,YAAA;AACA;AACA,OAAAC,WAAA;AACA,SAAAC,KAAA;AACA,YAAAC,OAAA;AACA;;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,WAAA;MACAC,SAAA;MACAC,MAAA;MAAA;MACAC,QAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,aAAA;MACAC,aAAA;MACAC,QAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,eAAA;MACAC,aAAA;MACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;MACAC,cAAA;MACAC,oBAAA;MAAA;MACAC,eAAA;MAAA;MACAC,MAAA;MAAA;MACAC,SAAA;MAAA;MACAC,oBAAA;MACAC,aAAA;MACAC,WAAA;MACAC,QAAA;MAAA;MACAC,sBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,WAAA;MACAC,SAAA;MACAC,YAAA;MAAA;MACAC,SAAA;MAAA;MACAC,kBAAA;MAAA;MACA;MACAC,WAAA;MAAA;MACAC,QAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,gBAAA;MACA,UAAAX,sBAAA,UAAAA,sBAAA,CAAAY,MAAA;QACA;MACA;MACA,YAAAZ,sBAAA,CAAAY,MAAA,CAAAC,MAAA,CAAAC,KAAA,IACAA,KAAA,CAAAC,MAAA;MAAA;MACAD,KAAA,CAAAE,SAAA;MAAA;MACA,wDAAAC,QAAA,CAAAH,KAAA,CAAAI,IAAA,EAAAC,WAAA,GACA;IACA;IAEA;IACAC,gBAAA;MACA,UAAApB,sBAAA,UAAAA,sBAAA,CAAAY,MAAA;QACA;MACA;MACA,YAAAZ,sBAAA,CAAAY,MAAA,CAAAC,MAAA,CAAAC,KAAA,IACAA,KAAA,CAAAC,MAAA;MAAA;MACAD,KAAA,CAAAE,SAAA;MAAA;MACA,+DAAAC,QAAA,CAAAH,KAAA,CAAAI,IAAA,EAAAC,WAAA,GACA;IACA;IAEA;IACAE,eAAA;MACA,UAAAd,kBAAA;QACA,YAAAV,aAAA;MACA;MACA,YAAAA,aAAA,CAAAgB,MAAA,CAAAC,KAAA,IACAA,KAAA,CAAA3C,IAAA,CAAAmD,WAAA,GAAAL,QAAA,MAAAV,kBAAA,CAAAe,WAAA,OACAR,KAAA,CAAAS,YAAA,IAAAT,KAAA,CAAAS,YAAA,CAAAD,WAAA,GAAAL,QAAA,MAAAV,kBAAA,CAAAe,WAAA,GACA;IACA;IAEA;IACAE,qBAAA;MACA,YAAA3B,aAAA,CAAAgB,MAAA,CAAAC,KAAA,IAAAA,KAAA,CAAAE,SAAA,UAAAS,MAAA;IACA;IAEA;IACAC,mBAAA;MACA,YAAA7B,aAAA,CAAAgB,MAAA,CAAAC,KAAA,IAAAA,KAAA,CAAAE,SAAA,UAAAS,MAAA;IACA;IAEA;IACAE,aAAA;MACA,OAAAC,IAAA,CAAAC,GAAA,MAAA/B,WAAA,CAAA2B,MAAA;IACA;IAEAK,gBAAA;MACA,MAAAC,KAAA,SAAAvB,WAAA,aAAAC,QAAA;MACA,MAAAuB,GAAA,GAAAD,KAAA,QAAAtB,QAAA;MACA,YAAAX,WAAA,CAAAmC,KAAA,CAAAF,KAAA,EAAAH,IAAA,CAAAC,GAAA,CAAAG,GAAA;IACA;EACA;EAEAE,QAAA;IACA;IACAC,YAAA,CAAAC,OAAA,kBAAArC,QAAA;IACA,KAAAsC,UAAA;IACA,KAAAC,YAAA;IACA,KAAAC,iBAAA;IACA;IACA,KAAAvD,kBAAA,IACA,gBACA,kBACA,kBACA,mBACA;IACA;IACAwD,QAAA,CAAAC,gBAAA,eAAAC,kBAAA;EACA;EAEAC,cAAA;IACA;IACAH,QAAA,CAAAI,mBAAA,eAAAF,kBAAA;EACA;EACAG,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAC,mBAAAC,cAAA;MACAC,OAAA,CAAAC,GAAA,aAAAF,cAAA;;MAEA;MACA,SAAAG,OAAA,SAAAhE,QAAA;QACA,IAAAgE,OAAA,CAAAC,WAAA,IAAAD,OAAA,CAAAC,WAAA,CAAAC,EAAA,KAAAL,cAAA,CAAAK,EAAA;UACAF,OAAA,CAAAC,WAAA,GAAAJ,cAAA;UACA;QACA;MACA;IACA;IAEAM,eAAA;MACA,KAAAlB,UAAA;MACA,KAAA3C,MAAA;IACA;IACA;IACA4C,aAAA;MACA,IAAAkB,cAAA,GAAArB,YAAA,CAAAsB,OAAA;MACA,KAAAD,cAAA;QACAA,cAAA,QAAAE,YAAA,CAAA7F,MAAA;QACAsE,YAAA,CAAAC,OAAA,mBAAAoB,cAAA;MACA;MACA,KAAAnE,QAAA,GAAAmE,cAAA;IACA;IAEA;IACAE,aAAAC,IAAA;MACA,IAAAC,MAAA;MACA,SAAAC,CAAA,MAAAA,CAAA,GAAAF,IAAA,CAAAlC,MAAA,IAAAoC,CAAA,MAAAA,CAAA;QACA,MAAAC,QAAA,GAAAH,IAAA,CAAAE,CAAA;QACAD,MAAA,GAAAA,MAAA,SAAAG,QAAA,CAAAD,QAAA;MACA;MACA,OAAAF,MAAA;IACA;IAEA;IACArB,kBAAA;MACA,KAAAnD,QAAA,CAAA4E,IAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IAEA;IACAC,oBAAA;MACA;MACA,KAAAhF,QAAA,QAAAA,QAAA,CAAAyB,MAAA,CAAAuC,OAAA,IACAA,OAAA,CAAAa,MAAA,KAAAb,OAAA,CAAAc,OAAA,CAAAjD,QAAA,aACA;IACA;IAEA;IACA6B,eAAA;MACA,SAAAuB,KAAA,CAAA9E,cAAA;QACA,KAAA8E,KAAA,CAAA9E,cAAA,CAAA+E,SAAA,QAAAD,KAAA,CAAA9E,cAAA,CAAAgF,YAAA;MACA;IACA;IAEA,MAAAlC,WAAA;MACA;QACA,MAAAmC,GAAA,SAAA7G,OAAA,CAAA8G,YAAA;QACA,IAAAD,GAAA,CAAAnG,IAAA,IAAAmG,GAAA,CAAAnG,IAAA,CAAAqG,IAAA;UACA,KAAAlG,MAAA,GAAAgG,GAAA,CAAAnG,IAAA,CAAAA,IAAA;UACA,KAAAI,QAAA,QAAAkG,eAAA,MAAAnG,MAAA;UACA,KAAAE,gBAAA,QAAAD,QAAA;QACA;UACA,KAAAD,MAAA;UACA,KAAAC,QAAA;UACA,KAAAC,gBAAA;QACA;MACA,SAAAkG,CAAA;QACA,KAAApG,MAAA;QACA,KAAAC,QAAA;QACA,KAAAC,gBAAA;MACA;IACA;IACA;IACAiG,gBAAAE,IAAA;MACA,IAAAC,MAAA;MACA,WAAAC,IAAA,IAAAF,IAAA;QACA,IAAAE,IAAA,CAAAC,IAAA;UACAF,MAAA,CAAAd,IAAA,CAAAe,IAAA;QACA,WAAAA,IAAA,CAAAE,QAAA,IAAAF,IAAA,CAAAE,QAAA,CAAAxD,MAAA;UACAqD,MAAA,GAAAA,MAAA,CAAAI,MAAA,MAAAP,eAAA,CAAAI,IAAA,CAAAE,QAAA;QACA;MACA;MACA,OAAAH,MAAA;IACA;IACA;IACAK,gBAAA;MACA,MAAAC,OAAA,QAAAzG,aAAA,CAAA0G,IAAA,GAAA/D,WAAA;MACA,KAAA8D,OAAA;QACA,KAAA1G,gBAAA,QAAAD,QAAA;MACA;QACA,KAAAC,gBAAA,QAAAD,QAAA,CAAAoC,MAAA,CAAAyE,EAAA,IAAAA,EAAA,CAAAnH,IAAA,IAAAmH,EAAA,CAAAnH,IAAA,CAAAmD,WAAA,GAAAL,QAAA,CAAAmE,OAAA;MACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACAG,eAAAC,OAAA;MACA;MACA,IAAAA,OAAA;QACA,KAAAC,iBAAA,CAAAD,OAAA;QACA;QACA,KAAAE,SAAA;UACA,KAAAC,eAAA;QACA;QACA;MACA;MAEA,KAAAA,eAAA;IACA;IAEA;IACAC,YAAA;MACA;MACA,KAAA5F,sBAAA;MACA,KAAAJ,oBAAA;MACA,KAAAC,aAAA;MACA,KAAAC,WAAA;;MAEA;MACA,KAAAX,aAAA;MACA,KAAA0G,aAAA;;MAEA;MACA;;MAEA;MACA,KAAAvF,SAAA;MACA,KAAAE,WAAA;MACA,KAAAP,kBAAA;MACA,KAAAC,kBAAA;;MAEA;MACA,KAAA4F,kBAAA;;MAEA;MACA,KAAAC,QAAA,CAAAC,OAAA;IACA;IAEA;IACAF,mBAAA;MACA;MACA,SAAA1G,QAAA,CAAAqC,MAAA,eAAArC,QAAA,IAAA8B,IAAA,oBAAA9B,QAAA,IAAA8E,OAAA,CAAAjD,QAAA;QACA,KAAA7B,QAAA,CAAA6G,OAAA;UACA/E,IAAA;UACAgD,OAAA;UACAgC,SAAA,MAAAC,IAAA,GAAAC,kBAAA;QACA;MACA;IACA;IAEA;IACA,MAAAC,oBAAAb,OAAA;MACA;MACA,MAAAc,OAAA;QACA,mBAAAvG,QAAA;QACA;QACA;QACA;MACA;MAEA;QACA;QACA,MAAAyE,GAAA,SAAA7G,OAAA,CAAA4I,gBAAA,CAAAf,OAAA,CAAAlC,EAAA,SAAAgD,OAAA;QAEA,IAAA9B,GAAA,CAAAnG,IAAA,IAAAmG,GAAA,CAAAnG,IAAA,CAAAqG,IAAA;UACA,MAAA8B,MAAA,GAAAhC,GAAA,CAAAnG,IAAA,CAAAA,IAAA;UACA;UACA,MAAAuC,MAAA,GAAA4F,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAnI,IAAA,IAAAmI,MAAA,CAAAnI,IAAA,CAAAuC,MAAA;;UAEA;UACA,MAAA8F,UAAA,GAAA9F,MAAA,CAAA+F,GAAA,CAAA7F,KAAA;YACA;cACAwC,EAAA,EAAAxC,KAAA,CAAAwC,EAAA;cACAsD,UAAA,EAAA9F,KAAA,CAAA8F,UAAA;cACAzI,IAAA,EAAA2C,KAAA,CAAA3C,IAAA;cACAoD,YAAA,EAAAT,KAAA,CAAAS,YAAA;cACAP,SAAA,EAAAF,KAAA,CAAAE,SAAA;cACAE,IAAA,EAAAJ,KAAA,CAAAI,IAAA;cACA2F,YAAA,EAAA/F,KAAA,CAAA+F,YAAA;cACAC,cAAA,EAAAhG,KAAA,CAAAgG,cAAA;cACAC,cAAA,EAAAjG,KAAA,CAAAiG,cAAA;YACA;UACA;;UAEA;UACA,KAAA/G,sBAAA;YACAsD,EAAA,EAAAkC,OAAA,CAAAlC,EAAA;YACAnF,IAAA,EAAAqH,OAAA,CAAArH,IAAA;YACAyC,MAAA,EAAA8F;UACA;;UAEA;UACA,KAAAtC,mBAAA;;UAEA;UACA,KAAA2B,QAAA,CAAAC,OAAA,WAAAR,OAAA,CAAArH,IAAA;;UAEA;UACA,KAAAuH,SAAA;YACA,MAAAsB,OAAA,GAAAxE,QAAA,CAAAyE,aAAA;YACA,IAAAD,OAAA,EAAAA,OAAA,CAAAE,KAAA;UACA;QACA;UACA,KAAAnB,QAAA,CAAAoB,KAAA;QACA;MACA,SAAAA,KAAA;QACAjE,OAAA,CAAAiE,KAAA,eAAAA,KAAA;QACA,KAAApB,QAAA,CAAAoB,KAAA;MACA;IACA;IAEA;IACAxB,gBAAA;MACAzC,OAAA,CAAAC,GAAA;MACAD,OAAA,CAAAC,GAAA,+BAAAvD,oBAAA;MACAsD,OAAA,CAAAC,GAAA,wBAAAtD,aAAA;MAEA,UAAAD,oBAAA,UAAAC,aAAA,CAAA4B,MAAA;QACAyB,OAAA,CAAAC,GAAA;QACA,KAAA4C,QAAA,CAAAqB,OAAA;QACA;MACA;;MAEA;MACA,MAAAV,UAAA,QAAA7G,aAAA,CAAA8G,GAAA,CAAA7F,KAAA;QACA;QACA;UACAwC,EAAA,EAAAxC,KAAA,CAAAwC,EAAA;UACAsD,UAAA,EAAA9F,KAAA,CAAA8F,UAAA;UACAzI,IAAA,EAAA2C,KAAA,CAAA3C,IAAA;UACAoD,YAAA,EAAAT,KAAA,CAAAS,YAAA;UACAP,SAAA,EAAAF,KAAA,CAAAE,SAAA;UACAE,IAAA,EAAAJ,KAAA,CAAAI,IAAA;UACA2F,YAAA,EAAA/F,KAAA,CAAA+F,YAAA;UACAC,cAAA,EAAAhG,KAAA,CAAAgG,cAAA;UACAC,cAAA,EAAAjG,KAAA,CAAAiG,cAAA;QACA;MACA;;MAEA;MACA,KAAA/G,sBAAA;QACAsD,EAAA,OAAA1D,oBAAA,CAAA0D,EAAA;QACAnF,IAAA,OAAAyB,oBAAA,CAAAzB,IAAA;QACAyC,MAAA,EAAA8F;MACA;;MAEA;MACA,KAAAtC,mBAAA;;MAEA;MACA,KAAAjF,aAAA;;MAEA;MACA,KAAA4G,QAAA,CAAAC,OAAA,gBAAApG,oBAAA,CAAAzB,IAAA;;MAEA;MACA,KAAAuH,SAAA;QACA,MAAAsB,OAAA,GAAAxE,QAAA,CAAAyE,aAAA;QACA,IAAAD,OAAA,EAAAA,OAAA,CAAAE,KAAA;MACA;IACA;IAEA;IACAG,qBAAAnG,IAAA,EAAAJ,KAAA;MACA,IAAAI,IAAA;QACA,MAAAoG,KAAA,QAAArH,kBAAA,CAAAsH,OAAA,CAAAzG,KAAA,CAAAwC,EAAA;QACA,IAAAgE,KAAA;UACA,KAAArH,kBAAA,CAAAuH,MAAA,CAAAF,KAAA;QACA;UACA,KAAArH,kBAAA,CAAA+D,IAAA,CAAAlD,KAAA,CAAAwC,EAAA;QACA;MACA,WAAApC,IAAA;QACA,MAAAoG,KAAA,QAAApH,kBAAA,CAAAqH,OAAA,CAAAzG,KAAA,CAAAwC,EAAA;QACA,IAAAgE,KAAA;UACA,KAAApH,kBAAA,CAAAsH,MAAA,CAAAF,KAAA;QACA;UACA,KAAApH,kBAAA,CAAA8D,IAAA,CAAAlD,KAAA,CAAAwC,EAAA;QACA;MACA;IACA;IAEA;IACAmE,cAAA;MACA,KAAAzH,sBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,kBAAA;MACA,KAAA6F,QAAA,CAAA2B,IAAA;IACA;IAEA,MAAAjC,kBAAAD,OAAA;MACA;MACAtC,OAAA,CAAAC,GAAA,8BAAAqC,OAAA;;MAEA;MACA,KAAAA,OAAA,KAAAA,OAAA,CAAAlC,EAAA;QACAJ,OAAA,CAAAiE,KAAA,cAAA3B,OAAA;QACA;QACA;MACA;;MAEA;MACA,MAAAc,OAAA;QACA,mBAAAvG,QAAA;QACA;QACA;QACA;MACA;MAEA;QACAmD,OAAA,CAAAC,GAAA,kBAAAqC,OAAA,CAAAlC,EAAA;QACA,MAAAkB,GAAA,SAAA7G,OAAA,CAAA4I,gBAAA,CAAAf,OAAA,CAAAlC,EAAA,SAAAgD,OAAA;QACApD,OAAA,CAAAC,GAAA,gBAAAqB,GAAA;QAEA,IAAAA,GAAA,CAAAnG,IAAA,IAAAmG,GAAA,CAAAnG,IAAA,CAAAqG,IAAA;UACA,MAAA8B,MAAA,GAAAhC,GAAA,CAAAnG,IAAA,CAAAA,IAAA;UACA6E,OAAA,CAAAC,GAAA,aAAAqD,MAAA;UACA,KAAA5G,oBAAA,GAAA4G,MAAA;UACA;UACA,KAAA3G,aAAA,GAAA2G,MAAA,CAAAC,SAAA,IAAAD,MAAA,CAAAnI,IAAA,IAAAmI,MAAA,CAAAnI,IAAA,CAAAuC,MAAA;UACA;UACA,KAAAd,WAAA,GAAA0G,MAAA,CAAAnI,IAAA,IAAAmI,MAAA,CAAAnI,IAAA,CAAAA,IAAA;UACA;UACA,KAAAiC,SAAA;UACA;UACA,KAAAE,WAAA;UACA;UACA0C,OAAA,CAAAC,GAAA,eAAAtD,aAAA,CAAA4B,MAAA;UACAyB,OAAA,CAAAC,GAAA,eAAArD,WAAA,CAAA2B,MAAA;UAEA,SAAA5B,aAAA,CAAA4B,MAAA;YACAyB,OAAA,CAAAyE,IAAA;UACA;UAEA,SAAA7H,WAAA,CAAA2B,MAAA;YACAyB,OAAA,CAAAyE,IAAA;UACA;QACA;UACAzE,OAAA,CAAAiE,KAAA,aAAA3C,GAAA,CAAAnG,IAAA;UACAuJ,KAAA,aAAApD,GAAA,CAAAnG,IAAA,CAAAwJ,GAAA;UACA,KAAAjI,oBAAA;UACA,KAAAC,aAAA;UACA,KAAAC,WAAA;UACA,KAAAQ,SAAA;QACA;QACA,KAAAnB,aAAA;MACA,SAAAyF,CAAA;QACA1B,OAAA,CAAAiE,KAAA,eAAAvC,CAAA;QACAgD,KAAA,UAAAhD,CAAA,CAAAxB,OAAA;QACA,KAAAxD,oBAAA;QACA,KAAAC,aAAA;QACA,KAAAC,WAAA;QACA,KAAAX,aAAA;MACA;IACA;IACA2I,gBAAA;MACA,KAAA/I,oBAAA,SAAAA,oBAAA;IACA;IACAgJ,YAAAC,CAAA;MACA,KAAAnJ,QAAA,GAAAmJ,CAAA;MACA,KAAAjJ,oBAAA;IACA;IACAkJ,eAAAC,KAAA;MACA,IAAAA,KAAA,CAAAC,SAAA;QACA;MACA;MACA;IACA;IAEA;IACA,MAAAC,eAAA;MACA,UAAAvJ,QAAA,CAAAwG,IAAA,WAAA/F,SAAA;QACA,KAAAyG,QAAA,CAAAqB,OAAA;QACA;MACA;;MAEA;MACA;MACA,IAAAiB,SAAA,QAAAxJ,QAAA,CAAAwG,IAAA;;MAEA;MACA,SAAArF,sBAAA;QACA;QACA,MAAAsI,WAAA;UACAC,SAAA,OAAAvI,sBAAA,CAAAsD,EAAA;UACAkF,WAAA,OAAAxI,sBAAA,CAAA7B,IAAA;UACAyC,MAAA,OAAAZ,sBAAA,CAAAY;QACA;;QAEA;QACAyH,SAAA,GAAAI,IAAA,CAAAC,SAAA;UACA7J,QAAA,OAAAA,QAAA,CAAAwG,IAAA;UACAG,OAAA,EAAA8C;QACA;MACA;;MAEA;MACA,MAAAK,OAAA;QACA1E,MAAA;QACAC,OAAA,OAAArF,QAAA,CAAAwG,IAAA;QACAlB,QAAA;MACA;MACA,KAAA/E,QAAA,CAAA4E,IAAA,CAAA2E,OAAA;;MAEA;MACA,MAAAC,MAAA;QACA3E,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA,KAAA/E,QAAA,CAAA4E,IAAA,CAAA4E,MAAA;;MAEA;MACA,MAAAC,OAAA,QAAAzJ,QAAA,MAAAA,QAAA,CAAAqC,MAAA;;MAEA;MACA,MAAA5C,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA;MACA,KAAAS,SAAA;MAEA;QACA;QACA,MAAA8D,OAAA,QAAApD,sBAAA,GACAqI,SAAA,GACA,GAAAxJ,QAAA;;QAEA;QACA,MAAAf,KAAA,CAAAgL,IAAA,CACA,4CACA;UAAAzJ,QAAA,OAAAA,QAAA;UAAA+D;QAAA,GACA;UACA2F,YAAA;UACAC,kBAAA,EAAApE,CAAA;YACA,MAAAqE,QAAA,GAAArE,CAAA,CAAAsE,KAAA,CAAAC,MAAA,CAAAC,YAAA;YACA,IAAAC,OAAA,GAAAJ,QAAA,CAAAK,SAAA,CAAAT,OAAA,CAAA3E,OAAA,CAAAzC,MAAA;YACAoH,OAAA,CAAA3E,OAAA,IAAAmF,OAAA;YACA,KAAAvG,cAAA;;YAEA;YACA,KAAArD,eAAA,GAAAwJ,QAAA;UACA;QACA,CACA;;QAEA;QACAJ,OAAA,CAAA1E,QAAA;QACA,KAAA7E,SAAA;;QAEA;QACA,KAAAiK,gBAAA,CAAAV,OAAA;MACA,SAAA1B,KAAA;QACAjE,OAAA,CAAAiE,KAAA,UAAAA,KAAA;QACA0B,OAAA,CAAA3E,OAAA;QACA2E,OAAA,CAAA1E,QAAA;QACA,KAAA7E,SAAA;MACA;IACA;IAEA;IACAiK,iBAAAnG,OAAA;MACAF,OAAA,CAAAC,GAAA,qBAAAC,OAAA,CAAAc,OAAA;;MAEA;MACA,MAAAsF,gBAAA,GAAApG,OAAA,CAAAc,OAAA,CAAAuF,KAAA;MACA,IAAAD,gBAAA,IAAAA,gBAAA;QACA,MAAAE,WAAA,GAAAF,gBAAA;QACAtG,OAAA,CAAAC,GAAA,cAAAuG,WAAA;;QAEA;QACA,KAAAC,kBAAA,CAAAD,WAAA,EAAAtG,OAAA;QACA;MACA;;MAEA;MACA,MAAAwG,gBAAA,GAAAxG,OAAA,CAAAc,OAAA,CAAAuF,KAAA;MACA,IAAAG,gBAAA,IAAAA,gBAAA;QACA1G,OAAA,CAAAC,GAAA,eAAAyG,gBAAA;QAEA;UACA,IAAAvG,WAAA,GAAAoF,IAAA,CAAAoB,KAAA,CAAAD,gBAAA;UACA1G,OAAA,CAAAC,GAAA,gBAAAE,WAAA;;UAEA;UACA,IAAAA,WAAA,CAAAqB,IAAA,UAAArB,WAAA,CAAAhF,IAAA;YACA6E,OAAA,CAAAC,GAAA,yBAAAE,WAAA,CAAAhF,IAAA;YACAgF,WAAA,GAAAA,WAAA,CAAAhF,IAAA;UACA;;UAEA;UACA,IAAAgF,WAAA,CAAAnC,IAAA,KAAAmC,WAAA,CAAAkF,SAAA,IAAAlF,WAAA,CAAAyG,OAAA,IAAAzG,WAAA,CAAAhF,IAAA;YACA6E,OAAA,CAAAC,GAAA,eAAAE,WAAA;YACAD,OAAA,CAAAC,WAAA,GAAAA,WAAA;;YAEA;YACAD,OAAA,CAAAc,OAAA,GAAAd,OAAA,CAAAc,OAAA,CAAA6F,OAAA,6BACA;YAEA;UACA;YACA7G,OAAA,CAAAC,GAAA,gBAAAE,WAAA;UACA;QACA,SAAA8D,KAAA;UACAjE,OAAA,CAAAiE,KAAA,cAAAA,KAAA;QACA;MACA;MAEAjE,OAAA,CAAAC,GAAA;;MAEA;MACA;QACA;QACA,MAAA6G,SAAA;QACA,MAAAC,SAAA,GAAA7G,OAAA,CAAAc,OAAA,CAAAuF,KAAA,CAAAO,SAAA;QAEA,IAAAC,SAAA;UACA/G,OAAA,CAAAC,GAAA,kBAAA8G,SAAA;;UAEA;UACA;UACA,MAAAC,OAAA,GAAAD,SAAA,IAAAF,OAAA;UACA7G,OAAA,CAAAC,GAAA,iBAAA+G,OAAA;UAEA;YACA,MAAA7G,WAAA,GAAAoF,IAAA,CAAAoB,KAAA,CAAAK,OAAA;YACAhH,OAAA,CAAAC,GAAA,gBAAAE,WAAA;YAEA,IAAAA,WAAA,CAAAqB,IAAA,UAAArB,WAAA,CAAAhF,IAAA;cACA,MAAAA,IAAA,GAAAgF,WAAA,CAAAhF,IAAA;cACA6E,OAAA,CAAAC,GAAA,aAAA9E,IAAA;;cAEA;cACA,IAAAA,IAAA,CAAA6C,IAAA,KAAA7C,IAAA,CAAAyL,OAAA,IAAAzL,IAAA,CAAAA,IAAA;gBACA6E,OAAA,CAAAC,GAAA,eAAA9E,IAAA;gBACA+E,OAAA,CAAAC,WAAA,GAAAhF,IAAA;gBACA+E,OAAA,CAAAc,OAAA,GAAAd,OAAA,CAAAc,OAAA,CAAA6F,OAAA,CAAAE,SAAA,KACA;gBACA;cACA;YACA;UACA,SAAAE,UAAA;YACAjH,OAAA,CAAAC,GAAA,cAAAgH,UAAA;UACA;QACA;MACA,SAAAhD,KAAA;QACAjE,OAAA,CAAAC,GAAA,aAAAgE,KAAA;MACA;;MAEA;MACA,IAAA/D,OAAA,CAAAc,OAAA,CAAAjD,QAAA,iBACAmC,OAAA,CAAAc,OAAA,CAAAjD,QAAA;QACAiC,OAAA,CAAAC,GAAA;;QAEA;QACA,MAAAiH,aAAA;UACA9G,EAAA,EAAA6C,IAAA,CAAAkE,GAAA,GAAAC,QAAA;UACApJ,IAAA;UACAqJ,KAAA;UACAT,OAAA,OAAAlL,aAAA,EAAA0E,EAAA;QACA;QAEAJ,OAAA,CAAAC,GAAA,eAAAiH,aAAA;QACAhH,OAAA,CAAAC,WAAA,GAAA+G,aAAA;MACA;IACA;IAEA;IACA,MAAAT,mBAAAD,WAAA,EAAAtG,OAAA;MACA;QACAF,OAAA,CAAAC,GAAA,qBAAAuG,WAAA;QACA,MAAAc,QAAA,SAAA1M,KAAA,CAAA2M,GAAA,yCAAAf,WAAA;QAEA,IAAAc,QAAA,CAAAnM,IAAA,IAAAmM,QAAA,CAAAE,MAAA;UACAxH,OAAA,CAAAC,GAAA,cAAAqH,QAAA,CAAAnM,IAAA;;UAEA;UACA,IAAAgF,WAAA,GAAAmH,QAAA,CAAAnM,IAAA;UACA,IAAAgF,WAAA,CAAAqB,IAAA,UAAArB,WAAA,CAAAhF,IAAA;YACAgF,WAAA,GAAAA,WAAA,CAAAhF,IAAA;UACA;;UAEA;UACA+E,OAAA,CAAAC,WAAA,GAAAA,WAAA;;UAEA;UACAD,OAAA,CAAAuH,qBAAA,QAAAC,yBAAA,CAAAvH,WAAA,CAAAnC,IAAA;;UAEA;UACA,KAAAkC,OAAA,CAAAc,OAAA,CAAAjD,QAAA;YACAmC,OAAA,CAAAc,OAAA;UACA;;UAEA;UACA,KAAA2G,YAAA;QACA;UACA3H,OAAA,CAAAiE,KAAA,cAAAqD,QAAA;UACApH,OAAA,CAAAc,OAAA;QACA;MACA,SAAAiD,KAAA;QACAjE,OAAA,CAAAiE,KAAA,cAAAA,KAAA;QACA/D,OAAA,CAAAc,OAAA,0CAAAiD,KAAA,CAAA/D,OAAA;MACA;IACA;IAEA;IACAwH,0BAAA;MACA,MAAAE,QAAA;MACA,OAAAA,QAAA;IACA;IAEA;IACAC,iBAAAC,SAAA;MACA,MAAAC,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,SAAA;IACA;IAEA;IACAE,iBAAAF,SAAA;MACA,MAAAG,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAH,SAAA;IACA;IAEA;IACAI,oBAAAJ,SAAA;MACA,YAAAD,gBAAA,CAAAC,SAAA;IACA;IAEA;IACAK,oBAAAjI,OAAA;MACA,KAAAkI,IAAA,CAAAlI,OAAA,mBAAAA,OAAA,CAAAmI,YAAA;IACA;IAEA;IACAC,uBAAApI,OAAA,EAAAqI,OAAA;MACA,IAAAA,OAAA,KAAArI,OAAA,CAAAC,WAAA,CAAAnC,IAAA;QACAkC,OAAA,CAAAmI,YAAA;QACA;MACA;;MAEA;MACA,KAAAD,IAAA,CAAAlI,OAAA,CAAAC,WAAA,UAAAoI,OAAA;;MAEA;MACArI,OAAA,CAAAmI,YAAA;;MAEA;MACA,KAAA7F,SAAA;QACA;QACA,KAAAmF,YAAA;MACA;IACA;IAEA;IACAa,UAAA;MACA;MACA,MAAAC,eAAA;QACArI,EAAA;QACApC,IAAA;QACAqJ,KAAA;QACAhC,SAAA;QACAuB,OAAA;QACAzL,IAAA;UACAA,IAAA,GACA;YAAAyC,KAAA;YAAA3C,IAAA;YAAAyN,KAAA;YAAAC,QAAA;UAAA,GACA;YAAA/K,KAAA;YAAA3C,IAAA;YAAAyN,KAAA;YAAAC,QAAA;UAAA,GACA;YAAA/K,KAAA;YAAA3C,IAAA;YAAAyN,KAAA;YAAAC,QAAA;UAAA,GACA;YAAA/K,KAAA;YAAA3C,IAAA;YAAAyN,KAAA;YAAAC,QAAA;UAAA,GACA;YAAA/K,KAAA;YAAA3C,IAAA;YAAAyN,KAAA;YAAAC,QAAA;UAAA,EACA;UACAjL,MAAA,GACA;YAAA0C,EAAA;YAAAnF,IAAA;YAAA6C,SAAA;UAAA,GACA;YAAAsC,EAAA;YAAAnF,IAAA;YAAA6C,SAAA;UAAA;QAEA;MACA;;MAEA;MACA,MAAA4H,MAAA;QACA3E,MAAA;QACAC,OAAA;QACAC,QAAA;QACAd,WAAA,EAAAsI,eAAA;QACAhB,qBAAA;MACA;MAEA,KAAAvL,QAAA,CAAA4E,IAAA,CAAA4E,MAAA;;MAEA;MACA,KAAAnJ,eAAA,GAAAgJ,IAAA,CAAAC,SAAA,CAAAiD,eAAA;;MAEA;MACAzI,OAAA,CAAAC,GAAA,iBAAA/D,QAAA;;MAEA;MACA,MAAA0M,eAAA;QACApH,IAAA;QACAmD,GAAA;QACAxJ,IAAA;UACA6C,IAAA;UACA7C,IAAA,GACA;YAAAyC,KAAA;YAAA8K,KAAA;UAAA,GACA;YAAA9K,KAAA;YAAA8K,KAAA;UAAA,GACA;YAAA9K,KAAA;YAAA8K,KAAA;UAAA,EACA;UACAG,OAAA;YAAA5N,IAAA;UAAA;QACA;MACA;;MAEA;MACA,MAAA6N,WAAA,iBAAAvD,IAAA,CAAAC,SAAA,CAAAoD,eAAA;MACA,MAAAG,cAAA;QACAhI,MAAA;QACAC,OAAA,gBAAA8H,WAAA;QACA7H,QAAA;MACA;MAEA,KAAA/E,QAAA,CAAA4E,IAAA,CAAAiI,cAAA;MACA,KAAA1C,gBAAA,CAAA0C,cAAA;;MAEA;MACA,KAAAxM,eAAA,GAAAuM,WAAA;MAEA9I,OAAA,CAAAC,GAAA,iBAAA8I,cAAA;IACA;IAEA;IACAC,aAAA;MACAhJ,OAAA,CAAAC,GAAA;;MAEA;MACA,MAAAgJ,QAAA;QACAzH,IAAA;QACAmD,GAAA;QACAxJ,IAAA;UACAiF,EAAA;UACAiH,KAAA;UACAT,OAAA;UACA5I,IAAA;UACA7C,IAAA;YACAA,IAAA,GACA;cAAAuN,KAAA;cAAA9K,KAAA;cAAA3C,IAAA;cAAA0N,QAAA;YAAA,GACA;cAAAD,KAAA;cAAA9K,KAAA;cAAA3C,IAAA;cAAA0N,QAAA;YAAA,GACA;cAAAD,KAAA;cAAA9K,KAAA;cAAA3C,IAAA;cAAA0N,QAAA;YAAA,EACA;YACAjL,MAAA,GACA;cAAA0C,EAAA;cAAAnF,IAAA;cAAA6C,SAAA;YAAA,GACA;cAAAsC,EAAA;cAAAnF,IAAA;cAAA6C,SAAA;YAAA;UAEA;QACA;MACA;;MAEA;MACA,MAAAoL,WAAA;QACAnI,MAAA;QACAC,OAAA;QACAC,QAAA;MACA;;MAEA;MACAiI,WAAA,CAAA/I,WAAA,GAAA8I,QAAA,CAAA9N,IAAA;;MAEA;MACA,KAAAe,QAAA,CAAA4E,IAAA,CAAAoI,WAAA;;MAEA;MACA,KAAA3M,eAAA,GAAAgJ,IAAA,CAAAC,SAAA,CAAAyD,QAAA;MAEAjJ,OAAA,CAAAC,GAAA,gBAAAiJ,WAAA;IACA;IAEA;IACAC,gBAAA;MACA,KAAA7M,oBAAA;MACA,KAAAC,eAAA,QAAAL,QAAA,MAAAA,QAAA,CAAAqC,MAAA,MAAAyC,OAAA;IACA;IAEA;IACAoI,kBAAApL,IAAA;MACA,MAAAqL,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAArL,IAAA,EAAAC,WAAA;IACA;IAEA;IACAqL,kBAAAtL,IAAA;MACA,MAAAuL,QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAvL,IAAA,EAAAC,WAAA,OAAAD,IAAA;IACA;IAEA;IACAwL,oBAAA5L,KAAA;MACA,IAAAA,KAAA,CAAAxC,WAAA;QACA,OAAAwC,KAAA,CAAAxC,WAAA;MACA;;MAEA;MACA,MAAAqO,QAAA,QAAAH,iBAAA,CAAA1L,KAAA,CAAAI,IAAA;MACA,MAAA0L,SAAA,GAAA9L,KAAA,CAAAE,SAAA;;MAEA;MACA,MAAA6L,SAAA,IAAA/L,KAAA,CAAA3C,IAAA,QAAAmD,WAAA;MACA,IAAAwL,WAAA;MAEA,IAAAD,SAAA,CAAA5L,QAAA,UAAA4L,SAAA,CAAA5L,QAAA;QACA6L,WAAA;MACA,WAAAD,SAAA,CAAA5L,QAAA,YAAA4L,SAAA,CAAA5L,QAAA;QACA6L,WAAA;MACA,WAAAD,SAAA,CAAA5L,QAAA,YAAA4L,SAAA,CAAA5L,QAAA,UAAA4L,SAAA,CAAA5L,QAAA;QACA6L,WAAA;MACA,WAAAD,SAAA,CAAA5L,QAAA,cAAA4L,SAAA,CAAA5L,QAAA,UAAA4L,SAAA,CAAA5L,QAAA;QACA6L,WAAA;MACA,WAAAD,SAAA,CAAA5L,QAAA,aAAA4L,SAAA,CAAA5L,QAAA,UAAA4L,SAAA,CAAA5L,QAAA;QACA6L,WAAA;MACA,WAAAhM,KAAA,CAAAE,SAAA;QACA8L,WAAA;MACA;QACAA,WAAA;MACA;MAEA,UAAAH,QAAA,MAAAC,SAAA,KAAAE,WAAA;IACA;IAEA;IACAC,kBAAAjM,KAAA;MACA,MAAAyL,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAzL,KAAA,CAAAI,IAAA,EAAAC,WAAA;IACA;IAEA;IACA6L,iBAAAlM,KAAA;MACA,MAAAmK,OAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAnK,KAAA,CAAAI,IAAA,EAAAC,WAAA;IACA;IAEA;IACA8L,iBAAAC,IAAA;MACA,KAAA1M,WAAA,GAAA0M,IAAA;IACA;IAEAC,YAAA7F,KAAA;MACA,aAAA9G,WAAA,aAAAC,QAAA,GAAA6G,KAAA;IACA;IAEA8F,aAAAC,GAAA,EAAAvM,KAAA;MACA,MAAA+L,SAAA,GAAA/L,KAAA,CAAAS,YAAA,IAAAT,KAAA,CAAA3C,IAAA;MACA,MAAAyN,KAAA,GAAAyB,GAAA,CAAAR,SAAA;;MAEA;MACA,IAAAjB,KAAA,aAAAA,KAAA,KAAA0B,SAAA;QACA;MACA;;MAEA;MACA,IAAAxM,KAAA,CAAAI,IAAA,sCAAAD,QAAA,CAAAH,KAAA,CAAAI,IAAA,CAAAC,WAAA;QACA,IAAAyK,KAAA;UACA;YACA,WAAAzF,IAAA,CAAAyF,KAAA,EAAA2B,cAAA;UACA,SAAA3I,CAAA;YACA,OAAAgH,KAAA;UACA;QACA;MACA;;MAEA;MACA,IAAA9K,KAAA,CAAAI,IAAA,+DAAAD,QAAA,CAAAH,KAAA,CAAAI,IAAA,CAAAC,WAAA;QACA,WAAAyK,KAAA;UACA,OAAAA,KAAA,CAAA2B,cAAA;QACA;MACA;MAEA,OAAA3B,KAAA;IACA;IAEA;IACA,MAAA4B,YAAApK,OAAA;MACA,KAAAA,OAAA,CAAAC,WAAA;MAEA;QACA;QACA,KAAAiI,IAAA,CAAAlI,OAAA;;QAEA;QACA,MAAAqK,aAAA,GAAAjL,QAAA,CAAAkL,aAAA;QACAD,aAAA,CAAAE,KAAA,CAAAC,QAAA;QACAH,aAAA,CAAAE,KAAA,CAAAE,IAAA;QACAJ,aAAA,CAAAE,KAAA,CAAAG,KAAA;QACAL,aAAA,CAAAE,KAAA,CAAAI,UAAA;QACAN,aAAA,CAAAE,KAAA,CAAAK,OAAA;QACAxL,QAAA,CAAAyL,IAAA,CAAAC,WAAA,CAAAT,aAAA;;QAEA;QACA;QACA,MAAAlD,KAAA,GAAAnH,OAAA,CAAAC,WAAA,CAAAkH,KAAA;QACA,MAAA/B,WAAA,QAAA5J,aAAA,QAAAA,aAAA,CAAAuP,SAAA;QACA,MAAAC,WAAA,OAAAjI,IAAA,GAAAoH,cAAA;;QAEA;QACA,IAAAjP,WAAA,GAAA8E,OAAA,CAAAc,OAAA,CACA6F,OAAA,6CACAA,OAAA,8CACA1E,IAAA;;QAEA;QACA,IAAA/G,WAAA,CAAA2C,QAAA;UACA,MAAAoN,OAAA,GAAA7L,QAAA,CAAAkL,aAAA;UACAW,OAAA,CAAAC,SAAA,GAAAhQ,WAAA;UACAA,WAAA,GAAA+P,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;QACA;;QAEA;QACAf,aAAA,CAAAa,SAAA;AACA;AACA,2DAAA/D,KAAA;AACA,8DAAA/B,WAAA;AACA,+DAAA4F,WAAA;AACA;;AAEA;AACA;AACA,mBAAA9P,WAAA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;QAEA;QACA,MAAAmQ,cAAA,GAAAhB,aAAA,CAAAxG,aAAA;QACA,MAAAyH,KAAA,GAAAxQ,OAAA,CAAAyQ,IAAA,CAAAF,cAAA;;QAEA;QACA,MAAApL,WAAA,GAAAD,OAAA,CAAAC,WAAA;QACA,IAAAuL,OAAA;;QAEA;QACA,QAAAvL,WAAA,CAAAnC,IAAA;UACA;YACA0N,OAAA,QAAAC,kBAAA,CAAAxL,WAAA;YACA;UACA;YACAuL,OAAA,QAAAE,mBAAA,CAAAzL,WAAA;YACA;UACA;YACAuL,OAAA,QAAAG,kBAAA,CAAA1L,WAAA;YACA;UACA;YACAuL,OAAA,QAAAI,uBAAA,CAAA3L,WAAA;YACA;UACA;YACAuL,OAAA,QAAAK,iBAAA,CAAA5L,WAAA;QACA;;QAEA;QACAqL,KAAA,CAAAQ,SAAA,CAAAN,OAAA;QACA1L,OAAA,CAAAC,GAAA,mBAAAyL,OAAA;;QAEA;QACA,UAAAO,OAAA,CAAAC,OAAA,IAAAC,UAAA,CAAAD,OAAA;;QAEA;QACA,MAAAE,kBAAA,GAAA7B,aAAA,CAAAxG,aAAA;QACA,KAAAsI,eAAA,CAAAD,kBAAA,EAAAlM,OAAA,CAAAC,WAAA;;QAEA;QACA,MAAAmM,MAAA,SAAAxR,WAAA,CAAAyP,aAAA;UACAgC,KAAA;UAAA;UACAC,OAAA;UACAC,UAAA;UACAC,eAAA;QACA;;QAEA;QACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;QACA,MAAAC,GAAA,OAAA9R,KAAA;UACA+R,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,MAAAC,QAAA;QACA,MAAAC,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAA1B,KAAA;;QAEA;QACAiC,GAAA,CAAAO,QAAA,CAAAT,OAAA,iBAAAM,QAAA,EAAAC,SAAA;;QAEA;QACAL,GAAA,CAAAQ,IAAA,IAAAhG,KAAA,QAAApE,IAAA,GAAAqK,OAAA;;QAEA;QACAhO,QAAA,CAAAyL,IAAA,CAAAwC,WAAA,CAAAhD,aAAA;QACAiB,KAAA,CAAAgC,OAAA;;QAEA;QACA,KAAApF,IAAA,CAAAlI,OAAA;;QAEA;QACA,KAAA2C,QAAA,CAAAC,OAAA;MAEA,SAAAmB,KAAA;QACAjE,OAAA,CAAAiE,KAAA,aAAAA,KAAA;QACA,KAAAmE,IAAA,CAAAlI,OAAA;QACA,KAAA2C,QAAA,CAAAoB,KAAA,eAAAA,KAAA,CAAA/D,OAAA;MACA;IACA;IAEA;IACA,MAAAuN,sBAAA;MACA;QACA;QACA,KAAAtQ,YAAA;;QAEA;QACA,IAAAuQ,aAAA;;QAEA;QACA,SAAA/M,CAAA,MAAAA,CAAA,QAAAzE,QAAA,CAAAqC,MAAA,EAAAoC,CAAA;UACA,MAAAT,OAAA,QAAAhE,QAAA,CAAAyE,CAAA;UACA,KAAAT,OAAA,CAAAa,MAAA,IAAAb,OAAA,CAAAC,WAAA;YACAuN,aAAA,CAAA5M,IAAA;cACAZ,OAAA,EAAAA,OAAA;cACAkE,KAAA,EAAAzD;YACA;UACA;QACA;;QAEA;QACA,IAAA+M,aAAA,CAAAnP,MAAA;UACA,KAAAsE,QAAA,CAAAqB,OAAA;UACA,KAAA/G,YAAA;UACA;QACA;;QAEA;QACA,MAAA0P,GAAA,OAAA9R,KAAA;UACA+R,WAAA;UACAC,IAAA;UACAC,MAAA;QACA;;QAEA;QACA,SAAArM,CAAA,MAAAA,CAAA,GAAA+M,aAAA,CAAAnP,MAAA,EAAAoC,CAAA;UACA;UACA,IAAAA,CAAA;YACAkM,GAAA,CAAAc,OAAA;UACA;UAEA,MAAAC,YAAA,GAAAF,aAAA,CAAA/M,CAAA,EAAAT,OAAA;UACA,MAAAC,WAAA,GAAAyN,YAAA,CAAAzN,WAAA;;UAEA;UACA,MAAAoK,aAAA,GAAAjL,QAAA,CAAAkL,aAAA;UACAD,aAAA,CAAAE,KAAA,CAAAC,QAAA;UACAH,aAAA,CAAAE,KAAA,CAAAE,IAAA;UACAJ,aAAA,CAAAE,KAAA,CAAAG,KAAA;UACAL,aAAA,CAAAE,KAAA,CAAAI,UAAA;UACAN,aAAA,CAAAE,KAAA,CAAAK,OAAA;UACAxL,QAAA,CAAAyL,IAAA,CAAAC,WAAA,CAAAT,aAAA;;UAEA;UACA,MAAAlD,KAAA,GAAAlH,WAAA,CAAAkH,KAAA;UACA,MAAA6D,WAAA,OAAAjI,IAAA,GAAAoH,cAAA;UACA,MAAA/E,WAAA,QAAA5J,aAAA,QAAAA,aAAA,CAAAuP,SAAA;UAEA,IAAA4C,WAAA;AACA;AACA,mGAAAxG,KAAA;AACA;AACA,8DAAA/B,WAAA;AACA,+DAAA4F,WAAA;AACA;;AAEA;AACA;AACA;;UAEA;UACA,IAAA9P,WAAA,GAAAwS,YAAA,CAAA5M,OAAA;;UAEA;UACA,IAAA5F,WAAA,CAAA2C,QAAA;YACA,MAAAoN,OAAA,GAAA7L,QAAA,CAAAkL,aAAA;YACAW,OAAA,CAAAC,SAAA,GAAAhQ,WAAA;YACAA,WAAA,GAAA+P,OAAA,CAAAE,WAAA,IAAAF,OAAA,CAAAG,SAAA;UACA;UAEAuC,WAAA;AACA,wEAAAzS,WAAA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;UAEAyS,WAAA;;UAEA;UACAtD,aAAA,CAAAa,SAAA,GAAAyC,WAAA;;UAEA;UACA,MAAAtC,cAAA,GAAAhB,aAAA,CAAAxG,aAAA;UACA,MAAAqI,kBAAA,GAAA7B,aAAA,CAAAxG,aAAA;UAEA,IAAAwH,cAAA,IAAAa,kBAAA;YACA;YACA,MAAA0B,aAAA,GAAA9S,OAAA,CAAAyQ,IAAA,CAAAF,cAAA;YACA,IAAAG,OAAA;;YAEA;YACA,QAAAvL,WAAA,CAAAnC,IAAA;cACA;gBACA0N,OAAA,QAAAC,kBAAA,CAAAxL,WAAA;gBACA;cACA;gBACAuL,OAAA,QAAAE,mBAAA,CAAAzL,WAAA;gBACA;cACA;gBACAuL,OAAA,QAAAG,kBAAA,CAAA1L,WAAA;gBACA;cACA;gBACAuL,OAAA,QAAAI,uBAAA,CAAA3L,WAAA;gBACA;cACA;gBACAuL,OAAA,QAAAK,iBAAA,CAAA5L,WAAA;YACA;YAEA2N,aAAA,CAAA9B,SAAA,CAAAN,OAAA;;YAEA;YACA,KAAAW,eAAA,CAAAD,kBAAA,EAAAjM,WAAA;;YAEA;YACA,UAAA8L,OAAA,CAAAC,OAAA,IAAAC,UAAA,CAAAD,OAAA;;YAEA;YACA,MAAAI,MAAA,SAAAxR,WAAA,CAAAyP,aAAA;cACAgC,KAAA;cACAC,OAAA;cACAC,UAAA;cACAC,eAAA;YACA;;YAEA;YACA,MAAAC,OAAA,GAAAL,MAAA,CAAAM,SAAA;;YAEA;YACA,MAAAmB,SAAA,GAAAlB,GAAA,CAAAmB,QAAA,CAAAzQ,QAAA,CAAA0Q,QAAA;YACA,MAAAC,UAAA,GAAArB,GAAA,CAAAmB,QAAA,CAAAzQ,QAAA,CAAA4Q,SAAA;YACA,MAAAlB,QAAA,GAAAc,SAAA;YACA,MAAAb,SAAA,GAAAZ,MAAA,CAAAa,MAAA,GAAAF,QAAA,GAAAX,MAAA,CAAA1B,KAAA;;YAEA;YACA,IAAAwD,cAAA,GAAAlB,SAAA;YACA,IAAAmB,aAAA,GAAApB,QAAA;YAEA,IAAAC,SAAA,GAAAgB,UAAA;cAAA;cACAE,cAAA,GAAAF,UAAA;cACAG,aAAA,GAAA/B,MAAA,CAAA1B,KAAA,GAAAwD,cAAA,GAAA9B,MAAA,CAAAa,MAAA;YACA;;YAEA;YACA,MAAAmB,IAAA,IAAAP,SAAA,GAAAM,aAAA;YACA,MAAAE,IAAA;;YAEA1B,GAAA,CAAAO,QAAA,CAAAT,OAAA,SAAA2B,IAAA,EAAAC,IAAA,EAAAF,aAAA,EAAAD,cAAA;;YAEA;YACAN,aAAA,CAAAN,OAAA;YACAlO,QAAA,CAAAyL,IAAA,CAAAwC,WAAA,CAAAhD,aAAA;UACA;QACA;;QAEA;QACA,MAAAiE,QAAA,iBAAAvL,IAAA,GAAAwL,WAAA,GAAA1P,KAAA;QACA8N,GAAA,CAAAQ,IAAA,CAAAmB,QAAA;;QAEA;QACA,KAAArR,YAAA;QACA,KAAA0F,QAAA,CAAAC,OAAA;MAEA,SAAAmB,KAAA;QACAjE,OAAA,CAAAiE,KAAA,YAAAA,KAAA;QACA,KAAA9G,YAAA;QACA,KAAA0F,QAAA,CAAAoB,KAAA,cAAAA,KAAA,CAAA/D,OAAA;MACA;IACA;IAEA;IACAwO,wBAAAvO,WAAA;MACA,MAAAnC,IAAA,GAAAmC,WAAA,CAAAnC,IAAA;MACA,IAAA7C,IAAA;MACA,IAAAwT,UAAA;;MAEA;MACA,IAAAxO,WAAA,CAAAhF,IAAA,IAAAgF,WAAA,CAAAhF,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgF,WAAA,CAAAhF,IAAA,CAAAA,IAAA;QACAwT,UAAA,GAAAxT,IAAA,CAAAsI,GAAA,CAAAmL,IAAA,IAAAA,IAAA,CAAAhR,KAAA,IAAAgR,IAAA,CAAA3T,IAAA;MACA;;MAEA;MACA,MAAAyQ,OAAA;QACArE,KAAA;UACAwH,IAAA,EAAA1O,WAAA,CAAAkH,KAAA;QACA;QACAyH,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAhR,IAAA;UACA7C,IAAA,EAAAwT;QACA;QACAM,KAAA;UACAjR,IAAA;QACA;QACAkR,MAAA;MACA;;MAEA;MACA,IAAA/T,IAAA,CAAAoD,MAAA;QACA,IAAApD,IAAA,IAAA+T,MAAA;UACA;UACA,MAAAC,SAAA;;UAEA;UACAhU,IAAA,CAAAiU,OAAA,CAAAR,IAAA;YACA,IAAAA,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACA,KAAAF,SAAA,CAAAE,CAAA,CAAA1G,QAAA;kBACAwG,SAAA,CAAAE,CAAA,CAAA1G,QAAA;oBACA1N,IAAA,EAAAoU,CAAA,CAAA1G,QAAA;oBACA3K,IAAA,EAAAA,IAAA;oBACA7C,IAAA,EAAAmU,KAAA,CAAAX,UAAA,CAAApQ,MAAA,EAAAgR,IAAA;kBACA;gBACA;cACA;YACA;UACA;;UAEA;UACApU,IAAA,CAAAiU,OAAA,EAAAR,IAAA,EAAAxK,KAAA;YACA,IAAAwK,IAAA,CAAAM,MAAA;cACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACAF,SAAA,CAAAE,CAAA,CAAA1G,QAAA,EAAAxN,IAAA,CAAAiJ,KAAA,IAAAiL,CAAA,CAAA3G,KAAA;cACA;YACA;UACA;;UAEA;UACAgD,OAAA,CAAAwD,MAAA,GAAAM,MAAA,CAAAC,MAAA,CAAAN,SAAA;QACA;UACA;UACAzD,OAAA,CAAAwD,MAAA,CAAApO,IAAA;YACA7F,IAAA;YACA+C,IAAA,EAAAA,IAAA;YACA7C,IAAA,EAAAA,IAAA,CAAAsI,GAAA,CAAAmL,IAAA,IAAAA,IAAA,CAAAlG,KAAA;UACA;QACA;MACA;MAEA,OAAAgD,OAAA;IACA;IAEA;IACA;IACAC,mBAAA+D,SAAA;MACA1P,OAAA,CAAAC,GAAA,gBAAAyP,SAAA;;MAEA;MACA,IAAAvU,IAAA;MACA,IAAA0N,OAAA;;MAEA;MACA,IAAA6G,SAAA,CAAAvU,IAAA,IAAAmU,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAvU,IAAA;QACAA,IAAA,GAAAuU,SAAA,CAAAvU,IAAA;QACA0N,OAAA,GAAA6G,SAAA,CAAA7G,OAAA;MACA;MACA;MAAA,KACA,IAAA6G,SAAA,CAAAvU,IAAA,IAAAuU,SAAA,CAAAvU,IAAA,CAAAA,IAAA,IAAAmU,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAvU,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAuU,SAAA,CAAAvU,IAAA,CAAAA,IAAA;QACA0N,OAAA,GAAA6G,SAAA,CAAAvU,IAAA,CAAAuC,MAAA,GACAgS,SAAA,CAAAvU,IAAA,CAAAuC,MAAA,CAAAC,MAAA,CAAAiS,CAAA,IAAAA,CAAA,CAAA9R,SAAA;MACA;MAEAkC,OAAA,CAAAC,GAAA,YAAA9E,IAAA;MACA6E,OAAA,CAAAC,GAAA,QAAA4I,OAAA;;MAEA;MACA,MAAAgH,SAAA,GAAA1U,IAAA,CAAAsI,GAAA,CAAAmL,IAAA,IAAAA,IAAA,CAAAhR,KAAA,IAAAgR,IAAA,CAAA3T,IAAA;;MAEA;MACA,MAAAiU,MAAA;MACA,IAAA/T,IAAA,CAAAoD,MAAA,QAAApD,IAAA,IAAA+T,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACA5U,IAAA,CAAAiU,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAA1G,QAAA;UACA;QACA;QAEA,MAAAgG,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAzG,QAAA;UACA,MAAAuH,UAAA,GAAA/U,IAAA,CAAAsI,GAAA,CAAAmL,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAA1G,QAAA,KAAAA,QAAA;YACA,OAAAuG,MAAA,GAAAA,MAAA,CAAAxG,KAAA;UACA;UAEAwG,MAAA,CAAApO,IAAA;YACA7F,IAAA,EAAA0N,QAAA;YACA3K,IAAA;YACA7C,IAAA,EAAA+U;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAApO,IAAA;UACA7F,IAAA,EAAA4N,OAAA,KAAA5N,IAAA;UACA+C,IAAA;UACA7C,IAAA,EAAAA,IAAA,CAAAsI,GAAA,CAAAmL,IAAA,IAAAA,IAAA,CAAAlG,KAAA;QACA;MACA;MAEA;QACArB,KAAA;UACAwH,IAAA,EAAAa,SAAA,CAAArI,KAAA;QACA;QACAyH,OAAA;UACAC,OAAA;UACAqB,WAAA;YACApS,IAAA;UACA;QACA;QACAqS,MAAA;UACAlV,IAAA,EAAA+T,MAAA,CAAAzL,GAAA,CAAA4L,CAAA,IAAAA,CAAA,CAAApU,IAAA;QACA;QACA+T,KAAA;UACAhR,IAAA;UACA7C,IAAA,EAAA0U;QACA;QACAZ,KAAA;UACAjR,IAAA;QACA;QACAkR,MAAA,EAAAA;MACA;IACA;IAEA;IACAtD,oBAAA8D,SAAA;MACA,IAAAvU,IAAA;MACA,IAAA0N,OAAA;;MAEA;MACA,IAAA6G,SAAA,CAAAvU,IAAA,IAAAmU,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAvU,IAAA;QACAA,IAAA,GAAAuU,SAAA,CAAAvU,IAAA;QACA0N,OAAA,GAAA6G,SAAA,CAAA7G,OAAA;MACA;MACA;MAAA,KACA,IAAA6G,SAAA,CAAAvU,IAAA,IAAAuU,SAAA,CAAAvU,IAAA,CAAAA,IAAA,IAAAmU,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAvU,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAuU,SAAA,CAAAvU,IAAA,CAAAA,IAAA;QACA0N,OAAA,GAAA6G,SAAA,CAAAvU,IAAA,CAAAuC,MAAA,GACAgS,SAAA,CAAAvU,IAAA,CAAAuC,MAAA,CAAAC,MAAA,CAAAiS,CAAA,IAAAA,CAAA,CAAA9R,SAAA;MACA;;MAEA;MACA,MAAA+R,SAAA,GAAA1U,IAAA,CAAAsI,GAAA,CAAAmL,IAAA,IAAAA,IAAA,CAAAhR,KAAA,IAAAgR,IAAA,CAAA3T,IAAA;;MAEA;MACA,MAAAiU,MAAA;MACA,IAAA/T,IAAA,CAAAoD,MAAA,QAAApD,IAAA,IAAA+T,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACA5U,IAAA,CAAAiU,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAA1G,QAAA;UACA;QACA;QAEA,MAAAgG,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAzG,QAAA;UACA,MAAAuH,UAAA,GAAA/U,IAAA,CAAAsI,GAAA,CAAAmL,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAA1G,QAAA,KAAAA,QAAA;YACA,OAAAuG,MAAA,GAAAA,MAAA,CAAAxG,KAAA;UACA;UAEAwG,MAAA,CAAApO,IAAA;YACA7F,IAAA,EAAA0N,QAAA;YACA3K,IAAA;YACA7C,IAAA,EAAA+U;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAApO,IAAA;UACA7F,IAAA,EAAA4N,OAAA,KAAA5N,IAAA;UACA+C,IAAA;UACA7C,IAAA,EAAAA,IAAA,CAAAsI,GAAA,CAAAmL,IAAA,IAAAA,IAAA,CAAAlG,KAAA;QACA;MACA;MAEA;QACArB,KAAA;UACAwH,IAAA,EAAAa,SAAA,CAAArI,KAAA;QACA;QACAyH,OAAA;UACAC,OAAA;QACA;QACAsB,MAAA;UACAlV,IAAA,EAAA+T,MAAA,CAAAzL,GAAA,CAAA4L,CAAA,IAAAA,CAAA,CAAApU,IAAA;QACA;QACA+T,KAAA;UACAhR,IAAA;UACA7C,IAAA,EAAA0U;QACA;QACAZ,KAAA;UACAjR,IAAA;QACA;QACAkR,MAAA,EAAAA;MACA;IACA;IAEA;IACArD,mBAAA6D,SAAA;MACA,IAAAvU,IAAA;;MAEA;MACA,IAAAuU,SAAA,CAAAvU,IAAA,IAAAmU,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAvU,IAAA;QACAA,IAAA,GAAAuU,SAAA,CAAAvU,IAAA;MACA;MACA;MAAA,KACA,IAAAuU,SAAA,CAAAvU,IAAA,IAAAuU,SAAA,CAAAvU,IAAA,CAAAA,IAAA,IAAAmU,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAvU,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAuU,SAAA,CAAAvU,IAAA,CAAAA,IAAA;MACA;MAEA,MAAA+U,UAAA,GAAA/U,IAAA,CAAAsI,GAAA,CAAAmL,IAAA;QACA3T,IAAA,EAAA2T,IAAA,CAAAhR,KAAA,IAAAgR,IAAA,CAAA3T,IAAA;QACAyN,KAAA,EAAAkG,IAAA,CAAAlG;MACA;MAEA;QACArB,KAAA;UACAwH,IAAA,EAAAa,SAAA,CAAArI,KAAA;QACA;QACAyH,OAAA;UACAC,OAAA;UACAuB,SAAA;QACA;QACAD,MAAA;UACAE,MAAA;UACAC,KAAA;UACAC,GAAA;UACAtV,IAAA,EAAA+U,UAAA,CAAAzM,GAAA,CAAAmL,IAAA,IAAAA,IAAA,CAAA3T,IAAA;QACA;QACAiU,MAAA;UACAjU,IAAA;UACA+C,IAAA;UACA0S,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAnG,QAAA;UACA;UACAoG,QAAA;YACAF,KAAA;cACAC,IAAA;cACAE,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAJ,IAAA;UACA;UACA1V,IAAA,EAAA+U;QACA;MACA;IACA;IAEA;IACApE,wBAAA4D,SAAA;MACA;MACA,IAAAvU,IAAA;MACA,IAAA0N,OAAA;;MAEA;MACA,IAAA6G,SAAA,CAAAvU,IAAA,IAAAmU,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAvU,IAAA;QACAA,IAAA,GAAAuU,SAAA,CAAAvU,IAAA;QACA0N,OAAA,GAAA6G,SAAA,CAAA7G,OAAA;MACA;MACA;MAAA,KACA,IAAA6G,SAAA,CAAAvU,IAAA,IAAAuU,SAAA,CAAAvU,IAAA,CAAAA,IAAA,IAAAmU,KAAA,CAAAK,OAAA,CAAAD,SAAA,CAAAvU,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAuU,SAAA,CAAAvU,IAAA,CAAAA,IAAA;QACA0N,OAAA,GAAA6G,SAAA,CAAAvU,IAAA,CAAAuC,MAAA,GACAgS,SAAA,CAAAvU,IAAA,CAAAuC,MAAA,CAAAC,MAAA,CAAAiS,CAAA,IAAAA,CAAA,CAAA9R,SAAA;MACA;;MAEA;MACA,MAAAoT,SAAA,GAAA/V,IAAA,CAAAsI,GAAA,CAAAmL,IAAA,IAAAA,IAAA,CAAAhR,KAAA,IAAAgR,IAAA,CAAA3T,IAAA;;MAEA;MACA,MAAAiU,MAAA;MACA,IAAA/T,IAAA,CAAAoD,MAAA,QAAApD,IAAA,IAAA+T,MAAA;QACA;QACA,MAAAY,aAAA,OAAAC,GAAA;QACA5U,IAAA,CAAAiU,OAAA,CAAAR,IAAA;UACA,IAAAA,IAAA,CAAAM,MAAA;YACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA,IAAAS,aAAA,CAAAE,GAAA,CAAAX,CAAA,CAAA1G,QAAA;UACA;QACA;QAEA,MAAAgG,UAAA,GAAAW,KAAA,CAAAW,IAAA,CAAAH,aAAA;QACAnB,UAAA,CAAAS,OAAA,CAAAzG,QAAA;UACA,MAAAuH,UAAA,GAAA/U,IAAA,CAAAsI,GAAA,CAAAmL,IAAA;YACA,MAAAM,MAAA,GAAAN,IAAA,CAAAM,MAAA,EAAAiB,IAAA,CAAAd,CAAA,IAAAA,CAAA,CAAA1G,QAAA,KAAAA,QAAA;YACA,OAAAuG,MAAA,GAAAA,MAAA,CAAAxG,KAAA;UACA;UAEAwG,MAAA,CAAApO,IAAA;YACA7F,IAAA,EAAA0N,QAAA;YACA3K,IAAA;YAAA;YACA7C,IAAA,EAAA+U;UACA;QACA;MACA;QACA;QACAhB,MAAA,CAAApO,IAAA;UACA7F,IAAA,EAAA4N,OAAA,KAAA5N,IAAA;UACA+C,IAAA;UAAA;UACA7C,IAAA,EAAAA,IAAA,CAAAsI,GAAA,CAAAmL,IAAA,IAAAA,IAAA,CAAAlG,KAAA;QACA;MACA;MAEA;QACArB,KAAA;UACAwH,IAAA,EAAAa,SAAA,CAAArI,KAAA;QACA;QACAyH,OAAA;UACAC,OAAA;UACAqB,WAAA;YACApS,IAAA;UACA;QACA;QACAqS,MAAA;UACAlV,IAAA,EAAA+T,MAAA,CAAAzL,GAAA,CAAA4L,CAAA,IAAAA,CAAA,CAAApU,IAAA;QACA;QACA;QACA+T,KAAA;UACAhR,IAAA;QACA;QACAiR,KAAA;UACAjR,IAAA;UAAA;UACA7C,IAAA,EAAA+V;QACA;QACAhC,MAAA,EAAAA;MACA;IACA;IAEA;IACAnD,kBAAA2D,SAAA;MACA;QACArI,KAAA;UACAwH,IAAA,EAAAa,SAAA,CAAArI,KAAA;QACA;QACAyH,OAAA;UACAC,OAAA;QACA;QACAC,KAAA;UACAhR,IAAA;UACA7C,IAAA;QACA;QACA8T,KAAA;UACAjR,IAAA;QACA;QACAkR,MAAA;UACAlR,IAAA,EAAA0R,SAAA,CAAA1R,IAAA;UACA7C,IAAA;QACA;MACA;IACA;IAEA;IACAkR,gBAAA8E,SAAA,EAAAhR,WAAA;MACA;MACA,IAAAhF,IAAA;MACA,IAAAiI,OAAA;;MAEA;MACA,IAAAjD,WAAA,CAAAhF,IAAA,IAAAgF,WAAA,CAAAhF,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAgF,WAAA,CAAAhF,IAAA,CAAAA,IAAA;;QAEA;QACA,IAAAA,IAAA,CAAAoD,MAAA;UACA,IAAApD,IAAA,IAAA+T,MAAA;YACA;YACA9L,OAAA;YACA,MAAAgO,SAAA,GAAAjW,IAAA;YACA,IAAAiW,SAAA,CAAAlC,MAAA,IAAAI,KAAA,CAAAK,OAAA,CAAAyB,SAAA,CAAAlC,MAAA;cACAkC,SAAA,CAAAlC,MAAA,CAAAE,OAAA,CAAAC,CAAA;gBACAjM,OAAA,CAAAtC,IAAA,CAAAuO,CAAA,CAAA1G,QAAA;cACA;YACA;UACA;YACA;YACAvF,OAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAiO,SAAA;;MAEA;MACAA,SAAA;MACAjO,OAAA,CAAAgM,OAAA,CAAAkC,MAAA;QACAD,SAAA,sGAAAC,MAAA;MACA;MACAD,SAAA;;MAEA;MACAA,SAAA;MACAlW,IAAA,CAAAiU,OAAA,CAAAR,IAAA;QACAyC,SAAA;;QAEA;QACAA,SAAA,yDAAAzC,IAAA,CAAAhR,KAAA,IAAAgR,IAAA,CAAA3T,IAAA;;QAEA;QACA,IAAA2T,IAAA,CAAAM,MAAA;UACA;UACAN,IAAA,CAAAM,MAAA,CAAAE,OAAA,CAAAC,CAAA;YACAgC,SAAA,yDAAAhC,CAAA,CAAA3G,KAAA,KAAA0B,SAAA,GAAAiF,CAAA,CAAA3G,KAAA;UACA;QACA;UACA;UACA2I,SAAA,yDAAAzC,IAAA,CAAAlG,KAAA,KAAA0B,SAAA,GAAAwE,IAAA,CAAAlG,KAAA;QACA;QAEA2I,SAAA;MACA;MACAA,SAAA;;MAEA;MACAF,SAAA,CAAA/F,SAAA,GAAAiG,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}