{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, isString } from 'zrender/lib/core/util.js';\nimport { isSeriesDataSchema } from './SeriesDataSchema.js';\n/**\r\n * Note that it is too complicated to support 3d stack by value\r\n * (have to create two-dimension inverted index), so in 3d case\r\n * we just support that stacked by index.\r\n *\r\n * @param seriesModel\r\n * @param dimensionsInput The same as the input of <module:echarts/data/SeriesData>.\r\n *        The input will be modified.\r\n * @param opt\r\n * @param opt.stackedCoordDimension Specify a coord dimension if needed.\r\n * @param opt.byIndex=false\r\n * @return calculationInfo\r\n * {\r\n *     stackedDimension: string\r\n *     stackedByDimension: string\r\n *     isStackedByIndex: boolean\r\n *     stackedOverDimension: string\r\n *     stackResultDimension: string\r\n * }\r\n */\nexport function enableDataStack(seriesModel, dimensionsInput, opt) {\n  opt = opt || {};\n  var byIndex = opt.byIndex;\n  var stackedCoordDimension = opt.stackedCoordDimension;\n  var dimensionDefineList;\n  var schema;\n  var store;\n  if (isLegacyDimensionsInput(dimensionsInput)) {\n    dimensionDefineList = dimensionsInput;\n  } else {\n    schema = dimensionsInput.schema;\n    dimensionDefineList = schema.dimensions;\n    store = dimensionsInput.store;\n  }\n  // Compatibal: when `stack` is set as '', do not stack.\n  var mayStack = !!(seriesModel && seriesModel.get('stack'));\n  var stackedByDimInfo;\n  var stackedDimInfo;\n  var stackResultDimension;\n  var stackedOverDimension;\n  each(dimensionDefineList, function (dimensionInfo, index) {\n    if (isString(dimensionInfo)) {\n      dimensionDefineList[index] = dimensionInfo = {\n        name: dimensionInfo\n      };\n    }\n    if (mayStack && !dimensionInfo.isExtraCoord) {\n      // Find the first ordinal dimension as the stackedByDimInfo.\n      if (!byIndex && !stackedByDimInfo && dimensionInfo.ordinalMeta) {\n        stackedByDimInfo = dimensionInfo;\n      }\n      // Find the first stackable dimension as the stackedDimInfo.\n      if (!stackedDimInfo && dimensionInfo.type !== 'ordinal' && dimensionInfo.type !== 'time' && (!stackedCoordDimension || stackedCoordDimension === dimensionInfo.coordDim)) {\n        stackedDimInfo = dimensionInfo;\n      }\n    }\n  });\n  if (stackedDimInfo && !byIndex && !stackedByDimInfo) {\n    // Compatible with previous design, value axis (time axis) only stack by index.\n    // It may make sense if the user provides elaborately constructed data.\n    byIndex = true;\n  }\n  // Add stack dimension, they can be both calculated by coordinate system in `unionExtent`.\n  // That put stack logic in List is for using conveniently in echarts extensions, but it\n  // might not be a good way.\n  if (stackedDimInfo) {\n    // Use a weird name that not duplicated with other names.\n    // Also need to use seriesModel.id as postfix because different\n    // series may share same data store. The stack dimension needs to be distinguished.\n    stackResultDimension = '__\\0ecstackresult_' + seriesModel.id;\n    stackedOverDimension = '__\\0ecstackedover_' + seriesModel.id;\n    // Create inverted index to fast query index by value.\n    if (stackedByDimInfo) {\n      stackedByDimInfo.createInvertedIndices = true;\n    }\n    var stackedDimCoordDim_1 = stackedDimInfo.coordDim;\n    var stackedDimType = stackedDimInfo.type;\n    var stackedDimCoordIndex_1 = 0;\n    each(dimensionDefineList, function (dimensionInfo) {\n      if (dimensionInfo.coordDim === stackedDimCoordDim_1) {\n        stackedDimCoordIndex_1++;\n      }\n    });\n    var stackedOverDimensionDefine = {\n      name: stackResultDimension,\n      coordDim: stackedDimCoordDim_1,\n      coordDimIndex: stackedDimCoordIndex_1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length\n    };\n    var stackResultDimensionDefine = {\n      name: stackedOverDimension,\n      // This dimension contains stack base (generally, 0), so do not set it as\n      // `stackedDimCoordDim` to avoid extent calculation, consider log scale.\n      coordDim: stackedOverDimension,\n      coordDimIndex: stackedDimCoordIndex_1 + 1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length + 1\n    };\n    if (schema) {\n      if (store) {\n        stackedOverDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackedOverDimension, stackedDimType);\n        stackResultDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackResultDimension, stackedDimType);\n      }\n      schema.appendCalculationDimension(stackedOverDimensionDefine);\n      schema.appendCalculationDimension(stackResultDimensionDefine);\n    } else {\n      dimensionDefineList.push(stackedOverDimensionDefine);\n      dimensionDefineList.push(stackResultDimensionDefine);\n    }\n  }\n  return {\n    stackedDimension: stackedDimInfo && stackedDimInfo.name,\n    stackedByDimension: stackedByDimInfo && stackedByDimInfo.name,\n    isStackedByIndex: byIndex,\n    stackedOverDimension: stackedOverDimension,\n    stackResultDimension: stackResultDimension\n  };\n}\nfunction isLegacyDimensionsInput(dimensionsInput) {\n  return !isSeriesDataSchema(dimensionsInput.schema);\n}\nexport function isDimensionStacked(data, stackedDim) {\n  // Each single series only maps to one pair of axis. So we do not need to\n  // check stackByDim, whatever stacked by a dimension or stacked by index.\n  return !!stackedDim && stackedDim === data.getCalculationInfo('stackedDimension');\n}\nexport function getStackedDimension(data, targetDim) {\n  return isDimensionStacked(data, targetDim) ? data.getCalculationInfo('stackResultDimension') : targetDim;\n}", "map": {"version": 3, "names": ["each", "isString", "isSeriesDataSchema", "enableDataStack", "seriesModel", "dimensionsInput", "opt", "byIndex", "stackedCoordDimension", "dimensionDefineList", "schema", "store", "isLegacyDimensionsInput", "dimensions", "may<PERSON><PERSON><PERSON>", "get", "stackedByDimInfo", "stackedDimInfo", "stackResultDimension", "stackedOverDimension", "dimensionInfo", "index", "name", "isExtraCoord", "ordinalMeta", "type", "coordDim", "id", "createInvertedIndices", "stackedDimCoordDim_1", "stackedDimType", "stackedDimCoordIndex_1", "stackedOverDimensionDefine", "coordDimIndex", "isCalculationCoord", "storeDimIndex", "length", "stackResultDimensionDefine", "ensureCalculationDimension", "appendCalculationDimension", "push", "stackedDimension", "stackedByDimension", "isStackedByIndex", "isDimensionStacked", "data", "<PERSON><PERSON>im", "getCalculationInfo", "getStackedDimension", "targetDim"], "sources": ["E:/indicator-qa-service/datafront/node_modules/echarts/lib/data/helper/dataStackHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { each, isString } from 'zrender/lib/core/util.js';\nimport { isSeriesDataSchema } from './SeriesDataSchema.js';\n/**\r\n * Note that it is too complicated to support 3d stack by value\r\n * (have to create two-dimension inverted index), so in 3d case\r\n * we just support that stacked by index.\r\n *\r\n * @param seriesModel\r\n * @param dimensionsInput The same as the input of <module:echarts/data/SeriesData>.\r\n *        The input will be modified.\r\n * @param opt\r\n * @param opt.stackedCoordDimension Specify a coord dimension if needed.\r\n * @param opt.byIndex=false\r\n * @return calculationInfo\r\n * {\r\n *     stackedDimension: string\r\n *     stackedByDimension: string\r\n *     isStackedByIndex: boolean\r\n *     stackedOverDimension: string\r\n *     stackResultDimension: string\r\n * }\r\n */\nexport function enableDataStack(seriesModel, dimensionsInput, opt) {\n  opt = opt || {};\n  var byIndex = opt.byIndex;\n  var stackedCoordDimension = opt.stackedCoordDimension;\n  var dimensionDefineList;\n  var schema;\n  var store;\n  if (isLegacyDimensionsInput(dimensionsInput)) {\n    dimensionDefineList = dimensionsInput;\n  } else {\n    schema = dimensionsInput.schema;\n    dimensionDefineList = schema.dimensions;\n    store = dimensionsInput.store;\n  }\n  // Compatibal: when `stack` is set as '', do not stack.\n  var mayStack = !!(seriesModel && seriesModel.get('stack'));\n  var stackedByDimInfo;\n  var stackedDimInfo;\n  var stackResultDimension;\n  var stackedOverDimension;\n  each(dimensionDefineList, function (dimensionInfo, index) {\n    if (isString(dimensionInfo)) {\n      dimensionDefineList[index] = dimensionInfo = {\n        name: dimensionInfo\n      };\n    }\n    if (mayStack && !dimensionInfo.isExtraCoord) {\n      // Find the first ordinal dimension as the stackedByDimInfo.\n      if (!byIndex && !stackedByDimInfo && dimensionInfo.ordinalMeta) {\n        stackedByDimInfo = dimensionInfo;\n      }\n      // Find the first stackable dimension as the stackedDimInfo.\n      if (!stackedDimInfo && dimensionInfo.type !== 'ordinal' && dimensionInfo.type !== 'time' && (!stackedCoordDimension || stackedCoordDimension === dimensionInfo.coordDim)) {\n        stackedDimInfo = dimensionInfo;\n      }\n    }\n  });\n  if (stackedDimInfo && !byIndex && !stackedByDimInfo) {\n    // Compatible with previous design, value axis (time axis) only stack by index.\n    // It may make sense if the user provides elaborately constructed data.\n    byIndex = true;\n  }\n  // Add stack dimension, they can be both calculated by coordinate system in `unionExtent`.\n  // That put stack logic in List is for using conveniently in echarts extensions, but it\n  // might not be a good way.\n  if (stackedDimInfo) {\n    // Use a weird name that not duplicated with other names.\n    // Also need to use seriesModel.id as postfix because different\n    // series may share same data store. The stack dimension needs to be distinguished.\n    stackResultDimension = '__\\0ecstackresult_' + seriesModel.id;\n    stackedOverDimension = '__\\0ecstackedover_' + seriesModel.id;\n    // Create inverted index to fast query index by value.\n    if (stackedByDimInfo) {\n      stackedByDimInfo.createInvertedIndices = true;\n    }\n    var stackedDimCoordDim_1 = stackedDimInfo.coordDim;\n    var stackedDimType = stackedDimInfo.type;\n    var stackedDimCoordIndex_1 = 0;\n    each(dimensionDefineList, function (dimensionInfo) {\n      if (dimensionInfo.coordDim === stackedDimCoordDim_1) {\n        stackedDimCoordIndex_1++;\n      }\n    });\n    var stackedOverDimensionDefine = {\n      name: stackResultDimension,\n      coordDim: stackedDimCoordDim_1,\n      coordDimIndex: stackedDimCoordIndex_1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length\n    };\n    var stackResultDimensionDefine = {\n      name: stackedOverDimension,\n      // This dimension contains stack base (generally, 0), so do not set it as\n      // `stackedDimCoordDim` to avoid extent calculation, consider log scale.\n      coordDim: stackedOverDimension,\n      coordDimIndex: stackedDimCoordIndex_1 + 1,\n      type: stackedDimType,\n      isExtraCoord: true,\n      isCalculationCoord: true,\n      storeDimIndex: dimensionDefineList.length + 1\n    };\n    if (schema) {\n      if (store) {\n        stackedOverDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackedOverDimension, stackedDimType);\n        stackResultDimensionDefine.storeDimIndex = store.ensureCalculationDimension(stackResultDimension, stackedDimType);\n      }\n      schema.appendCalculationDimension(stackedOverDimensionDefine);\n      schema.appendCalculationDimension(stackResultDimensionDefine);\n    } else {\n      dimensionDefineList.push(stackedOverDimensionDefine);\n      dimensionDefineList.push(stackResultDimensionDefine);\n    }\n  }\n  return {\n    stackedDimension: stackedDimInfo && stackedDimInfo.name,\n    stackedByDimension: stackedByDimInfo && stackedByDimInfo.name,\n    isStackedByIndex: byIndex,\n    stackedOverDimension: stackedOverDimension,\n    stackResultDimension: stackResultDimension\n  };\n}\nfunction isLegacyDimensionsInput(dimensionsInput) {\n  return !isSeriesDataSchema(dimensionsInput.schema);\n}\nexport function isDimensionStacked(data, stackedDim) {\n  // Each single series only maps to one pair of axis. So we do not need to\n  // check stackByDim, whatever stacked by a dimension or stacked by index.\n  return !!stackedDim && stackedDim === data.getCalculationInfo('stackedDimension');\n}\nexport function getStackedDimension(data, targetDim) {\n  return isDimensionStacked(data, targetDim) ? data.getCalculationInfo('stackResultDimension') : targetDim;\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAI,EAAEC,QAAQ,QAAQ,0BAA0B;AACzD,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,WAAW,EAAEC,eAAe,EAAEC,GAAG,EAAE;EACjEA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EACf,IAAIC,OAAO,GAAGD,GAAG,CAACC,OAAO;EACzB,IAAIC,qBAAqB,GAAGF,GAAG,CAACE,qBAAqB;EACrD,IAAIC,mBAAmB;EACvB,IAAIC,MAAM;EACV,IAAIC,KAAK;EACT,IAAIC,uBAAuB,CAACP,eAAe,CAAC,EAAE;IAC5CI,mBAAmB,GAAGJ,eAAe;EACvC,CAAC,MAAM;IACLK,MAAM,GAAGL,eAAe,CAACK,MAAM;IAC/BD,mBAAmB,GAAGC,MAAM,CAACG,UAAU;IACvCF,KAAK,GAAGN,eAAe,CAACM,KAAK;EAC/B;EACA;EACA,IAAIG,QAAQ,GAAG,CAAC,EAAEV,WAAW,IAAIA,WAAW,CAACW,GAAG,CAAC,OAAO,CAAC,CAAC;EAC1D,IAAIC,gBAAgB;EACpB,IAAIC,cAAc;EAClB,IAAIC,oBAAoB;EACxB,IAAIC,oBAAoB;EACxBnB,IAAI,CAACS,mBAAmB,EAAE,UAAUW,aAAa,EAAEC,KAAK,EAAE;IACxD,IAAIpB,QAAQ,CAACmB,aAAa,CAAC,EAAE;MAC3BX,mBAAmB,CAACY,KAAK,CAAC,GAAGD,aAAa,GAAG;QAC3CE,IAAI,EAAEF;MACR,CAAC;IACH;IACA,IAAIN,QAAQ,IAAI,CAACM,aAAa,CAACG,YAAY,EAAE;MAC3C;MACA,IAAI,CAAChB,OAAO,IAAI,CAACS,gBAAgB,IAAII,aAAa,CAACI,WAAW,EAAE;QAC9DR,gBAAgB,GAAGI,aAAa;MAClC;MACA;MACA,IAAI,CAACH,cAAc,IAAIG,aAAa,CAACK,IAAI,KAAK,SAAS,IAAIL,aAAa,CAACK,IAAI,KAAK,MAAM,KAAK,CAACjB,qBAAqB,IAAIA,qBAAqB,KAAKY,aAAa,CAACM,QAAQ,CAAC,EAAE;QACxKT,cAAc,GAAGG,aAAa;MAChC;IACF;EACF,CAAC,CAAC;EACF,IAAIH,cAAc,IAAI,CAACV,OAAO,IAAI,CAACS,gBAAgB,EAAE;IACnD;IACA;IACAT,OAAO,GAAG,IAAI;EAChB;EACA;EACA;EACA;EACA,IAAIU,cAAc,EAAE;IAClB;IACA;IACA;IACAC,oBAAoB,GAAG,oBAAoB,GAAGd,WAAW,CAACuB,EAAE;IAC5DR,oBAAoB,GAAG,oBAAoB,GAAGf,WAAW,CAACuB,EAAE;IAC5D;IACA,IAAIX,gBAAgB,EAAE;MACpBA,gBAAgB,CAACY,qBAAqB,GAAG,IAAI;IAC/C;IACA,IAAIC,oBAAoB,GAAGZ,cAAc,CAACS,QAAQ;IAClD,IAAII,cAAc,GAAGb,cAAc,CAACQ,IAAI;IACxC,IAAIM,sBAAsB,GAAG,CAAC;IAC9B/B,IAAI,CAACS,mBAAmB,EAAE,UAAUW,aAAa,EAAE;MACjD,IAAIA,aAAa,CAACM,QAAQ,KAAKG,oBAAoB,EAAE;QACnDE,sBAAsB,EAAE;MAC1B;IACF,CAAC,CAAC;IACF,IAAIC,0BAA0B,GAAG;MAC/BV,IAAI,EAAEJ,oBAAoB;MAC1BQ,QAAQ,EAAEG,oBAAoB;MAC9BI,aAAa,EAAEF,sBAAsB;MACrCN,IAAI,EAAEK,cAAc;MACpBP,YAAY,EAAE,IAAI;MAClBW,kBAAkB,EAAE,IAAI;MACxBC,aAAa,EAAE1B,mBAAmB,CAAC2B;IACrC,CAAC;IACD,IAAIC,0BAA0B,GAAG;MAC/Bf,IAAI,EAAEH,oBAAoB;MAC1B;MACA;MACAO,QAAQ,EAAEP,oBAAoB;MAC9Bc,aAAa,EAAEF,sBAAsB,GAAG,CAAC;MACzCN,IAAI,EAAEK,cAAc;MACpBP,YAAY,EAAE,IAAI;MAClBW,kBAAkB,EAAE,IAAI;MACxBC,aAAa,EAAE1B,mBAAmB,CAAC2B,MAAM,GAAG;IAC9C,CAAC;IACD,IAAI1B,MAAM,EAAE;MACV,IAAIC,KAAK,EAAE;QACTqB,0BAA0B,CAACG,aAAa,GAAGxB,KAAK,CAAC2B,0BAA0B,CAACnB,oBAAoB,EAAEW,cAAc,CAAC;QACjHO,0BAA0B,CAACF,aAAa,GAAGxB,KAAK,CAAC2B,0BAA0B,CAACpB,oBAAoB,EAAEY,cAAc,CAAC;MACnH;MACApB,MAAM,CAAC6B,0BAA0B,CAACP,0BAA0B,CAAC;MAC7DtB,MAAM,CAAC6B,0BAA0B,CAACF,0BAA0B,CAAC;IAC/D,CAAC,MAAM;MACL5B,mBAAmB,CAAC+B,IAAI,CAACR,0BAA0B,CAAC;MACpDvB,mBAAmB,CAAC+B,IAAI,CAACH,0BAA0B,CAAC;IACtD;EACF;EACA,OAAO;IACLI,gBAAgB,EAAExB,cAAc,IAAIA,cAAc,CAACK,IAAI;IACvDoB,kBAAkB,EAAE1B,gBAAgB,IAAIA,gBAAgB,CAACM,IAAI;IAC7DqB,gBAAgB,EAAEpC,OAAO;IACzBY,oBAAoB,EAAEA,oBAAoB;IAC1CD,oBAAoB,EAAEA;EACxB,CAAC;AACH;AACA,SAASN,uBAAuBA,CAACP,eAAe,EAAE;EAChD,OAAO,CAACH,kBAAkB,CAACG,eAAe,CAACK,MAAM,CAAC;AACpD;AACA,OAAO,SAASkC,kBAAkBA,CAACC,IAAI,EAAEC,UAAU,EAAE;EACnD;EACA;EACA,OAAO,CAAC,CAACA,UAAU,IAAIA,UAAU,KAAKD,IAAI,CAACE,kBAAkB,CAAC,kBAAkB,CAAC;AACnF;AACA,OAAO,SAASC,mBAAmBA,CAACH,IAAI,EAAEI,SAAS,EAAE;EACnD,OAAOL,kBAAkB,CAACC,IAAI,EAAEI,SAAS,CAAC,GAAGJ,IAAI,CAACE,kBAAkB,CAAC,sBAAsB,CAAC,GAAGE,SAAS;AAC1G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}