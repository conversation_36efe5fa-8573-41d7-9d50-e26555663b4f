<template>
  <div class="chart-container">
    <!-- 图表切换工具栏 -->
    <div v-if="showChartSwitcher" class="chart-switcher">
      <div class="chart-switcher-title">图表切换</div>
      <div class="chart-type-buttons">
        <button
          v-for="chartType in availableChartTypes"
          :key="chartType.type"
          :class="['chart-type-btn', {
            'active': currentChartType === chartType.type,
            'disabled': !chartType.compatible
          }]"
          :disabled="!chartType.compatible || switchingChart"
          @click="switchChartType(chartType.type)"
          :title="chartType.name"
        >
          <i :class="chartType.icon"></i>
          <span>{{ chartType.name }}</span>
          <div v-if="switchingChart && currentChartType === chartType.type" class="switching-loader">
            <i class="el-icon-loading"></i>
          </div>
        </button>
      </div>
    </div>

    <!-- 图表显示区域 -->
    <div v-if="loading" class="chart-loading">
      <i class="el-icon-loading"></i>
      <span>加载图表中...</span>
    </div>
    <div v-else-if="error" class="chart-error">
      <i class="el-icon-warning"></i>
      <span>{{ errorMsg }}</span>
    </div>
    <div v-else ref="chartRef" class="chart-canvas"></div>
  </div>
</template>

<script>
import { getData } from '../api/index';
import * as echarts from 'echarts';

export default {
  name: 'ChartDisplay',
  props: {
    chartConfig: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 推荐的图表类型列表
    recommendedChartTypes: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      error: false,
      errorMsg: '',
      chartInstance: null,
      switchingChart: false,
      currentChartType: '',
      localChartConfig: null, // 本地图表配置副本
      // 支持的图表类型配置
      chartTypeConfig: {
        'bar': {
          name: '柱图',
          icon: 'el-icon-s-data',
          compatible: true
        },
        'line': {
          name: '线图',
          icon: 'el-icon-connection',
          compatible: true
        },
        'pie': {
          name: '饼图',
          icon: 'el-icon-pie-chart',
          compatible: true
        },
        'bar-horizontal': {
          name: '条形图',
          icon: 'el-icon-s-operation',
          compatible: true
        }
      }
    }
  },
  computed: {
    // 是否显示图表切换器
    showChartSwitcher() {
      return this.availableChartTypes.length > 1 && !this.loading && !this.error;
    },

    // 可用的图表类型列表
    availableChartTypes() {
      const types = [];

      // 获取所有支持的图表类型
      const allTypes = Object.keys(this.chartTypeConfig);

      // 添加当前图表类型（排在第一位）
      if (this.currentChartType && this.chartTypeConfig[this.currentChartType]) {
        types.push({
          type: this.currentChartType,
          ...this.chartTypeConfig[this.currentChartType],
          compatible: true // 当前类型肯定兼容
        });
      }

      // 添加其他所有图表类型
      allTypes.forEach(type => {
        if (type !== this.currentChartType) {
          types.push({
            type: type,
            ...this.chartTypeConfig[type],
            compatible: true // 暂时都设为兼容
          });
        }
      });

      return types;
    }
  },
  watch: {
    chartConfig: {
      deep: true,
      handler(newConfig) {
        // 更新当前图表类型
        if (newConfig && newConfig.type) {
          this.currentChartType = newConfig.type;
        }
        this.renderChart();
      }
    }
  },
  mounted() {
    // 初始化当前图表类型
    if (this.chartConfig && this.chartConfig.type) {
      this.currentChartType = this.chartConfig.type;
    }
    this.renderChart();
    // 添加窗口大小变化监听，以便图表能够自适应
    window.addEventListener('resize', this.resizeChart);
  },
  beforeDestroy() {
    // 移除窗口大小变化监听，避免内存泄漏
    window.removeEventListener('resize', this.resizeChart);
    // 销毁图表实例
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
  },
  methods: {
    // 切换图表类型
    async switchChartType(newType) {
      if (newType === this.currentChartType || this.switchingChart) {
        return;
      }

      console.log('切换图表类型:', this.currentChartType, '->', newType);

      try {
        this.switchingChart = true;

        // 更新图表类型
        this.currentChartType = newType;

        // 创建新的图表配置，复用相同数据
        const newChartConfig = {
          ...this.chartConfig,
          type: newType
        };

        // 触发重新渲染
        this.$emit('chart-type-changed', newChartConfig);

        // 使用本地副本进行渲染，避免直接修改 prop
        this.localChartConfig = { ...newChartConfig };
        this.renderChartWithConfig(this.localChartConfig);

        this.$message.success(`已切换到${this.chartTypeConfig[newType]?.name}`);

      } catch (error) {
        console.error('图表切换失败:', error);
        this.$message.error('图表切换失败: ' + error.message);
      } finally {
        this.switchingChart = false;
      }
    },

    renderChart() {
      this.renderChartWithConfig(this.chartConfig);
    },

    renderChartWithConfig(config) {
      this.loading = true;
      this.error = false;

      // 如果没有配置或缺少必要的配置，显示错误
      if (!config || !config.type) {
        this.loading = false;
        this.error = true;
        this.errorMsg = '无效的图表配置';
        console.error('图表配置无效:', config);
        return;
      }

      console.log('开始渲染图表，配置:', config);

      // 检查chartConfig是否已包含完整数据
      if (config.data) {
        console.log('图表配置中已包含数据，直接使用');
        this.loading = false;

        try {
          // 将原始数据转换为echarts选项
          const options = this.convertToChartOptions(config);
          console.log('生成的echarts选项:', options);

          // 渲染图表
          this.drawChart(options);
        } catch (error) {
          console.error('处理图表数据失败:', error);
          this.error = true;
          this.errorMsg = '处理图表数据失败: ' + error.message;
        }
        return;
      }

      // 如果没有数据，才调用API获取
      getData(config).then(response => {
        console.log('图表数据API响应:', response);
        this.loading = false;
        
        if (!response) {
          this.error = true;
          this.errorMsg = '获取数据失败: 响应为空';
          console.error('图表数据响应为空');
          return;
        }
        
        if (response.code !== 0) {
          this.error = true;
          this.errorMsg = response.msg || '获取数据失败: ' + response.code;
          console.error('图表数据获取失败:', response);
          return;
        }
        
        const chartData = response.data || {};
        console.log('解析后的图表数据:', chartData);
        
        try {
          // 将原始数据转换为echarts选项
          const options = this.convertToChartOptions(chartData);
          console.log('生成的echarts选项:', options);
          
          // 渲染图表
          this.drawChart(options);
        } catch (error) {
          console.error('处理图表数据失败:', error);
          this.error = true;
          this.errorMsg = '处理图表数据失败: ' + error.message;
        }
      }).catch(error => {
        console.error('图表数据获取失败:', error);
        this.loading = false;
        this.error = true;
        this.errorMsg = '图表数据获取失败: ' + error.message;
      });
    },
    
    // 将原始数据转换为不同类型图表的echarts选项
    convertToChartOptions(chartData) {
      console.log('转换图表数据为echarts选项，数据:', chartData);

      // 使用当前的图表类型（可能是切换后的类型）
      const chartType = chartData.type || this.currentChartType || this.chartConfig.type;

      // 检查是否已经是完整的数据格式
      if (chartData.data && Array.isArray(chartData.data)) {
        console.log('检测到标准数据格式');
        // 根据图表类型调用不同的处理函数
        switch(chartType) {
          case 'bar':
            return this.getBarChartOptions(chartData);
          case 'line':
            return this.getLineChartOptions(chartData);
          case 'pie':
            return this.getPieChartOptions(chartData);
          case 'bar-horizontal':
            return this.getBarHorizontalOptions(chartData);
          default:
            return this.getDefaultOptions();
        }
      } else {
        console.log('检测到非标准数据格式，尝试提取数据');
        
        // 尝试从复杂对象中提取数据
        let extractedData = null;
        
        // 检查是否有data.data字段（嵌套数据结构）
        if (chartData.data && chartData.data.data) {
          console.log('从嵌套结构中提取数据');
          extractedData = {
            type: chartData.data.type || this.chartConfig.type,
            data: chartData.data.data,
            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []
          };
        }
        
        // 如果没有提取到数据，使用默认选项
        if (!extractedData) {
          console.log('无法提取数据，使用默认选项');
          return this.getDefaultOptions();
        }
        
        console.log('提取的数据:', extractedData);
        
        // 根据当前图表类型调用不同的处理函数
        const currentType = chartType || extractedData.type;
        switch(currentType) {
          case 'bar':
            return this.getBarChartOptions(extractedData);
          case 'line':
            return this.getLineChartOptions(extractedData);
          case 'pie':
            return this.getPieChartOptions(extractedData);
          case 'bar-horizontal':
            return this.getBarHorizontalOptions(extractedData);
          default:
            return this.getDefaultOptions();
        }
      }
    },
    
    // 柱状图选项生成
    getBarChartOptions(chartData) {
      console.log('生成柱状图选项，数据:', chartData);
      
      // 提取数据
      let data = [];
      let metrics = [];
      
      // 处理标准格式
      if (chartData.data && Array.isArray(chartData.data)) {
        data = chartData.data;
        metrics = chartData.metrics || [];
      } 
      // 处理DataEase返回的格式
      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
        data = chartData.data.data;
        metrics = chartData.data.fields ? 
          chartData.data.fields.filter(f => f.groupType === 'q') : [];
      }
      
      console.log('处理后的数据:', data);
      console.log('指标:', metrics);
      
      // 提取X轴数据（维度）
      const xAxisData = data.map(item => item.field || item.name);
      
      // 提取系列数据（指标）
      const series = [];
      if (data.length > 0 && data[0].series) {
        // 多系列情况
        const categoriesSet = new Set();
        data.forEach(item => {
          if (item.series) {
            item.series.forEach(s => categoriesSet.add(s.category));
          }
        });
        
        const categories = Array.from(categoriesSet);
        categories.forEach(category => {
          const seriesData = data.map(item => {
            const series = item.series?.find(s => s.category === category);
            return series ? series.value : null;
          });
          
          series.push({
            name: category,
            type: 'bar',
            data: seriesData
          });
        });
      } else {
        // 单系列情况
        series.push({
          name: metrics[0]?.name || '数值',
          type: 'bar',
          data: data.map(item => item.value)
        });
      }
      
      return {
        title: {
          text: (this.localChartConfig && this.localChartConfig.title) || this.chartConfig.title || ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: series.map(s => s.name)
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: series
      };
    },
    
    // 折线图选项生成
    getLineChartOptions(chartData) {
      const { data = [], metrics = [] } = chartData;
      
      // 提取X轴数据（维度）
      const xAxisData = data.map(item => item.field);
      
      // 提取系列数据（指标）
      const series = [];
      if (data.length > 0 && data[0].series) {
        // 多系列情况
        const categoriesSet = new Set();
        data.forEach(item => {
          if (item.series) {
            item.series.forEach(s => categoriesSet.add(s.category));
          }
        });
        
        const categories = Array.from(categoriesSet);
        categories.forEach(category => {
          const seriesData = data.map(item => {
            const series = item.series?.find(s => s.category === category);
            return series ? series.value : null;
          });
          
          series.push({
            name: category,
            type: 'line',
            data: seriesData
          });
        });
      } else {
        // 单系列情况
        series.push({
          name: metrics[0]?.name || '数值',
          type: 'line',
          data: data.map(item => item.value)
        });
      }
      
      return {
        title: {
          text: this.chartConfig.title || ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: series.map(s => s.name)
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value'
        },
        series: series
      };
    },
    
    // 饼图选项生成
    getPieChartOptions(chartData) {
      const { data = [] } = chartData;
      
      const seriesData = data.map(item => ({
        name: item.field,
        value: item.value
      }));
      
      return {
        title: {
          text: this.chartConfig.title || ''
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: seriesData.map(item => item.name)
        },
        series: [{
          name: '数据',
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: seriesData
        }]
      };
    },
    
    // 条形图选项生成（水平柱状图）
    getBarHorizontalOptions(chartData) {
      console.log('生成条形图选项，数据:', chartData);
      
      // 提取数据
      let data = [];
      let metrics = [];
      
      // 处理标准格式
      if (chartData.data && Array.isArray(chartData.data)) {
        data = chartData.data;
        metrics = chartData.metrics || [];
      } 
      // 处理DataEase返回的格式
      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {
        data = chartData.data.data;
        metrics = chartData.data.fields ? 
          chartData.data.fields.filter(f => f.groupType === 'q') : [];
      }
      
      console.log('处理后的数据:', data);
      console.log('指标:', metrics);
      
      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴
      const yAxisData = data.map(item => item.field || item.name);
      
      // 提取系列数据（指标）
      const series = [];
      if (data.length > 0 && data[0].series) {
        // 多系列情况
        const categoriesSet = new Set();
        data.forEach(item => {
          if (item.series) {
            item.series.forEach(s => categoriesSet.add(s.category));
          }
        });
        
        const categories = Array.from(categoriesSet);
        categories.forEach(category => {
          const seriesData = data.map(item => {
            const series = item.series?.find(s => s.category === category);
            return series ? series.value : null;
          });
          
          series.push({
            name: category,
            type: 'bar',  // 使用标准的'bar'类型
            data: seriesData
          });
        });
      } else {
        // 单系列情况
        series.push({
          name: metrics[0]?.name || '数值',
          type: 'bar',  // 使用标准的'bar'类型
          data: data.map(item => item.value)
        });
      }
      
      return {
        title: {
          text: this.chartConfig.title || ''
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: series.map(s => s.name)
        },
        // 关键区别：交换X轴和Y轴的配置
        xAxis: {
          type: 'value'  // 值轴在X轴
        },
        yAxis: {
          type: 'category',  // 类别轴在Y轴
          data: yAxisData
        },
        series: series
      };
    },
    
    // 获取默认图表选项
    getDefaultOptions() {
      console.log('使用默认图表选项');
      return {
        title: {
          text: this.chartConfig.title || '数据图表'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['暂无数据']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          type: this.chartConfig.type || 'bar',
          data: [0]
        }]
      };
    },
    
    // 绘制图表
    drawChart(options) {
      try {
        console.log('开始绘制图表');
        if (!this.$refs.chartRef) {
          console.error('图表DOM引用不存在');
          this.error = true;
          this.errorMsg = '图表渲染失败: DOM不存在';
          return;
        }
        
        // 如果已有实例，先销毁
        if (this.chartInstance) {
          this.chartInstance.dispose();
        }
        
        // 创建新的图表实例
        this.chartInstance = echarts.init(this.$refs.chartRef);
        this.chartInstance.setOption(options);
        console.log('图表绘制完成');
      } catch (error) {
        console.error('图表绘制失败:', error);
        this.error = true;
        this.errorMsg = '图表渲染失败: ' + error.message;
      }
    },
    
    // 调整图表大小
    resizeChart() {
      if (this.chartInstance) {
        this.chartInstance.resize();
      }
    }
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  position: relative;
  max-width: 650px;
  margin: 0 auto;
  z-index: 1;
  box-sizing: border-box;
  isolation: isolate; /* 创建新的层叠上下文 */
}

/* 图表切换工具栏样式 */
.chart-switcher {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px 8px 0 0;
  padding: 12px 16px;
  margin-bottom: 0;
}

.chart-switcher-title {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
}

.chart-type-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.chart-type-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: #fff;
  color: #495057;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-width: 70px;
  justify-content: center;
}

.chart-type-btn:hover:not(.disabled) {
  border-color: #409eff;
  color: #409eff;
  background: #ecf5ff;
}

.chart-type-btn.active {
  border-color: #409eff;
  background: #409eff;
  color: #fff;
}

.chart-type-btn.disabled {
  background: #f5f5f5;
  color: #c0c4cc;
  cursor: not-allowed;
  border-color: #e4e7ed;
}

.chart-type-btn i {
  font-size: 14px;
}

.switching-loader {
  position: absolute;
  top: 50%;
  right: 4px;
  transform: translateY(-50%);
}

.switching-loader i {
  font-size: 12px;
  animation: rotating 1s linear infinite;
}

@keyframes rotating {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.chart-canvas {
  width: 100%;
  height: 350px;
  position: relative;
  z-index: 1;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 8px 8px;
  background: #fff;
}

.chart-loading, .chart-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #909399;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  background: rgba(255,255,255,0.9);
}

.chart-loading i, .chart-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.chart-error {
  color: #F56C6C;
}
</style> 