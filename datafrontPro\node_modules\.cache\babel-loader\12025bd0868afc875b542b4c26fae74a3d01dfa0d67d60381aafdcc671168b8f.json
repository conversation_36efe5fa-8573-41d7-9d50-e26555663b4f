{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nimport ChartTypeSelector from './ChartTypeSelector.vue';\nimport { getCompatibleChartTypes, getRecommendedChartType, CHART_TYPES } from '@/utils/chartCompatibility';\nexport default {\n  name: 'ChartDisplay',\n  components: {\n    ChartTypeSelector\n  },\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 是否启用图表类型切换功能\n    enableTypeSwitch: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      // 多图表类型相关状态\n      chartData: null,\n      currentChartType: CHART_TYPES.BAR,\n      chartCompatibility: {},\n      chartInstances: {},\n      // 缓存不同类型的图表实例\n      chartLoadingStates: {},\n      // 各图表类型的加载状态\n      switchLoading: false // 切换时的加载状态\n    };\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler(newConfig, oldConfig) {\n        // 如果是新的图表配置，重新初始化\n        if (!oldConfig || newConfig.chartDataId !== oldConfig.chartDataId) {\n          this.initializeChart();\n        } else {\n          this.renderChart();\n        }\n      }\n    }\n  },\n  mounted() {\n    this.initializeChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁所有图表实例\n    this.destroyAllCharts();\n  },\n  methods: {\n    // 初始化图表（新方法）\n    async initializeChart() {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n      console.log('开始初始化图表，配置:', this.chartConfig);\n      try {\n        // 获取图表数据\n        await this.loadChartData();\n        if (this.chartData) {\n          // 分析数据兼容性\n          this.analyzeCompatibility();\n\n          // 确定默认图表类型\n          this.setDefaultChartType();\n\n          // 渲染默认图表\n          await this.renderCurrentChart();\n\n          // 异步预渲染其他兼容的图表类型\n          if (this.enableTypeSwitch) {\n            this.preRenderOtherCharts();\n          }\n        }\n      } catch (error) {\n        console.error('初始化图表失败:', error);\n        this.error = true;\n        this.errorMsg = '初始化图表失败: ' + error.message;\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 加载图表数据\n    async loadChartData() {\n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.chartData = this.chartConfig.data;\n        return;\n      }\n\n      // 如果有chartDataId，从后端获取数据\n      if (this.chartConfig.chartDataId) {\n        console.log('从后端获取图表数据，ID:', this.chartConfig.chartDataId);\n        const response = await getData(this.chartConfig);\n        console.log('获取到的数据:', response);\n        if (response && response.code === 0 && response.data) {\n          this.chartData = response.data.data || response.data;\n        } else {\n          throw new Error(response?.msg || '获取图表数据失败');\n        }\n      } else {\n        throw new Error('缺少数据源配置');\n      }\n    },\n    // 分析数据兼容性\n    analyzeCompatibility() {\n      if (!this.chartData) return;\n      this.chartCompatibility = getCompatibleChartTypes(this.chartData);\n      console.log('图表兼容性分析结果:', this.chartCompatibility);\n    },\n    // 设置默认图表类型\n    setDefaultChartType() {\n      // 如果配置中指定了类型且兼容，使用指定类型\n      if (this.chartConfig.type && this.chartCompatibility[this.chartConfig.type]?.compatible) {\n        this.currentChartType = this.chartConfig.type;\n      } else {\n        // 否则使用推荐的图表类型\n        this.currentChartType = getRecommendedChartType(this.chartData);\n      }\n      console.log('设置默认图表类型:', this.currentChartType);\n    },\n    // 渲染当前图表类型\n    async renderCurrentChart() {\n      if (!this.chartData || !this.currentChartType) return;\n      try {\n        const options = this.convertToChartOptions(this.chartData, this.currentChartType);\n        console.log('生成的echarts选项:', options);\n\n        // 渲染图表\n        this.drawChart(options, this.currentChartType);\n      } catch (error) {\n        console.error('渲染当前图表失败:', error);\n        throw error;\n      }\n    },\n    // 预渲染其他兼容的图表类型\n    async preRenderOtherCharts() {\n      const compatibleTypes = Object.keys(this.chartCompatibility).filter(type => type !== this.currentChartType && this.chartCompatibility[type].compatible);\n      console.log('开始预渲染其他图表类型:', compatibleTypes);\n\n      // 异步渲染其他类型，不阻塞当前显示\n      setTimeout(async () => {\n        for (const type of compatibleTypes) {\n          try {\n            this.$set(this.chartLoadingStates, type, true);\n            await this.renderChartType(type, false); // false表示不显示\n            this.$set(this.chartLoadingStates, type, false);\n          } catch (error) {\n            console.error(`预渲染图表类型 ${type} 失败:`, error);\n            this.$set(this.chartLoadingStates, type, false);\n          }\n        }\n      }, 100);\n    },\n    // 渲染指定类型的图表\n    async renderChartType(chartType, display = true) {\n      if (!this.chartData) return;\n      try {\n        const options = this.convertToChartOptions(this.chartData, chartType);\n        if (display) {\n          // 显示图表\n          this.drawChart(options, chartType);\n        } else {\n          // 仅创建实例缓存，不显示\n          this.createChartInstance(options, chartType);\n        }\n      } catch (error) {\n        console.error(`渲染图表类型 ${chartType} 失败:`, error);\n        throw error;\n      }\n    },\n    // 处理图表类型切换\n    async handleChartTypeChange(newType) {\n      if (newType === this.currentChartType) return;\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n      this.switchLoading = true;\n      try {\n        // 检查是否已有缓存的实例\n        if (this.chartInstances[newType]) {\n          console.log('使用缓存的图表实例');\n          this.currentChartType = newType;\n\n          // 重新绑定到当前容器\n          this.$nextTick(() => {\n            if (this.$refs.chartRef) {\n              this.chartInstance.dispose();\n              this.chartInstance = echarts.init(this.$refs.chartRef);\n              this.chartInstance.setOption(this.chartInstances[newType].getOption());\n            }\n          });\n        } else {\n          // 渲染新的图表类型\n          this.currentChartType = newType;\n          await this.renderCurrentChart();\n        }\n      } catch (error) {\n        console.error('切换图表类型失败:', error);\n        this.$message.error('切换图表类型失败: ' + error.message);\n      } finally {\n        this.switchLoading = false;\n      }\n    },\n    // 销毁所有图表实例\n    destroyAllCharts() {\n      // 销毁当前实例\n      if (this.chartInstance) {\n        this.chartInstance.dispose();\n        this.chartInstance = null;\n      }\n\n      // 销毁缓存的实例\n      Object.values(this.chartInstances).forEach(instance => {\n        if (instance) {\n          instance.dispose();\n        }\n      });\n      this.chartInstances = {};\n    },\n    // 兼容原有的renderChart方法\n    renderChart() {\n      this.initializeChart();\n    },\n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData, chartType) {\n      console.log('转换图表数据为echarts选项，数据:', chartData, '图表类型:', chartType);\n\n      // 使用传入的chartType，如果没有则使用配置中的类型\n      const type = chartType || this.chartConfig.type || this.currentChartType;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch (type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n\n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n\n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n\n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        console.log('提取的数据:', extractedData);\n\n        // 根据提取的数据类型调用不同的处理函数\n        switch (extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const {\n        data = [],\n        metrics = []\n      } = chartData;\n\n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const {\n        data = []\n      } = chartData;\n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n\n      // 提取数据\n      let data = [];\n      let metrics = [];\n\n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      }\n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n\n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n\n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          series.push({\n            name: category,\n            type: 'bar',\n            // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value' // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',\n          // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n\n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n\n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["getData", "echarts", "ChartTypeSelector", "getCompatibleChartTypes", "getRecommendedChartType", "CHART_TYPES", "name", "components", "props", "chartConfig", "type", "Object", "required", "default", "enableTypeSwitch", "Boolean", "data", "loading", "error", "errorMsg", "chartInstance", "chartData", "currentChartType", "BAR", "chartCompatibility", "chartInstances", "chartLoadingStates", "switchLoading", "watch", "deep", "handler", "newConfig", "oldConfig", "chartDataId", "initializeChart", "<PERSON><PERSON><PERSON>", "mounted", "window", "addEventListener", "resizeChart", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "destroy<PERSON>ll<PERSON><PERSON><PERSON>", "methods", "console", "log", "loadChartData", "analyzeCompatibility", "setDefaultChartType", "renderCurrentChart", "<PERSON>R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "message", "response", "code", "Error", "msg", "compatible", "options", "convertToChartOptions", "<PERSON><PERSON><PERSON>", "compatibleTypes", "keys", "filter", "setTimeout", "$set", "renderChartType", "chartType", "display", "createChartInstance", "handleChartTypeChange", "newType", "$nextTick", "$refs", "chartRef", "dispose", "init", "setOption", "getOption", "$message", "values", "for<PERSON>ach", "instance", "Array", "isArray", "getBarChartOptions", "getLineChartOptions", "getPieChartOptions", "getBarHorizontalOptions", "getDefaultOptions", "extractedData", "metrics", "fields", "f", "groupType", "xAxisData", "map", "item", "field", "series", "length", "categoriesSet", "Set", "s", "add", "category", "categories", "from", "seriesData", "find", "value", "push", "title", "text", "tooltip", "trigger", "axisPointer", "legend", "xAxis", "yAxis", "formatter", "orient", "right", "top", "radius", "avoidLabelOverlap", "label", "show", "position", "emphasis", "fontSize", "fontWeight", "labelLine", "yAxisData", "resize"], "sources": ["src/components/ChartDisplay.vue"], "sourcesContent": ["<template>\n  <div class=\"chart-container\">\n    <!-- 图表类型选择器 -->\n    <chart-type-selector\n      v-if=\"enableTypeSwitch && chartData\"\n      :current-type=\"currentChartType\"\n      :compatibility=\"chartCompatibility\"\n      :loading-states=\"chartLoadingStates\"\n      @type-change=\"handleChartTypeChange\"\n    />\n\n    <div v-if=\"loading\" class=\"chart-loading\">\n      <i class=\"el-icon-loading\"></i>\n      <span>加载图表中...</span>\n    </div>\n    <div v-else-if=\"error\" class=\"chart-error\">\n      <i class=\"el-icon-warning\"></i>\n      <span>{{ errorMsg }}</span>\n    </div>\n    <div v-else class=\"chart-wrapper\">\n      <!-- 图表切换过渡容器 -->\n      <transition name=\"chart-fade\" mode=\"out-in\">\n        <div\n          :key=\"currentChartType\"\n          ref=\"chartRef\"\n          class=\"chart-canvas\"\n          v-show=\"!switchLoading\"\n        ></div>\n      </transition>\n\n      <!-- 切换加载状态 -->\n      <div v-if=\"switchLoading\" class=\"chart-switch-loading\">\n        <i class=\"el-icon-loading\"></i>\n        <span>切换图表中...</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { getData } from '../api/index';\nimport * as echarts from 'echarts';\nimport ChartTypeSelector from './ChartTypeSelector.vue';\nimport {\n  getCompatibleChartTypes,\n  getRecommendedChartType,\n  CHART_TYPES\n} from '@/utils/chartCompatibility';\n\nexport default {\n  name: 'ChartDisplay',\n  components: {\n    ChartTypeSelector\n  },\n  props: {\n    chartConfig: {\n      type: Object,\n      required: true,\n      default: () => ({})\n    },\n    // 是否启用图表类型切换功能\n    enableTypeSwitch: {\n      type: Boolean,\n      default: true\n    }\n  },\n  data() {\n    return {\n      loading: false,\n      error: false,\n      errorMsg: '',\n      chartInstance: null,\n      // 多图表类型相关状态\n      chartData: null,\n      currentChartType: CHART_TYPES.BAR,\n      chartCompatibility: {},\n      chartInstances: {}, // 缓存不同类型的图表实例\n      chartLoadingStates: {}, // 各图表类型的加载状态\n      switchLoading: false // 切换时的加载状态\n    }\n  },\n  watch: {\n    chartConfig: {\n      deep: true,\n      handler(newConfig, oldConfig) {\n        // 如果是新的图表配置，重新初始化\n        if (!oldConfig || newConfig.chartDataId !== oldConfig.chartDataId) {\n          this.initializeChart();\n        } else {\n          this.renderChart();\n        }\n      }\n    }\n  },\n  mounted() {\n    this.initializeChart();\n    // 添加窗口大小变化监听，以便图表能够自适应\n    window.addEventListener('resize', this.resizeChart);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化监听，避免内存泄漏\n    window.removeEventListener('resize', this.resizeChart);\n    // 销毁所有图表实例\n    this.destroyAllCharts();\n  },\n  methods: {\n    // 初始化图表（新方法）\n    async initializeChart() {\n      this.loading = true;\n      this.error = false;\n\n      // 如果没有配置或缺少必要的配置，显示错误\n      if (!this.chartConfig || !this.chartConfig.type) {\n        this.loading = false;\n        this.error = true;\n        this.errorMsg = '无效的图表配置';\n        console.error('图表配置无效:', this.chartConfig);\n        return;\n      }\n\n      console.log('开始初始化图表，配置:', this.chartConfig);\n\n      try {\n        // 获取图表数据\n        await this.loadChartData();\n\n        if (this.chartData) {\n          // 分析数据兼容性\n          this.analyzeCompatibility();\n\n          // 确定默认图表类型\n          this.setDefaultChartType();\n\n          // 渲染默认图表\n          await this.renderCurrentChart();\n\n          // 异步预渲染其他兼容的图表类型\n          if (this.enableTypeSwitch) {\n            this.preRenderOtherCharts();\n          }\n        }\n      } catch (error) {\n        console.error('初始化图表失败:', error);\n        this.error = true;\n        this.errorMsg = '初始化图表失败: ' + error.message;\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 加载图表数据\n    async loadChartData() {\n      // 检查chartConfig是否已包含完整数据\n      if (this.chartConfig.data) {\n        console.log('图表配置中已包含数据，直接使用');\n        this.chartData = this.chartConfig.data;\n        return;\n      }\n\n      // 如果有chartDataId，从后端获取数据\n      if (this.chartConfig.chartDataId) {\n        console.log('从后端获取图表数据，ID:', this.chartConfig.chartDataId);\n\n        const response = await getData(this.chartConfig);\n        console.log('获取到的数据:', response);\n\n        if (response && response.code === 0 && response.data) {\n          this.chartData = response.data.data || response.data;\n        } else {\n          throw new Error(response?.msg || '获取图表数据失败');\n        }\n      } else {\n        throw new Error('缺少数据源配置');\n      }\n    },\n\n    // 分析数据兼容性\n    analyzeCompatibility() {\n      if (!this.chartData) return;\n\n      this.chartCompatibility = getCompatibleChartTypes(this.chartData);\n      console.log('图表兼容性分析结果:', this.chartCompatibility);\n    },\n\n    // 设置默认图表类型\n    setDefaultChartType() {\n      // 如果配置中指定了类型且兼容，使用指定类型\n      if (this.chartConfig.type && this.chartCompatibility[this.chartConfig.type]?.compatible) {\n        this.currentChartType = this.chartConfig.type;\n      } else {\n        // 否则使用推荐的图表类型\n        this.currentChartType = getRecommendedChartType(this.chartData);\n      }\n      console.log('设置默认图表类型:', this.currentChartType);\n    },\n\n    // 渲染当前图表类型\n    async renderCurrentChart() {\n      if (!this.chartData || !this.currentChartType) return;\n\n      try {\n        const options = this.convertToChartOptions(this.chartData, this.currentChartType);\n        console.log('生成的echarts选项:', options);\n\n        // 渲染图表\n        this.drawChart(options, this.currentChartType);\n      } catch (error) {\n        console.error('渲染当前图表失败:', error);\n        throw error;\n      }\n    },\n\n    // 预渲染其他兼容的图表类型\n    async preRenderOtherCharts() {\n      const compatibleTypes = Object.keys(this.chartCompatibility)\n        .filter(type => type !== this.currentChartType && this.chartCompatibility[type].compatible);\n\n      console.log('开始预渲染其他图表类型:', compatibleTypes);\n\n      // 异步渲染其他类型，不阻塞当前显示\n      setTimeout(async () => {\n        for (const type of compatibleTypes) {\n          try {\n            this.$set(this.chartLoadingStates, type, true);\n            await this.renderChartType(type, false); // false表示不显示\n            this.$set(this.chartLoadingStates, type, false);\n          } catch (error) {\n            console.error(`预渲染图表类型 ${type} 失败:`, error);\n            this.$set(this.chartLoadingStates, type, false);\n          }\n        }\n      }, 100);\n    },\n\n    // 渲染指定类型的图表\n    async renderChartType(chartType, display = true) {\n      if (!this.chartData) return;\n\n      try {\n        const options = this.convertToChartOptions(this.chartData, chartType);\n\n        if (display) {\n          // 显示图表\n          this.drawChart(options, chartType);\n        } else {\n          // 仅创建实例缓存，不显示\n          this.createChartInstance(options, chartType);\n        }\n      } catch (error) {\n        console.error(`渲染图表类型 ${chartType} 失败:`, error);\n        throw error;\n      }\n    },\n\n    // 处理图表类型切换\n    async handleChartTypeChange(newType) {\n      if (newType === this.currentChartType) return;\n\n      console.log('切换图表类型:', this.currentChartType, '->', newType);\n\n      this.switchLoading = true;\n\n      try {\n        // 检查是否已有缓存的实例\n        if (this.chartInstances[newType]) {\n          console.log('使用缓存的图表实例');\n          this.currentChartType = newType;\n\n          // 重新绑定到当前容器\n          this.$nextTick(() => {\n            if (this.$refs.chartRef) {\n              this.chartInstance.dispose();\n              this.chartInstance = echarts.init(this.$refs.chartRef);\n              this.chartInstance.setOption(this.chartInstances[newType].getOption());\n            }\n          });\n        } else {\n          // 渲染新的图表类型\n          this.currentChartType = newType;\n          await this.renderCurrentChart();\n        }\n      } catch (error) {\n        console.error('切换图表类型失败:', error);\n        this.$message.error('切换图表类型失败: ' + error.message);\n      } finally {\n        this.switchLoading = false;\n      }\n    },\n\n    // 销毁所有图表实例\n    destroyAllCharts() {\n      // 销毁当前实例\n      if (this.chartInstance) {\n        this.chartInstance.dispose();\n        this.chartInstance = null;\n      }\n\n      // 销毁缓存的实例\n      Object.values(this.chartInstances).forEach(instance => {\n        if (instance) {\n          instance.dispose();\n        }\n      });\n      this.chartInstances = {};\n    },\n\n    // 兼容原有的renderChart方法\n    renderChart() {\n      this.initializeChart();\n    },\n\n    // 将原始数据转换为不同类型图表的echarts选项\n    convertToChartOptions(chartData, chartType) {\n      console.log('转换图表数据为echarts选项，数据:', chartData, '图表类型:', chartType);\n\n      // 使用传入的chartType，如果没有则使用配置中的类型\n      const type = chartType || this.chartConfig.type || this.currentChartType;\n\n      // 检查是否已经是完整的数据格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        console.log('检测到标准数据格式');\n        // 根据图表类型调用不同的处理函数\n        switch(type) {\n          case 'bar':\n            return this.getBarChartOptions(chartData);\n          case 'line':\n            return this.getLineChartOptions(chartData);\n          case 'pie':\n            return this.getPieChartOptions(chartData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(chartData);\n          default:\n            return this.getDefaultOptions();\n        }\n      } else {\n        console.log('检测到非标准数据格式，尝试提取数据');\n        \n        // 尝试从复杂对象中提取数据\n        let extractedData = null;\n        \n        // 检查是否有data.data字段（嵌套数据结构）\n        if (chartData.data && chartData.data.data) {\n          console.log('从嵌套结构中提取数据');\n          extractedData = {\n            type: chartData.data.type || this.chartConfig.type,\n            data: chartData.data.data,\n            metrics: chartData.data.fields ? chartData.data.fields.filter(f => f.groupType === 'q') : []\n          };\n        }\n        \n        // 如果没有提取到数据，使用默认选项\n        if (!extractedData) {\n          console.log('无法提取数据，使用默认选项');\n          return this.getDefaultOptions();\n        }\n        \n        console.log('提取的数据:', extractedData);\n        \n        // 根据提取的数据类型调用不同的处理函数\n        switch(extractedData.type) {\n          case 'bar':\n            return this.getBarChartOptions(extractedData);\n          case 'line':\n            return this.getLineChartOptions(extractedData);\n          case 'pie':\n            return this.getPieChartOptions(extractedData);\n          case 'bar-horizontal':\n            return this.getBarHorizontalOptions(extractedData);\n          default:\n            return this.getDefaultOptions();\n        }\n      }\n    },\n    \n    // 柱状图选项生成\n    getBarChartOptions(chartData) {\n      console.log('生成柱状图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 折线图选项生成\n    getLineChartOptions(chartData) {\n      const { data = [], metrics = [] } = chartData;\n      \n      // 提取X轴数据（维度）\n      const xAxisData = data.map(item => item.field);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'line',\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'line',\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        xAxis: {\n          type: 'category',\n          data: xAxisData\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: series\n      };\n    },\n    \n    // 饼图选项生成\n    getPieChartOptions(chartData) {\n      const { data = [] } = chartData;\n      \n      const seriesData = data.map(item => ({\n        name: item.field,\n        value: item.value\n      }));\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: seriesData.map(item => item.name)\n        },\n        series: [{\n          name: '数据',\n          type: 'pie',\n          radius: ['50%', '70%'],\n          avoidLabelOverlap: false,\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '16',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: seriesData\n        }]\n      };\n    },\n    \n    // 条形图选项生成（水平柱状图）\n    getBarHorizontalOptions(chartData) {\n      console.log('生成条形图选项，数据:', chartData);\n      \n      // 提取数据\n      let data = [];\n      let metrics = [];\n      \n      // 处理标准格式\n      if (chartData.data && Array.isArray(chartData.data)) {\n        data = chartData.data;\n        metrics = chartData.metrics || [];\n      } \n      // 处理DataEase返回的格式\n      else if (chartData.data && chartData.data.data && Array.isArray(chartData.data.data)) {\n        data = chartData.data.data;\n        metrics = chartData.data.fields ? \n          chartData.data.fields.filter(f => f.groupType === 'q') : [];\n      }\n      \n      console.log('处理后的数据:', data);\n      console.log('指标:', metrics);\n      \n      // 提取Y轴数据（维度）- 注意与柱状图相比，这里是Y轴\n      const yAxisData = data.map(item => item.field || item.name);\n      \n      // 提取系列数据（指标）\n      const series = [];\n      if (data.length > 0 && data[0].series) {\n        // 多系列情况\n        const categoriesSet = new Set();\n        data.forEach(item => {\n          if (item.series) {\n            item.series.forEach(s => categoriesSet.add(s.category));\n          }\n        });\n        \n        const categories = Array.from(categoriesSet);\n        categories.forEach(category => {\n          const seriesData = data.map(item => {\n            const series = item.series?.find(s => s.category === category);\n            return series ? series.value : null;\n          });\n          \n          series.push({\n            name: category,\n            type: 'bar',  // 使用标准的'bar'类型\n            data: seriesData\n          });\n        });\n      } else {\n        // 单系列情况\n        series.push({\n          name: metrics[0]?.name || '数值',\n          type: 'bar',  // 使用标准的'bar'类型\n          data: data.map(item => item.value)\n        });\n      }\n      \n      return {\n        title: {\n          text: this.chartConfig.title || ''\n        },\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          }\n        },\n        legend: {\n          data: series.map(s => s.name)\n        },\n        // 关键区别：交换X轴和Y轴的配置\n        xAxis: {\n          type: 'value'  // 值轴在X轴\n        },\n        yAxis: {\n          type: 'category',  // 类别轴在Y轴\n          data: yAxisData\n        },\n        series: series\n      };\n    },\n    \n    // 获取默认图表选项\n    getDefaultOptions() {\n      console.log('使用默认图表选项');\n      return {\n        title: {\n          text: this.chartConfig.title || '数据图表'\n        },\n        tooltip: {\n          trigger: 'axis'\n        },\n        xAxis: {\n          type: 'category',\n          data: ['暂无数据']\n        },\n        yAxis: {\n          type: 'value'\n        },\n        series: [{\n          type: this.chartConfig.type || 'bar',\n          data: [0]\n        }]\n      };\n    },\n    \n    // 绘制图表\n    drawChart(options) {\n      try {\n        console.log('开始绘制图表');\n        if (!this.$refs.chartRef) {\n          console.error('图表DOM引用不存在');\n          this.error = true;\n          this.errorMsg = '图表渲染失败: DOM不存在';\n          return;\n        }\n        \n        // 如果已有实例，先销毁\n        if (this.chartInstance) {\n          this.chartInstance.dispose();\n        }\n        \n        // 创建新的图表实例\n        this.chartInstance = echarts.init(this.$refs.chartRef);\n        this.chartInstance.setOption(options);\n        console.log('图表绘制完成');\n      } catch (error) {\n        console.error('图表绘制失败:', error);\n        this.error = true;\n        this.errorMsg = '图表渲染失败: ' + error.message;\n      }\n    },\n    \n    // 调整图表大小\n    resizeChart() {\n      if (this.chartInstance) {\n        this.chartInstance.resize();\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.chart-container {\n  width: 100%;\n  height: 350px;\n  position: relative;\n  max-width: 650px;\n  margin: 0 auto;\n  z-index: 1;\n  box-sizing: border-box;\n  isolation: isolate; /* 创建新的层叠上下文 */\n}\n\n.chart-canvas {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  z-index: 1;\n}\n\n.chart-loading, .chart-error {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  color: #909399;\n  position: absolute;\n  top: 0;\n  left: 0;\n  z-index: 2;\n  background: rgba(255,255,255,0.9);\n}\n\n.chart-loading i, .chart-error i {\n  font-size: 24px;\n  margin-bottom: 10px;\n}\n\n.chart-error {\n  color: #F56C6C;\n}\n</style> "], "mappings": ";;;;;;;;;;;;;AAwCA,SAAAA,OAAA;AACA,YAAAC,OAAA;AACA,OAAAC,iBAAA;AACA,SACAC,uBAAA,EACAC,uBAAA,EACAC,WAAA,QACA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACAC,WAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA,EAAAA,CAAA;IACA;IACA;IACAC,gBAAA;MACAJ,IAAA,EAAAK,OAAA;MACAF,OAAA;IACA;EACA;EACAG,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,QAAA;MACAC,aAAA;MACA;MACAC,SAAA;MACAC,gBAAA,EAAAjB,WAAA,CAAAkB,GAAA;MACAC,kBAAA;MACAC,cAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACAnB,WAAA;MACAoB,IAAA;MACAC,QAAAC,SAAA,EAAAC,SAAA;QACA;QACA,KAAAA,SAAA,IAAAD,SAAA,CAAAE,WAAA,KAAAD,SAAA,CAAAC,WAAA;UACA,KAAAC,eAAA;QACA;UACA,KAAAC,WAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAF,eAAA;IACA;IACAG,MAAA,CAAAC,gBAAA,gBAAAC,WAAA;EACA;EACAC,cAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,gBAAAF,WAAA;IACA;IACA,KAAAG,gBAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAT,gBAAA;MACA,KAAAjB,OAAA;MACA,KAAAC,KAAA;;MAEA;MACA,UAAAT,WAAA,UAAAA,WAAA,CAAAC,IAAA;QACA,KAAAO,OAAA;QACA,KAAAC,KAAA;QACA,KAAAC,QAAA;QACAyB,OAAA,CAAA1B,KAAA,iBAAAT,WAAA;QACA;MACA;MAEAmC,OAAA,CAAAC,GAAA,qBAAApC,WAAA;MAEA;QACA;QACA,WAAAqC,aAAA;QAEA,SAAAzB,SAAA;UACA;UACA,KAAA0B,oBAAA;;UAEA;UACA,KAAAC,mBAAA;;UAEA;UACA,WAAAC,kBAAA;;UAEA;UACA,SAAAnC,gBAAA;YACA,KAAAoC,oBAAA;UACA;QACA;MACA,SAAAhC,KAAA;QACA0B,OAAA,CAAA1B,KAAA,aAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,iBAAAD,KAAA,CAAAiC,OAAA;MACA;QACA,KAAAlC,OAAA;MACA;IACA;IAEA;IACA,MAAA6B,cAAA;MACA;MACA,SAAArC,WAAA,CAAAO,IAAA;QACA4B,OAAA,CAAAC,GAAA;QACA,KAAAxB,SAAA,QAAAZ,WAAA,CAAAO,IAAA;QACA;MACA;;MAEA;MACA,SAAAP,WAAA,CAAAwB,WAAA;QACAW,OAAA,CAAAC,GAAA,uBAAApC,WAAA,CAAAwB,WAAA;QAEA,MAAAmB,QAAA,SAAApD,OAAA,MAAAS,WAAA;QACAmC,OAAA,CAAAC,GAAA,YAAAO,QAAA;QAEA,IAAAA,QAAA,IAAAA,QAAA,CAAAC,IAAA,UAAAD,QAAA,CAAApC,IAAA;UACA,KAAAK,SAAA,GAAA+B,QAAA,CAAApC,IAAA,CAAAA,IAAA,IAAAoC,QAAA,CAAApC,IAAA;QACA;UACA,UAAAsC,KAAA,CAAAF,QAAA,EAAAG,GAAA;QACA;MACA;QACA,UAAAD,KAAA;MACA;IACA;IAEA;IACAP,qBAAA;MACA,UAAA1B,SAAA;MAEA,KAAAG,kBAAA,GAAArB,uBAAA,MAAAkB,SAAA;MACAuB,OAAA,CAAAC,GAAA,oBAAArB,kBAAA;IACA;IAEA;IACAwB,oBAAA;MACA;MACA,SAAAvC,WAAA,CAAAC,IAAA,SAAAc,kBAAA,MAAAf,WAAA,CAAAC,IAAA,GAAA8C,UAAA;QACA,KAAAlC,gBAAA,QAAAb,WAAA,CAAAC,IAAA;MACA;QACA;QACA,KAAAY,gBAAA,GAAAlB,uBAAA,MAAAiB,SAAA;MACA;MACAuB,OAAA,CAAAC,GAAA,mBAAAvB,gBAAA;IACA;IAEA;IACA,MAAA2B,mBAAA;MACA,UAAA5B,SAAA,UAAAC,gBAAA;MAEA;QACA,MAAAmC,OAAA,QAAAC,qBAAA,MAAArC,SAAA,OAAAC,gBAAA;QACAsB,OAAA,CAAAC,GAAA,kBAAAY,OAAA;;QAEA;QACA,KAAAE,SAAA,CAAAF,OAAA,OAAAnC,gBAAA;MACA,SAAAJ,KAAA;QACA0B,OAAA,CAAA1B,KAAA,cAAAA,KAAA;QACA,MAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAgC,qBAAA;MACA,MAAAU,eAAA,GAAAjD,MAAA,CAAAkD,IAAA,MAAArC,kBAAA,EACAsC,MAAA,CAAApD,IAAA,IAAAA,IAAA,UAAAY,gBAAA,SAAAE,kBAAA,CAAAd,IAAA,EAAA8C,UAAA;MAEAZ,OAAA,CAAAC,GAAA,iBAAAe,eAAA;;MAEA;MACAG,UAAA;QACA,WAAArD,IAAA,IAAAkD,eAAA;UACA;YACA,KAAAI,IAAA,MAAAtC,kBAAA,EAAAhB,IAAA;YACA,WAAAuD,eAAA,CAAAvD,IAAA;YACA,KAAAsD,IAAA,MAAAtC,kBAAA,EAAAhB,IAAA;UACA,SAAAQ,KAAA;YACA0B,OAAA,CAAA1B,KAAA,YAAAR,IAAA,QAAAQ,KAAA;YACA,KAAA8C,IAAA,MAAAtC,kBAAA,EAAAhB,IAAA;UACA;QACA;MACA;IACA;IAEA;IACA,MAAAuD,gBAAAC,SAAA,EAAAC,OAAA;MACA,UAAA9C,SAAA;MAEA;QACA,MAAAoC,OAAA,QAAAC,qBAAA,MAAArC,SAAA,EAAA6C,SAAA;QAEA,IAAAC,OAAA;UACA;UACA,KAAAR,SAAA,CAAAF,OAAA,EAAAS,SAAA;QACA;UACA;UACA,KAAAE,mBAAA,CAAAX,OAAA,EAAAS,SAAA;QACA;MACA,SAAAhD,KAAA;QACA0B,OAAA,CAAA1B,KAAA,WAAAgD,SAAA,QAAAhD,KAAA;QACA,MAAAA,KAAA;MACA;IACA;IAEA;IACA,MAAAmD,sBAAAC,OAAA;MACA,IAAAA,OAAA,UAAAhD,gBAAA;MAEAsB,OAAA,CAAAC,GAAA,iBAAAvB,gBAAA,QAAAgD,OAAA;MAEA,KAAA3C,aAAA;MAEA;QACA;QACA,SAAAF,cAAA,CAAA6C,OAAA;UACA1B,OAAA,CAAAC,GAAA;UACA,KAAAvB,gBAAA,GAAAgD,OAAA;;UAEA;UACA,KAAAC,SAAA;YACA,SAAAC,KAAA,CAAAC,QAAA;cACA,KAAArD,aAAA,CAAAsD,OAAA;cACA,KAAAtD,aAAA,GAAAnB,OAAA,CAAA0E,IAAA,MAAAH,KAAA,CAAAC,QAAA;cACA,KAAArD,aAAA,CAAAwD,SAAA,MAAAnD,cAAA,CAAA6C,OAAA,EAAAO,SAAA;YACA;UACA;QACA;UACA;UACA,KAAAvD,gBAAA,GAAAgD,OAAA;UACA,WAAArB,kBAAA;QACA;MACA,SAAA/B,KAAA;QACA0B,OAAA,CAAA1B,KAAA,cAAAA,KAAA;QACA,KAAA4D,QAAA,CAAA5D,KAAA,gBAAAA,KAAA,CAAAiC,OAAA;MACA;QACA,KAAAxB,aAAA;MACA;IACA;IAEA;IACAe,iBAAA;MACA;MACA,SAAAtB,aAAA;QACA,KAAAA,aAAA,CAAAsD,OAAA;QACA,KAAAtD,aAAA;MACA;;MAEA;MACAT,MAAA,CAAAoE,MAAA,MAAAtD,cAAA,EAAAuD,OAAA,CAAAC,QAAA;QACA,IAAAA,QAAA;UACAA,QAAA,CAAAP,OAAA;QACA;MACA;MACA,KAAAjD,cAAA;IACA;IAEA;IACAU,YAAA;MACA,KAAAD,eAAA;IACA;IAEA;IACAwB,sBAAArC,SAAA,EAAA6C,SAAA;MACAtB,OAAA,CAAAC,GAAA,yBAAAxB,SAAA,WAAA6C,SAAA;;MAEA;MACA,MAAAxD,IAAA,GAAAwD,SAAA,SAAAzD,WAAA,CAAAC,IAAA,SAAAY,gBAAA;;MAEA;MACA,IAAAD,SAAA,CAAAL,IAAA,IAAAkE,KAAA,CAAAC,OAAA,CAAA9D,SAAA,CAAAL,IAAA;QACA4B,OAAA,CAAAC,GAAA;QACA;QACA,QAAAnC,IAAA;UACA;YACA,YAAA0E,kBAAA,CAAA/D,SAAA;UACA;YACA,YAAAgE,mBAAA,CAAAhE,SAAA;UACA;YACA,YAAAiE,kBAAA,CAAAjE,SAAA;UACA;YACA,YAAAkE,uBAAA,CAAAlE,SAAA;UACA;YACA,YAAAmE,iBAAA;QACA;MACA;QACA5C,OAAA,CAAAC,GAAA;;QAEA;QACA,IAAA4C,aAAA;;QAEA;QACA,IAAApE,SAAA,CAAAL,IAAA,IAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA;UACA4B,OAAA,CAAAC,GAAA;UACA4C,aAAA;YACA/E,IAAA,EAAAW,SAAA,CAAAL,IAAA,CAAAN,IAAA,SAAAD,WAAA,CAAAC,IAAA;YACAM,IAAA,EAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA;YACA0E,OAAA,EAAArE,SAAA,CAAAL,IAAA,CAAA2E,MAAA,GAAAtE,SAAA,CAAAL,IAAA,CAAA2E,MAAA,CAAA7B,MAAA,CAAA8B,CAAA,IAAAA,CAAA,CAAAC,SAAA;UACA;QACA;;QAEA;QACA,KAAAJ,aAAA;UACA7C,OAAA,CAAAC,GAAA;UACA,YAAA2C,iBAAA;QACA;QAEA5C,OAAA,CAAAC,GAAA,WAAA4C,aAAA;;QAEA;QACA,QAAAA,aAAA,CAAA/E,IAAA;UACA;YACA,YAAA0E,kBAAA,CAAAK,aAAA;UACA;YACA,YAAAJ,mBAAA,CAAAI,aAAA;UACA;YACA,YAAAH,kBAAA,CAAAG,aAAA;UACA;YACA,YAAAF,uBAAA,CAAAE,aAAA;UACA;YACA,YAAAD,iBAAA;QACA;MACA;IACA;IAEA;IACAJ,mBAAA/D,SAAA;MACAuB,OAAA,CAAAC,GAAA,gBAAAxB,SAAA;;MAEA;MACA,IAAAL,IAAA;MACA,IAAA0E,OAAA;;MAEA;MACA,IAAArE,SAAA,CAAAL,IAAA,IAAAkE,KAAA,CAAAC,OAAA,CAAA9D,SAAA,CAAAL,IAAA;QACAA,IAAA,GAAAK,SAAA,CAAAL,IAAA;QACA0E,OAAA,GAAArE,SAAA,CAAAqE,OAAA;MACA;MACA;MAAA,KACA,IAAArE,SAAA,CAAAL,IAAA,IAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA,IAAAkE,KAAA,CAAAC,OAAA,CAAA9D,SAAA,CAAAL,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA;QACA0E,OAAA,GAAArE,SAAA,CAAAL,IAAA,CAAA2E,MAAA,GACAtE,SAAA,CAAAL,IAAA,CAAA2E,MAAA,CAAA7B,MAAA,CAAA8B,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAjD,OAAA,CAAAC,GAAA,YAAA7B,IAAA;MACA4B,OAAA,CAAAC,GAAA,QAAA6C,OAAA;;MAEA;MACA,MAAAI,SAAA,GAAA9E,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAA1F,IAAA;;MAEA;MACA,MAAA4F,MAAA;MACA,IAAAlF,IAAA,CAAAmF,MAAA,QAAAnF,IAAA,IAAAkF,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACArF,IAAA,CAAAgE,OAAA,CAAAgB,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAlB,OAAA,CAAAsB,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAvB,KAAA,CAAAwB,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAAzB,OAAA,CAAAwB,QAAA;UACA,MAAAG,UAAA,GAAA3F,IAAA,CAAA+E,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAU,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAN,MAAA,GAAAA,MAAA,CAAAW,KAAA;UACA;UAEAX,MAAA,CAAAY,IAAA;YACAxG,IAAA,EAAAkG,QAAA;YACA9F,IAAA;YACAM,IAAA,EAAA2F;UACA;QACA;MACA;QACA;QACAT,MAAA,CAAAY,IAAA;UACAxG,IAAA,EAAAoF,OAAA,KAAApF,IAAA;UACAI,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAa,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAvG,WAAA,CAAAsG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACAzG,IAAA;UACA;QACA;QACA0G,MAAA;UACApG,IAAA,EAAAkF,MAAA,CAAAH,GAAA,CAAAO,CAAA,IAAAA,CAAA,CAAAhG,IAAA;QACA;QACA+G,KAAA;UACA3G,IAAA;UACAM,IAAA,EAAA8E;QACA;QACAwB,KAAA;UACA5G,IAAA;QACA;QACAwF,MAAA,EAAAA;MACA;IACA;IAEA;IACAb,oBAAAhE,SAAA;MACA;QAAAL,IAAA;QAAA0E,OAAA;MAAA,IAAArE,SAAA;;MAEA;MACA,MAAAyE,SAAA,GAAA9E,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA;;MAEA;MACA,MAAAC,MAAA;MACA,IAAAlF,IAAA,CAAAmF,MAAA,QAAAnF,IAAA,IAAAkF,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACArF,IAAA,CAAAgE,OAAA,CAAAgB,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAlB,OAAA,CAAAsB,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAvB,KAAA,CAAAwB,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAAzB,OAAA,CAAAwB,QAAA;UACA,MAAAG,UAAA,GAAA3F,IAAA,CAAA+E,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAU,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAN,MAAA,GAAAA,MAAA,CAAAW,KAAA;UACA;UAEAX,MAAA,CAAAY,IAAA;YACAxG,IAAA,EAAAkG,QAAA;YACA9F,IAAA;YACAM,IAAA,EAAA2F;UACA;QACA;MACA;QACA;QACAT,MAAA,CAAAY,IAAA;UACAxG,IAAA,EAAAoF,OAAA,KAAApF,IAAA;UACAI,IAAA;UACAM,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAa,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAvG,WAAA,CAAAsG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAE,MAAA;UACApG,IAAA,EAAAkF,MAAA,CAAAH,GAAA,CAAAO,CAAA,IAAAA,CAAA,CAAAhG,IAAA;QACA;QACA+G,KAAA;UACA3G,IAAA;UACAM,IAAA,EAAA8E;QACA;QACAwB,KAAA;UACA5G,IAAA;QACA;QACAwF,MAAA,EAAAA;MACA;IACA;IAEA;IACAZ,mBAAAjE,SAAA;MACA;QAAAL,IAAA;MAAA,IAAAK,SAAA;MAEA,MAAAsF,UAAA,GAAA3F,IAAA,CAAA+E,GAAA,CAAAC,IAAA;QACA1F,IAAA,EAAA0F,IAAA,CAAAC,KAAA;QACAY,KAAA,EAAAb,IAAA,CAAAa;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAvG,WAAA,CAAAsG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAK,SAAA;QACA;QACAH,MAAA;UACAI,MAAA;UACAC,KAAA;UACAC,GAAA;UACA1G,IAAA,EAAA2F,UAAA,CAAAZ,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA1F,IAAA;QACA;QACA4F,MAAA;UACA5F,IAAA;UACAI,IAAA;UACAiH,MAAA;UACAC,iBAAA;UACAC,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACAC,QAAA;YACAH,KAAA;cACAC,IAAA;cACAG,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAL,IAAA;UACA;UACA9G,IAAA,EAAA2F;QACA;MACA;IACA;IAEA;IACApB,wBAAAlE,SAAA;MACAuB,OAAA,CAAAC,GAAA,gBAAAxB,SAAA;;MAEA;MACA,IAAAL,IAAA;MACA,IAAA0E,OAAA;;MAEA;MACA,IAAArE,SAAA,CAAAL,IAAA,IAAAkE,KAAA,CAAAC,OAAA,CAAA9D,SAAA,CAAAL,IAAA;QACAA,IAAA,GAAAK,SAAA,CAAAL,IAAA;QACA0E,OAAA,GAAArE,SAAA,CAAAqE,OAAA;MACA;MACA;MAAA,KACA,IAAArE,SAAA,CAAAL,IAAA,IAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA,IAAAkE,KAAA,CAAAC,OAAA,CAAA9D,SAAA,CAAAL,IAAA,CAAAA,IAAA;QACAA,IAAA,GAAAK,SAAA,CAAAL,IAAA,CAAAA,IAAA;QACA0E,OAAA,GAAArE,SAAA,CAAAL,IAAA,CAAA2E,MAAA,GACAtE,SAAA,CAAAL,IAAA,CAAA2E,MAAA,CAAA7B,MAAA,CAAA8B,CAAA,IAAAA,CAAA,CAAAC,SAAA;MACA;MAEAjD,OAAA,CAAAC,GAAA,YAAA7B,IAAA;MACA4B,OAAA,CAAAC,GAAA,QAAA6C,OAAA;;MAEA;MACA,MAAA0C,SAAA,GAAApH,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA,IAAAD,IAAA,CAAA1F,IAAA;;MAEA;MACA,MAAA4F,MAAA;MACA,IAAAlF,IAAA,CAAAmF,MAAA,QAAAnF,IAAA,IAAAkF,MAAA;QACA;QACA,MAAAE,aAAA,OAAAC,GAAA;QACArF,IAAA,CAAAgE,OAAA,CAAAgB,IAAA;UACA,IAAAA,IAAA,CAAAE,MAAA;YACAF,IAAA,CAAAE,MAAA,CAAAlB,OAAA,CAAAsB,CAAA,IAAAF,aAAA,CAAAG,GAAA,CAAAD,CAAA,CAAAE,QAAA;UACA;QACA;QAEA,MAAAC,UAAA,GAAAvB,KAAA,CAAAwB,IAAA,CAAAN,aAAA;QACAK,UAAA,CAAAzB,OAAA,CAAAwB,QAAA;UACA,MAAAG,UAAA,GAAA3F,IAAA,CAAA+E,GAAA,CAAAC,IAAA;YACA,MAAAE,MAAA,GAAAF,IAAA,CAAAE,MAAA,EAAAU,IAAA,CAAAN,CAAA,IAAAA,CAAA,CAAAE,QAAA,KAAAA,QAAA;YACA,OAAAN,MAAA,GAAAA,MAAA,CAAAW,KAAA;UACA;UAEAX,MAAA,CAAAY,IAAA;YACAxG,IAAA,EAAAkG,QAAA;YACA9F,IAAA;YAAA;YACAM,IAAA,EAAA2F;UACA;QACA;MACA;QACA;QACAT,MAAA,CAAAY,IAAA;UACAxG,IAAA,EAAAoF,OAAA,KAAApF,IAAA;UACAI,IAAA;UAAA;UACAM,IAAA,EAAAA,IAAA,CAAA+E,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAa,KAAA;QACA;MACA;MAEA;QACAE,KAAA;UACAC,IAAA,OAAAvG,WAAA,CAAAsG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;UACAC,WAAA;YACAzG,IAAA;UACA;QACA;QACA0G,MAAA;UACApG,IAAA,EAAAkF,MAAA,CAAAH,GAAA,CAAAO,CAAA,IAAAA,CAAA,CAAAhG,IAAA;QACA;QACA;QACA+G,KAAA;UACA3G,IAAA;QACA;QACA4G,KAAA;UACA5G,IAAA;UAAA;UACAM,IAAA,EAAAoH;QACA;QACAlC,MAAA,EAAAA;MACA;IACA;IAEA;IACAV,kBAAA;MACA5C,OAAA,CAAAC,GAAA;MACA;QACAkE,KAAA;UACAC,IAAA,OAAAvG,WAAA,CAAAsG,KAAA;QACA;QACAE,OAAA;UACAC,OAAA;QACA;QACAG,KAAA;UACA3G,IAAA;UACAM,IAAA;QACA;QACAsG,KAAA;UACA5G,IAAA;QACA;QACAwF,MAAA;UACAxF,IAAA,OAAAD,WAAA,CAAAC,IAAA;UACAM,IAAA;QACA;MACA;IACA;IAEA;IACA2C,UAAAF,OAAA;MACA;QACAb,OAAA,CAAAC,GAAA;QACA,UAAA2B,KAAA,CAAAC,QAAA;UACA7B,OAAA,CAAA1B,KAAA;UACA,KAAAA,KAAA;UACA,KAAAC,QAAA;UACA;QACA;;QAEA;QACA,SAAAC,aAAA;UACA,KAAAA,aAAA,CAAAsD,OAAA;QACA;;QAEA;QACA,KAAAtD,aAAA,GAAAnB,OAAA,CAAA0E,IAAA,MAAAH,KAAA,CAAAC,QAAA;QACA,KAAArD,aAAA,CAAAwD,SAAA,CAAAnB,OAAA;QACAb,OAAA,CAAAC,GAAA;MACA,SAAA3B,KAAA;QACA0B,OAAA,CAAA1B,KAAA,YAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,QAAA,gBAAAD,KAAA,CAAAiC,OAAA;MACA;IACA;IAEA;IACAZ,YAAA;MACA,SAAAnB,aAAA;QACA,KAAAA,aAAA,CAAAiH,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}