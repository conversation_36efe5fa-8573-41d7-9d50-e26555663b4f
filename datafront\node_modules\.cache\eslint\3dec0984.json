[{"E:\\indicator-qa-service\\datafront\\src\\main.js": "1", "E:\\indicator-qa-service\\datafront\\src\\App.vue": "2", "E:\\indicator-qa-service\\datafront\\src\\components\\ChartDisplay.vue": "3", "E:\\indicator-qa-service\\datafront\\src\\api\\index.js": "4", "E:\\frontCodeCode\\datafront\\src\\main.js": "5", "E:\\frontCodeCode\\datafront\\src\\App.vue": "6", "E:\\frontCodeCode\\datafront\\src\\components\\ChartDisplay.vue": "7", "E:\\frontCodeCode\\datafront\\src\\api\\index.js": "8", "D:\\FastBI\\datafront\\src\\main.js": "9", "D:\\FastBI\\datafront\\src\\App.vue": "10", "D:\\FastBI\\datafront\\src\\components\\ChartDisplay.vue": "11", "D:\\FastBI\\datafront\\src\\api\\index.js": "12"}, {"size": 343, "mtime": 1751352483989, "results": "13", "hashOfConfig": "14"}, {"size": 71509, "mtime": 1753180258266, "results": "15", "hashOfConfig": "14"}, {"size": 15684, "mtime": 1753163173912, "results": "16", "hashOfConfig": "14"}, {"size": 3817, "mtime": 1752743839756, "results": "17", "hashOfConfig": "14"}, {"size": 343, "mtime": 1751352483989, "results": "18", "hashOfConfig": "19"}, {"size": 139548, "mtime": 1753346493065, "results": "20", "hashOfConfig": "19"}, {"size": 15947, "mtime": 1753260542700, "results": "21", "hashOfConfig": "19"}, {"size": 3817, "mtime": 1752743839756, "results": "22", "hashOfConfig": "19"}, {"size": 343, "mtime": 1751352483989, "results": "23", "hashOfConfig": "24"}, {"size": 142319, "mtime": 1753767569214, "results": "25", "hashOfConfig": "24"}, {"size": 23926, "mtime": 1753768701136, "results": "26", "hashOfConfig": "24"}, {"size": 3817, "mtime": 1752743839756, "results": "27", "hashOfConfig": "24"}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "30"}, "n5xxqj", {"filePath": "31", "messages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "35"}, {"filePath": "36", "messages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "30"}, {"filePath": "38", "messages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "40"}, "q1zo4s", {"filePath": "41", "messages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "45"}, {"filePath": "46", "messages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "40"}, {"filePath": "48", "messages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "50"}, "1wdbbvd", {"filePath": "51", "messages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "53"}, {"filePath": "54", "messages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "50"}, "E:\\indicator-qa-service\\datafront\\src\\main.js", [], [], "E:\\indicator-qa-service\\datafront\\src\\App.vue", [], "E:\\indicator-qa-service\\datafront\\src\\components\\ChartDisplay.vue", [], [], "E:\\indicator-qa-service\\datafront\\src\\api\\index.js", [], "E:\\frontCodeCode\\datafront\\src\\main.js", [], [], "E:\\frontCodeCode\\datafront\\src\\App.vue", [], "E:\\frontCodeCode\\datafront\\src\\components\\ChartDisplay.vue", [], [], "E:\\frontCodeCode\\datafront\\src\\api\\index.js", [], "D:\\FastBI\\datafront\\src\\main.js", [], [], "D:\\FastBI\\datafront\\src\\App.vue", [], [], "D:\\FastBI\\datafront\\src\\components\\ChartDisplay.vue", [], "D:\\FastBI\\datafront\\src\\api\\index.js", []]