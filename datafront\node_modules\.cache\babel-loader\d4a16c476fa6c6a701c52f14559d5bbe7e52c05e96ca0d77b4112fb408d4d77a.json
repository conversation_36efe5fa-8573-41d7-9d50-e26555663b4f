{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { makeInner } from '../util/model.js';\nimport { getDecalFromPalette } from '../model/mixin/palette.js';\nvar DEFAULT_OPTION = {\n  label: {\n    enabled: true\n  },\n  decal: {\n    show: false\n  }\n};\nvar inner = makeInner();\nvar decalPaletteScope = {};\nexport default function ariaVisual(ecModel, api) {\n  var ariaModel = ecModel.getModel('aria');\n  // See \"area enabled\" detection code in `GlobalModel.ts`.\n  if (!ariaModel.get('enabled')) {\n    return;\n  }\n  var defaultOption = zrUtil.clone(DEFAULT_OPTION);\n  zrUtil.merge(defaultOption.label, ecModel.getLocaleModel().get('aria'), false);\n  zrUtil.merge(ariaModel.option, defaultOption, false);\n  setDecal();\n  setLabel();\n  function setDecal() {\n    var decalModel = ariaModel.getModel('decal');\n    var useDecal = decalModel.get('show');\n    if (useDecal) {\n      // Each type of series use one scope.\n      // Pie and funnel are using different scopes.\n      var paletteScopeGroupByType_1 = zrUtil.createHashMap();\n      ecModel.eachSeries(function (seriesModel) {\n        if (seriesModel.isColorBySeries()) {\n          return;\n        }\n        var decalScope = paletteScopeGroupByType_1.get(seriesModel.type);\n        if (!decalScope) {\n          decalScope = {};\n          paletteScopeGroupByType_1.set(seriesModel.type, decalScope);\n        }\n        inner(seriesModel).scope = decalScope;\n      });\n      ecModel.eachRawSeries(function (seriesModel) {\n        if (ecModel.isSeriesFiltered(seriesModel)) {\n          return;\n        }\n        if (zrUtil.isFunction(seriesModel.enableAriaDecal)) {\n          // Let series define how to use decal palette on data\n          seriesModel.enableAriaDecal();\n          return;\n        }\n        var data = seriesModel.getData();\n        if (!seriesModel.isColorBySeries()) {\n          var dataAll_1 = seriesModel.getRawData();\n          var idxMap_1 = {};\n          var decalScope_1 = inner(seriesModel).scope;\n          data.each(function (idx) {\n            var rawIdx = data.getRawIndex(idx);\n            idxMap_1[rawIdx] = idx;\n          });\n          var dataCount_1 = dataAll_1.count();\n          dataAll_1.each(function (rawIdx) {\n            var idx = idxMap_1[rawIdx];\n            var name = dataAll_1.getName(rawIdx) || rawIdx + '';\n            var paletteDecal = getDecalFromPalette(seriesModel.ecModel, name, decalScope_1, dataCount_1);\n            var specifiedDecal = data.getItemVisual(idx, 'decal');\n            data.setItemVisual(idx, 'decal', mergeDecal(specifiedDecal, paletteDecal));\n          });\n        } else {\n          var paletteDecal = getDecalFromPalette(seriesModel.ecModel, seriesModel.name, decalPaletteScope, ecModel.getSeriesCount());\n          var specifiedDecal = data.getVisual('decal');\n          data.setVisual('decal', mergeDecal(specifiedDecal, paletteDecal));\n        }\n        function mergeDecal(specifiedDecal, paletteDecal) {\n          // Merge decal from palette to decal from itemStyle.\n          // User do not need to specify all of the decal props.\n          var resultDecal = specifiedDecal ? zrUtil.extend(zrUtil.extend({}, paletteDecal), specifiedDecal) : paletteDecal;\n          resultDecal.dirty = true;\n          return resultDecal;\n        }\n      });\n    }\n  }\n  function setLabel() {\n    var dom = api.getZr().dom;\n    // TODO: support for SSR\n    if (!dom) {\n      return;\n    }\n    var labelLocale = ecModel.getLocaleModel().get('aria');\n    var labelModel = ariaModel.getModel('label');\n    labelModel.option = zrUtil.defaults(labelModel.option, labelLocale);\n    if (!labelModel.get('enabled')) {\n      return;\n    }\n    dom.setAttribute('role', 'img');\n    if (labelModel.get('description')) {\n      dom.setAttribute('aria-label', labelModel.get('description'));\n      return;\n    }\n    var seriesCnt = ecModel.getSeriesCount();\n    var maxDataCnt = labelModel.get(['data', 'maxCount']) || 10;\n    var maxSeriesCnt = labelModel.get(['series', 'maxCount']) || 10;\n    var displaySeriesCnt = Math.min(seriesCnt, maxSeriesCnt);\n    var ariaLabel;\n    if (seriesCnt < 1) {\n      // No series, no aria label\n      return;\n    } else {\n      var title = getTitle();\n      if (title) {\n        var withTitle = labelModel.get(['general', 'withTitle']);\n        ariaLabel = replace(withTitle, {\n          title: title\n        });\n      } else {\n        ariaLabel = labelModel.get(['general', 'withoutTitle']);\n      }\n      var seriesLabels_1 = [];\n      var prefix = seriesCnt > 1 ? labelModel.get(['series', 'multiple', 'prefix']) : labelModel.get(['series', 'single', 'prefix']);\n      ariaLabel += replace(prefix, {\n        seriesCount: seriesCnt\n      });\n      ecModel.eachSeries(function (seriesModel, idx) {\n        if (idx < displaySeriesCnt) {\n          var seriesLabel = void 0;\n          var seriesName = seriesModel.get('name');\n          var withName = seriesName ? 'withName' : 'withoutName';\n          seriesLabel = seriesCnt > 1 ? labelModel.get(['series', 'multiple', withName]) : labelModel.get(['series', 'single', withName]);\n          seriesLabel = replace(seriesLabel, {\n            seriesId: seriesModel.seriesIndex,\n            seriesName: seriesModel.get('name'),\n            seriesType: getSeriesTypeName(seriesModel.subType)\n          });\n          var data = seriesModel.getData();\n          if (data.count() > maxDataCnt) {\n            // Show part of data\n            var partialLabel = labelModel.get(['data', 'partialData']);\n            seriesLabel += replace(partialLabel, {\n              displayCnt: maxDataCnt\n            });\n          } else {\n            seriesLabel += labelModel.get(['data', 'allData']);\n          }\n          var middleSeparator_1 = labelModel.get(['data', 'separator', 'middle']);\n          var endSeparator_1 = labelModel.get(['data', 'separator', 'end']);\n          var excludeDimensionId_1 = labelModel.get(['data', 'excludeDimensionId']);\n          var dataLabels = [];\n          for (var i = 0; i < data.count(); i++) {\n            if (i < maxDataCnt) {\n              var name_1 = data.getName(i);\n              var value = !excludeDimensionId_1 ? data.getValues(i) : zrUtil.filter(data.getValues(i), function (v, j) {\n                return zrUtil.indexOf(excludeDimensionId_1, j) === -1;\n              });\n              var dataLabel = labelModel.get(['data', name_1 ? 'withName' : 'withoutName']);\n              dataLabels.push(replace(dataLabel, {\n                name: name_1,\n                value: value.join(middleSeparator_1)\n              }));\n            }\n          }\n          seriesLabel += dataLabels.join(middleSeparator_1) + endSeparator_1;\n          seriesLabels_1.push(seriesLabel);\n        }\n      });\n      var separatorModel = labelModel.getModel(['series', 'multiple', 'separator']);\n      var middleSeparator = separatorModel.get('middle');\n      var endSeparator = separatorModel.get('end');\n      ariaLabel += seriesLabels_1.join(middleSeparator) + endSeparator;\n      dom.setAttribute('aria-label', ariaLabel);\n    }\n  }\n  function replace(str, keyValues) {\n    if (!zrUtil.isString(str)) {\n      return str;\n    }\n    var result = str;\n    zrUtil.each(keyValues, function (value, key) {\n      result = result.replace(new RegExp('\\\\{\\\\s*' + key + '\\\\s*\\\\}', 'g'), value);\n    });\n    return result;\n  }\n  function getTitle() {\n    var title = ecModel.get('title');\n    if (title && title.length) {\n      title = title[0];\n    }\n    return title && title.text;\n  }\n  function getSeriesTypeName(type) {\n    var typeNames = ecModel.getLocaleModel().get(['series', 'typeNames']);\n    return typeNames[type] || typeNames.chart;\n  }\n}", "map": {"version": 3, "names": ["zrUtil", "makeInner", "getDecalFromPalette", "DEFAULT_OPTION", "label", "enabled", "decal", "show", "inner", "decalPaletteScope", "ariaVisual", "ecModel", "api", "ariaModel", "getModel", "get", "defaultOption", "clone", "merge", "getLocaleModel", "option", "setDecal", "<PERSON><PERSON><PERSON><PERSON>", "decalModel", "useDecal", "paletteScopeGroupByType_1", "createHashMap", "eachSeries", "seriesModel", "isColorBySeries", "decalScope", "type", "set", "scope", "eachRawSeries", "isSeriesFiltered", "isFunction", "enableAriaDecal", "data", "getData", "dataAll_1", "getRawData", "idxMap_1", "decalScope_1", "each", "idx", "rawIdx", "getRawIndex", "dataCount_1", "count", "name", "getName", "paletteDecal", "specifiedDecal", "getItemVisual", "setItemVisual", "mergeDecal", "getSeriesCount", "getVisual", "setVisual", "resultDecal", "extend", "dirty", "dom", "getZr", "labelLocale", "labelModel", "defaults", "setAttribute", "seriesCnt", "maxDataCnt", "maxSeriesCnt", "displaySeriesCnt", "Math", "min", "aria<PERSON><PERSON><PERSON>", "title", "getTitle", "with<PERSON><PERSON><PERSON>", "replace", "seriesLabels_1", "prefix", "seriesCount", "seriesLabel", "seriesName", "with<PERSON><PERSON>", "seriesId", "seriesIndex", "seriesType", "getSeriesTypeName", "subType", "partialLabel", "displayCnt", "middleSeparator_1", "endSeparator_1", "excludeDimensionId_1", "dataLabels", "i", "name_1", "value", "getV<PERSON>ues", "filter", "v", "j", "indexOf", "dataLabel", "push", "join", "separatorModel", "middleSeparator", "endSeparator", "str", "keyV<PERSON><PERSON>", "isString", "result", "key", "RegExp", "length", "text", "typeNames", "chart"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/visual/aria.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { makeInner } from '../util/model.js';\nimport { getDecalFromPalette } from '../model/mixin/palette.js';\nvar DEFAULT_OPTION = {\n  label: {\n    enabled: true\n  },\n  decal: {\n    show: false\n  }\n};\nvar inner = makeInner();\nvar decalPaletteScope = {};\nexport default function ariaVisual(ecModel, api) {\n  var ariaModel = ecModel.getModel('aria');\n  // See \"area enabled\" detection code in `GlobalModel.ts`.\n  if (!ariaModel.get('enabled')) {\n    return;\n  }\n  var defaultOption = zrUtil.clone(DEFAULT_OPTION);\n  zrUtil.merge(defaultOption.label, ecModel.getLocaleModel().get('aria'), false);\n  zrUtil.merge(ariaModel.option, defaultOption, false);\n  setDecal();\n  setLabel();\n  function setDecal() {\n    var decalModel = ariaModel.getModel('decal');\n    var useDecal = decalModel.get('show');\n    if (useDecal) {\n      // Each type of series use one scope.\n      // Pie and funnel are using different scopes.\n      var paletteScopeGroupByType_1 = zrUtil.createHashMap();\n      ecModel.eachSeries(function (seriesModel) {\n        if (seriesModel.isColorBySeries()) {\n          return;\n        }\n        var decalScope = paletteScopeGroupByType_1.get(seriesModel.type);\n        if (!decalScope) {\n          decalScope = {};\n          paletteScopeGroupByType_1.set(seriesModel.type, decalScope);\n        }\n        inner(seriesModel).scope = decalScope;\n      });\n      ecModel.eachRawSeries(function (seriesModel) {\n        if (ecModel.isSeriesFiltered(seriesModel)) {\n          return;\n        }\n        if (zrUtil.isFunction(seriesModel.enableAriaDecal)) {\n          // Let series define how to use decal palette on data\n          seriesModel.enableAriaDecal();\n          return;\n        }\n        var data = seriesModel.getData();\n        if (!seriesModel.isColorBySeries()) {\n          var dataAll_1 = seriesModel.getRawData();\n          var idxMap_1 = {};\n          var decalScope_1 = inner(seriesModel).scope;\n          data.each(function (idx) {\n            var rawIdx = data.getRawIndex(idx);\n            idxMap_1[rawIdx] = idx;\n          });\n          var dataCount_1 = dataAll_1.count();\n          dataAll_1.each(function (rawIdx) {\n            var idx = idxMap_1[rawIdx];\n            var name = dataAll_1.getName(rawIdx) || rawIdx + '';\n            var paletteDecal = getDecalFromPalette(seriesModel.ecModel, name, decalScope_1, dataCount_1);\n            var specifiedDecal = data.getItemVisual(idx, 'decal');\n            data.setItemVisual(idx, 'decal', mergeDecal(specifiedDecal, paletteDecal));\n          });\n        } else {\n          var paletteDecal = getDecalFromPalette(seriesModel.ecModel, seriesModel.name, decalPaletteScope, ecModel.getSeriesCount());\n          var specifiedDecal = data.getVisual('decal');\n          data.setVisual('decal', mergeDecal(specifiedDecal, paletteDecal));\n        }\n        function mergeDecal(specifiedDecal, paletteDecal) {\n          // Merge decal from palette to decal from itemStyle.\n          // User do not need to specify all of the decal props.\n          var resultDecal = specifiedDecal ? zrUtil.extend(zrUtil.extend({}, paletteDecal), specifiedDecal) : paletteDecal;\n          resultDecal.dirty = true;\n          return resultDecal;\n        }\n      });\n    }\n  }\n  function setLabel() {\n    var dom = api.getZr().dom;\n    // TODO: support for SSR\n    if (!dom) {\n      return;\n    }\n    var labelLocale = ecModel.getLocaleModel().get('aria');\n    var labelModel = ariaModel.getModel('label');\n    labelModel.option = zrUtil.defaults(labelModel.option, labelLocale);\n    if (!labelModel.get('enabled')) {\n      return;\n    }\n    dom.setAttribute('role', 'img');\n    if (labelModel.get('description')) {\n      dom.setAttribute('aria-label', labelModel.get('description'));\n      return;\n    }\n    var seriesCnt = ecModel.getSeriesCount();\n    var maxDataCnt = labelModel.get(['data', 'maxCount']) || 10;\n    var maxSeriesCnt = labelModel.get(['series', 'maxCount']) || 10;\n    var displaySeriesCnt = Math.min(seriesCnt, maxSeriesCnt);\n    var ariaLabel;\n    if (seriesCnt < 1) {\n      // No series, no aria label\n      return;\n    } else {\n      var title = getTitle();\n      if (title) {\n        var withTitle = labelModel.get(['general', 'withTitle']);\n        ariaLabel = replace(withTitle, {\n          title: title\n        });\n      } else {\n        ariaLabel = labelModel.get(['general', 'withoutTitle']);\n      }\n      var seriesLabels_1 = [];\n      var prefix = seriesCnt > 1 ? labelModel.get(['series', 'multiple', 'prefix']) : labelModel.get(['series', 'single', 'prefix']);\n      ariaLabel += replace(prefix, {\n        seriesCount: seriesCnt\n      });\n      ecModel.eachSeries(function (seriesModel, idx) {\n        if (idx < displaySeriesCnt) {\n          var seriesLabel = void 0;\n          var seriesName = seriesModel.get('name');\n          var withName = seriesName ? 'withName' : 'withoutName';\n          seriesLabel = seriesCnt > 1 ? labelModel.get(['series', 'multiple', withName]) : labelModel.get(['series', 'single', withName]);\n          seriesLabel = replace(seriesLabel, {\n            seriesId: seriesModel.seriesIndex,\n            seriesName: seriesModel.get('name'),\n            seriesType: getSeriesTypeName(seriesModel.subType)\n          });\n          var data = seriesModel.getData();\n          if (data.count() > maxDataCnt) {\n            // Show part of data\n            var partialLabel = labelModel.get(['data', 'partialData']);\n            seriesLabel += replace(partialLabel, {\n              displayCnt: maxDataCnt\n            });\n          } else {\n            seriesLabel += labelModel.get(['data', 'allData']);\n          }\n          var middleSeparator_1 = labelModel.get(['data', 'separator', 'middle']);\n          var endSeparator_1 = labelModel.get(['data', 'separator', 'end']);\n          var excludeDimensionId_1 = labelModel.get(['data', 'excludeDimensionId']);\n          var dataLabels = [];\n          for (var i = 0; i < data.count(); i++) {\n            if (i < maxDataCnt) {\n              var name_1 = data.getName(i);\n              var value = !excludeDimensionId_1 ? data.getValues(i) : zrUtil.filter(data.getValues(i), function (v, j) {\n                return zrUtil.indexOf(excludeDimensionId_1, j) === -1;\n              });\n              var dataLabel = labelModel.get(['data', name_1 ? 'withName' : 'withoutName']);\n              dataLabels.push(replace(dataLabel, {\n                name: name_1,\n                value: value.join(middleSeparator_1)\n              }));\n            }\n          }\n          seriesLabel += dataLabels.join(middleSeparator_1) + endSeparator_1;\n          seriesLabels_1.push(seriesLabel);\n        }\n      });\n      var separatorModel = labelModel.getModel(['series', 'multiple', 'separator']);\n      var middleSeparator = separatorModel.get('middle');\n      var endSeparator = separatorModel.get('end');\n      ariaLabel += seriesLabels_1.join(middleSeparator) + endSeparator;\n      dom.setAttribute('aria-label', ariaLabel);\n    }\n  }\n  function replace(str, keyValues) {\n    if (!zrUtil.isString(str)) {\n      return str;\n    }\n    var result = str;\n    zrUtil.each(keyValues, function (value, key) {\n      result = result.replace(new RegExp('\\\\{\\\\s*' + key + '\\\\s*\\\\}', 'g'), value);\n    });\n    return result;\n  }\n  function getTitle() {\n    var title = ecModel.get('title');\n    if (title && title.length) {\n      title = title[0];\n    }\n    return title && title.text;\n  }\n  function getSeriesTypeName(type) {\n    var typeNames = ecModel.getLocaleModel().get(['series', 'typeNames']);\n    return typeNames[type] || typeNames.chart;\n  }\n}"], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,IAAIC,cAAc,GAAG;EACnBC,KAAK,EAAE;IACLC,OAAO,EAAE;EACX,CAAC;EACDC,KAAK,EAAE;IACLC,IAAI,EAAE;EACR;AACF,CAAC;AACD,IAAIC,KAAK,GAAGP,SAAS,CAAC,CAAC;AACvB,IAAIQ,iBAAiB,GAAG,CAAC,CAAC;AAC1B,eAAe,SAASC,UAAUA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAC/C,IAAIC,SAAS,GAAGF,OAAO,CAACG,QAAQ,CAAC,MAAM,CAAC;EACxC;EACA,IAAI,CAACD,SAAS,CAACE,GAAG,CAAC,SAAS,CAAC,EAAE;IAC7B;EACF;EACA,IAAIC,aAAa,GAAGhB,MAAM,CAACiB,KAAK,CAACd,cAAc,CAAC;EAChDH,MAAM,CAACkB,KAAK,CAACF,aAAa,CAACZ,KAAK,EAAEO,OAAO,CAACQ,cAAc,CAAC,CAAC,CAACJ,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC;EAC9Ef,MAAM,CAACkB,KAAK,CAACL,SAAS,CAACO,MAAM,EAAEJ,aAAa,EAAE,KAAK,CAAC;EACpDK,QAAQ,CAAC,CAAC;EACVC,QAAQ,CAAC,CAAC;EACV,SAASD,QAAQA,CAAA,EAAG;IAClB,IAAIE,UAAU,GAAGV,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC;IAC5C,IAAIU,QAAQ,GAAGD,UAAU,CAACR,GAAG,CAAC,MAAM,CAAC;IACrC,IAAIS,QAAQ,EAAE;MACZ;MACA;MACA,IAAIC,yBAAyB,GAAGzB,MAAM,CAAC0B,aAAa,CAAC,CAAC;MACtDf,OAAO,CAACgB,UAAU,CAAC,UAAUC,WAAW,EAAE;QACxC,IAAIA,WAAW,CAACC,eAAe,CAAC,CAAC,EAAE;UACjC;QACF;QACA,IAAIC,UAAU,GAAGL,yBAAyB,CAACV,GAAG,CAACa,WAAW,CAACG,IAAI,CAAC;QAChE,IAAI,CAACD,UAAU,EAAE;UACfA,UAAU,GAAG,CAAC,CAAC;UACfL,yBAAyB,CAACO,GAAG,CAACJ,WAAW,CAACG,IAAI,EAAED,UAAU,CAAC;QAC7D;QACAtB,KAAK,CAACoB,WAAW,CAAC,CAACK,KAAK,GAAGH,UAAU;MACvC,CAAC,CAAC;MACFnB,OAAO,CAACuB,aAAa,CAAC,UAAUN,WAAW,EAAE;QAC3C,IAAIjB,OAAO,CAACwB,gBAAgB,CAACP,WAAW,CAAC,EAAE;UACzC;QACF;QACA,IAAI5B,MAAM,CAACoC,UAAU,CAACR,WAAW,CAACS,eAAe,CAAC,EAAE;UAClD;UACAT,WAAW,CAACS,eAAe,CAAC,CAAC;UAC7B;QACF;QACA,IAAIC,IAAI,GAAGV,WAAW,CAACW,OAAO,CAAC,CAAC;QAChC,IAAI,CAACX,WAAW,CAACC,eAAe,CAAC,CAAC,EAAE;UAClC,IAAIW,SAAS,GAAGZ,WAAW,CAACa,UAAU,CAAC,CAAC;UACxC,IAAIC,QAAQ,GAAG,CAAC,CAAC;UACjB,IAAIC,YAAY,GAAGnC,KAAK,CAACoB,WAAW,CAAC,CAACK,KAAK;UAC3CK,IAAI,CAACM,IAAI,CAAC,UAAUC,GAAG,EAAE;YACvB,IAAIC,MAAM,GAAGR,IAAI,CAACS,WAAW,CAACF,GAAG,CAAC;YAClCH,QAAQ,CAACI,MAAM,CAAC,GAAGD,GAAG;UACxB,CAAC,CAAC;UACF,IAAIG,WAAW,GAAGR,SAAS,CAACS,KAAK,CAAC,CAAC;UACnCT,SAAS,CAACI,IAAI,CAAC,UAAUE,MAAM,EAAE;YAC/B,IAAID,GAAG,GAAGH,QAAQ,CAACI,MAAM,CAAC;YAC1B,IAAII,IAAI,GAAGV,SAAS,CAACW,OAAO,CAACL,MAAM,CAAC,IAAIA,MAAM,GAAG,EAAE;YACnD,IAAIM,YAAY,GAAGlD,mBAAmB,CAAC0B,WAAW,CAACjB,OAAO,EAAEuC,IAAI,EAAEP,YAAY,EAAEK,WAAW,CAAC;YAC5F,IAAIK,cAAc,GAAGf,IAAI,CAACgB,aAAa,CAACT,GAAG,EAAE,OAAO,CAAC;YACrDP,IAAI,CAACiB,aAAa,CAACV,GAAG,EAAE,OAAO,EAAEW,UAAU,CAACH,cAAc,EAAED,YAAY,CAAC,CAAC;UAC5E,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAIA,YAAY,GAAGlD,mBAAmB,CAAC0B,WAAW,CAACjB,OAAO,EAAEiB,WAAW,CAACsB,IAAI,EAAEzC,iBAAiB,EAAEE,OAAO,CAAC8C,cAAc,CAAC,CAAC,CAAC;UAC1H,IAAIJ,cAAc,GAAGf,IAAI,CAACoB,SAAS,CAAC,OAAO,CAAC;UAC5CpB,IAAI,CAACqB,SAAS,CAAC,OAAO,EAAEH,UAAU,CAACH,cAAc,EAAED,YAAY,CAAC,CAAC;QACnE;QACA,SAASI,UAAUA,CAACH,cAAc,EAAED,YAAY,EAAE;UAChD;UACA;UACA,IAAIQ,WAAW,GAAGP,cAAc,GAAGrD,MAAM,CAAC6D,MAAM,CAAC7D,MAAM,CAAC6D,MAAM,CAAC,CAAC,CAAC,EAAET,YAAY,CAAC,EAAEC,cAAc,CAAC,GAAGD,YAAY;UAChHQ,WAAW,CAACE,KAAK,GAAG,IAAI;UACxB,OAAOF,WAAW;QACpB;MACF,CAAC,CAAC;IACJ;EACF;EACA,SAAStC,QAAQA,CAAA,EAAG;IAClB,IAAIyC,GAAG,GAAGnD,GAAG,CAACoD,KAAK,CAAC,CAAC,CAACD,GAAG;IACzB;IACA,IAAI,CAACA,GAAG,EAAE;MACR;IACF;IACA,IAAIE,WAAW,GAAGtD,OAAO,CAACQ,cAAc,CAAC,CAAC,CAACJ,GAAG,CAAC,MAAM,CAAC;IACtD,IAAImD,UAAU,GAAGrD,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC;IAC5CoD,UAAU,CAAC9C,MAAM,GAAGpB,MAAM,CAACmE,QAAQ,CAACD,UAAU,CAAC9C,MAAM,EAAE6C,WAAW,CAAC;IACnE,IAAI,CAACC,UAAU,CAACnD,GAAG,CAAC,SAAS,CAAC,EAAE;MAC9B;IACF;IACAgD,GAAG,CAACK,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;IAC/B,IAAIF,UAAU,CAACnD,GAAG,CAAC,aAAa,CAAC,EAAE;MACjCgD,GAAG,CAACK,YAAY,CAAC,YAAY,EAAEF,UAAU,CAACnD,GAAG,CAAC,aAAa,CAAC,CAAC;MAC7D;IACF;IACA,IAAIsD,SAAS,GAAG1D,OAAO,CAAC8C,cAAc,CAAC,CAAC;IACxC,IAAIa,UAAU,GAAGJ,UAAU,CAACnD,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,IAAI,EAAE;IAC3D,IAAIwD,YAAY,GAAGL,UAAU,CAACnD,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,IAAI,EAAE;IAC/D,IAAIyD,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAACL,SAAS,EAAEE,YAAY,CAAC;IACxD,IAAII,SAAS;IACb,IAAIN,SAAS,GAAG,CAAC,EAAE;MACjB;MACA;IACF,CAAC,MAAM;MACL,IAAIO,KAAK,GAAGC,QAAQ,CAAC,CAAC;MACtB,IAAID,KAAK,EAAE;QACT,IAAIE,SAAS,GAAGZ,UAAU,CAACnD,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QACxD4D,SAAS,GAAGI,OAAO,CAACD,SAAS,EAAE;UAC7BF,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACLD,SAAS,GAAGT,UAAU,CAACnD,GAAG,CAAC,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;MACzD;MACA,IAAIiE,cAAc,GAAG,EAAE;MACvB,IAAIC,MAAM,GAAGZ,SAAS,GAAG,CAAC,GAAGH,UAAU,CAACnD,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,GAAGmD,UAAU,CAACnD,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;MAC9H4D,SAAS,IAAII,OAAO,CAACE,MAAM,EAAE;QAC3BC,WAAW,EAAEb;MACf,CAAC,CAAC;MACF1D,OAAO,CAACgB,UAAU,CAAC,UAAUC,WAAW,EAAEiB,GAAG,EAAE;QAC7C,IAAIA,GAAG,GAAG2B,gBAAgB,EAAE;UAC1B,IAAIW,WAAW,GAAG,KAAK,CAAC;UACxB,IAAIC,UAAU,GAAGxD,WAAW,CAACb,GAAG,CAAC,MAAM,CAAC;UACxC,IAAIsE,QAAQ,GAAGD,UAAU,GAAG,UAAU,GAAG,aAAa;UACtDD,WAAW,GAAGd,SAAS,GAAG,CAAC,GAAGH,UAAU,CAACnD,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAEsE,QAAQ,CAAC,CAAC,GAAGnB,UAAU,CAACnD,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAEsE,QAAQ,CAAC,CAAC;UAC/HF,WAAW,GAAGJ,OAAO,CAACI,WAAW,EAAE;YACjCG,QAAQ,EAAE1D,WAAW,CAAC2D,WAAW;YACjCH,UAAU,EAAExD,WAAW,CAACb,GAAG,CAAC,MAAM,CAAC;YACnCyE,UAAU,EAAEC,iBAAiB,CAAC7D,WAAW,CAAC8D,OAAO;UACnD,CAAC,CAAC;UACF,IAAIpD,IAAI,GAAGV,WAAW,CAACW,OAAO,CAAC,CAAC;UAChC,IAAID,IAAI,CAACW,KAAK,CAAC,CAAC,GAAGqB,UAAU,EAAE;YAC7B;YACA,IAAIqB,YAAY,GAAGzB,UAAU,CAACnD,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;YAC1DoE,WAAW,IAAIJ,OAAO,CAACY,YAAY,EAAE;cACnCC,UAAU,EAAEtB;YACd,CAAC,CAAC;UACJ,CAAC,MAAM;YACLa,WAAW,IAAIjB,UAAU,CAACnD,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;UACpD;UACA,IAAI8E,iBAAiB,GAAG3B,UAAU,CAACnD,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;UACvE,IAAI+E,cAAc,GAAG5B,UAAU,CAACnD,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;UACjE,IAAIgF,oBAAoB,GAAG7B,UAAU,CAACnD,GAAG,CAAC,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;UACzE,IAAIiF,UAAU,GAAG,EAAE;UACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,IAAI,CAACW,KAAK,CAAC,CAAC,EAAEgD,CAAC,EAAE,EAAE;YACrC,IAAIA,CAAC,GAAG3B,UAAU,EAAE;cAClB,IAAI4B,MAAM,GAAG5D,IAAI,CAACa,OAAO,CAAC8C,CAAC,CAAC;cAC5B,IAAIE,KAAK,GAAG,CAACJ,oBAAoB,GAAGzD,IAAI,CAAC8D,SAAS,CAACH,CAAC,CAAC,GAAGjG,MAAM,CAACqG,MAAM,CAAC/D,IAAI,CAAC8D,SAAS,CAACH,CAAC,CAAC,EAAE,UAAUK,CAAC,EAAEC,CAAC,EAAE;gBACvG,OAAOvG,MAAM,CAACwG,OAAO,CAACT,oBAAoB,EAAEQ,CAAC,CAAC,KAAK,CAAC,CAAC;cACvD,CAAC,CAAC;cACF,IAAIE,SAAS,GAAGvC,UAAU,CAACnD,GAAG,CAAC,CAAC,MAAM,EAAEmF,MAAM,GAAG,UAAU,GAAG,aAAa,CAAC,CAAC;cAC7EF,UAAU,CAACU,IAAI,CAAC3B,OAAO,CAAC0B,SAAS,EAAE;gBACjCvD,IAAI,EAAEgD,MAAM;gBACZC,KAAK,EAAEA,KAAK,CAACQ,IAAI,CAACd,iBAAiB;cACrC,CAAC,CAAC,CAAC;YACL;UACF;UACAV,WAAW,IAAIa,UAAU,CAACW,IAAI,CAACd,iBAAiB,CAAC,GAAGC,cAAc;UAClEd,cAAc,CAAC0B,IAAI,CAACvB,WAAW,CAAC;QAClC;MACF,CAAC,CAAC;MACF,IAAIyB,cAAc,GAAG1C,UAAU,CAACpD,QAAQ,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;MAC7E,IAAI+F,eAAe,GAAGD,cAAc,CAAC7F,GAAG,CAAC,QAAQ,CAAC;MAClD,IAAI+F,YAAY,GAAGF,cAAc,CAAC7F,GAAG,CAAC,KAAK,CAAC;MAC5C4D,SAAS,IAAIK,cAAc,CAAC2B,IAAI,CAACE,eAAe,CAAC,GAAGC,YAAY;MAChE/C,GAAG,CAACK,YAAY,CAAC,YAAY,EAAEO,SAAS,CAAC;IAC3C;EACF;EACA,SAASI,OAAOA,CAACgC,GAAG,EAAEC,SAAS,EAAE;IAC/B,IAAI,CAAChH,MAAM,CAACiH,QAAQ,CAACF,GAAG,CAAC,EAAE;MACzB,OAAOA,GAAG;IACZ;IACA,IAAIG,MAAM,GAAGH,GAAG;IAChB/G,MAAM,CAAC4C,IAAI,CAACoE,SAAS,EAAE,UAAUb,KAAK,EAAEgB,GAAG,EAAE;MAC3CD,MAAM,GAAGA,MAAM,CAACnC,OAAO,CAAC,IAAIqC,MAAM,CAAC,SAAS,GAAGD,GAAG,GAAG,SAAS,EAAE,GAAG,CAAC,EAAEhB,KAAK,CAAC;IAC9E,CAAC,CAAC;IACF,OAAOe,MAAM;EACf;EACA,SAASrC,QAAQA,CAAA,EAAG;IAClB,IAAID,KAAK,GAAGjE,OAAO,CAACI,GAAG,CAAC,OAAO,CAAC;IAChC,IAAI6D,KAAK,IAAIA,KAAK,CAACyC,MAAM,EAAE;MACzBzC,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;IAClB;IACA,OAAOA,KAAK,IAAIA,KAAK,CAAC0C,IAAI;EAC5B;EACA,SAAS7B,iBAAiBA,CAAC1D,IAAI,EAAE;IAC/B,IAAIwF,SAAS,GAAG5G,OAAO,CAACQ,cAAc,CAAC,CAAC,CAACJ,GAAG,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;IACrE,OAAOwG,SAAS,CAACxF,IAAI,CAAC,IAAIwF,SAAS,CAACC,KAAK;EAC3C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}