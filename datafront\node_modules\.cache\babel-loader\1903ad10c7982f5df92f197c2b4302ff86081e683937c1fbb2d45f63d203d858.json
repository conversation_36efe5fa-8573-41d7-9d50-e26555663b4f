{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { use } from '../../extension.js';\nimport { install as installDataZoomSelect } from '../../component/dataZoom/installDataZoomSelect.js';\nimport ToolboxModel from './ToolboxModel.js';\nimport ToolboxView from './ToolboxView.js';\n// TODOD: REGISTER IN INSTALL\nimport { registerFeature } from './featureManager.js';\nimport SaveAsImage from './feature/SaveAsImage.js';\nimport MagicType from './feature/MagicType.js';\nimport DataView from './feature/DataView.js';\nimport Restore from './feature/Restore.js';\nimport DataZoom from './feature/DataZoom.js';\nexport function install(registers) {\n  registers.registerComponentModel(ToolboxModel);\n  registers.registerComponentView(ToolboxView);\n  registerFeature('saveAsImage', SaveAsImage);\n  registerFeature('magicType', MagicType);\n  registerFeature('dataView', DataView);\n  registerFeature('dataZoom', DataZoom);\n  registerFeature('restore', Restore);\n  use(installDataZoomSelect);\n}", "map": {"version": 3, "names": ["use", "install", "installDataZoomSelect", "ToolboxModel", "ToolboxView", "registerFeature", "SaveAsImage", "MagicType", "DataView", "Rest<PERSON>", "DataZoom", "registers", "registerComponentModel", "registerComponentView"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/component/toolbox/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { use } from '../../extension.js';\nimport { install as installDataZoomSelect } from '../../component/dataZoom/installDataZoomSelect.js';\nimport ToolboxModel from './ToolboxModel.js';\nimport ToolboxView from './ToolboxView.js';\n// TODOD: REGISTER IN INSTALL\nimport { registerFeature } from './featureManager.js';\nimport SaveAsImage from './feature/SaveAsImage.js';\nimport MagicType from './feature/MagicType.js';\nimport DataView from './feature/DataView.js';\nimport Restore from './feature/Restore.js';\nimport DataZoom from './feature/DataZoom.js';\nexport function install(registers) {\n  registers.registerComponentModel(ToolboxModel);\n  registers.registerComponentView(ToolboxView);\n  registerFeature('saveAsImage', SaveAsImage);\n  registerFeature('magicType', MagicType);\n  registerFeature('dataView', DataView);\n  registerFeature('dataZoom', DataZoom);\n  registerFeature('restore', Restore);\n  use(installDataZoomSelect);\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAG,QAAQ,oBAAoB;AACxC,SAASC,OAAO,IAAIC,qBAAqB,QAAQ,mDAAmD;AACpG,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C;AACA,SAASC,eAAe,QAAQ,qBAAqB;AACrD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAO,SAAST,OAAOA,CAACU,SAAS,EAAE;EACjCA,SAAS,CAACC,sBAAsB,CAACT,YAAY,CAAC;EAC9CQ,SAAS,CAACE,qBAAqB,CAACT,WAAW,CAAC;EAC5CC,eAAe,CAAC,aAAa,EAAEC,WAAW,CAAC;EAC3CD,eAAe,CAAC,WAAW,EAAEE,SAAS,CAAC;EACvCF,eAAe,CAAC,UAAU,EAAEG,QAAQ,CAAC;EACrCH,eAAe,CAAC,UAAU,EAAEK,QAAQ,CAAC;EACrCL,eAAe,CAAC,SAAS,EAAEI,OAAO,CAAC;EACnCT,GAAG,CAACE,qBAAqB,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}