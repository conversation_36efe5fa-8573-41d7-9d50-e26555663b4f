{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentView from '../../view/Component.js';\nimport { each, bind, extend } from 'zrender/lib/core/util.js';\nimport { createOrUpdate, clear } from '../../util/throttle.js';\nvar CLICK_THRESHOLD = 5; // > 4\nvar ParallelView = /** @class */function (_super) {\n  __extends(ParallelView, _super);\n  function ParallelView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelView.type;\n    return _this;\n  }\n  ParallelView.prototype.render = function (parallelModel, ecModel, api) {\n    this._model = parallelModel;\n    this._api = api;\n    if (!this._handlers) {\n      this._handlers = {};\n      each(handlers, function (handler, eventName) {\n        api.getZr().on(eventName, this._handlers[eventName] = bind(handler, this));\n      }, this);\n    }\n    createOrUpdate(this, '_throttledDispatchExpand', parallelModel.get('axisExpandRate'), 'fixRate');\n  };\n  ParallelView.prototype.dispose = function (ecModel, api) {\n    clear(this, '_throttledDispatchExpand');\n    each(this._handlers, function (handler, eventName) {\n      api.getZr().off(eventName, handler);\n    });\n    this._handlers = null;\n  };\n  /**\r\n   * @internal\r\n   * @param {Object} [opt] If null, cancel the last action triggering for debounce.\r\n   */\n  ParallelView.prototype._throttledDispatchExpand = function (opt) {\n    this._dispatchExpand(opt);\n  };\n  /**\r\n   * @internal\r\n   */\n  ParallelView.prototype._dispatchExpand = function (opt) {\n    opt && this._api.dispatchAction(extend({\n      type: 'parallelAxisExpand'\n    }, opt));\n  };\n  ParallelView.type = 'parallel';\n  return ParallelView;\n}(ComponentView);\nvar handlers = {\n  mousedown: function (e) {\n    if (checkTrigger(this, 'click')) {\n      this._mouseDownPoint = [e.offsetX, e.offsetY];\n    }\n  },\n  mouseup: function (e) {\n    var mouseDownPoint = this._mouseDownPoint;\n    if (checkTrigger(this, 'click') && mouseDownPoint) {\n      var point = [e.offsetX, e.offsetY];\n      var dist = Math.pow(mouseDownPoint[0] - point[0], 2) + Math.pow(mouseDownPoint[1] - point[1], 2);\n      if (dist > CLICK_THRESHOLD) {\n        return;\n      }\n      var result = this._model.coordinateSystem.getSlidedAxisExpandWindow([e.offsetX, e.offsetY]);\n      result.behavior !== 'none' && this._dispatchExpand({\n        axisExpandWindow: result.axisExpandWindow\n      });\n    }\n    this._mouseDownPoint = null;\n  },\n  mousemove: function (e) {\n    // Should do nothing when brushing.\n    if (this._mouseDownPoint || !checkTrigger(this, 'mousemove')) {\n      return;\n    }\n    var model = this._model;\n    var result = model.coordinateSystem.getSlidedAxisExpandWindow([e.offsetX, e.offsetY]);\n    var behavior = result.behavior;\n    behavior === 'jump' && this._throttledDispatchExpand.debounceNextCall(model.get('axisExpandDebounce'));\n    this._throttledDispatchExpand(behavior === 'none' ? null // Cancel the last trigger, in case that mouse slide out of the area quickly.\n    : {\n      axisExpandWindow: result.axisExpandWindow,\n      // Jumping uses animation, and sliding suppresses animation.\n      animation: behavior === 'jump' ? null : {\n        duration: 0 // Disable animation.\n      }\n    });\n  }\n};\nfunction checkTrigger(view, triggerOn) {\n  var model = view._model;\n  return model.get('axisExpandable') && model.get('axisExpandTriggerOn') === triggerOn;\n}\nexport default ParallelView;", "map": {"version": 3, "names": ["__extends", "ComponentView", "each", "bind", "extend", "createOrUpdate", "clear", "CLICK_THRESHOLD", "<PERSON><PERSON><PERSON><PERSON>iew", "_super", "_this", "apply", "arguments", "type", "prototype", "render", "parallelModel", "ecModel", "api", "_model", "_api", "_handlers", "handlers", "handler", "eventName", "getZr", "on", "get", "dispose", "off", "_throttledDispatchExpand", "opt", "_dispatchExpand", "dispatchAction", "mousedown", "e", "checkTrigger", "_mouseDownPoint", "offsetX", "offsetY", "mouseup", "mouseDownPoint", "point", "dist", "Math", "pow", "result", "coordinateSystem", "getSlidedAxisExpandWindow", "behavior", "axisExpandWindow", "mousemove", "model", "debounceNextCall", "animation", "duration", "view", "triggerOn"], "sources": ["E:/AllProject/datafront/node_modules/echarts/lib/component/parallel/ParallelView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentView from '../../view/Component.js';\nimport { each, bind, extend } from 'zrender/lib/core/util.js';\nimport { createOrUpdate, clear } from '../../util/throttle.js';\nvar CLICK_THRESHOLD = 5; // > 4\nvar ParallelView = /** @class */function (_super) {\n  __extends(ParallelView, _super);\n  function ParallelView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelView.type;\n    return _this;\n  }\n  ParallelView.prototype.render = function (parallelModel, ecModel, api) {\n    this._model = parallelModel;\n    this._api = api;\n    if (!this._handlers) {\n      this._handlers = {};\n      each(handlers, function (handler, eventName) {\n        api.getZr().on(eventName, this._handlers[eventName] = bind(handler, this));\n      }, this);\n    }\n    createOrUpdate(this, '_throttledDispatchExpand', parallelModel.get('axisExpandRate'), 'fixRate');\n  };\n  ParallelView.prototype.dispose = function (ecModel, api) {\n    clear(this, '_throttledDispatchExpand');\n    each(this._handlers, function (handler, eventName) {\n      api.getZr().off(eventName, handler);\n    });\n    this._handlers = null;\n  };\n  /**\r\n   * @internal\r\n   * @param {Object} [opt] If null, cancel the last action triggering for debounce.\r\n   */\n  ParallelView.prototype._throttledDispatchExpand = function (opt) {\n    this._dispatchExpand(opt);\n  };\n  /**\r\n   * @internal\r\n   */\n  ParallelView.prototype._dispatchExpand = function (opt) {\n    opt && this._api.dispatchAction(extend({\n      type: 'parallelAxisExpand'\n    }, opt));\n  };\n  ParallelView.type = 'parallel';\n  return ParallelView;\n}(ComponentView);\nvar handlers = {\n  mousedown: function (e) {\n    if (checkTrigger(this, 'click')) {\n      this._mouseDownPoint = [e.offsetX, e.offsetY];\n    }\n  },\n  mouseup: function (e) {\n    var mouseDownPoint = this._mouseDownPoint;\n    if (checkTrigger(this, 'click') && mouseDownPoint) {\n      var point = [e.offsetX, e.offsetY];\n      var dist = Math.pow(mouseDownPoint[0] - point[0], 2) + Math.pow(mouseDownPoint[1] - point[1], 2);\n      if (dist > CLICK_THRESHOLD) {\n        return;\n      }\n      var result = this._model.coordinateSystem.getSlidedAxisExpandWindow([e.offsetX, e.offsetY]);\n      result.behavior !== 'none' && this._dispatchExpand({\n        axisExpandWindow: result.axisExpandWindow\n      });\n    }\n    this._mouseDownPoint = null;\n  },\n  mousemove: function (e) {\n    // Should do nothing when brushing.\n    if (this._mouseDownPoint || !checkTrigger(this, 'mousemove')) {\n      return;\n    }\n    var model = this._model;\n    var result = model.coordinateSystem.getSlidedAxisExpandWindow([e.offsetX, e.offsetY]);\n    var behavior = result.behavior;\n    behavior === 'jump' && this._throttledDispatchExpand.debounceNextCall(model.get('axisExpandDebounce'));\n    this._throttledDispatchExpand(behavior === 'none' ? null // Cancel the last trigger, in case that mouse slide out of the area quickly.\n    : {\n      axisExpandWindow: result.axisExpandWindow,\n      // Jumping uses animation, and sliding suppresses animation.\n      animation: behavior === 'jump' ? null : {\n        duration: 0 // Disable animation.\n      }\n    });\n  }\n};\nfunction checkTrigger(view, triggerOn) {\n  var model = view._model;\n  return model.get('axisExpandable') && model.get('axisExpandTriggerOn') === triggerOn;\n}\nexport default ParallelView;"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,QAAQ,0BAA0B;AAC7D,SAASC,cAAc,EAAEC,KAAK,QAAQ,wBAAwB;AAC9D,IAAIC,eAAe,GAAG,CAAC,CAAC,CAAC;AACzB,IAAIC,YAAY,GAAG,aAAa,UAAUC,MAAM,EAAE;EAChDT,SAAS,CAACQ,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,IAAI,GAAGL,YAAY,CAACK,IAAI;IAC9B,OAAOH,KAAK;EACd;EACAF,YAAY,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUC,aAAa,EAAEC,OAAO,EAAEC,GAAG,EAAE;IACrE,IAAI,CAACC,MAAM,GAAGH,aAAa;IAC3B,IAAI,CAACI,IAAI,GAAGF,GAAG;IACf,IAAI,CAAC,IAAI,CAACG,SAAS,EAAE;MACnB,IAAI,CAACA,SAAS,GAAG,CAAC,CAAC;MACnBnB,IAAI,CAACoB,QAAQ,EAAE,UAAUC,OAAO,EAAEC,SAAS,EAAE;QAC3CN,GAAG,CAACO,KAAK,CAAC,CAAC,CAACC,EAAE,CAACF,SAAS,EAAE,IAAI,CAACH,SAAS,CAACG,SAAS,CAAC,GAAGrB,IAAI,CAACoB,OAAO,EAAE,IAAI,CAAC,CAAC;MAC5E,CAAC,EAAE,IAAI,CAAC;IACV;IACAlB,cAAc,CAAC,IAAI,EAAE,0BAA0B,EAAEW,aAAa,CAACW,GAAG,CAAC,gBAAgB,CAAC,EAAE,SAAS,CAAC;EAClG,CAAC;EACDnB,YAAY,CAACM,SAAS,CAACc,OAAO,GAAG,UAAUX,OAAO,EAAEC,GAAG,EAAE;IACvDZ,KAAK,CAAC,IAAI,EAAE,0BAA0B,CAAC;IACvCJ,IAAI,CAAC,IAAI,CAACmB,SAAS,EAAE,UAAUE,OAAO,EAAEC,SAAS,EAAE;MACjDN,GAAG,CAACO,KAAK,CAAC,CAAC,CAACI,GAAG,CAACL,SAAS,EAAED,OAAO,CAAC;IACrC,CAAC,CAAC;IACF,IAAI,CAACF,SAAS,GAAG,IAAI;EACvB,CAAC;EACD;AACF;AACA;AACA;EACEb,YAAY,CAACM,SAAS,CAACgB,wBAAwB,GAAG,UAAUC,GAAG,EAAE;IAC/D,IAAI,CAACC,eAAe,CAACD,GAAG,CAAC;EAC3B,CAAC;EACD;AACF;AACA;EACEvB,YAAY,CAACM,SAAS,CAACkB,eAAe,GAAG,UAAUD,GAAG,EAAE;IACtDA,GAAG,IAAI,IAAI,CAACX,IAAI,CAACa,cAAc,CAAC7B,MAAM,CAAC;MACrCS,IAAI,EAAE;IACR,CAAC,EAAEkB,GAAG,CAAC,CAAC;EACV,CAAC;EACDvB,YAAY,CAACK,IAAI,GAAG,UAAU;EAC9B,OAAOL,YAAY;AACrB,CAAC,CAACP,aAAa,CAAC;AAChB,IAAIqB,QAAQ,GAAG;EACbY,SAAS,EAAE,SAAAA,CAAUC,CAAC,EAAE;IACtB,IAAIC,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE;MAC/B,IAAI,CAACC,eAAe,GAAG,CAACF,CAAC,CAACG,OAAO,EAAEH,CAAC,CAACI,OAAO,CAAC;IAC/C;EACF,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAUL,CAAC,EAAE;IACpB,IAAIM,cAAc,GAAG,IAAI,CAACJ,eAAe;IACzC,IAAID,YAAY,CAAC,IAAI,EAAE,OAAO,CAAC,IAAIK,cAAc,EAAE;MACjD,IAAIC,KAAK,GAAG,CAACP,CAAC,CAACG,OAAO,EAAEH,CAAC,CAACI,OAAO,CAAC;MAClC,IAAII,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACJ,cAAc,CAAC,CAAC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACJ,cAAc,CAAC,CAAC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChG,IAAIC,IAAI,GAAGpC,eAAe,EAAE;QAC1B;MACF;MACA,IAAIuC,MAAM,GAAG,IAAI,CAAC3B,MAAM,CAAC4B,gBAAgB,CAACC,yBAAyB,CAAC,CAACb,CAAC,CAACG,OAAO,EAAEH,CAAC,CAACI,OAAO,CAAC,CAAC;MAC3FO,MAAM,CAACG,QAAQ,KAAK,MAAM,IAAI,IAAI,CAACjB,eAAe,CAAC;QACjDkB,gBAAgB,EAAEJ,MAAM,CAACI;MAC3B,CAAC,CAAC;IACJ;IACA,IAAI,CAACb,eAAe,GAAG,IAAI;EAC7B,CAAC;EACDc,SAAS,EAAE,SAAAA,CAAUhB,CAAC,EAAE;IACtB;IACA,IAAI,IAAI,CAACE,eAAe,IAAI,CAACD,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE;MAC5D;IACF;IACA,IAAIgB,KAAK,GAAG,IAAI,CAACjC,MAAM;IACvB,IAAI2B,MAAM,GAAGM,KAAK,CAACL,gBAAgB,CAACC,yBAAyB,CAAC,CAACb,CAAC,CAACG,OAAO,EAAEH,CAAC,CAACI,OAAO,CAAC,CAAC;IACrF,IAAIU,QAAQ,GAAGH,MAAM,CAACG,QAAQ;IAC9BA,QAAQ,KAAK,MAAM,IAAI,IAAI,CAACnB,wBAAwB,CAACuB,gBAAgB,CAACD,KAAK,CAACzB,GAAG,CAAC,oBAAoB,CAAC,CAAC;IACtG,IAAI,CAACG,wBAAwB,CAACmB,QAAQ,KAAK,MAAM,GAAG,IAAI,CAAC;IAAA,EACvD;MACAC,gBAAgB,EAAEJ,MAAM,CAACI,gBAAgB;MACzC;MACAI,SAAS,EAAEL,QAAQ,KAAK,MAAM,GAAG,IAAI,GAAG;QACtCM,QAAQ,EAAE,CAAC,CAAC;MACd;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AACD,SAASnB,YAAYA,CAACoB,IAAI,EAAEC,SAAS,EAAE;EACrC,IAAIL,KAAK,GAAGI,IAAI,CAACrC,MAAM;EACvB,OAAOiC,KAAK,CAACzB,GAAG,CAAC,gBAAgB,CAAC,IAAIyB,KAAK,CAACzB,GAAG,CAAC,qBAAqB,CAAC,KAAK8B,SAAS;AACtF;AACA,eAAejD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}