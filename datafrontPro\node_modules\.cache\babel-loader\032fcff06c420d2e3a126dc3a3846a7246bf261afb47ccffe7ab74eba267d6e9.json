{"ast": null, "code": "import axios from 'axios';\nexport const dataApi = {\n  getAllTables() {\n    return axios.get('/api/indicator/dataset');\n  },\n  getDatasetDetail(datasetId) {\n    return axios.post('/api/indicator/dataset/detail', {\n      datasetId: datasetId\n    });\n  },\n  getTableSample(tableCode) {\n    return axios.get(`/data/tables/${tableCode}/sample`);\n  },\n  queryByQuestion(tableCode, question) {\n    return axios.get('/analytics/query', {\n      params: {\n        tableCode,\n        question\n      }\n    });\n  },\n  getDatasetTree() {\n    return axios.post('http://localhost:8088/api/dataease/datasets', {\n      busiFlag: \"dataset\"\n    });\n  },\n  getDatasetInfo(datasetId) {\n    return axios.post(`http://localhost:8088/api/dataease/dataset/${datasetId}`);\n  }\n};", "map": {"version": 3, "names": ["axios", "dataApi", "getAllTables", "get", "getDatasetDetail", "datasetId", "post", "getTableSample", "tableCode", "queryByQuestion", "question", "params", "getDatasetTree", "busiFlag", "getDatasetInfo"], "sources": ["E:/AllProject/datafront/src/api/index.js"], "sourcesContent": ["import axios from 'axios'\n\nexport const dataApi = {\n  getAllTables() {\n    return axios.get('/api/indicator/dataset')\n  },\n   getDatasetDetail(datasetId) {\n    return axios.post('/api/indicator/dataset/detail', {\n      datasetId: datasetId\n    })\n    },\n  getTableSample(tableCode) {\n    return axios.get(`/data/tables/${tableCode}/sample`)\n  },\n  queryByQuestion(tableCode, question) {\n    return axios.get('/analytics/query', {\n      params: {\n        tableCode,\n        question\n      }\n    })\n  },\n  getDatasetTree() {\n    return axios.post('http://localhost:8088/api/dataease/datasets', {\n      busiFlag: \"dataset\"\n    })\n  },\n  getDatasetInfo(datasetId) {\n    return axios.post(`http://localhost:8088/api/dataease/dataset/${datasetId}`)\n  }\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,OAAO,MAAMC,OAAO,GAAG;EACrBC,YAAYA,CAAA,EAAG;IACb,OAAOF,KAAK,CAACG,GAAG,CAAC,wBAAwB,CAAC;EAC5C,CAAC;EACAC,gBAAgBA,CAACC,SAAS,EAAE;IAC3B,OAAOL,KAAK,CAACM,IAAI,CAAC,+BAA+B,EAAE;MACjDD,SAAS,EAAEA;IACb,CAAC,CAAC;EACF,CAAC;EACHE,cAAcA,CAACC,SAAS,EAAE;IACxB,OAAOR,KAAK,CAACG,GAAG,CAAC,gBAAgBK,SAAS,SAAS,CAAC;EACtD,CAAC;EACDC,eAAeA,CAACD,SAAS,EAAEE,QAAQ,EAAE;IACnC,OAAOV,KAAK,CAACG,GAAG,CAAC,kBAAkB,EAAE;MACnCQ,MAAM,EAAE;QACNH,SAAS;QACTE;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACDE,cAAcA,CAAA,EAAG;IACf,OAAOZ,KAAK,CAACM,IAAI,CAAC,6CAA6C,EAAE;MAC/DO,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EACDC,cAAcA,CAACT,SAAS,EAAE;IACxB,OAAOL,KAAK,CAACM,IAAI,CAAC,8CAA8CD,SAAS,EAAE,CAAC;EAC9E;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}