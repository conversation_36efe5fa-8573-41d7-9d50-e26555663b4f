{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.typed-array.with.js\";\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/* global Float32Array */\nimport createRenderPlanner from '../helper/createRenderPlanner.js';\nimport { error } from '../../util/log.js';\nvar linesLayout = {\n  seriesType: 'lines',\n  plan: createRenderPlanner(),\n  reset: function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (!coordSys) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('The lines series must have a coordinate system.');\n      }\n      return;\n    }\n    var isPolyline = seriesModel.get('polyline');\n    var isLarge = seriesModel.pipelineContext.large;\n    return {\n      progress: function (params, lineData) {\n        var lineCoords = [];\n        if (isLarge) {\n          var points = void 0;\n          var segCount = params.end - params.start;\n          if (isPolyline) {\n            var totalCoordsCount = 0;\n            for (var i = params.start; i < params.end; i++) {\n              totalCoordsCount += seriesModel.getLineCoordsCount(i);\n            }\n            points = new Float32Array(segCount + totalCoordsCount * 2);\n          } else {\n            points = new Float32Array(segCount * 4);\n          }\n          var offset = 0;\n          var pt = [];\n          for (var i = params.start; i < params.end; i++) {\n            var len = seriesModel.getLineCoords(i, lineCoords);\n            if (isPolyline) {\n              points[offset++] = len;\n            }\n            for (var k = 0; k < len; k++) {\n              pt = coordSys.dataToPoint(lineCoords[k], false, pt);\n              points[offset++] = pt[0];\n              points[offset++] = pt[1];\n            }\n          }\n          lineData.setLayout('linesPoints', points);\n        } else {\n          for (var i = params.start; i < params.end; i++) {\n            var itemModel = lineData.getItemModel(i);\n            var len = seriesModel.getLineCoords(i, lineCoords);\n            var pts = [];\n            if (isPolyline) {\n              for (var j = 0; j < len; j++) {\n                pts.push(coordSys.dataToPoint(lineCoords[j]));\n              }\n            } else {\n              pts[0] = coordSys.dataToPoint(lineCoords[0]);\n              pts[1] = coordSys.dataToPoint(lineCoords[1]);\n              var curveness = itemModel.get(['lineStyle', 'curveness']);\n              if (+curveness) {\n                pts[2] = [(pts[0][0] + pts[1][0]) / 2 - (pts[0][1] - pts[1][1]) * curveness, (pts[0][1] + pts[1][1]) / 2 - (pts[1][0] - pts[0][0]) * curveness];\n              }\n            }\n            lineData.setItemLayout(i, pts);\n          }\n        }\n      }\n    };\n  }\n};\nexport default linesLayout;", "map": {"version": 3, "names": ["createRenderPlanner", "error", "linesLayout", "seriesType", "plan", "reset", "seriesModel", "coordSys", "coordinateSystem", "process", "env", "NODE_ENV", "isPolyline", "get", "is<PERSON>arge", "pipelineContext", "large", "progress", "params", "lineData", "lineCoords", "points", "segCount", "end", "start", "totalCoordsCount", "i", "getLineCoordsCount", "Float32Array", "offset", "pt", "len", "getLineCoords", "k", "dataToPoint", "setLayout", "itemModel", "getItemModel", "pts", "j", "push", "curveness", "setItemLayout"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/lines/linesLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/* global Float32Array */\nimport createRenderPlanner from '../helper/createRenderPlanner.js';\nimport { error } from '../../util/log.js';\nvar linesLayout = {\n  seriesType: 'lines',\n  plan: createRenderPlanner(),\n  reset: function (seriesModel) {\n    var coordSys = seriesModel.coordinateSystem;\n    if (!coordSys) {\n      if (process.env.NODE_ENV !== 'production') {\n        error('The lines series must have a coordinate system.');\n      }\n      return;\n    }\n    var isPolyline = seriesModel.get('polyline');\n    var isLarge = seriesModel.pipelineContext.large;\n    return {\n      progress: function (params, lineData) {\n        var lineCoords = [];\n        if (isLarge) {\n          var points = void 0;\n          var segCount = params.end - params.start;\n          if (isPolyline) {\n            var totalCoordsCount = 0;\n            for (var i = params.start; i < params.end; i++) {\n              totalCoordsCount += seriesModel.getLineCoordsCount(i);\n            }\n            points = new Float32Array(segCount + totalCoordsCount * 2);\n          } else {\n            points = new Float32Array(segCount * 4);\n          }\n          var offset = 0;\n          var pt = [];\n          for (var i = params.start; i < params.end; i++) {\n            var len = seriesModel.getLineCoords(i, lineCoords);\n            if (isPolyline) {\n              points[offset++] = len;\n            }\n            for (var k = 0; k < len; k++) {\n              pt = coordSys.dataToPoint(lineCoords[k], false, pt);\n              points[offset++] = pt[0];\n              points[offset++] = pt[1];\n            }\n          }\n          lineData.setLayout('linesPoints', points);\n        } else {\n          for (var i = params.start; i < params.end; i++) {\n            var itemModel = lineData.getItemModel(i);\n            var len = seriesModel.getLineCoords(i, lineCoords);\n            var pts = [];\n            if (isPolyline) {\n              for (var j = 0; j < len; j++) {\n                pts.push(coordSys.dataToPoint(lineCoords[j]));\n              }\n            } else {\n              pts[0] = coordSys.dataToPoint(lineCoords[0]);\n              pts[1] = coordSys.dataToPoint(lineCoords[1]);\n              var curveness = itemModel.get(['lineStyle', 'curveness']);\n              if (+curveness) {\n                pts[2] = [(pts[0][0] + pts[1][0]) / 2 - (pts[0][1] - pts[1][1]) * curveness, (pts[0][1] + pts[1][1]) / 2 - (pts[1][0] - pts[0][0]) * curveness];\n              }\n            }\n            lineData.setItemLayout(i, pts);\n          }\n        }\n      }\n    };\n  }\n};\nexport default linesLayout;"], "mappings": ";;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,mBAAmB,MAAM,kCAAkC;AAClE,SAASC,KAAK,QAAQ,mBAAmB;AACzC,IAAIC,WAAW,GAAG;EAChBC,UAAU,EAAE,OAAO;EACnBC,IAAI,EAAEJ,mBAAmB,CAAC,CAAC;EAC3BK,KAAK,EAAE,SAAAA,CAAUC,WAAW,EAAE;IAC5B,IAAIC,QAAQ,GAAGD,WAAW,CAACE,gBAAgB;IAC3C,IAAI,CAACD,QAAQ,EAAE;MACb,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCV,KAAK,CAAC,iDAAiD,CAAC;MAC1D;MACA;IACF;IACA,IAAIW,UAAU,GAAGN,WAAW,CAACO,GAAG,CAAC,UAAU,CAAC;IAC5C,IAAIC,OAAO,GAAGR,WAAW,CAACS,eAAe,CAACC,KAAK;IAC/C,OAAO;MACLC,QAAQ,EAAE,SAAAA,CAAUC,MAAM,EAAEC,QAAQ,EAAE;QACpC,IAAIC,UAAU,GAAG,EAAE;QACnB,IAAIN,OAAO,EAAE;UACX,IAAIO,MAAM,GAAG,KAAK,CAAC;UACnB,IAAIC,QAAQ,GAAGJ,MAAM,CAACK,GAAG,GAAGL,MAAM,CAACM,KAAK;UACxC,IAAIZ,UAAU,EAAE;YACd,IAAIa,gBAAgB,GAAG,CAAC;YACxB,KAAK,IAAIC,CAAC,GAAGR,MAAM,CAACM,KAAK,EAAEE,CAAC,GAAGR,MAAM,CAACK,GAAG,EAAEG,CAAC,EAAE,EAAE;cAC9CD,gBAAgB,IAAInB,WAAW,CAACqB,kBAAkB,CAACD,CAAC,CAAC;YACvD;YACAL,MAAM,GAAG,IAAIO,YAAY,CAACN,QAAQ,GAAGG,gBAAgB,GAAG,CAAC,CAAC;UAC5D,CAAC,MAAM;YACLJ,MAAM,GAAG,IAAIO,YAAY,CAACN,QAAQ,GAAG,CAAC,CAAC;UACzC;UACA,IAAIO,MAAM,GAAG,CAAC;UACd,IAAIC,EAAE,GAAG,EAAE;UACX,KAAK,IAAIJ,CAAC,GAAGR,MAAM,CAACM,KAAK,EAAEE,CAAC,GAAGR,MAAM,CAACK,GAAG,EAAEG,CAAC,EAAE,EAAE;YAC9C,IAAIK,GAAG,GAAGzB,WAAW,CAAC0B,aAAa,CAACN,CAAC,EAAEN,UAAU,CAAC;YAClD,IAAIR,UAAU,EAAE;cACdS,MAAM,CAACQ,MAAM,EAAE,CAAC,GAAGE,GAAG;YACxB;YACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;cAC5BH,EAAE,GAAGvB,QAAQ,CAAC2B,WAAW,CAACd,UAAU,CAACa,CAAC,CAAC,EAAE,KAAK,EAAEH,EAAE,CAAC;cACnDT,MAAM,CAACQ,MAAM,EAAE,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;cACxBT,MAAM,CAACQ,MAAM,EAAE,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC;YAC1B;UACF;UACAX,QAAQ,CAACgB,SAAS,CAAC,aAAa,EAAEd,MAAM,CAAC;QAC3C,CAAC,MAAM;UACL,KAAK,IAAIK,CAAC,GAAGR,MAAM,CAACM,KAAK,EAAEE,CAAC,GAAGR,MAAM,CAACK,GAAG,EAAEG,CAAC,EAAE,EAAE;YAC9C,IAAIU,SAAS,GAAGjB,QAAQ,CAACkB,YAAY,CAACX,CAAC,CAAC;YACxC,IAAIK,GAAG,GAAGzB,WAAW,CAAC0B,aAAa,CAACN,CAAC,EAAEN,UAAU,CAAC;YAClD,IAAIkB,GAAG,GAAG,EAAE;YACZ,IAAI1B,UAAU,EAAE;cACd,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,GAAG,EAAEQ,CAAC,EAAE,EAAE;gBAC5BD,GAAG,CAACE,IAAI,CAACjC,QAAQ,CAAC2B,WAAW,CAACd,UAAU,CAACmB,CAAC,CAAC,CAAC,CAAC;cAC/C;YACF,CAAC,MAAM;cACLD,GAAG,CAAC,CAAC,CAAC,GAAG/B,QAAQ,CAAC2B,WAAW,CAACd,UAAU,CAAC,CAAC,CAAC,CAAC;cAC5CkB,GAAG,CAAC,CAAC,CAAC,GAAG/B,QAAQ,CAAC2B,WAAW,CAACd,UAAU,CAAC,CAAC,CAAC,CAAC;cAC5C,IAAIqB,SAAS,GAAGL,SAAS,CAACvB,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;cACzD,IAAI,CAAC4B,SAAS,EAAE;gBACdH,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAACA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAACA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIG,SAAS,EAAE,CAACH,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAACA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIG,SAAS,CAAC;cACjJ;YACF;YACAtB,QAAQ,CAACuB,aAAa,CAAChB,CAAC,EAAEY,GAAG,CAAC;UAChC;QACF;MACF;IACF,CAAC;EACH;AACF,CAAC;AACD,eAAepC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}