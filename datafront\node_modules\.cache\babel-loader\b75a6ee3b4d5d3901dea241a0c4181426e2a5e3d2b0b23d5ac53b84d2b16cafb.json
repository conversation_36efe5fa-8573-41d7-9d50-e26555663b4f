{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// Poly path support NaN point\nimport Path from 'zrender/lib/graphic/Path.js';\nimport PathProxy from 'zrender/lib/core/PathProxy.js';\nimport { cubicRootAt, cubicAt } from 'zrender/lib/core/curve.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nfunction isPointNull(x, y) {\n  return isNaN(x) || isNaN(y);\n}\n/**\r\n * Draw smoothed line in non-monotone, in may cause undesired curve in extreme\r\n * situations. This should be used when points are non-monotone neither in x or\r\n * y dimension.\r\n */\nfunction drawSegment(ctx, points, start, segLen, allLen, dir, smooth, smoothMonotone, connectNulls) {\n  var prevX;\n  var prevY;\n  var cpx0;\n  var cpy0;\n  var cpx1;\n  var cpy1;\n  var idx = start;\n  var k = 0;\n  for (; k < segLen; k++) {\n    var x = points[idx * 2];\n    var y = points[idx * 2 + 1];\n    if (idx >= allLen || idx < 0) {\n      break;\n    }\n    if (isPointNull(x, y)) {\n      if (connectNulls) {\n        idx += dir;\n        continue;\n      }\n      break;\n    }\n    if (idx === start) {\n      ctx[dir > 0 ? 'moveTo' : 'lineTo'](x, y);\n      cpx0 = x;\n      cpy0 = y;\n    } else {\n      var dx = x - prevX;\n      var dy = y - prevY;\n      // Ignore tiny segment.\n      if (dx * dx + dy * dy < 0.5) {\n        idx += dir;\n        continue;\n      }\n      if (smooth > 0) {\n        var nextIdx = idx + dir;\n        var nextX = points[nextIdx * 2];\n        var nextY = points[nextIdx * 2 + 1];\n        // Ignore duplicate point\n        while (nextX === x && nextY === y && k < segLen) {\n          k++;\n          nextIdx += dir;\n          idx += dir;\n          nextX = points[nextIdx * 2];\n          nextY = points[nextIdx * 2 + 1];\n          x = points[idx * 2];\n          y = points[idx * 2 + 1];\n          dx = x - prevX;\n          dy = y - prevY;\n        }\n        var tmpK = k + 1;\n        if (connectNulls) {\n          // Find next point not null\n          while (isPointNull(nextX, nextY) && tmpK < segLen) {\n            tmpK++;\n            nextIdx += dir;\n            nextX = points[nextIdx * 2];\n            nextY = points[nextIdx * 2 + 1];\n          }\n        }\n        var ratioNextSeg = 0.5;\n        var vx = 0;\n        var vy = 0;\n        var nextCpx0 = void 0;\n        var nextCpy0 = void 0;\n        // Is last point\n        if (tmpK >= segLen || isPointNull(nextX, nextY)) {\n          cpx1 = x;\n          cpy1 = y;\n        } else {\n          vx = nextX - prevX;\n          vy = nextY - prevY;\n          var dx0 = x - prevX;\n          var dx1 = nextX - x;\n          var dy0 = y - prevY;\n          var dy1 = nextY - y;\n          var lenPrevSeg = void 0;\n          var lenNextSeg = void 0;\n          if (smoothMonotone === 'x') {\n            lenPrevSeg = Math.abs(dx0);\n            lenNextSeg = Math.abs(dx1);\n            var dir_1 = vx > 0 ? 1 : -1;\n            cpx1 = x - dir_1 * lenPrevSeg * smooth;\n            cpy1 = y;\n            nextCpx0 = x + dir_1 * lenNextSeg * smooth;\n            nextCpy0 = y;\n          } else if (smoothMonotone === 'y') {\n            lenPrevSeg = Math.abs(dy0);\n            lenNextSeg = Math.abs(dy1);\n            var dir_2 = vy > 0 ? 1 : -1;\n            cpx1 = x;\n            cpy1 = y - dir_2 * lenPrevSeg * smooth;\n            nextCpx0 = x;\n            nextCpy0 = y + dir_2 * lenNextSeg * smooth;\n          } else {\n            lenPrevSeg = Math.sqrt(dx0 * dx0 + dy0 * dy0);\n            lenNextSeg = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n            // Use ratio of seg length\n            ratioNextSeg = lenNextSeg / (lenNextSeg + lenPrevSeg);\n            cpx1 = x - vx * smooth * (1 - ratioNextSeg);\n            cpy1 = y - vy * smooth * (1 - ratioNextSeg);\n            // cp0 of next segment\n            nextCpx0 = x + vx * smooth * ratioNextSeg;\n            nextCpy0 = y + vy * smooth * ratioNextSeg;\n            // Smooth constraint between point and next point.\n            // Avoid exceeding extreme after smoothing.\n            nextCpx0 = mathMin(nextCpx0, mathMax(nextX, x));\n            nextCpy0 = mathMin(nextCpy0, mathMax(nextY, y));\n            nextCpx0 = mathMax(nextCpx0, mathMin(nextX, x));\n            nextCpy0 = mathMax(nextCpy0, mathMin(nextY, y));\n            // Reclaculate cp1 based on the adjusted cp0 of next seg.\n            vx = nextCpx0 - x;\n            vy = nextCpy0 - y;\n            cpx1 = x - vx * lenPrevSeg / lenNextSeg;\n            cpy1 = y - vy * lenPrevSeg / lenNextSeg;\n            // Smooth constraint between point and prev point.\n            // Avoid exceeding extreme after smoothing.\n            cpx1 = mathMin(cpx1, mathMax(prevX, x));\n            cpy1 = mathMin(cpy1, mathMax(prevY, y));\n            cpx1 = mathMax(cpx1, mathMin(prevX, x));\n            cpy1 = mathMax(cpy1, mathMin(prevY, y));\n            // Adjust next cp0 again.\n            vx = x - cpx1;\n            vy = y - cpy1;\n            nextCpx0 = x + vx * lenNextSeg / lenPrevSeg;\n            nextCpy0 = y + vy * lenNextSeg / lenPrevSeg;\n          }\n        }\n        ctx.bezierCurveTo(cpx0, cpy0, cpx1, cpy1, x, y);\n        cpx0 = nextCpx0;\n        cpy0 = nextCpy0;\n      } else {\n        ctx.lineTo(x, y);\n      }\n    }\n    prevX = x;\n    prevY = y;\n    idx += dir;\n  }\n  return k;\n}\nvar ECPolylineShape = /** @class */function () {\n  function ECPolylineShape() {\n    this.smooth = 0;\n    this.smoothConstraint = true;\n  }\n  return ECPolylineShape;\n}();\nvar ECPolyline = /** @class */function (_super) {\n  __extends(ECPolyline, _super);\n  function ECPolyline(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-polyline';\n    return _this;\n  }\n  ECPolyline.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  ECPolyline.prototype.getDefaultShape = function () {\n    return new ECPolylineShape();\n  };\n  ECPolyline.prototype.buildPath = function (ctx, shape) {\n    var points = shape.points;\n    var i = 0;\n    var len = points.length / 2;\n    // const result = getBoundingBox(points, shape.smoothConstraint);\n    if (shape.connectNulls) {\n      // Must remove first and last null values avoid draw error in polygon\n      for (; len > 0; len--) {\n        if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n          break;\n        }\n      }\n      for (; i < len; i++) {\n        if (!isPointNull(points[i * 2], points[i * 2 + 1])) {\n          break;\n        }\n      }\n    }\n    while (i < len) {\n      i += drawSegment(ctx, points, i, len, len, 1, shape.smooth, shape.smoothMonotone, shape.connectNulls) + 1;\n    }\n  };\n  ECPolyline.prototype.getPointOn = function (xOrY, dim) {\n    if (!this.path) {\n      this.createPathProxy();\n      this.buildPath(this.path, this.shape);\n    }\n    var path = this.path;\n    var data = path.data;\n    var CMD = PathProxy.CMD;\n    var x0;\n    var y0;\n    var isDimX = dim === 'x';\n    var roots = [];\n    for (var i = 0; i < data.length;) {\n      var cmd = data[i++];\n      var x = void 0;\n      var y = void 0;\n      var x2 = void 0;\n      var y2 = void 0;\n      var x3 = void 0;\n      var y3 = void 0;\n      var t = void 0;\n      switch (cmd) {\n        case CMD.M:\n          x0 = data[i++];\n          y0 = data[i++];\n          break;\n        case CMD.L:\n          x = data[i++];\n          y = data[i++];\n          t = isDimX ? (xOrY - x0) / (x - x0) : (xOrY - y0) / (y - y0);\n          if (t <= 1 && t >= 0) {\n            var val = isDimX ? (y - y0) * t + y0 : (x - x0) * t + x0;\n            return isDimX ? [xOrY, val] : [val, xOrY];\n          }\n          x0 = x;\n          y0 = y;\n          break;\n        case CMD.C:\n          x = data[i++];\n          y = data[i++];\n          x2 = data[i++];\n          y2 = data[i++];\n          x3 = data[i++];\n          y3 = data[i++];\n          var nRoot = isDimX ? cubicRootAt(x0, x, x2, x3, xOrY, roots) : cubicRootAt(y0, y, y2, y3, xOrY, roots);\n          if (nRoot > 0) {\n            for (var i_1 = 0; i_1 < nRoot; i_1++) {\n              var t_1 = roots[i_1];\n              if (t_1 <= 1 && t_1 >= 0) {\n                var val = isDimX ? cubicAt(y0, y, y2, y3, t_1) : cubicAt(x0, x, x2, x3, t_1);\n                return isDimX ? [xOrY, val] : [val, xOrY];\n              }\n            }\n          }\n          x0 = x3;\n          y0 = y3;\n          break;\n      }\n    }\n  };\n  return ECPolyline;\n}(Path);\nexport { ECPolyline };\nvar ECPolygonShape = /** @class */function (_super) {\n  __extends(ECPolygonShape, _super);\n  function ECPolygonShape() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return ECPolygonShape;\n}(ECPolylineShape);\nvar ECPolygon = /** @class */function (_super) {\n  __extends(ECPolygon, _super);\n  function ECPolygon(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-polygon';\n    return _this;\n  }\n  ECPolygon.prototype.getDefaultShape = function () {\n    return new ECPolygonShape();\n  };\n  ECPolygon.prototype.buildPath = function (ctx, shape) {\n    var points = shape.points;\n    var stackedOnPoints = shape.stackedOnPoints;\n    var i = 0;\n    var len = points.length / 2;\n    var smoothMonotone = shape.smoothMonotone;\n    if (shape.connectNulls) {\n      // Must remove first and last null values avoid draw error in polygon\n      for (; len > 0; len--) {\n        if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n          break;\n        }\n      }\n      for (; i < len; i++) {\n        if (!isPointNull(points[i * 2], points[i * 2 + 1])) {\n          break;\n        }\n      }\n    }\n    while (i < len) {\n      var k = drawSegment(ctx, points, i, len, len, 1, shape.smooth, smoothMonotone, shape.connectNulls);\n      drawSegment(ctx, stackedOnPoints, i + k - 1, k, len, -1, shape.stackedOnSmooth, smoothMonotone, shape.connectNulls);\n      i += k + 1;\n      ctx.closePath();\n    }\n  };\n  return ECPolygon;\n}(Path);\nexport { ECPolygon };", "map": {"version": 3, "names": ["__extends", "Path", "PathProxy", "cubicRootAt", "cubicAt", "mathMin", "Math", "min", "mathMax", "max", "isPointNull", "x", "y", "isNaN", "drawSegment", "ctx", "points", "start", "segLen", "allLen", "dir", "smooth", "smoothMonotone", "connectNulls", "prevX", "prevY", "cpx0", "cpy0", "cpx1", "cpy1", "idx", "k", "dx", "dy", "nextIdx", "nextX", "nextY", "tmpK", "ratioNextSeg", "vx", "vy", "nextCpx0", "nextCpy0", "dx0", "dx1", "dy0", "dy1", "lenPrevSeg", "lenNextSeg", "abs", "dir_1", "dir_2", "sqrt", "bezierCurveTo", "lineTo", "ECPolylineShape", "smoothConstraint", "ECPolyline", "_super", "opts", "_this", "call", "type", "prototype", "getDefaultStyle", "stroke", "fill", "getDefaultShape", "buildPath", "shape", "i", "len", "length", "getPointOn", "xOrY", "dim", "path", "createPathProxy", "data", "CMD", "x0", "y0", "isDimX", "roots", "cmd", "x2", "y2", "x3", "y3", "t", "M", "L", "val", "C", "nRoot", "i_1", "t_1", "ECPolygonShape", "apply", "arguments", "ECPolygon", "stackedOnPoints", "stackedOnSmooth", "closePath"], "sources": ["D:/FastBI/datafront/node_modules/echarts/lib/chart/line/poly.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// Poly path support NaN point\nimport Path from 'zrender/lib/graphic/Path.js';\nimport PathProxy from 'zrender/lib/core/PathProxy.js';\nimport { cubicRootAt, cubicAt } from 'zrender/lib/core/curve.js';\nvar mathMin = Math.min;\nvar mathMax = Math.max;\nfunction isPointNull(x, y) {\n  return isNaN(x) || isNaN(y);\n}\n/**\r\n * Draw smoothed line in non-monotone, in may cause undesired curve in extreme\r\n * situations. This should be used when points are non-monotone neither in x or\r\n * y dimension.\r\n */\nfunction drawSegment(ctx, points, start, segLen, allLen, dir, smooth, smoothMonotone, connectNulls) {\n  var prevX;\n  var prevY;\n  var cpx0;\n  var cpy0;\n  var cpx1;\n  var cpy1;\n  var idx = start;\n  var k = 0;\n  for (; k < segLen; k++) {\n    var x = points[idx * 2];\n    var y = points[idx * 2 + 1];\n    if (idx >= allLen || idx < 0) {\n      break;\n    }\n    if (isPointNull(x, y)) {\n      if (connectNulls) {\n        idx += dir;\n        continue;\n      }\n      break;\n    }\n    if (idx === start) {\n      ctx[dir > 0 ? 'moveTo' : 'lineTo'](x, y);\n      cpx0 = x;\n      cpy0 = y;\n    } else {\n      var dx = x - prevX;\n      var dy = y - prevY;\n      // Ignore tiny segment.\n      if (dx * dx + dy * dy < 0.5) {\n        idx += dir;\n        continue;\n      }\n      if (smooth > 0) {\n        var nextIdx = idx + dir;\n        var nextX = points[nextIdx * 2];\n        var nextY = points[nextIdx * 2 + 1];\n        // Ignore duplicate point\n        while (nextX === x && nextY === y && k < segLen) {\n          k++;\n          nextIdx += dir;\n          idx += dir;\n          nextX = points[nextIdx * 2];\n          nextY = points[nextIdx * 2 + 1];\n          x = points[idx * 2];\n          y = points[idx * 2 + 1];\n          dx = x - prevX;\n          dy = y - prevY;\n        }\n        var tmpK = k + 1;\n        if (connectNulls) {\n          // Find next point not null\n          while (isPointNull(nextX, nextY) && tmpK < segLen) {\n            tmpK++;\n            nextIdx += dir;\n            nextX = points[nextIdx * 2];\n            nextY = points[nextIdx * 2 + 1];\n          }\n        }\n        var ratioNextSeg = 0.5;\n        var vx = 0;\n        var vy = 0;\n        var nextCpx0 = void 0;\n        var nextCpy0 = void 0;\n        // Is last point\n        if (tmpK >= segLen || isPointNull(nextX, nextY)) {\n          cpx1 = x;\n          cpy1 = y;\n        } else {\n          vx = nextX - prevX;\n          vy = nextY - prevY;\n          var dx0 = x - prevX;\n          var dx1 = nextX - x;\n          var dy0 = y - prevY;\n          var dy1 = nextY - y;\n          var lenPrevSeg = void 0;\n          var lenNextSeg = void 0;\n          if (smoothMonotone === 'x') {\n            lenPrevSeg = Math.abs(dx0);\n            lenNextSeg = Math.abs(dx1);\n            var dir_1 = vx > 0 ? 1 : -1;\n            cpx1 = x - dir_1 * lenPrevSeg * smooth;\n            cpy1 = y;\n            nextCpx0 = x + dir_1 * lenNextSeg * smooth;\n            nextCpy0 = y;\n          } else if (smoothMonotone === 'y') {\n            lenPrevSeg = Math.abs(dy0);\n            lenNextSeg = Math.abs(dy1);\n            var dir_2 = vy > 0 ? 1 : -1;\n            cpx1 = x;\n            cpy1 = y - dir_2 * lenPrevSeg * smooth;\n            nextCpx0 = x;\n            nextCpy0 = y + dir_2 * lenNextSeg * smooth;\n          } else {\n            lenPrevSeg = Math.sqrt(dx0 * dx0 + dy0 * dy0);\n            lenNextSeg = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n            // Use ratio of seg length\n            ratioNextSeg = lenNextSeg / (lenNextSeg + lenPrevSeg);\n            cpx1 = x - vx * smooth * (1 - ratioNextSeg);\n            cpy1 = y - vy * smooth * (1 - ratioNextSeg);\n            // cp0 of next segment\n            nextCpx0 = x + vx * smooth * ratioNextSeg;\n            nextCpy0 = y + vy * smooth * ratioNextSeg;\n            // Smooth constraint between point and next point.\n            // Avoid exceeding extreme after smoothing.\n            nextCpx0 = mathMin(nextCpx0, mathMax(nextX, x));\n            nextCpy0 = mathMin(nextCpy0, mathMax(nextY, y));\n            nextCpx0 = mathMax(nextCpx0, mathMin(nextX, x));\n            nextCpy0 = mathMax(nextCpy0, mathMin(nextY, y));\n            // Reclaculate cp1 based on the adjusted cp0 of next seg.\n            vx = nextCpx0 - x;\n            vy = nextCpy0 - y;\n            cpx1 = x - vx * lenPrevSeg / lenNextSeg;\n            cpy1 = y - vy * lenPrevSeg / lenNextSeg;\n            // Smooth constraint between point and prev point.\n            // Avoid exceeding extreme after smoothing.\n            cpx1 = mathMin(cpx1, mathMax(prevX, x));\n            cpy1 = mathMin(cpy1, mathMax(prevY, y));\n            cpx1 = mathMax(cpx1, mathMin(prevX, x));\n            cpy1 = mathMax(cpy1, mathMin(prevY, y));\n            // Adjust next cp0 again.\n            vx = x - cpx1;\n            vy = y - cpy1;\n            nextCpx0 = x + vx * lenNextSeg / lenPrevSeg;\n            nextCpy0 = y + vy * lenNextSeg / lenPrevSeg;\n          }\n        }\n        ctx.bezierCurveTo(cpx0, cpy0, cpx1, cpy1, x, y);\n        cpx0 = nextCpx0;\n        cpy0 = nextCpy0;\n      } else {\n        ctx.lineTo(x, y);\n      }\n    }\n    prevX = x;\n    prevY = y;\n    idx += dir;\n  }\n  return k;\n}\nvar ECPolylineShape = /** @class */function () {\n  function ECPolylineShape() {\n    this.smooth = 0;\n    this.smoothConstraint = true;\n  }\n  return ECPolylineShape;\n}();\nvar ECPolyline = /** @class */function (_super) {\n  __extends(ECPolyline, _super);\n  function ECPolyline(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-polyline';\n    return _this;\n  }\n  ECPolyline.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  ECPolyline.prototype.getDefaultShape = function () {\n    return new ECPolylineShape();\n  };\n  ECPolyline.prototype.buildPath = function (ctx, shape) {\n    var points = shape.points;\n    var i = 0;\n    var len = points.length / 2;\n    // const result = getBoundingBox(points, shape.smoothConstraint);\n    if (shape.connectNulls) {\n      // Must remove first and last null values avoid draw error in polygon\n      for (; len > 0; len--) {\n        if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n          break;\n        }\n      }\n      for (; i < len; i++) {\n        if (!isPointNull(points[i * 2], points[i * 2 + 1])) {\n          break;\n        }\n      }\n    }\n    while (i < len) {\n      i += drawSegment(ctx, points, i, len, len, 1, shape.smooth, shape.smoothMonotone, shape.connectNulls) + 1;\n    }\n  };\n  ECPolyline.prototype.getPointOn = function (xOrY, dim) {\n    if (!this.path) {\n      this.createPathProxy();\n      this.buildPath(this.path, this.shape);\n    }\n    var path = this.path;\n    var data = path.data;\n    var CMD = PathProxy.CMD;\n    var x0;\n    var y0;\n    var isDimX = dim === 'x';\n    var roots = [];\n    for (var i = 0; i < data.length;) {\n      var cmd = data[i++];\n      var x = void 0;\n      var y = void 0;\n      var x2 = void 0;\n      var y2 = void 0;\n      var x3 = void 0;\n      var y3 = void 0;\n      var t = void 0;\n      switch (cmd) {\n        case CMD.M:\n          x0 = data[i++];\n          y0 = data[i++];\n          break;\n        case CMD.L:\n          x = data[i++];\n          y = data[i++];\n          t = isDimX ? (xOrY - x0) / (x - x0) : (xOrY - y0) / (y - y0);\n          if (t <= 1 && t >= 0) {\n            var val = isDimX ? (y - y0) * t + y0 : (x - x0) * t + x0;\n            return isDimX ? [xOrY, val] : [val, xOrY];\n          }\n          x0 = x;\n          y0 = y;\n          break;\n        case CMD.C:\n          x = data[i++];\n          y = data[i++];\n          x2 = data[i++];\n          y2 = data[i++];\n          x3 = data[i++];\n          y3 = data[i++];\n          var nRoot = isDimX ? cubicRootAt(x0, x, x2, x3, xOrY, roots) : cubicRootAt(y0, y, y2, y3, xOrY, roots);\n          if (nRoot > 0) {\n            for (var i_1 = 0; i_1 < nRoot; i_1++) {\n              var t_1 = roots[i_1];\n              if (t_1 <= 1 && t_1 >= 0) {\n                var val = isDimX ? cubicAt(y0, y, y2, y3, t_1) : cubicAt(x0, x, x2, x3, t_1);\n                return isDimX ? [xOrY, val] : [val, xOrY];\n              }\n            }\n          }\n          x0 = x3;\n          y0 = y3;\n          break;\n      }\n    }\n  };\n  return ECPolyline;\n}(Path);\nexport { ECPolyline };\nvar ECPolygonShape = /** @class */function (_super) {\n  __extends(ECPolygonShape, _super);\n  function ECPolygonShape() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return ECPolygonShape;\n}(ECPolylineShape);\nvar ECPolygon = /** @class */function (_super) {\n  __extends(ECPolygon, _super);\n  function ECPolygon(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-polygon';\n    return _this;\n  }\n  ECPolygon.prototype.getDefaultShape = function () {\n    return new ECPolygonShape();\n  };\n  ECPolygon.prototype.buildPath = function (ctx, shape) {\n    var points = shape.points;\n    var stackedOnPoints = shape.stackedOnPoints;\n    var i = 0;\n    var len = points.length / 2;\n    var smoothMonotone = shape.smoothMonotone;\n    if (shape.connectNulls) {\n      // Must remove first and last null values avoid draw error in polygon\n      for (; len > 0; len--) {\n        if (!isPointNull(points[len * 2 - 2], points[len * 2 - 1])) {\n          break;\n        }\n      }\n      for (; i < len; i++) {\n        if (!isPointNull(points[i * 2], points[i * 2 + 1])) {\n          break;\n        }\n      }\n    }\n    while (i < len) {\n      var k = drawSegment(ctx, points, i, len, len, 1, shape.smooth, smoothMonotone, shape.connectNulls);\n      drawSegment(ctx, stackedOnPoints, i + k - 1, k, len, -1, shape.stackedOnSmooth, smoothMonotone, shape.connectNulls);\n      i += k + 1;\n      ctx.closePath();\n    }\n  };\n  return ECPolygon;\n}(Path);\nexport { ECPolygon };"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAAS,QAAQ,OAAO;AACjC;AACA,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,EAAEC,OAAO,QAAQ,2BAA2B;AAChE,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG;AACtB,IAAIC,OAAO,GAAGF,IAAI,CAACG,GAAG;AACtB,SAASC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOC,KAAK,CAACF,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,WAAWA,CAACC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAEC,MAAM,EAAEC,cAAc,EAAEC,YAAY,EAAE;EAClG,IAAIC,KAAK;EACT,IAAIC,KAAK;EACT,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,IAAI;EACR,IAAIC,GAAG,GAAGb,KAAK;EACf,IAAIc,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGb,MAAM,EAAEa,CAAC,EAAE,EAAE;IACtB,IAAIpB,CAAC,GAAGK,MAAM,CAACc,GAAG,GAAG,CAAC,CAAC;IACvB,IAAIlB,CAAC,GAAGI,MAAM,CAACc,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAIA,GAAG,IAAIX,MAAM,IAAIW,GAAG,GAAG,CAAC,EAAE;MAC5B;IACF;IACA,IAAIpB,WAAW,CAACC,CAAC,EAAEC,CAAC,CAAC,EAAE;MACrB,IAAIW,YAAY,EAAE;QAChBO,GAAG,IAAIV,GAAG;QACV;MACF;MACA;IACF;IACA,IAAIU,GAAG,KAAKb,KAAK,EAAE;MACjBF,GAAG,CAACK,GAAG,GAAG,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC,CAACT,CAAC,EAAEC,CAAC,CAAC;MACxCc,IAAI,GAAGf,CAAC;MACRgB,IAAI,GAAGf,CAAC;IACV,CAAC,MAAM;MACL,IAAIoB,EAAE,GAAGrB,CAAC,GAAGa,KAAK;MAClB,IAAIS,EAAE,GAAGrB,CAAC,GAAGa,KAAK;MAClB;MACA,IAAIO,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAG,GAAG,EAAE;QAC3BH,GAAG,IAAIV,GAAG;QACV;MACF;MACA,IAAIC,MAAM,GAAG,CAAC,EAAE;QACd,IAAIa,OAAO,GAAGJ,GAAG,GAAGV,GAAG;QACvB,IAAIe,KAAK,GAAGnB,MAAM,CAACkB,OAAO,GAAG,CAAC,CAAC;QAC/B,IAAIE,KAAK,GAAGpB,MAAM,CAACkB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;QACnC;QACA,OAAOC,KAAK,KAAKxB,CAAC,IAAIyB,KAAK,KAAKxB,CAAC,IAAImB,CAAC,GAAGb,MAAM,EAAE;UAC/Ca,CAAC,EAAE;UACHG,OAAO,IAAId,GAAG;UACdU,GAAG,IAAIV,GAAG;UACVe,KAAK,GAAGnB,MAAM,CAACkB,OAAO,GAAG,CAAC,CAAC;UAC3BE,KAAK,GAAGpB,MAAM,CAACkB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;UAC/BvB,CAAC,GAAGK,MAAM,CAACc,GAAG,GAAG,CAAC,CAAC;UACnBlB,CAAC,GAAGI,MAAM,CAACc,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;UACvBE,EAAE,GAAGrB,CAAC,GAAGa,KAAK;UACdS,EAAE,GAAGrB,CAAC,GAAGa,KAAK;QAChB;QACA,IAAIY,IAAI,GAAGN,CAAC,GAAG,CAAC;QAChB,IAAIR,YAAY,EAAE;UAChB;UACA,OAAOb,WAAW,CAACyB,KAAK,EAAEC,KAAK,CAAC,IAAIC,IAAI,GAAGnB,MAAM,EAAE;YACjDmB,IAAI,EAAE;YACNH,OAAO,IAAId,GAAG;YACde,KAAK,GAAGnB,MAAM,CAACkB,OAAO,GAAG,CAAC,CAAC;YAC3BE,KAAK,GAAGpB,MAAM,CAACkB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;UACjC;QACF;QACA,IAAII,YAAY,GAAG,GAAG;QACtB,IAAIC,EAAE,GAAG,CAAC;QACV,IAAIC,EAAE,GAAG,CAAC;QACV,IAAIC,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAIC,QAAQ,GAAG,KAAK,CAAC;QACrB;QACA,IAAIL,IAAI,IAAInB,MAAM,IAAIR,WAAW,CAACyB,KAAK,EAAEC,KAAK,CAAC,EAAE;UAC/CR,IAAI,GAAGjB,CAAC;UACRkB,IAAI,GAAGjB,CAAC;QACV,CAAC,MAAM;UACL2B,EAAE,GAAGJ,KAAK,GAAGX,KAAK;UAClBgB,EAAE,GAAGJ,KAAK,GAAGX,KAAK;UAClB,IAAIkB,GAAG,GAAGhC,CAAC,GAAGa,KAAK;UACnB,IAAIoB,GAAG,GAAGT,KAAK,GAAGxB,CAAC;UACnB,IAAIkC,GAAG,GAAGjC,CAAC,GAAGa,KAAK;UACnB,IAAIqB,GAAG,GAAGV,KAAK,GAAGxB,CAAC;UACnB,IAAImC,UAAU,GAAG,KAAK,CAAC;UACvB,IAAIC,UAAU,GAAG,KAAK,CAAC;UACvB,IAAI1B,cAAc,KAAK,GAAG,EAAE;YAC1ByB,UAAU,GAAGzC,IAAI,CAAC2C,GAAG,CAACN,GAAG,CAAC;YAC1BK,UAAU,GAAG1C,IAAI,CAAC2C,GAAG,CAACL,GAAG,CAAC;YAC1B,IAAIM,KAAK,GAAGX,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC3BX,IAAI,GAAGjB,CAAC,GAAGuC,KAAK,GAAGH,UAAU,GAAG1B,MAAM;YACtCQ,IAAI,GAAGjB,CAAC;YACR6B,QAAQ,GAAG9B,CAAC,GAAGuC,KAAK,GAAGF,UAAU,GAAG3B,MAAM;YAC1CqB,QAAQ,GAAG9B,CAAC;UACd,CAAC,MAAM,IAAIU,cAAc,KAAK,GAAG,EAAE;YACjCyB,UAAU,GAAGzC,IAAI,CAAC2C,GAAG,CAACJ,GAAG,CAAC;YAC1BG,UAAU,GAAG1C,IAAI,CAAC2C,GAAG,CAACH,GAAG,CAAC;YAC1B,IAAIK,KAAK,GAAGX,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC3BZ,IAAI,GAAGjB,CAAC;YACRkB,IAAI,GAAGjB,CAAC,GAAGuC,KAAK,GAAGJ,UAAU,GAAG1B,MAAM;YACtCoB,QAAQ,GAAG9B,CAAC;YACZ+B,QAAQ,GAAG9B,CAAC,GAAGuC,KAAK,GAAGH,UAAU,GAAG3B,MAAM;UAC5C,CAAC,MAAM;YACL0B,UAAU,GAAGzC,IAAI,CAAC8C,IAAI,CAACT,GAAG,GAAGA,GAAG,GAAGE,GAAG,GAAGA,GAAG,CAAC;YAC7CG,UAAU,GAAG1C,IAAI,CAAC8C,IAAI,CAACR,GAAG,GAAGA,GAAG,GAAGE,GAAG,GAAGA,GAAG,CAAC;YAC7C;YACAR,YAAY,GAAGU,UAAU,IAAIA,UAAU,GAAGD,UAAU,CAAC;YACrDnB,IAAI,GAAGjB,CAAC,GAAG4B,EAAE,GAAGlB,MAAM,IAAI,CAAC,GAAGiB,YAAY,CAAC;YAC3CT,IAAI,GAAGjB,CAAC,GAAG4B,EAAE,GAAGnB,MAAM,IAAI,CAAC,GAAGiB,YAAY,CAAC;YAC3C;YACAG,QAAQ,GAAG9B,CAAC,GAAG4B,EAAE,GAAGlB,MAAM,GAAGiB,YAAY;YACzCI,QAAQ,GAAG9B,CAAC,GAAG4B,EAAE,GAAGnB,MAAM,GAAGiB,YAAY;YACzC;YACA;YACAG,QAAQ,GAAGpC,OAAO,CAACoC,QAAQ,EAAEjC,OAAO,CAAC2B,KAAK,EAAExB,CAAC,CAAC,CAAC;YAC/C+B,QAAQ,GAAGrC,OAAO,CAACqC,QAAQ,EAAElC,OAAO,CAAC4B,KAAK,EAAExB,CAAC,CAAC,CAAC;YAC/C6B,QAAQ,GAAGjC,OAAO,CAACiC,QAAQ,EAAEpC,OAAO,CAAC8B,KAAK,EAAExB,CAAC,CAAC,CAAC;YAC/C+B,QAAQ,GAAGlC,OAAO,CAACkC,QAAQ,EAAErC,OAAO,CAAC+B,KAAK,EAAExB,CAAC,CAAC,CAAC;YAC/C;YACA2B,EAAE,GAAGE,QAAQ,GAAG9B,CAAC;YACjB6B,EAAE,GAAGE,QAAQ,GAAG9B,CAAC;YACjBgB,IAAI,GAAGjB,CAAC,GAAG4B,EAAE,GAAGQ,UAAU,GAAGC,UAAU;YACvCnB,IAAI,GAAGjB,CAAC,GAAG4B,EAAE,GAAGO,UAAU,GAAGC,UAAU;YACvC;YACA;YACApB,IAAI,GAAGvB,OAAO,CAACuB,IAAI,EAAEpB,OAAO,CAACgB,KAAK,EAAEb,CAAC,CAAC,CAAC;YACvCkB,IAAI,GAAGxB,OAAO,CAACwB,IAAI,EAAErB,OAAO,CAACiB,KAAK,EAAEb,CAAC,CAAC,CAAC;YACvCgB,IAAI,GAAGpB,OAAO,CAACoB,IAAI,EAAEvB,OAAO,CAACmB,KAAK,EAAEb,CAAC,CAAC,CAAC;YACvCkB,IAAI,GAAGrB,OAAO,CAACqB,IAAI,EAAExB,OAAO,CAACoB,KAAK,EAAEb,CAAC,CAAC,CAAC;YACvC;YACA2B,EAAE,GAAG5B,CAAC,GAAGiB,IAAI;YACbY,EAAE,GAAG5B,CAAC,GAAGiB,IAAI;YACbY,QAAQ,GAAG9B,CAAC,GAAG4B,EAAE,GAAGS,UAAU,GAAGD,UAAU;YAC3CL,QAAQ,GAAG9B,CAAC,GAAG4B,EAAE,GAAGQ,UAAU,GAAGD,UAAU;UAC7C;QACF;QACAhC,GAAG,CAACsC,aAAa,CAAC3B,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAElB,CAAC,EAAEC,CAAC,CAAC;QAC/Cc,IAAI,GAAGe,QAAQ;QACfd,IAAI,GAAGe,QAAQ;MACjB,CAAC,MAAM;QACL3B,GAAG,CAACuC,MAAM,CAAC3C,CAAC,EAAEC,CAAC,CAAC;MAClB;IACF;IACAY,KAAK,GAAGb,CAAC;IACTc,KAAK,GAAGb,CAAC;IACTkB,GAAG,IAAIV,GAAG;EACZ;EACA,OAAOW,CAAC;AACV;AACA,IAAIwB,eAAe,GAAG,aAAa,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG;IACzB,IAAI,CAAClC,MAAM,GAAG,CAAC;IACf,IAAI,CAACmC,gBAAgB,GAAG,IAAI;EAC9B;EACA,OAAOD,eAAe;AACxB,CAAC,CAAC,CAAC;AACH,IAAIE,UAAU,GAAG,aAAa,UAAUC,MAAM,EAAE;EAC9C1D,SAAS,CAACyD,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAACE,IAAI,EAAE;IACxB,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,IAAI,CAAC,IAAI,IAAI;IAC3CC,KAAK,CAACE,IAAI,GAAG,aAAa;IAC1B,OAAOF,KAAK;EACd;EACAH,UAAU,CAACM,SAAS,CAACC,eAAe,GAAG,YAAY;IACjD,OAAO;MACLC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE;IACR,CAAC;EACH,CAAC;EACDT,UAAU,CAACM,SAAS,CAACI,eAAe,GAAG,YAAY;IACjD,OAAO,IAAIZ,eAAe,CAAC,CAAC;EAC9B,CAAC;EACDE,UAAU,CAACM,SAAS,CAACK,SAAS,GAAG,UAAUrD,GAAG,EAAEsD,KAAK,EAAE;IACrD,IAAIrD,MAAM,GAAGqD,KAAK,CAACrD,MAAM;IACzB,IAAIsD,CAAC,GAAG,CAAC;IACT,IAAIC,GAAG,GAAGvD,MAAM,CAACwD,MAAM,GAAG,CAAC;IAC3B;IACA,IAAIH,KAAK,CAAC9C,YAAY,EAAE;MACtB;MACA,OAAOgD,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;QACrB,IAAI,CAAC7D,WAAW,CAACM,MAAM,CAACuD,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEvD,MAAM,CAACuD,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;UAC1D;QACF;MACF;MACA,OAAOD,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QACnB,IAAI,CAAC5D,WAAW,CAACM,MAAM,CAACsD,CAAC,GAAG,CAAC,CAAC,EAAEtD,MAAM,CAACsD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;UAClD;QACF;MACF;IACF;IACA,OAAOA,CAAC,GAAGC,GAAG,EAAE;MACdD,CAAC,IAAIxD,WAAW,CAACC,GAAG,EAAEC,MAAM,EAAEsD,CAAC,EAAEC,GAAG,EAAEA,GAAG,EAAE,CAAC,EAAEF,KAAK,CAAChD,MAAM,EAAEgD,KAAK,CAAC/C,cAAc,EAAE+C,KAAK,CAAC9C,YAAY,CAAC,GAAG,CAAC;IAC3G;EACF,CAAC;EACDkC,UAAU,CAACM,SAAS,CAACU,UAAU,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAE;IACrD,IAAI,CAAC,IAAI,CAACC,IAAI,EAAE;MACd,IAAI,CAACC,eAAe,CAAC,CAAC;MACtB,IAAI,CAACT,SAAS,CAAC,IAAI,CAACQ,IAAI,EAAE,IAAI,CAACP,KAAK,CAAC;IACvC;IACA,IAAIO,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIE,IAAI,GAAGF,IAAI,CAACE,IAAI;IACpB,IAAIC,GAAG,GAAG7E,SAAS,CAAC6E,GAAG;IACvB,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,MAAM,GAAGP,GAAG,KAAK,GAAG;IACxB,IAAIQ,KAAK,GAAG,EAAE;IACd,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,IAAI,CAACN,MAAM,GAAG;MAChC,IAAIY,GAAG,GAAGN,IAAI,CAACR,CAAC,EAAE,CAAC;MACnB,IAAI3D,CAAC,GAAG,KAAK,CAAC;MACd,IAAIC,CAAC,GAAG,KAAK,CAAC;MACd,IAAIyE,EAAE,GAAG,KAAK,CAAC;MACf,IAAIC,EAAE,GAAG,KAAK,CAAC;MACf,IAAIC,EAAE,GAAG,KAAK,CAAC;MACf,IAAIC,EAAE,GAAG,KAAK,CAAC;MACf,IAAIC,CAAC,GAAG,KAAK,CAAC;MACd,QAAQL,GAAG;QACT,KAAKL,GAAG,CAACW,CAAC;UACRV,EAAE,GAAGF,IAAI,CAACR,CAAC,EAAE,CAAC;UACdW,EAAE,GAAGH,IAAI,CAACR,CAAC,EAAE,CAAC;UACd;QACF,KAAKS,GAAG,CAACY,CAAC;UACRhF,CAAC,GAAGmE,IAAI,CAACR,CAAC,EAAE,CAAC;UACb1D,CAAC,GAAGkE,IAAI,CAACR,CAAC,EAAE,CAAC;UACbmB,CAAC,GAAGP,MAAM,GAAG,CAACR,IAAI,GAAGM,EAAE,KAAKrE,CAAC,GAAGqE,EAAE,CAAC,GAAG,CAACN,IAAI,GAAGO,EAAE,KAAKrE,CAAC,GAAGqE,EAAE,CAAC;UAC5D,IAAIQ,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,EAAE;YACpB,IAAIG,GAAG,GAAGV,MAAM,GAAG,CAACtE,CAAC,GAAGqE,EAAE,IAAIQ,CAAC,GAAGR,EAAE,GAAG,CAACtE,CAAC,GAAGqE,EAAE,IAAIS,CAAC,GAAGT,EAAE;YACxD,OAAOE,MAAM,GAAG,CAACR,IAAI,EAAEkB,GAAG,CAAC,GAAG,CAACA,GAAG,EAAElB,IAAI,CAAC;UAC3C;UACAM,EAAE,GAAGrE,CAAC;UACNsE,EAAE,GAAGrE,CAAC;UACN;QACF,KAAKmE,GAAG,CAACc,CAAC;UACRlF,CAAC,GAAGmE,IAAI,CAACR,CAAC,EAAE,CAAC;UACb1D,CAAC,GAAGkE,IAAI,CAACR,CAAC,EAAE,CAAC;UACbe,EAAE,GAAGP,IAAI,CAACR,CAAC,EAAE,CAAC;UACdgB,EAAE,GAAGR,IAAI,CAACR,CAAC,EAAE,CAAC;UACdiB,EAAE,GAAGT,IAAI,CAACR,CAAC,EAAE,CAAC;UACdkB,EAAE,GAAGV,IAAI,CAACR,CAAC,EAAE,CAAC;UACd,IAAIwB,KAAK,GAAGZ,MAAM,GAAG/E,WAAW,CAAC6E,EAAE,EAAErE,CAAC,EAAE0E,EAAE,EAAEE,EAAE,EAAEb,IAAI,EAAES,KAAK,CAAC,GAAGhF,WAAW,CAAC8E,EAAE,EAAErE,CAAC,EAAE0E,EAAE,EAAEE,EAAE,EAAEd,IAAI,EAAES,KAAK,CAAC;UACtG,IAAIW,KAAK,GAAG,CAAC,EAAE;YACb,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,KAAK,EAAEC,GAAG,EAAE,EAAE;cACpC,IAAIC,GAAG,GAAGb,KAAK,CAACY,GAAG,CAAC;cACpB,IAAIC,GAAG,IAAI,CAAC,IAAIA,GAAG,IAAI,CAAC,EAAE;gBACxB,IAAIJ,GAAG,GAAGV,MAAM,GAAG9E,OAAO,CAAC6E,EAAE,EAAErE,CAAC,EAAE0E,EAAE,EAAEE,EAAE,EAAEQ,GAAG,CAAC,GAAG5F,OAAO,CAAC4E,EAAE,EAAErE,CAAC,EAAE0E,EAAE,EAAEE,EAAE,EAAES,GAAG,CAAC;gBAC5E,OAAOd,MAAM,GAAG,CAACR,IAAI,EAAEkB,GAAG,CAAC,GAAG,CAACA,GAAG,EAAElB,IAAI,CAAC;cAC3C;YACF;UACF;UACAM,EAAE,GAAGO,EAAE;UACPN,EAAE,GAAGO,EAAE;UACP;MACJ;IACF;EACF,CAAC;EACD,OAAO/B,UAAU;AACnB,CAAC,CAACxD,IAAI,CAAC;AACP,SAASwD,UAAU;AACnB,IAAIwC,cAAc,GAAG,aAAa,UAAUvC,MAAM,EAAE;EAClD1D,SAAS,CAACiG,cAAc,EAAEvC,MAAM,CAAC;EACjC,SAASuC,cAAcA,CAAA,EAAG;IACxB,OAAOvC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACwC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACjE;EACA,OAAOF,cAAc;AACvB,CAAC,CAAC1C,eAAe,CAAC;AAClB,IAAI6C,SAAS,GAAG,aAAa,UAAU1C,MAAM,EAAE;EAC7C1D,SAAS,CAACoG,SAAS,EAAE1C,MAAM,CAAC;EAC5B,SAAS0C,SAASA,CAACzC,IAAI,EAAE;IACvB,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,IAAI,CAAC,IAAI,IAAI;IAC3CC,KAAK,CAACE,IAAI,GAAG,YAAY;IACzB,OAAOF,KAAK;EACd;EACAwC,SAAS,CAACrC,SAAS,CAACI,eAAe,GAAG,YAAY;IAChD,OAAO,IAAI8B,cAAc,CAAC,CAAC;EAC7B,CAAC;EACDG,SAAS,CAACrC,SAAS,CAACK,SAAS,GAAG,UAAUrD,GAAG,EAAEsD,KAAK,EAAE;IACpD,IAAIrD,MAAM,GAAGqD,KAAK,CAACrD,MAAM;IACzB,IAAIqF,eAAe,GAAGhC,KAAK,CAACgC,eAAe;IAC3C,IAAI/B,CAAC,GAAG,CAAC;IACT,IAAIC,GAAG,GAAGvD,MAAM,CAACwD,MAAM,GAAG,CAAC;IAC3B,IAAIlD,cAAc,GAAG+C,KAAK,CAAC/C,cAAc;IACzC,IAAI+C,KAAK,CAAC9C,YAAY,EAAE;MACtB;MACA,OAAOgD,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;QACrB,IAAI,CAAC7D,WAAW,CAACM,MAAM,CAACuD,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEvD,MAAM,CAACuD,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;UAC1D;QACF;MACF;MACA,OAAOD,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;QACnB,IAAI,CAAC5D,WAAW,CAACM,MAAM,CAACsD,CAAC,GAAG,CAAC,CAAC,EAAEtD,MAAM,CAACsD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;UAClD;QACF;MACF;IACF;IACA,OAAOA,CAAC,GAAGC,GAAG,EAAE;MACd,IAAIxC,CAAC,GAAGjB,WAAW,CAACC,GAAG,EAAEC,MAAM,EAAEsD,CAAC,EAAEC,GAAG,EAAEA,GAAG,EAAE,CAAC,EAAEF,KAAK,CAAChD,MAAM,EAAEC,cAAc,EAAE+C,KAAK,CAAC9C,YAAY,CAAC;MAClGT,WAAW,CAACC,GAAG,EAAEsF,eAAe,EAAE/B,CAAC,GAAGvC,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAEwC,GAAG,EAAE,CAAC,CAAC,EAAEF,KAAK,CAACiC,eAAe,EAAEhF,cAAc,EAAE+C,KAAK,CAAC9C,YAAY,CAAC;MACnH+C,CAAC,IAAIvC,CAAC,GAAG,CAAC;MACVhB,GAAG,CAACwF,SAAS,CAAC,CAAC;IACjB;EACF,CAAC;EACD,OAAOH,SAAS;AAClB,CAAC,CAACnG,IAAI,CAAC;AACP,SAASmG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}